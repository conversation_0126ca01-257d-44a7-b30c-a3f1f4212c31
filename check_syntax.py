#!/usr/bin/env python3
"""
Script to check Python syntax errors in CLEAR views
"""

import ast
import sys

def check_file_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the AST
        ast.parse(content, filename=filename)
        print(f"✓ {filename}: Syntax OK")
        return True
        
    except SyntaxError as e:
        print(f"✗ {filename}: Syntax Error at line {e.lineno}")
        print(f"   Error: {e.msg}")
        if e.text:
            print(f"   Text: {e.text.strip()}")
        
        # Show context around the error
        lines = content.split('\n')
        start = max(0, e.lineno - 5)
        end = min(len(lines), e.lineno + 5)
        
        print("   Context:")
        for i in range(start, end):
            marker = ">>> " if i == e.lineno - 1 else "    "
            print(f"   {marker}{i+1:4d}: {lines[i]}")
        
        return False
    except Exception as e:
        print(f"✗ {filename}: Other error: {e}")
        return False

if __name__ == "__main__":
    files_to_check = [
        "CLEAR/views/project_views.py",
        "CLEAR/views/auth_views.py", 
        "CLEAR/views/admin_views.py",
        "CLEAR/views/gis_views.py",
        "CLEAR/services/analytics.py",
        "CLEAR/services/system_monitoring.py"
    ]
    
    all_good = True
    for filename in files_to_check:
        if not check_file_syntax(filename):
            all_good = False
    
    if all_good:
        print("\n✓ All files passed syntax check")
    else:
        print("\n✗ Some files have syntax errors")
        sys.exit(1)