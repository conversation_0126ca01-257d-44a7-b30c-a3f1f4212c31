# OPERATION C.L.E.A.R. - AI COORDINATION PROMPT

## MISSION OBJECTIVE
Execute the comprehensive codebase analysis system (`full-review.py`) and coordinate the generation of automated fix scripts by each sub-agent to achieve 95% HDA (Hypermedia-Driven Application) compliance for the CLEAR Django project.

## IMPROVED ARCHITECTURE: PYTHON HEAVY LIFTING + AGENT COORDINATION

### **New Approach:**
1. **Python Analysis Engine** (`full-review.py`) - Does all the heavy lifting
2. **AI Agents** - Coordinate, interpret results, and generate fix scripts
3. **Automated Fix Scripts** - Each sub-agent creates its own Python fix script
4. **Coordinated Execution** - AI orchestrates the entire process

## COMPREHENSIVE TECHNICAL REPORTING ARCHITECTURE

### **Multi-Layer Reporting System:**

#### **Layer 1: Raw Analysis Reports (Python Generated)**
- `alpha_scanner_raw_report.json` - Detailed file system analysis
- `beta_tester_raw_report.json` - Comprehensive test results
- `charlie_auditor_raw_report.json` - Compliance violation details
- `delta_validator_raw_report.json` - Application validation results

#### **Layer 2: Technical Error Analysis Reports (AI Generated)**
- `error_reasoning_analysis.md` - Deep technical analysis of each error
- `validation_matrix_report.json` - Cross-agent validation results
- `compliance_gap_analysis.md` - Detailed gap analysis with technical reasoning
- `security_vulnerability_analysis.md` - Security issue technical details

#### **Layer 3: Fix Script Technical Reviews (AI Generated)**
- `alpha_fixes_technical_review.md` - Technical review of Alpha fixes
- `charlie_fixes_technical_review.md` - Technical review of Charlie fixes
- `beta_fixes_technical_review.md` - Technical review of Beta fixes
- `delta_fixes_technical_review.md` - Technical review of Delta fixes

#### **Layer 4: Parsed Plain Text Summary (AI Generated)**
- `executive_summary_plain_text.md` - Human-readable summary
- `technical_summary_plain_text.md` - Technical summary for developers
- `action_items_plain_text.md` - Clear action items and next steps

## EXECUTION INSTRUCTIONS

### Step 1: System Setup and Validation
1. **Environment Verification**
   - Confirm you're in the CLEAR project root directory
   - Verify Django is properly configured with `clear_htmx.settings`
   - Check that all required Python packages are installed (django, coverage, etc.)
   - Ensure the project structure matches the expected Django layout

2. **File System Validation**
   - Confirm `full-review.py` exists and is executable
   - Verify the CLEAR app structure: `CLEAR/models/`, `CLEAR/views/`, `templates/`, etc.
   - Check for any missing dependencies or configuration files

### Step 2: Execute the Python Analysis Engine
```bash
# Run the comprehensive analysis engine
python full-review.py
```

**Expected Output:**
- Real-time logging of agent execution
- Phase-by-phase progress updates
- Sub-agent reports and confidence scores
- Compliance score calculations
- Generated report files with detailed issue analysis

### Step 3: AI Agent Coordination Phase
After the Python analysis completes, coordinate the generation of fix scripts:

**A. Issue Analysis and Prioritization**
- Parse the generated reports (`phase_reports.json`, `final_mission_report.json`)
- Extract all identified issues with their severity, file locations, and descriptions
- Prioritize issues by severity (CRITICAL → HIGH → MEDIUM → LOW)
- Group issues by type and affected components

**B. Sub-Agent Fix Script Generation**
For each sub-agent, create a specialized Python fix script:

**Alpha Scanner Fix Script** (`alpha_fixes.py`)
```python
# Generated by AI Agent based on Alpha Scanner findings
class AlphaFixer:
    def fix_django_structure_violations(self):
        # Fix Django app structure issues
        pass
    
    def fix_model_issues(self):
        # Fix model-related issues
        pass
    
    def fix_view_issues(self):
        # Fix view-related issues
        pass
```

**Charlie Auditor Fix Script** (`charlie_fixes.py`)
```python
# Generated by AI Agent based on Charlie Auditor findings
class CharlieFixer:
    def fix_hda_compliance(self):
        # Convert JSON APIs to HTMX endpoints
        pass
    
    def fix_technology_stack(self):
        # Remove unauthorized libraries
        pass
    
    def fix_security_vulnerabilities(self):
        # Add CSRF tokens, fix security issues
        pass
```

**Beta Tester Fix Script** (`beta_fixes.py`)
```python
# Generated by AI Agent based on Beta Tester findings
class BetaFixer:
    def fix_test_coverage(self):
        # Add missing tests
        pass
    
    def fix_performance_issues(self):
        # Optimize slow tests and code
        pass
```

**Delta Validator Fix Script** (`delta_fixes.py`)
```python
# Generated by AI Agent based on Delta Validator findings
class DeltaFixer:
    def fix_htmx_functionality(self):
        # Fix HTMX implementation issues
        pass
    
    def fix_endpoint_issues(self):
        # Fix broken endpoints
        pass
```

### Step 4: Coordinated Fix Execution
Execute the generated fix scripts in the correct order:

```bash
# Execute fixes in priority order
python alpha_fixes.py
python charlie_fixes.py  # Most critical - HDA compliance
python beta_fixes.py
python delta_fixes.py
```

## TECHNICAL REPORTING REQUIREMENTS

### **1. Error Reasoning Analysis Report**
Generate `error_reasoning_analysis.md` with deep technical analysis:

```markdown
# ERROR REASONING ANALYSIS REPORT

## CRITICAL ISSUES - TECHNICAL ANALYSIS

### Issue: HDA_001 - JSON API Violation
**File:** CLEAR/api_views.py:1-50
**Technical Root Cause:**
- REST framework imports detected: `from rest_framework import viewsets, status`
- Violates HDA principle: "Server responses must contain hypermedia controls"
- Current implementation returns JSON instead of HTML with embedded controls

**Technical Impact:**
- Breaks hypermedia-driven architecture principles
- Creates tight coupling between client and server
- Prevents server-driven state transitions

**Technical Solution:**
- Convert APIView classes to Django views
- Replace JSON responses with HTML template rendering
- Add HTMX attributes for client-side interactions
- Implement proper hypermedia controls in templates

**Validation Requirements:**
- Verify all responses return HTML content-type
- Ensure templates contain proper HTMX attributes
- Test client-side interactions work without JavaScript knowledge

### Issue: SEC_001 - CSRF Token Missing
**File:** templates/auth/login.html:15
**Technical Root Cause:**
- Form element lacks `{% csrf_token %}` template tag
- Django CSRF middleware enabled but token not included
- Creates security vulnerability for cross-site request forgery

**Technical Impact:**
- Forms can be submitted from malicious sites
- Session hijacking potential
- Violates Django security best practices

**Technical Solution:**
- Add `{% csrf_token %}` to all form elements
- Verify CSRF middleware configuration
- Test form submissions with proper token validation

**Validation Requirements:**
- All forms contain CSRF tokens
- Form submissions fail without valid tokens
- No CSRF-related errors in logs
```

### **2. Validation Matrix Report**
Generate `validation_matrix_report.json` with cross-agent validation:

```json
{
  "validation_matrix": {
    "cross_agent_consistency": 0.92,
    "confidence_scores": {
      "alpha_scanner": 0.95,
      "beta_tester": 0.90,
      "charlie_auditor": 0.93,
      "delta_validator": 0.87
    },
    "issue_validation": {
      "hda_001": {
        "identified_by": ["charlie_auditor"],
        "verified_by": ["alpha_scanner", "delta_validator"],
        "confidence": 0.98,
        "validation_method": "AST analysis + template parsing"
      },
      "sec_001": {
        "identified_by": ["charlie_auditor"],
        "verified_by": ["delta_validator"],
        "confidence": 0.95,
        "validation_method": "Template regex analysis"
      }
    },
    "compliance_validation": {
      "hda_compliance": {
        "current_score": 0.15,
        "target_score": 0.95,
        "gap_analysis": "80% gap due to JSON API endpoints",
        "validation_method": "Import analysis + response type checking"
      }
    }
  }
}
```

### **3. Fix Script Technical Reviews**
Generate technical reviews for each fix script:

**`alpha_fixes_technical_review.md`:**
```markdown
# ALPHA FIXES TECHNICAL REVIEW

## Fix Script: alpha_fixes.py
**Generated:** 2024-01-15 14:30:00
**Reviewer:** AI Technical Review Agent
**Confidence Score:** 0.94

## Technical Analysis

### Fix Method: fix_django_structure_violations()
**Technical Approach:**
- AST-based code analysis for Django app structure
- File system validation for required Django files
- Automatic creation of missing __init__.py files
- URL pattern validation and correction

**Technical Risks:**
- Low risk: File creation operations
- Medium risk: URL pattern modifications
- Mitigation: Backup original files before modification

**Validation Strategy:**
- Verify Django app structure compliance
- Test URL pattern functionality
- Ensure no broken imports

### Fix Method: fix_model_issues()
**Technical Approach:**
- Model field validation and correction
- Foreign key relationship verification
- Missing model method implementation
- Database migration generation

**Technical Risks:**
- High risk: Database schema changes
- Medium risk: Model relationship modifications
- Mitigation: Generate and test migrations on copy

**Validation Strategy:**
- Run Django model validation
- Test database migrations
- Verify model relationships

## Overall Assessment
**Technical Quality:** Excellent
**Risk Level:** Medium
**Recommended Execution Order:** 1st (before other fixes)
**Estimated Execution Time:** 45 minutes
```

### **4. Parsed Plain Text Summary**
Generate `executive_summary_plain_text.md`:

```markdown
# OPERATION C.L.E.A.R. - EXECUTIVE SUMMARY

## MISSION STATUS: IN PROGRESS
**Overall Compliance Score:** 15% (Target: 95%)
**Gap to Target:** 80%
**Critical Issues Found:** 12
**High Priority Issues:** 8

## WHAT WE FOUND

### Critical Problems (Must Fix Immediately)
1. **JSON API Endpoints** - 50+ REST framework endpoints violate HDA principles
   - Location: CLEAR/api_views.py
   - Impact: Breaks hypermedia-driven architecture
   - Fix: Convert to HTMX endpoints returning HTML

2. **Missing CSRF Protection** - 15 forms lack security tokens
   - Location: templates/auth/login.html, templates/projects/create.html
   - Impact: Security vulnerability for cross-site request forgery
   - Fix: Add {% csrf_token %} to all forms

3. **Unauthorized JavaScript** - jQuery usage detected
   - Location: templates/dashboard/dashboard.html
   - Impact: Violates technology stack requirements
   - Fix: Replace with HTMX or Alpine.js

### High Priority Problems (Fix This Week)
1. **Missing HTMX Implementation** - Only 30% of interactions use HTMX
2. **Bootstrap 5 Compliance** - Some templates use unauthorized CSS
3. **Test Coverage Gaps** - 60% test coverage (target: 80%)

## WHAT WE'RE DOING ABOUT IT

### Phase 1: Critical Fixes (This Week)
- **Charlie Fixer** will convert JSON APIs to HTMX (8 hours)
- **Alpha Fixer** will fix Django structure issues (2 hours)
- **Delta Fixer** will add CSRF protection (1 hour)

### Phase 2: Security & Technology (Next Week)
- **Charlie Fixer** will remove unauthorized libraries (4 hours)
- **Delta Fixer** will implement missing HTMX functionality (6 hours)

### Phase 3: Quality & Testing (Week 3)
- **Beta Fixer** will improve test coverage (8 hours)
- **Alpha Fixer** will fix best practice violations (4 hours)

## TECHNICAL VALIDATION

### Cross-Agent Consistency: 92%
All agents agree on issue identification and severity classification.

### Fix Script Quality: Excellent
- All fix scripts include error handling
- Rollback capabilities implemented
- Tested on sample data before production

### Risk Assessment: Medium
- Database changes require careful testing
- URL modifications need validation
- Backup strategy implemented

## NEXT STEPS

1. **Execute Charlie Fixer** (highest priority - HDA compliance)
2. **Run validation tests** after each fix script
3. **Monitor compliance score** improvements
4. **Generate progress reports** after each phase

## SUCCESS METRICS

- **Target:** 95% HDA compliance
- **Current:** 15% compliance
- **Expected after Phase 1:** 65% compliance
- **Expected after Phase 2:** 85% compliance
- **Expected after Phase 3:** 95% compliance

## TECHNICAL DETAILS

### Files Analyzed: 1,247
### Lines of Code: 45,892
### Issues Identified: 47 total
### Fix Scripts Generated: 4
### Estimated Total Effort: 33 hours over 3 weeks

**Confidence Level:** 94% - High confidence in analysis and fix approach
```

## ANALYSIS REVIEW REQUIREMENTS

### 1. **Compliance Score Assessment**
- **Target**: 95% HDA compliance
- **Current Score**: Extract from Charlie Auditor results
- **Gap Analysis**: Identify specific areas preventing 95% compliance
- **Priority Violations**: List CRITICAL and HIGH severity issues

### 2. **Issue Classification and Prioritization**
Categorize all identified issues by:

**A. HDA Compliance Violations (CRITICAL)**
- JSON API endpoints that must be converted to HTMX
- Unauthorized JavaScript usage (jQuery, etc.)
- Missing hypermedia controls in responses

**B. Technology Stack Violations (HIGH)**
- Non-Bootstrap 5 CSS frameworks
- Unauthorized JavaScript libraries
- Missing HTMX implementation

**C. Security Vulnerabilities (HIGH)**
- Missing CSRF tokens
- SQL injection risks
- XSS vulnerabilities

**D. Code Quality Issues (MEDIUM)**
- TODO comments requiring resolution
- Mock data/stubs needing implementation
- Missing error handling

**E. Django Best Practices (MEDIUM)**
- Improper view implementations
- Missing model methods
- URL configuration errors

### 3. **Automated Fix Script Generation**
For each issue type, generate Python code that can automatically fix the problem:

**Example: JSON API to HTMX Conversion**
```python
def convert_json_api_to_htmx(api_view_file: str, template_file: str):
    """
    Convert a JSON API endpoint to HTMX endpoint
    """
    # Read the API view
    with open(api_view_file, 'r') as f:
        content = f.read()
    
    # Convert APIView to regular Django view
    new_content = content.replace('from rest_framework.views import APIView', '')
    new_content = new_content.replace('class LoginAPIView(APIView):', 'def login_view(request):')
    new_content = new_content.replace('return Response({', 'return render(request, template_file, {')
    
    # Write the converted view
    with open(api_view_file, 'w') as f:
        f.write(new_content)
```

**Example: CSRF Token Addition**
```python
def add_csrf_tokens_to_forms():
    """
    Add CSRF tokens to all forms in templates
    """
    templates_dir = Path('templates')
    for template_file in templates_dir.rglob('*.html'):
        with open(template_file, 'r') as f:
            content = f.read()
        
        # Add CSRF token to forms that don't have it
        if '<form' in content and '{% csrf_token %}' not in content:
            content = content.replace('<form', '<form>{% csrf_token %}\n<form')
        
        with open(template_file, 'w') as f:
            f.write(content)
```

### 4. **Implementation Roadmap Creation**
Create a prioritized implementation plan:

**Phase 1: Critical HDA Compliance (Week 1)**
- Execute Charlie Auditor fixes (JSON API conversion)
- Execute Alpha Scanner fixes (structure issues)
- Verify compliance improvements

**Phase 2: Security and Technology Stack (Week 2)**
- Execute Delta Validator fixes (HTMX functionality)
- Execute remaining Charlie Auditor fixes (security)
- Ensure Bootstrap 5 compliance

**Phase 3: Code Quality and Best Practices (Week 3)**
- Execute Beta Tester fixes (test coverage)
- Execute remaining Alpha Scanner fixes (best practices)
- Resolve TODO comments and mock data

**Phase 4: Testing and Verification (Week 4)**
- Run comprehensive test suite
- Verify compliance improvements
- Document changes and lessons learned

### 5. **Resource Estimation**
For each phase, provide:
- **Time Estimates**: Hours required for implementation
- **Complexity Scores**: 1-5 scale for implementation difficulty
- **Dependencies**: What must be completed before each task
- **Risk Assessment**: Potential issues during implementation

### 6. **Success Metrics Definition**
Define measurable success criteria:
- **Compliance Score**: Target 95% HDA compliance
- **Issue Resolution**: 100% of CRITICAL issues resolved
- **Test Coverage**: Maintain >80% test coverage
- **Performance**: No regression in response times
- **Security**: Zero high/critical security vulnerabilities

## EXPECTED OUTPUT FORMAT

### Executive Summary
```
OPERATION C.L.E.A.R. - EXECUTION RESULTS

MISSION STATUS: [SUCCESS/INCOMPLETE/FAILED]
OVERALL COMPLIANCE SCORE: [X]%
TARGET COMPLIANCE: 95%
GAP TO TARGET: [X]%

EXECUTION STATISTICS:
- Total Execution Time: [X] minutes
- Phases Completed: [X]/4
- Iterations Required: [X]/3
- Issues Identified: [X] total
- Critical Issues: [X]
- High Priority Issues: [X]
- Fix Scripts Generated: [X]
```

### Generated Fix Scripts Summary
```
AUTOMATED FIX SCRIPTS GENERATED:

1. alpha_fixes.py - Django Structure Fixes
   - Issues to fix: [X]
   - Estimated time: [X] minutes
   - Risk level: [LOW/MEDIUM/HIGH]

2. charlie_fixes.py - HDA Compliance Fixes
   - Issues to fix: [X]
   - Estimated time: [X] minutes
   - Risk level: [LOW/MEDIUM/HIGH]

3. beta_fixes.py - Test Coverage Fixes
   - Issues to fix: [X]
   - Estimated time: [X] minutes
   - Risk level: [LOW/MEDIUM/HIGH]

4. delta_fixes.py - HTMX Functionality Fixes
   - Issues to fix: [X]
   - Estimated time: [X] minutes
   - Risk level: [LOW/MEDIUM/HIGH]
```

### Detailed Findings
```
CRITICAL ISSUES (Must Fix):
1. [Issue ID] - [Description] - [File:Line] - [Fix Script: charlie_fixes.py]
2. [Issue ID] - [Description] - [File:Line] - [Fix Script: alpha_fixes.py]

HIGH PRIORITY ISSUES (Should Fix):
1. [Issue ID] - [Description] - [File:Line] - [Fix Script: delta_fixes.py]
2. [Issue ID] - [Description] - [File:Line] - [Fix Script: beta_fixes.py]

COMPLIANCE BREAKDOWN:
- HDA Compliance: [X]% (Target: 95%)
- HTMX Implementation: [X]% (Target: 90%)
- Bootstrap 5 Compliance: [X]% (Target: 95%)
- Security Score: [X]% (Target: 100%)
```

### Implementation Roadmap
```
PHASE 1: CRITICAL HDA COMPLIANCE (Week 1)
Tasks:
1. Execute charlie_fixes.py - [X] hours - Complexity: [X]/5
2. Execute alpha_fixes.py - [X] hours - Complexity: [X]/5

PHASE 2: SECURITY & TECHNOLOGY STACK (Week 2)
Tasks:
1. Execute delta_fixes.py - [X] hours - Complexity: [X]/5
2. Execute remaining charlie_fixes.py - [X] hours - Complexity: [X]/5

TOTAL ESTIMATED EFFORT: [X] hours over [X] weeks
```

### Risk Assessment
```
HIGH RISK AREAS:
1. [Risk Description] - Mitigation: [Strategy]
2. [Risk Description] - Mitigation: [Strategy]

DEPENDENCIES:
1. [Dependency] - Required by: [Task/Phase]
2. [Dependency] - Required by: [Task/Phase]

ROLLBACK STRATEGY:
- Backup original files before running fix scripts
- Version control all changes
- Test each fix script on a copy before production
```

## QUALITY ASSURANCE CHECKLIST

Before providing final recommendations, verify:

✅ **Data Completeness**
- All 4 sub-agents executed successfully
- All phases completed or failed with clear reason
- All generated reports are accessible and complete

✅ **Analysis Accuracy**
- Compliance scores are calculated correctly
- Issue severity classifications are appropriate
- File paths and line numbers are accurate

✅ **Fix Script Quality**
- All fix scripts are syntactically correct Python
- Fix scripts include proper error handling
- Fix scripts include rollback capabilities
- Fix scripts are tested on sample data

✅ **Technical Review Quality**
- All fix scripts have comprehensive technical reviews
- Error reasoning analysis is technically accurate
- Validation matrix shows high confidence scores
- Plain text summaries are clear and actionable

✅ **Recommendation Validity**
- Implementation plan is feasible within constraints
- Time estimates are realistic
- Dependencies are correctly identified
- Risk mitigation strategies are practical

✅ **Actionability**
- All recommendations have clear next steps
- Priority order is logical and achievable
- Success criteria are measurable
- Rollback strategies are defined for high-risk changes

## TECHNICAL ANALYSIS REQUIREMENTS

### 1. **Code Structure Analysis**
- **Django App Structure**: Verify proper Django app organization
- **Model Relationships**: Analyze model dependencies and foreign keys
- **View Patterns**: Identify view types and their compliance with HDA
- **Template Structure**: Assess template inheritance and HTMX usage
- **URL Configuration**: Review URL patterns and routing

### 2. **Static Code Analysis**
- **Import Analysis**: Identify unauthorized library imports
- **Pattern Detection**: Find code patterns that violate guidelines
- **Complexity Metrics**: Calculate cyclomatic complexity and maintainability
- **Code Smells**: Detect anti-patterns and technical debt

### 3. **Dynamic Analysis**
- **Test Coverage**: Measure test coverage across all modules
- **Performance Profiling**: Identify performance bottlenecks
- **Security Scanning**: Detect security vulnerabilities
- **Integration Testing**: Verify component interactions

### 4. **Compliance Verification**
- **HDA Principles**: Verify hypermedia-driven architecture compliance
- **Technology Stack**: Confirm authorized technology usage
- **Security Standards**: Validate security implementation
- **Best Practices**: Check Django and web development best practices

## ERROR HANDLING AND RECOVERY

### 1. **Execution Failures**
If the analysis system fails:
- **Log the Error**: Capture detailed error information
- **Identify Root Cause**: Determine why the failure occurred
- **Provide Workaround**: Suggest alternative analysis methods
- **Recommend Fixes**: Propose solutions to resolve the issue

### 2. **Partial Results**
If only some agents complete:
- **Assess Completeness**: Determine what analysis was completed
- **Extrapolate Results**: Use available data to estimate full results
- **Identify Gaps**: Note what analysis is missing
- **Plan Completion**: Suggest how to complete the missing analysis

### 3. **Data Quality Issues**
If results seem inaccurate:
- **Validate Assumptions**: Check if analysis assumptions are correct
- **Cross-Reference**: Compare with manual analysis where possible
- **Adjust Parameters**: Modify analysis parameters if needed
- **Re-run Analysis**: Execute analysis again with corrected parameters

### 4. **Fix Script Failures**
If generated fix scripts fail:
- **Log the Error**: Capture detailed error information
- **Identify Root Cause**: Determine why the fix failed
- **Provide Manual Fix**: Suggest manual steps to resolve the issue
- **Update Fix Script**: Correct the fix script for future use

## FINAL DELIVERABLE

Provide a comprehensive report that includes:

1. **Executive Summary** with key metrics and status
2. **Detailed Analysis** of all findings with specific file references
3. **Generated Fix Scripts** with execution instructions
4. **Technical Reviews** of all fix scripts
5. **Implementation Roadmap** with realistic timelines and effort estimates
6. **Risk Assessment** with mitigation strategies
7. **Success Metrics** with clear measurement criteria
8. **Next Steps** with immediate action items
9. **Parsed Plain Text Summary** for human consumption

The report should be actionable, comprehensive, and provide clear guidance for achieving 95% HDA compliance within the specified timeframe.

## SUCCESS CRITERIA

The analysis is successful if:
- ✅ All sub-agents complete execution successfully
- ✅ Compliance scores are calculated accurately
- ✅ All critical issues are identified and prioritized
- ✅ Automated fix scripts are generated and tested
- ✅ Technical reviews are comprehensive and accurate
- ✅ Plain text summaries are clear and actionable
- ✅ Implementation roadmap is realistic and actionable
- ✅ Risk assessment is comprehensive and practical
- ✅ Final recommendations lead to measurable compliance improvements

## CONTINGENCY PLANS

If the automated analysis fails or provides insufficient results:
1. **Manual Analysis**: Conduct manual code review focusing on critical areas
2. **Incremental Approach**: Analyze one component at a time
3. **Expert Consultation**: Seek input from Django/HDA experts
4. **Alternative Tools**: Use different analysis tools or approaches

If the automated fix scripts fail:
1. **Manual Fixes**: Provide step-by-step manual fix instructions
2. **Incremental Fixes**: Apply fixes one at a time with testing
3. **Expert Review**: Have fixes reviewed by experienced developers
4. **Rollback Strategy**: Maintain ability to revert all changes

The goal is to provide actionable insights and automated fixes regardless of the analysis method used.
