# Project C.L.E.A.R. - Comprehensive Codebase Refactoring Guidelines

## 1. Core Mandate: The Hypermedia-Driven Application (HDA)

The single, overarching goal of this initiative is to transform the application into a pure, robust, and maintainable **Hypermedia-Driven Application (HDA)**. This is not a partial refactor. Every component, view, and interaction must align with the principles of true REST and HATEOAS (Hypermedia as the Engine of Application State).

**The Prime Directive: The Server is the Source of Truth.**
The server is responsible for *all* application state and rendering logic. The client (browser) is a "dumb" terminal that renders the HTML it receives. All user actions must result in a request to the server, which will respond with a new HTML document or an HTML fragment to update the UI.

**JSON APIs are strictly forbidden.** The only currency exchanged between client and server is HTML, with the specific, approved exceptions for CAD functionality.

## 2. Architectural Principles

- **HATEOAS is Law**: Every server response must contain all the necessary "controls" (links and forms) for the user to take the next possible actions. The client should not have to guess or have hardcoded knowledge of endpoints. The user's journey through the application is driven entirely by the hypermedia provided by the server.
- **Statelessness**: Interactions should be as stateless as possible. Each request from the client must contain all information necessary to process it. Avoid server-side session state where possible.
- **Uniform Interface**: We will strictly adhere to the four constraints of the Uniform Interface:
    1.  **Identification of Resources**: Every resource is uniquely addressable via a URL.
    2.  **Manipulation through Representations**: The server will always return HTML representations.
    3.  **Self-Descriptive Messages**: The HTML response is the complete message. It contains the data and the controls to manipulate it.
    4.  **HATEOAS**: As stated above, this is the core principle.
- **Preservation First**: Reuse and respect the existing file structure, data models, and UI behaviors. Do **not** redesign working features. When you encounter a component that already fulfills its purpose (e.g., the admin template builder with drag-and-drop custom columns), preserve its current UX and data schema. Only refactor to fix defects, improve maintainability, or ensure Hypermedia compliance.

## 3. Technology Stack & Implementation Rules

### 3.1. Backend (Django)
- **Structure**: Adhere to modern Django project structure best practices:
    - Use the existing `CLEAR/` app structure but ensure proper separation of concerns
    - All Django apps must have proper `__init__.py`, `models.py`, `views.py`, `urls.py`, and `tests.py` files
    - Use Django's `apps.py` configuration for proper app registration
    - Follow Django's naming conventions for all modules and classes
- **Models**: All models must be well-defined, with proper relationships, constraints, and managers.
    - Implement proper `__str__` methods for all models
    - Use Django's built-in field types and validators
    - Implement proper database indexing for performance
    - All foreign keys must have proper `on_delete` behavior defined
- **Views**: Views must handle application logic and render HTML templates. No API-style views returning JSON.
    - Use Django's class-based views (CBVs) where appropriate for consistency
    - All views must include proper error handling and user feedback
    - Use Django's built-in authentication and permission decorators
    - Implement proper form validation and CSRF protection
- **URLs**: Use Django's URL patterns properly:
    - All URLs must use `path()` or `re_path()` with proper naming
    - Use `reverse()` and `{% url %}` tags instead of hardcoded URLs
    - Implement proper URL namespacing for apps
- **Templates**: Follow Django template best practices:
    - All templates must extend from a base template
    - Use Django's template inheritance properly
    - Include proper CSRF tokens in all forms
    - Use Django's built-in template tags and filters
- **Tests**: A comprehensive test suite using Django's testing framework is non-negotiable. Coverage should be as close to 100% as possible.
    - Test all models, views, forms, and business logic
    - Use Django's `TestCase` and `TransactionTestCase` appropriately
    - Mock external dependencies and services
    - Test both success and failure scenarios

### 3.2. Frontend (The "Hypermedia Trinity")
This is not a traditional frontend. It is a combination of technologies to render hypermedia.

- **HTML**: Use semantic, clean, and accessible HTML5.
- **Bootstrap 5**: This is the **exclusive** CSS framework for all styling, layout, and components. All UI must be built with Bootstrap 5 components and utility classes.
- **HTMX**: This is the **primary and default** engine for client-server communication.
    - All actions that don't require a full page reload (e.g., form submissions, searches, deletions, UI updates) **MUST** be implemented using `hx-*` attributes.
    - Use server-sent HTML fragments to update targeted parts of the DOM (`hx-target`, `hx-swap`).
    - **HTMX Implementation Patterns**:
        - Use `hx-get` for retrieving data and updating UI components
        - Use `hx-post` for form submissions and data creation
        - Use `hx-delete` for resource deletion with proper confirmation
        - Use `hx-put` or `hx-patch` for updates
        - Implement proper error handling with `hx-trigger` and response codes
        - Use `hx-indicator` for loading states and user feedback
        - Leverage `hx-swap-oob` for out-of-band updates when needed
        - Use `hx-push-url` for maintaining browser history on significant state changes

### 3.3. JavaScript Policy: "Allowed by Exception, Justified by Matrix"

JavaScript is a tool of last resort. Its use implies that a specific, complex UI interaction cannot be reasonably achieved through the HDA model (HTML/HTMX from the server).

- **Default Stance**: **Forbidden**.
- **Allowed Libraries**:
    1.  **Alpine.js**: For trivial, purely client-side "islands of interactivity" (e.g., managing the open/closed state of a custom dropdown menu).
    2.  **Three.js**: **MANDATORY** and unrestricted for the 3D CAD viewer.
    3.  **Map/2D Drawing Library: OpenLayers**: Based on a formal analysis, **OpenLayers is the mandatory and exclusive library** for all 2D mapping and CAD-related functionality. Its feature set is best aligned with the complex GIS and vector-editing requirements of this project.

- **Justification Matrix**: The decision to mandate OpenLayers is based on the following analysis. Any agent working on this project must adhere to this choice.

| Feature | Leaflet | OpenLayers | MapLibre GL |
| :--- | :--- | :--- | :--- |
| **Core Strength** | Simplicity, ease of use, large plugin ecosystem. | Power, flexibility, comprehensive feature set for complex GIS. | High-performance vector tile rendering, dynamic styling. |
| **CAD/Vector Editing** | Basic support. Relies heavily on plugins (e.g., `Leaflet.draw`) for advanced features. Can become cumbersome. | **Excellent**. Rich, built-in support for vector drawing, modification, snapping, and complex interactions. The professional choice for CAD-like features. | Strong vector support, but more focused on rendering and styling than complex, user-driven geometry editing. |
| **Data Formats** | Core supports GeoJSON. Other formats (WKT, TopoJSON, KML) require plugins. | **Excellent**. Native support for GeoJSON, KML, GML, WMS, WFS, and more OGC standards out-of-the-box. | Primarily focused on vector tiles (PBF), but handles GeoJSON sources well. |
| **Projections** | Limited to a single projection per map instance. | **Excellent**. Supports a vast range of projections and can re-project data on the fly. Critical for professional GIS. | Limited projection support, similar to Leaflet. |
| **Integration w/ HDA** | Simple. Can be initialized easily. Data fetching can be wired into HTMX triggers. | Structured and robust. The more formal API can lead to cleaner, more maintainable code when integrating with server-rendered fragments from Django. | WebGL-based; can be more complex to integrate into a simple HDA pattern compared to the others. |
| **Performance** | Good for simple maps and moderate data. Can struggle with a very high number of vector features. | **Very Good**. Optimized for handling large numbers of complex vector features and frequent updates. | **Excellent**. Uses WebGL for GPU-accelerated rendering, offering the best performance, especially with large vector tile datasets. |
| **Recommendation**| Not suitable for this project's core CAD requirements. | **Mandatory Choice**. Its built-in feature set for vector editing, data format support, and projection handling are non-negotiable for the stated goals. | Overkill for rendering needs and less focused on the specific CAD-style editing features required than OpenLayers. |

- **Justification**: Any use of Alpine.js **MUST** be rigorously justified through the "Solution Design Matrix," proving that no hypermedia-based alternative exists.

## 4. Data Modeling and Functionality

- **No Mock Data or Stubs**: The final application must be fully functional with live data interactions. All placeholders must be replaced.
- **"Git-like" Entity Versioning**: Implement a comprehensive audit and versioning system for all key data models (Projects, Tasks, Comments, etc.). Every change should be tracked, timestamped, and attributable to a user, creating a full history accessible through the admin interface. 
    - **Implementation Requirements**:
        - Track all CRUD operations (Create, Read, Update, Delete) on critical models
        - Store the complete state of each entity at each version, not just diffs
        - Include metadata: timestamp, user, action type, and optional comment
        - Implement soft deletes where appropriate to maintain audit trail
        - Provide a clean interface in Django admin to browse version history
        - Consider using `django-reversion` or implement custom audit models
        - Ensure versioning doesn't significantly impact performance
- **Admin Logs**: The Django admin area must contain a detailed logging system for all significant application events, particularly the entity versioning history.
    - **Log Requirements**:
        - User authentication events (login, logout, failed attempts)
        - All data modification events with before/after states
        - System configuration changes
        - Error events and exceptions
        - Performance metrics and slow query logs
        - Integration with Django's built-in logging framework

## 5. Code Quality and Completion

- **No TODOs**: All `TODO` or `FIXME` comments must be resolved.
- **No Commented-Out Code**: Dead code must be deleted.
- **Endpoint Integrity**: All URLs must resolve to a functioning view. There will be no broken or missing endpoints.
- **Test Everything**: Run all existing Django tests. Augment them to cover all new functionality and ensure all business logic is validated. The application must pass all tests before the task is considered complete.
- **95% Completion Target**: The goal is a 95% complete application, where "complete" means functionally perfect. The remaining 5% is reserved for purely cosmetic and minor visual tweaks. 