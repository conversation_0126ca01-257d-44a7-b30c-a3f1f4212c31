# Enhanced Multi-Agent Reporting Schema for Operation C.L.E.A.R.

# Custom scalars
scalar DateTime
scalar JSON

# Enumerations for issue classification
enum IssueType {
  CODE_SMELL
  BROKEN_ENDPOINT
  MISSING_ENDPOINT
  DJANGO_STRUCTURE_VIOLATION
  HYPERMEDIA_VIOLATION
  BOOTSTRAP_INCOMPATIBILITY
  UNAUTHORIZED_JS_USAGE
  MOCK_DATA_STUB
  TODO_COMMENT
  COMMENTED_OUT_CODE
  TESTING_GAP
  CAD_IMPLEMENTATION_ERROR
  DATA_MODEL_INCONSISTENCY
  SECURITY_VULNERABILITY
  TEMPLATE_VIOLATION
  URL_CONFIGURATION_ERROR
  FORM_VALIDATION_MISSING
  CSRF_TOKEN_MISSING
  HARDCODED_URL
  MISSING_ERROR_HANDLING
  HTMX_MISCONFIGURATION
  DATABASE_MIGRATION_NEEDED
  MISSING_MODEL_METHOD
  IMPROPER_VIEW_IMPLEMENTATION
  AUDIT_VERSIONING_MISSING
  ALPINE_JUSTIFICATION_REQUIRED
}

enum IssueSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum IssueStatus {
  IDENTIFIED
  PLANNING_SOLUTION
  SOLUTION_IMPLEMENTED
  VERIFIED_FIXED
  WONT_FIX
  REQUIRES_ITERATION
}

enum IssueSource {
  STATIC_ANALYSIS
  TEST_FAILURE
  APPLICATION_CRAWL
  MANUAL_REVIEW
  CROSS_AGENT_VERIFICATION
  VALIDATION_MATRIX
  OTHER
}

# New enumerations for multi-agent system
enum PhaseType {
  PHASE_1_ANALYSIS
  PHASE_2_PLANNING
  PHASE_3_IMPLEMENTATION
  PHASE_4_VERIFICATION
}

enum AgentType {
  ALPHA_SCANNER
  BETA_TESTER
  CHARLIE_AUDITOR
  DELTA_VALIDATOR
  CHIEF_ARCHITECT
}

enum PhaseStatus {
  INITIALIZING
  IN_PROGRESS
  VALIDATION_PENDING
  COMPLETE
  FAILED
  REQUIRES_ITERATION
}

enum ValidationResult {
  PASS
  FAIL
  WARNING
  REQUIRES_REVIEW
}

# Core issue representation (enhanced)
type Issue {
  id: ID!
  type: IssueType!
  status: IssueStatus!
  severity: IssueSeverity!
  file: String!
  line: Int
  codeSnippet: String
  description: String!
  guidelineViolated: String!
  certaintyScore: Float! # 0.0 to 1.0
  solutionProposed: String
  solutionJustification: String
  implementationPlan: [String!]
  estimatedTimeMinutes: Int
  complexityScore: Float # 1.0 to 5.0
  relatedIssues: [ID!]
  dateIdentified: DateTime!
  dateResolved: DateTime
  source: IssueSource!
  
  # Multi-agent tracking
  identifiedByAgent: AgentType!
  verifiedByAgents: [AgentType!]!
  phase: PhaseType!
  iterationNumber: Int!
  
  # Implementation tracking
  implementationDetails: ImplementationResult
  testCoverage: TestCoverageInfo
}

# Sub-agent specific reporting
type SubAgentReport {
  agentId: AgentType!
  phase: PhaseType!
  startTime: DateTime!
  completionTime: DateTime!
  executionTimeMs: Int!
  confidence: Float! # 0.0 to 1.0
  
  # Agent-specific results
  scanResults: ScanResults
  testResults: TestResults
  complianceResults: ComplianceResults
  validationResults: ValidationResults
  
  # Issues found by this agent
  issuesIdentified: [Issue!]!
  issuesVerified: [Issue!]!
  
  # Performance metrics
  performanceMetrics: JSON
}

# Specialized result types for different agents
type ScanResults {
  totalFiles: Int!
  djangoApps: [String!]!
  models: [String!]!
  views: [String!]!
  templates: [String!]!
  urlPatterns: [String!]!
  staticFiles: [String!]!
  violations: [Issue!]!
  structureCompliance: Float!
}

type TestResults {
  totalTests: Int!
  passedTests: Int!
  failedTests: Int!
  errorTests: Int!
  skippedTests: Int!
  coveragePercentage: Float!
  executionTimeMs: Int!
  failures: [TestFailure!]!
  missingCoverage: [String!]!
  performanceBottlenecks: [PerformanceBottleneck!]!
}

type ComplianceResults {
  overallComplianceScore: Float!
  guidelineViolations: [Issue!]!
  securityIssues: [Issue!]!
  codeQualityMetrics: JSON
  technicalDebtScore: Float!
  unauthorizedLibraries: [String!]!
  missingImplementations: [String!]!
  hdaComplianceScore: Float!
  htmxComplianceScore: Float!
  bootstrapComplianceScore: Float!
}

type ValidationResults {
  crawlStatistics: CrawlStatistics!
  endpointTests: [EndpointTest!]!
  performanceMetrics: JSON
  securityTests: [SecurityTest!]!
  htmxValidation: [HTMXValidation!]!
  functionalTests: [FunctionalTest!]!
}

# Supporting types for detailed reporting
type TestFailure {
  testName: String!
  errorMessage: String!
  stackTrace: String!
  category: String!
  severity: IssueSeverity!
}

type PerformanceBottleneck {
  location: String!
  executionTimeMs: Int!
  description: String!
  recommendation: String!
}

type CrawlStatistics {
  totalLinks: Int!
  validLinks: Int!
  brokenLinks: Int!
  totalForms: Int!
  validForms: Int!
  failedForms: Int!
  httpErrors: [HTTPError!]!
  jsErrors: [JSError!]!
}

type EndpointTest {
  url: String!
  method: String!
  status: Int!
  responseTime: Int!
  contentType: String!
  isHTMLResponse: Boolean!
  hasHTMXAttributes: Boolean!
}

type SecurityTest {
  testType: String!
  status: ValidationResult!
  description: String!
  recommendation: String
}

type HTMXValidation {
  element: String!
  attributes: [String!]!
  isValid: Boolean!
  returnsHTML: Boolean!
  errorMessage: String
}

type FunctionalTest {
  feature: String!
  status: ValidationResult!
  description: String!
  errorDetails: String
}

type HTTPError {
  url: String!
  statusCode: Int!
  errorMessage: String!
}

type JSError {
  url: String!
  message: String!
  stack: String!
  isUnauthorized: Boolean!
}

# Implementation tracking
type ImplementationResult {
  changesApplied: [CodeChange!]!
  migrationsCreated: [Migration!]!
  testsAdded: [TestCase!]!
  filesModified: [String!]!
  linesOfCodeChanged: Int!
  implementationTime: DateTime!
}

type CodeChange {
  file: String!
  startLine: Int!
  endLine: Int!
  changeType: String! # "ADD", "MODIFY", "DELETE"
  description: String!
  beforeCode: String
  afterCode: String
}

type Migration {
  app: String!
  migrationName: String!
  description: String!
  isReversible: Boolean!
  dependencies: [String!]!
}

type TestCase {
  file: String!
  testName: String!
  testType: String! # "unit", "integration", "functional"
  coverage: String!
}

type TestCoverageInfo {
  beforeCoverage: Float!
  afterCoverage: Float!
  newLinesCovered: Int!
  totalNewLines: Int!
}

# Validation matrices for each phase
type ValidationMatrix {
  phase: PhaseType!
  crossAgentConsistency: Float!
  completenessScore: Float!
  qualityAssuranceScore: Float!
  overallConfidence: Float!
  
  # Phase-specific validation scores
  solutionQualityScore: Float
  strategicAlignmentScore: Float
  riskMitigationScore: Float
  implementationQualityScore: Float
  functionalVerificationScore: Float
  complianceAchievementScore: Float
  missionAchievementScore: Float
  completionVerificationScore: Float
  
  # Detailed validation results
  validationDetails: [ValidationDetail!]!
}

type ValidationDetail {
  metric: String!
  score: Float!
  status: ValidationResult!
  description: String!
  recommendation: String
  evidence: [String!]!
}

# Phase-level reporting
type PhaseReport {
  phaseId: PhaseType!
  iterationNumber: Int!
  status: PhaseStatus!
  startTime: DateTime!
  completionTime: DateTime
  totalExecutionTime: Int
  
  # Sub-agent reports for this phase
  subAgentReports: [SubAgentReport!]!
  
  # Consolidated phase results
  consolidatedFindings: ConsolidatedFindings!
  validationMatrix: ValidationMatrix!
  
  # Phase transition information
  phaseCompletion: PhaseCompletion!
}

type ConsolidatedFindings {
  totalIssues: Int!
  newIssuesFound: Int!
  issuesResolved: Int!
  issuesByType: [IssueTypeCount!]!
  issuesBySeverity: [IssueSeverityCount!]!
  complianceScore: Float!
  improvementFromPreviousPhase: Float
  issues: [Issue!]!
}

type PhaseCompletion {
  status: PhaseStatus!
  nextPhase: PhaseType
  autoAdvance: Boolean!
  completionCriteriaMet: Boolean!
  iterationRequired: Boolean!
  iterationReason: String
}

# Summary types
type AnalysisSummary {
  totalIssues: Int!
  issuesByType: [IssueTypeCount!]!
  issuesBySeverity: [IssueSeverityCount!]!
  complianceScore: Float!
  currentPhase: PhaseType!
  currentIteration: Int!
  overallProgress: Float!
}

type IssueTypeCount {
  type: IssueType!
  count: Int!
}

type IssueSeverityCount {
  severity: IssueSeverity!
  count: Int!
}

# Execution tracking
type ExecutionSummary {
  missionStartTime: DateTime!
  currentPhase: PhaseType!
  currentIteration: Int!
  totalIterations: Int!
  maxIterationsAllowed: Int!
  overallComplianceScore: Float!
  targetComplianceScore: Float!
  isComplete: Boolean!
  completionReason: String
  
  # Phase progression
  phaseHistory: [PhaseReport!]!
  currentPhaseReport: PhaseReport
  
  # Performance metrics
  totalExecutionTime: Int
  averagePhaseTime: Int
  estimatedTimeRemaining: Int
}

# Root query type
type Query {
  # Current state queries
  summary: AnalysisSummary!
  executionSummary: ExecutionSummary!
  
  # Issue queries with enhanced filtering
  issues(
    status: IssueStatus
    type: IssueType
    severity: IssueSeverity
    source: IssueSource
    phase: PhaseType
    agent: AgentType
    iteration: Int
  ): [Issue!]!
  
  issue(id: ID!): Issue
  
  # Phase and agent reporting
  phaseReports(phase: PhaseType, iteration: Int): [PhaseReport!]!
  phaseReport(phase: PhaseType!, iteration: Int!): PhaseReport
  
  subAgentReports(
    agent: AgentType
    phase: PhaseType
    iteration: Int
  ): [SubAgentReport!]!
  
  # Validation and metrics
  validationMatrices(phase: PhaseType): [ValidationMatrix!]!
  validationMatrix(phase: PhaseType!, iteration: Int!): ValidationMatrix
  
  # Implementation tracking
  implementationResults(phase: PhaseType): [ImplementationResult!]!
  codeChanges(file: String): [CodeChange!]!
  migrations: [Migration!]!
  
  # Performance and quality metrics
  performanceMetrics(phase: PhaseType): JSON
  complianceHistory: [Float!]!
  testCoverageHistory: [Float!]!
}