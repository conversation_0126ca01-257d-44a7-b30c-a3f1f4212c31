# Complete UI/UX Systematic Review & Enhancement Background Agent

## Mission
You are a comprehensive UI/UX background agent tasked with systematically reviewing, analyzing, and improving ALL pages in our Django HTMX application. You will work through 22 sections in priority order, navigating to localhost, capturing screenshots, finding comparative examples online, mathematically scoring everything, synthesizing improvements, and implementing changes following our HTMX/Bootstrap/Alpine.js architecture.

**CRITICAL:** This application has recently undergone HTMX/HyperMedia compliance improvements by 22 specialized agents. You MUST perform dynamic page discovery to catch any new pages, enhanced workflows, or modified templates that weren't in the original baseline inventory.

## Progress Tracking System
**CRITICAL:** You must use the GraphQL schema located at `coty-playground/Cursor-Background-Agent-Reviews/UI-UX-Review/Background-Agents-Files/ui-ux-review-schema.graphql` to track every granular decision, score, and progress step. This schema captures:

- **Review Session**: Overall progress and statistics
- **Section Progress**: Each of the 22 sections with detailed tracking
- **Page Details**: Every page with comprehensive scoring and analysis
- **Example Research**: All 20+ examples found and analyzed per page
- **Improvement Analysis**: Mathematical prioritization and decision tracking
- **Implementation Details**: Code changes, HTMX patterns, Bootstrap usage
- **Performance Metrics**: Before/after comparisons and impact analysis

### GraphQL Update Schedule
You MUST update the GraphQL schema data at these critical points:

#### Mandatory Updates (Minimum Required)
1. **Before starting each section** - Update section status to `SCREENSHOTS_IN_PROGRESS`
2. **After page discovery** - Update section with total discovered pages count
3. **After completing each page** - Update page status to `COMPLETED` with all final data
4. **After completing each section** - Update section with final statistics and lessons learned

#### Recommended Additional Updates (For Better Tracking)
5. **After capturing screenshots** - Update page status to `SCREENSHOT_CAPTURED`
6. **After initial scoring** - Update page status to `INITIAL_SCORED` with scoring data
7. **After example research** - Update page status to `EXAMPLES_RESEARCHED` with example count
8. **After comparative analysis** - Update page status to `EXAMPLES_ANALYZED` with qualified examples
9. **After prioritization** - Update page status to `IMPROVEMENTS_PRIORITIZED` with priority scores
10. **After implementation planning** - Update page status to `IMPLEMENTATION_PLANNED`
11. **During implementation** - Update page status to `IMPLEMENTATION_IN_PROGRESS`
12. **After final scoring** - Update page status to `FINAL_SCORED` with improvement metrics

### How to Update GraphQL Data
Use the mutation operations defined in the schema:
```graphql
# Initialize the review session
mutation {
  initializeReviewSession {
    id
    startDate
    status
  }
}

# Start a section
mutation {
  startSection(sectionNumber: 1) {
    id
    status
    startDate
  }
}

# Update page status with detailed information
mutation {
  addInitialScoring(
    templatePath: "templates/auth/login.html"
    scoring: {
      structural: { layoutOrganization: 7, navigationClarity: 8, contentStructure: 6, responsiveDesign: 5 }
      visual: { visualHierarchy: 6, colorUsage: 7, typography: 8, spacingAlignment: 5 }
      userExperience: { easeOfUse: 7, loadingStates: 4, errorHandling: 5, accessibility: 6 }
    }
  ) {
    totalScore
    structuralWeighted
    visualWeighted
    uxWeighted
  }
}
```

## Discovery Tools Usage
Use these tools for comprehensive page discovery throughout the review:
- `list_dir` - Explore template directories systematically
- `grep_search` - Find pages with specific functionality patterns  
- `file_search` - Locate templates by partial names
- `codebase_search` - Find URL patterns and page references
- `run_terminal_cmd` - Execute file system searches for recent changes

## Complete Section Review Order & Pages

### **Phase 1: Core User Experience (High Priority)**

#### **SECTION 1: Authentication & Security** 🔐
**Priority: CRITICAL** | **Estimated Time: 180+ minutes**

**Baseline Pages to Review (9 minimum):**
- templates/auth/login.html (Main login interface)
- templates/auth/error.html (Authentication error handling)
- templates/auth/reset-password.html (Password reset workflow)
- templates/auth/mfa_setup.html (MFA setup wizard)
- templates/auth/mfa_verify.html (MFA verification)
- templates/auth/mfa_verify_setup.html (MFA setup verification)
- templates/auth/mfa_disable.html (MFA disable process)
- templates/auth/mfa_backup_verify.html (Backup code verification)
- templates/auth/mfa_backup_tokens.html (Backup token management)

**Discovery Keywords:** `auth, login, mfa, password, register, signin, signup, verify, token, reset, security, access`
**Functional Patterns:** User authentication, security workflows, access control, session management
**HTMX Agent Impact:** Check for new MFA flows, enhanced login patterns, security improvements, progressive authentication

**Expected Discoveries:** Enhanced MFA workflows, new security pages, improved error handling, additional authentication methods

#### **SECTION 3: Main Dashboard** 📊
**Priority: HIGH** | **Estimated Time: 90+ minutes**

**Baseline Pages to Review (3 minimum):**
- templates/dashboard/dashboard.html (Main application dashboard)
- templates/CLEAR/dashboard.html (CLEAR module dashboard)
- templates/CLEAR/dashboard_fixed.html (Fixed layout dashboard)

**Discovery Keywords:** `dashboard, home, main, overview, summary, stats, metrics, widgets`
**Functional Patterns:** Data visualization, user home pages, summary views, quick actions
**HTMX Agent Impact:** Check for enhanced dashboard widgets, real-time updates, improved data loading

**Expected Discoveries:** New dashboard variants, enhanced widget systems, improved data presentation

#### **SECTION 4: Project Management** 🏗️
**Priority: HIGH** | **Estimated Time: 160+ minutes**

**Baseline Pages to Review (8+ minimum):**
- templates/projects/projects.html (Project listing)
- templates/projects/project_detail.html (Project detail view)
- templates/projects/my-projects.html (User's projects)
- templates/projects/project-portfolio.html (Project portfolio view)
- templates/CLEAR/my_projects.html (CLEAR module projects)
- templates/CLEAR/project_portfolio.html (CLEAR portfolio)
- templates/projects/partials/ (2 files - Project partial templates)

**Discovery Keywords:** `project, portfolio, task, milestone, timeline, gantt, kanban, workflow`
**Functional Patterns:** Project management, task tracking, collaboration, progress monitoring
**HTMX Agent Impact:** Check for enhanced project workflows, real-time collaboration, improved task management

**Expected Discoveries:** New project views, enhanced collaboration features, improved task management interfaces

#### **SECTION 5: Communication System** 💬
**Priority: HIGH** | **Estimated Time: 120+ minutes**

**Baseline Pages to Review (5+ minimum):**
- templates/messaging/messages.html (Message center)
- templates/messaging/chat_interface.html (Chat interface)
- templates/communication/messages.html (Communication messages)
- templates/CLEAR/ai_communication.html (AI communication)
- templates/CLEAR/ai_communication/ (1 file - AI communication components)

**Discovery Keywords:** `message, chat, communication, conversation, thread, notification, ai, whisper`
**Functional Patterns:** Real-time messaging, AI communication, notification systems, conversation management
**HTMX Agent Impact:** Check for enhanced chat interfaces, improved AI communication, real-time messaging features

**Expected Discoveries:** Enhanced chat interfaces, new AI communication patterns, improved notification systems

### **Phase 2: Administrative & Power Features (Medium Priority)**

#### **SECTION 2: Administrative Interface** 👑
**Priority: HIGH** | **Estimated Time: 400+ minutes**

**Baseline Pages to Review (20+ minimum):**
Core Admin Pages:
- templates/admin/dashboard.html (Admin main dashboard)
- templates/admin/users.html (User management)
- templates/admin/user_management.html (Advanced user management)
- templates/admin/clients.html (Client management)
- templates/admin/organization_settings.html (Organization configuration)

System Management:
- templates/admin/database_management.html (Database administration)
- templates/admin/system_monitoring.html (System health monitoring)
- templates/admin/analytics.html (Admin analytics)
- templates/admin/analytics_dashboard.html (Analytics dashboard)

Components:
- templates/admin/partials/ (17 files - Admin partial templates)
- templates/admin/modals/ (3 files - Admin modal components)

**Discovery Keywords:** `admin, management, system, monitor, config, settings, control, panel, super`
**Functional Patterns:** System administration, user management, configuration, monitoring, analytics
**HTMX Agent Impact:** Check for enhanced admin interfaces, improved system monitoring, better management workflows

**Expected Discoveries:** Enhanced admin workflows, new management interfaces, improved system monitoring

#### **SECTION 8: Analytics & Reporting** 📈
**Priority: MEDIUM** | **Estimated Time: 100 minutes**
```
Pages to Review (4 total):
- templates/reports/reports.html (Reports main page)
- templates/reports/dashboard.html (Reports dashboard)
- templates/reports/executive_dashboard.html (Executive dashboard)
- templates/reports/scheduled_reports.html (Scheduled reports)
```

#### **SECTION 9: Stakeholder Management** 👥
**Priority: MEDIUM** | **Estimated Time: 90 minutes**
```
Pages to Review (3 total):
- templates/stakeholders/stakeholders.html (Stakeholder listing)
- templates/stakeholders/detail.html (Stakeholder detail)
- templates/stakeholders/analytics_dashboard.html (Stakeholder analytics)
```

#### **SECTION 7: GIS & Mapping** 🗺️
**Priority: MEDIUM** | **Estimated Time: 90 minutes**
```
Pages to Review (3+ total):
- templates/mapping/gis.html (GIS interface)
- templates/CLEAR/mapping/ (2 files - CLEAR mapping components)
```

### **Phase 3: Supporting Features (Lower Priority)**

#### **SECTION 6: Document Management** 📄
**Priority: MEDIUM** | **Estimated Time: 60 minutes**
```
Pages to Review (2 total):
- templates/documents/documents.html (Document listing)
- templates/documents/document_workspace.html (Document workspace)
```

#### **SECTION 10: Knowledge Management** 🧠
**Priority: MEDIUM** | **Estimated Time: 180 minutes**
```
Pages to Review (8+ total):
- templates/knowledge/knowledge.html (Knowledge main page)
- templates/knowledge/knowledge_base.html (Knowledge base)
- templates/knowledge_base/article_list.html (Article listing)
- templates/knowledge_base/article_detail.html (Article detail)
- templates/knowledge_base/category_list.html (Category listing)
- templates/knowledge_base/partials/ (6 files - Knowledge base partials)
```

#### **SECTION 14: User Profile Management** 👤
**Priority: MEDIUM** | **Estimated Time: 150 minutes**
```
Pages to Review (6+ total):
- templates/profile/profile.html (User profile)
- templates/CLEAR/profile/ (3 files - CLEAR profile components)
- templates/profiles/partials/ (2 files - Profile partials)
```

#### **SECTION 11: Time Management** ⏰
**Priority: LOW** | **Estimated Time: 60 minutes**
```
Pages to Review (2 total):
- templates/timesheet/timesheet.html (Timesheet interface)
- templates/CLEAR/timesheet.html (CLEAR timesheet)
```

### **Phase 4: Utility & System Features**

#### **SECTION 12: Task Management** ✅
**Priority: LOW** | **Estimated Time: 30 minutes**
```
Pages to Review (1 total):
- templates/tasks/tasks.html (Task management interface)
```

#### **SECTION 13: Digital Notebook** 📓
**Priority: LOW** | **Estimated Time: 30 minutes**
```
Pages to Review (1 total):
- templates/notebook/notebook.html (Digital notebook interface)
```

#### **SECTION 15: Application Settings** ⚙️
**Priority: LOW** | **Estimated Time: 30 minutes**
```
Pages to Review (1 total):
- templates/settings/settings.html (Application settings)
```

#### **SECTION 16: Feature Request System** 💡
**Priority: LOW** | **Estimated Time: 30 minutes**
```
Pages to Review (1 total):
- templates/feature_requests/feature_requests.html (Feature request system)
```

#### **SECTION 17: Template Management** 📋
**Priority: LOW** | **Estimated Time: 150 minutes**
```
Pages to Review (6+ total):
- templates/templates/templates.html (Template listing)
- templates/templates/template-management.html (Template management)
- templates/templates/partials/ (1 file - Template partials)
- templates/templates/advanced_template_functions.js (Advanced functions)
- templates/templates/versioning_functions.js (Version control)
```

#### **SECTION 18: Email Templates** 📧
**Priority: LOW** | **Estimated Time: 150 minutes**
```
Pages to Review (6+ total):
- templates/emails/notification.html (Email notifications)
- templates/emails/notification_batch.html (Batch notifications)
- templates/emails/notification_batch.txt (Text notifications)
- templates/emails/ (3+ additional files - Additional email templates)
```

#### **SECTION 19: Error Pages** ❌
**Priority: LOW** | **Estimated Time: 60 minutes**
```
Pages to Review (2 total):
- templates/error/500.html (Server error page)
- templates/403.html (Forbidden access page)
```

#### **SECTION 20: Reusable Components** 🧩
**Priority: MEDIUM** | **Estimated Time: 800+ minutes**

**Baseline Pages to Review (170+ minimum):
Component Categories:
- templates/components/dashboard/ (15 files)
- templates/components/navigation/ (8 files)
- templates/components/ui/ (15 files)
- templates/components/mapping/ (17 files)
- templates/components/messages/ (25 files)
- templates/components/documents/ (21 files)
- templates/components/projects/ (6 files)
- templates/components/reports/ (5 files)
- templates/components/ai_communication/ (6 files)
- templates/components/timeline/ (5 files)
- templates/components/timer/ (5 files)
- templates/components/timesheet/ (3 files)
- templates/components/whispers/ (7 files)
- templates/components/notifications/ (4 files)
- templates/components/settings/ (3 files)
- templates/components/profile/ (2 files)
- templates/components/comments/ (6 files)

Plus 25+ standalone components including:
- templates/components/error_message.html
- templates/components/dashboard_stats.html
- templates/components/data_table.html
- templates/components/recent_activity.html
[All other standalone components]

**Discovery Keywords:** `component, partial, widget, element, reusable, shared, common`
**Functional Patterns:** Reusable UI components, shared functionality, modular design, component libraries
**HTMX Agent Impact:** Check for new reusable components, enhanced widget systems, improved modular design

**Expected Discoveries:** Many new reusable components, enhanced widget systems, improved modular architecture

#### **SECTION 22: Test & Utility Pages** 🔧
**Priority: LOW** | **Estimated Time: 90 minutes**
```
Pages to Review (3+ total):
- templates/test_login.html (Test login page)
- templates/test_websocket.html (WebSocket test page)
- templates/utilities/ (Utility templates directory)
```

#### **SECTION 21: Base Templates** 🏠 ⚠️ **HANDLE LAST - AFFECTS ENTIRE APP**
**Priority: CRITICAL** | **Estimated Time: 120+ minutes**

**Baseline Pages to Review (2 minimum):**
- templates/base.html (Main base template)
- templates/base/base.html (Alternative base template)

**Discovery Keywords:** `base, layout, master, main, core, foundation, structure`
**Functional Patterns:** Base template systems, layout structures, shared components, global styling
**HTMX Agent Impact:** Check for enhanced base templates, improved layout systems, better shared components

**Expected Discoveries:** Enhanced base templates, improved layout systems, better global architecture

## Systematic Review Process

### Initial Setup & GraphQL Initialization
```bash
# Ensure server is running
python manage.py runserver 0.0.0.0:8000

# Analysis directory structure already exists at:
# coty-playground/Cursor-Background-Agent-Reviews/UI-UX-Review/Background-Agents-Files/ui-ux-analysis/
# (with subdirectories: screenshots/, examples/, analysis/, implementation/, discovery/)

# Initialize GraphQL tracking
# Execute GraphQL mutation to initialize review session
```

**🔄 UPDATE GRAPHQL:** Initialize review session with `initializeReviewSession` mutation

### For Each Section (Work Through All 22 Sections in Order):

#### Step 0: Dynamic Page Discovery & Inventory
**CRITICAL:** Before starting each section, perform comprehensive page discovery:

**🔄 UPDATE GRAPHQL:** Use `startSection(sectionNumber: X)` mutation to begin section tracking

**Discovery Process:**
1. **Scan templates directory** for the current section's functional area:
   ```bash
   # Use discovery keywords from section definition
   find templates/ -name "*.html" | grep -E "(keyword1|keyword2|keyword3)"
   
   # Check for pattern-based matches
   find templates/ -name "*.html" -exec grep -l "hx-" {} \; | grep -E "[section-pattern]"
   ```

2. **Cross-reference with URL patterns** to find active pages:
   ```bash
   grep -r "path\|url" */urls.py | grep -E "[section-keywords]"
   ```

3. **Check for recently modified templates** (catch new pages from HTMX agents):
   ```bash
   find templates/ -name "*.html" -mtime -30 | sort
   ```

4. **Validate page accessibility** by attempting to navigate to each discovered page

5. **Document discovery results**:
   - **Baseline Pages**: [List from original prompt] ✓ Known
   - **Newly Discovered Pages**: [List any additional pages found] 🆕 New  
   - **Inaccessible Pages**: [Pages that exist but can't be reached] ⚠️ Issue
   - **Section Reassignments**: [Pages that belong in different sections] 🔄 Move

**Save discovery results as**: `coty-playground/Cursor-Background-Agent-Reviews/UI-UX-Review/Background-Agents-Files/ui-ux-analysis/discovery/section-[XX]-discovery-report.md`

**🔄 UPDATE GRAPHQL:** Update section with `totalPages` count and add any new pages with mutations

#### Step 1: Section Initialization
**🔄 UPDATE GRAPHQL:** Update section status to `SCREENSHOTS_IN_PROGRESS`

#### Step 2: Screenshot Capture & Initial Scoring
For every page in the current section (baseline + discovered):

1. **Navigate to page** at `http://localhost:8000/[page-url]`
2. **Capture full-page screenshots** at:
   - Desktop: 1920x1080
   - Tablet: 768x1024
   - Mobile: 375x667
3. **Save as**: `coty-playground/Cursor-Background-Agent-Reviews/UI-UX-Review/Background-Agents-Files/ui-ux-analysis/screenshots/section-[XX]-[page-name]-[resolution].png`
4. **Document page state** (logged in user, sample data, etc.)

**🔄 UPDATE GRAPHQL:** Use `addScreenshot` mutation for each screenshot captured

#### Step 3: Initial UI/UX Scoring
Score each page 1-10 in these categories:

**Structural Assessment (40% weight)**
- **Layout Organization** (logical flow, clear hierarchy)
- **Navigation Clarity** (intuitive, consistent)
- **Content Structure** (readable, scannable, well-organized)
- **Responsive Design** (works across all device sizes)

**Visual Design Assessment (30% weight)**
- **Visual Hierarchy** (clear importance levels)
- **Color Usage** (consistent, accessible, purposeful)
- **Typography** (readable, consistent, appropriate)
- **Spacing & Alignment** (clean, balanced, professional)

**User Experience Assessment (30% weight)**
- **Ease of Use** (intuitive, efficient workflows)
- **Loading States** (clear feedback, no confusion)
- **Error Handling** (helpful, clear, recoverable)
- **Accessibility** (keyboard nav, screen readers, contrast)

**Calculate Initial Score:**
```
Initial Score = (Structural × 0.4) + (Visual × 0.3) + (UX × 0.3)
Section Average = Sum of all page scores / Number of pages
```

**🔄 UPDATE GRAPHQL:** Use `addInitialScoring` mutation with detailed scoring data

#### Step 4: Comparative Example Research
For each page type in the section, find 20 high-quality examples:

**Search Strategy by Page Type:**
Use the discovery keywords from each section definition to create targeted searches:
- **Authentication pages**: "login interface design", "MFA setup UI", "password reset flow"
- **Dashboard pages**: "admin dashboard design", "project dashboard UI", "analytics dashboard"
- **Admin pages**: "admin panel design", "user management interface", "system monitoring UI"
- **Communication pages**: "chat interface design", "messaging app UI", "communication dashboard"
- **And so on for each section...**

**Search Sources (Priority Order):**
1. **Dribbble**: `site:dribbble.com [functionality] interface`
2. **Behance**: `site:behance.net [page-type] dashboard`
3. **UI Movement**: `site:uimovement.com [feature]`
4. **Page Flows**: `site:pageflows.com [user-flow]`
5. **Mobbin**: `site:mobbin.design [functionality]`
6. **SaaS Examples**: Industry-specific applications

**🔄 UPDATE GRAPHQL:** Use `startExampleResearch` mutation to begin research tracking

**Example Scoring System:**
For each example found, score 1-10:

**Structural & Contextual Alignment (60% weight)**
- **Functional Similarity** (performs same/similar tasks as our page)
- **User Flow Alignment** (matches our app's intended workflow)
- **Information Architecture** (similar data organization needs)
- **Interaction Patterns** (compatible with HTMX approach)
- **Complexity Level** (appropriate for our use case)

**UI/UX Quality Assessment (40% weight)**
- **Visual Design Quality** (professional, modern, polished)
- **Layout Effectiveness** (clear, organized, efficient)
- **User Experience Flow** (intuitive, smooth, logical)
- **Accessibility Considerations** (inclusive design principles)

**Calculate Example Relevance Score:**
```
Example Score = (Structural × 0.6) + (Quality × 0.4)
Minimum Threshold: 7.0/10
Keep Top 20 Examples Only
```

**Save examples as**: `coty-playground/Cursor-Background-Agent-Reviews/UI-UX-Review/Background-Agents-Files/ui-ux-analysis/examples/section-[XX]-example-[number]-score-[X.X].png`

**🔄 UPDATE GRAPHQL:** Use `addExample` mutation for each qualifying example

#### Step 5: Detailed Comparative Analysis
For each qualifying example (score ≥ 7.0):

**Structural Analysis:**
- **Layout Comparison** (grid systems, component placement)
- **Navigation Patterns** (menu styles, user flow improvements)
- **Content Organization** (better information hierarchy)
- **Responsive Strategies** (mobile-first improvements)

**HTMX Compatibility Assessment:**
- **Interaction Patterns** (achievable with HTMX)
- **Progressive Enhancement** (works without JavaScript)
- **Hypermedia Principles** (aligns with `docs/hypermedia-systems-complete.md`)
- **Alpine.js Requirements** (minimal JavaScript needs)

**Implementation Feasibility:**
- **Bootstrap 5.x Compatibility** (uses compatible patterns)
- **Development Complexity** (realistic with current tech stack)
- **Performance Impact** (efficient, fast-loading)
- **Maintenance Burden** (sustainable long-term)

**🔄 UPDATE GRAPHQL:** Use `analyzeExample` mutation for detailed analysis data

#### Step 6: Comment Synthesis & Mathematical Prioritization
Group all improvement comments into categories:

**Structural Improvements**
- Layout reorganization needs
- Navigation enhancements
- Information architecture changes
- Responsive design fixes

**Visual Design Improvements**
- Color scheme updates
- Typography improvements
- Spacing and alignment fixes
- Visual hierarchy enhancements

**User Experience Improvements**
- Workflow optimizations
- Loading state improvements
- Error handling enhancements
- Accessibility upgrades

**Technical Implementation Needs**
- HTMX pattern requirements
- Bootstrap component usage
- Alpine.js integration (minimal)
- Performance optimizations

**Mathematical Prioritization Formula:**
For each improvement comment:
```
Priority Score = (UX Impact × 0.3) + (Business Value × 0.25) + 
                (Implementation Feasibility × 0.2) + (Maintenance Benefit × 0.15) + 
                (Low Effort Bonus × 0.1)

Where each factor is scored 1-10:
- UX Impact: How much it improves user experience
- Business Value: ROI and business benefit
- Implementation Feasibility: How realistic with current tech stack
- Maintenance Benefit: Long-term code quality improvement
- Low Effort Bonus: Bonus points for quick wins

Priority Levels:
- High Priority: 8.0-10.0 (Implement immediately)
- Medium Priority: 6.0-7.9 (Implement in phase 2)
- Low Priority: 4.0-5.9 (Consider for future)
- Discard: <4.0 (Don't implement)
```

**🔄 UPDATE GRAPHQL:** Use `addImprovementSuggestion` and `prioritizeSuggestions` mutations

#### Step 7: Technology Stack Decision Framework
For each high/medium priority improvement:

**HTMX/Bootstrap/Alpine.js Evaluation:**
```
Can this be achieved with HTMX + Bootstrap alone?
├─ YES: Pure HTMX/Bootstrap implementation ✅
└─ NO: Evaluate Alpine.js necessity
    ├─ Complex client-side state needed?
    │   ├─ YES: Minimal Alpine.js integration ⚠️
    │   └─ NO: Reconsider design approach
    └─ Alternative HTMX pattern available?
        ├─ YES: Use HTMX alternative ✅
        └─ NO: Document as technical debt
```

**Hypermedia Systems Compliance Check:**
Reference `docs/hypermedia-systems-complete.md` to ensure:
- Follows REST principles
- Maintains server-side state management
- Uses hypermedia controls for navigation
- Supports graceful degradation
- Implements proper error handling

#### Step 8: Implementation & Final Scoring
For each section:

**Create Implementation Branch:**
```bash
git checkout -b ui-ux-improvements-section-[XX]
```

**🔄 UPDATE GRAPHQL:** Use `startImplementation` mutation with branch name

**Implement by Priority Order (High Priority First):**
1. **Backup original files**
2. **Implement using HTMX/Bootstrap patterns**
3. **Test responsive behavior**
4. **Verify HTMX functionality**
5. **Add Alpine.js only if absolutely necessary**
6. **Document changes made**

**🔄 UPDATE GRAPHQL:** Use `addImplementedSuggestion`, `addHTMXPattern`, `addBootstrapComponent`, `addAlpineJSUsage` mutations during implementation

**Implementation Standards:**
```html
<!-- HTMX Patterns (Primary) -->
<div hx-get="/api/data" hx-target="#content" hx-trigger="load">
    <div class="spinner-border" role="status"></div>
</div>

<!-- Bootstrap 5.x Integration -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6">
            <!-- Responsive grid -->
        </div>
    </div>
</div>

<!-- Alpine.js (Minimal Usage Only) -->
<div x-data="{ open: false }" x-show="open">
    <!-- Use sparingly -->
</div>
```

**Re-Score All Pages in Section:**
Using same initial scoring framework, calculate:
```
Final Score = (New Structural × 0.4) + (New Visual × 0.3) + (New UX × 0.3)
Section Improvement = Final Section Average - Initial Section Average
Percentage Improvement = (Improvement / Initial Average) × 100%
```

**🔄 UPDATE GRAPHQL:** Use `addFinalScoring` mutation with final scores

#### Step 9: Section Completion Verification & Cross-Section Check

**Post-Section Discovery Sweep:**
1. **Re-scan for missed pages** in current section's functional area
2. **Check for orphaned pages** that don't clearly belong to any section
3. **Validate no duplicate coverage** with other sections
4. **Document any pages that should be moved** to different sections

**Cross-Section Validation:**
```bash
# Find any templates that might belong to this section but weren't covered
find templates/ -name "*.html" -exec grep -l "[section-specific-keywords]" {} \;

# Check for pages with similar functionality patterns
grep -r "hx-" templates/ | grep -E "[section-functionality]" | cut -d: -f1 | sort -u
```

**Section Completion Criteria:**
- ✅ All baseline pages reviewed and improved
- ✅ All newly discovered pages reviewed and improved  
- ✅ No orphaned pages in functional area
- ✅ No overlap with other sections
- ✅ Cross-section validation complete

**🔄 UPDATE GRAPHQL:** Use `completeSectionWithNotes` mutation with:
- Section completion status
- Lessons learned
- Recommendations for next sections
- Final statistics
- Discovery summary

For each completed section, create:
`coty-playground/Cursor-Background-Agent-Reviews/UI-UX-Review/Background-Agents-Files/ui-ux-analysis/analysis/section-[XX]-[SECTION_NAME]-report.md`

## Final Deliverables & Overall Progress

### Complete Analysis Report
The GraphQL schema will maintain comprehensive tracking of:
- **Overall Statistics**: Total pages (baseline + discovered), average improvements, implementation time
- **Section-by-Section Results**: Detailed progress and outcomes including discovery results
- **Cross-Section Patterns**: Reusable improvements and common challenges
- **Technical Implementation**: HTMX patterns, Bootstrap usage, Alpine.js minimal usage
- **Performance Impact**: Before/after metrics and analysis
- **Discovery Summary**: New pages found, patterns identified, architectural insights

### Success Criteria
Your complete UI/UX review is successful when:
- ✅ All 22 sections systematically reviewed in priority order
- ✅ Dynamic page discovery performed for each section
- ✅ Every page (baseline + discovered) scored initially and finally with measurable improvement
- ✅ 20+ relevant examples found and analyzed for each page type
- ✅ Mathematical prioritization applied to all improvement suggestions
- ✅ High-priority improvements implemented using HTMX/Bootstrap/Alpine.js
- ✅ All changes follow hypermedia systems principles from `docs/hypermedia-systems-complete.md`
- ✅ Complete GraphQL schema populated with granular decision tracking
- ✅ Cross-section patterns and recommendations documented
- ✅ Discovery insights documented for future development
- ✅ Final statistical analysis shows significant UI/UX improvements across entire application

## Tools Available
Use these tools throughout the process:
- `read_file` - Analyze template files and documentation
- `grep_search` - Find patterns across the codebase
- `list_dir` - Explore directory structures
- `codebase_search` - Search for specific implementations
- `web_search` - Find comparative examples and references

## Critical Reminders
1. **Always perform dynamic page discovery** before starting each section
2. **Always update the GraphQL schema** at the specified intervals
3. **Reference the GraphQL schema structure** for consistent data tracking
4. **Follow the exact section order** provided in this prompt
5. **Maintain mathematical rigor** in all scoring and prioritization
6. **Document every granular decision** for future reference
7. **Ensure all changes align with HTMX/Bootstrap/Alpine.js architecture**
8. **Reference `docs/hypermedia-systems-complete.md`** for hypermedia compliance
9. **Validate section completion** with cross-section checks
10. **Document discovery insights** for architectural improvements

Begin by initializing the GraphQL tracking system, then systematically work through each section in the specified priority order with dynamic page discovery. This comprehensive application-wide UI/UX transformation will provide complete visibility into every decision made, every page discovered, and every improvement implemented.