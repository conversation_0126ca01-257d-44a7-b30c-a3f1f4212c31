# UI/UX Analysis Directory Structure

This directory contains all artifacts generated during the comprehensive UI/UX review and enhancement process.

## Directory Structure

```
ui-ux-analysis/
├── discovery/          # Page discovery reports for each section
├── screenshots/        # Full-page screenshots (Desktop/Tablet/Mobile)
├── examples/          # Comparative UI/UX examples from research
├── analysis/          # Section analysis reports and findings
├── implementation/    # Implementation tracking and code changes
└── README.md         # This file
```

## Directory Purposes

### 📁 discovery/
- **Purpose**: Contains discovery reports for each of the 22 sections
- **Files**: `section-[XX]-discovery-report.md`
- **Content**: 
  - Baseline pages vs newly discovered pages
  - Discovery method and keywords used
  - Page accessibility validation results
  - Section reassignments and orphaned pages

### 📁 screenshots/
- **Purpose**: Stores full-page screenshots for all reviewed pages
- **Files**: `section-[XX]-[page-name]-[resolution].png`
- **Resolutions**: 
  - Desktop: 1920x1080
  - Tablet: 768x1024
  - Mobile: 375x667

### 📁 examples/
- **Purpose**: Comparative UI/UX examples found during research
- **Files**: `section-[XX]-example-[number]-score-[X.X].png`
- **Content**: High-quality examples (score ≥ 7.0) for inspiration and analysis

### 📁 analysis/
- **Purpose**: Comprehensive analysis reports for each completed section
- **Files**: `section-[XX]-[SECTION_NAME]-report.md`
- **Content**:
  - Initial vs final scoring results
  - Improvement recommendations and prioritization
  - Implementation decisions and rationale
  - Lessons learned and cross-section insights

### 📁 implementation/
- **Purpose**: Implementation tracking and code change documentation
- **Content**:
  - Git branch information
  - Code change summaries
  - HTMX pattern implementations
  - Bootstrap component usage
  - Alpine.js integration notes
  - Performance impact measurements

## Usage

This directory structure is automatically created and populated by the UI/UX Comprehensive Review Background Agent. The agent follows a systematic 9-step process for each of the 22 application sections:

1. **Dynamic Page Discovery** → `discovery/`
2. **Screenshot Capture** → `screenshots/`
3. **Initial Scoring** → Tracked in GraphQL
4. **Example Research** → `examples/`
5. **Comparative Analysis** → `analysis/`
6. **Mathematical Prioritization** → `analysis/`
7. **Technology Decisions** → `analysis/`
8. **Implementation** → `implementation/`
9. **Section Completion** → `analysis/`

## GraphQL Integration

All progress, scoring, and decision data is tracked in the GraphQL schema located at:
`../ui-ux-review-schema.graphql`

## Review Process

The review follows this priority order:
1. **Phase 1**: Core User Experience (Auth, Dashboard, Projects, Communication)
2. **Phase 2**: Administrative & Power Features (Admin, Analytics, GIS, etc.)
3. **Phase 3**: Supporting Features (Documents, Knowledge, Profiles, etc.)
4. **Phase 4**: Utility & System Features (Tasks, Settings, Components, etc.)

Each section includes dynamic page discovery to catch new pages created by previous HTMX/HyperMedia compliance agents. 