# UI/UX Review Progress Tracking Schema
# This GraphQL schema tracks every granular decision, score, and progress
# for the comprehensive UI/UX review and enhancement process

scalar DateTime
scalar JSON

# Main review session tracking
type ReviewSession {
  id: ID!
  startDate: DateTime!
  endDate: DateTime
  status: ReviewStatus!
  agentVersion: String!
  totalSections: Int!
  completedSections: Int!
  totalPages: Int!
  reviewedPages: Int!
  overallProgress: Float! # Percentage 0-100
  currentSection: Section
  sections: [Section!]!
  globalStats: GlobalStats!
  createdAt: DateTime!
  updatedAt: DateTime!
}

enum ReviewStatus {
  NOT_STARTED
  IN_PROGRESS
  PAUSED
  COMPLETED
  FAILED
}

# Global statistics across all sections
type GlobalStats {
  averageInitialScore: Float!
  averageFinalScore: Float!
  overallImprovement: Float!
  totalImplementationTime: Int! # minutes
  totalExamplesAnalyzed: Int!
  totalHTMXPatternsAdded: Int!
  totalBootstrapComponentsUsed: Int!
  totalAlpineJSUsage: Int!
  mostEffectiveImprovements: [String!]!
  commonChallenges: [String!]!
  reusablePatterns: [String!]!
}

# Section-level tracking
type Section {
  id: ID!
  sectionNumber: Int!
  name: String!
  emoji: String!
  priority: SectionPriority!
  status: SectionStatus!
  startDate: DateTime
  endDate: DateTime
  estimatedTimeMinutes: Int!
  actualTimeMinutes: Int
  totalPages: Int!
  completedPages: Int!
  averageInitialScore: Float
  averageFinalScore: Float
  sectionImprovement: Float
  pages: [Page!]!
  sectionNotes: String
  lessonsLearned: [String!]!
  nextSectionRecommendations: [String!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

enum SectionPriority {
  HIGH
  MEDIUM
  LOW
  CRITICAL
}

enum SectionStatus {
  NOT_STARTED
  SCREENSHOTS_IN_PROGRESS
  INITIAL_SCORING_IN_PROGRESS
  EXAMPLE_RESEARCH_IN_PROGRESS
  COMPARATIVE_ANALYSIS_IN_PROGRESS
  PRIORITIZATION_IN_PROGRESS
  IMPLEMENTATION_IN_PROGRESS
  FINAL_SCORING_IN_PROGRESS
  DOCUMENTATION_IN_PROGRESS
  COMPLETED
  FAILED
}

# Page-level tracking with comprehensive details
type Page {
  id: ID!
  templatePath: String!
  pageName: String!
  pageType: PageType!
  url: String
  section: Section!
  status: PageStatus!
  startDate: DateTime
  endDate: DateTime
  implementationTimeMinutes: Int
  
  # Screenshot tracking
  screenshots: [Screenshot!]!
  
  # Initial scoring
  initialScoring: Scoring
  
  # Example research and analysis
  exampleResearch: ExampleResearch
  
  # Improvement analysis
  improvementAnalysis: ImprovementAnalysis
  
  # Implementation tracking
  implementation: Implementation
  
  # Final scoring
  finalScoring: Scoring
  
  # Overall page results
  overallImprovement: Float
  percentageImprovement: Float
  keyChanges: [String!]!
  
  # Notes and documentation
  pageNotes: String
  challenges: [String!]!
  successes: [String!]!
  
  createdAt: DateTime!
  updatedAt: DateTime!
}

enum PageType {
  AUTHENTICATION
  DASHBOARD
  ADMIN
  FORM
  LIST
  DETAIL
  COMMUNICATION
  MAPPING
  ANALYTICS
  COMPONENT
  ERROR
  UTILITY
}

enum PageStatus {
  NOT_STARTED
  SCREENSHOT_CAPTURED
  INITIAL_SCORED
  EXAMPLES_RESEARCHED
  EXAMPLES_ANALYZED
  IMPROVEMENTS_PRIORITIZED
  IMPLEMENTATION_PLANNED
  IMPLEMENTATION_IN_PROGRESS
  IMPLEMENTATION_COMPLETED
  FINAL_SCORED
  DOCUMENTED
  COMPLETED
}

# Screenshot tracking for each device size
type Screenshot {
  id: ID!
  device: DeviceType!
  resolution: String!
  filePath: String!
  fileSize: Int!
  captureDate: DateTime!
  pageState: String # Description of page state when captured
  hasErrors: Boolean!
  errorDetails: String
}

enum DeviceType {
  DESKTOP
  TABLET
  MOBILE
}

# Comprehensive scoring system
type Scoring {
  id: ID!
  scoringDate: DateTime!
  scoringType: ScoringType!
  
  # Structural Assessment (40% weight)
  structural: StructuralScoring!
  
  # Visual Design Assessment (30% weight)
  visual: VisualScoring!
  
  # User Experience Assessment (30% weight)
  userExperience: UXScoring!
  
  # Calculated scores
  structuralWeighted: Float! # structural * 0.4
  visualWeighted: Float! # visual * 0.3
  uxWeighted: Float! # ux * 0.3
  totalScore: Float! # Sum of weighted scores
  
  # Additional notes
  scoringNotes: String
  scoringRationale: String
}

enum ScoringType {
  INITIAL
  FINAL
  EXAMPLE
}

type StructuralScoring {
  layoutOrganization: Int! # 1-10
  navigationClarity: Int! # 1-10
  contentStructure: Int! # 1-10
  responsiveDesign: Int! # 1-10
  averageScore: Float!
  notes: String
}

type VisualScoring {
  visualHierarchy: Int! # 1-10
  colorUsage: Int! # 1-10
  typography: Int! # 1-10
  spacingAlignment: Int! # 1-10
  averageScore: Float!
  notes: String
}

type UXScoring {
  easeOfUse: Int! # 1-10
  loadingStates: Int! # 1-10
  errorHandling: Int! # 1-10
  accessibility: Int! # 1-10
  averageScore: Float!
  notes: String
}

# Example research and analysis
type ExampleResearch {
  id: ID!
  researchDate: DateTime!
  searchQueries: [String!]!
  totalExamplesFound: Int!
  qualifyingExamples: Int! # Examples with score >= 7.0
  examples: [Example!]!
  researchNotes: String
  searchStrategy: String
}

type Example {
  id: ID!
  exampleNumber: Int!
  source: ExampleSource!
  sourceUrl: String
  imageFilePath: String!
  description: String!
  
  # Example scoring
  structuralAlignment: StructuralAlignment!
  qualityAssessment: QualityAssessment!
  relevanceScore: Float! # (structural * 0.6) + (quality * 0.4)
  
  # Analysis results
  analysis: ExampleAnalysis
  
  qualified: Boolean! # Score >= 7.0
  selected: Boolean! # Used for improvement recommendations
  
  createdAt: DateTime!
}

enum ExampleSource {
  DRIBBBLE
  BEHANCE
  UI_MOVEMENT
  PAGE_FLOWS
  MOBBIN
  SAAS_EXAMPLE
  OTHER
}

type StructuralAlignment {
  functionalSimilarity: Int! # 1-10
  userFlowAlignment: Int! # 1-10
  informationArchitecture: Int! # 1-10
  interactionPatterns: Int! # 1-10
  complexityLevel: Int! # 1-10
  averageScore: Float!
  notes: String
}

type QualityAssessment {
  visualDesignQuality: Int! # 1-10
  layoutEffectiveness: Int! # 1-10
  userExperienceFlow: Int! # 1-10
  accessibilityConsiderations: Int! # 1-10
  averageScore: Float!
  notes: String
}

type ExampleAnalysis {
  id: ID!
  analysisDate: DateTime!
  
  # Detailed comparisons
  layoutComparison: String!
  navigationPatterns: String!
  contentOrganization: String!
  responsiveStrategy: String!
  
  # HTMX compatibility
  htmxCompatibility: HTMXCompatibility!
  
  # Implementation feasibility
  implementationFeasibility: ImplementationFeasibility!
  
  # Specific improvement suggestions
  improvementSuggestions: [ImprovementSuggestion!]!
}

type HTMXCompatibility {
  pureHTMXAchievable: Boolean!
  progressiveEnhancement: Boolean!
  hypermediaCompliant: Boolean!
  alpineJSRequired: Boolean!
  compatibilityScore: Int! # 1-10
  compatibilityNotes: String
}

type ImplementationFeasibility {
  developmentComplexity: Int! # 1-10 (lower is easier)
  performanceImpact: Int! # 1-10 (higher is better)
  maintenanceBurden: Int! # 1-10 (higher is easier)
  businessValue: Int! # 1-10 (higher is more valuable)
  feasibilityScore: Float!
  feasibilityNotes: String
}

# Improvement analysis and prioritization
type ImprovementAnalysis {
  id: ID!
  analysisDate: DateTime!
  totalSuggestions: Int!
  categorizedSuggestions: CategorizedSuggestions!
  prioritizedSuggestions: [PrioritizedSuggestion!]!
  implementationPlan: ImplementationPlan!
}

type CategorizedSuggestions {
  structural: [ImprovementSuggestion!]!
  visual: [ImprovementSuggestion!]!
  userExperience: [ImprovementSuggestion!]!
  technical: [ImprovementSuggestion!]!
}

type ImprovementSuggestion {
  id: ID!
  category: ImprovementCategory!
  title: String!
  description: String!
  sourceExample: Example
  priority: ImprovementPriority
  priorityScore: Float
  estimatedTimeMinutes: Int
  implemented: Boolean!
  implementationNotes: String
}

enum ImprovementCategory {
  STRUCTURAL
  VISUAL
  USER_EXPERIENCE
  TECHNICAL
}

enum ImprovementPriority {
  HIGH # 8.0-10.0
  MEDIUM # 6.0-7.9
  LOW # 4.0-5.9
  DISCARD # <4.0
}

type PrioritizedSuggestion {
  suggestion: ImprovementSuggestion!
  priorityCalculation: PriorityCalculation!
  technologyDecision: TechnologyDecision!
  complianceCheck: ComplianceCheck!
}

type PriorityCalculation {
  uxImpact: Int! # 1-10 (30% weight)
  businessValue: Int! # 1-10 (25% weight)
  implementationFeasibility: Int! # 1-10 (20% weight)
  maintenanceBenefit: Int! # 1-10 (15% weight)
  lowEffortBonus: Int! # 1-10 (10% weight)
  finalPriorityScore: Float!
  calculationNotes: String
}

type TechnologyDecision {
  htmxBootstrapOnly: Boolean!
  requiresAlpineJS: Boolean!
  alternativeHTMXPattern: String
  technologyNotes: String
  decisionRationale: String
}

type ComplianceCheck {
  followsRESTPrinciples: Boolean!
  serverSideState: Boolean!
  hypermediaControls: Boolean!
  gracefulDegradation: Boolean!
  properErrorHandling: Boolean!
  complianceScore: Int! # 1-10
  complianceNotes: String
}

type ImplementationPlan {
  id: ID!
  planDate: DateTime!
  highPriorityItems: [PrioritizedSuggestion!]!
  mediumPriorityItems: [PrioritizedSuggestion!]!
  lowPriorityItems: [PrioritizedSuggestion!]!
  discardedItems: [PrioritizedSuggestion!]!
  estimatedTotalTime: Int! # minutes
  implementationOrder: [String!]! # IDs in order
  planNotes: String
}

# Implementation tracking
type Implementation {
  id: ID!
  implementationDate: DateTime!
  branchName: String!
  implementedSuggestions: [ImplementedSuggestion!]!
  htmxPatterns: [HTMXPattern!]!
  bootstrapComponents: [BootstrapComponent!]!
  alpineJSUsage: [AlpineJSUsage!]!
  performanceImpact: PerformanceImpact!
  implementationChallenges: [String!]!
  implementationSuccesses: [String!]!
  codeChanges: CodeChanges!
}

type ImplementedSuggestion {
  suggestion: ImprovementSuggestion!
  implementationDate: DateTime!
  actualTimeMinutes: Int!
  implementationMethod: String!
  codeChanges: String!
  testingNotes: String!
  success: Boolean!
  issues: [String!]!
}

type HTMXPattern {
  patternType: HTMXPatternType!
  implementation: String!
  targetElement: String
  triggerEvent: String
  swapStrategy: String
  additionalAttributes: JSON
}

enum HTMXPatternType {
  DYNAMIC_LOADING
  FORM_HANDLING
  NAVIGATION
  REAL_TIME_UPDATES
  PROGRESSIVE_ENHANCEMENT
  LAZY_LOADING
  INFINITE_SCROLL
  MODAL_HANDLING
  OTHER
}

type BootstrapComponent {
  componentType: BootstrapComponentType!
  implementation: String!
  customizations: String
  responsiveClasses: [String!]!
}

enum BootstrapComponentType {
  GRID_SYSTEM
  NAVIGATION
  FORMS
  BUTTONS
  CARDS
  MODALS
  ALERTS
  BADGES
  DROPDOWNS
  PAGINATION
  PROGRESS
  SPINNERS
  TOOLTIPS
  POPOVERS
  OTHER
}

type AlpineJSUsage {
  usageReason: String!
  implementation: String!
  complexity: AlpineJSComplexity!
  justification: String!
}

enum AlpineJSComplexity {
  SIMPLE # Basic x-data, x-show
  MODERATE # Some logic, multiple directives
  COMPLEX # Advanced patterns, multiple components
}

type PerformanceImpact {
  loadTimeChange: Float # milliseconds (+/-)
  bundleSizeChange: Float # KB (+/-)
  renderTimeChange: Float # milliseconds (+/-)
  performanceScore: Int # 1-10 (higher is better)
  performanceNotes: String
}

type CodeChanges {
  filesModified: Int!
  linesAdded: Int!
  linesRemoved: Int!
  linesModified: Int!
  newFiles: Int!
  deletedFiles: Int!
  commitHash: String
  commitMessage: String
}

# Progress tracking queries and mutations
type Query {
  # Get current review session
  currentReviewSession: ReviewSession
  
  # Get specific section
  section(sectionNumber: Int!): Section
  
  # Get specific page
  page(templatePath: String!): Page
  
  # Get progress statistics
  progressStats: GlobalStats
  
  # Get sections by status
  sectionsByStatus(status: SectionStatus!): [Section!]!
  
  # Get pages by status
  pagesByStatus(status: PageStatus!): [Page!]!
  
  # Search functionality
  searchPages(query: String!): [Page!]!
  searchExamples(query: String!): [Example!]!
}

type Mutation {
  # Initialize new review session
  initializeReviewSession: ReviewSession!
  
  # Update review session status
  updateReviewSessionStatus(status: ReviewStatus!): ReviewSession!
  
  # Section management
  startSection(sectionNumber: Int!): Section!
  updateSectionStatus(sectionNumber: Int!, status: SectionStatus!): Section!
  completeSectionWithNotes(sectionNumber: Int!, notes: String, lessonsLearned: [String!], recommendations: [String!]): Section!
  
  # Page management
  startPage(templatePath: String!): Page!
  updatePageStatus(templatePath: String!, status: PageStatus!): Page!
  
  # Screenshot management
  addScreenshot(templatePath: String!, device: DeviceType!, resolution: String!, filePath: String!, pageState: String): Screenshot!
  
  # Scoring
  addInitialScoring(templatePath: String!, scoring: ScoringInput!): Scoring!
  addFinalScoring(templatePath: String!, scoring: ScoringInput!): Scoring!
  
  # Example research
  startExampleResearch(templatePath: String!, searchQueries: [String!]!): ExampleResearch!
  addExample(templatePath: String!, example: ExampleInput!): Example!
  analyzeExample(exampleId: ID!, analysis: ExampleAnalysisInput!): ExampleAnalysis!
  
  # Improvement analysis
  addImprovementSuggestion(templatePath: String!, suggestion: ImprovementSuggestionInput!): ImprovementSuggestion!
  prioritizeSuggestions(templatePath: String!): ImprovementAnalysis!
  
  # Implementation
  startImplementation(templatePath: String!, branchName: String!): Implementation!
  addImplementedSuggestion(templatePath: String!, implemented: ImplementedSuggestionInput!): ImplementedSuggestion!
  addHTMXPattern(templatePath: String!, pattern: HTMXPatternInput!): HTMXPattern!
  addBootstrapComponent(templatePath: String!, component: BootstrapComponentInput!): BootstrapComponent!
  addAlpineJSUsage(templatePath: String!, usage: AlpineJSUsageInput!): AlpineJSUsage!
  updatePerformanceImpact(templatePath: String!, impact: PerformanceImpactInput!): PerformanceImpact!
  
  # Complete page
  completePage(templatePath: String!, overallNotes: String, challenges: [String!], successes: [String!]): Page!
}

# Input types for mutations
input ScoringInput {
  structural: StructuralScoringInput!
  visual: VisualScoringInput!
  userExperience: UXScoringInput!
  scoringNotes: String
  scoringRationale: String
}

input StructuralScoringInput {
  layoutOrganization: Int!
  navigationClarity: Int!
  contentStructure: Int!
  responsiveDesign: Int!
  notes: String
}

input VisualScoringInput {
  visualHierarchy: Int!
  colorUsage: Int!
  typography: Int!
  spacingAlignment: Int!
  notes: String
}

input UXScoringInput {
  easeOfUse: Int!
  loadingStates: Int!
  errorHandling: Int!
  accessibility: Int!
  notes: String
}

input ExampleInput {
  exampleNumber: Int!
  source: ExampleSource!
  sourceUrl: String
  imageFilePath: String!
  description: String!
  structuralAlignment: StructuralAlignmentInput!
  qualityAssessment: QualityAssessmentInput!
}

input StructuralAlignmentInput {
  functionalSimilarity: Int!
  userFlowAlignment: Int!
  informationArchitecture: Int!
  interactionPatterns: Int!
  complexityLevel: Int!
  notes: String
}

input QualityAssessmentInput {
  visualDesignQuality: Int!
  layoutEffectiveness: Int!
  userExperienceFlow: Int!
  accessibilityConsiderations: Int!
  notes: String
}

input ExampleAnalysisInput {
  layoutComparison: String!
  navigationPatterns: String!
  contentOrganization: String!
  responsiveStrategy: String!
  htmxCompatibility: HTMXCompatibilityInput!
  implementationFeasibility: ImplementationFeasibilityInput!
  improvementSuggestions: [ImprovementSuggestionInput!]!
}

input HTMXCompatibilityInput {
  pureHTMXAchievable: Boolean!
  progressiveEnhancement: Boolean!
  hypermediaCompliant: Boolean!
  alpineJSRequired: Boolean!
  compatibilityScore: Int!
  compatibilityNotes: String
}

input ImplementationFeasibilityInput {
  developmentComplexity: Int!
  performanceImpact: Int!
  maintenanceBurden: Int!
  businessValue: Int!
  feasibilityNotes: String
}

input ImprovementSuggestionInput {
  category: ImprovementCategory!
  title: String!
  description: String!
  estimatedTimeMinutes: Int
}

input ImplementedSuggestionInput {
  suggestionId: ID!
  actualTimeMinutes: Int!
  implementationMethod: String!
  codeChanges: String!
  testingNotes: String!
  success: Boolean!
  issues: [String!]!
}

input HTMXPatternInput {
  patternType: HTMXPatternType!
  implementation: String!
  targetElement: String
  triggerEvent: String
  swapStrategy: String
  additionalAttributes: JSON
}

input BootstrapComponentInput {
  componentType: BootstrapComponentType!
  implementation: String!
  customizations: String
  responsiveClasses: [String!]!
}

input AlpineJSUsageInput {
  usageReason: String!
  implementation: String!
  complexity: AlpineJSComplexity!
  justification: String!
}

input PerformanceImpactInput {
  loadTimeChange: Float
  bundleSizeChange: Float
  renderTimeChange: Float
  performanceScore: Int
  performanceNotes: String
}
