# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: GIS & Mapping

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **GIS & Mapping** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these specific pages into true hypermedia-driven interfaces that leverage HTMX's full potential while maintaining excellent UX and acknowledging the unique requirements of interactive mapping applications.

## Your Assigned Pages
Work through each page in the **GIS & Mapping** section systematically. Mark each page as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Mapping Pages
- [ ] `templates/mapping/gis.html` - GIS interface
- [ ] `templates/CLEAR/mapping/` - CLEAR mapping components (2 files)

## Core Directives

### 1. Hypermedia Systems Compliance (Adapted for GIS)
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every page follows the core HTMX/Hypermedia principles WHERE APPLICABLE:
  - **Hypermedia as the Engine of Application State (HATEOAS)** - for non-map UI elements
  - **Progressive Enhancement** - provide fallbacks for users without JavaScript
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes over custom JavaScript for non-map features
  - **Server-Side Rendering First** - minimize client-side state management outside of map interactions

### 2. HTMX Implementation Standards (GIS-Adapted)
Apply these HTMX patterns for non-map interface elements:

**Navigation & Routing:**
- Use `hx-get`, `hx-post`, `hx-put`, `hx-delete` for all non-map interactions
- Implement `hx-push-url="true"` for navigational requests
- Use `hx-target` and `hx-swap` strategically for map controls and data panels
- Implement `hx-boost="true"` on forms and links where appropriate

**Dynamic Content Loading:**
- Use `hx-trigger` for map-related data updates (layer changes, feature selection)
- Implement `hx-indicator` for loading states when fetching spatial data
- Use `hx-select` to extract specific content from spatial data responses
- Apply `hx-vals` or `hx-include` for spatial query parameters

**User Experience Enhancements:**
- Implement `hx-confirm` for destructive spatial operations
- Use `hx-disabled-elt` during spatial data processing
- Apply appropriate `hx-swap` modifiers for map control panels
- Implement optimistic updates for spatial data with `hx-swap-oob`

### 3. Technology Stack Guidelines (GIS-Specific)

**Mapping Library Integration:**
- Use **Leaflet** as the primary mapping library (lightweight, HTMX-friendly)
- Integrate **OpenLayers** only if advanced GIS functionality is required
- Avoid heavy frameworks like Mapbox GL JS unless absolutely necessary
- Ensure mapping library initialization works with HTMX page updates

**Bootstrap Integration:**
- Use Bootstrap 5.x classes for map controls and panels
- Implement responsive map containers with Bootstrap grid
- Style map popups and tooltips with Bootstrap components
- Ensure map controls are mobile-friendly

**Alpine.js Usage (Enhanced for GIS):**
- Use Alpine.js for:
  - **Map state management** (current zoom, center, active layers)
  - **Layer toggle controls** and visibility management
  - **Drawing tool states** and geometry editing
  - **Coordinate display** and real-time tracking
  - **Map interaction modes** (pan, zoom, draw, select)
  - **Spatial search interfaces** and filter controls
- Keep Alpine.js focused on map-specific interactions
- Use HTMX for data fetching and server communication

### 4. Data Integration Requirements

**Spatial Database Connectivity:**
- Ensure connection to PostGIS-enabled Neon PostgreSQL
- Implement proper spatial queries using Django's GIS extensions
- Use GeoJSON format for spatial data exchange
- Add spatial indexes for performance optimization

**Redis Integration:**
- Cache frequently accessed spatial datasets
- Store map tile cache metadata
- Cache spatial query results appropriately
- Implement spatial cache invalidation strategies

**Spatial API Endpoints:**
- Return GeoJSON for spatial data
- Provide separate endpoints for map tiles if serving custom tiles
- Implement proper spatial filtering and querying
- Handle large spatial datasets with pagination

### 5. GIS & Mapping-Specific Requirements

**Progressive Enhancement for Maps:**
- **With JavaScript**: Full interactive mapping experience
- **Without JavaScript**: Provide static map images or basic location information
- **Fallback Strategy**: Show address lists, coordinates, or static map embeds
- **Accessibility**: Ensure map data is available in text format

**Map Functionality:**
- Integrate Leaflet with HTMX for dynamic layer management
- Implement server-side layer switching via HTMX
- Enable dynamic map data visualization with real-time updates
- Provide spatial search and filtering through HTMX endpoints

**GIS Operations:**
- Support geometric data display with proper styling
- Enable basic spatial analysis tools (buffer, intersection)
- Implement coordinate system handling and transformations
- Provide map export functionality (PNG, PDF, GeoJSON)

**Performance Considerations:**
- Implement map tile caching strategies
- Use vector tiles for large datasets
- Optimize spatial queries with proper indexing
- Implement progressive loading for large spatial datasets

### 6. Special Considerations for GIS

**Map-HTMX Integration Patterns:**
- Use HTMX to update map data without reinitializing the map
- Implement map event handlers that trigger HTMX requests
- Update map layers based on HTMX responses
- Coordinate map state with server-side application state

**Mobile Responsiveness:**
- Ensure maps work well on touch devices
- Implement mobile-friendly map controls
- Optimize map performance for mobile networks
- Provide alternative interfaces for small screens

## Success Criteria
A GIS page is considered complete when it:
1. Adheres to Hypermedia Systems principles for non-map elements
2. Uses HTMX for all non-map dynamic interactions
3. Connects to real spatial data sources (PostGIS + Redis)
4. Provides excellent UX with Bootstrap styling
5. **Provides meaningful fallbacks for users without JavaScript**
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible (including map alternatives)
8. Is marked as complete in the audit checklist
9. Implements comprehensive GIS and mapping functionality with proper performance optimization

## 🗺️ GIS-Specific Success Indicators
- Maps load quickly and perform well
- Spatial data updates dynamically via HTMX
- Layer controls work seamlessly with server state
- Fallback content is meaningful for non-JS users
- Mobile experience is optimized for touch interaction 