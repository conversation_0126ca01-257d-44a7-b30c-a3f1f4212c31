# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: Communication System

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **Communication System** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these specific pages into true hypermedia-driven interfaces that leverage HTMX's full potential while maintaining excellent UX and acknowledging the unique requirements of real-time communication systems.

## Your Assigned Pages
Work through each page in the **Communication System** section systematically. Mark each page as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Messaging Core
- [ ] `templates/messaging/messages.html` - Message center
- [ ] `templates/messaging/chat_interface.html` - Chat interface
- [ ] `templates/communication/messages.html` - Communication messages
- [ ] `templates/CLEAR/ai_communication.html` - AI communication

### AI Communication
- [ ] `templates/CLEAR/ai_communication/` - AI communication components (1 file)

## Core Directives

### 1. Hypermedia Systems Compliance (Adapted for Real-Time Communication)
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every page follows the core HTMX/Hypermedia principles WHERE APPLICABLE:
  - **Hypermedia as the Engine of Application State (HATEOAS)** - for non-real-time UI elements
  - **Progressive Enhancement** - provide fallbacks for users without JavaScript
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes over custom JavaScript for non-real-time features
  - **Server-Side Rendering First** - minimize client-side state management outside of real-time interactions

### 2. HTMX Implementation Standards (Communication-Adapted)
Apply these HTMX patterns for non-real-time interface elements:

**Navigation & Routing:**
- Use `hx-get`, `hx-post`, `hx-put`, `hx-delete` for message management operations
- Implement `hx-push-url="true"` for navigational requests
- Use `hx-target` and `hx-swap` strategically for message panels and conversation lists
- Implement `hx-boost="true"` on forms and links where appropriate

**Dynamic Content Loading:**
- Use `hx-trigger` for message-related updates (conversation selection, message actions)
- Implement `hx-indicator` for loading states when fetching message history
- Use `hx-select` to extract specific content from message responses
- Apply `hx-vals` or `hx-include` for message context and metadata

**User Experience Enhancements:**
- Implement `hx-confirm` for destructive message operations (delete, archive)
- Use `hx-disabled-elt` during message processing
- Apply appropriate `hx-swap` modifiers for message containers
- Implement optimistic updates for message actions with `hx-swap-oob`

### 3. Technology Stack Guidelines (Communication-Specific)

**Real-Time Communication Stack:**
- Use **WebSockets** for real-time message delivery and presence
- Implement **Server-Sent Events (SSE)** as fallback for one-way updates
- Use **Django Channels** for WebSocket handling on the backend
- Integrate **Redis** for real-time message queuing and presence tracking

**Bootstrap Integration:**
- Use Bootstrap 5.x classes for chat interfaces and message panels
- Implement responsive message containers with Bootstrap grid
- Style message bubbles and conversation lists with Bootstrap components
- Ensure chat interfaces are mobile-friendly and touch-optimized

**Alpine.js Usage (Enhanced for Communication):**
- Use Alpine.js for:
  - **Real-time message state management** (unread counts, typing indicators)
  - **Chat interface interactions** (emoji picker, file upload UI)
  - **Message composition** (auto-resize text areas, draft saving)
  - **Presence indicators** (online/offline status, typing status)
  - **AI chat interactions** (streaming response handling, conversation context)
  - **Message search and filtering** interfaces
  - **Notification management** (sound, desktop notifications)
- Keep Alpine.js focused on real-time and interactive features
- Use HTMX for message history, conversation management, and server communication

### 4. Data Integration Requirements

**Real-Time Database Connectivity:**
- Ensure connection to Neon PostgreSQL for message persistence
- Implement proper message threading and conversation management
- Use efficient pagination for message history
- Add database indexes for message search and filtering

**Redis Integration:**
- Store real-time presence data (online users, typing indicators)
- Cache recent message data for quick access
- Implement message queuing for reliable delivery
- Store WebSocket connection metadata

**Communication API Endpoints:**
- Provide REST endpoints for message CRUD operations
- Implement WebSocket endpoints for real-time communication
- Return proper JSON for API responses and HTML fragments for HTMX
- Handle file uploads and media attachments
- Implement message search and filtering endpoints

### 5. Communication-Specific Requirements

**Progressive Enhancement for Messaging:**
- **With JavaScript**: Full real-time chat experience with WebSockets
- **Without JavaScript**: Basic message viewing and form-based messaging
- **Fallback Strategy**: Show message threads as standard web pages with refresh
- **Accessibility**: Ensure messages are readable by screen readers

**Real-Time Messaging Features:**
- Implement live message delivery via WebSockets
- Show typing indicators and read receipts
- Enable real-time presence (online/offline status)
- Provide instant message notifications

**AI Communication Integration:**
- Integrate AI chat functionality with streaming responses
- Implement conversation context and memory
- Provide AI conversation management and history
- Enable seamless switching between human and AI conversations

**Message Management:**
- Support message threading and replies
- Enable message search and filtering
- Implement message archiving and deletion
- Provide conversation management (create, join, leave)

**File and Media Handling:**
- Support file uploads in conversations
- Implement image and document preview
- Provide file sharing with proper permissions
- Enable drag-and-drop file uploads

### 6. Special Considerations for Communication

**WebSocket-HTMX Integration Patterns:**
- Use WebSockets for real-time message delivery
- Use HTMX for message history and conversation management
- Coordinate WebSocket events with HTMX updates
- Implement graceful fallbacks when WebSockets fail

**Performance Optimization:**
- Implement message pagination and lazy loading
- Optimize WebSocket connection management
- Cache frequently accessed conversations
- Implement efficient message search indexing

**Security Considerations:**
- Implement proper message encryption for sensitive communications
- Validate all message content and file uploads
- Implement rate limiting for message sending
- Ensure proper authentication for WebSocket connections

**Mobile Responsiveness:**
- Optimize chat interfaces for mobile devices
- Implement touch-friendly message interactions
- Ensure proper keyboard handling on mobile
- Provide offline message queuing

## Success Criteria
A communication page is considered complete when it:
1. Adheres to Hypermedia Systems principles for non-real-time elements
2. Uses HTMX for message management and navigation
3. Connects to real data sources (Neon DB + Redis + WebSockets)
4. Provides excellent UX with Bootstrap styling
5. **Provides meaningful fallbacks for users without JavaScript**
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible (including message alternatives)
8. Is marked as complete in the audit checklist
9. Implements comprehensive real-time communication functionality

## 💬 Communication-Specific Success Indicators
- Messages deliver instantly via WebSockets
- Typing indicators and presence work reliably
- Message history loads efficiently via HTMX
- Fallback messaging works without JavaScript
- AI conversations stream responses smoothly
- File uploads integrate seamlessly with chat
- Mobile chat experience is optimized for touch 