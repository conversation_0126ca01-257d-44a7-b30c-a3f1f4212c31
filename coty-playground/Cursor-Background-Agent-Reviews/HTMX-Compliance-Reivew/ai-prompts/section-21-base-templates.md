# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: Base Templates ⚠️ CRITICAL

## ⚠️ CRITICAL WARNING ⚠️
**These templates affect the ENTIRE application. Any changes here will impact every single page. Proceed with extreme caution and test thoroughly before deploying.**

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **Base Templates** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these critical templates into true hypermedia-driven foundations that leverage HTMX's full potential while maintaining excellent UX across the entire application.

## Your Assigned Pages
Work through each template in the **Base Templates** section systematically. Mark each template as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Core Templates
- [ ] `templates/base.html` - Main base template ⚠️ CRITICAL
- [ ] `templates/base/base.html` - Alternative base template ⚠️ CRITICAL

## Core Directives

### 1. Hypermedia Systems Compliance
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every template follows the core HTMX/Hypermedia principles:
  - **Hypermedia as the Engine of Application State (HATEOAS)**
  - **Progressive Enhancement** - templates must work without JavaScript
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes over custom JavaScript
  - **Server-Side Rendering First** - minimize client-side state management

### 2. Base Template-Specific Requirements

**Global HTMX Setup:**
- Include HTMX library properly
- Set up global HTMX configuration
- Implement application-wide HTMX patterns
- Configure CSRF tokens for HTMX requests

**Foundation Elements:**
- Ensure proper HTML5 semantic structure
- Include Bootstrap 5.x framework
- Set up responsive meta tags
- Configure global navigation with HTMX

**Critical Safety Measures:**
- **BACKUP FIRST**: Create backups of existing base templates
- **TEST INCREMENTALLY**: Make small changes and test thoroughly
- **COORDINATE**: Ensure changes don't break other sections
- **VALIDATE**: Test on multiple pages before finalizing

### 3. Implementation Strategy

**Phase 1: Analysis**
- Document current template structure
- Identify all dependencies and inheritance
- Map out all pages that extend these templates
- Create comprehensive backup

**Phase 2: HTMX Integration**
- Add HTMX library and configuration
- Implement global HTMX patterns
- Set up CSRF handling for HTMX
- Add global loading indicators

**Phase 3: Progressive Enhancement**
- Ensure templates work without JavaScript
- Implement fallback mechanisms
- Test with JavaScript disabled
- Validate accessibility

**Phase 4: Testing & Validation**
- Test across all major pages
- Validate responsive design
- Check browser compatibility
- Verify performance impact

## Success Criteria
A base template is considered complete when it:
1. Fully adheres to Hypermedia Systems principles
2. Properly includes and configures HTMX
3. Maintains compatibility with all existing pages
4. Provides excellent foundation for Bootstrap styling
5. Works perfectly with JavaScript disabled
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible
8. Is marked as complete in the audit checklist
9. Has been thoroughly tested across the entire application

## ⚠️ FINAL WARNING ⚠️
**These are the most critical templates in the application. Any mistakes here will break the entire system. Test extensively and coordinate with other agents working on dependent sections.** 