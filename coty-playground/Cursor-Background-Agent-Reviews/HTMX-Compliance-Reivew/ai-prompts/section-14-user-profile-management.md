# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: User Profile Management

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **User Profile Management** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these specific pages into true hypermedia-driven interfaces that leverage HTMX's full potential while maintaining excellent UX.

## Your Assigned Pages
Work through each page in the **User Profile Management** section systematically. Mark each page as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Profile Pages
- [ ] `templates/profile/profile.html` - User profile
- [ ] `templates/CLEAR/profile/` - CLEAR profile components (3 files)
- [ ] `templates/profiles/partials/` - Profile partials (2 files)

## Core Directives

### 1. Hypermedia Systems Compliance
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every page follows the core HTMX/Hypermedia principles:
  - **Hypermedia as the Engine of Application State (HATEOAS)**
  - **Progressive Enhancement** - pages must work without JavaScript
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes over custom JavaScript
  - **Server-Side Rendering First** - minimize client-side state management

### 2. HTMX Implementation Standards
Apply these HTMX patterns consistently:

**Navigation & Routing:**
- Use `hx-get`, `hx-post`, `hx-put`, `hx-delete` for all interactions
- Implement `hx-push-url="true"` for navigational requests
- Use `hx-target` and `hx-swap` strategically (prefer `outerHTML` or `innerHTML`)
- Implement `hx-boost="true"` on forms and links where appropriate

**Dynamic Content Loading:**
- Use `hx-trigger` for event-driven updates (click, change, keyup, etc.)
- Implement `hx-indicator` for loading states
- Use `hx-select` to extract specific content from responses
- Apply `hx-vals` or `hx-include` for additional data submission

**User Experience Enhancements:**
- Implement `hx-confirm` for destructive actions
- Use `hx-disabled-elt` during form submissions
- Apply appropriate `hx-swap` modifiers (`show`, `settle`, `scroll`)
- Implement optimistic updates where appropriate with `hx-swap-oob`

### 3. Technology Stack Guidelines

**Bootstrap Integration:**
- Use Bootstrap 5.x classes for responsive layout and components
- Implement Bootstrap's utility classes for spacing, typography, and colors
- Leverage Bootstrap components (modals, dropdowns, alerts) enhanced with HTMX
- Ensure mobile-first responsive design

**Alpine.js Usage (Minimal):**
- Use Alpine.js ONLY for:
  - Complex client-side interactions that can't be handled by HTMX
  - Temporary UI state (show/hide toggles, client-side validation feedback)
  - Form input formatting or real-time validation display
- Avoid Alpine.js for navigation, data fetching, or server communication
- Keep Alpine.js code minimal and focused

### 4. Data Integration Requirements

**Database Connectivity:**
- Ensure every page connects to actual Neon PostgreSQL data
- Remove ALL mock data, placeholder content, and hardcoded values
- Implement proper Django ORM queries in views
- Add appropriate database indexes for performance

**Redis Integration:**
- Implement Redis caching for frequently accessed data
- Use Redis for session management and temporary data storage
- Cache expensive database queries appropriately
- Implement cache invalidation strategies

**API Endpoints:**
- Verify all HTMX endpoints return proper HTML fragments
- Ensure endpoints handle both full page loads and partial updates
- Implement proper HTTP status codes and error handling
- Add CSRF protection and proper authentication checks

### 5. User Profile-Specific Requirements

**Profile Information Management:**
- Inline editing of profile fields with HTMX
- Real-time validation and feedback
- Avatar upload with progress indicators
- Profile completeness tracking

**Activity & Statistics:**
- Dynamic activity timeline loading
- Real-time statistics updates
- Project and task completion metrics
- Performance dashboard components

**Privacy & Security:**
- Privacy settings management with HTMX
- Password change functionality
- Two-factor authentication management
- Account security overview

**Preferences & Customization:**
- Theme and appearance settings
- Notification preferences
- Language and localization settings
- Dashboard customization options

## Success Criteria
A page is considered complete when it:
1. Fully adheres to Hypermedia Systems principles
2. Uses HTMX for all dynamic interactions
3. Connects to real data sources (Neon DB + Redis)
4. Provides excellent UX with Bootstrap styling
5. Works perfectly with JavaScript disabled
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible
8. Is marked as complete in the audit checklist
9. Implements comprehensive profile management features 