# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: Reusable Components

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **Reusable Components** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these specific components into true hypermedia-driven interfaces that leverage HTMX's full potential while maintaining excellent UX.

## Your Assigned Pages
Work through each component in the **Reusable Components** section systematically. Mark each component as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Component Categories
- [ ] `templates/components/dashboard/` - Dashboard components (15 files)
- [ ] `templates/components/navigation/` - Navigation components (8 files)
- [ ] `templates/components/ui/` - UI components (15 files)
- [ ] `templates/components/mapping/` - Mapping components (17 files)
- [ ] `templates/components/messages/` - Message components (25 files)
- [ ] `templates/components/documents/` - Document components (21 files)
- [ ] `templates/components/projects/` - Project components (6 files)
- [ ] `templates/components/reports/` - Report components (5 files)
- [ ] `templates/components/ai_communication/` - AI communication components (6 files)
- [ ] `templates/components/timeline/` - Timeline components (5 files)
- [ ] `templates/components/timer/` - Timer components (5 files)
- [ ] `templates/components/timesheet/` - Timesheet components (3 files)
- [ ] `templates/components/whispers/` - Whisper components (7 files)
- [ ] `templates/components/notifications/` - Notification components (4 files)
- [ ] `templates/components/settings/` - Settings components (3 files)
- [ ] `templates/components/profile/` - Profile components (2 files)
- [ ] `templates/components/comments/` - Comment components (6 files)

### Standalone Components (High Priority)
- [ ] `templates/components/error_message.html`
- [ ] `templates/components/dashboard_stats.html`
- [ ] `templates/components/style-guide.html`
- [ ] `templates/components/data_table.html`
- [ ] `templates/components/data_table_body.html`
- [ ] `templates/components/recent_activity.html`

## Core Directives

### 1. Hypermedia Systems Compliance
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every component follows the core HTMX/Hypermedia principles:
  - **Hypermedia as the Engine of Application State (HATEOAS)**
  - **Progressive Enhancement** - components must work without JavaScript
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes over custom JavaScript
  - **Server-Side Rendering First** - minimize client-side state management

### 2. Component-Specific Requirements

**Reusability:**
- Ensure components are truly reusable across different contexts
- Use parameterized templates with clear interfaces
- Avoid hardcoded values and ensure data comes from context
- Create consistent naming conventions

**HTMX Integration:**
- Implement proper HTMX patterns for dynamic behavior
- Use appropriate `hx-target` and `hx-swap` strategies
- Enable component-level updates and interactions
- Provide loading states and error handling

**Bootstrap Styling:**
- Use Bootstrap 5.x classes consistently
- Ensure responsive design across all components
- Implement proper spacing and typography
- Use Bootstrap components (modals, dropdowns, etc.) enhanced with HTMX

### 3. Priority Order
Work on components in this order:
1. **Core UI Components** (error messages, data tables, style guide)
2. **Navigation Components** (critical for app functionality)
3. **Dashboard Components** (high visibility)
4. **Form and Input Components** (frequently used)
5. **Specialized Components** (mapping, reports, etc.)

## Success Criteria
A component is considered complete when it:
1. Fully adheres to Hypermedia Systems principles
2. Uses HTMX for all dynamic interactions
3. Is truly reusable across different contexts
4. Provides excellent UX with Bootstrap styling
5. Works perfectly with JavaScript disabled
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible
8. Is marked as complete in the audit checklist
9. Has clear documentation for reuse 