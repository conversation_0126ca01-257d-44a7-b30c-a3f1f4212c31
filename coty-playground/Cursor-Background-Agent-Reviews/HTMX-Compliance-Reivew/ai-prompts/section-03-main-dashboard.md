# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: Main Dashboard

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **Main Dashboard** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these specific pages into true hypermedia-driven interfaces that leverage HTMX's full potential while maintaining excellent UX and supporting interactive dashboard widgets where beneficial.

## Your Assigned Pages
Work through each page in the **Main Dashboard** section systematically. Mark each page as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Dashboard Pages
- [ ] `templates/dashboard/dashboard.html` - Main application dashboard
- [ ] `templates/CLEAR/dashboard.html` - CLEAR module dashboard
- [ ] `templates/CLEAR/dashboard_fixed.html` - Fixed layout dashboard

## Core Directives

### 1. Hypermedia Systems Compliance (Dashboard-Optimized)
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every page follows the core HTMX/Hypermedia principles with dashboard considerations:
  - **Hypermedia as the Engine of Application State (HATEOAS)** - for navigation and data management
  - **Progressive Enhancement** - dashboards work without JavaScript, enhanced with it
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes, use JavaScript for complex widgets only
  - **Server-Side Rendering First** - minimize client-side state management

### 2. HTMX Implementation Standards (Dashboard-Focused)
Apply these HTMX patterns as the primary interaction method:

**Navigation & Routing:**
- Use `hx-get`, `hx-post`, `hx-put`, `hx-delete` for all dashboard interactions
- Implement `hx-push-url="true"` for navigational requests
- Use `hx-target` and `hx-swap` strategically for widget updates and dashboard sections
- Implement `hx-boost="true"` on forms and links where appropriate

**Dynamic Content Loading:**
- Use `hx-trigger` for dashboard updates (time-based, user-triggered)
- Implement `hx-indicator` for loading states on widgets and data updates
- Use `hx-select` to extract specific content from dashboard responses
- Apply `hx-vals` or `hx-include` for dashboard filters and parameters

**Real-Time Dashboard Updates:**
- Use HTMX polling with `hx-trigger="every 30s"` for live data updates
- Implement `hx-swap-oob` for updating multiple dashboard widgets
- Use `hx-trigger="load"` for initial dashboard data loading
- Apply conditional triggers based on user activity

**User Experience Enhancements:**
- Implement `hx-confirm` for destructive dashboard actions
- Use `hx-disabled-elt` during dashboard operations
- Apply appropriate `hx-swap` modifiers for smooth widget updates
- Implement optimistic updates for dashboard settings

### 3. Technology Stack Guidelines (Dashboard-Balanced)

**Dashboard Widget Strategy:**
- **Primary Approach**: Use HTMX for most dashboard functionality
- **Enhanced Widgets**: Use lightweight JavaScript for complex interactions only
- **Chart Integration**: Simple charts via HTMX, complex charts via Chart.js
- **Real-time Updates**: Prefer HTMX polling, use WebSockets for critical real-time needs

**Bootstrap Integration:**
- Use Bootstrap 5.x classes for responsive dashboard layouts
- Implement Bootstrap grid system for dashboard widget organization
- Style dashboard cards and panels with Bootstrap components
- Ensure mobile-first responsive dashboard design

**Alpine.js Usage (Selective for Dashboards):**
- Use Alpine.js ONLY when HTMX is insufficient for:
  - **Complex widget interactions** (drag-and-drop widget reordering)
  - **Real-time chart updates** (live data streaming to charts)
  - **Dashboard customization** (widget resize, layout persistence)
  - **Interactive data filtering** (complex multi-select filters)
  - **Quick actions** (keyboard shortcuts, bulk operations)
- Avoid Alpine.js for basic dashboard operations
- Keep Alpine.js usage minimal and focused

### 4. Data Integration Requirements

**Dashboard Database Connectivity:**
- Ensure every widget connects to actual Neon PostgreSQL data
- Remove ALL mock data, placeholder content, and hardcoded values
- Implement efficient dashboard queries with proper aggregation
- Add appropriate database indexes for dashboard performance

**Redis Integration:**
- Cache frequently accessed dashboard data and metrics
- Store user dashboard preferences and layout settings
- Cache expensive dashboard calculations
- Implement dashboard data refresh strategies

**Dashboard API Endpoints:**
- Verify all HTMX endpoints return proper HTML fragments for widgets
- Provide JSON endpoints for chart data when needed
- Ensure endpoints handle both full dashboard loads and widget updates
- Implement proper HTTP status codes and error handling
- Add CSRF protection and proper authentication checks

### 5. Dashboard-Specific Requirements

**Progressive Enhancement Strategy:**
- **Without JavaScript**: Static dashboard with basic data display and navigation
- **With HTMX**: Dynamic widget updates, real-time data, smooth interactions
- **With Enhanced JS**: Interactive charts, drag-and-drop, advanced customization
- **Accessibility**: Ensure all dashboard data is accessible via keyboard and screen readers

**Real-Time Dashboard Features:**
- Implement live data refreshing with HTMX polling (primary method)
- Show real-time statistics and metrics updates
- Update charts and graphs dynamically via HTMX or lightweight JS
- Provide live notifications and alerts through HTMX

**Dashboard User Experience:**
- Create responsive dashboard widgets that work on all screen sizes
- Implement smooth transitions between dashboard states
- Provide dashboard customization (widget visibility, layout preferences)
- Enable quick actions and shortcuts for common tasks

**Performance Optimization:**
- Implement efficient dashboard data loading strategies
- Use progressive loading for dashboard widgets
- Cache dashboard configurations and user preferences
- Optimize database queries for dashboard aggregations

### 6. Dashboard Implementation Strategy

**Widget Development Approach:**
1. **Start with HTMX**: Build each widget using HTMX patterns first
2. **Evaluate Enhancement**: Determine if JavaScript enhancement adds significant value
3. **Selective Enhancement**: Add JavaScript only for complex interactions
4. **Maintain Fallbacks**: Ensure HTMX functionality remains as fallback

**Dashboard Layout Management:**
- Use CSS Grid and Bootstrap for responsive layouts
- Implement server-side layout persistence
- Provide basic customization via HTMX forms
- Enhance with JavaScript only for advanced features (drag-and-drop)

**Real-Time Data Strategy:**
- **Primary**: HTMX polling for most real-time needs
- **Secondary**: WebSockets for critical real-time requirements
- **Fallback**: Manual refresh buttons for users without JavaScript
- **Performance**: Intelligent polling intervals based on data importance

## Success Criteria
A dashboard page is considered complete when it:
1. Fully adheres to Hypermedia Systems principles as the foundation
2. Uses HTMX for all primary dynamic interactions
3. Connects to real data sources (Neon DB + Redis)
4. Provides excellent UX with Bootstrap styling
5. Works effectively without JavaScript (basic functionality)
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible
8. Is marked as complete in the audit checklist
9. Implements real-time dashboard functionality efficiently
10. Uses JavaScript enhancement judiciously and only when beneficial

## 📊 Dashboard-Specific Success Indicators
- Dashboard loads quickly with meaningful data
- Real-time updates work smoothly via HTMX polling
- Widget interactions are responsive and intuitive
- Mobile dashboard experience is fully functional
- Basic dashboard works without JavaScript
- Enhanced features provide clear value over HTMX-only approach
- Dashboard customization persists across sessions 