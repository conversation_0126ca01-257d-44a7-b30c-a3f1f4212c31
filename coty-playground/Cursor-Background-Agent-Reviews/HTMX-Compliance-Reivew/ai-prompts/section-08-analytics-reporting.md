# HTMX Hypermedia Systems Compliance & Implementation Agent

## ASSIGNED PAGE GROUP: Analytics & Reporting

## Mission
You are a specialized background agent tasked with systematically auditing and refactoring the pages in the **Analytics & Reporting** section of our Django HTMX application to ensure strict compliance with Hypermedia Systems principles as defined in `docs/hypermedia-systems-complete.md`. Your goal is to transform these specific pages into true hypermedia-driven interfaces that leverage HTMX's full potential while maintaining excellent UX and acknowledging the unique requirements of interactive data visualization and analytics systems.

## Your Assigned Pages
Work through each page in the **Analytics & Reporting** section systematically. Mark each page as complete in the checklist in `coty-playground/background-agents/page-audit-checklist.md` as you finish them.

### Report Pages
- [ ] `templates/reports/reports.html` - Reports main page
- [ ] `templates/reports/dashboard.html` - Reports dashboard
- [ ] `templates/reports/executive_dashboard.html` - Executive dashboard
- [ ] `templates/reports/scheduled_reports.html` - Scheduled reports

## Core Directives

### 1. Hypermedia Systems Compliance (Adapted for Data Visualization)
- **READ AND INTERNALIZE** `docs/hypermedia-systems-complete.md` completely before starting any work
- Ensure every page follows the core HTMX/Hypermedia principles WHERE APPLICABLE:
  - **Hypermedia as the Engine of Application State (HATEOAS)** - for non-visualization UI elements
  - **Progressive Enhancement** - provide fallbacks for users without JavaScript
  - **Locality of Behavior** - keep behavior close to HTML elements
  - **Simplicity over Complexity** - prefer HTMX attributes over custom JavaScript for non-chart features
  - **Server-Side Rendering First** - minimize client-side state management outside of data visualization

### 2. HTMX Implementation Standards (Analytics-Adapted)
Apply these HTMX patterns for non-visualization interface elements:

**Navigation & Routing:**
- Use `hx-get`, `hx-post`, `hx-put`, `hx-delete` for report management operations
- Implement `hx-push-url="true"` for navigational requests
- Use `hx-target` and `hx-swap` strategically for report panels and data containers
- Implement `hx-boost="true"` on forms and links where appropriate

**Dynamic Content Loading:**
- Use `hx-trigger` for report filtering and parameter changes
- Implement `hx-indicator` for loading states when generating reports
- Use `hx-select` to extract specific content from report responses
- Apply `hx-vals` or `hx-include` for report parameters and filters

**User Experience Enhancements:**
- Implement `hx-confirm` for destructive report operations (delete, reset)
- Use `hx-disabled-elt` during report generation
- Apply appropriate `hx-swap` modifiers for report containers
- Implement optimistic updates for report settings with `hx-swap-oob`

### 3. Technology Stack Guidelines (Analytics-Specific)

**Data Visualization Stack:**
- Use **Chart.js** as the primary charting library (lightweight, responsive)
- Integrate **D3.js** only for complex custom visualizations
- Use **ApexCharts** for advanced interactive charts if needed
- Avoid heavy frameworks unless absolutely necessary for specific chart types

**Bootstrap Integration:**
- Use Bootstrap 5.x classes for report layouts and control panels
- Implement responsive chart containers with Bootstrap grid
- Style report filters and controls with Bootstrap components
- Ensure charts and dashboards are mobile-friendly

**Alpine.js Usage (Enhanced for Analytics):**
- Use Alpine.js for:
  - **Chart state management** (zoom levels, selected data points, filters)
  - **Interactive chart controls** (legend toggles, data series selection)
  - **Real-time data updates** (chart refresh, live data streaming)
  - **Report parameter management** (date ranges, filters, grouping)
  - **Dashboard widget interactions** (resize, reorder, customize)
  - **Export functionality** (PDF, CSV, image generation)
  - **Drill-down interactions** (chart click handlers, data exploration)
- Keep Alpine.js focused on data visualization and interactive features
- Use HTMX for report generation, data fetching, and server communication

### 4. Data Integration Requirements

**Analytics Database Connectivity:**
- Ensure connection to Neon PostgreSQL for analytical data
- Implement efficient aggregation queries for large datasets
- Use database views and materialized views for complex reports
- Add appropriate indexes for analytical query performance

**Redis Integration:**
- Cache frequently accessed report data and aggregations
- Store pre-computed analytics for faster dashboard loading
- Implement real-time analytics data streaming
- Cache chart configuration and user preferences

**Analytics API Endpoints:**
- Provide JSON endpoints for chart data
- Implement efficient data aggregation endpoints
- Return HTML fragments for HTMX-powered report components
- Handle large dataset pagination and streaming
- Implement real-time data update endpoints

### 5. Analytics & Reporting-Specific Requirements

**Progressive Enhancement for Analytics:**
- **With JavaScript**: Full interactive charts and real-time dashboards
- **Without JavaScript**: Static charts (PNG/SVG), data tables, and basic reports
- **Fallback Strategy**: Show tabular data and static visualizations
- **Accessibility**: Ensure chart data is available in table format for screen readers

**Data Visualization Features:**
- Implement interactive charts with zoom, pan, and selection
- Enable real-time data updates and live chart refreshing
- Provide drill-down capabilities and data exploration
- Support multiple chart types (line, bar, pie, scatter, heatmap)

**Report Generation:**
- Create scheduled report functionality with background processing
- Enable report export in multiple formats (PDF, CSV, Excel, PNG)
- Implement custom report builders with drag-and-drop
- Provide report templates and customization options

**Dashboard Functionality:**
- Create responsive dashboard layouts with moveable widgets
- Implement real-time dashboard updates
- Enable dashboard customization and personalization
- Provide executive-level summary dashboards

**Performance Optimization:**
- Implement efficient data aggregation and caching
- Use progressive data loading for large datasets
- Optimize chart rendering performance
- Implement data sampling for very large datasets

### 6. Special Considerations for Analytics

**Chart-HTMX Integration Patterns:**
- Use HTMX to update chart data without reinitializing charts
- Implement chart filter controls that trigger HTMX requests
- Update chart configurations based on HTMX responses
- Coordinate chart state with server-side report parameters

**Real-Time Analytics:**
- Implement live data streaming for real-time dashboards
- Use WebSockets or SSE for live chart updates
- Provide real-time KPI monitoring
- Enable live alert and notification systems

**Mobile Analytics Experience:**
- Ensure charts are touch-friendly and responsive
- Implement mobile-optimized chart interactions
- Provide simplified mobile dashboard layouts
- Optimize chart performance for mobile devices

**Accessibility for Data Visualization:**
- Provide alternative text descriptions for charts
- Ensure charts are keyboard navigable
- Implement high contrast mode for charts
- Provide data tables as alternatives to visual charts

## Success Criteria
An analytics page is considered complete when it:
1. Adheres to Hypermedia Systems principles for non-visualization elements
2. Uses HTMX for report management and navigation
3. Connects to real data sources (Neon DB + Redis)
4. Provides excellent UX with Bootstrap styling
5. **Provides meaningful fallbacks for users without JavaScript**
6. Demonstrates proper error handling and loading states
7. Is fully responsive and accessible (including chart alternatives)
8. Is marked as complete in the audit checklist
9. Implements comprehensive analytics and reporting functionality

## 📊 Analytics-Specific Success Indicators
- Charts render quickly and perform smoothly
- Real-time data updates work reliably
- Report generation is efficient and user-friendly
- Fallback tables provide meaningful data access
- Mobile chart experience is optimized
- Export functionality works across all formats
- Dashboard customization is intuitive and persistent 