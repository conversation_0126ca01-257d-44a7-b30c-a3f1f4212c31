#!/bin/bash

# Test MCP Servers Setup Script
# This script tests each MCP server configuration

echo "MCP Server Testing Script"
echo "========================"

# Check if .env.mcp exists
if [ ! -f ".env.mcp" ]; then
    echo "⚠️  Warning: .env.mcp file not found!"
    echo "Please copy .env.mcp.example to .env.mcp and fill in your credentials."
    exit 1
fi

# Load environment variables
set -a
source .env.mcp
set +a

echo ""
echo "Testing MCP Servers..."
echo ""

# Test each server
servers=("filesystem" "memory" "postgresql" "redis" "github" "git" "docker" "code-runner" "sequential-thinking")

for server in "${servers[@]}"; do
    echo -n "Testing $server server... "
    
    # Extract the command from .mcp.json
    command=$(jq -r ".mcpServers.$server.command" .mcp.json 2>/dev/null)
    
    if [ "$command" == "null" ] || [ -z "$command" ]; then
        echo "❌ Not configured"
        continue
    fi
    
    # Check if the command exists
    if command -v "$command" &> /dev/null; then
        echo "✅ Command found: $command"
    else
        echo "❌ Command not found: $command"
    fi
done

echo ""
echo "Environment Variables Status:"
echo "----------------------------"

# Check environment variables
vars=("DATABASE_URL" "REDIS_URL" "GITHUB_PERSONAL_ACCESS_TOKEN" "RAILWAY_API_KEY" "SUPABASE_ACCESS_TOKEN" "NEON_API_KEY")

for var in "${vars[@]}"; do
    if [ -z "${!var}" ] || [ "${!var}" == *"your_"* ]; then
        echo "❌ $var: Not set or using placeholder"
    else
        echo "✅ $var: Set"
    fi
done

echo ""
echo "Testing complete!"