#!/usr/bin/env python
"""
Utility script to convert Tailwind CSS classes to Bootstrap 5 equivalents.

This script helps migrate templates from Tailwind to Bootstrap by:
1. Finding all Tailwind classes in templates
2. Suggesting Bootstrap equivalents
3. Optionally applying the conversions
"""

import os
import re
from pathlib import Path

# Tailwind to Bootstrap class mappings
TAILWIND_TO_BOOTSTRAP = {
    # Layout
    'flex': 'd-flex',
    'flex-col': 'd-flex flex-column',
    'flex-row': 'd-flex flex-row',
    'flex-wrap': 'flex-wrap',
    'flex-nowrap': 'flex-nowrap',
    'items-center': 'align-items-center',
    'items-start': 'align-items-start',
    'items-end': 'align-items-end',
    'justify-center': 'justify-content-center',
    'justify-between': 'justify-content-between',
    'justify-around': 'justify-content-around',
    'justify-start': 'justify-content-start',
    'justify-end': 'justify-content-end',
    
    # Grid
    'grid': 'row',
    'grid-cols-2': 'row row-cols-2',
    'grid-cols-3': 'row row-cols-3',
    'grid-cols-4': 'row row-cols-4',
    'col-span-2': 'col-span-2',  # Custom CSS needed
    'gap-4': 'gap-3',
    
    # Spacing (Tailwind uses different scale)
    'p-0': 'p-0',
    'p-1': 'p-1',
    'p-2': 'p-1',
    'p-3': 'p-2',
    'p-4': 'p-3',
    'p-5': 'p-3',
    'p-6': 'p-4',
    'p-8': 'p-5',
    'px-4': 'px-3',
    'py-4': 'py-3',
    'mt-4': 'mt-3',
    'mb-4': 'mb-3',
    'ml-4': 'ms-3',
    'mr-4': 'me-3',
    
    # Text
    'text-center': 'text-center',
    'text-left': 'text-start',
    'text-right': 'text-end',
    'text-sm': 'small',
    'text-lg': 'fs-5',
    'text-xl': 'fs-4',
    'text-2xl': 'fs-3',
    'text-3xl': 'fs-2',
    'text-4xl': 'fs-1',
    'font-bold': 'fw-bold',
    'font-semibold': 'fw-semibold',
    'font-normal': 'fw-normal',
    'uppercase': 'text-uppercase',
    'lowercase': 'text-lowercase',
    
    # Colors
    'text-white': 'text-white',
    'text-black': 'text-dark',
    'text-gray-500': 'text-muted',
    'text-gray-600': 'text-secondary',
    'text-red-500': 'text-danger',
    'text-blue-500': 'text-primary',
    'text-green-500': 'text-success',
    'bg-white': 'bg-white',
    'bg-gray-100': 'bg-light',
    'bg-gray-200': 'bg-light',
    'bg-blue-500': 'bg-primary',
    'bg-red-500': 'bg-danger',
    'bg-green-500': 'bg-success',
    
    # Borders
    'border': 'border',
    'border-2': 'border',
    'border-gray-300': 'border-secondary',
    'rounded': 'rounded',
    'rounded-lg': 'rounded-3',
    'rounded-xl': 'rounded-4',
    'rounded-full': 'rounded-circle',
    
    # Width/Height
    'w-full': 'w-100',
    'w-1/2': 'w-50',
    'w-1/3': 'w-33',  # Custom CSS needed
    'h-full': 'h-100',
    'min-h-screen': 'min-vh-100',
    
    # Display
    'hidden': 'd-none',
    'block': 'd-block',
    'inline-block': 'd-inline-block',
    'inline': 'd-inline',
    
    # Position
    'relative': 'position-relative',
    'absolute': 'position-absolute',
    'fixed': 'position-fixed',
    'top-0': 'top-0',
    'bottom-0': 'bottom-0',
    'left-0': 'start-0',
    'right-0': 'end-0',
    
    # Shadow
    'shadow': 'shadow-sm',
    'shadow-md': 'shadow',
    'shadow-lg': 'shadow-lg',
    'shadow-xl': 'shadow-lg',
    
    # Hover (requires custom CSS)
    'hover:shadow-lg': 'shadow-hover',
    'hover:scale-105': 'hover-scale',
    
    # Transitions (requires custom CSS)
    'transition-all': 'transition',
    'duration-300': '',  # Handled in CSS
}

# EGIS Brand Color Mappings
EGIS_COLOR_MAPPINGS = {
    'bg-egis-green': 'bg-primary',  # Map to Bootstrap primary
    'text-egis-green': 'text-primary',
    'bg-egis-midnightBlue': 'bg-dark',
    'text-egis-midnightBlue': 'text-dark',
}


def find_tailwind_classes(content):
    """Find all potential Tailwind classes in HTML content."""
    # Pattern to match class attributes
    class_pattern = r'class=["\']([^"\']+)["\']'
    matches = re.findall(class_pattern, content)
    
    tailwind_classes = set()
    for match in matches:
        classes = match.split()
        for cls in classes:
            # Check if it looks like a Tailwind class
            if any(cls.startswith(prefix) for prefix in [
                'flex', 'grid', 'p-', 'px-', 'py-', 'pt-', 'pb-', 'pl-', 'pr-',
                'm-', 'mx-', 'my-', 'mt-', 'mb-', 'ml-', 'mr-',
                'text-', 'bg-', 'border', 'rounded', 'w-', 'h-',
                'min-h-', 'max-h-', 'min-w-', 'max-w-',
                'hover:', 'focus:', 'active:',
                'shadow', 'transition', 'duration', 'ease',
                'items-', 'justify-', 'content-', 'self-',
                'absolute', 'relative', 'fixed', 'sticky',
                'top-', 'bottom-', 'left-', 'right-',
                'z-', 'opacity-', 'scale-', 'rotate-', 'translate-',
                'gap-', 'space-x-', 'space-y-',
            ]):
                tailwind_classes.add(cls)
    
    return tailwind_classes


def convert_class_string(class_string):
    """Convert a class string from Tailwind to Bootstrap."""
    classes = class_string.split()
    converted = []
    
    for cls in classes:
        # Check EGIS color mappings first
        if cls in EGIS_COLOR_MAPPINGS:
            converted.append(EGIS_COLOR_MAPPINGS[cls])
        # Then check general mappings
        elif cls in TAILWIND_TO_BOOTSTRAP:
            bootstrap_equiv = TAILWIND_TO_BOOTSTRAP[cls]
            if bootstrap_equiv:  # Skip empty mappings
                converted.append(bootstrap_equiv)
        # Keep non-Tailwind classes
        elif not any(cls.startswith(prefix) for prefix in [
            'flex', 'grid', 'p-', 'px-', 'py-', 'm-', 'mx-', 'my-',
            'text-', 'bg-', 'border', 'rounded', 'w-', 'h-',
            'hover:', 'focus:', 'shadow', 'transition'
        ]):
            converted.append(cls)
        else:
            # Keep unmapped Tailwind classes with comment
            converted.append(f"{cls} /* TODO: Convert from Tailwind */")
    
    return ' '.join(converted)


def convert_file(filepath, dry_run=True):
    """Convert Tailwind classes to Bootstrap in a single file."""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Find and replace class attributes
    def replace_classes(match):
        class_string = match.group(1)
        converted = convert_class_string(class_string)
        return f'class="{converted}"'
    
    content = re.sub(r'class=["\']([^"\']+)["\']', replace_classes, content)
    
    if content != original_content:
        if dry_run:
            print(f"\nChanges for {filepath}:")
            # Show diff-like output
            lines_original = original_content.splitlines()
            lines_new = content.splitlines()
            for i, (old, new) in enumerate(zip(lines_original, lines_new)):
                if old != new:
                    print(f"  Line {i+1}:")
                    print(f"    - {old.strip()}")
                    print(f"    + {new.strip()}")
        else:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Converted {filepath}")
    else:
        print(f"✓ No Tailwind classes found in {filepath}")


def main():
    """Main function to convert all templates."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert Tailwind CSS to Bootstrap 5')
    parser.add_argument('--apply', action='store_true', help='Apply conversions (default is dry run)')
    parser.add_argument('--path', default='templates', help='Path to templates directory')
    parser.add_argument('--file', help='Convert a specific file only')
    args = parser.parse_args()
    
    if args.file:
        convert_file(args.file, dry_run=not args.apply)
    else:
        template_dir = Path(args.path)
        if not template_dir.exists():
            print(f"Error: Directory {template_dir} not found")
            return
        
        html_files = list(template_dir.glob('**/*.html'))
        print(f"Found {len(html_files)} HTML files")
        
        for filepath in html_files:
            convert_file(filepath, dry_run=not args.apply)
        
        if not args.apply:
            print("\n✨ Dry run complete. Use --apply to make changes.")
        else:
            print("\n✨ Conversion complete!")
            
        # Print custom CSS needed
        print("\n📝 Custom CSS needed for full compatibility:")
        print("""
/* Add to your custom CSS file */

/* Hover effects */
.shadow-hover:hover {
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
}

.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease-in-out;
}

/* Transitions */
.transition {
    transition: all 0.3s ease-in-out;
}

/* Custom widths */
.w-33 {
    width: 33.333333%;
}

/* EGIS Brand Colors */
:root {
    --egis-green: #8CC63F;
    --egis-midnight-blue: #1e293b;
    --egis-black: #000000;
    --egis-blue: #005AAB;
    --egis-teal: #14b8a6;
    --egis-gray: #6B7280;
    --egis-orange: #FF7F00;
}

.bg-egis-green { background-color: var(--egis-green) !important; }
.text-egis-green { color: var(--egis-green) !important; }
.bg-egis-midnight-blue { background-color: var(--egis-midnight-blue) !important; }
.text-egis-midnight-blue { color: var(--egis-midnight-blue) !important; }
""")


if __name__ == '__main__':
    main()