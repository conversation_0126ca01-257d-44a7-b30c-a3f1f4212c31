#!/bin/bash

# Browser Compatibility Test Runner
# Executes browser compatibility tests using Playwright

echo "🚀 Starting Browser Compatibility Tests..."
echo "======================================"

# Check if running in CI or local environment
if [ -n "$CI" ]; then
    echo "Running in CI mode"
    export HEADLESS=true
else
    echo "Running in local mode"
    export HEADLESS=false
fi

# Set up environment
export DJANGO_SETTINGS_MODULE=clear_htmx.dev_settings
export PYTHONPATH=/workspaces/clear_htmx:$PYTHONPATH

# Ensure test directories exist
mkdir -p reports/screenshots
mkdir -p test-results

# Check if Django server is running
if ! curl -s http://localhost:8000 > /dev/null; then
    echo "⚠️  Django server not running. Starting server..."
    python manage.py runserver --settings=clear_htmx.dev_settings &
    SERVER_PID=$!
    sleep 5
fi

# Install playwright browsers if needed
echo "📦 Checking Playwright browsers..."
npx playwright install chromium firefox webkit

# Run browser compatibility tests
echo ""
echo "🧪 Running Playwright browser tests..."
echo "-------------------------------------"

# Run specific browser compatibility tests
npx playwright test tests/e2e/browser-compatibility/ \
    --reporter=html \
    --reporter=json \
    --output=test-results/browser-compat.json

# Run Python-based compatibility tests
echo ""
echo "🐍 Running Python compatibility tests..."
echo "--------------------------------------"

cd /workspaces/clear_htmx
python scripts/browser_compatibility_test.py

# Generate summary report
echo ""
echo "📊 Generating compatibility report..."
echo "-----------------------------------"

# Create summary
cat > reports/browser_test_summary.txt << EOF
Browser Compatibility Test Summary
==================================
Date: $(date)

Tests Executed:
- Playwright browser tests
- Python compatibility scanner
- Feature detection tests

Results Location:
- Matrix: reports/browser_compatibility_matrix.md
- Screenshots: reports/screenshots/
- Detailed: reports/browser_compatibility_detailed.json
- Playwright: test-results/html-report/

Next Steps:
1. Review the compatibility matrix
2. Check screenshots for visual issues
3. Implement additional fallbacks if needed
EOF

echo ""
echo "✅ Browser compatibility testing completed!"
echo ""
echo "📋 Summary:"
cat reports/browser_test_summary.txt

# Cleanup
if [ -n "$SERVER_PID" ]; then
    echo ""
    echo "Stopping test server..."
    kill $SERVER_PID 2>/dev/null
fi

echo ""
echo "🎉 All tests completed successfully!"