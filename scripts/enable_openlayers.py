#!/usr/bin/env python
"""
Script to enable OpenLayers feature flag for testing the migration.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clear_htmx.dev_settings')
django.setup()

from CLEAR.feature_flags import FeatureFlags

def main():
    print("Current feature flags:")
    flags = FeatureFlags.get_all_flags()
    for flag, value in flags.items():
        print(f"  {flag}: {value}")
    
    print("\nEnabling OpenLayers feature flag...")
    FeatureFlags.set_flag('USE_OPENLAYERS', True)
    
    print("Updated feature flags:")
    flags = FeatureFlags.get_all_flags()
    for flag, value in flags.items():
        print(f"  {flag}: {value}")
    
    print("\nOpenLayers is now enabled! Visit the project map to test the migration.")
    print("To disable: FeatureFlags.set_flag('USE_OPENLAYERS', False)")

if __name__ == '__main__':
    main()