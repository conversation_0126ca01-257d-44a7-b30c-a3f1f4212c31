{"agentId": "CHARLIE_STRATEGIST", "complianceAlignment": {"hdaArchitecture": {"hateoas": {"principle": "Hypermedia As The Engine Of Application State", "requirements": ["All state transitions driven by server-provided hypermedia", "No client-side routing or state management", "HTML responses contain all necessary application state", "HTMX attributes define all dynamic behaviors"], "violations": ["JSON API endpoints for UI updates", "Client-side state stores (Redux, Context, etc.)", "JavaScript-based routing", "REST APIs returning data instead of HTML"]}, "htmlOnlyResponses": {"principle": "Server returns HTML fragments, never JSON for UI", "requirements": ["All HTMX endpoints return HTML fragments", "Full page loads return complete HTML documents", "Partial updates via HTML fragment replacement", "Form submissions return HTML responses"], "allowedExceptions": ["File downloads (binary data)", "Export functionality (CSV, PDF)", "WebSocket messages for notifications only"]}, "serverSideState": {"principle": "All application state managed server-side", "requirements": ["Django sessions for user state", "Database for persistent state", "Redis for temporary/cache state", "No client-side state persistence"], "patterns": ["Form state in Django forms", "Pagination state in URL parameters", "Filter state in session or URL", "User preferences in database"]}}, "technologyStack": {"frontend": {"cssFramework": {"allowed": ["Bootstrap 5"], "forbidden": ["Tailwind CSS", "Material UI", "<PERSON><PERSON><PERSON>", "Foundation"], "usage": "Bootstrap 5 utilities and components exclusively"}, "jsFramework": {"allowed": ["HTMX 1.9+"], "forbidden": ["React", "<PERSON><PERSON>", "Angular", "Alpine.js", "Svelte"], "usage": "HTMX for all dynamic interactions"}, "jsLibraries": {"allowed": ["Leaflet (for maps only)", "MapboxGL (for 3D maps only)", "Chart.js (progressive enhancement only)", "Flatpickr (date pickers)"], "restrictions": ["No state management in JS libraries", "Server provides all data via HTML attributes", "Progressive enhancement only", "Fallback to server-side functionality"]}}, "backend": {"framework": "Django 5.2+", "database": "PostgreSQL with PostGIS", "cache": "Redis with fallback to memory", "websockets": "Django Channels (notifications only)", "templates": "Django template engine with components"}}}, "implementationStandards": [{"category": "<PERSON><PERSON><PERSON>", "standards": [{"name": "View Organization", "requirements": ["Class-based views for standard CRUD operations", "Function-based views for HTMX endpoints", "Separate views module per domain area", "Mixins for common functionality"]}, {"name": "Model Structure", "requirements": ["Domain-based model separation", "Abstract base models for common fields", "Proper indexes on foreign keys and filters", "PostGIS fields for all spatial data"]}, {"name": "URL Patterns", "requirements": ["Namespaced URLs per app", "RESTful URL structure", "HTMX endpoints under /htmx/ prefix", "Consistent naming conventions"]}, {"name": "Template Architecture", "requirements": ["Base template with consistent layout", "Component templates in templates/components/", "Partial templates for HTMX responses", "Template inheritance for consistency"]}]}, {"category": "HTMX Patterns", "standards": [{"name": "Request/Response Cycle", "requirements": ["hx-get/post/put/delete for actions", "hx-target for response placement", "hx-swap for replacement strategy", "hx-trigger for event handling"]}, {"name": "Progressive Enhancement", "requirements": ["Forms work without JavaScript", "Links function without HTMX", "HTMX enhances existing HTML", "Graceful degradation"]}, {"name": "Loading States", "requirements": ["hx-indicator for loading feedback", "Proper ARIA labels for accessibility", "Disable buttons during requests", "Show progress for long operations"]}]}, {"category": "Security Requirements", "standards": [{"name": "CSRF Protection", "requirements": ["{% csrf_token %} in all forms", "HTMX requests include CSRF token", "Django middleware enabled", "Proper token rotation"]}, {"name": "Authentication", "requirements": ["@login_required on all views", "Permission checks for actions", "Session-based authentication", "Proper logout handling"]}, {"name": "Data Validation", "requirements": ["Django forms for all input", "Model validation rules", "Sanitize user input", "Escape output in templates"]}]}, {"category": "Performance Standards", "standards": [{"name": "Database Optimization", "requirements": ["select_related() for foreign keys", "prefetch_related() for many-to-many", "Database indexes on filters", "Query count monitoring"]}, {"name": "Caching Strategy", "requirements": ["Cache template fragments", "Redis for session data", "Static file optimization", "CDN for external resources"]}, {"name": "HTMX Optimization", "requirements": ["Minimal HTML in responses", "Debounce rapid requests", "Lazy loading for large lists", "Response size monitoring"]}]}], "verificationCriteria": [{"feature": "Live Mapping Interface", "criteria": ["Map initialization via Django template with data attributes", "HTMX endpoints return HTML with embedded GeoJSON", "No direct AJAX calls to JSON APIs", "Server-side spatial calculations with PostGIS", "Progressive enhancement for map interactions"], "tests": ["Verify map loads without JavaScript", "Check HTMX responses are HTML fragments", "Validate PostGIS query performance", "Test fallback for non-JS browsers"]}, {"feature": "Real-time Collaboration Tools", "criteria": ["WebSocket for notifications only, not UI updates", "HTMX polling for presence updates", "HTML fragments for user status changes", "Server maintains all collaboration state", "Conflict resolution handled server-side"], "tests": ["Verify collaboration without WebSockets", "Check HTMX polling intervals", "Test concurrent edit handling", "Validate state consistency"]}, {"feature": "Advanced Conflict Detection", "criteria": ["PostGIS spatial queries for detection", "Results rendered as HTML tables/lists", "HTMX for filter updates", "Server-side pagination", "No client-side data processing"], "tests": ["Verify spatial query accuracy", "Check HTML response format", "Test filter combinations", "Validate performance with large datasets"]}, {"feature": "Team Workload Analytics", "criteria": ["Server-rendered charts or progressive enhancement", "HTMX for date range updates", "HTML tables with data", "Django aggregation queries", "Export via server-generated files"], "tests": ["Verify analytics without JavaScript", "Check aggregation query performance", "Test data accuracy", "Validate export functionality"]}], "complianceChecklists": {"preImplementation": {"architecture": ["Confirm HTMX-only approach for feature", "Verify no JSON APIs for UI updates", "Check Bootstrap 5 component availability", "Plan server-side state management", "Design HTML fragment responses"], "dependencies": ["No React/Vue/Angular dependencies", "No Tailwind CSS classes", "HTMX included in base template", "Bootstrap 5 properly configured", "Required Django packages installed"]}, "implementation": {"code": ["Django views return HTML via render()", "HTMX attributes on interactive elements", "Bootstrap 5 classes exclusively", "CSRF tokens in all forms", "Proper Django model usage"], "patterns": ["Function-based views for HTMX endpoints", "Template components for reusability", "Proper URL namespacing", "Consistent error handling", "Loading indicators for all requests"]}, "integration": {"htmx": ["hx-target points to correct element", "hx-swap strategy appropriate", "hx-trigger events properly set", "Error handling via htmx:error", "Loading states visible"], "testing": ["Unit tests for views", "Template rendering tests", "HTMX response format tests", "JavaScript disabled tests", "Performance benchmarks"]}, "deployment": {"security": ["CSRF protection active", "Authentication required", "Permissions enforced", "Input validation complete", "XSS prevention verified"], "performance": ["Database queries optimized", "Response sizes minimized", "Caching implemented", "Static files optimized", "CDN configured"], "documentation": ["HTMX attributes documented", "Component usage guides", "URL endpoint documentation", "Template hierarchy docs", "Deployment notes"]}}, "riskAssessment": {"high": [{"risk": "JavaScript library usage beyond allowed scope", "mitigation": "Strict code review for any JS additions", "indicator": "Package.json modifications or <script> tags"}, {"risk": "JSON API endpoints for UI updates", "mitigation": "All endpoints must return HTML fragments", "indicator": "JsonResponse usage in views"}], "medium": [{"risk": "Client-side state management creep", "mitigation": "Regular architecture reviews", "indicator": "LocalStorage usage or JS state variables"}, {"risk": "Performance degradation from full HTML responses", "mitigation": "Response size monitoring and optimization", "indicator": "Response times over 200ms"}], "low": [{"risk": "Bootstrap 5 limitations for complex UI", "mitigation": "Custom CSS following Bootstrap patterns", "indicator": "Extensive custom CSS files"}]}, "successMetrics": [{"metric": "Zero JSON API endpoints for UI", "target": "100% HTML responses", "measurement": "Code analysis tool"}, {"metric": "JavaScript bundle size", "target": "< 50KB excluding maps", "measurement": "Build output analysis"}, {"metric": "Page load time", "target": "< 1 second", "measurement": "Lighthouse scores"}, {"metric": "HTMX response time", "target": "< 200ms average", "measurement": "Server monitoring"}, {"metric": "Bootstrap 5 compliance", "target": "No Tailwind classes", "measurement": "CSS analysis"}], "completionTime": "2025-01-23T14:45:00Z", "confidence": 0.98}