#!/usr/bin/env python3
"""
Performance Benchmark Script for Leaflet vs OpenLayers
======================================================

This script benchmarks various mapping operations to compare performance
between Leaflet and OpenLayers implementations in the CLEAR platform.
"""

import os
import sys
import time
import json
import asyncio
import psutil
import statistics
from datetime import datetime
from typing import Dict, List, Tuple, Any
from contextlib import contextmanager
import subprocess
import tracemalloc

# Add Django project to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clear_htmx.dev_settings')

import django
django.setup()

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page
from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from CLEAR.models import Project, UtilityLineData, Conflict

User = get_user_model()


class PerformanceBenchmark:
    """Main benchmarking class for mapping performance comparison"""
    
    def __init__(self):
        self.results = {
            'leaflet': {},
            'openlayers': {},
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'system_info': self.get_system_info()
            }
        }
        self.test_user = None
        self.test_project = None
        
    def get_system_info(self) -> Dict[str, Any]:
        """Collect system information for benchmark context"""
        return {
            'cpu_count': psutil.cpu_count(),
            'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else 'N/A',
            'memory_total': psutil.virtual_memory().total / (1024**3),  # GB
            'python_version': sys.version,
            'django_version': django.VERSION
        }
    
    @contextmanager
    def measure_memory(self):
        """Context manager to measure memory usage"""
        tracemalloc.start()
        start_memory = tracemalloc.get_traced_memory()[0]
        yield
        end_memory = tracemalloc.get_traced_memory()[0]
        tracemalloc.stop()
        return (end_memory - start_memory) / (1024**2)  # Convert to MB
    
    def setup_test_data(self, feature_count: int):
        """Create test data with specified number of features"""
        # Clean up existing test data
        self.cleanup_test_data()
        
        # Create test user and project
        self.test_user = User.objects.create_user(
            username=f'benchmark_user_{feature_count}',
            email=f'benchmark_{feature_count}@test.com',
            password='testpass123'
        )
        
        self.test_project = Project.objects.create(
            name=f'Benchmark Project {feature_count} features',
            description='Performance benchmark test project',
            location='Test Location',
            lead_engineer=self.test_user,
            status='active'
        )
        
        # Create utility lines
        utilities = []
        for i in range(feature_count):
            utility = UtilityLineData(
                name=f'Utility Line {i}',
                utility_type='electric' if i % 3 == 0 else 'water' if i % 3 == 1 else 'gas',
                coordinates={
                    'type': 'LineString',
                    'coordinates': [
                        [-86.1581 + (i * 0.001), 39.7684 + (i * 0.001)],
                        [-86.1581 + (i * 0.001) + 0.01, 39.7684 + (i * 0.001) + 0.01]
                    ]
                },
                project=self.test_project
            )
            utilities.append(utility)
        
        UtilityLineData.objects.bulk_create(utilities)
        
        # Create some conflicts for larger datasets
        if feature_count >= 100:
            conflicts = []
            for i in range(min(50, feature_count // 10)):
                conflict = Conflict(
                    description=f'Test conflict {i}',
                    severity='medium',
                    location={
                        'type': 'Point',
                        'coordinates': [-86.1581 + (i * 0.002), 39.7684 + (i * 0.002)]
                    },
                    project=self.test_project
                )
                conflicts.append(conflict)
            Conflict.objects.bulk_create(conflicts)
    
    def cleanup_test_data(self):
        """Clean up test data"""
        User.objects.filter(username__startswith='benchmark_user_').delete()
        Project.objects.filter(name__startswith='Benchmark Project').delete()
    
    async def measure_page_metrics(self, page: Page) -> Dict[str, float]:
        """Measure various page performance metrics"""
        metrics = await page.evaluate("""() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            const paintData = performance.getEntriesByType('paint');
            
            return {
                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                firstPaint: paintData.find(p => p.name === 'first-paint')?.startTime || 0,
                firstContentfulPaint: paintData.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                resourceCount: performance.getEntriesByType('resource').length,
                totalResourceSize: performance.getEntriesByType('resource').reduce((acc, r) => acc + (r.transferSize || 0), 0) / 1024, // KB
                memoryUsed: performance.memory ? (performance.memory.usedJSHeapSize / 1024 / 1024) : 0  // MB
            };
        }""")
        return metrics
    
    async def benchmark_initial_load(self, browser: Browser, map_type: str, feature_count: int) -> Dict[str, Any]:
        """Benchmark initial map load time"""
        context = await browser.new_context()
        page = await context.new_page()
        
        # Login
        await page.goto('http://localhost:8000/login/')
        await page.fill('input[name="username"]', self.test_user.username)
        await page.fill('input[name="password"]', 'testpass123')
        await page.click('button[type="submit"]')
        await page.wait_for_url('**/dashboard/')
        
        # Start measuring
        start_time = time.time()
        
        # Navigate to appropriate map
        if map_type == 'leaflet':
            url = f'http://localhost:8000/projects/{self.test_project.id}/map/'
        else:  # openlayers
            url = f'http://localhost:8000/projects/{self.test_project.id}/map-openlayers/'
        
        await page.goto(url)
        
        # Wait for map to fully load
        if map_type == 'leaflet':
            await page.wait_for_function("() => window.map && window.map._loaded")
        else:  # openlayers
            await page.wait_for_function("() => window.map && window.map.getView()")
        
        # Wait for features to load
        await page.wait_for_timeout(2000)  # Allow time for feature rendering
        
        end_time = time.time()
        load_time = (end_time - start_time) * 1000  # Convert to ms
        
        # Get performance metrics
        metrics = await self.measure_page_metrics(page)
        
        # Measure feature rendering
        feature_render_time = await page.evaluate(f"""() => {{
            const start = performance.now();
            // Force redraw
            if ('{map_type}' === 'leaflet') {{
                window.map.invalidateSize();
            }} else {{
                window.map.updateSize();
                window.map.renderSync();
            }}
            const end = performance.now();
            return end - start;
        }}""")
        
        await context.close()
        
        return {
            'load_time': load_time,
            'feature_render_time': feature_render_time,
            'metrics': metrics,
            'feature_count': feature_count
        }
    
    async def benchmark_drawing_operations(self, browser: Browser, map_type: str) -> Dict[str, Any]:
        """Benchmark drawing operations performance"""
        context = await browser.new_context()
        page = await context.new_page()
        
        # Login and navigate to map
        await page.goto('http://localhost:8000/login/')
        await page.fill('input[name="username"]', self.test_user.username)
        await page.fill('input[name="password"]', 'testpass123')
        await page.click('button[type="submit"]')
        await page.wait_for_url('**/dashboard/')
        
        if map_type == 'leaflet':
            url = f'http://localhost:8000/projects/{self.test_project.id}/map/'
        else:
            url = f'http://localhost:8000/projects/{self.test_project.id}/map-openlayers/'
        
        await page.goto(url)
        
        # Wait for map to load
        if map_type == 'leaflet':
            await page.wait_for_function("() => window.map && window.map._loaded")
        else:
            await page.wait_for_function("() => window.map && window.map.getView()")
        
        # Measure polygon drawing
        polygon_time = await page.evaluate(f"""() => {{
            const coordinates = [
                [-86.16, 39.77],
                [-86.15, 39.77],
                [-86.15, 39.76],
                [-86.16, 39.76],
                [-86.16, 39.77]
            ];
            
            const start = performance.now();
            
            if ('{map_type}' === 'leaflet') {{
                const polygon = L.polygon(coordinates.map(c => [c[1], c[0]]));
                polygon.addTo(window.map);
            }} else {{
                const feature = new ol.Feature({{
                    geometry: new ol.geom.Polygon([coordinates.map(c => ol.proj.fromLonLat(c))])
                }});
                const vectorSource = new ol.source.Vector({{ features: [feature] }});
                const vectorLayer = new ol.layer.Vector({{ source: vectorSource }});
                window.map.addLayer(vectorLayer);
            }}
            
            const end = performance.now();
            return end - start;
        }}""")
        
        # Measure multiple features drawing
        multi_feature_time = await page.evaluate(f"""() => {{
            const start = performance.now();
            
            for (let i = 0; i < 100; i++) {{
                const coords = [
                    [-86.16 + (i * 0.001), 39.77 + (i * 0.001)],
                    [-86.15 + (i * 0.001), 39.77 + (i * 0.001)]
                ];
                
                if ('{map_type}' === 'leaflet') {{
                    L.polyline(coords.map(c => [c[1], c[0]])).addTo(window.map);
                }} else {{
                    const feature = new ol.Feature({{
                        geometry: new ol.geom.LineString(coords.map(c => ol.proj.fromLonLat(c)))
                    }});
                    // Assume we have a vector layer
                    if (window.vectorLayer) {{
                        window.vectorLayer.getSource().addFeature(feature);
                    }}
                }}
            }}
            
            const end = performance.now();
            return end - start;
        }}""")
        
        await context.close()
        
        return {
            'polygon_draw_time': polygon_time,
            'multi_feature_draw_time': multi_feature_time,
            'features_drawn': 100
        }
    
    async def benchmark_pan_zoom(self, browser: Browser, map_type: str) -> Dict[str, Any]:
        """Benchmark pan and zoom operations"""
        context = await browser.new_context()
        page = await context.new_page()
        
        # Login and navigate
        await page.goto('http://localhost:8000/login/')
        await page.fill('input[name="username"]', self.test_user.username)
        await page.fill('input[name="password"]', 'testpass123')
        await page.click('button[type="submit"]')
        await page.wait_for_url('**/dashboard/')
        
        if map_type == 'leaflet':
            url = f'http://localhost:8000/projects/{self.test_project.id}/map/'
        else:
            url = f'http://localhost:8000/projects/{self.test_project.id}/map-openlayers/'
        
        await page.goto(url)
        
        # Wait for map
        if map_type == 'leaflet':
            await page.wait_for_function("() => window.map && window.map._loaded")
        else:
            await page.wait_for_function("() => window.map && window.map.getView()")
        
        # Measure zoom operations
        zoom_times = []
        for i in range(5):
            zoom_time = await page.evaluate(f"""() => {{
                const start = performance.now();
                
                if ('{map_type}' === 'leaflet') {{
                    window.map.zoomIn();
                }} else {{
                    const view = window.map.getView();
                    view.setZoom(view.getZoom() + 1);
                }}
                
                return new Promise(resolve => {{
                    setTimeout(() => {{
                        const end = performance.now();
                        resolve(end - start);
                    }}, 500);  // Wait for animation
                }});
            }}""")
            zoom_times.append(zoom_time)
            await page.wait_for_timeout(100)
        
        # Measure pan operations
        pan_times = []
        for i in range(5):
            pan_time = await page.evaluate(f"""() => {{
                const start = performance.now();
                
                if ('{map_type}' === 'leaflet') {{
                    const center = window.map.getCenter();
                    window.map.panTo([center.lat + 0.01, center.lng + 0.01]);
                }} else {{
                    const view = window.map.getView();
                    const center = ol.proj.toLonLat(view.getCenter());
                    view.setCenter(ol.proj.fromLonLat([center[0] + 0.01, center[1] + 0.01]));
                }}
                
                return new Promise(resolve => {{
                    setTimeout(() => {{
                        const end = performance.now();
                        resolve(end - start);
                    }}, 500);  // Wait for animation
                }});
            }}""")
            pan_times.append(pan_time)
            await page.wait_for_timeout(100)
        
        await context.close()
        
        return {
            'avg_zoom_time': statistics.mean(zoom_times),
            'avg_pan_time': statistics.mean(pan_times),
            'zoom_operations': len(zoom_times),
            'pan_operations': len(pan_times)
        }
    
    async def benchmark_memory_usage(self, browser: Browser, map_type: str, feature_count: int) -> Dict[str, Any]:
        """Benchmark memory usage over time"""
        context = await browser.new_context()
        page = await context.new_page()
        
        # Login and navigate
        await page.goto('http://localhost:8000/login/')
        await page.fill('input[name="username"]', self.test_user.username)
        await page.fill('input[name="password"]', 'testpass123')
        await page.click('button[type="submit"]')
        await page.wait_for_url('**/dashboard/')
        
        if map_type == 'leaflet':
            url = f'http://localhost:8000/projects/{self.test_project.id}/map/'
        else:
            url = f'http://localhost:8000/projects/{self.test_project.id}/map-openlayers/'
        
        # Measure initial memory
        await page.goto(url)
        
        if map_type == 'leaflet':
            await page.wait_for_function("() => window.map && window.map._loaded")
        else:
            await page.wait_for_function("() => window.map && window.map.getView()")
        
        await page.wait_for_timeout(2000)  # Let everything settle
        
        # Collect memory samples
        memory_samples = []
        for i in range(10):
            memory = await page.evaluate("""() => {
                if (performance.memory) {
                    return {
                        total: performance.memory.totalJSHeapSize / 1024 / 1024,
                        used: performance.memory.usedJSHeapSize / 1024 / 1024
                    };
                }
                return { total: 0, used: 0 };
            }""")
            memory_samples.append(memory)
            
            # Perform some operations to stress memory
            await page.evaluate(f"""() => {{
                if ('{map_type}' === 'leaflet') {{
                    window.map.zoomIn();
                    window.map.zoomOut();
                }} else {{
                    const view = window.map.getView();
                    view.setZoom(view.getZoom() + 1);
                    view.setZoom(view.getZoom() - 1);
                }}
            }}""")
            
            await page.wait_for_timeout(500)
        
        await context.close()
        
        return {
            'initial_memory': memory_samples[0]['used'],
            'final_memory': memory_samples[-1]['used'],
            'avg_memory': statistics.mean([s['used'] for s in memory_samples]),
            'max_memory': max([s['used'] for s in memory_samples]),
            'memory_growth': memory_samples[-1]['used'] - memory_samples[0]['used']
        }
    
    async def run_benchmarks(self):
        """Run all benchmarks"""
        feature_counts = [100, 500, 1000, 5000]
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            
            for feature_count in feature_counts:
                print(f"\nBenchmarking with {feature_count} features...")
                
                # Setup test data
                self.setup_test_data(feature_count)
                
                # Run benchmarks for both map types
                for map_type in ['leaflet', 'openlayers']:
                    print(f"  Testing {map_type}...")
                    
                    # Initial load benchmark
                    load_results = await self.benchmark_initial_load(browser, map_type, feature_count)
                    
                    # Drawing operations (only for smaller datasets)
                    if feature_count <= 1000:
                        draw_results = await self.benchmark_drawing_operations(browser, map_type)
                    else:
                        draw_results = None
                    
                    # Pan/zoom operations
                    pan_zoom_results = await self.benchmark_pan_zoom(browser, map_type)
                    
                    # Memory usage
                    memory_results = await self.benchmark_memory_usage(browser, map_type, feature_count)
                    
                    # Store results
                    if map_type not in self.results:
                        self.results[map_type] = {}
                    
                    self.results[map_type][f'{feature_count}_features'] = {
                        'initial_load': load_results,
                        'drawing': draw_results,
                        'pan_zoom': pan_zoom_results,
                        'memory': memory_results
                    }
            
            await browser.close()
        
        # Cleanup
        self.cleanup_test_data()
        
        return self.results
    
    def save_results(self, filename: str):
        """Save benchmark results to file"""
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"Results saved to {filename}")
    
    def generate_report(self):
        """Generate markdown report from results"""
        report_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'reports',
            'performance_benchmark_report.md'
        )
        
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            f.write("# Mapping Performance Benchmark Report\n\n")
            f.write(f"Generated: {self.results['metadata']['timestamp']}\n\n")
            
            # System info
            f.write("## System Information\n\n")
            sys_info = self.results['metadata']['system_info']
            f.write(f"- CPU Cores: {sys_info['cpu_count']}\n")
            f.write(f"- CPU Frequency: {sys_info['cpu_freq']} MHz\n")
            f.write(f"- Total Memory: {sys_info['memory_total']:.2f} GB\n")
            f.write(f"- Python Version: {sys_info['python_version'].split()[0]}\n")
            f.write(f"- Django Version: {'.'.join(map(str, sys_info['django_version']))}\n\n")
            
            # Performance comparison
            f.write("## Performance Comparison\n\n")
            
            # Initial load times
            f.write("### Initial Load Time (ms)\n\n")
            f.write("| Feature Count | Leaflet | OpenLayers | Difference |\n")
            f.write("|---------------|---------|------------|------------|\n")
            
            for feature_count in [100, 500, 1000, 5000]:
                key = f'{feature_count}_features'
                if key in self.results.get('leaflet', {}) and key in self.results.get('openlayers', {}):
                    leaflet_time = self.results['leaflet'][key]['initial_load']['load_time']
                    openlayers_time = self.results['openlayers'][key]['initial_load']['load_time']
                    diff = ((openlayers_time - leaflet_time) / leaflet_time) * 100
                    
                    f.write(f"| {feature_count} | {leaflet_time:.0f} | {openlayers_time:.0f} | ")
                    f.write(f"{diff:+.1f}% |\n")
            
            # Memory usage
            f.write("\n### Memory Usage (MB)\n\n")
            f.write("| Feature Count | Leaflet Avg | OpenLayers Avg | Difference |\n")
            f.write("|---------------|-------------|----------------|------------|\n")
            
            for feature_count in [100, 500, 1000, 5000]:
                key = f'{feature_count}_features'
                if key in self.results.get('leaflet', {}) and key in self.results.get('openlayers', {}):
                    leaflet_mem = self.results['leaflet'][key]['memory']['avg_memory']
                    openlayers_mem = self.results['openlayers'][key]['memory']['avg_memory']
                    diff = ((openlayers_mem - leaflet_mem) / leaflet_mem) * 100 if leaflet_mem > 0 else 0
                    
                    f.write(f"| {feature_count} | {leaflet_mem:.1f} | {openlayers_mem:.1f} | ")
                    f.write(f"{diff:+.1f}% |\n")
            
            # Drawing operations
            f.write("\n### Drawing Operations (ms)\n\n")
            f.write("| Operation | Leaflet | OpenLayers | Difference |\n")
            f.write("|-----------|---------|------------|------------|\n")
            
            # Get drawing results from 100 features test
            if '100_features' in self.results.get('leaflet', {}) and self.results['leaflet']['100_features'].get('drawing'):
                leaflet_draw = self.results['leaflet']['100_features']['drawing']
                openlayers_draw = self.results['openlayers']['100_features']['drawing']
                
                # Polygon drawing
                leaflet_poly = leaflet_draw['polygon_draw_time']
                openlayers_poly = openlayers_draw['polygon_draw_time']
                poly_diff = ((openlayers_poly - leaflet_poly) / leaflet_poly) * 100 if leaflet_poly > 0 else 0
                f.write(f"| Single Polygon | {leaflet_poly:.2f} | {openlayers_poly:.2f} | {poly_diff:+.1f}% |\n")
                
                # Multi-feature drawing
                leaflet_multi = leaflet_draw['multi_feature_draw_time']
                openlayers_multi = openlayers_draw['multi_feature_draw_time']
                multi_diff = ((openlayers_multi - leaflet_multi) / leaflet_multi) * 100 if leaflet_multi > 0 else 0
                f.write(f"| 100 Features | {leaflet_multi:.2f} | {openlayers_multi:.2f} | {multi_diff:+.1f}% |\n")
            
            # Pan/Zoom operations
            f.write("\n### Pan/Zoom Operations (ms)\n\n")
            f.write("| Feature Count | Leaflet Zoom | OpenLayers Zoom | Leaflet Pan | OpenLayers Pan |\n")
            f.write("|---------------|--------------|-----------------|-------------|-----------------|\n")
            
            for feature_count in [100, 500, 1000, 5000]:
                key = f'{feature_count}_features'
                if key in self.results.get('leaflet', {}) and key in self.results.get('openlayers', {}):
                    leaflet_pz = self.results['leaflet'][key]['pan_zoom']
                    openlayers_pz = self.results['openlayers'][key]['pan_zoom']
                    
                    f.write(f"| {feature_count} | {leaflet_pz['avg_zoom_time']:.1f} | ")
                    f.write(f"{openlayers_pz['avg_zoom_time']:.1f} | ")
                    f.write(f"{leaflet_pz['avg_pan_time']:.1f} | ")
                    f.write(f"{openlayers_pz['avg_pan_time']:.1f} |\n")
            
            # Recommendations
            f.write("\n## Recommendations\n\n")
            
            # Analyze results and provide recommendations
            leaflet_wins = 0
            openlayers_wins = 0
            
            # Compare load times
            for fc in [100, 500, 1000, 5000]:
                key = f'{fc}_features'
                if key in self.results.get('leaflet', {}) and key in self.results.get('openlayers', {}):
                    if self.results['leaflet'][key]['initial_load']['load_time'] < self.results['openlayers'][key]['initial_load']['load_time']:
                        leaflet_wins += 1
                    else:
                        openlayers_wins += 1
            
            f.write("### Performance Summary\n\n")
            if leaflet_wins > openlayers_wins:
                f.write("**Leaflet demonstrates better overall performance** in most test scenarios.\n\n")
            elif openlayers_wins > leaflet_wins:
                f.write("**OpenLayers demonstrates better overall performance** in most test scenarios.\n\n")
            else:
                f.write("**Both libraries show comparable performance** with trade-offs in different areas.\n\n")
            
            f.write("### Specific Recommendations\n\n")
            f.write("1. **For small to medium datasets (< 1000 features)**:\n")
            f.write("   - Leaflet provides faster initial load times and lower memory usage\n")
            f.write("   - Suitable for most standard mapping applications\n\n")
            
            f.write("2. **For large datasets (> 1000 features)**:\n")
            f.write("   - Consider implementing clustering or data virtualization\n")
            f.write("   - Both libraries benefit from server-side filtering\n\n")
            
            f.write("3. **For complex GIS operations**:\n")
            f.write("   - OpenLayers provides more advanced features out-of-the-box\n")
            f.write("   - Better suited for professional GIS applications\n\n")
            
            f.write("4. **For mobile performance**:\n")
            f.write("   - Leaflet's smaller footprint provides better mobile experience\n")
            f.write("   - Consider progressive loading strategies\n\n")
            
            f.write("### Optimization Strategies\n\n")
            f.write("1. **Data Loading**:\n")
            f.write("   - Implement viewport-based loading\n")
            f.write("   - Use vector tiles for large datasets\n")
            f.write("   - Cache frequently accessed data\n\n")
            
            f.write("2. **Rendering Performance**:\n")
            f.write("   - Use WebGL rendering where available\n")
            f.write("   - Implement level-of-detail (LOD) strategies\n")
            f.write("   - Optimize symbol and style complexity\n\n")
            
            f.write("3. **Memory Management**:\n")
            f.write("   - Remove off-screen features\n")
            f.write("   - Implement feature pooling\n")
            f.write("   - Use simplified geometries at lower zoom levels\n\n")
        
        print(f"Report generated at {report_path}")


async def main():
    """Main execution function"""
    print("Starting mapping performance benchmark...")
    print("=" * 60)
    
    # Ensure Django server is running
    print("Note: Please ensure Django development server is running on http://localhost:8000")
    print("Run: python manage.py runserver --settings=clear_htmx.dev_settings")
    input("Press Enter when server is ready...")
    
    benchmark = PerformanceBenchmark()
    
    try:
        results = await benchmark.run_benchmarks()
        
        # Save raw results
        results_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'reports',
            'benchmark_results.json'
        )
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        benchmark.save_results(results_file)
        
        # Generate report
        benchmark.generate_report()
        
        print("\n" + "=" * 60)
        print("Benchmark completed successfully!")
        print(f"Raw results: {results_file}")
        print("Report: reports/performance_benchmark_report.md")
        
    except Exception as e:
        print(f"Error during benchmark: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())