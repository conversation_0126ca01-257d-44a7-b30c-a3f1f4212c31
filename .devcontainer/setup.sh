#!/bin/bash
# CLEAR HTMX Dev Container Setup Script

echo "🚀 Setting up CLEAR HTMX development environment..."

# Initialize conda
source /opt/conda/etc/profile.d/conda.sh

# Create the clear-htmx environment from the environment.yml file
echo "📦 Creating conda environment 'clear-htmx' from environment.yml..."
if conda env create -f .devcontainer/environment.yml; then
    echo "✅ Conda environment created successfully"
else
    echo "⚠️  Environment already exists, updating instead..."
    conda env update -f .devcontainer/environment.yml
fi

# Activate the conda environment
conda activate clear-htmx

# Verify conda environment is active
echo "📦 Current conda environment: $CONDA_DEFAULT_ENV"
python --version

# Verify GDAL installation
echo "🗺️  Checking GDAL installation..."
python -c "from osgeo import gdal; print(f'GDAL version: {gdal.__version__}')" || echo "⚠️  GDAL not found, continuing anyway..."

# Create Django secret key if not exists
if [ ! -f .env ]; then
    echo "🔐 Creating .env file with Django secret key..."
    python -c "
from django.core.management.utils import get_random_secret_key
import os
with open('.env', 'w') as f:
    f.write(f'SECRET_KEY={get_random_secret_key()}\n')
    f.write('DEBUG=True\n')
    f.write('DATABASE_URL=spatialite:///db.sqlite3\n')
    f.write('# Redis and email settings for when services are available\n')
    f.write('REDIS_URL=redis://localhost:6379\n')
    f.write('EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend\n')
"
fi

# Make migrations for CLEAR app
echo "🗃️  Making migrations for CLEAR app..."
python manage.py makemigrations CLEAR

# Run Django migrations
echo "🗃️  Running Django migrations..."
python manage.py migrate

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Make the conda environment available for VS Code
echo "export CONDA_DEFAULT_ENV=clear-htmx" >> ~/.bashrc
echo "export CONDA_PREFIX=/opt/conda/envs/clear-htmx" >> ~/.bashrc
echo "export PATH=/opt/conda/envs/clear-htmx/bin:\$PATH" >> ~/.bashrc

echo "✅ CLEAR HTMX development environment setup complete!"
echo ""
echo "🎉 Ready to develop!"
echo "🌐 Django server will auto-start at: http://localhost:8000"
echo "📧 Email backend: Console (check terminal for emails)"
echo "🗄️  Database: SpatiaLite (db.sqlite3)"
echo ""
echo "💡 Available VS Code tasks:"
echo "   - Start Django Server"
echo "   - Stop Django Server" 
echo "   - Django Migrate"
echo "   - Django Make Migrations"
echo ""
echo "🔧 Manual commands:"
echo "   python manage.py runserver 0.0.0.0:8000"
echo "   python manage.py shell"
echo "   python manage.py createsuperuser"
