# CLEAR HTMX Dev Container

This dev container is configured for the CLEAR Django + HTMX project using SpatiaLite for development.

## What's Included

- **Python 3.12** with conda environment named `clear-htmx`
- **SpatiaLite** for spatial database functionality (development-friendly)
- **Redis** for caching and Django Channels
- **MailHog** for email testing
- **GDAL/FIONA** for geospatial data processing
- **All Django dependencies** from requirements.txt

## Services

- **Django Dev Server**: http://localhost:8000
- **MailHog UI**: http://localhost:8025 (for viewing test emails)
- **Redis**: redis://localhost:6379

## Getting Started

1. Open the project in VS Code
2. When prompted, choose "Reopen in Container"
3. Wait for the container to build and setup to complete
4. The setup script will:
   - Create the conda environment
   - Install all dependencies
   - Set up environment variables
   - Run migrations
   - Collect static files

## Running the Development Server

```bash
python manage.py runserver 0.0.0.0:8000
```

## Database Configuration

Currently using **SpatiaLite** for development, which provides:
- SQLite database with spatial extensions
- Easy development setup (no external database required)
- Full Django GIS functionality
- Easy data inspection with standard SQLite tools

### Migration to PostgreSQL/PostGIS

When you're ready to move to PostgreSQL (recommended for production):

1. Install PostgreSQL 17 with PostGIS extension
2. Update your `.env` file:
   ```
   DATABASE_URL=postgis://user:password@localhost:5432/clear_htmx
   ```
3. Run migrations: `python manage.py migrate`

The spatial data models will work seamlessly with both backends.

## VS Code Extensions Included

- Python support with linting and formatting
- Django template support
- Tailwind CSS support
- HTMX snippets
- Git integration
- Jupyter notebook support

## Environment Variables

The setup creates a `.env` file with:
- `SECRET_KEY`: Django secret key
- `DEBUG=True`: Development mode
- `DATABASE_URL`: SpatiaLite database
- `REDIS_URL`: Redis connection
- Email settings for MailHog

## Conda Environment

The `clear-htmx` conda environment includes:
- All spatial libraries (GDAL, Fiona, GeoPandas)
- SpatiaLite tools
- All Python dependencies from requirements.txt

## Benefits of This Setup

1. **Fast Development**: SpatiaLite requires no external database
2. **Spatial Ready**: Full GIS functionality for your utility coordination features
3. **Production Path**: Easy migration to PostgreSQL/PostGIS later
4. **Isolated Environment**: Everything contained in conda environment
5. **Email Testing**: MailHog captures all outgoing emails for testing

## When to Migrate to PostgreSQL

Consider migrating when you need:
- Multi-user concurrent access
- Advanced PostGIS functions
- Better performance with large datasets
- Production deployment
- Advanced spatial indexing

## Troubleshooting

### Docker Already Installed on WSL Error

If you get an error about Docker needing to be installed when it's already installed:

1. **Check Docker is running in WSL**:
   ```bash
   docker --version
   docker ps
   ```

2. **Restart VS Code and Docker**:
   - Close VS Code completely
   - Restart Docker Desktop
   - Open VS Code and try again

3. **Alternative: Use existing Docker installation**:
   - The devcontainer is configured to use your existing Docker socket
   - Make sure Docker Desktop has WSL integration enabled
   - Go to Docker Desktop → Settings → Resources → WSL Integration
   - Enable integration for your WSL distribution

4. **If still having issues**:
   - Try opening the project in WSL first: `code .` from WSL terminal
   - Then use "Dev Containers: Reopen in Container" from Command Palette

### Container Build Issues

If the container fails to build:

1. **Clear Docker cache**:
   ```bash
   docker system prune -a
   ```

2. **Rebuild without cache**:
   - Command Palette → "Dev Containers: Rebuild Container"

3. **Check conda environment**:
   - The setup script creates the environment automatically
   - If it fails, you can manually create it: `conda env create -f .devcontainer/environment.yml`
