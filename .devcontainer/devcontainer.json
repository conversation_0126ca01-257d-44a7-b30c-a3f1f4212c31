// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "CLEAR HTMX Django Project",
	"image": "mcr.microsoft.com/devcontainers/miniconda:1-3",

	// Forward ports for Django development server
	"forwardPorts": [8000],
	"portsAttributes": {
		"8000": {
			"label": "Django Development Server",
			"onAutoForward": "notify"
		}
	},

	// Set up the conda environment and install dependencies
	// "postCreateCommand": "bash .devcontainer/setup.sh",
	"postCreateCommand": "sleep infinity",

	// Simple initialization
	"initializeCommand": "echo 'Starting CLEAR HTMX dev container...'",

	// Configure tool-specific properties for VS Code
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"ms-python.debugpy",
				"batisteo.vscode-django"
			],
			"settings": {
				"python.defaultInterpreterPath": "/opt/conda/envs/clear-htmx/bin/python",
				"python.terminal.activateEnvironment": true
			}
		}
	}
}
