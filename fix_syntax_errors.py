#!/usr/bin/env python3
"""
Script to fix syntax errors in CLEAR views
"""

import os
import re
import glob

def fix_unterminated_docstrings(content):
    """Fix unterminated docstrings at the beginning of files"""
    # Pattern to find docstring that doesn't close before imports
    pattern = r'("""[^"]*?)(^\s*(?:import|from)\s+)'
    
    def replace_func(match):
        return match.group(1) + '"""\n\n' + match.group(2)
    
    return re.sub(pattern, replace_func, content, flags=re.MULTILINE)

def fix_hanging_triple_quotes(content):
    """Remove hanging triple quotes at end of files"""
    # Remove standalone triple quotes at end
    content = re.sub(r'\n\s*"""\s*$', '', content)
    content = re.sub(r'\n\s*\'\'\'\s*$', '', content)
    return content

def fix_incomplete_try_except(content):
    """Fix incomplete try-except blocks"""
    # Fix empty try blocks
    content = re.sub(r'(\s+try:\s*\n\s+)except', r'\1    pass\n    except', content)
    return content

def fix_file(filepath):
    """Fix syntax errors in a single file"""
    print(f"Fixing {filepath}...")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = fix_unterminated_docstrings(content)
        content = fix_hanging_triple_quotes(content)
        content = fix_incomplete_try_except(content)
        
        # Only write if changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✓ Fixed {filepath}")
            return True
        else:
            print(f"  - No changes needed for {filepath}")
            return False
            
    except Exception as e:
        print(f"  ✗ Error fixing {filepath}: {e}")
        return False

def main():
    """Main function to fix all view files"""
    view_files = glob.glob('CLEAR/views/*.py')
    service_files = glob.glob('CLEAR/services/*.py')
    
    all_files = view_files + service_files
    
    fixed_count = 0
    for filepath in all_files:
        if fix_file(filepath):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files out of {len(all_files)} total files")

if __name__ == "__main__":
    main()