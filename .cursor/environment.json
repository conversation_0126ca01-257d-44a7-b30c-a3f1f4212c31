{"snapshot": "snapshot-20250622-b695cd3b-e370-4f08-acb9-db944b9b7c11", "install": "sudo apt update && sudo apt install -y python3.13-venv python3-full gdal-bin libgdal-dev libgeos-dev libproj-dev binutils libspatialite-dev postgresql-client && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt && python manage.py migrate --noinput", "start": "source venv/bin/activate && export DATABASE_URL='postgresql://neondb_owner:<EMAIL>:5432/neondb?sslmode=require' && python manage.py runserver 0.0.0.0:8743", "terminals": [{"name": "Django Server", "command": "source venv/bin/activate && python manage.py runserver 0.0.0.0:8743"}, {"name": "Django Shell", "command": "source venv/bin/activate && python manage.py shell"}]}