---
description: 
globs: 
alwaysApply: true
---
# Development Workflow

## Getting Started

### Project Setup
1. **<PERSON>lone and Setup**: <PERSON>lone the repository and set up the virtual environment
2. **Database**: Configure PostgreSQL with PostGIS extension
3. **Dependencies**: Install requirements from [requirements.txt](mdc:requirements.txt)
4. **Environment**: Set up environment variables for development
5. **Migrations**: Run database migrations to set up the schema

### Development Environment
```bash
# Install dependencies
pip install -r requirements.txt

# Set up database
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

## Code Organization Principles

### File Structure Guidelines
- **Models**: Split into domain modules in [CLEAR/models/](mdc:CLEAR/models)
- **Views**: Organized by functionality in [CLEAR/views/](mdc:CLEAR/views)
- **Templates**: Component-based structure in [templates/components/](mdc:templates/components)
- **Static Files**: Organized by type in [static/](mdc:static)
- **Tests**: Mirror main app structure in [CLEAR/tests/](mdc:CLEAR/tests)

### Naming Conventions
- **Models**: PascalCase (e.g., `ProjectMember`, `UtilityLineData`)
- **Views**: snake_case (e.g., `project_detail_view`, `message_create_htmx`)
- **URLs**: kebab-case (e.g., `project-detail`, `message-create`)
- **Templates**: kebab-case (e.g., `project-detail.html`, `message-list.html`)
- **CSS Classes**: kebab-case (e.g., `dashboard-card`, `message-item`)

## Feature Development Workflow

### 1. Model Development
Start with data models in appropriate domain module:

```python
# CLEAR/models/your_domain.py
from django.db import models
import uuid

class YourModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
```

### 2. Admin Configuration
Add admin interface in [CLEAR/admin.py](mdc:CLEAR/admin.py):

```python
from django.contrib import admin
from .models import YourModel

@admin.register(YourModel)
class YourModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']
    list_filter = ['created_at']
```

### 3. View Development
Create views in appropriate domain module:

```python
# CLEAR/views/your_domain_views.py
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import YourModel

@login_required
def your_model_list(request):
    """List view for your model"""
    objects = YourModel.objects.all()
    return render(request, 'your_domain/list.html', {
        'objects': objects
    })

@login_required
def your_model_create_htmx(request):
    """HTMX endpoint for creating objects"""
    if request.method == 'POST':
        # Process form data
        # Return partial template
        pass
    return render(request, 'components/your_domain/create_form.html')
```

### 4. URL Configuration
Add URLs to [CLEAR/urls.py](mdc:CLEAR/urls.py):

```python
# Add to appropriate pattern group
your_domain_patterns = [
    path('your-models/', views.your_model_list, name='your_model_list'),
    path('htmx/your-models/create/', views.your_model_create_htmx, name='your_model_create_htmx'),
]
```

### 5. Template Development
Create templates in [templates/](mdc:templates):

```html
<!-- templates/your_domain/list.html -->
{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h1>Your Models</h1>
    
    <div hx-get="{% url 'CLEAR:your_model_create_htmx' %}"
         hx-target="#create-form">
        <button class="btn btn-primary">Add New</button>
    </div>
    
    <div id="create-form"></div>
    
    <div class="grid grid-cols-1 gap-4">
        {% for object in objects %}
            {% include 'components/your_domain/item.html' %}
        {% endfor %}
    </div>
</div>
{% endblock %}
```

### 6. Component Templates
Create reusable components:

```html
<!-- templates/components/your_domain/item.html -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">{{ object.name }}</h5>
        <p class="text-muted">{{ object.created_at|date:"M d, Y" }}</p>
    </div>
</div>
```

## HTMX Development Patterns

### 1. Form Handling
```html
<!-- HTMX form with validation -->
<form hx-post="{% url 'CLEAR:form_endpoint' %}"
      hx-target="#form-container"
      hx-swap="outerHTML">
    {% csrf_token %}
    <!-- Form fields -->
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

### 2. Dynamic Content Loading
```html
<!-- Load content on trigger -->
<div hx-get="{% url 'CLEAR:dynamic_content' %}"
     hx-trigger="load, every 30s"
     hx-target="#dynamic-area">
    <div class="htmx-indicator">Loading...</div>
</div>
```

### 3. Search and Filtering
```html
<!-- Search with debounced input -->
<input type="text" 
       hx-get="{% url 'CLEAR:search_endpoint' %}"
       hx-trigger="keyup changed delay:500ms"
       hx-target="#search-results"
       placeholder="Search...">
```

## Testing Workflow

### 1. Write Tests First (TDD)
```python
# CLEAR/tests/test_your_feature.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from ..models import YourModel

class YourModelTest(TestCase):
    def setUp(self):
        self.user = get_user_model().objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_model_creation(self):
        obj = YourModel.objects.create(name='Test Object')
        self.assertEqual(str(obj), 'Test Object')
    
    def test_view_response(self):
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/your-models/')
        self.assertEqual(response.status_code, 200)
```

### 2. Run Tests Regularly
```bash
# Run all tests
python manage.py test

# Run specific test module
python manage.py test CLEAR.tests.test_your_feature

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

## Database Development

### 1. Model Changes
```bash
# Create migration after model changes
python manage.py makemigrations

# Review migration file
# Edit migration if needed for data migration

# Apply migration
python manage.py migrate
```

### 2. Data Migrations
```python
# Custom data migration
from django.db import migrations

def migrate_data_forward(apps, schema_editor):
    YourModel = apps.get_model('CLEAR', 'YourModel')
    # Data migration logic here

def migrate_data_reverse(apps, schema_editor):
    # Reverse migration logic here

class Migration(migrations.Migration):
    dependencies = [
        ('CLEAR', '0001_initial'),
    ]
    
    operations = [
        migrations.RunPython(migrate_data_forward, migrate_data_reverse),
    ]
```

## Performance Considerations

### 1. Database Queries
```python
# Use select_related for foreign keys
objects = YourModel.objects.select_related('related_model').all()

# Use prefetch_related for many-to-many
objects = YourModel.objects.prefetch_related('many_to_many_field').all()

# Use only() to limit fields
objects = YourModel.objects.only('name', 'created_at').all()
```

### 2. Template Optimization
```html
<!-- Cache expensive template fragments -->
{% load cache %}
{% cache 300 expensive_content object.id %}
    <!-- Expensive content here -->
{% endcache %}
```

### 3. Static File Optimization
- Use CSS custom properties for theming
- Minimize JavaScript dependencies
- Optimize images for web
- Use CDN for external libraries

## Debugging and Development Tools

### 1. Django Debug Toolbar
```python
# Add to INSTALLED_APPS in development
INSTALLED_APPS = [
    # ... other apps
    'debug_toolbar',
]

# Add to middleware
MIDDLEWARE = [
    # ... other middleware
    'debug_toolbar.middleware.DebugToolbarMiddleware',
]
```

### 2. Django Extensions
```bash
# Useful management commands
python manage.py show_urls  # List all URLs
python manage.py shell_plus  # Enhanced shell
python manage.py graph_models -a -o models.png  # Generate model diagram
```

### 3. Browser Developer Tools
- Use Network tab to monitor HTMX requests
- Console for JavaScript debugging
- Elements tab for CSS debugging
- Performance tab for optimization

## Git Workflow

### 1. Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `refactor/description` - Code refactoring
- `docs/description` - Documentation updates

### 2. Commit Messages
```
feat: add user messaging system
fix: resolve HTMX form validation issue
refactor: split large models.py into domain modules
docs: update API documentation
```

### 3. Pull Request Process
1. Create feature branch from main
2. Implement feature with tests
3. Run test suite locally
4. Create pull request with description
5. Code review and feedback
6. Merge after approval

## Documentation

### 1. Code Documentation
- Docstrings for all functions and classes
- Inline comments for complex logic
- Type hints where appropriate

### 2. API Documentation
- Document all API endpoints
- Include request/response examples
- Keep documentation up to date

### 3. User Documentation
- Feature guides for end users
- Admin documentation
- Deployment guides
