---
description: 
globs: 
alwaysApply: true
---
# Database and GIS Development

## Database Configuration

### PostgreSQL with PostGIS
Database configuration in [clear_htmx/settings.py](mdc:clear_htmx/settings.py):

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': 'neondb',
        'USER': 'neondb_owner',
        'HOST': 'ep-fragrant-mouse-a82qphvn-pooler.eastus2.azure.neon.tech',
        'PORT': '5432',
        'OPTIONS': {
            'sslmode': 'require',
        },
        'CONN_MAX_AGE': 600,
        'CONN_HEALTH_CHECKS': True,
    }
}
```

### GDAL Configuration
Windows GDAL setup for GIS operations:
```python
if os.name == 'nt':  # Windows
    GDAL_LIBRARY_PATH = r'C:\Program Files\QGIS 3.34.12\bin\gdal309.dll'
    GEOS_LIBRARY_PATH = r'C:\Program Files\QGIS 3.34.12\bin\geos_c.dll'
```

## Spatial Data Models

### GIS Model Patterns
Spatial models in [CLEAR/models/spatial.py](mdc:CLEAR/models/spatial.py):

```python
from django.contrib.gis.db import models
from django.contrib.gis.geos import Point

class UtilityLineData(models.Model):
    """Utility line spatial data with PostGIS geometry"""
    geometry = models.LineStringField(srid=4326)
    utility_type = models.CharField(max_length=50)
    depth = models.FloatField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['geometry']),  # Spatial index
        ]

class SpatialAnnotation(models.Model):
    """Point annotations on maps"""
    location = models.PointField(srid=4326)
    annotation_type = models.CharField(max_length=50)
    description = models.TextField()
```

### Coordinate Systems
- **SRID 4326**: WGS84 (GPS coordinates)
- **SRID 3857**: Web Mercator (web mapping)
- **State Plane**: Local coordinate systems for surveying

## Spatial Queries and Operations

### Common GIS Query Patterns
```python
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import Distance

# Point within distance
point = Point(-86.1581, 39.7684)  # Indianapolis
nearby_utilities = UtilityLineData.objects.filter(
    geometry__distance_lte=(point, Distance(mi=1))
)

# Intersection queries
conflicting_utilities = UtilityLineData.objects.filter(
    geometry__intersects=project_boundary
)

# Buffer operations
buffer_zone = utility_line.geometry.buffer(0.001)  # ~100m buffer
```

### Spatial Aggregations
```python
from django.contrib.gis.db.models import Union, Extent

# Combine geometries
combined_geometry = UtilityLineData.objects.aggregate(
    Union('geometry')
)['geometry__union']

# Calculate bounding box
extent = Project.objects.aggregate(
    Extent('boundary')
)['boundary__extent']
```

## GIS Integration

### Mapping Views
Spatial views in [CLEAR/views/mapping_views.py](mdc:CLEAR/views/mapping_views.py):

```python
def map_geojson_data(request, project_id):
    """Return GeoJSON for mapping"""
    utilities = UtilityLineData.objects.filter(
        project_id=project_id
    ).values(
        'id', 'utility_type', 'geometry'
    )
    
    features = []
    for utility in utilities:
        features.append({
            'type': 'Feature',
            'geometry': utility['geometry'].geojson,
            'properties': {
                'id': utility['id'],
                'type': utility['utility_type']
            }
        })
    
    return JsonResponse({
        'type': 'FeatureCollection',
        'features': features
    })
```

### Frontend Mapping
Leaflet integration for interactive maps:
```javascript
// Initialize map
const map = L.map('map').setView([39.7684, -86.1581], 13);

// Add tile layer
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// Load GeoJSON data
fetch(`/projects/${projectId}/map/geojson/`)
    .then(response => response.json())
    .then(data => {
        L.geoJSON(data, {
            style: function(feature) {
                return {
                    color: getUtilityColor(feature.properties.type),
                    weight: 3
                };
            }
        }).addTo(map);
    });
```

## Spatial Analysis

### Conflict Detection
```python
def detect_utility_conflicts(project):
    """Detect intersecting utilities"""
    utilities = UtilityLineData.objects.filter(project=project)
    conflicts = []
    
    for i, utility1 in enumerate(utilities):
        for utility2 in utilities[i+1:]:
            if utility1.geometry.intersects(utility2.geometry):
                intersection = utility1.geometry.intersection(utility2.geometry)
                conflicts.append({
                    'utility1': utility1,
                    'utility2': utility2,
                    'intersection_point': intersection
                })
    
    return conflicts
```

### Distance Calculations
```python
from django.contrib.gis.measure import Distance

def calculate_clearance_violations(project):
    """Check minimum clearance requirements"""
    violations = []
    utilities = UtilityLineData.objects.filter(project=project)
    
    for utility in utilities:
        nearby = UtilityLineData.objects.filter(
            project=project,
            geometry__distance_lte=(utility.geometry, Distance(ft=5))
        ).exclude(id=utility.id)
        
        for nearby_utility in nearby:
            distance = utility.geometry.distance(nearby_utility.geometry)
            if distance < get_minimum_clearance(utility.utility_type, nearby_utility.utility_type):
                violations.append({
                    'utility1': utility,
                    'utility2': nearby_utility,
                    'distance': distance
                })
    
    return violations
```

## Performance Optimization

### Spatial Indexes
```python
class UtilityLineData(models.Model):
    geometry = models.LineStringField(srid=4326)
    
    class Meta:
        indexes = [
            # Spatial index for geometry queries
            models.Index(fields=['geometry']),
            # Composite index for filtered spatial queries
            models.Index(fields=['utility_type', 'geometry']),
        ]
```

### Query Optimization
```python
# Use spatial indexes efficiently
utilities = UtilityLineData.objects.filter(
    geometry__intersects=boundary
).select_related('project', 'utility_type')

# Avoid expensive operations in loops
geometries = UtilityLineData.objects.values_list('geometry', flat=True)
combined = reduce(lambda x, y: x.union(y), geometries)
```

## Data Import/Export

### Shapefile Import
```python
from django.contrib.gis.utils import LayerMapping

def import_shapefile(shapefile_path, model_class):
    """Import spatial data from shapefile"""
    mapping = {
        'geometry': 'GEOMETRY',
        'name': 'NAME',
        'type': 'TYPE',
    }
    
    lm = LayerMapping(model_class, shapefile_path, mapping)
    lm.save(verbose=True)
```

### GeoJSON Export
```python
def export_geojson(queryset):
    """Export spatial data as GeoJSON"""
    features = []
    for obj in queryset:
        features.append({
            'type': 'Feature',
            'geometry': json.loads(obj.geometry.geojson),
            'properties': {
                'id': obj.id,
                'name': obj.name,
                'type': obj.type
            }
        })
    
    return {
        'type': 'FeatureCollection',
        'features': features
    }
```

## Testing Spatial Features

### GIS Test Patterns
```python
from django.contrib.gis.geos import Point, LineString
from django.test import TestCase

class SpatialModelTest(TestCase):
    def test_point_within_distance(self):
        # Create test geometries
        point1 = Point(-86.1581, 39.7684)
        point2 = Point(-86.1590, 39.7690)
        
        # Test distance calculation
        distance = point1.distance(point2)
        self.assertLess(distance, 0.01)  # Within ~1km
    
    def test_line_intersection(self):
        line1 = LineString((0, 0), (1, 1))
        line2 = LineString((0, 1), (1, 0))
        
        self.assertTrue(line1.intersects(line2))
        intersection = line1.intersection(line2)
        self.assertEqual(intersection.coords, [(0.5, 0.5)])
```
