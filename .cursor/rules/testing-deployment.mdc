---
description: 
globs: 
alwaysApply: true
---
# Testing and Deployment

## Testing Strategy

### Test Structure
Tests are organized in [CLEAR/tests/](mdc:CLEAR/tests) and [tests/](mdc:tests):

- **Unit Tests**: Model and utility function tests
- **Integration Tests**: View and API endpoint tests
- **E2E Tests**: Browser automation with Playwright
- **HTMX Tests**: Frontend interaction tests

### Django Unit Tests
Test files in [CLEAR/tests/](mdc:CLEAR/tests):

```python
# test_models.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from CLEAR.models import Project, TimeEntry

User = get_user_model()

class ProjectModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_project_creation(self):
        project = Project.objects.create(
            name='Test Project',
            created_by=self.user
        )
        self.assertEqual(project.name, 'Test Project')
        self.assertEqual(project.created_by, self.user)
    
    def test_project_str_method(self):
        project = Project.objects.create(
            name='Test Project',
            created_by=self.user
        )
        self.assertEqual(str(project), 'Test Project')
```

### HTMX View Tests
```python
# test_htmx_views.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

User = get_user_model()

class HTMXViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.login(email='<EMAIL>', password='testpass123')
    
    def test_message_create_htmx(self):
        """Test HTMX message creation"""
        response = self.client.post(
            reverse('CLEAR:message_create_htmx'),
            {
                'conversation_id': 'test-uuid',
                'content': 'Test message'
            },
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test message')
    
    def test_dashboard_stats_htmx(self):
        """Test dashboard stats loading"""
        response = self.client.get(
            reverse('CLEAR:dashboard_stats'),
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'dashboard-stat')
```

### API Tests
```python
# test_api.py
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model

User = get_user_model()

class ProjectAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_project_list_api(self):
        """Test project list API endpoint"""
        response = self.client.get('/api/projects/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_project_create_api(self):
        """Test project creation via API"""
        data = {
            'name': 'API Test Project',
            'description': 'Created via API'
        }
        response = self.client.post('/api/projects/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
```

## End-to-End Testing

### Playwright Configuration
E2E tests in [tests/e2e/](mdc:tests/e2e) using Playwright:

```python
# conftest.py
import pytest
from playwright.sync_api import sync_playwright

@pytest.fixture(scope="session")
def browser():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        yield browser
        browser.close()

@pytest.fixture
def page(browser):
    page = browser.new_page()
    yield page
    page.close()

@pytest.fixture
def authenticated_page(page):
    """Page with authenticated user"""
    page.goto('http://localhost:8000/auth/login/')
    page.fill('input[name="email"]', '<EMAIL>')
    page.fill('input[name="password"]', 'testpass123')
    page.click('button[type="submit"]')
    page.wait_for_url('**/dashboard/')
    return page
```

### E2E Test Examples
```python
# test_authentication.py
import pytest

def test_login_flow(page):
    """Test user login flow"""
    page.goto('http://localhost:8000/auth/login/')
    
    # Fill login form
    page.fill('input[name="email"]', '<EMAIL>')
    page.fill('input[name="password"]', 'testpass123')
    page.click('button[type="submit"]')
    
    # Should redirect to dashboard
    page.wait_for_url('**/dashboard/')
    assert page.title() == 'Dashboard - CLEAR'

def test_htmx_messaging(authenticated_page):
    """Test HTMX messaging functionality"""
    page = authenticated_page
    page.goto('http://localhost:8000/messages/')
    
    # Send a message
    page.fill('input[name="content"]', 'Test HTMX message')
    page.click('button[type="submit"]')
    
    # Check message appears
    page.wait_for_selector('text=Test HTMX message')
    assert page.is_visible('text=Test HTMX message')
```

### HTMX-Specific Tests
```python
# test_htmx_interactions.py
def test_dashboard_component_loading(authenticated_page):
    """Test HTMX dashboard component loading"""
    page = authenticated_page
    page.goto('http://localhost:8000/dashboard/')
    
    # Wait for HTMX components to load
    page.wait_for_selector('[hx-get]')
    
    # Check components are loaded
    assert page.is_visible('.dashboard-stat')
    assert page.is_visible('#team-chat')

def test_form_submission_htmx(authenticated_page):
    """Test HTMX form submission"""
    page = authenticated_page
    page.goto('http://localhost:8000/projects/create/')
    
    # Fill form
    page.fill('input[name="name"]', 'Test Project')
    page.fill('textarea[name="description"]', 'Test Description')
    
    # Submit via HTMX
    page.click('button[hx-post]')
    
    # Check success message
    page.wait_for_selector('.alert-success')
```

## Performance Testing

### Load Testing
```python
# test_performance.py
import time
from django.test import TestCase
from django.test.utils import override_settings

class PerformanceTest(TestCase):
    @override_settings(DEBUG=False)
    def test_dashboard_load_time(self):
        """Test dashboard loading performance"""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        start_time = time.time()
        response = self.client.get('/dashboard/')
        end_time = time.time()
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 2.0)  # Should load in < 2 seconds
    
    def test_database_query_efficiency(self):
        """Test database query efficiency"""
        with self.assertNumQueries(5):  # Expect exactly 5 queries
            response = self.client.get('/projects/')
            self.assertEqual(response.status_code, 200)
```

## Deployment

### Production Settings
Production configuration in [clear_htmx/settings.py](mdc:clear_htmx/settings.py):

```python
# Production-specific settings
if not DEBUG:
    # Security settings
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    
    # Database connection pooling
    DATABASES['default']['CONN_MAX_AGE'] = 600
    DATABASES['default']['CONN_HEALTH_CHECKS'] = True
    
    # Static files
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

### Railway Deployment
Deployment configuration for Railway platform:

```bash
# Railway deployment command
railway deploy --service clear-htmx
```

Environment variables for production:
- `DATABASE_URL`: PostgreSQL connection string
- `SECRET_KEY`: Django secret key
- `DEBUG`: Set to 'False'
- `ALLOWED_HOSTS`: Production domain names

### Database Migrations
```bash
# Run migrations in production
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

### Health Checks
Health check endpoint in [clear_htmx/urls.py](mdc:clear_htmx/urls.py):

```python
def health_check(request):
    """Simple health check endpoint for production"""
    try:
        # Test database connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        db_status = "ok"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    status = "ok" if db_status == "ok" else "error"
    
    return JsonResponse({
        "status": status,
        "database": db_status,
        "timestamp": timezone.now().isoformat()
    })
```

### Monitoring and Logging

#### Application Logging
```python
# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'CLEAR': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

#### Error Tracking
```python
# Custom error handling
def custom_error_handler(request, exception):
    """Custom error handler with logging"""
    import logging
    logger = logging.getLogger('CLEAR')
    
    logger.error(f'Error: {exception}', extra={
        'request': request,
        'user': request.user if hasattr(request, 'user') else None
    })
    
    return render(request, 'error/500.html', status=500)
```

## CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgis/postgis:13-3.1
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
    
    - name: Run tests
      run: |
        python manage.py test
        
    - name: Run E2E tests
      run: |
        playwright install
        pytest tests/e2e/
```

### Pre-deployment Checks
```bash
# Pre-deployment checklist
python manage.py check --deploy
python manage.py test
python manage.py collectstatic --dry-run
```
