---
description: 
globs: 
alwaysApply: true
---
# CLEAR Project Overview

CLEAR (Comprehensive Location-based Engineering and Analysis Resource) is a modern utility infrastructure management platform built with Django and HTMX.

## Project Architecture

### Core Technologies
- **Backend**: Django 5.2.3 with PostgreSQL/PostGIS
- **Frontend**: HTMX 2.0.5 + Bootstrap 5 + Custom CSS
- **Database**: Neon PostgreSQL with PostGIS for spatial operations
- **Authentication**: Custom User model with email-based login
- **Real-time**: Django Channels for WebSocket support
- **API**: Django REST Framework

### Key Project Files
- **Main Settings**: [clear_htmx/settings.py](mdc:clear_htmx/settings.py)
- **Root URLs**: [clear_htmx/urls.py](mdc:clear_htmx/urls.py)
- **App URLs**: [CLEAR/urls.py](mdc:CLEAR/urls.py)
- **Requirements**: [requirements.txt](mdc:requirements.txt)
- **Base Template**: [templates/base.html](mdc:templates/base.html)
- **Main Stylesheet**: [static/css/style.css](mdc:static/css/style.css)

### Application Structure
The CLEAR app follows a modular architecture with domain-separated modules:

#### Models ([CLEAR/models/](mdc:CLEAR/models))
- `auth.py` - Authentication and security models
- `projects.py` - Project management core models
- `spatial.py` - GIS and spatial analysis models
- `documents.py` - Document management system models
- `messaging.py` - Communication and messaging models
- `analytics.py` - Analytics and reporting models
- `knowledge.py` - Knowledge management models
- `financial.py` - Time tracking and financial models

#### Views ([CLEAR/views/](mdc:CLEAR/views))
- `auth_views.py` - Authentication, login, logout, MFA
- `dashboard_views.py` - Dashboard with enhanced error handling
- `project_views.py` - Core project management views
- `messaging_views.py` - Communication and messaging systems
- `document_views.py` - Document management and collaboration
- `mapping_views.py` - GIS, spatial analysis, and mapping
- `htmx_views.py` - HTMX endpoints for dynamic content

## Development Workflow

### File Organization
- Models are split into domain modules (max 500 lines each)
- Views are organized by functionality
- Templates use component-based structure in `templates/components/`
- Static files organized by type in `static/css/`, `static/js/`, `static/images/`

### HTMX Integration
- Extensive use of HTMX for dynamic updates
- HTMX endpoints prefixed with `htmx/` in URLs
- Real-time messaging with WebSocket support
- Progressive enhancement approach

### CSS Architecture
- Custom design system based on EGIS brand guidelines
- CSS variables for consistent theming
- Bootstrap 5 as base framework
- Component-specific stylesheets

### Database Design
- Custom User model with email authentication
- PostGIS for spatial data operations
- UUID primary keys for most models
- Comprehensive foreign key relationships

## Key Features
- **Project Management**: Utility coordination projects with phases and tasks
- **Real-time Messaging**: Team communication with whispers and threads
- **Document Management**: Version control and collaboration
- **GIS Integration**: Spatial analysis and mapping
- **Time Tracking**: Timesheet management and reporting
- **Knowledge Base**: Article management system
- **Analytics Dashboard**: Business metrics and reporting

