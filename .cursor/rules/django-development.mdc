---
description: 
globs: 
alwaysApply: true
---
# Django Development Guidelines

## Project Structure and Conventions

### Models Organization
Models are split into domain-specific modules in [CLEAR/models/](mdc:CLEAR/models):

```python
# Import pattern in __init__.py
from .auth import *
from .projects import *
from .spatial import *
# ... other domain modules
```

**Key Model Patterns:**
- Use UUID primary keys: `id = models.UUIDField(primary_key=True, default=uuid.uuid4)`
- Custom User model in [CLEAR/models/auth.py](mdc:CLEAR/models/auth.py)
- PostGIS fields for spatial data: `location = models.PointField(srid=4326)`
- Comprehensive `__str__` methods for admin interface

### Views Architecture
Views are organized by domain in [CLEAR/views/](mdc:CLEAR/views):

- **Function-based views** for HTMX endpoints
- **Class-based views** for full page renders
- **Mixins** for common functionality (LoginRequiredMixin, etc.)

**HTMX View Patterns:**
```python
# HTMX views return partial templates
def some_htmx_view(request):
    # Logic here
    return render(request, 'components/partial.html', context)

# Full page views
class SomeListView(LoginRequiredMixin, ListView):
    model = SomeModel
    template_name = 'some/list.html'
```

### URL Configuration
- Main URLs in [clear_htmx/urls.py](mdc:clear_htmx/urls.py)
- App URLs in [CLEAR/urls.py](mdc:CLEAR/urls.py)
- HTMX endpoints prefixed with `htmx/`
- API endpoints in [CLEAR/api_urls.py](mdc:CLEAR/api_urls.py)

## Database and Models

### Key Models Reference
- **User**: Custom user model with email authentication
- **Project**: Core project management with phases
- **Document**: Version-controlled document management
- **ChatMessage**: Real-time messaging system
- **TimeEntry**: Time tracking and billing

### Migrations
- Migrations in [CLEAR/migrations/](mdc:CLEAR/migrations)
- Use descriptive migration names
- PostGIS extensions enabled in initial migration

### Admin Configuration
Admin customizations in [CLEAR/admin.py](mdc:CLEAR/admin.py):
- Custom admin site branding
- Inline editing for related models
- Search and filter configurations

## Authentication and Security

### Custom User Model
Located in [CLEAR/models/auth.py](mdc:CLEAR/models/auth.py):
- Email-based authentication (USERNAME_FIELD = 'email')
- Additional profile fields
- MFA support with backup tokens

### Authentication Views
In [CLEAR/views/auth_views.py](mdc:CLEAR/views/auth_views.py):
- Custom login/logout views
- MFA implementation
- Password reset functionality

### Security Features
- CSRF protection enabled
- Secure headers middleware
- Rate limiting on authentication endpoints
- Session security configurations

## Testing

### Test Structure
Tests organized in [CLEAR/tests/](mdc:CLEAR/tests):
- `test_models.py` - Model unit tests
- `test_views.py` - View integration tests
- `test_api.py` - API endpoint tests

### Test Patterns
```python
from django.test import TestCase, Client
from django.contrib.auth import get_user_model

class ProjectModelTest(TestCase):
    def setUp(self):
        self.user = get_user_model().objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_project_creation(self):
        # Test logic here
        pass
```

## Management Commands

Custom management commands in [CLEAR/management/commands/](mdc:CLEAR/management/commands):
- Database maintenance commands
- Data migration utilities
- Cleanup operations

## Performance Considerations

### Database Optimization
- Use `select_related()` and `prefetch_related()` for foreign keys
- Database indexes on frequently queried fields
- Connection pooling configured in settings

### Caching Strategy
- Redis for session storage and caching
- Template fragment caching for expensive operations
- Database query optimization

### Static Files
- WhiteNoise for static file serving
- CSS/JS minification in production
- CDN integration for external libraries
