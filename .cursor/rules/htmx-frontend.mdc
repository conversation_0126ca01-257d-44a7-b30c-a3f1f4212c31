---
description: 
globs: 
alwaysApply: true
---
# HTMX and Frontend Development

## HTMX Integration Patterns

### Core HTMX Setup
HTMX is loaded in [templates/base.html](mdc:templates/base.html):
```html
<script src="https://unpkg.com/htmx.org@2.0.5"></script>
```

### HTMX View Patterns
HTMX endpoints are organized in [CLEAR/urls.py](mdc:CLEAR/urls.py) with `htmx/` prefix:

```python
# HTMX URL patterns
htmx_patterns = [
    path('notifications/count/', views.htmx_notification_count, name='htmx_notification_count'),
    path('messages/create/', views.message_create_htmx, name='message_create_htmx'),
    # ... more HTMX endpoints
]
```

### Common HTMX Attributes
- `hx-get` - GET requests for loading content
- `hx-post` - POST requests for form submissions
- `hx-target` - Specify target element for response
- `hx-swap` - Control how content is swapped
- `hx-trigger` - Custom event triggers
- `hx-indicator` - Loading indicators

### HTMX Response Patterns
```python
def some_htmx_view(request):
    if request.method == 'POST':
        # Process form data
        form = SomeForm(request.POST)
        if form.is_valid():
            form.save()
            # Return success partial
            return render(request, 'components/success_message.html')
        else:
            # Return form with errors
            return render(request, 'components/form_errors.html', {'form': form})
    
    # GET request - return initial content
    return render(request, 'components/some_partial.html', context)
```

## Template Architecture

### Base Template Structure
Main template: [templates/base.html](mdc:templates/base.html)
- Navigation header with dropdowns
- HTMX configuration
- Bootstrap 5 + custom CSS
- Lucide icons integration

### Component Templates
Templates are organized in [templates/components/](mdc:templates/components):
- Reusable UI components
- HTMX partial responses
- Form components
- Dashboard widgets

### Template Patterns
```html
<!-- HTMX-enabled component -->
<div hx-get="{% url 'CLEAR:some_htmx_endpoint' %}" 
     hx-trigger="load"
     hx-target="#content-area"
     hx-indicator="#loading-spinner">
    <div id="loading-spinner" class="htmx-indicator">Loading...</div>
</div>

<!-- Form with HTMX -->
<form hx-post="{% url 'CLEAR:form_submit' %}"
      hx-target="#form-result"
      hx-swap="innerHTML">
    {% csrf_token %}
    <!-- Form fields -->
</form>
```

## CSS Architecture

### Main Stylesheet
Primary styles in [static/css/style.css](mdc:static/css/style.css):
- EGIS brand color system
- CSS custom properties for theming
- Component-specific styles
- HTMX integration styles

### CSS Organization
```css
/* CSS Variables for EGIS brand colors */
:root {
  --egis-green: #8CC63F;
  --egis-midnight-blue: #1e293b;
  --egis-blue: #005AAB;
  /* ... more variables */
}

/* HTMX-specific styles */
.htmx-indicator {
  opacity: 0;
  transition: opacity 200ms ease-in;
}

.htmx-request .htmx-indicator {
  opacity: 1;
}
```

### Component Stylesheets
Additional CSS files in [static/css/](mdc:static/css):
- [static/css/dashboard-grid.css](mdc:static/css/dashboard-grid.css) - Dashboard layout
- [static/css/egis-color-system.css](mdc:static/css/egis-color-system.css) - Brand colors
- [static/css/clear-style-guide.css](mdc:static/css/clear-style-guide.css) - Style guide

## JavaScript Integration

### Dashboard Grid System
Interactive dashboard in [static/js/dashboard-grid.js](mdc:static/js/dashboard-grid.js):
- Drag and drop functionality
- Layout persistence
- Responsive grid system

### HTMX Event Handling
```javascript
// Listen for HTMX events
document.addEventListener('htmx:afterSwap', function(event) {
    // Re-initialize components after HTMX swap
    initializeComponents(event.target);
});

// Custom HTMX configuration
htmx.config.defaultSwapStyle = 'innerHTML';
htmx.config.defaultSettleDelay = 100;
```

### Common JavaScript Patterns
```javascript
// Initialize Lucide icons after HTMX updates
function initializeLucideIcons(container = document) {
    lucide.createIcons({
        icons: container.querySelectorAll('[data-lucide]')
    });
}

// Toast notifications
function showToast(message, type = 'info') {
    // Toast implementation
}
```

## Real-time Features

### WebSocket Integration
Django Channels configuration for real-time messaging:
- WebSocket consumers for chat
- Real-time notifications
- Live dashboard updates

### HTMX Polling
```html
<!-- Auto-refresh content -->
<div hx-get="{% url 'CLEAR:live_data' %}"
     hx-trigger="every 30s"
     hx-target="#live-content">
</div>
```

## Form Handling

### HTMX Form Patterns
```html
<!-- Inline form editing -->
<form hx-post="{% url 'CLEAR:update_field' %}"
      hx-target="this"
      hx-swap="outerHTML">
    {% csrf_token %}
    <input type="text" name="field_name" value="{{ object.field_name }}">
    <button type="submit">Save</button>
</form>
```

### Validation and Error Handling
```python
def form_htmx_view(request):
    if request.method == 'POST':
        form = MyForm(request.POST)
        if form.is_valid():
            form.save()
            return render(request, 'components/success.html')
        else:
            return render(request, 'components/form_with_errors.html', {
                'form': form
            })
```

## Progressive Enhancement

### Accessibility Considerations
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Focus management after HTMX swaps

### Performance Optimization
- Lazy loading with HTMX
- Debounced search inputs
- Efficient DOM updates
- Minimal JavaScript dependencies

### Browser Compatibility
- Graceful degradation without JavaScript
- HTMX polyfills for older browsers
- Progressive enhancement approach
