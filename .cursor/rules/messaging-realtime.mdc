---
description: 
globs: 
alwaysApply: true
---
# Messaging and Real-time Features

## Messaging System Architecture

### Core Models
Messaging models in [CLEAR/models/messaging.py](mdc:CLEAR/models/messaging.py):

```python
class Conversation(models.Model):
    """Group conversations with multiple participants"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    title = models.CharField(max_length=200)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class ChatMessage(models.Model):
    """Messages within conversations"""
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE)
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    
class WhisperMessage(models.Model):
    """Private messages between users"""
    sender = models.Foreign<PERSON><PERSON>(User, on_delete=models.CASCADE, related_name='sent_whispers')
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_whispers')
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
```

### Messaging Views
Messaging views in [CLEAR/views/messaging_views.py](mdc:CLEAR/views/messaging_views.py):

#### HTMX Messaging Endpoints
```python
def message_create_htmx(request):
    """Create new message via HTMX"""
    if request.method == 'POST':
        conversation_id = request.POST.get('conversation_id')
        content = request.POST.get('content')
        
        message = ChatMessage.objects.create(
            conversation_id=conversation_id,
            sender=request.user,
            content=content
        )
        
        # Return message partial
        return render(request, 'components/messages/message_item.html', {
            'message': message
        })

def conversation_list_htmx(request):
    """Load conversation list"""
    conversations = Conversation.objects.filter(
        members=request.user
    ).order_by('-last_message_at')
    
    return render(request, 'components/messages/conversation_list.html', {
        'conversations': conversations
    })
```

## Real-time Communication

### WebSocket Configuration
Django Channels setup in [clear_htmx/asgi.py](mdc:clear_htmx/asgi.py):

```python
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.core.asgi import get_asgi_application

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter([
            path("ws/chat/<str:conversation_id>/", ChatConsumer.as_asgi()),
            path("ws/notifications/", NotificationConsumer.as_asgi()),
        ])
    ),
})
```

### WebSocket Consumer
```python
from channels.generic.websocket import AsyncWebsocketConsumer
import json

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.conversation_id = self.scope['url_route']['kwargs']['conversation_id']
        self.room_group_name = f'chat_{self.conversation_id}'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']
        
        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': message,
                'sender': self.scope['user'].email
            }
        )
    
    async def chat_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'message': event['message'],
            'sender': event['sender']
        }))
```

## Whisper Messages (Private Messages)

### Whisper System
Private messaging implementation:

```python
def whisper_send_htmx(request):
    """Send private whisper message"""
    if request.method == 'POST':
        recipient_id = request.POST.get('recipient_id')
        content = request.POST.get('content')
        
        whisper = WhisperMessage.objects.create(
            sender=request.user,
            recipient_id=recipient_id,
            content=content
        )
        
        # Send real-time notification
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'user_{recipient_id}',
            {
                'type': 'whisper_notification',
                'whisper_id': whisper.id,
                'sender': request.user.email
            }
        )
        
        return render(request, 'components/whispers/whisper_sent.html')

def whisper_conversation_htmx(request, user_id):
    """Load whisper conversation with specific user"""
    whispers = WhisperMessage.objects.filter(
        models.Q(sender=request.user, recipient_id=user_id) |
        models.Q(sender_id=user_id, recipient=request.user)
    ).order_by('created_at')
    
    # Mark received whispers as read
    WhisperMessage.objects.filter(
        sender_id=user_id,
        recipient=request.user,
        is_read=False
    ).update(is_read=True)
    
    return render(request, 'components/whispers/conversation.html', {
        'whispers': whispers,
        'other_user': User.objects.get(id=user_id)
    })
```

## Message Threading and Mentions

### Message Threading
```python
class MessageThread(models.Model):
    """Thread for organizing related messages"""
    parent_message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE)
    title = models.CharField(max_length=200, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

class MessageMention(models.Model):
    """@mentions in messages"""
    message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE)
    mentioned_user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

def message_thread_create_htmx(request):
    """Create message thread"""
    parent_message_id = request.POST.get('parent_message_id')
    thread_title = request.POST.get('thread_title', '')
    
    thread = MessageThread.objects.create(
        parent_message_id=parent_message_id,
        title=thread_title
    )
    
    return render(request, 'components/messages/thread_created.html', {
        'thread': thread
    })
```

### @Mention Processing
```python
import re

def process_mentions(message_content, message):
    """Process @mentions in message content"""
    mention_pattern = r'@(\w+)'
    mentions = re.findall(mention_pattern, message_content)
    
    for username in mentions:
        try:
            user = User.objects.get(email__icontains=username)
            MessageMention.objects.create(
                message=message,
                mentioned_user=user
            )
            
            # Create notification
            create_mention_notification(user, message)
        except User.DoesNotExist:
            continue

def user_mention_search_htmx(request):
    """Search users for @mentions"""
    query = request.GET.get('q', '')
    users = User.objects.filter(
        email__icontains=query
    )[:10]
    
    return render(request, 'components/messages/mention_suggestions.html', {
        'users': users
    })
```

## Message Reactions

### Reaction System
```python
class MessageReaction(models.Model):
    """Emoji reactions to messages"""
    message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    emoji = models.CharField(max_length=10)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['message', 'user', 'emoji']

def message_reaction_add_htmx(request):
    """Add emoji reaction to message"""
    message_id = request.POST.get('message_id')
    emoji = request.POST.get('emoji')
    
    reaction, created = MessageReaction.objects.get_or_create(
        message_id=message_id,
        user=request.user,
        emoji=emoji
    )
    
    if not created:
        # Remove reaction if already exists
        reaction.delete()
    
    # Return updated reactions
    reactions = MessageReaction.objects.filter(
        message_id=message_id
    ).values('emoji').annotate(count=Count('id'))
    
    return render(request, 'components/messages/message_reactions.html', {
        'reactions': reactions,
        'message_id': message_id
    })
```

## Performance and Scalability

### Message Pagination
Paginated message loading in [CLEAR/views_package/message_pagination_views.py](mdc:CLEAR/views_package/message_pagination_views.py):

```python
from django.core.paginator import Paginator

def conversation_messages_paginated(request, conversation_id):
    """Load paginated messages for conversation"""
    messages = ChatMessage.objects.filter(
        conversation_id=conversation_id
    ).order_by('-created_at')
    
    paginator = Paginator(messages, 25)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'components/messages/message_list_paginated.html', {
        'page_obj': page_obj,
        'conversation_id': conversation_id
    })

def load_more_messages(request):
    """Load more messages via HTMX"""
    conversation_id = request.GET.get('conversation_id')
    offset = int(request.GET.get('offset', 0))
    
    messages = ChatMessage.objects.filter(
        conversation_id=conversation_id
    ).order_by('-created_at')[offset:offset+25]
    
    return render(request, 'components/messages/message_batch.html', {
        'messages': messages
    })
```

### Typing Indicators
```python
def set_typing_status(request, conversation_id):
    """Set user typing status"""
    if request.method == 'POST':
        is_typing = request.POST.get('is_typing') == 'true'
        
        # Store typing status in cache
        cache_key = f'typing_{conversation_id}_{request.user.id}'
        if is_typing:
            cache.set(cache_key, True, timeout=10)
        else:
            cache.delete(cache_key)
        
        # Broadcast typing status via WebSocket
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'chat_{conversation_id}',
            {
                'type': 'typing_status',
                'user_id': request.user.id,
                'is_typing': is_typing
            }
        )
        
        return HttpResponse()
```

## Frontend Integration

### HTMX Message Interface
```html
<!-- Message input form -->
<form hx-post="{% url 'CLEAR:message_create_htmx' %}"
      hx-target="#message-list"
      hx-swap="beforeend"
      hx-on::after-request="this.reset()">
    {% csrf_token %}
    <input type="hidden" name="conversation_id" value="{{ conversation.id }}">
    <div class="input-group">
        <input type="text" name="content" class="form-control" 
               placeholder="Type a message..." required>
        <button type="submit" class="btn btn-primary">Send</button>
    </div>
</form>

<!-- Auto-refresh message list -->
<div id="message-list" 
     hx-get="{% url 'CLEAR:conversation_messages_paginated' conversation.id %}"
     hx-trigger="every 5s">
    <!-- Messages loaded here -->
</div>
```

### WebSocket JavaScript
```javascript
// WebSocket connection for real-time messaging
const chatSocket = new WebSocket(
    'ws://' + window.location.host + '/ws/chat/' + conversationId + '/'
);

chatSocket.onmessage = function(e) {
    const data = JSON.parse(e.data);
    const messageList = document.getElementById('message-list');
    
    // Add new message to list
    const messageHtml = `
        <div class="message">
            <strong>${data.sender}:</strong> ${data.message}
        </div>
    `;
    messageList.insertAdjacentHTML('beforeend', messageHtml);
};

// Send message via WebSocket
function sendMessage(content) {
    chatSocket.send(JSON.stringify({
        'message': content
    }));
}
```
