"""
Implementation Task Management Models
Handles tracking and management of implementation tasks from docs/project-docs/tasks.md
"""

import os
import re
from django.conf import settings
from django.db import models
from django.utils import timezone





class ImplementationTask(models.Model):
    """Model for tracking implementation tasks from docs/project-docs/tasks.md"""
    TASK_TYPES = [
        ('FIX', 'Critical Fix'),
        ('COMP', 'Completion Task'),
        ('DEV', 'New Development'),
        ('REF', 'Refactoring Task'),
        ('TEST', 'Testing Task'),
        ('OTHER', 'Other Task'),
    ]
    
    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('blocked', 'Blocked'),
    ]
    
    PRIORITY_CHOICES = [
        ('P0', 'P0 - Critical'),
        ('P1', 'P1 - High'),
        ('P2', 'P2 - Medium'),
        ('P3', 'P3 - Low'),
        ('P4', 'P4 - Backlog'),
    ]
    
    # Task identification
    task_id = models.CharField(max_length=50, primary_key=True, help_text="Unique identifier for the task (e.g., FIX-1, COMP-2)")
    task_type = models.CharField(max_length=10, choices=TASK_TYPES, help_text="Type of implementation task")
    
    # Task details
    title = models.CharField(max_length=255, help_text="Task title")
    description = models.TextField(blank=True, null=True, help_text="Detailed task description")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, help_text="Task priority")
    effort = models.CharField(max_length=50, blank=True, null=True, help_text="Estimated effort (Small, Medium, Large)")
    
    # Task status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started', help_text="Current task status")
    completed = models.BooleanField(default=False, help_text="Whether the task is completed")
    
    # Task relationships
    dependencies = models.ManyToManyField('self', symmetrical=False, blank=True, related_name='dependent_tasks', help_text="Tasks that must be completed before this task")
    
    # Task metadata
    requirements_impact = models.TextField(blank=True, null=True, help_text="Requirements affected by this task")
    files_affected = models.TextField(blank=True, null=True, help_text="Files affected by this task")
    
    # Implementation details
    implementation_strategy = models.TextField(blank=True, null=True, help_text="Strategy for implementing this task")
    acceptance_criteria = models.TextField(blank=True, null=True, help_text="Criteria for task completion")
    
    # Subtasks
    subtasks = models.JSONField(default=list, help_text="List of subtasks with completion status")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(null=True, blank=True, help_text="When work on the task started")
    completed_at = models.DateTimeField(null=True, blank=True, help_text="When the task was completed")
    
    # Strategic alignment
    strategic_objectives = models.TextField(blank=True, null=True, help_text="Strategic objectives this task supports")
    business_value = models.TextField(blank=True, null=True, help_text="Business value provided by this task")
    
    class Meta:
        ordering = ['priority', 'task_id']
        verbose_name = "Implementation Task"
        verbose_name_plural = "Implementation Tasks"
    
    def __str__(self):
        return f"{self.task_id}: {self.title}"
    
    def mark_in_progress(self):
        """Mark task as in progress"""
        if self.status == 'not_started':
            self.status = 'in_progress'
            self.started_at = timezone.now()
            self.save()
    
    def mark_completed(self):
        """Mark task as completed"""
        self.status = 'completed'
        self.completed = True
        self.completed_at = timezone.now()
        self.save()
        
        # Update tasks.md file
        self.update_tasks_md()
    
    def mark_blocked(self, reason=None):
        """Mark task as blocked"""
        self.status = 'blocked'
        if reason:
            # Store reason in implementation_notes if it exists
            self.implementation_notes = f"BLOCKED: {reason}\n\n{self.implementation_notes or ''}"
        self.save()
    
    def update_tasks_md(self):
        """Update the tasks.md file to mark this task as completed"""
        tasks_md_path = os.path.join(settings.BASE_DIR, 'docs', 'project-docs', 'tasks.md')
        
        if not os.path.exists(tasks_md_path):
            return False
        
        try:
            with open(tasks_md_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Find the task in the file and update its checkbox
            task_pattern = re.compile(r'(\[ \]|\[x\]) ' + re.escape(f"{self.task_id}."))
            updated_content = task_pattern.sub(r'[x] ' + f"{self.task_id}.", content)
            
            # Also update subtasks if they exist
            for subtask in self.subtasks:
                if subtask.get('completed'):
                    subtask_id = subtask.get('id')
                    if subtask_id:
                        subtask_pattern = re.compile(r'(\[ \]|\[x\]) ' + re.escape(f"{subtask_id}"))
                        updated_content = subtask_pattern.sub(r'[x] ' + f"{subtask_id}", updated_content)
            
            with open(tasks_md_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            return True
        except Exception as e:
            print(f"Error updating tasks.md: {e}")
            return False
    
    @classmethod
    def sync_from_tasks_md(cls):
        """Sync tasks from tasks.md file"""
        tasks_md_path = os.path.join(settings.BASE_DIR, 'docs', 'project-docs', 'tasks.md')
        
        if not os.path.exists(tasks_md_path):
            return False
        
        try:
            with open(tasks_md_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Find all tasks in the file
            task_pattern = re.compile(r'#### ([\w\-]+): (.+?)\n\*\*Priority\*\*: (P\d).*?\*\*Effort\*\*: (.+?)\n', re.DOTALL)
            tasks = task_pattern.findall(content)
            
            # Find all subtasks
            subtask_pattern = re.compile(r'- \[([ x])\] ([\w\-\.]+): (.+?)$', re.MULTILINE)
            subtasks = subtask_pattern.findall(content)
            
            # Create or update tasks
            for task_id, title, priority, effort in tasks:
                task, created = cls.objects.update_or_create(
                    task_id=task_id,
                    defaults={
                        'title': title.strip(),
                        'priority': priority,
                        'effort': effort.strip(),
                        'status': 'completed' if f'[x] {task_id}' in content else 'not_started',
                        'completed': f'[x] {task_id}' in content,
                    }
                )
                
                # Update subtasks
                task_subtasks = []
                for completed, subtask_id, subtask_title in subtasks:
                    if subtask_id.startswith(f"{task_id}."):
                        task_subtasks.append({
                            'id': subtask_id,
                            'title': subtask_title.strip(),
                            'completed': completed == 'x',
                        })
                
                if task_subtasks:
                    task.subtasks = task_subtasks
                    task.save()
            
            return True
        except Exception as e:
            print(f"Error syncing from tasks.md: {e}")
            return False


class ImplementationNote(models.Model):
    """Notes and documentation for implementation tasks"""
    task = models.ForeignKey('ImplementationTask', on_delete=models.CASCADE, related_name='notes')
    title = models.CharField(max_length=255)
    content = models.TextField()
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='implementation_notes')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.task.task_id})"


class ImplementationFile(models.Model):
    """Files associated with implementation tasks"""
    task = models.ForeignKey('ImplementationTask', on_delete=models.CASCADE, related_name='files')
    file_path = models.CharField(max_length=255, help_text="Path to the file relative to project root")
    status = models.CharField(max_length=20, choices=[
        ('unchanged', 'Unchanged'),
        ('modified', 'Modified'),
        ('created', 'Created'),
        ('deleted', 'Deleted'),
    ], default='unchanged')
    changes_description = models.TextField(blank=True, null=True, help_text="Description of changes made to the file")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['file_path']
        unique_together = ['task', 'file_path']
    
    def __str__(self):
        return f"{self.file_path} ({self.task.task_id})"
