"""
AI and Intelligence Models
Handles AI commands, knowledge graphs, entity relationships, and intelligent features
"""

import uuid
from django.contrib.gis.db import models


# from django.contrib.postgres.fields import models.JSONField  # Commented out for SQLite compatibility



class EntityMention(models.Model):
    """Track entity mentions across the platform for intelligence features"""
    # Entity being mentioned
    entity_type = models.CharField(max_length=50, choices=[
        ('project', 'Project'),
        ('user', 'User'),
        ('task', 'Task'),
        ('utility', 'Utility'),
        ('conflict', 'Conflict'),
        ('stakeholder', 'Stakeholder'),
        ('document', 'Document'),
        ('location', 'Location'),
        ('organization', 'Organization'),
    ])
    entity_id = models.CharField(max_length=255)
    entity_name = models.CharField(max_length=255)
    
    # Context where mentioned
    source_type = models.Char<PERSON>ield(max_length=50)  # message, comment, document, etc.
    source_id = models.Char<PERSON><PERSON>(max_length=255)
    source_content = models.TextField()  # Snippet of content containing the mention
    
    # Mention details
    mention_text = models.Char<PERSON>ield(max_length=500)  # Actual text that triggered the mention
    confidence_score = models.FloatField(default=1.0)  # How confident we are this is correct
    context_before = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    context_after = models.CharField(max_length=200, blank=True, null=True)
    
    # Metadata
    mentioned_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='entity_mentions')
    mentioned_at = models.DateTimeField(auto_now_add=True)
    
    # Processing status
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='verified_mentions')
    
    class Meta:
        indexes = [
            models.Index(fields=['entity_type', 'entity_id', '-mentioned_at']),
            models.Index(fields=['mentioned_by', '-mentioned_at']),
            models.Index(fields=['source_type', 'source_id']),
            models.Index(fields=['confidence_score']),
        ]
    
    def __str__(self):
        return f"{self.entity_type} '{self.entity_name}' mentioned in {self.source_type}"


class EntityRelationship(models.Model):
    """Track relationships between entities for knowledge graph building"""
    # Source entity
    from_entity_type = models.CharField(max_length=50)
    from_entity_id = models.CharField(max_length=255)
    from_entity_name = models.CharField(max_length=255)
    
    # Target entity
    to_entity_type = models.CharField(max_length=50)
    to_entity_id = models.CharField(max_length=255)
    to_entity_name = models.CharField(max_length=255)
    
    # Relationship details
    relationship_type = models.CharField(max_length=100, choices=[
        ('works_on', 'Works On'),
        ('assigned_to', 'Assigned To'),
        ('depends_on', 'Depends On'),
        ('blocks', 'Blocks'),
        ('related_to', 'Related To'),
        ('mentions', 'Mentions'),
        ('located_in', 'Located In'),
        ('managed_by', 'Managed By'),
        ('collaborates_with', 'Collaborates With'),
        ('conflicts_with', 'Conflicts With'),
        ('resolves', 'Resolves'),
        ('documents', 'Documents'),
        ('part_of', 'Part Of'),
        ('contains', 'Contains'),
    ])
    
    # Relationship strength and metadata
    strength = models.FloatField(default=1.0, help_text='Strength of the relationship (0.0 to 1.0)')
    confidence = models.FloatField(default=1.0, help_text='Confidence in the relationship accuracy')
    
    # Context
    discovered_from = models.CharField(max_length=100, help_text='How this relationship was discovered')
    evidence_sources = models.JSONField(default=list, help_text='List of evidence sources')
    metadata = models.JSONField(default=dict)
    
    # Temporal aspects
    relationship_start = models.DateTimeField(blank=True, null=True)
    relationship_end = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Verification
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='verified_relationships')
    
    class Meta:
        unique_together = ['from_entity_type', 'from_entity_id', 'to_entity_type', 'to_entity_id', 'relationship_type']
        indexes = [
            models.Index(fields=['from_entity_type', 'from_entity_id']),
            models.Index(fields=['to_entity_type', 'to_entity_id']),
            models.Index(fields=['relationship_type', '-strength']),
            models.Index(fields=['confidence', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.from_entity_name} {self.relationship_type} {self.to_entity_name}"


class AICommand(models.Model):
    """Track AI commands and their execution for intelligence features"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='ai_commands')
    
    # Command details
    command_type = models.CharField(max_length=100, choices=[
        ('summarize', 'Summarize'),
        ('analyze', 'Analyze'),
        ('recommend', 'Recommend'),
        ('search', 'Intelligent Search'),
        ('extract', 'Extract Information'),
        ('classify', 'Classify'),
        ('predict', 'Predict'),
        ('generate', 'Generate Content'),
    ])
    
    # Input and context
    input_text = models.TextField()
    context_data = models.JSONField(default=dict)
    
    # Processing
    status = models.CharField(max_length=20, default='pending', choices=[
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ])
    
    # Results
    output_text = models.TextField(blank=True, null=True)
    output_data = models.JSONField(default=dict)
    confidence_score = models.FloatField(blank=True, null=True)
    
    # Performance metrics
    processing_time_ms = models.IntegerField(blank=True, null=True)
    tokens_used = models.IntegerField(blank=True, null=True)
    cost_usd = models.DecimalField(max_digits=10, decimal_places=4, blank=True, null=True)
    
    # Error handling
    error_message = models.TextField(blank=True, null=True)
    retry_count = models.IntegerField(default=0)
    
    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    # User feedback
    user_rating = models.IntegerField(blank=True, null=True, choices=[
        (1, 'Poor'),
        (2, 'Fair'),
        (3, 'Good'),
        (4, 'Very Good'),
        (5, 'Excellent'),
    ])
    user_feedback = models.TextField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-started_at']),
            models.Index(fields=['command_type', '-started_at']),
            models.Index(fields=['status', '-started_at']),
            models.Index(fields=['user_rating', '-completed_at']),
        ]
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.command_type} by {self.user.username}"


class KnowledgeGraphNode(models.Model):
    """Nodes in the knowledge graph for relationship mapping"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Node identity
    entity_type = models.CharField(max_length=50)
    entity_id = models.CharField(max_length=255)
    entity_name = models.CharField(max_length=255)
    
    # Node properties
    properties = models.JSONField(default=dict)
    tags = models.JSONField(models.CharField(max_length=50), default=list, blank=True)
    
    # Graph metrics
    centrality_score = models.FloatField(default=0.0, help_text='How central this node is in the graph')
    connection_count = models.IntegerField(default=0, help_text='Number of connections to other nodes')
    importance_score = models.FloatField(default=0.0, help_text='Calculated importance in the domain')
    
    # Temporal data
    first_seen = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_by = models.CharField(max_length=100, default='system')
    metadata = models.JSONField(default=dict)
    
    class Meta:
        unique_together = ['entity_type', 'entity_id']
        indexes = [
            models.Index(fields=['entity_type', 'entity_name']),
            models.Index(fields=['-centrality_score']),
            models.Index(fields=['-importance_score']),
            models.Index(fields=['-connection_count']),
            models.Index(fields=['-last_activity']),
        ]
    
    def __str__(self):
        return f"{self.entity_type}: {self.entity_name}"


class EntityHierarchy(models.Model):
    """Track hierarchical relationships between entities"""
    parent_entity_type = models.CharField(max_length=50)
    parent_entity_id = models.CharField(max_length=255)
    parent_entity_name = models.CharField(max_length=255)
    
    child_entity_type = models.CharField(max_length=50)
    child_entity_id = models.CharField(max_length=255)
    child_entity_name = models.CharField(max_length=255)
    
    # Hierarchy details
    hierarchy_type = models.CharField(max_length=50, choices=[
        ('organizational', 'Organizational'),
        ('functional', 'Functional'),
        ('geographical', 'Geographical'),
        ('temporal', 'Temporal'),
        ('logical', 'Logical'),
    ])
    
    level = models.IntegerField(default=1, help_text='Depth level in the hierarchy')
    order = models.IntegerField(default=0, help_text='Order among siblings')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['parent_entity_type', 'parent_entity_id', 'child_entity_type', 'child_entity_id']
        indexes = [
            models.Index(fields=['parent_entity_type', 'parent_entity_id', 'level']),
            models.Index(fields=['child_entity_type', 'child_entity_id']),
            models.Index(fields=['hierarchy_type', 'level']),
        ]
    
    def __str__(self):
        return f"{self.parent_entity_name} → {self.child_entity_name}"


class ChainedEntityContext(models.Model):
    """Context chains for entity relationships and mentions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Chain identification
    chain_id = models.CharField(max_length=100)
    sequence_number = models.IntegerField()
    
    # Entity in this step
    entity_type = models.CharField(max_length=50)
    entity_id = models.CharField(max_length=255)
    entity_name = models.CharField(max_length=255)
    
    # Context data
    context_type = models.CharField(max_length=50, choices=[
        ('conversation', 'Conversation'),
        ('document', 'Document'),
        ('workflow', 'Workflow'),
        ('analysis', 'Analysis'),
    ])
    context_data = models.JSONField(default=dict)
    
    # Chain metadata
    chain_strength = models.FloatField(default=1.0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['chain_id', 'sequence_number']
        indexes = [
            models.Index(fields=['chain_id', 'sequence_number']),
            models.Index(fields=['entity_type', 'entity_id']),
            models.Index(fields=['context_type', '-created_at']),
        ]
    
    def __str__(self):
        return f"Chain {self.chain_id}[{self.sequence_number}]: {self.entity_name}"


class ChainedEntityMention(models.Model):
    """Enhanced entity mentions with chaining context"""
    # Base mention info (similar to EntityMention but enhanced)
    entity_type = models.CharField(max_length=50)
    entity_id = models.CharField(max_length=255)
    entity_name = models.CharField(max_length=255)
    
    # Source context
    source_type = models.CharField(max_length=50)
    source_id = models.CharField(max_length=255)
    source_content_excerpt = models.TextField()
    
    # Mention specifics
    mention_text = models.CharField(max_length=500)
    mention_position = models.IntegerField()
    mention_length = models.IntegerField()
    
    # Chaining information
    context_chain = models.ForeignKey(ChainedEntityContext, on_delete=models.CASCADE, blank=True, null=True, related_name='mentions')
    related_mentions = models.ManyToManyField('self', symmetrical=True, blank=True)
    
    # Confidence and processing
    confidence_score = models.FloatField(default=1.0)
    processing_method = models.CharField(max_length=50, default='manual')
    
    # User context
    mentioned_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='chained_mentions')
    mentioned_at = models.DateTimeField(auto_now_add=True)
    
    # Verification and feedback
    is_verified = models.BooleanField(default=False)
    verification_notes = models.TextField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['entity_type', 'entity_id', '-mentioned_at']),
            models.Index(fields=['source_type', 'source_id']),
            models.Index(fields=['mentioned_by', '-mentioned_at']),
            models.Index(fields=['confidence_score', '-mentioned_at']),
            models.Index(fields=['context_chain']),
        ]
    
    def __str__(self):
        return f"Mention: {self.entity_name} in {self.source_type}"
