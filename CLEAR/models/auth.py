"""
Authentication and Security Models
Handles user authentication, MFA, permissions, and security events
"""

import uuid
from django.contrib.auth.models import AbstractUser
from django.contrib.gis.db import models
from django.utils import timezone
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
try:
    import pyotp
except ImportError:
    pyotp = None
import secrets
import string
from datetime import timedelta


class User(AbstractUser):
    """Extended User model with CLEAR-specific fields"""
    first_name = models.Char<PERSON>ield(max_length=150)
    last_name = models.CharField(max_length=150)
    email = models.EmailField(unique=True)
    display_name = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    role = models.CharField(max_length=50, default="user",
                           choices=[('admin', 'Admin'), ('user', 'User'), ('manager', 'Manager'), ('coordinator', 'Coordinator')])
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, null=True, blank=True, related_name='users')
    department = models.CharField(max_length=50, blank=True, null=True)
    job_title = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=20, default="pending", 
                             choices=[('pending', 'Pending'), ('active', 'Active'), ('inactive', 'Inactive')])
    email_verified = models.BooleanField(default=False)
    invite_token = models.CharField(max_length=255, blank=True, null=True)
    invite_expires_at = models.DateTimeField(blank=True, null=True)
    permissions = JSONField(default=dict)
    last_login_at = models.DateTimeField(blank=True, null=True)
    last_seen_at = models.DateTimeField(blank=True, null=True)
    last_activity = models.DateTimeField(blank=True, null=True)
    created_by = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='invited_users')
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    salary = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    pay_rate_effective_date = models.DateField(blank=True, null=True)
    pay_rate_notes = models.TextField(blank=True, null=True)
    email_domain = models.CharField(max_length=255, blank=True, null=True)
    
    # Legacy fields for backward compatibility
    avatar_url = models.URLField(blank=True, null=True)
    unit_preference = models.CharField(max_length=20, default="imperial")
    custom_settings = JSONField(default=dict)
    reset_password_on_login = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)
    created_by_saml = models.BooleanField(default=False)
    dashboard_layout = JSONField(default=dict)
    ui_preferences = JSONField(default=dict)
    
    # Multi-Factor Authentication fields
    is_mfa_enabled = models.BooleanField(default=False)
    totp_secret = models.CharField(max_length=32, blank=True, null=True)
    backup_tokens = JSONField(default=list)
    mfa_setup_completed = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    def get_initials(self):
        """Get user initials for avatars"""
        if self.first_name and self.last_name:
            return f"{self.first_name[0]}{self.last_name[0]}".upper()
        elif self.first_name:
            return self.first_name[0].upper()
        elif self.last_name:
            return self.last_name[0].upper()
        elif self.username:
            return self.username[0].upper()
        else:
            return "?"
    def generate_totp_secret(self) -> str:
        """Generate a new TOTP secret"""
        if pyotp is None:
            # Fallback if pyotp is not available
            import secrets
            secret = secrets.token_urlsafe(32)
        else:
            secret = pyotp.random_base32()
        self.totp_secret = secret
        return secret

    def get_totp_uri(self) -> str | None:
        """Get the TOTP provisioning URI"""
        if not self.totp_secret or pyotp is None:
            return None
        totp = pyotp.TOTP(self.totp_secret)
        return totp.provisioning_uri(
            name=self.email,
            issuer_name="CLEAR Platform"
        )

    def verify_totp(self, token: str) -> bool:
        """Verify a TOTP token"""
        if not self.totp_secret or pyotp is None:
            return False
        totp = pyotp.TOTP(self.totp_secret)
        return totp.verify(token)
    
    def generate_backup_tokens(self, count=8):
        """Generate backup tokens for MFA recovery"""
        tokens = []
        for _ in range(count):
            token = ''.join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(8))
            tokens.append(token)
        self.backup_tokens = tokens
        return tokens
    
    def use_backup_token(self, token):
        """Use a backup token for MFA"""
        if token in self.backup_tokens:
            self.backup_tokens.remove(token)
            self.save()
            return True
        return False


class Permission(models.Model):
    """Custom permissions system"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class MFASession(models.Model):
    """Track MFA verification sessions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='mfa_sessions')
    session_key = models.CharField(max_length=40, unique=True)
    is_verified = models.BooleanField(default=False)
    verification_method = models.CharField(max_length=20, choices=[
        ('totp', 'TOTP Authenticator'),
        ('backup', 'Backup Token')
    ], blank=True, null=True)
    verified_at = models.DateTimeField(blank=True, null=True)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def __str__(self):
        return f"MFA Session for {self.user.email}"


class RolePermission(models.Model):
    """Role-based permissions"""
    role = models.CharField(max_length=50)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['role', 'permission']


class PasswordResetToken(models.Model):
    """Password reset tokens"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    token = models.CharField(max_length=255, unique=True)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(blank=True, null=True)


class SecurityEvent(models.Model):
    """Security audit logging"""
    EVENT_TYPES = [
        ('login_success', 'Login Success'),
        ('login_failure', 'Login Failure'),
        ('mfa_setup', 'MFA Setup'),
        ('mfa_disable', 'MFA Disable'),
        ('password_change', 'Password Change'),
        ('password_reset', 'Password Reset'),
        ('account_lockout', 'Account Lockout'),
        ('suspicious_activity', 'Suspicious Activity'),
        ('privilege_escalation', 'Privilege Escalation'),
        ('data_access', 'Data Access'),
        ('data_export', 'Data Export'),
        ('configuration_change', 'Configuration Change'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    event_type = models.CharField(max_length=30, choices=EVENT_TYPES)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    additional_data = models.JSONField(default=dict)
    severity = models.CharField(max_length=10, choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ], default='medium')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['severity', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.user} at {self.created_at}"


class LoginAttempt(models.Model):
    """Track login attempts for security monitoring"""
    email = models.EmailField()
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(null=True, blank=True)
    success = models.BooleanField(default=False)
    failure_reason = models.CharField(max_length=100, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['success', 'created_at']),
        ]
    
    def __str__(self):
        status = 'Success' if self.success else 'Failed'
        return f"{status} login attempt for {self.email} from {self.ip_address}"


class PasswordResetAttempt(models.Model):
    """Track password reset attempts"""
    email = models.EmailField()
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(null=True, blank=True)
    success = models.BooleanField(default=False)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
        ]
    
    def __str__(self):
        status = 'Success' if self.success else 'Failed'
        return f"{status} password reset for {self.email}"


class APIToken(models.Model):
    """API tokens for programmatic access"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='api_tokens')
    name = models.CharField(max_length=255, help_text="Human-readable name for identifying the token")
    token = models.CharField(max_length=255, unique=True, db_index=True)
    
    # Scopes and permissions
    scopes = models.JSONField(default=list, help_text="List of permission scopes this token grants")
    is_active = models.BooleanField(default=True)
    
    # Usage tracking
    last_used = models.DateTimeField(null=True, blank=True)
    usage_count = models.IntegerField(default=0)
    
    # Lifecycle
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    revoked_at = models.DateTimeField(null=True, blank=True)
    
    # Security metadata
    created_ip = models.GenericIPAddressField(null=True, blank=True)
    last_used_ip = models.GenericIPAddressField(null=True, blank=True)
    allowed_ips = models.JSONField(models.GenericIPAddressField(), default=list, blank=True, 
                            help_text="If specified, token can only be used from these IPs")
    
    # Additional metadata
    description = models.TextField(blank=True, null=True)
    metadata = models.JSONField(default=dict)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['token']),
            models.Index(fields=['is_active', '-created_at']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.user.email}"
    
    def is_expired(self):
        """Check if token has expired"""
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at
    
    def is_valid(self):
        """Check if token is valid for use"""
        return self.is_active and not self.is_expired() and not self.revoked_at
    
    def record_usage(self, ip_address=None):
        """Record token usage"""
        self.last_used = timezone.now()
        self.usage_count += 1
        if ip_address:
            self.last_used_ip = ip_address
        self.save(update_fields=['last_used', 'usage_count', 'last_used_ip'])
    
    def revoke(self):
        """Revoke this token"""
        self.is_active = False
        self.revoked_at = timezone.now()
        self.save(update_fields=['is_active', 'revoked_at'])
    
    def has_scope(self, scope):
        """Check if token has a specific scope"""
        return scope in self.scopes or 'all' in self.scopes
    
    def check_ip_allowed(self, ip_address):
        """Check if the given IP is allowed to use this token"""
        if not self.allowed_ips:
            return True
        return ip_address in self.allowed_ips
    
    def generate_token(self):
        """Generate a secure token value"""
        # Generate a URL-safe token
        self.token = secrets.token_urlsafe(32)
        return self.token
    
    def save(self, *args, **kwargs):
        """Override save to generate token if not set"""
        if not self.token:
            self.generate_token()
        super().save(*args, **kwargs)


class ConnectedAccount(models.Model):
    """External accounts connected via OAuth or similar"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='connected_accounts')
    
    # Provider information
    provider = models.CharField(max_length=50, choices=[
        ('google', 'Google'),
        ('microsoft', 'Microsoft'),
        ('github', 'GitHub'),
        ('linkedin', 'LinkedIn'),
        ('slack', 'Slack'),
        ('azure', 'Azure Active Directory'),
        ('okta', 'Okta'),
        ('auth0', 'Auth0'),
        ('saml', 'SAML'),
        ('oauth2', 'Generic OAuth2'),
    ])
    provider_account_id = models.CharField(max_length=255, help_text="Unique ID from the provider")
    provider_username = models.CharField(max_length=255, blank=True, null=True)
    provider_email = models.EmailField(blank=True, null=True)
    
    # OAuth tokens (encrypted in production)
    access_token = models.TextField(blank=True, null=True)
    refresh_token = models.TextField(blank=True, null=True)
    token_expires_at = models.DateTimeField(blank=True, null=True)
    
    # Account status
    is_active = models.BooleanField(default=True)
    is_primary = models.BooleanField(default=False, help_text="Primary account for this provider")
    
    # Permissions and scopes
    granted_scopes = models.JSONField(models.CharField(max_length=100), default=list, blank=True)
    
    # Metadata
    provider_data = models.JSONField(default=dict, help_text="Additional provider-specific data")
    
    # Timestamps
    connected_at = models.DateTimeField(auto_now_add=True)
    last_refreshed = models.DateTimeField(auto_now=True)
    last_used = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        unique_together = ['user', 'provider', 'provider_account_id']
        indexes = [
            models.Index(fields=['user', 'provider']),
            models.Index(fields=['is_active', 'provider']),
            models.Index(fields=['token_expires_at']),
        ]
        ordering = ['-connected_at']
    
    def __str__(self):
        return f"{self.user.email} - {self.provider} ({self.provider_username or self.provider_account_id})"
    
    def is_token_expired(self):
        """Check if access token has expired"""
        if not self.token_expires_at:
            return False
        return timezone.now() >= self.token_expires_at
    
    def needs_refresh(self):
        """Check if token needs refreshing (5 minutes before expiry)"""
        if not self.token_expires_at:
            return False
        return timezone.now() >= (self.token_expires_at - timedelta(minutes=5))
    
    def disconnect(self):
        """Disconnect this account"""
        self.is_active = False
        self.access_token = None
        self.refresh_token = None
        self.save()
    
    def update_tokens(self, access_token, refresh_token=None, expires_in=None):
        """Update OAuth tokens"""
        self.access_token = access_token
        if refresh_token:
            self.refresh_token = refresh_token
        if expires_in:
            self.token_expires_at = timezone.now() + timedelta(seconds=expires_in)
        self.last_refreshed = timezone.now()
        self.save()
    
    def record_usage(self):
        """Record that this account was used"""
        self.last_used = timezone.now()
        self.save(update_fields=['last_used'])


class LoginHistory(models.Model):
    """Track user login history for security and auditing"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_history')
    
    # Login details
    timestamp = models.DateTimeField(auto_now_add=True)
    success = models.BooleanField(default=True)
    failure_reason = models.CharField(max_length=100, blank=True, null=True)
    
    # Authentication method
    auth_method = models.CharField(max_length=50, default='password', choices=[
        ('password', 'Password'),
        ('mfa', 'Multi-Factor Authentication'),
        ('oauth', 'OAuth'),
        ('saml', 'SAML'),
        ('api_token', 'API Token'),
        ('session', 'Session Cookie'),
        ('remember_me', 'Remember Me Token'),
    ])
    
    # Device and location
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True, null=True)
    device_type = models.CharField(max_length=20, blank=True, null=True)
    browser = models.CharField(max_length=50, blank=True, null=True)
    operating_system = models.CharField(max_length=50, blank=True, null=True)
    
    # Geographic information
    country = models.CharField(max_length=100, blank=True, null=True)
    region = models.CharField(max_length=100, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    latitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True)
    
    # Session information
    session_key = models.CharField(max_length=40, blank=True, null=True)
    session_duration = models.DurationField(blank=True, null=True)
    
    # Security flags
    is_suspicious = models.BooleanField(default=False)
    risk_score = models.IntegerField(default=0, help_text="Risk score 0-100")
    security_alerts = models.JSONField(models.CharField(max_length=100), default=list, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['ip_address', '-timestamp']),
            models.Index(fields=['success', '-timestamp']),
            models.Index(fields=['is_suspicious', '-timestamp']),
            models.Index(fields=['auth_method', '-timestamp']),
        ]
        ordering = ['-timestamp']
        verbose_name = 'Login History'
        verbose_name_plural = 'Login Histories'
    
    def __str__(self):
        status = 'Success' if self.success else f'Failed ({self.failure_reason})'
        return f"{self.user.email} - {status} from {self.ip_address} at {self.timestamp}"
    
    def calculate_risk_score(self):
        """Calculate risk score based on various factors"""
        score = 0
        
        # Check for new location
        recent_logins = LoginHistory.objects.filter(
            user=self.user,
            success=True,
            timestamp__gte=timezone.now() - timezone.timedelta(days=30)
        ).exclude(id=self.id)
        
        # New IP address
        if not recent_logins.filter(ip_address=self.ip_address).exists():
            score += 20
        
        # New country
        if self.country and not recent_logins.filter(country=self.country).exists():
            score += 30
        
        # Failed login attempts
        recent_failures = LoginAttempt.objects.filter(
            email=self.user.email,
            ip_address=self.ip_address,
            success=False,
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).count()
        if recent_failures > 3:
            score += 25
        
        # Time-based anomaly (login at unusual hour for user)
        # This is simplified - in production, analyze user's typical login patterns
        login_hour = self.timestamp.hour
        if 2 <= login_hour <= 5:  # Late night login
            score += 15
        
        self.risk_score = min(score, 100)
        return self.risk_score
    
    def flag_if_suspicious(self):
        """Flag login as suspicious based on risk score and patterns"""
        risk_score = self.calculate_risk_score()
        
        if risk_score >= 50:
            self.is_suspicious = True
            
            # Add security alerts
            alerts = []
            if risk_score >= 80:
                alerts.append('high_risk_login')
            if self.country and not LoginHistory.objects.filter(
                user=self.user, country=self.country
            ).exclude(id=self.id).exists():
                alerts.append('new_country_login')
            if not LoginHistory.objects.filter(
                user=self.user, ip_address=self.ip_address
            ).exclude(id=self.id).exists():
                alerts.append('new_ip_address')
            
            self.security_alerts = alerts
            
        self.save()