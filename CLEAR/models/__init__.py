"""
CLEAR Models Package
Organized by domain for improved maintainability

This package splits the large models.py file into logical domain modules:
- auth.py: Authentication and security models
- projects.py: Project management core models  
- spatial.py: GIS and spatial analysis models
- documents.py: Document management system models
- messaging.py: Communication and messaging models
- analytics.py: Analytics and reporting models
- system.py: System settings and configuration models
- knowledge.py: Knowledge management models
- financial.py: Time tracking and financial models
- intelligence.py: AI and knowledge graph models
- user_activity.py: User activity and session models
- implementation.py: Implementation task management models
"""

from .auth import *  # type: ignore
from .projects import *  # type: ignore
from .spatial import *  # type: ignore
from .documents import *  # type: ignore
from .messaging import *  # type: ignore
from .analytics import *  # type: ignore
from .system import *  # type: ignore
from .knowledge import *  # type: ignore
from .financial import *  # type: ignore
from .intelligence import *  # type: ignore
from .user_activity import *  # type: ignore

# Import all models from domain modules to maintain Django compatibility

# Maintain backward compatibility
__all__ = [
    # Authentication models
    'User', 'Permission', 'MFASession', 'RolePermission', 'PasswordResetToken',
    'SecurityEvent', 'LoginAttempt', 'PasswordResetAttempt', 'APIToken',
    'ConnectedAccount', 'LoginHistory',

    # Project models
    'Project', 'ProjectTemplate', 'ProjectPhase', 'ProjectMember', 'ProjectActivity',
    'ProjectLog', 'Task', 'Workflow', 'WorkflowExecution',

    # Stakeholder models
    'Stakeholder', 'Organization', 'OrganizationMember',

    # Spatial models
    'CoordinateSystem', 'Utility', 'LineStyle', 'UtilityLineData', 'Conflict',
    'GISLayer', 'SpatialAnnotation', 'CollaborativeDrawing', 'CollaborationSession',
    'UtilityPhaseStatus',

    # Document models
    'Document', 'DocumentVersion', 'DocumentShare', 'DocumentActivity',
    'DocumentDiscussion', 'DocumentDiscussionParticipant', 'DocumentDiscussionMessage',
    'DocumentReviewProcess', 'DocumentReviewer', 'DocumentVersionBranch',
    'DocumentVersionDiff', 'DocumentLock', 'DocumentEditingSession',
    'DocumentSearchIndex', 'SavedDocumentSearch',

    # Messaging models
    'Conversation', 'ConversationMember', 'ChatMessage', 'MessageRead',
    'WhisperMessage', 'MessageThread', 'MessageMention', 'MessageReaction',
    'InternalEmail', 'InternalEmailMessage', 'Comment', 'CommentManager',

    # Analytics models
    'SystemMetric', 'BusinessMetric', 'AnalyticsReport', 'ReportExecution',
    'AnalyticsEvent', 'Report',

    # System models
    'CollaborationSettings', 'Activity', 'Notification', 'NotificationSettings',
    'NotificationBatch', 'NotificationDelivery', 'AppVersion', 'UserVersionAcknowledgment',
    'DatabaseBackup', 'PendingChange', 'AdminLog',

    # Knowledge models
    'KnowledgeArticle', 'KnowledgeCategory', 'Article', 'ArticleCategory',
    'ArticleView', 'ArticleVote', 'ArticleRevision', 'ArticleAttachment',

    # Financial models
    'TimeEntry', 'TimesheetPeriod', 'TimesheetEntry', 'WorkType',
    'FeeTemplate', 'FeeTemplateSection', 'FeeTemplateOption',
    'ContractAdministration', 'Invoice',

    # Intelligence models
    'EntityMention', 'EntityRelationship', 'AICommand', 'KnowledgeGraphNode',
    'EntityHierarchy', 'ChainedEntityContext', 'ChainedEntityMention',

    # User activity models
    'UserNote', 'UserActivity', 'UserSession', 'UserSkill', 'UserPresence',
    'FeatureRequest', 'FeatureVote', 'Note', 'NotebookEntry',

    # Implementation models
    'ImplementationTask', 'ImplementationNote', 'ImplementationFile',
]
