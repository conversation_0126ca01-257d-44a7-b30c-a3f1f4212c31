"""
User Activity and Session Models
Handles user activity tracking, sessions, skills, presence, and notes
"""

import uuid
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.db import models
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.db.models import Q




# from django.contrib.postgres.fields import models.J<PERSON><PERSON>ield  # Commented out for SQLite compatibility


class UserActivity(models.Model):
    """Track user activities across the platform"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='user_activities')
    action = models.CharField(max_length=100)
    description = models.TextField()
    
    # Target object (optional)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, blank=True, null=True)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Context
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='user_activities')
    session_id = models.CharField(max_length=255, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    
    # Activity metadata
    metadata = models.JSONField(default=dict)
    duration_seconds = models.IntegerField(blank=True, null=True)
    
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['action', '-timestamp']),
            models.Index(fields=['project', '-timestamp']),
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['session_id', '-timestamp']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user.username}: {self.action}"


class UserSession(models.Model):
    """Track user login sessions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    
    # Session details
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    device_type = models.CharField(max_length=20, blank=True, null=True)
    browser = models.CharField(max_length=50, blank=True, null=True)
    operating_system = models.CharField(max_length=50, blank=True, null=True)
    
    # Geographic data
    country = models.CharField(max_length=100, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    timezone = models.CharField(max_length=50, blank=True, null=True)
    
    # Session lifecycle
    started_at = models.DateTimeField(auto_now_add=True)
    last_activity_at = models.DateTimeField(auto_now=True)
    ended_at = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    # Security
    is_suspicious = models.BooleanField(default=False)
    security_flags = models.JSONField(default=list, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-started_at']),
            models.Index(fields=['session_key']),
            models.Index(fields=['is_active', '-last_activity_at']),
            models.Index(fields=['ip_address', '-started_at']),
            models.Index(fields=['is_suspicious', '-started_at']),
        ]
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.user.username} session from {self.ip_address}"
    
    def get_duration(self):
        """Get session duration in seconds"""
        end_time = self.ended_at or timezone.now()
        return (end_time - self.started_at).total_seconds()
    
    def end_session(self):
        """Mark session as ended"""
        self.is_active = False
        self.ended_at = timezone.now()
        self.save(update_fields=['is_active', 'ended_at'])


class UserSkill(models.Model):
    """Track user skills and expertise levels"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='skills')
    skill_name = models.CharField(max_length=100)
    category = models.CharField(max_length=50, choices=[
        ('technical', 'Technical'),
        ('software', 'Software'),
        ('management', 'Management'),
        ('communication', 'Communication'),
        ('domain', 'Domain Knowledge'),
        ('certification', 'Certification'),
    ])
    
    # Skill level
    proficiency_level = models.CharField(max_length=20, choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ])
    
    # Experience
    years_experience = models.DecimalField(max_digits=4, decimal_places=1, blank=True, null=True)
    last_used_date = models.DateField(blank=True, null=True)
    
    # Validation
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='verified_skills')
    verified_at = models.DateTimeField(blank=True, null=True)
    
    # Additional info
    notes = models.TextField(blank=True, null=True)
    certifications = models.JSONField(default=list, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'skill_name']
        indexes = [
            models.Index(fields=['user', 'category']),
            models.Index(fields=['skill_name', 'proficiency_level']),
            models.Index(fields=['category', 'proficiency_level']),
            models.Index(fields=['is_verified', '-verified_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username}: {self.skill_name} ({self.proficiency_level})"


class UserPresence(models.Model):
    """Track user presence on mapping interface"""
    
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='user_presences')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='map_presences')
    last_cursor_position = models.PointField(srid=4326, blank=True, null=True)
    current_tool = models.CharField(max_length=50, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    session_id = models.CharField(max_length=255)  # WebSocket session identifier
    last_seen = models.DateTimeField(auto_now=True)
    joined_at = models.DateTimeField(auto_now_add=True)
    left_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        db_table = 'user_presence'
        unique_together = ['user', 'project', 'session_id']
        indexes = [
            models.Index(fields=['project', 'is_active']),
            models.Index(fields=['last_seen']),
        ]
    
    def __str__(self):
        return f"{self.user.username} on {self.project.name}"
    
    def mark_inactive(self):
        """Mark user as inactive"""
        self.is_active = False
        self.left_at = timezone.now()
        self.save()
    
    @classmethod
    def get_active_users(cls, project):
        """Get active users for a project"""
        cutoff_time = timezone.now() - timedelta(minutes=5)
        return cls.objects.filter(
            project=project,
            is_active=True,
            last_seen__gte=cutoff_time
        ).select_related('user')


class UserNote(models.Model):
    """Personal notes by users"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='personal_notes')
    title = models.CharField(max_length=255)
    content = models.TextField()
    
    # Context linking (optional)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, blank=True, null=True)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Organization
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='user_notes')
    tags = models.JSONField(default=list, blank=True)
    
    # Settings
    is_private = models.BooleanField(default=True)
    is_pinned = models.BooleanField(default=False)
    reminder_date = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-updated_at']),
            models.Index(fields=['project', '-updated_at']),
            models.Index(fields=['is_pinned', '-updated_at']),
            models.Index(fields=['reminder_date']),
            models.Index(fields=['content_type', 'object_id']),
        ]
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"{self.user.username}: {self.title}"


class FeatureRequest(models.Model):
    """User feature requests and suggestions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='feature_requests')
    
    # Request details
    title = models.CharField(max_length=255)
    description = models.TextField()
    use_case = models.TextField(blank=True, null=True)
    
    # Categorization
    category = models.CharField(max_length=50, choices=[
        ('ui_ux', 'UI/UX'),
        ('functionality', 'New Functionality'),
        ('integration', 'Integration'),
        ('performance', 'Performance'),
        ('mobile', 'Mobile'),
        ('reporting', 'Reporting'),
        ('collaboration', 'Collaboration'),
        ('automation', 'Automation'),
        ('other', 'Other'),
    ])
    priority = models.CharField(max_length=20, default='medium', choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ])
    
    # Status tracking
    status = models.CharField(max_length=20, default='submitted', choices=[
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('in_development', 'In Development'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('duplicate', 'Duplicate'),
    ])
    
    # Engagement
    vote_count = models.IntegerField(default=0)
    comment_count = models.IntegerField(default=0)
    
    # Administrative
    reviewed_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='reviewed_features')
    reviewed_at = models.DateTimeField(blank=True, null=True)
    admin_notes = models.TextField(blank=True, null=True)
    
    # Estimated effort
    estimated_effort_days = models.IntegerField(blank=True, null=True)
    target_release = models.CharField(max_length=50, blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['status', '-vote_count']),
            models.Index(fields=['category', '-created_at']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['-vote_count', '-created_at']),
        ]
        ordering = ['-vote_count', '-created_at']
    
    def __str__(self):
        return self.title


class FeatureVote(models.Model):
    """Votes on feature requests"""
    feature_request = models.ForeignKey(FeatureRequest, on_delete=models.CASCADE, related_name='votes')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='feature_votes')
    vote_type = models.CharField(max_length=10, choices=[
        ('upvote', 'Upvote'),
        ('downvote', 'Downvote'),
    ])
    comment = models.TextField(blank=True, null=True)
    voted_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['feature_request', 'user']
        indexes = [
            models.Index(fields=['feature_request', 'vote_type']),
            models.Index(fields=['user', '-voted_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} {self.vote_type} on {self.feature_request.title}"


class NotebookEntry(models.Model):
    """Personal notebook entries for users to track thoughts, ideas, and notes"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='notebook_entries')
    title = models.CharField(max_length=255)
    content = models.TextField(help_text="Supports markdown formatting")
    
    # Organization
    category = models.CharField(max_length=50, default='general', choices=[
        ('general', 'General'),
        ('meeting', 'Meeting Notes'),
        ('project', 'Project Notes'),
        ('personal', 'Personal'),
        ('technical', 'Technical Notes'),
        ('ideas', 'Ideas & Brainstorming'),
        ('todo', 'To-Do Items'),
        ('reference', 'Reference Material'),
    ])
    tags = models.JSONField(default=list, help_text="List of tags for categorization")
    is_favorite = models.BooleanField(default=False)
    
    # Project association (optional)
    project = models.ForeignKey('Project', on_delete=models.CASCADE, null=True, blank=True, 
                               related_name='notebook_entries', help_text="Optional project association")
    
    # Rich content support
    has_attachments = models.BooleanField(default=False)
    attachments = models.JSONField(default=list, help_text="List of attachment metadata")
    
    # Privacy and sharing
    is_private = models.BooleanField(default=True, help_text="Private entries are only visible to the owner")
    shared_with = models.ManyToManyField('User', blank=True, related_name='shared_notebook_entries')
    
    # Metadata
    word_count = models.IntegerField(default=0)
    reading_time_minutes = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_accessed_at = models.DateTimeField(null=True, blank=True)
    
    # Search and indexing
    search_vector = models.TextField(blank=True, help_text="Full-text search index")
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-updated_at']),
            models.Index(fields=['user', 'category', '-updated_at']),
            models.Index(fields=['user', 'is_favorite', '-updated_at']),
            models.Index(fields=['project', '-updated_at']),
            models.Index(fields=['is_private', '-updated_at']),
            models.Index(fields=['-created_at']),
        ]
        ordering = ['-updated_at']
        verbose_name = 'Notebook Entry'
        verbose_name_plural = 'Notebook Entries'
    
    def __str__(self):
        return f"{self.title} - {self.user.username}"
    
    def save(self, *args, **kwargs):
        """Override save to calculate word count and reading time"""
        # Calculate word count
        self.word_count = len(self.content.split())
        
        # Calculate reading time (assuming 200 words per minute)
        self.reading_time_minutes = max(1, self.word_count // 200)
        
        # Update search vector (simplified - in production use PostgreSQL full-text search)
        self.search_vector = f"{self.title} {self.content} {' '.join(self.tags)}"
        
        super().save(*args, **kwargs)
    
    def mark_accessed(self):
        """Update last accessed timestamp"""
        self.last_accessed_at = timezone.now()
        self.save(update_fields=['last_accessed_at'])
    
    def get_tag_list(self):
        """Get tags as a list"""
        return self.tags if isinstance(self.tags, list) else []
    
    def add_tag(self, tag):
        """Add a tag to the entry"""
        if isinstance(self.tags, list):
            if tag not in self.tags:
                self.tags.append(tag)
                self.save(update_fields=['tags'])
        else:
            self.tags = [tag]
            self.save(update_fields=['tags'])
    
    def remove_tag(self, tag):
        """Remove a tag from the entry"""
        if isinstance(self.tags, list) and tag in self.tags:
            self.tags.remove(tag)
            self.save(update_fields=['tags'])
    
    def toggle_favorite(self):
        """Toggle favorite status"""
        self.is_favorite = not self.is_favorite
        self.save(update_fields=['is_favorite'])
    
    @classmethod
    def get_user_tags(cls, user):
        """Get all unique tags used by a user"""
        entries = cls.objects.filter(user=user).values_list('tags', flat=True)
        all_tags = []
        for tags in entries:
            if isinstance(tags, list):
                all_tags.extend(tags)
        return sorted(list(set(all_tags)))
    
    @classmethod
    def search(cls, user, query):
        """Search notebook entries for a user"""
        return cls.objects.filter(
            user=user
        ).filter(
            Q(title__icontains=query) | 
            Q(content__icontains=query) | 
            Q(search_vector__icontains=query)
        )


# Legacy model for backward compatibility
class Note(models.Model):
    """Legacy note model"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='legacy_notes')
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Note by {self.user.username}"
