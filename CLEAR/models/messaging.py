"""
Messaging and Communication Models
Handles chat messages, conversations, comments, and communication features
"""

import re
import uuid
from django.contrib.gis.db import models
from django.utils import timezone
from django.db.models import Count



# from django.contrib.postgres.fields import models.JSO<PERSON>ield  # Commented out for SQLite compatibility


class CommentManager(models.Manager):
    """Custom manager for Comment model"""

    def for_entity(self, entity_type, entity_id):
        """Get comments for a specific entity"""
        return self.filter(
            commentable_type=entity_type.lower(),
            commentable_id=str(entity_id),
            deleted_at__isnull=True
        ).select_related('user').order_by('-created_at')

    def top_level_for_entity(self, entity_type, entity_id):
        """Get top-level comments (no parent) for a specific entity"""
        return self.for_entity(entity_type, entity_id).filter(parent__isnull=True)

    def count_for_entity(self, entity_type, entity_id):
        """Count comments for a specific entity"""
        return self.filter(
            commentable_type=entity_type.lower(),
            commentable_id=str(entity_id),
            deleted_at__isnull=True
        ).count()


class Comment(models.Model):
    """Universal commenting system"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    commentable_type = models.CharField(max_length=50)
    commentable_id = models.CharField(max_length=255)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='comments')
    content = models.TextField()
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='replies')
    mentions = models.JSONField(models.IntegerField(), default=list)
    deleted_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = CommentManager()

    class Meta:
        indexes = [
            models.Index(fields=['commentable_type', 'commentable_id'], name='comment_entity_idx'),
            models.Index(fields=['user'], name='comment_user_idx'),
            models.Index(fields=['parent'], name='comment_parent_idx'),
        ]

    def __str__(self):
        return f"Comment by {self.user.username} on {self.commentable_type}"

    def soft_delete(self):
        """Soft delete this comment"""
        self.deleted_at = timezone.now()
        self.save()

    def restore(self):
        """Restore soft-deleted comment"""
        self.deleted_at = None
        self.save()

    @property
    def is_deleted(self):
        """Check if comment is soft-deleted"""
        return self.deleted_at is not None

    @property
    def reply_count(self):
        """Get number of replies to this comment"""
        return self.replies.filter(deleted_at__isnull=True).count()

    def get_replies(self):
        """Get all replies to this comment"""
        return self.replies.filter(deleted_at__isnull=True).select_related('user').order_by('created_at')


class Conversation(models.Model):
    """Conversation threads for organizing messages"""
    CONVERSATION_TYPES = [
        ('direct', 'Direct Message'),
        ('group', 'Group Chat'),
        ('project', 'Project Discussion'),
        ('channel', 'Channel/Team Chat'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, blank=True, null=True, help_text='Optional conversation name for group chats')
    conversation_type = models.CharField(max_length=20, choices=CONVERSATION_TYPES, default='direct')
    participants = models.ManyToManyField('User', through='ConversationMember', related_name='conversations')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='conversations')
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_conversations')
    last_message_at = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['conversation_type', '-last_message_at'], name='conv_type_message_idx'),
            models.Index(fields=['project', '-last_message_at'], name='conv_project_message_idx'),
            models.Index(fields=['created_by', '-last_message_at'], name='conv_creator_message_idx'),
        ]
        ordering = ['-last_message_at', '-created_at']

    def __str__(self):
        if self.name:
            return self.name
        elif self.conversation_type == 'direct':
            participants = list(self.participants.all()[:2])
            if len(participants) == 2:
                return f"{participants[0].first_name} & {participants[1].first_name}"
        elif self.project:
            return f"{self.project.name} Discussion"
        return f"{self.get_conversation_type_display()} - {self.created_at.strftime('%m/%d')}"

    def get_unread_count_for_user(self, user):
        """Get count of unread messages for a specific user"""
        last_read = self.members.filter(user=user).first()
        if not last_read or not last_read.last_read_at:
            return self.messages.count()
        return self.messages.filter(created_at__gt=last_read.last_read_at).count()

    def get_last_message(self):
        """Get the most recent message in this conversation"""
        return self.messages.first()

    def add_participant(self, user):
        """Add a user to the conversation"""
        member, created = ConversationMember.objects.get_or_create(
            conversation=self,
            user=user,
            defaults={'joined_at': timezone.now()}
        )
        return member


class ConversationMember(models.Model):
    """Tracks conversation participants and their read status"""
    conversation = models.ForeignKey('Conversation', on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='conversation_memberships')
    joined_at = models.DateTimeField(auto_now_add=True)
    last_read_at = models.DateTimeField(blank=True, null=True)
    is_admin = models.BooleanField(default=False, help_text='Can manage conversation settings')
    notifications_enabled = models.BooleanField(default=True)

    class Meta:
        unique_together = ['conversation', 'user']
        indexes = [
            models.Index(fields=['user', '-last_read_at'], name='conv_member_last_read_idx'),
            models.Index(fields=['conversation', 'user'], name='conv_member_user_idx'),
        ]

    def __str__(self):
        return f"{self.user.username} in {self.conversation}"

    def mark_read(self):
        """Mark conversation as read up to now"""
        self.last_read_at = timezone.now()
        self.save(update_fields=['last_read_at'])


class ChatMessage(models.Model):
    """Consolidated chat messages (combines chat_messages and team_messages)"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='chat_messages')
    conversation = models.ForeignKey('Conversation', on_delete=models.CASCADE, blank=True, null=True, related_name='messages')
    content = models.TextField()
    channel = models.CharField(max_length=50, default='general', help_text='Channel for organizing messages (legacy)')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='chat_messages', help_text='Optional project context')
    timestamp = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Additional message features
    is_urgent = models.BooleanField(default=False, help_text='Mark message as urgent')
    read_by = models.ManyToManyField('User', blank=True, related_name='read_messages', through='MessageRead')
    reply_to = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='replies')
    attachments = models.ManyToManyField('Document', blank=True, related_name='message_attachments')
    message_type = models.CharField(max_length=20, default='text', choices=[
        ('text', 'Text Message'),
        ('system', 'System Message'),
        ('file', 'File Attachment'),
        ('image', 'Image'),
    ])

    class Meta:
        indexes = [
            models.Index(fields=['channel', '-created_at'], name='chat_msg_channel_idx'),
            models.Index(fields=['project', '-created_at'], name='chat_msg_project_idx'),
            models.Index(fields=['user', '-created_at'], name='chat_msg_user_idx'),
            models.Index(fields=['is_urgent', '-created_at'], name='chat_msg_urgent_idx'),
            models.Index(fields=['message_type', '-created_at'], name='chat_msg_type_idx'),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} in #{self.channel}: {self.content[:50]}..."

    def get_read_count(self):
        """Get count of users who have read this message"""
        return self.read_by.count()

    def is_read_by_user(self, user):
        """Check if message has been read by specific user"""
        return self.read_by.filter(id=user.id).exists()

    def mark_read_by_user(self, user):
        """Mark message as read by user"""
        read_record, created = MessageRead.objects.get_or_create(
            message=self,
            user=user,
            defaults={'read_at': timezone.now()}
        )
        return read_record

    def get_thread_count(self):
        """Get count of replies to this message"""
        return self.replies.count()

    def get_conversation_id(self):
        """Get conversation identifier for grouping messages"""
        if self.conversation:
            return str(self.conversation.id)
        elif self.project:
            return f"project_{self.project.id}"
        return f"channel_{self.channel}"

    def is_thread_root(self):
        """Check if this message is the root of a thread"""
        return hasattr(self, 'thread_root') and self.thread_root is not None

    def get_thread(self):
        """Get the MessageThread object if this is a thread root"""
        return getattr(self, 'thread_root', None)

    def has_mentions(self):
        """Check if this message contains @mentions"""
        return self.mentions.exists()

    def get_mentioned_users(self):
        """Get all users mentioned in this message"""
        return self.user.__class__.objects.filter(message_mentions__message=self)

    def get_reactions_summary(self):
        """Get a summary of reactions grouped by emoji"""
        return (self.reactions
                .values('emoji', 'emoji_unicode')
                .annotate(count=Count('id'))
                .order_by('-count'))

    def get_user_reaction(self, user):
        """Get the reaction this user gave to this message, if any"""
        return self.reactions.filter(user=user).first()

    def has_user_reacted(self, user, emoji=None):
        """Check if user has reacted to this message with specific emoji or any emoji"""
        reactions = self.reactions.filter(user=user)
        if emoji:
            reactions = reactions.filter(emoji=emoji)
        return reactions.exists()

    def process_mentions(self):
        """Extract and create mention objects from message content"""
        # Pattern to match @username or @"display name"
        mention_pattern = r'@(?:"([^"]+)"|([a-zA-Z0-9_]+))'

        # Clear existing mentions
        self.mentions.all().delete()

        mentions = []
        for match in re.finditer(mention_pattern, self.content):
            mention_text = match.group(0)
            username = match.group(2) or match.group(1)
            start_pos = match.start()
            end_pos = match.end()

            # Try to find the mentioned user
            try:
                mentioned_user = User.objects.get(username=username)

                # Create mention object
                mention = MessageMention(
                    message=self,
                    mentioned_user=mentioned_user,
                    mention_text=mention_text,
                    start_position=start_pos,
                    end_position=end_pos
                )
                mentions.append(mention)

            except User.DoesNotExist:
                # User not found, skip this mention
                continue

        # Bulk create all mentions
        if mentions:
            MessageMention.objects.bulk_create(mentions)


class MessageRead(models.Model):
    """Track which users have read which messages"""
    message = models.ForeignKey('ChatMessage', on_delete=models.CASCADE, related_name='read_records')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='message_reads')
    read_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['message', 'user']
        indexes = [
            models.Index(fields=['user', '-read_at'], name='msg_read_user_idx'),
            models.Index(fields=['message', 'user'], name='msg_read_message_idx'),
        ]

    def __str__(self):
        return f"{self.user.username} read message {self.message.id}"


class WhisperMessage(models.Model):
    """Private whisper messages between users"""
    sender = models.ForeignKey('User', on_delete=models.CASCADE, related_name='sent_whispers')
    recipient = models.ForeignKey('User', on_delete=models.CASCADE, related_name='received_whispers')
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['recipient', '-created_at'], name='whisper_recipient_idx'),
            models.Index(fields=['sender', '-created_at'], name='whisper_sender_idx'),
            models.Index(fields=['is_read'], name='whisper_read_idx'),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"Whisper from {self.sender.username} to {self.recipient.username}"

    def mark_read(self):
        """Mark whisper as read"""
        self.is_read = True
        self.read_at = timezone.now()
        self.save(update_fields=['is_read', 'read_at'])


class MessageThread(models.Model):
    """Thread organization for message replies"""
    root_message = models.OneToOneField(ChatMessage, on_delete=models.CASCADE, related_name='thread_root')
    title = models.CharField(max_length=255, blank=True, null=True)
    last_reply_at = models.DateTimeField(auto_now=True)
    reply_count = models.IntegerField(default=0)
    participants = models.ManyToManyField('User', related_name='message_threads')

    class Meta:
        indexes = [
            models.Index(fields=['-last_reply_at'], name='thread_last_reply_idx'),
            models.Index(fields=['root_message'], name='thread_root_msg_idx'),
        ]
        ordering = ['-last_reply_at']

    def __str__(self):
        if self.title:
            return self.title
        return f"Thread: {self.root_message.content[:50]}..."

    def add_reply(self, message):
        """Add a reply to this thread"""
        message.reply_to = self.root_message
        message.save()
        self.reply_count = self.root_message.replies.count()
        self.save(update_fields=['reply_count'])

    def add_participant(self, user):
        """Add a user to thread participants"""
        self.participants.add(user)


class MessageMention(models.Model):
    """Track @mentions in messages"""
    message = models.ForeignKey('ChatMessage', on_delete=models.CASCADE, related_name='mentions')
    mentioned_user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='message_mentions')
    mention_text = models.CharField(max_length=255)  # The actual @username text
    start_position = models.IntegerField()  # Position in message content
    end_position = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['message', 'mentioned_user', 'start_position']
        indexes = [
            models.Index(fields=['mentioned_user', '-created_at'], name='mention_user_idx'),
            models.Index(fields=['message'], name='mention_message_idx'),
        ]

    def __str__(self):
        return f"@{self.mentioned_user.username} in message {self.message.id}"


class MessageReaction(models.Model):
    """Emoji reactions to messages"""
    message = models.ForeignKey('ChatMessage', on_delete=models.CASCADE, related_name='reactions')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='message_reactions')
    emoji = models.CharField(max_length=100)  # Emoji name (e.g., 'thumbs_up')
    emoji_unicode = models.CharField(max_length=10)  # Unicode representation (e.g., '👍')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['message', 'user', 'emoji']
        indexes = [
            models.Index(fields=['message', 'emoji'], name='reaction_message_emoji_idx'),
            models.Index(fields=['user', '-created_at'], name='reaction_user_idx'),
        ]

    def __str__(self):
        return f"{self.user.username} reacted {self.emoji} to message {self.message.id}"


class InternalEmail(models.Model):
    """Internal email addressing system for project-specific and user-specific communications"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    address = models.CharField(max_length=255, unique=True, help_text="e.g., project-123@clear.<NAME_EMAIL>")

    # Entity linking
    entity_type = models.CharField(max_length=50, choices=[
        ('project', 'Project'),
        ('user', 'User'),
        ('task', 'Task'),
        ('utility', 'Utility'),
        ('conflict', 'Conflict'),
        ('stakeholder', 'Stakeholder'),
    ])
    entity_id = models.CharField(max_length=255)

    # Configuration
    is_active = models.BooleanField(default=True)
    forward_to_email = models.EmailField(blank=True, null=True, help_text='Forward emails to external address')
    auto_response = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['entity_type', 'entity_id']),
            models.Index(fields=['address']),
        ]

    def __str__(self):
        return self.address


class InternalEmailMessage(models.Model):
    """Messages sent to internal email addresses"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    internal_email = models.ForeignKey('InternalEmail', on_delete=models.CASCADE, related_name='messages')

    # Email details
    from_address = models.EmailField()
    from_name = models.CharField(max_length=255, blank=True, null=True)
    subject = models.CharField(max_length=500)
    body_text = models.TextField()
    body_html = models.TextField(blank=True, null=True)

    # Processing
    processed_at = models.DateTimeField(blank=True, null=True)
    processing_status = models.CharField(max_length=50, default='pending', choices=[
        ('pending', 'Pending'),
        ('processed', 'Processed'),
        ('failed', 'Failed'),
        ('ignored', 'Ignored'),
    ])
    processing_notes = models.TextField(blank=True, null=True)

    # Created entities
    created_chat_message = models.ForeignKey('ChatMessage', on_delete=models.SET_NULL, blank=True, null=True)
    created_comment = models.ForeignKey('Comment', on_delete=models.SET_NULL, blank=True, null=True)

    received_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['internal_email', '-received_at']),
            models.Index(fields=['processing_status', '-received_at']),
        ]
        ordering = ['-received_at']

    def __str__(self):
        return f"Email to {self.internal_email.address}: {self.subject}"
