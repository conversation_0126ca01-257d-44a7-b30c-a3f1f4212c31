"""
Analytics and Reporting Models
Handles system metrics, business analytics, and reporting
"""

import uuid
from django.db import models




# from django.contrib.postgres.fields import models.JSONField  # Commented out for SQLite compatibility


class SystemMetric(models.Model):
    """System performance and usage metrics with enhanced analytics capabilities"""
    metric_name = models.CharField(max_length=100)
    metric_value = models.FloatField()
    metric_unit = models.CharField(max_length=50, blank=True, null=True)
    category = models.CharField(max_length=50)
    tags = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Enhanced analytics fields
    aggregation_period = models.CharField(max_length=20, default='hour', choices=[
        ('minute', 'Minute'),
        ('hour', 'Hour'), 
        ('day', 'Day'),
        ('week', 'Week'),
        ('month', 'Month'),
    ])
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='metrics')
    user = models.ForeignKey('User', on_delete=models.CASCADE, blank=True, null=True, related_name='metrics')
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, blank=True, null=True, related_name='metrics')
    metadata = models.JSONField(default=dict, help_text='Additional metric metadata for analytics')
    
    class Meta:
        indexes = [
            models.Index(fields=['metric_name', '-timestamp']),
            models.Index(fields=['category', '-timestamp']),
            models.Index(fields=['aggregation_period', '-timestamp']),
            models.Index(fields=['project', 'metric_name', '-timestamp']),
            models.Index(fields=['organization', 'category', '-timestamp']),
        ]
    
    def __str__(self):
        return f"{self.metric_name}: {self.metric_value} {self.metric_unit or ''}"


class BusinessMetric(models.Model):
    """Business-specific metrics and KPIs"""
    name = models.CharField(max_length=100)
    value = models.FloatField()
    target_value = models.FloatField(blank=True, null=True)
    unit = models.CharField(max_length=50, blank=True, null=True)
    category = models.CharField(max_length=50, choices=[
        ('financial', 'Financial'),
        ('productivity', 'Productivity'),
        ('quality', 'Quality'),
        ('customer', 'Customer'),
        ('team', 'Team'),
        ('project', 'Project'),
    ])
    
    # Context
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='business_metrics')
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='business_metrics')
    
    # Temporal data
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    calculated_at = models.DateTimeField(auto_now_add=True)
    
    # Metadata
    calculation_method = models.CharField(max_length=100, blank=True, null=True)
    metadata = models.JSONField(default=dict)
    
    class Meta:
        indexes = [
            models.Index(fields=['category', '-calculated_at']),
            models.Index(fields=['organization', 'category', '-calculated_at']),
            models.Index(fields=['project', '-calculated_at']),
        ]
        unique_together = ['name', 'project', 'organization', 'period_start', 'period_end']
    
    def __str__(self):
        return f"{self.name}: {self.value} {self.unit or ''}"
    
    def is_on_target(self):
        """Check if metric meets target"""
        if self.target_value is None:
            return None
        return self.value >= self.target_value
    
    def variance_from_target(self):
        """Calculate percentage variance from target"""
        if self.target_value is None or self.target_value == 0:
            return None
        return ((self.value - self.target_value) / self.target_value) * 100


class AnalyticsReport(models.Model):
    """Pre-defined analytics reports"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    report_type = models.CharField(max_length=50, choices=[
        ('dashboard', 'Dashboard'),
        ('summary', 'Summary Report'),
        ('detailed', 'Detailed Report'),
        ('export', 'Export Report'),
        ('scheduled', 'Scheduled Report'),
    ])
    
    # Report configuration
    configuration = models.JSONField(default=dict)
    filters = models.JSONField(default=dict)
    chart_config = models.JSONField(default=dict)
    
    # Access control
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='analytics_reports')
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_reports')
    is_public = models.BooleanField(default=False)
    allowed_users = models.ManyToManyField('User', blank=True, related_name='accessible_reports')
    
    # Scheduling
    is_scheduled = models.BooleanField(default=False)
    schedule_config = models.JSONField(default=dict)
    last_executed_at = models.DateTimeField(blank=True, null=True)
    next_execution_at = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['organization', '-created_at']),
            models.Index(fields=['report_type', '-created_at']),
            models.Index(fields=['is_scheduled', 'next_execution_at']),
        ]
    
    def __str__(self):
        return self.name


class ReportExecution(models.Model):
    """Track report execution history"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report = models.ForeignKey(AnalyticsReport, on_delete=models.CASCADE, related_name='executions')
    executed_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='report_executions')
    
    # Execution details
    status = models.CharField(max_length=20, default='running', choices=[
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ])
    
    # Results
    result_data = models.JSONField(default=dict)
    result_file_path = models.CharField(max_length=500, blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    
    # Performance metrics
    execution_time_ms = models.IntegerField(blank=True, null=True)
    data_points_processed = models.IntegerField(blank=True, null=True)
    
    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['report', '-started_at']),
            models.Index(fields=['status', '-started_at']),
            models.Index(fields=['executed_by', '-started_at']),
        ]
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.report.name} execution at {self.started_at}"


class AnalyticsEvent(models.Model):
    """Track user actions and events for analytics"""
    event_name = models.CharField(max_length=100)
    event_category = models.CharField(max_length=50)
    user = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='analytics_events')
    session_id = models.CharField(max_length=255, blank=True, null=True)
    
    # Event context
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='analytics_events')
    page_url = models.URLField(blank=True, null=True)
    referrer_url = models.URLField(blank=True, null=True)
    
    # Event data
    properties = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Technical details
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    device_type = models.CharField(max_length=20, blank=True, null=True)
    browser = models.CharField(max_length=50, blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['event_name', '-timestamp']),
            models.Index(fields=['event_category', '-timestamp']),
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['project', '-timestamp']),
            models.Index(fields=['session_id', '-timestamp']),
        ]
    
    def __str__(self):
        return f"{self.event_category}.{self.event_name}"


class Report(models.Model):
    """Legacy report model for backward compatibility"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    report_data = models.JSONField(default=dict)
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='legacy_reports')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
