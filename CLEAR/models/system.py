"""
System Configuration and Settings Models
Handles system settings, notifications, activities, and administrative functions
"""

import uuid
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.db import models
from django.utils import timezone
from datetime import datetime
import pytz




# from django.contrib.postgres.fields import models.J<PERSON><PERSON><PERSON>  # Commented out for SQLite compatibility


class CollaborationSettings(models.Model):
    """User-specific collaboration preferences"""
    user = models.OneToOneField('User', on_delete=models.CASCADE, related_name='collaboration_settings')
    
    # Notification preferences
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    desktop_notifications = models.BooleanField(default=True)
    
    # Collaboration preferences
    auto_join_project_rooms = models.BooleanField(default=True)
    show_typing_indicators = models.BooleanField(default=True)
    show_read_receipts = models.BooleanField(default=True)
    allow_direct_messages = models.BooleanField(default=True)
    
    # Meeting preferences
    default_meeting_duration = models.IntegerField(default=30)  # minutes
    auto_record_meetings = models.BooleanField(default=False)
    meeting_reminder_minutes = models.IntegerField(default=5)
    
    # Workspace preferences
    default_workspace_layout = models.JSONField(default=dict)
    enable_keyboard_shortcuts = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Collaboration settings for {self.user.username}"


class Activity(models.Model):
    """System-wide activity tracking"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='activities')
    action = models.CharField(max_length=100)
    description = models.TextField(default='Activity recorded')
    
    # Context linking
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, blank=True, null=True)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Additional context
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='activities')
    metadata = models.JSONField(default=dict)
    
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['project', '-timestamp']),
            models.Index(fields=['action', '-timestamp']),
            models.Index(fields=['content_type', 'object_id']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user.username}: {self.action}"


class Notification(models.Model):
    """Enhanced notification system"""
    NOTIFICATION_TYPES = [
        ('message', 'New Message'),
        ('mention', 'Mentioned in Message'),
        ('comment', 'New Comment'),
        ('task_assigned', 'Task Assigned'),
        ('task_due', 'Task Due'),
        ('document_shared', 'Document Shared'),
        ('project_updated', 'Project Updated'),
        ('system', 'System Notification'),
        ('reminder', 'Reminder'),
        ('approval_needed', 'Approval Needed'),
        ('conflict_detected', 'Conflict Detected'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey('User', on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='sent_notifications')
    
    # Notification content
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=255)
    message = models.TextField()
    action_url = models.URLField(blank=True, null=True)
    action_label = models.CharField(max_length=100, blank=True, null=True)
    
    # Priority and categorization
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal')
    category = models.CharField(max_length=50, blank=True, null=True)
    tags = models.JSONField(default=list, blank=True)
    
    # Status tracking
    is_read = models.BooleanField(default=False)
    is_delivered = models.BooleanField(default=False)
    read_at = models.DateTimeField(blank=True, null=True)
    delivered_at = models.DateTimeField(blank=True, null=True)
    
    # Context linking
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, blank=True, null=True)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Scheduling
    scheduled_for = models.DateTimeField(blank=True, null=True)
    expires_at = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    metadata = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['recipient', '-created_at'], name='notif_recipient_created_idx'),
            models.Index(fields=['notification_type', '-created_at'], name='notif_type_created_idx'),
            models.Index(fields=['is_read', 'recipient'], name='notif_read_recipient_idx'),
            models.Index(fields=['priority', '-created_at'], name='notif_priority_created_idx'),
            models.Index(fields=['scheduled_for'], name='notif_scheduled_idx'),
            models.Index(fields=['expires_at'], name='notif_expires_idx'),
            models.Index(fields=['content_type', 'object_id'], name='notif_content_object_idx'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} for {self.recipient.username}"
    
    def mark_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def mark_delivered(self):
        """Mark notification as delivered"""
        if not self.is_delivered:
            self.is_delivered = True
            self.delivered_at = timezone.now()
            self.save(update_fields=['is_delivered', 'delivered_at'])
    
    def is_expired(self):
        """Check if notification has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class NotificationSettings(models.Model):
    """User notification preferences"""
    user = models.OneToOneField('User', on_delete=models.CASCADE, related_name='notification_settings')
    
    # Email notifications
    email_enabled = models.BooleanField(default=True)
    email_frequency = models.CharField(max_length=20, default='immediate', choices=[
        ('immediate', 'Immediate'),
        ('daily', 'Daily Digest'),
        ('weekly', 'Weekly Digest'),
        ('never', 'Never'),
    ])
    
    # Push notifications
    push_enabled = models.BooleanField(default=True)
    
    # In-app notifications
    desktop_enabled = models.BooleanField(default=True)
    sound_enabled = models.BooleanField(default=True)
    
    # Notification type preferences
    notification_preferences = models.JSONField(default=dict, help_text='Per-type notification settings')
    
    # Quiet hours
    quiet_hours_enabled = models.BooleanField(default=False)
    quiet_start_time = models.TimeField(blank=True, null=True)
    quiet_end_time = models.TimeField(blank=True, null=True)
    quiet_timezone = models.CharField(max_length=50, default='UTC')
    
    # Project-specific settings
    project_overrides = models.JSONField(default=dict, help_text='Project-specific notification settings')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Notification settings for {self.user.username}"
    
    def is_in_quiet_hours(self):
        """Check if current time is within quiet hours"""
        if not self.quiet_hours_enabled or not self.quiet_start_time or not self.quiet_end_time:
            return False
        

        
        try:
            tz = pytz.timezone(self.quiet_timezone)
        except pytz.UnknownTimeZoneError:
            tz = pytz.UTC
        
        now = datetime.now(tz).time()
        
        if self.quiet_start_time <= self.quiet_end_time:
            return self.quiet_start_time <= now <= self.quiet_end_time
        else:  # Quiet hours span midnight
            return now >= self.quiet_start_time or now <= self.quiet_end_time


class NotificationBatch(models.Model):
    """Batch notifications for efficient delivery"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey('User', on_delete=models.CASCADE, related_name='notification_batches')
    batch_type = models.CharField(max_length=20, choices=[
        ('email_digest', 'Email Digest'),
        ('push_bulk', 'Push Bulk'),
        ('scheduled', 'Scheduled Batch'),
    ])
    
    # Batch content
    notifications = models.ManyToManyField(Notification, related_name='batches')
    subject = models.CharField(max_length=255, blank=True, null=True)
    content = models.TextField(blank=True, null=True)
    
    # Delivery status
    status = models.CharField(max_length=20, default='pending', choices=[
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    ])
    sent_at = models.DateTimeField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    
    # Metadata
    metadata = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['recipient', '-created_at'], name='notif_batch_recipient_idx'),
            models.Index(fields=['batch_type', 'status'], name='notif_batch_type_status_idx'),
            models.Index(fields=['status', 'created_at'], name='notif_batch_status_created_idx'),
        ]
    
    def __str__(self):
        return f"{self.batch_type} batch for {self.recipient.username}"


class NotificationDelivery(models.Model):
    """Track notification delivery attempts"""
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='delivery_attempts')
    delivery_method = models.CharField(max_length=20, choices=[
        ('email', 'Email'),
        ('push', 'Push Notification'),
        ('sms', 'SMS'),
        ('webhook', 'Webhook'),
    ])
    
    # Delivery status
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('bounced', 'Bounced'),
    ])
    
    # Delivery details
    external_id = models.CharField(max_length=255, blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    response_data = models.JSONField(default=dict)
    
    # Timing
    sent_at = models.DateTimeField(blank=True, null=True)
    delivered_at = models.DateTimeField(blank=True, null=True)
    failed_at = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['notification', 'delivery_method'], name='notif_del_notif_method_idx'),
            models.Index(fields=['status', '-created_at'], name='notif_del_status_created_idx'),
            models.Index(fields=['external_id'], name='notif_del_external_idx'),
        ]
    
    def __str__(self):
        return f"{self.delivery_method} delivery for notification {self.notification.id}"


class AppVersion(models.Model):
    """Application version tracking"""
    version = models.CharField(max_length=20, unique=True)
    release_date = models.DateTimeField()
    is_current = models.BooleanField(default=False)
    release_notes = models.TextField(blank=True, null=True)
    minimum_version = models.CharField(max_length=20, blank=True, null=True)
    force_update = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Version {self.version}"


class UserVersionAcknowledgment(models.Model):
    """Track which users have acknowledged version updates"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='version_acknowledgments')
    version = models.ForeignKey(AppVersion, on_delete=models.CASCADE, related_name='user_acknowledgments')
    acknowledged_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'version']
    
    def __str__(self):
        return f"{self.user.username} acknowledged {self.version.version}"


class DatabaseBackup(models.Model):
    """Database backup tracking"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    backup_type = models.CharField(max_length=20, choices=[
        ('full', 'Full Backup'),
        ('incremental', 'Incremental'),
        ('differential', 'Differential'),
    ])
    
    # Backup details
    file_path = models.CharField(max_length=500)
    file_size = models.BigIntegerField()
    checksum = models.CharField(max_length=64)
    
    # Status
    status = models.CharField(max_length=20, choices=[
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ])
    
    # Timing
    started_at = models.DateTimeField()
    completed_at = models.DateTimeField(blank=True, null=True)
    duration_seconds = models.IntegerField(blank=True, null=True)
    
    # Metadata
    initiated_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='initiated_backups')
    error_message = models.TextField(blank=True, null=True)
    metadata = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['-started_at'], name='backup_started_idx'),
            models.Index(fields=['status'], name='backup_status_idx'),
        ]
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.backup_type} backup at {self.started_at}"


class PendingChange(models.Model):
    """Track pending system changes for approval"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    change_type = models.CharField(max_length=50)
    description = models.TextField()
    
    # Change details
    change_data = models.JSONField(default=dict)
    affected_models = models.JSONField(default=list)
    
    # Approval workflow
    requested_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='requested_changes')
    approved_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_changes')
    
    # Status
    status = models.CharField(max_length=20, default='pending', choices=[
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('applied', 'Applied'),
        ('failed', 'Failed'),
    ])
    
    # Timing
    requested_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(blank=True, null=True)
    applied_at = models.DateTimeField(blank=True, null=True)
    
    # Results
    application_result = models.JSONField(default=dict)
    error_message = models.TextField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['status', '-requested_at'], name='pending_change_status_idx'),
            models.Index(fields=['requested_by', '-requested_at'], name='pending_change_requester_idx'),
        ]
        ordering = ['-requested_at']
    
    def __str__(self):
        return f"{self.change_type}: {self.description[:50]}..."


class AdminLog(models.Model):
    """Administrative action logging"""
    admin_user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='admin_actions', null=True, blank=True)
    action = models.CharField(max_length=100, default='action')
    description = models.TextField(default='Administrative action performed')
    
    # Target information
    target_model = models.CharField(max_length=100, blank=True, null=True)
    target_object_id = models.CharField(max_length=255, blank=True, null=True)
    target_object_repr = models.CharField(max_length=500, blank=True, null=True)
    
    # Change details
    changes = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['admin_user', '-timestamp'], name='admin_log_user_idx'),
            models.Index(fields=['action', '-timestamp'], name='admin_log_action_idx'),
            models.Index(fields=['target_model', '-timestamp'], name='admin_log_target_idx'),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.admin_user.username}: {self.action}"
