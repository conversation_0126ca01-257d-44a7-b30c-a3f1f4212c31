"""
Knowledge Management Models
Handles knowledge articles, categories, and knowledge base features
"""

import uuid
from django.contrib.gis.db import models


# from django.contrib.postgres.fields import models.J<PERSON><PERSON>ield  # Commented out for SQLite compatibility



class KnowledgeCategory(models.Model):
    """Categories for organizing knowledge articles"""
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='subcategories')
    
    # Organization and ordering
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='knowledge_categories')
    order = models.IntegerField(default=0)
    slug = models.SlugField(max_length=255)
    
    # Visual styling
    icon = models.CharField(max_length=50, blank=True, null=True)
    color = models.Char<PERSON>ield(max_length=7, blank=True, null=True)
    
    # Access control
    is_public = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['organization', 'slug']
        indexes = [
            models.Index(fields=['organization', 'parent', 'order']),
            models.Index(fields=['slug']),
        ]
        ordering = ['order', 'name']
    
    def __str__(self):
        return self.name
    
    def get_full_path(self):
        """Get the full category path"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name
    
    def get_article_count(self):
        """Get total number of articles in this category and subcategories"""
        # Get this category and all descendants
        descendant_ids = self._get_descendant_ids()
        return KnowledgeArticle.objects.filter(category_id__in=descendant_ids).count()
    
    def _get_descendant_ids(self):
        """Get IDs of this category and all descendants"""
        ids = [self.id]
        for child in self.subcategories.all():
            ids.extend(child._get_descendant_ids())
        return ids


class KnowledgeArticle(models.Model):
    """Knowledge base articles"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    content = models.TextField()
    summary = models.TextField(blank=True, null=True)
    
    # Organization
    category = models.ForeignKey(KnowledgeCategory, on_delete=models.CASCADE, related_name='articles')
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='knowledge_articles')
    
    # Authoring
    author = models.ForeignKey('User', on_delete=models.CASCADE, related_name='authored_articles')
    last_modified_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='modified_articles')
    
    # Status and publication
    status = models.CharField(max_length=20, default='draft', choices=[
        ('draft', 'Draft'),
        ('review', 'Under Review'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ])
    is_published = models.BooleanField(default=False)
    published_at = models.DateTimeField(blank=True, null=True)
    
    # Content metadata
    tags = models.JSONField(default=list, blank=True)
    reading_time_minutes = models.IntegerField(blank=True, null=True)
    difficulty_level = models.CharField(max_length=20, default='beginner', choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ])
    
    # Access control
    is_public = models.BooleanField(default=True)
    allowed_roles = models.JSONField(default=list, blank=True)
    
    # Engagement metrics
    view_count = models.IntegerField(default=0)
    helpful_votes = models.IntegerField(default=0)
    not_helpful_votes = models.IntegerField(default=0)
    
    # SEO and search
    slug = models.SlugField(max_length=255)
    meta_description = models.CharField(max_length=160, blank=True, null=True)
    featured_image = models.URLField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['organization', 'slug']
        indexes = [
            models.Index(fields=['category', 'status', '-updated_at']),
            models.Index(fields=['organization', 'status', '-updated_at']),
            models.Index(fields=['author', '-created_at']),
            models.Index(fields=['slug']),
            models.Index(fields=['-view_count']),
            models.Index(fields=['status', '-published_at']),
        ]
        ordering = ['-updated_at']
    
    def __str__(self):
        return self.title
    
    def get_helpfulness_ratio(self):
        """Calculate helpfulness ratio (0-1)"""
        total_votes = self.helpful_votes + self.not_helpful_votes
        if total_votes == 0:
            return None
        return self.helpful_votes / total_votes
    
    def get_reading_time(self):
        """Estimate reading time based on content length"""
        if self.reading_time_minutes:
            return self.reading_time_minutes
        
        # Rough estimate: 200 words per minute
        word_count = len(self.content.split())
        return max(1, round(word_count / 200))
    
    def is_article_published(self):
        """Check if article is published"""
        return self.status == 'published' and self.published_at is not None


# Legacy models for backward compatibility
class Article(models.Model):
    """Legacy article model"""
    title = models.CharField(max_length=255)
    content = models.TextField()
    category = models.ForeignKey('ArticleCategory', on_delete=models.CASCADE, related_name='articles')
    author = models.ForeignKey('User', on_delete=models.CASCADE, related_name='legacy_articles')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title


class ArticleCategory(models.Model):
    """Legacy article category model"""
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name


class ArticleView(models.Model):
    """Track article views"""
    article = models.ForeignKey(KnowledgeArticle, on_delete=models.CASCADE, related_name='views')
    user = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='article_views')
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True, null=True)
    referrer = models.URLField(blank=True, null=True)
    viewed_at = models.DateTimeField(auto_now_add=True)
    
    # Reading behavior
    time_spent_seconds = models.IntegerField(blank=True, null=True)
    scroll_percentage = models.IntegerField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['article', '-viewed_at']),
            models.Index(fields=['user', '-viewed_at']),
            models.Index(fields=['ip_address', '-viewed_at']),
        ]
    
    def __str__(self):
        return f"View of {self.article.title}"


class ArticleVote(models.Model):
    """Track helpful/not helpful votes on articles"""
    article = models.ForeignKey(KnowledgeArticle, on_delete=models.CASCADE, related_name='votes')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='article_votes')
    is_helpful = models.BooleanField()
    feedback = models.TextField(blank=True, null=True)
    voted_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['article', 'user']
        indexes = [
            models.Index(fields=['article', 'is_helpful']),
            models.Index(fields=['user', '-voted_at']),
        ]
    
    def __str__(self):
        vote_type = "helpful" if self.is_helpful else "not helpful"
        return f"{self.user.username} voted {vote_type} on {self.article.title}"


class ArticleRevision(models.Model):
    """Track article revision history"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    article = models.ForeignKey(KnowledgeArticle, on_delete=models.CASCADE, related_name='revisions')
    revision_number = models.IntegerField()
    
    # Snapshot of content at revision
    title = models.CharField(max_length=255)
    content = models.TextField()
    summary = models.TextField(blank=True, null=True)
    
    # Change metadata
    changed_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='article_revisions')
    change_summary = models.TextField(blank=True, null=True)
    change_type = models.CharField(max_length=20, choices=[
        ('created', 'Created'),
        ('content', 'Content Update'),
        ('metadata', 'Metadata Update'),
        ('status', 'Status Change'),
        ('major', 'Major Revision'),
    ])
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['article', 'revision_number']
        indexes = [
            models.Index(fields=['article', '-revision_number']),
            models.Index(fields=['changed_by', '-created_at']),
        ]
        ordering = ['-revision_number']
    
    def __str__(self):
        return f"{self.article.title} revision {self.revision_number}"


class ArticleAttachment(models.Model):
    """File attachments for knowledge articles"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    article = models.ForeignKey(KnowledgeArticle, on_delete=models.CASCADE, related_name='attachments')
    
    # File details
    name = models.CharField(max_length=255)
    file_path = models.CharField(max_length=500)
    file_size = models.BigIntegerField()
    mime_type = models.CharField(max_length=100)
    
    # Metadata
    description = models.TextField(blank=True, null=True)
    uploaded_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='article_attachments')
    
    # Organization
    order = models.IntegerField(default=0)
    is_featured = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['article', 'order']),
            models.Index(fields=['uploaded_by', '-created_at']),
        ]
        ordering = ['order', 'name']
    
    def __str__(self):
        return f"{self.name} for {self.article.title}"
    
    def get_file_extension(self):
        """Get file extension"""
        return self.name.split('.')[-1].lower() if '.' in self.name else ''
    
    def is_image(self):
        """Check if attachment is an image"""
        image_types = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
        return self.get_file_extension() in image_types
