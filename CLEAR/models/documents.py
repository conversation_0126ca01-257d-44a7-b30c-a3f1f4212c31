"""
Document Management Models
Handles documents, versions, sharing, reviews, and collaborative editing
"""

import uuid
from django.contrib.gis.db import models
from django.utils import timezone
from datetime import timedelta




# from django.contrib.postgres.fields import models.JSONField  # Commented out for SQLite compatibility


class Document(models.Model):
    """Document management with enhanced features"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    file_path = models.CharField(max_length=500)
    file_size = models.BigIntegerField(default=0)
    file_type = models.CharField(max_length=100)
    mime_type = models.CharField(max_length=200)
    
    # Organization and permissions
    project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True, related_name='documents')
    task = models.ForeignKey('Task', on_delete=models.CASCADE, blank=True, null=True, related_name='documents')
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='documents')
    uploaded_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='uploaded_documents')
    
    # Enhanced metadata
    tags = models.JSONField(default=list)
    metadata = models.JSONField(default=dict)
    folder_path = models.CharField(max_length=500, blank=True, null=True)
    is_archived = models.BooleanField(default=False)
    is_public = models.BooleanField(default=False)
    
    # Version control
    current_version = models.IntegerField(default=1)
    is_locked = models.BooleanField(default=False)
    locked_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='locked_documents')
    locked_at = models.DateTimeField(blank=True, null=True)
    
    # Enhanced timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_accessed_at = models.DateTimeField(blank=True, null=True)
    
    # Search and indexing
    search_vector = models.TextField(blank=True, null=True)  # For full-text search
    checksum = models.CharField(max_length=64, blank=True, null=True)  # File integrity
    
    class Meta:
        indexes = [
            models.Index(fields=['project', '-created_at'], name='doc_project_created_idx'),
            models.Index(fields=['task', '-created_at'], name='doc_task_created_idx'),
            models.Index(fields=['uploaded_by', '-created_at'], name='doc_uploader_created_idx'),
            models.Index(fields=['file_type', '-created_at'], name='doc_type_created_idx'),
            models.Index(fields=['organization', '-created_at'], name='doc_org_created_idx'),
            models.Index(fields=['is_archived', '-created_at'], name='doc_archived_created_idx'),
            models.Index(fields=['folder_path'], name='doc_folder_idx'),
            models.Index(fields=['checksum'], name='doc_checksum_idx'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    def get_latest_version(self):
        """Get the latest version of this document"""
        return self.versions.order_by('-version_number').first()
    
    def get_file_extension(self):
        """Get file extension"""
        return self.name.split('.')[-1].lower() if '.' in self.name else ''
    
    def is_image(self):
        """Check if document is an image"""
        image_types = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
        return self.get_file_extension() in image_types
    
    def is_pdf(self):
        """Check if document is a PDF"""
        return self.get_file_extension() == 'pdf'
    
    def can_preview(self):
        """Check if document can be previewed"""
        previewable_types = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'md']
        return self.get_file_extension() in previewable_types
    
    def get_size_display(self):
        """Get human-readable file size"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"


class DocumentVersion(models.Model):
    """Document version control"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='versions')
    version_number = models.IntegerField()
    file_path = models.CharField(max_length=500)
    file_size = models.BigIntegerField()
    checksum = models.CharField(max_length=64)
    
    # Version metadata
    change_summary = models.TextField(blank=True, null=True)
    uploaded_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='document_versions')
    upload_reason = models.CharField(max_length=500, blank=True, null=True)
    
    # Branching support
    parent_version = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='child_versions')
    branch_name = models.CharField(max_length=100, blank=True, null=True)
    is_merged = models.BooleanField(default=False)
    merged_at = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['document', 'version_number']
        indexes = [
            models.Index(fields=['document', '-version_number'], name='doc_ver_number_idx'),
            models.Index(fields=['uploaded_by', '-created_at'], name='doc_ver_uploader_idx'),
            models.Index(fields=['branch_name'], name='doc_ver_branch_idx'),
        ]
        ordering = ['-version_number']
    
    def __str__(self):
        return f"{self.document.name} v{self.version_number}"


class DocumentShare(models.Model):
    """Document sharing permissions"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='shares')
    shared_with = models.ForeignKey('User', on_delete=models.CASCADE, related_name='shared_documents')
    shared_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='document_shares')
    permission_level = models.CharField(max_length=20, default='view', choices=[
        ('view', 'View Only'),
        ('comment', 'View & Comment'),
        ('edit', 'Edit'),
        ('admin', 'Admin'),
    ])
    expires_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['document', 'shared_with']
    
    def __str__(self):
        return f"{self.document.name} shared with {self.shared_with.username}"


class DocumentActivity(models.Model):
    """Track document-related activities"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='activities')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='document_activities')
    action = models.CharField(max_length=50, choices=[
        ('uploaded', 'Uploaded'),
        ('downloaded', 'Downloaded'),
        ('viewed', 'Viewed'),
        ('shared', 'Shared'),
        ('commented', 'Commented'),
        ('version_created', 'New Version'),
        ('locked', 'Locked'),
        ('unlocked', 'Unlocked'),
        ('archived', 'Archived'),
        ('restored', 'Restored'),
        ('deleted', 'Deleted'),
    ])
    details = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['document', '-timestamp'], name='doc_activity_doc_idx'),
            models.Index(fields=['user', '-timestamp'], name='doc_activity_user_idx'),
            models.Index(fields=['action', '-timestamp'], name='doc_activity_action_idx'),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user.username} {self.action} {self.document.name}"


class DocumentDiscussion(models.Model):
    """Document discussion threads"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='discussions')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_discussions')
    
    # Discussion state
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(blank=True, null=True)
    resolved_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='resolved_discussions')
    
    # Location in document (for PDFs, images, etc.)
    page_number = models.IntegerField(blank=True, null=True)
    coordinates = models.JSONField(default=dict)  # x, y, width, height for annotations
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['document', '-created_at'], name='doc_discussion_doc_idx'),
            models.Index(fields=['created_by', '-created_at'], name='doc_discussion_creator_idx'),
            models.Index(fields=['is_resolved'], name='doc_discussion_resolved_idx'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Discussion: {self.title}"


class DocumentDiscussionParticipant(models.Model):
    """Discussion participants"""
    discussion = models.ForeignKey(DocumentDiscussion, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='discussion_participations')
    joined_at = models.DateTimeField(auto_now_add=True)
    last_read_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        unique_together = ['discussion', 'user']
    
    def __str__(self):
        return f"{self.user.username} in {self.discussion.title}"


class DocumentDiscussionMessage(models.Model):
    """Messages in document discussions"""
    discussion = models.ForeignKey(DocumentDiscussion, on_delete=models.CASCADE, related_name='messages')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='discussion_messages')
    content = models.TextField()
    reply_to = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='replies')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['discussion', '-created_at'], name='doc_msg_discussion_idx'),
            models.Index(fields=['user', '-created_at'], name='doc_msg_user_idx'),
        ]
        ordering = ['created_at']
    
    def __str__(self):
        return f"Message by {self.user.username} in {self.discussion.title}"


class DocumentReviewProcess(models.Model):
    """Document review and approval workflows"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='review_processes')
    document_version = models.ForeignKey(DocumentVersion, on_delete=models.CASCADE, related_name='review_processes')
    
    # Review details
    review_type = models.CharField(max_length=50, choices=[
        ('approval', 'Approval Required'),
        ('feedback', 'Feedback Only'),
        ('compliance', 'Compliance Check'),
    ])
    deadline = models.DateTimeField(blank=True, null=True)
    
    # Status tracking
    status = models.CharField(max_length=20, default='pending', choices=[
        ('pending', 'Pending'),
        ('in_review', 'In Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
    ])
    
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_reviews')
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['document', '-created_at'], name='doc_review_doc_idx'),
            models.Index(fields=['status'], name='doc_review_status_idx'),
            models.Index(fields=['deadline'], name='doc_review_deadline_idx'),
        ]
    
    def __str__(self):
        return f"Review of {self.document.name} v{self.document_version.version_number}"


class DocumentReviewer(models.Model):
    """Individual reviewers for document review processes"""
    review_process = models.ForeignKey(DocumentReviewProcess, on_delete=models.CASCADE, related_name='reviewers')
    reviewer = models.ForeignKey('User', on_delete=models.CASCADE, related_name='document_reviews')
    review_status = models.CharField(max_length=20, default='pending', choices=[
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('abstained', 'Abstained'),
    ])
    comments = models.TextField(blank=True, null=True)
    reviewed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        unique_together = ['review_process', 'reviewer']
    
    def __str__(self):
        return f"{self.reviewer.username} review of {self.review_process.document.name}"


class DocumentVersionBranch(models.Model):
    """Document version branching for parallel development"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='branches')
    branch_name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    
    # Branching info
    branched_from_version = models.ForeignKey(DocumentVersion, on_delete=models.CASCADE, related_name='branches')
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_branches')
    
    # Merge info
    is_merged = models.BooleanField(default=False)
    merged_into_version = models.ForeignKey(DocumentVersion, on_delete=models.SET_NULL, blank=True, null=True, related_name='merged_branches')
    merged_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='merged_branches')
    merged_at = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['document', 'branch_name']
        indexes = [
            models.Index(fields=['document', 'is_merged'], name='doc_branch_doc_merged_idx'),
        ]
    
    def __str__(self):
        return f"{self.document.name} - {self.branch_name}"


class DocumentVersionDiff(models.Model):
    """Track differences between document versions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    from_version = models.ForeignKey(DocumentVersion, on_delete=models.CASCADE, related_name='diffs_from')
    to_version = models.ForeignKey(DocumentVersion, on_delete=models.CASCADE, related_name='diffs_to')
    
    # Diff data
    diff_data = models.JSONField(default=dict)  # Structured diff information
    diff_summary = models.TextField(blank=True, null=True)
    changes_count = models.IntegerField(default=0)
    
    # Auto-computed
    computed_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['from_version', 'to_version']
    
    def __str__(self):
        return f"Diff: v{self.from_version.version_number} → v{self.to_version.version_number}"


class DocumentLock(models.Model):
    """Document locking for editing"""
    document = models.OneToOneField(Document, on_delete=models.CASCADE, related_name='lock')
    locked_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='document_locks')
    locked_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    
    # Lock metadata
    lock_reason = models.CharField(max_length=500, blank=True, null=True)
    session_id = models.CharField(max_length=255, blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['locked_by'], name='doc_lock_user_idx'),
            models.Index(fields=['expires_at'], name='doc_lock_expires_idx'),
        ]
    
    def __str__(self):
        return f"{self.document.name} locked by {self.locked_by.username}"
    
    def is_expired(self):
        """Check if lock has expired"""
        return timezone.now() > self.expires_at
    
    def extend_lock(self, hours=2):
        """Extend lock expiration"""
        self.expires_at = timezone.now() + timedelta(hours=hours)
        self.save(update_fields=['expires_at'])


class DocumentEditingSession(models.Model):
    """Track active editing sessions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='editing_sessions')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='editing_sessions')
    
    # Session info
    session_id = models.CharField(max_length=255)
    started_at = models.DateTimeField(auto_now_add=True)
    last_activity_at = models.DateTimeField(auto_now=True)
    ended_at = models.DateTimeField(blank=True, null=True)
    
    # Session data
    cursor_position = models.JSONField(default=dict)
    selection_range = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['document', 'is_active'], name='doc_edit_session_active_idx'),
            models.Index(fields=['user', 'is_active'], name='doc_edit_session_user_idx'),
        ]
    
    def __str__(self):
        return f"{self.user.username} editing {self.document.name}"


class DocumentSearchIndex(models.Model):
    """Full-text search index for documents"""
    document = models.OneToOneField(Document, on_delete=models.CASCADE, related_name='search_index')
    
    # Indexed content
    title_vector = models.TextField()
    content_vector = models.TextField()
    metadata_vector = models.TextField(blank=True, null=True)
    
    # Search optimization
    word_count = models.IntegerField(default=0)
    language = models.CharField(max_length=10, default='en')
    
    # Indexing info
    indexed_at = models.DateTimeField(auto_now=True)
    indexing_version = models.IntegerField(default=1)
    
    class Meta:
        indexes = [
            models.Index(fields=['word_count'], name='doc_search_word_count_idx'),
            models.Index(fields=['language'], name='doc_search_language_idx'),
        ]
    
    def __str__(self):
        return f"Search index for {self.document.name}"


class SavedDocumentSearch(models.Model):
    """Save search queries for reuse"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='saved_searches')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    
    # Search parameters
    query = models.CharField(max_length=500)
    filters = models.JSONField(default=dict)
    sort_by = models.CharField(max_length=50, default='-created_at')
    
    # Usage tracking
    last_used_at = models.DateTimeField(blank=True, null=True)
    use_count = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'name']
        indexes = [
            models.Index(fields=['user', '-last_used_at'], name='saved_search_user_used_idx'),
        ]
    
    def __str__(self):
        return f"{self.user.username}: {self.name}"
