"""
Financial and Time Tracking Models
Handles time entries, billing, contracts, and financial management
"""

import uuid
from decimal import Decimal
from django.db import models
from django.utils import timezone




# from django.contrib.postgres.fields import models.J<PERSON><PERSON>ield  # Commented out for SQLite compatibility


class WorkType(models.Model):
    """Types of work for time tracking"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    default_rate = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)

    # Organization and categorization
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='work_types')
    category = models.CharField(max_length=50, blank=True, null=True)

    # Billing settings
    is_billable = models.BooleanField(default=True)
    billing_multiplier = models.DecimalField(max_digits=5, decimal_places=2, default=1.0)

    # Status
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['organization', 'name']
        indexes = [
            models.Index(fields=['organization', 'is_active']),
            models.Index(fields=['category']),
        ]
        ordering = ['name']

    def __str__(self):
        return self.name


class TimeEntry(models.Model):
    """Time tracking entries"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='time_entries')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='time_entries')
    task = models.ForeignKey('Task', on_delete=models.SET_NULL, blank=True, null=True, related_name='time_entries')
    work_type = models.ForeignKey(WorkType, on_delete=models.CASCADE, related_name='time_entries')

    # Time details
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(blank=True, null=True)
    duration_hours = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    duration_minutes = models.IntegerField(blank=True, null=True)  # Total minutes for easier queries

    # Timer functionality
    is_active_timer = models.BooleanField(default=False)
    timer_started_at = models.DateTimeField(blank=True, null=True)

    # Work description
    description = models.TextField()
    notes = models.TextField(blank=True, null=True)

    # Billing information
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    is_billable = models.BooleanField(default=True)
    billing_status = models.CharField(max_length=20, default='unbilled', choices=[
        ('unbilled', 'Unbilled'),
        ('billed', 'Billed'),
        ('paid', 'Paid'),
        ('non_billable', 'Non-billable'),
    ])

    # Approval workflow
    approval_status = models.CharField(max_length=20, default='pending', choices=[
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('needs_review', 'Needs Review'),
    ])
    approved_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_time_entries')
    approved_at = models.DateTimeField(blank=True, null=True)

    # Metadata
    tags = models.JSONField(default=list, blank=True)
    is_timer_based = models.BooleanField(default=False)  # Was this entry created via timer?

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', '-start_time']),
            models.Index(fields=['project', '-start_time']),
            models.Index(fields=['billing_status', '-start_time']),
            models.Index(fields=['approval_status', '-start_time']),
            models.Index(fields=['start_time']),
            models.Index(fields=['user', 'is_active_timer']),
        ]
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.user.username} - {self.project.name} ({self.duration_hours}h)"

    def calculate_duration(self):
        """Calculate duration from start and end times"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            total_minutes = int(delta.total_seconds() / 60)
            hours = round(delta.total_seconds() / 3600, 2)
            return hours, total_minutes
        return None, None

    def get_billable_amount(self):
        """Calculate billable amount for this entry"""
        if not self.is_billable or not self.duration_hours:
            return Decimal('0.00')

        rate = self.hourly_rate or self.work_type.default_rate or Decimal('0.00')
        multiplier = self.work_type.billing_multiplier or Decimal('1.0')

        return self.duration_hours * rate * multiplier

    def start_timer(self):
        """Start the timer for this time entry"""
        self.is_active_timer = True
        self.timer_started_at = timezone.now()
        self.save()
        return self

    def stop_timer(self):
        """Stop the timer and calculate duration"""
        if self.is_active_timer and self.timer_started_at:
            self.is_active_timer = False
            self.end_time = timezone.now()
            duration = self.end_time - self.timer_started_at
            self.duration_hours = round(duration.total_seconds() / 3600, 2)
            self.duration_minutes = int(duration.total_seconds() / 60)
            self.save()
        return self

    @property
    def timer_duration_display(self):
        """Get formatted timer duration for display"""
        if not self.is_active_timer or not self.timer_started_at:
            return "00:00:00"

        duration = timezone.now() - self.timer_started_at
        hours, remainder = divmod(int(duration.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    @classmethod
    def get_active_timer(cls, user):
        """Get active timer for a user (should only be one)"""
        return cls.objects.filter(user=user, is_active_timer=True).first()

    @classmethod
    def stop_all_active_timers(cls, user):
        """Stop all active timers for a user"""
        active_timers = cls.objects.filter(user=user, is_active_timer=True)
        for timer in active_timers:
            timer.stop_timer()
        return active_timers.count()

    def save(self, *args, **kwargs):
        # Auto-calculate duration if not set
        if self.start_time and self.end_time:
            if not self.duration_hours or not self.duration_minutes:
                hours, minutes = self.calculate_duration()
                if not self.duration_hours:
                    self.duration_hours = hours
                if not self.duration_minutes:
                    self.duration_minutes = minutes
        
        # If only duration_hours is set, calculate duration_minutes
        elif self.duration_hours and not self.duration_minutes:
            self.duration_minutes = int(float(self.duration_hours) * 60)
        
        # If only duration_minutes is set, calculate duration_hours
        elif self.duration_minutes and not self.duration_hours:
            self.duration_hours = round(self.duration_minutes / 60.0, 2)

        super().save(*args, **kwargs)


class TimesheetPeriod(models.Model):
    """Timesheet periods for grouping time entries"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    start_date = models.DateField()
    end_date = models.DateField()

    # Organization
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='timesheet_periods')

    # Status
    status = models.CharField(max_length=20, default='open', choices=[
        ('open', 'Open'),
        ('locked', 'Locked'),
        ('processed', 'Processed'),
        ('archived', 'Archived'),
    ])

    # Processing
    locked_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='locked_timesheet_periods')
    locked_at = models.DateTimeField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['organization', 'start_date', 'end_date']
        indexes = [
            models.Index(fields=['organization', 'status']),
            models.Index(fields=['start_date', 'end_date']),
        ]
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    def get_total_hours(self):
        """Get total hours for this period"""
        return TimeEntry.objects.filter(
            start_time__date__gte=self.start_date,
            start_time__date__lte=self.end_date
        ).aggregate(
            total=models.Sum('duration_hours')
        )['total'] or Decimal('0.00')


class TimesheetEntry(models.Model):
    """Individual timesheet entries (grouped time entries)"""
    timesheet_period = models.ForeignKey(TimesheetPeriod, on_delete=models.CASCADE, related_name='entries')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='timesheet_entries')

    # Aggregated data
    total_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    billable_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Status
    status = models.CharField(max_length=20, default='draft', choices=[
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ])

    submitted_at = models.DateTimeField(blank=True, null=True)
    approved_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_timesheets')
    approved_at = models.DateTimeField(blank=True, null=True)

    # Comments
    notes = models.TextField(blank=True, null=True)
    approval_notes = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['timesheet_period', 'user']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status', '-submitted_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.timesheet_period.name}"


class FeeTemplate(models.Model):
    """Templates for standardized fee structures"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    # Organization
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='fee_templates')
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_fee_templates')

    # Template configuration
    template_type = models.CharField(max_length=50, choices=[
        ('hourly', 'Hourly Rate'),
        ('fixed', 'Fixed Fee'),
        ('milestone', 'Milestone Based'),
        ('hybrid', 'Hybrid'),
    ])

    # Status
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['organization', 'name']
        indexes = [
            models.Index(fields=['organization', 'is_active']),
            models.Index(fields=['template_type']),
        ]

    def __str__(self):
        return self.name


class FeeTemplateSection(models.Model):
    """Sections within fee templates"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    fee_template = models.ForeignKey(FeeTemplate, on_delete=models.CASCADE, related_name='sections')

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    order = models.IntegerField(default=0)

    # Fee structure
    fee_type = models.CharField(max_length=20, choices=[
        ('hourly', 'Hourly'),
        ('fixed', 'Fixed'),
        ('percentage', 'Percentage'),
    ])
    base_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    estimated_hours = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)

    # Optionality
    is_required = models.BooleanField(default=True)
    is_billable = models.BooleanField(default=True)

    class Meta:
        indexes = [
            models.Index(fields=['fee_template', 'order']),
        ]
        ordering = ['order', 'name']

    def __str__(self):
        return f"{self.fee_template.name} - {self.name}"

    def get_estimated_total(self):
        """Calculate estimated total for this section"""
        if self.fee_type == 'fixed':
            return self.base_amount or Decimal('0.00')
        elif self.fee_type == 'hourly':
            rate = self.hourly_rate or Decimal('0.00')
            hours = self.estimated_hours or Decimal('0.00')
            return rate * hours
        return Decimal('0.00')


class FeeTemplateOption(models.Model):
    """Optional components within fee template sections"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    section = models.ForeignKey(FeeTemplateSection, on_delete=models.CASCADE, related_name='options')

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    # Option pricing
    additional_fee = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    additional_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)

    # Defaults
    is_default_selected = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.section.name} - {self.name}"


class ContractAdministration(models.Model):
    """Contract administration tracking"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='contracts')
    contract_number = models.CharField(max_length=100)
    contract_title = models.CharField(max_length=255)
    contractor_name = models.CharField(max_length=255)
    contract_amount = models.DecimalField(max_digits=15, decimal_places=2)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=50, default="active")
    payment_terms = models.TextField(blank=True, null=True)
    deliverables = models.JSONField(default=list)
    milestones = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.contract_number} - {self.contractor_name}"


class Invoice(models.Model):
    """Invoice management"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='invoices')
    invoice_number = models.CharField(max_length=100, unique=True)
    invoice_date = models.DateField()
    due_date = models.DateField()
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, default="draft")
    payment_date = models.DateField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_invoices')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Invoice {self.invoice_number}"
