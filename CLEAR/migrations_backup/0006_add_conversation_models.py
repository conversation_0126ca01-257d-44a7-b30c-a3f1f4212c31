import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated by Django 5.2.3 on 2025-06-15 17:25




class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0005_enhanced_chat_models'),
    ]

    operations = [
        migrations.AlterField(
            model_name='chatmessage',
            name='channel',
            field=models.CharField(default='general', help_text='Channel for organizing messages (legacy)', max_length=50),
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, help_text='Optional conversation name', max_length=255, null=True)),
                ('conversation_type', models.CharField(choices=[('direct', 'Direct Message'), ('group', 'Group Chat'), ('project', 'Project Discussion'), ('channel', 'Channel/Team Chat')], default='direct', max_length=20)),
                ('last_message_at', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_conversations', to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to='CLEAR.project')),
            ],
            options={
                'ordering': ['-last_message_at', '-created_at'],
            },
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='conversation',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='CLEAR.conversation'),
        ),
        migrations.CreateModel(
            name='ConversationMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('last_read_at', models.DateTimeField(blank=True, null=True)),
                ('is_admin', models.BooleanField(default=False, help_text='Can manage conversation settings')),
                ('notifications_enabled', models.BooleanField(default=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='CLEAR.conversation')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_memberships', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='conversation',
            name='participants',
            field=models.ManyToManyField(related_name='conversations', through='CLEAR.ConversationMember', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='conversationmember',
            index=models.Index(fields=['user', '-last_read_at'], name='CLEAR_conve_user_id_c70a36_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationmember',
            index=models.Index(fields=['conversation', 'user'], name='CLEAR_conve_convers_6234f4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='conversationmember',
            unique_together={('conversation', 'user')},
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['conversation_type', '-last_message_at'], name='CLEAR_conve_convers_26d1f5_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['project', '-last_message_at'], name='CLEAR_conve_project_655c10_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['created_by', '-last_message_at'], name='CLEAR_conve_created_dc98ee_idx'),
        ),
    ]
