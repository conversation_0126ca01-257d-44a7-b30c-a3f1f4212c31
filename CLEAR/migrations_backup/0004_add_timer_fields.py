from django.db import migrations, models

# Generated by Django 5.2.3 on 2025-06-15 01:49



class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0003_add_user_last_activity'),
    ]

    operations = [
        migrations.AddField(
            model_name='timeentry',
            name='hours',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Total hours for this entry', max_digits=8, null=True),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='is_active_timer',
            field=models.BooleanField(default=False, help_text='Whether this entry has an active timer'),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='timer_started_at',
            field=models.DateTimeField(blank=True, help_text='When timer was started for this entry', null=True),
        ),
        migrations.AddIndex(
            model_name='timeentry',
            index=models.Index(fields=['is_active_timer'], name='CLEAR_timee_is_acti_46ee0b_idx'),
        ),
        migrations.AddIndex(
            model_name='timeentry',
            index=models.Index(fields=['user', 'is_active_timer'], name='CLEAR_timee_user_id_bf8968_idx'),
        ),
    ]
