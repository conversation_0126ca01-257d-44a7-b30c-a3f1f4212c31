import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated by Django 5.2.3 on 2025-06-15 16:53



class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0004_add_timer_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('comment_added', 'Comment Added'), ('task_created', 'Task Created'), ('task_updated', 'Task Updated'), ('task_completed', 'Task Completed'), ('document_uploaded', 'Document Uploaded'), ('utility_updated', 'Utility Updated'), ('conflict_reported', 'Conflict Reported'), ('conflict_resolved', 'Conflict Resolved'), ('member_added', 'Team Member Added'), ('member_removed', 'Team Member Removed'), ('project_updated', 'Project Updated')], max_length=100)),
                ('description', models.TextField()),
                ('target_type', models.CharField(blank=True, max_length=50, null=True)),
                ('target_id', models.CharField(blank=True, max_length=255, null=True)),
                ('metadata', models.JSONField(default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ProjectMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('manager', 'Project Manager'), ('coordinator', 'Coordinator'), ('contributor', 'Contributor'), ('viewer', 'Viewer')], default='contributor', max_length=50)),
                ('permissions', models.JSONField(default=list)),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.AlterModelOptions(
            name='chatmessage',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='attachments',
            field=models.ManyToManyField(blank=True, related_name='message_attachments', to='CLEAR.document'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='is_urgent',
            field=models.BooleanField(default=False, help_text='Mark message as urgent'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='message_type',
            field=models.CharField(choices=[('text', 'Text Message'), ('system', 'System Message'), ('file', 'File Attachment'), ('image', 'Image')], default='text', max_length=20),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='reply_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replies', to='CLEAR.chatmessage'),
        ),
        migrations.CreateModel(
            name='MessageRead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('read_at', models.DateTimeField(auto_now_add=True)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='read_records', to='CLEAR.chatmessage')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_reads', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='read_by',
            field=models.ManyToManyField(blank=True, related_name='read_messages', through='CLEAR.MessageRead', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='chatmessage',
            index=models.Index(fields=['is_urgent', '-created_at'], name='CLEAR_chatm_is_urge_9debb6_idx'),
        ),
        migrations.AddIndex(
            model_name='chatmessage',
            index=models.Index(fields=['message_type', '-created_at'], name='CLEAR_chatm_message_771858_idx'),
        ),
        migrations.AddField(
            model_name='projectactivity',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_activities', to='CLEAR.project'),
        ),
        migrations.AddField(
            model_name='projectactivity',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_activities', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projectmember',
            name='added_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_project_members', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projectmember',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='team_members', to='CLEAR.project'),
        ),
        migrations.AddField(
            model_name='projectmember',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_memberships', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='messageread',
            index=models.Index(fields=['message', 'user'], name='CLEAR_messa_message_5d681b_idx'),
        ),
        migrations.AddIndex(
            model_name='messageread',
            index=models.Index(fields=['user', '-read_at'], name='CLEAR_messa_user_id_7eafc4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messageread',
            unique_together={('message', 'user')},
        ),
        migrations.AddIndex(
            model_name='projectactivity',
            index=models.Index(fields=['project', '-timestamp'], name='CLEAR_proje_project_76aba8_idx'),
        ),
        migrations.AddIndex(
            model_name='projectactivity',
            index=models.Index(fields=['user', '-timestamp'], name='CLEAR_proje_user_id_f4561f_idx'),
        ),
        migrations.AddIndex(
            model_name='projectactivity',
            index=models.Index(fields=['action_type', '-timestamp'], name='CLEAR_proje_action__e0c5ca_idx'),
        ),
        migrations.AddIndex(
            model_name='projectmember',
            index=models.Index(fields=['project', 'is_active'], name='CLEAR_proje_project_556e98_idx'),
        ),
        migrations.AddIndex(
            model_name='projectmember',
            index=models.Index(fields=['user', 'is_active'], name='CLEAR_proje_user_id_8f3a25_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='projectmember',
            unique_together={('project', 'user')},
        ),
    ]
