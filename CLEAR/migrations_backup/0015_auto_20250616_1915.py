import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

# Generated by Django 5.2.3 on 2025-06-16 23:15



class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0014_spatial_collaboration_models'),
    ]

    operations = [
        # Add MFA fields to User model
        migrations.AddField(
            model_name='user',
            name='is_mfa_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='totp_secret',
            field=models.CharField(blank=True, max_length=32, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='backup_tokens',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='user',
            name='mfa_setup_completed',
            field=models.BooleanField(default=False),
        ),
        
        # Create MFASession model
        migrations.CreateModel(
            name='MFASession',
            fields=[
                ('id', models.BigA<PERSON>Field(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40, unique=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('verification_method', models.CharField(blank=True, choices=[('totp', 'TOTP Authenticator'), ('backup', 'Backup Token')], max_length=20, null=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mfa_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
