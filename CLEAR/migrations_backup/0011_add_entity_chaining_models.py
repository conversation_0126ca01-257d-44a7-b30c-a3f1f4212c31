import uuid

import django.contrib.postgres.fields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated manually for Entity Chaining Phase 2 Implementation




class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('CLEAR', '0009_advanced_collaboration_features'),
    ]

    operations = [
        migrations.CreateModel(
            name='EntityHierarchy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('parent_entity_type', models.CharField(help_text='Type of parent entity (project, task, etc.)', max_length=50)),
                ('parent_entity_id', models.CharField(help_text='ID of parent entity', max_length=255)),
                ('child_entity_type', models.CharField(help_text='Type of child entity', max_length=50)),
                ('child_entity_id', models.Char<PERSON><PERSON>(help_text='ID of child entity', max_length=255)),
                ('hierarchy_level', models.PositiveIntegerField(help_text='Level in hierarchy (0=root, 1=direct child, etc.)')),
                ('relationship_type', models.CharField(choices=[('contains', 'Contains'), ('owns', 'Owns'), ('manages', 'Manages'), ('assigned_to', 'Assigned To'), ('part_of', 'Part Of'), ('depends_on', 'Depends On'), ('coordinates_with', 'Coordinates With')], default='contains', max_length=50)),
                ('hierarchy_path', models.TextField(help_text="Full path from root to child (e.g., 'project-123/phase-2/task-456')")),
                ('is_active', models.BooleanField(default=True, help_text='Whether this hierarchy relationship is active')),
                ('inherited_properties', models.JSONField(default=dict, help_text='Properties inherited from parent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_hierarchies', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [
                    models.Index(fields=['parent_entity_type', 'parent_entity_id'], name='CLEAR_entit_parent__f3c4d8_idx'),
                    models.Index(fields=['child_entity_type', 'child_entity_id'], name='CLEAR_entit_child_e_e8b2a1_idx'),
                    models.Index(fields=['hierarchy_level', 'relationship_type'], name='CLEAR_entit_hierarc_96d7b4_idx'),
                    models.Index(fields=['hierarchy_path'], name='CLEAR_entit_hierarc_8a1b5e_idx'),
                    models.Index(fields=['is_active', '-created_at'], name='CLEAR_entit_is_acti_c7f3e2_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='ChainedEntityContext',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('entity_type', models.CharField(max_length=50)),
                ('entity_id', models.CharField(max_length=255)),
                ('chain_position', models.PositiveIntegerField(help_text='Position in the entity chain (0=root)')),
                ('full_chain_path', models.TextField(help_text="Complete chain path (e.g., 'project-123/phase-2/task-456')")),
                ('inherited_tags', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=100), blank=True, default=list, size=None)),
                ('inherited_permissions', models.JSONField(default=dict, help_text='Access permissions inherited from parents')),
                ('inherited_metadata', models.JSONField(default=dict, help_text='Metadata inherited from parent entities')),
                ('chain_properties', models.JSONField(default=dict, help_text='Properties specific to this chain context')),
                ('effective_permissions', models.JSONField(default=dict, help_text='Final computed permissions for this entity in chain')),
                ('mention_frequency', models.PositiveIntegerField(default=0, help_text='How often this entity is mentioned in chains')),
                ('last_chain_mention', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'indexes': [
                    models.Index(fields=['entity_type', 'entity_id'], name='CLEAR_chain_entity__b8e4f7_idx'),
                    models.Index(fields=['chain_position', 'full_chain_path'], name='CLEAR_chain_chain_p_d9a6c2_idx'),
                    models.Index(fields=['-mention_frequency'], name='CLEAR_chain_mention_5f7b8e_idx'),
                    models.Index(fields=['-last_chain_mention'], name='CLEAR_chain_last_ch_3e9d1a_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='ChainedEntityMention',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('full_chain_text', models.TextField(help_text="Complete chain mention (e.g., '@project-123/phase-2/task-456')")),
                ('chain_position', models.PositiveIntegerField(help_text='Position of this entity in the chain (0=root)')),
                ('chain_depth', models.PositiveIntegerField(help_text='Total depth of the chain')),
                ('is_chain_root', models.BooleanField(default=False, help_text='Whether this is the root entity in the chain')),
                ('is_chain_leaf', models.BooleanField(default=False, help_text='Whether this is the final entity in the chain')),
                ('is_valid_chain', models.BooleanField(default=True, help_text='Whether this chain represents valid entity relationships')),
                ('validation_errors', models.JSONField(default=list, help_text='Any validation errors for this chain')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('base_mention', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chained_mentions', to='CLEAR.entitymention')),
                ('chain_context', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='CLEAR.chainedentitycontext')),
                ('parent_mention', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_mentions', to='CLEAR.chainedentitymention')),
            ],
            options={
                'indexes': [
                    models.Index(fields=['full_chain_text'], name='CLEAR_chain_full_ch_e6f8a4_idx'),
                    models.Index(fields=['chain_position', 'chain_depth'], name='CLEAR_chain_chain_p_2d7c9b_idx'),
                    models.Index(fields=['is_chain_root', 'is_chain_leaf'], name='CLEAR_chain_is_chai_f4e5d8_idx'),
                    models.Index(fields=['is_valid_chain', '-created_at'], name='CLEAR_chain_is_vali_a8c7f2_idx'),
                    models.Index(fields=['parent_mention', 'chain_position'], name='CLEAR_chain_parent__9b3e6a_idx'),
                ],
            },
        ),
        migrations.AlterUniqueTogether(
            name='entityhierarchy',
            unique_together={('parent_entity_type', 'parent_entity_id', 'child_entity_type', 'child_entity_id')},
        ),
        migrations.AlterUniqueTogether(
            name='chainedentitycontext',
            unique_together={('entity_type', 'entity_id', 'full_chain_path')},
        ),
    ]