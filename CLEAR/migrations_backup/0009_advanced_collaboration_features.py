import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated by Django for advanced collaboration features
# Week 4 Task 4.2: Advanced Collaboration Features



class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0008_add_ai_communication_intelligence'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageThread',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, help_text='Optional thread title', max_length=255, null=True)),
                ('is_collapsed', models.BooleanField(default=False, help_text='Whether thread is collapsed by default')),
                ('is_locked', models.BooleanField(default=False, help_text='Prevent new replies')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('root_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='thread_root', to='CLEAR.chatmessage')),
            ],
        ),
        migrations.CreateModel(
            name='MessageMention',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mention_text', models.CharField(help_text='The actual mention text used (e.g., @username)', max_length=255)),
                ('position_start', models.IntegerField(help_text='Character position where mention starts')),
                ('position_end', models.IntegerField(help_text='Character position where mention ends')),
                ('is_notified', models.BooleanField(default=False, help_text='Whether user was notified of this mention')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('mentioned_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_mentions', to=settings.AUTH_USER_MODEL)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentions', to='CLEAR.chatmessage')),
            ],
        ),
        migrations.CreateModel(
            name='MessageReaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emoji', models.CharField(help_text='Emoji unicode or shortcode (e.g., 👍, :thumbsup:)', max_length=100)),
                ('emoji_unicode', models.CharField(blank=True, help_text='Standardized unicode representation', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to='CLEAR.chatmessage')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_reactions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CollaborationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('auto_collapse_threads', models.BooleanField(default=False, help_text='Auto-collapse threads with 3+ replies')),
                ('show_thread_previews', models.BooleanField(default=True, help_text='Show thread reply previews')),
                ('mention_notifications', models.BooleanField(default=True, help_text='Receive notifications for @mentions')),
                ('mention_sound', models.BooleanField(default=True, help_text='Play sound for @mentions')),
                ('mention_email', models.BooleanField(default=False, help_text='Email notifications for @mentions')),
                ('show_reaction_tooltips', models.BooleanField(default=True, help_text='Show who reacted with tooltips')),
                ('reaction_notifications', models.BooleanField(default=True, help_text='Receive notifications for reactions to my messages')),
                ('custom_emoji_set', models.CharField(choices=[('default', 'Default'), ('minimal', 'Minimal'), ('professional', 'Professional'), ('fun', 'Fun')], default='default', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='collaboration_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='messagethread',
            index=models.Index(fields=['root_message', '-updated_at'], name='CLEAR_messa_root_me_43cd39_idx'),
        ),
        migrations.AddIndex(
            model_name='messagethread',
            index=models.Index(fields=['-updated_at'], name='CLEAR_messa_updated_9a3c74_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagereaction',
            unique_together={('message', 'user', 'emoji')},
        ),
        migrations.AddIndex(
            model_name='messagereaction',
            index=models.Index(fields=['message', 'emoji'], name='CLEAR_messa_message_c717a1_idx'),
        ),
        migrations.AddIndex(
            model_name='messagereaction',
            index=models.Index(fields=['user', '-created_at'], name='CLEAR_messa_user_id_2ff9a2_idx'),
        ),
        migrations.AddIndex(
            model_name='messagereaction',
            index=models.Index(fields=['message', '-created_at'], name='CLEAR_messa_message_52c834_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagemention',
            unique_together={('message', 'mentioned_user')},
        ),
        migrations.AddIndex(
            model_name='messagemention',
            index=models.Index(fields=['mentioned_user', '-created_at'], name='CLEAR_messa_mention_827c95_idx'),
        ),
        migrations.AddIndex(
            model_name='messagemention',
            index=models.Index(fields=['message', 'mentioned_user'], name='CLEAR_messa_message_45e8b7_idx'),
        ),
        migrations.AddIndex(
            model_name='messagemention',
            index=models.Index(fields=['is_notified', '-created_at'], name='CLEAR_messa_is_noti_7b2f0c_idx'),
        ),
    ]