import uuid

import django.contrib.auth.models
import django.contrib.auth.validators
import django.contrib.gis.db.models.fields
import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

# Generated by Django 5.2.3 on 2025-06-13 23:59




class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CoordinateSystem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("srid", models.IntegerField()),
                ("type", models.CharField(max_length=50)),
                ("description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("state", models.CharField(default="Indiana", max_length=50)),
                ("zone_name", models.CharField(blank=True, max_length=100, null=True)),
                ("county_code", models.CharField(blank=True, max_length=10, null=True)),
                (
                    "projection_group",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "lat_origin_deg",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "lat_origin_min",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "lat_origin_sec",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "central_meridian_deg",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "central_meridian_min",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "central_meridian_sec",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "central_meridian_scale",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "false_easting",
                    models.DecimalField(
                        blank=True, decimal_places=3, max_digits=15, null=True
                    ),
                ),
                (
                    "false_northing",
                    models.DecimalField(
                        blank=True, decimal_places=3, max_digits=15, null=True
                    ),
                ),
                (
                    "validation_point_easting",
                    models.DecimalField(
                        blank=True, decimal_places=3, max_digits=15, null=True
                    ),
                ),
                (
                    "validation_point_northing",
                    models.DecimalField(
                        blank=True, decimal_places=3, max_digits=15, null=True
                    ),
                ),
                (
                    "indiana_2digit_county",
                    models.CharField(blank=True, max_length=5, null=True),
                ),
                (
                    "projection_group_abbreviation",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("subdomain", models.CharField(max_length=100, unique=True)),
                ("logo_url", models.URLField(blank=True, null=True)),
                ("primary_color", models.CharField(default="#005AAB", max_length=7)),
                ("secondary_color", models.CharField(default="#8CC63F", max_length=7)),
                ("settings", models.JSONField(default=dict)),
                (
                    "subscription_plan",
                    models.CharField(default="professional", max_length=50),
                ),
                (
                    "subscription_status",
                    models.CharField(default="active", max_length=20),
                ),
                (
                    "subscription_expires_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.CharField(max_length=255, primary_key=True, serialize=False),
                ),
                ("monday_id", models.CharField(blank=True, max_length=50, null=True)),
                ("name", models.CharField(max_length=255)),
                ("client", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                ("manager_id", models.IntegerField(blank=True, null=True)),
                ("high_priority_items", models.IntegerField(blank=True, null=True)),
                ("medium_priority_items", models.IntegerField(blank=True, null=True)),
                ("low_priority_items", models.IntegerField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("record_id", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "client_job_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("work_type", models.CharField(blank=True, max_length=100, null=True)),
                ("rag_status", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "project_id_only",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "phase_id_only",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "last_milestone",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "coordination_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "project_funding",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("ntp_date", models.DateField(blank=True, null=True)),
                ("letting_bid_date", models.DateField(blank=True, null=True)),
                (
                    "this_month_status",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("status_update_date", models.DateField(blank=True, null=True)),
                ("client_pm", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "hourly_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "contract_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "billed_to_date",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "project_hours_for_billed",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "wip",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "billed_plus_wip",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "current_cost",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "billed_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "profit_to_date",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "profit_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "project_priority",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "egis_project_manager",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "egis_project_manager_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                (
                    "client_contact",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "project_health_rag",
                    models.CharField(default="Project Health", max_length=50),
                ),
                (
                    "current_phase",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "coordinator_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ProjectTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("icon", models.CharField(blank=True, max_length=50, null=True)),
                ("color", models.CharField(blank=True, max_length=7, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_default", models.BooleanField(default=False)),
                ("workflow_phases", models.JSONField(default=list)),
                ("settings", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="LineStyle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("utility_type", models.CharField(max_length=50)),
                ("installation_type", models.CharField(max_length=50)),
                ("line_color", models.CharField(max_length=7)),
                ("line_weight", models.FloatField()),
                ("line_pattern", models.CharField(max_length=100)),
                (
                    "letter_symbols",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("color_name", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "dash_pattern",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("letter_spacing", models.IntegerField(blank=True, null=True)),
                ("standard_811", models.BooleanField(default=True)),
            ],
            options={
                "unique_together": {("utility_type", "installation_type")},
            },
        ),
        migrations.CreateModel(
            name="ContractAdministration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("contract_number", models.CharField(max_length=100)),
                ("contract_title", models.CharField(max_length=255)),
                ("contractor_name", models.CharField(max_length=255)),
                (
                    "contract_amount",
                    models.DecimalField(decimal_places=2, max_digits=15),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("status", models.CharField(default="active", max_length=50)),
                ("payment_terms", models.TextField(blank=True, null=True)),
                ("deliverables", models.JSONField(default=list)),
                ("milestones", models.JSONField(default=list)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contracts",
                        to="CLEAR.project",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="project",
            name="template",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="projects",
                to="CLEAR.projecttemplate",
            ),
        ),
        migrations.CreateModel(
            name="Stakeholder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("full_name", models.CharField(max_length=255)),
                ("contact_company", models.CharField(max_length=255)),
                (
                    "company_abbreviation",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "type_delivery",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "stakeholder_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business_phone",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "business_fax",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "mobile_phone",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                ("is_utility_coordinator", models.BooleanField(default=False)),
                ("is_indot_authorized_rep", models.BooleanField(default=False)),
                ("aliases", models.TextField(blank=True, null=True)),
                ("company_logo", models.URLField(blank=True, null=True)),
                (
                    "client_company",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "full_name_company",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
            ],
            options={
                "unique_together": {("full_name_company", "type_delivery")},
            },
        ),
        migrations.CreateModel(
            name="SystemMetric",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("metric_name", models.CharField(max_length=100)),
                ("metric_value", models.FloatField()),
                ("metric_unit", models.CharField(blank=True, max_length=50, null=True)),
                ("category", models.CharField(max_length=50)),
                ("tags", models.JSONField(default=dict)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["metric_name", "-timestamp"],
                        name="CLEAR_syste_metric__a68195_idx",
                    ),
                    models.Index(
                        fields=["category", "-timestamp"],
                        name="CLEAR_syste_categor_b25171_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="Utility",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("type", models.CharField(max_length=100)),
                ("status", models.CharField(default="active", max_length=50)),
                (
                    "contact_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "contact_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                (
                    "contact_phone",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("notes", models.TextField(blank=True, null=True)),
                ("last_response", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("initiation_date", models.DateField(blank=True, null=True)),
                ("depth_notes", models.TextField(blank=True, null=True)),
                (
                    "installation_depth",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                (
                    "vertical_clearance",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                (
                    "vertical_datum",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="utilities",
                        to="CLEAR.project",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Conflict",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("description", models.TextField()),
                ("status", models.CharField(default="open", max_length=50)),
                ("priority", models.CharField(max_length=20)),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                ("resolution_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("confidence_score", models.IntegerField(blank=True, null=True)),
                (
                    "conflict_3d_geometry",
                    django.contrib.gis.db.models.fields.GeometryField(
                        blank=True, geography=True, null=True, srid=4326
                    ),
                ),
                (
                    "conflict_elevation",
                    models.DecimalField(
                        blank=True, decimal_places=3, max_digits=10, null=True
                    ),
                ),
                (
                    "conflict_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("detected_timestamp", models.DateTimeField(blank=True, null=True)),
                ("detection_method", models.CharField(default="manual", max_length=50)),
                ("impact_score", models.IntegerField(blank=True, null=True)),
                ("is_vertical_conflict", models.BooleanField(default=False)),
                ("likelihood_score", models.IntegerField(blank=True, null=True)),
                (
                    "reviewed_by",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("reviewed_timestamp", models.DateTimeField(blank=True, null=True)),
                ("risk_score", models.IntegerField(blank=True, null=True)),
                (
                    "vertical_clearance_violation",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conflicts",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "utility",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conflicts",
                        to="CLEAR.utility",
                    ),
                ),
                (
                    "utility2",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conflicts2",
                        to="CLEAR.utility",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Workflow",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("workflow_type", models.CharField(max_length=100)),
                ("steps_config", models.JSONField(default=list)),
                ("triggers_config", models.JSONField(default=dict)),
                ("conditions_config", models.JSONField(default=dict)),
                ("is_active", models.BooleanField(default=True)),
                ("is_template", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workflows",
                        to="CLEAR.organization",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                ("first_name", models.CharField(max_length=150)),
                ("last_name", models.CharField(max_length=150)),
                ("email", models.EmailField(max_length=254, unique=True)),
                (
                    "role",
                    models.CharField(default="Utility Coordinator", max_length=50),
                ),
                ("avatar_url", models.URLField(blank=True, null=True)),
                (
                    "unit_preference",
                    models.CharField(default="imperial", max_length=20),
                ),
                ("custom_settings", models.JSONField(default=dict)),
                ("is_active", models.BooleanField(default=True)),
                ("reset_password_on_login", models.BooleanField(default=False)),
                ("is_admin", models.BooleanField(default=False)),
                ("created_by_saml", models.BooleanField(default=False)),
                ("dashboard_layout", models.JSONField(default=dict)),
                ("ui_preferences", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="WorkflowExecution",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("status", models.CharField(default="running", max_length=20)),
                ("current_step", models.IntegerField(default=0)),
                ("context_data", models.JSONField(default=dict)),
                ("error_message", models.TextField(blank=True, null=True)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workflow_executions",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="executions",
                        to="CLEAR.workflow",
                    ),
                ),
                (
                    "triggered_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="triggered_workflows",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="workflow",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_workflows",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="UtilityLineData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("line_type", models.CharField(max_length=100)),
                ("utility_type", models.CharField(max_length=100)),
                ("installation_type", models.CharField(max_length=100)),
                (
                    "geometry",
                    django.contrib.gis.db.models.fields.GeometryField(
                        blank=True, geography=True, null=True, srid=4326
                    ),
                ),
                ("properties", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "coordinate_system",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="CLEAR.coordinatesystem",
                    ),
                ),
                (
                    "line_style",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="CLEAR.linestyle",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="CLEAR.project"
                    ),
                ),
                (
                    "utility",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_data",
                        to="CLEAR.utility",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Task",
            fields=[
                (
                    "id",
                    models.CharField(max_length=255, primary_key=True, serialize=False),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("priority", models.CharField(default="Medium", max_length=20)),
                ("due_date", models.DateField(blank=True, null=True)),
                ("completed", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("monday_id", models.CharField(blank=True, max_length=50, null=True)),
                ("status", models.CharField(default="Not Started", max_length=50)),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "estimated_hours",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                (
                    "actual_hours",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                ("progress_percentage", models.IntegerField(default=0)),
                (
                    "assigned_to_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("dependencies", models.JSONField(default=list)),
                (
                    "predecessors",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        default=list,
                        size=None,
                    ),
                ),
                ("task_type", models.CharField(default="Task", max_length=50)),
                ("milestone", models.BooleanField(default=False)),
                ("critical_path", models.BooleanField(default=False)),
                ("phase", models.CharField(blank=True, max_length=100, null=True)),
                ("board_id", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tasks",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_tasks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Report",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("report_type", models.CharField(max_length=50)),
                ("category", models.CharField(max_length=100)),
                ("query_config", models.JSONField(default=dict)),
                ("chart_config", models.JSONField(default=dict)),
                ("filters_config", models.JSONField(default=dict)),
                (
                    "schedule_config",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="projecttemplate",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_templates",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                ("expires_at", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("invoice_number", models.CharField(max_length=100, unique=True)),
                ("invoice_date", models.DateField()),
                ("due_date", models.DateField()),
                ("amount", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("total_amount", models.DecimalField(decimal_places=2, max_digits=12)),
                ("status", models.CharField(default="draft", max_length=20)),
                ("payment_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_invoices",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="GISLayer",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("layer_type", models.CharField(max_length=50)),
                ("data_source", models.CharField(max_length=500)),
                ("style_config", models.JSONField(default=dict)),
                ("visibility", models.BooleanField(default=True)),
                ("opacity", models.FloatField(default=1.0)),
                ("z_index", models.IntegerField(default=0)),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gis_layers",
                        to="CLEAR.organization",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_layers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["z_index", "name"],
            },
        ),
        migrations.CreateModel(
            name="FeatureRequest",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("category", models.CharField(max_length=100)),
                ("priority", models.CharField(default="medium", max_length=20)),
                ("status", models.CharField(default="submitted", max_length=20)),
                ("votes", models.IntegerField(default=0)),
                (
                    "estimated_effort",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "target_release",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_features",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "submitted_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="submitted_features",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("file_path", models.CharField(max_length=500)),
                ("file_size", models.BigIntegerField()),
                ("file_type", models.CharField(max_length=100)),
                ("mime_type", models.CharField(max_length=200)),
                ("folder", models.CharField(default="/", max_length=500)),
                (
                    "tags",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        default=list,
                        size=None,
                    ),
                ),
                ("version", models.IntegerField(default=1)),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="CLEAR.task",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="uploaded_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("commentable_type", models.CharField(max_length=50)),
                ("commentable_id", models.CharField(max_length=255)),
                ("content", models.TextField()),
                (
                    "mentions",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.IntegerField(), default=list, size=None
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="CLEAR.comment",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                (
                    "channel",
                    models.CharField(
                        default="general",
                        help_text="Channel for organizing messages",
                        max_length=50,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        help_text="Optional project context",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_messages",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_messages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Activity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("action", models.CharField(max_length=100)),
                ("target", models.CharField(blank=True, max_length=255, null=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activities",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ProjectPhase",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("phase_number", models.IntegerField()),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                ("expected_duration_days", models.IntegerField(blank=True, null=True)),
                ("status", models.CharField(default="planned", max_length=50)),
                ("completion_percentage", models.IntegerField(default=0)),
                ("deliverables", models.JSONField(default=list)),
                ("requirements", models.JSONField(default=list)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="phases",
                        to="CLEAR.project",
                    ),
                ),
            ],
            options={
                "ordering": ["phase_number"],
                "unique_together": {("project", "phase_number")},
            },
        ),
        migrations.CreateModel(
            name="RolePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("role", models.CharField(max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "permission",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="CLEAR.permission",
                    ),
                ),
            ],
            options={
                "unique_together": {("role", "permission")},
            },
        ),
        migrations.CreateModel(
            name="WhisperMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "expires_at",
                    models.DateTimeField(
                        help_text="Message expires and is auto-deleted"
                    ),
                ),
                (
                    "read_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When recipient read the message",
                        null=True,
                    ),
                ),
                (
                    "from_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_whispers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "to_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="received_whispers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["to_user", "expires_at"],
                        name="CLEAR_whisp_to_user_3afea8_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="CLEAR_whisp_expires_50496b_idx"
                    ),
                    models.Index(
                        fields=["from_user", "-created_at"],
                        name="CLEAR_whisp_from_us_64719d_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TimeEntry",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("start_time", models.DateTimeField()),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                ("duration_minutes", models.IntegerField(blank=True, null=True)),
                ("billable", models.BooleanField(default=True)),
                (
                    "hourly_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("approved", models.BooleanField(default=False)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_entries",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_entries",
                        to="CLEAR.task",
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_time_entries",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_entries",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["user", "start_time"],
                        name="CLEAR_timee_user_id_306f3d_idx",
                    ),
                    models.Index(
                        fields=["project", "start_time"],
                        name="CLEAR_timee_project_80adb3_idx",
                    ),
                    models.Index(
                        fields=["billable"], name="CLEAR_timee_billabl_c0d6ef_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ReportExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("parameters", models.JSONField(default=dict)),
                ("status", models.CharField(default="running", max_length=20)),
                ("result_data", models.JSONField(blank=True, default=dict, null=True)),
                (
                    "result_file_path",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                ("execution_time_ms", models.IntegerField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "report",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="executions",
                        to="CLEAR.report",
                    ),
                ),
                (
                    "executed_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="executed_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["report", "-created_at"],
                        name="CLEAR_repor_report__0bf17d_idx",
                    ),
                    models.Index(
                        fields=["executed_by", "-created_at"],
                        name="CLEAR_repor_execute_b1148e_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="OrganizationMember",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("role", models.CharField(default="member", max_length=50)),
                ("permissions", models.JSONField(default=list)),
                ("joined_at", models.DateTimeField(auto_now_add=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="members",
                        to="CLEAR.organization",
                    ),
                ),
                (
                    "invited_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invitations_sent",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="organizations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("organization", "user")},
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("type", models.CharField(max_length=50)),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                ("data", models.JSONField(blank=True, default=dict, null=True)),
                ("read", models.BooleanField(default=False)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("action_url", models.URLField(blank=True, null=True)),
                (
                    "action_label",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("priority", models.CharField(default="normal", max_length=20)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["user"], name="CLEAR_notif_user_id_14036f_idx"
                    ),
                    models.Index(
                        fields=["-created_at"], name="CLEAR_notif_created_66e025_idx"
                    ),
                    models.Index(fields=["read"], name="CLEAR_notif_read_aeffe5_idx"),
                    models.Index(fields=["type"], name="CLEAR_notif_type_8a57db_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="Note",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("content", models.TextField()),
                (
                    "tags",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        default=list,
                        size=None,
                    ),
                ),
                ("is_private", models.BooleanField(default=True)),
                ("is_pinned", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notes",
                        to="CLEAR.project",
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notes",
                        to="CLEAR.task",
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shared_with",
                    models.ManyToManyField(
                        blank=True,
                        related_name="shared_notes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["author", "-updated_at"],
                        name="CLEAR_note_author__911f1f_idx",
                    ),
                    models.Index(
                        fields=["project", "-updated_at"],
                        name="CLEAR_note_project_a22a9c_idx",
                    ),
                    models.Index(
                        fields=["is_pinned", "-updated_at"],
                        name="CLEAR_note_is_pinn_0dd8d0_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="KnowledgeArticle",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("slug", models.SlugField(unique=True)),
                ("content", models.TextField()),
                ("category", models.CharField(max_length=100)),
                (
                    "tags",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        default=list,
                        size=None,
                    ),
                ),
                ("is_published", models.BooleanField(default=False)),
                ("views_count", models.IntegerField(default=0)),
                ("helpful_votes", models.IntegerField(default=0)),
                ("unhelpful_votes", models.IntegerField(default=0)),
                ("last_reviewed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="knowledge_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["category"], name="CLEAR_knowl_categor_a203f9_idx"
                    ),
                    models.Index(
                        fields=["is_published"], name="CLEAR_knowl_is_publ_2683ed_idx"
                    ),
                    models.Index(
                        fields=["-views_count"], name="CLEAR_knowl_views_c_f6e998_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="FeatureVote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "feature_request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_votes",
                        to="CLEAR.featurerequest",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feature_votes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("feature_request", "user")},
            },
        ),
        migrations.CreateModel(
            name="DocumentVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version_number", models.IntegerField()),
                ("file_path", models.CharField(max_length=500)),
                ("file_size", models.BigIntegerField()),
                ("change_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="versions",
                        to="CLEAR.document",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-version_number"],
                "unique_together": {("document", "version_number")},
            },
        ),
        migrations.CreateModel(
            name="DocumentShare",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("permission_level", models.CharField(default="view", max_length=20)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shares",
                        to="CLEAR.document",
                    ),
                ),
                (
                    "shared_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents_shared",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shared_with",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shared_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("document", "shared_with")},
            },
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["project"], name="CLEAR_docum_project_fae15e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(fields=["folder"], name="CLEAR_docum_folder_e4b581_idx"),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["file_type"], name="CLEAR_docum_file_ty_c34a56_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["-created_at"], name="CLEAR_docum_created_9ce36c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["commentable_type", "commentable_id"],
                name="CLEAR_comme_comment_6fec3f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(fields=["user"], name="CLEAR_comme_user_id_600f9d_idx"),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["parent"], name="CLEAR_comme_parent__dc0fce_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["channel", "-created_at"], name="CLEAR_chatm_channel_b1bd21_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["project", "-created_at"], name="CLEAR_chatm_project_e09dc8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["user", "-created_at"], name="CLEAR_chatm_user_id_376bd5_idx"
            ),
        ),
    ]
