import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated manually for Document Management System Phase 2: Advanced Features




class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0011_add_entity_chaining_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentVersionBranch',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('base_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spawned_branches', to='CLEAR.documentversion')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_document_branches', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branches', to='CLEAR.document')),
                ('parent_branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_branches', to='CLEAR.documentversionbranch')),
            ],
            options={
                'indexes': [
                    models.Index(fields=['document', 'is_active'], name='doc_branch_doc_active_idx'),
                    models.Index(fields=['document', 'is_default'], name='doc_branch_doc_default_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='DocumentVersionDiff',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('diff_type', models.CharField(default='text', max_length=20)),
                ('diff_data', models.JSONField(default=dict)),
                ('similarity_score', models.FloatField(default=0.0)),
                ('changes_summary', models.JSONField(default=dict)),
                ('computed_at', models.DateTimeField(auto_now_add=True)),
                ('from_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diffs_as_source', to='CLEAR.documentversion')),
                ('to_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diffs_as_target', to='CLEAR.documentversion')),
            ],
            options={
                'indexes': [
                    models.Index(fields=['from_version', 'to_version'], name='doc_diff_versions_idx'),
                    models.Index(fields=['similarity_score'], name='doc_diff_similarity_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='DocumentLock',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('lock_type', models.CharField(default='edit', max_length=20)),
                ('section', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('auto_release_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('released_at', models.DateTimeField(blank=True, null=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locks', to='CLEAR.document')),
                ('locked_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_locks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [
                    models.Index(fields=['document', 'is_active'], name='doc_lock_doc_active_idx'),
                    models.Index(fields=['locked_by', 'is_active'], name='doc_lock_user_active_idx'),
                    models.Index(fields=['auto_release_at'], name='doc_lock_auto_release_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='DocumentEditingSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100)),
                ('cursor_position', models.JSONField(blank=True, default=dict)),
                ('selection_range', models.JSONField(blank=True, default=dict)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='editing_sessions', to='CLEAR.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_editing_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [
                    models.Index(fields=['document', 'is_active'], name='doc_session_doc_active_idx'),
                    models.Index(fields=['user', 'is_active'], name='doc_session_user_active_idx'),
                    models.Index(fields=['last_activity'], name='doc_session_activity_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='DocumentSearchIndex',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content_text', models.TextField()),
                ('metadata_text', models.TextField(blank=True, null=True)),
                ('search_vector', models.TextField(blank=True, null=True)),
                ('tags_text', models.TextField(blank=True, null=True)),
                ('last_indexed', models.DateTimeField(auto_now=True)),
                ('index_version', models.IntegerField(default=1)),
                ('document', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='search_index', to='CLEAR.document')),
            ],
            options={
                'indexes': [
                    models.Index(fields=['last_indexed'], name='doc_search_last_indexed_idx'),
                    models.Index(fields=['index_version'], name='doc_search_version_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='SavedDocumentSearch',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('query', models.TextField()),
                ('filters', models.JSONField(default=dict)),
                ('folder_scope', models.CharField(blank=True, max_length=500, null=True)),
                ('is_public', models.BooleanField(default=False)),
                ('use_count', models.IntegerField(default=0)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='saved_document_searches', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [
                    models.Index(fields=['user', '-last_used'], name='saved_search_user_used_idx'),
                    models.Index(fields=['is_public', '-use_count'], name='saved_search_public_count_idx'),
                ],
            },
        ),
        # Add branch field to DocumentVersion model
        migrations.AddField(
            model_name='documentversion',
            name='branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='CLEAR.documentversionbranch'),
        ),
        migrations.AddField(
            model_name='documentversion',
            name='parent_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_versions', to='CLEAR.documentversion'),
        ),
        migrations.AddField(
            model_name='documentversion',
            name='merge_source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_versions', to='CLEAR.documentversion'),
        ),
        migrations.AddField(
            model_name='documentversion',
            name='checksum',
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
        # Add unique constraints
        migrations.AlterUniqueTogether(
            name='documentversionbranch',
            unique_together={('document', 'name')},
        ),
        migrations.AlterUniqueTogether(
            name='documentversiondiff',
            unique_together={('from_version', 'to_version')},
        ),
        migrations.AlterUniqueTogether(
            name='saveddocumentsearch',
            unique_together={('user', 'name')},
        ),
    ]