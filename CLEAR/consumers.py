"""
WebSocket consumers for CLEAR application.

Handles real-time communication for various features.

import asyncio
import json
import logging
from channels.db import database_sync_to_async
from channels.generic.websocket import AsyncWebsocketConsumer
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone
from .models import (
from .models.documents import Document
from .services.spatial_collaboration import SpatialCollaborationService
            from .models import Document




    Activity,
    ChatMessage,
    Conflict,
    Notification,
    Project,
    Task,
    User,
    UserPresence,
    WhisperMessage,
)

logger = logging.getLogger(__name__)


class BaseConsumer(AsyncWebsocketConsumer):
    """Base consumer with common functionality and heartbeat support"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.heartbeat_task = None
        self.last_heartbeat = None
        self.heartbeat_interval = 30  # seconds
        self.heartbeat_timeout = 60  # seconds
        self.connection_retries = 0
        self.max_retries = 3
    
    async def connect(self):
        """Handle WebSocket connection"""
        try:
            if self.scope["user"] == AnonymousUser():
                logger.warning("Anonymous user attempted WebSocket connection")
                await self.close()
                return
            
            await self.accept()
            self.last_heartbeat = timezone.now()
            
            # Start heartbeat task
            self.heartbeat_task = asyncio.create_task(self.heartbeat_loop())
            
            logger.info(f"WebSocket connected with heartbeat: {self.scope['user'].username} from {self.scope.get('client', ['Unknown', 'Unknown'])[0]}")
            
            # Log connection metrics
            self.connection_start_time = timezone.now()
            
        except Exception as e:
            logger.error(f"Error in WebSocket connect for user {getattr(self.scope.get('user'), 'username', 'Unknown')}: {e}")
            await self.close()
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        try:
            # Cancel heartbeat task
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # Calculate connection duration
            if hasattr(self, 'connection_start_time'):
                duration = (timezone.now() - self.connection_start_time).total_seconds()
                logger.info(f"WebSocket disconnected: {close_code} for user {self.scope['user'].username}, duration: {duration:.2f}s")
            else:
                logger.info(f"WebSocket disconnected: {close_code} for user {self.scope['user'].username}")
            
            # Log detailed disconnection info
            disconnect_reasons = {
                1000: "Normal closure",
                1001: "Going away",
                1002: "Protocol error",
                1003: "Unsupported data",
                1005: "No status received",
                1006: "Abnormal closure",
                1007: "Invalid frame payload data",
                1008: "Policy violation",
                1009: "Message too big",
                1010: "Mandatory extension",
                1011: "Internal server error",
                1015: "TLS handshake"
            }
            
            reason = disconnect_reasons.get(close_code, f"Unknown ({close_code})")
            
            if close_code != 1000:  # Not normal closure
                logger.warning(f"Abnormal WebSocket disconnect for {self.scope['user'].username}: {reason}")
                
        except Exception as e:
            logger.error(f"Error during WebSocket disconnect: {e}")
    
    async def heartbeat_loop(self):
        """Maintain connection with periodic heartbeat"""
        try:
            while True:
                await asyncio.sleep(self.heartbeat_interval)
                
                # Check if we've received a recent heartbeat response
                if self.last_heartbeat:
                    time_since_heartbeat = (timezone.now() - self.last_heartbeat).total_seconds()
                    if time_since_heartbeat > self.heartbeat_timeout:
                        logger.warning(f"Heartbeat timeout for user {self.scope['user'].username}")
                        await self.handle_heartbeat_timeout()
                        break
                
                # Send heartbeat ping
                await self.send_heartbeat_ping()
                
        except asyncio.CancelledError:
            logger.debug(f"Heartbeat loop cancelled for user {self.scope['user'].username}")
        except Exception as e:
            logger.error(f"Error in heartbeat loop for user {self.scope['user'].username}: {e}")
    
    async def send_heartbeat_ping(self):
        """Send heartbeat ping to client"""
        try:
            await self.send_json({
                'type': 'heartbeat_ping',
                'timestamp': timezone.now().isoformat(),
                'server_time': timezone.now().timestamp()
            })
        except Exception as e:
            logger.error(f"Failed to send heartbeat ping: {e}")
    
    async def handle_heartbeat_pong(self, data):
        """Handle heartbeat pong from client"""
        self.last_heartbeat = timezone.now()
        client_timestamp = data.get('client_timestamp')
        
        # Calculate round-trip time if client timestamp provided
        if client_timestamp:
            try:
                rtt = timezone.now().timestamp() - float(client_timestamp)
                logger.debug(f"Heartbeat RTT for {self.scope['user'].username}: {rtt:.3f}s")
            except (ValueError, TypeError):
                pass
    
    async def handle_heartbeat_timeout(self):
        """Handle heartbeat timeout - attempt reconnection"""
        logger.warning(f"Heartbeat timeout for user {self.scope['user'].username}, retries: {self.connection_retries}")
        
        if self.connection_retries < self.max_retries:
            self.connection_retries += 1
            await self.send_json({
                'type': 'connection_warning',
                'message': 'Connection unstable - attempting to maintain connection',
                'retry_count': self.connection_retries
            })
            # Reset heartbeat timer for another attempt
            self.last_heartbeat = timezone.now()
        else:
            logger.error(f"Max heartbeat retries exceeded for user {self.scope['user'].username}")
            await self.send_json({
                'type': 'connection_error',
                'message': 'Connection lost - please refresh the page',
                'retry_count': self.connection_retries
            })
            await self.close()
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages with heartbeat support"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            # Handle heartbeat pong
            if message_type == 'heartbeat_pong':
                await self.handle_heartbeat_pong(text_data_json)
                return
            
            # Handle ping for connection testing
            if message_type == 'ping':
                await self.send_json({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                })
                return
            
            # Call child class receive method for other message types
            await self.handle_message(text_data_json)
            
        except json.JSONDecodeError:
            await self.send_error("Invalid JSON")
        except Exception as e:
            logger.error(f"Error in BaseConsumer.receive: {e}")
            await self.send_error("Internal error")
    
    async def handle_message(self, data):
        """Override in child classes to handle specific message types"""
        pass
    
    async def send_json(self, content):
        """Send JSON message to WebSocket with enhanced error handling"""
        try:
            message_data = json.dumps(content)
            await self.send(text_data=message_data)
            
            # Track message metrics
            if not hasattr(self, 'messages_sent'):
                self.messages_sent = 0
            self.messages_sent += 1
            
        except json.JSONEncodeError as e:
            logger.error(f"JSON encoding error for user {self.scope['user'].username}: {e}")
            await self.send_error("Message encoding failed")
        except ConnectionResetError as e:
            logger.warning(f"Connection reset while sending to {self.scope['user'].username}: {e}")
            # Connection lost, let disconnect handler deal with it
        except ConnectionAbortedError as e:
            logger.warning(f"Connection aborted while sending to {self.scope['user'].username}: {e}")
            # Connection lost, let disconnect handler deal with it  
        except BrokenPipeError as e:
            logger.warning(f"Broken pipe while sending to {self.scope['user'].username}: {e}")
            # Connection lost, let disconnect handler deal with it
        except Exception as e:
            logger.error(f"Unexpected error sending message to {self.scope['user'].username}: {type(e).__name__}: {e}")
            # Don't re-raise to avoid disconnecting on send errors
    
    async def send_error(self, message):
        """Send error message"""
        await self.send_json({
            'type': 'error',
            'message': message,
            'timestamp': timezone.now().isoformat()
        })
    
    def get_connection_metrics(self):
        """Get connection health metrics"""
        metrics = {
            'user': self.scope['user'].username,
            'connected_at': getattr(self, 'connection_start_time', timezone.now()).isoformat(),
            'messages_sent': getattr(self, 'messages_sent', 0),
            'connection_retries': self.connection_retries,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            'heartbeat_active': self.heartbeat_task is not None and not self.heartbeat_task.done(),
        }
        
        if hasattr(self, 'connection_start_time'):
            metrics['connection_duration'] = (timezone.now() - self.connection_start_time).total_seconds()
        
        return metrics
    
    async def log_connection_health(self):
        """Log connection health for monitoring"""
        try:
            metrics = self.get_connection_metrics()
            logger.info(f"Connection health for {metrics['user']}: {metrics}")
        except Exception as e:
            logger.error(f"Error logging connection health: {e}")
    
    async def handle_connection_error(self, error_type, error_message):
        """Centralized connection error handling"""
        logger.error(f"Connection error for {self.scope['user'].username}: {error_type} - {error_message}")
        
        # Send error to client
        await self.send_json({
            'type': 'connection_error',
            'error_type': error_type,
            'message': error_message,
            'timestamp': timezone.now().isoformat(),
            'metrics': self.get_connection_metrics()
        })
        
        # Log metrics for debugging
        await self.log_connection_health()


class ProjectConsumer(BaseConsumer):
    """Handle project-specific real-time updates"""
    
    async def connect(self):
        self.project_id = self.scope['url_route']['kwargs']['project_id']
        self.room_group_name = f'project_{self.project_id}'
        
        # Check if user has access to this project
        has_access = await self.check_project_access()
        if not has_access:
            await self.close()
            return
        
        # Join project group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave project group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def handle_message(self, data):
        """Handle incoming WebSocket messages"""
        try:
            message_type = data.get('type')
            
            if message_type == 'project_update':
                await self.handle_project_update(data)
            elif message_type == 'user_activity':
                await self.handle_user_activity(data)
                
        except Exception as e:
            logger.error(f"Error in ProjectConsumer.handle_message: {e}")
            await self.send_error("Internal error")
    
    async def handle_project_update(self, data):
        """Handle project update events"""
        # Broadcast update to all project members
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'project_update_message',
                'data': data,
                'user': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_user_activity(self, data):
        """Handle user activity tracking"""
        activity = data.get('activity', '')
        await self.create_activity_record(activity)
        
        # Broadcast activity to project members
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_activity_message',
                'user': self.scope['user'].username,
                'activity': activity,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def project_update_message(self, event):
        """Send project update to WebSocket"""
        await self.send_json({
            'type': 'project_update',
            'data': event['data'],
            'user': event['user'],
            'timestamp': event['timestamp']
        })
    
    async def user_activity_message(self, event):
        """Send user activity to WebSocket"""
        await self.send_json({
            'type': 'user_activity',
            'user': event['user'],
            'activity': event['activity'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def check_project_access(self):
        """Check if user has access to the project"""
        try:
            project = Project.objects.get(id=self.project_id)
            user = self.scope['user']
            
            # Check if user is project manager or coordinator
            return (
                project.egis_project_manager == user.username or
                project.coordinator_id == str(user.id) or
                user.is_admin
            )
        except Project.DoesNotExist:
            return False
    
    @database_sync_to_async
    def create_activity_record(self, activity):
        """Create activity record in database"""
        try:
            project = Project.objects.get(id=self.project_id)
            Activity.objects.create(
                project=project,
                user=self.scope['user'],
                action=activity,
                timestamp=timezone.now()
            )
        except Exception as e:
            logger.error(f"Failed to create activity record: {e}")


class ChatConsumer(BaseConsumer):
    """Handle chat messages"""
    
    async def connect(self):
        self.channel_name_param = self.scope['url_route']['kwargs']['channel_name']
        self.room_group_name = f'chat_{self.channel_name_param}'
        
        # Join chat group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave chat group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def handle_message(self, data):
        """Handle incoming chat messages"""
        try:
            message = data.get('message', '')
            project_id = data.get('project_id')
            
            if not message.strip():
                await self.send_error("Message cannot be empty")
                return
            
            # Save message to database
            chat_message = await self.save_chat_message(message, project_id)
            
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'user': self.scope['user'].username,
                    'user_id': self.scope['user'].id,
                    'timestamp': chat_message.created_at.isoformat(),
                    'message_id': chat_message.id
                }
            )
            
        except Exception as e:
            logger.error(f"Error in ChatConsumer.handle_message: {e}")
            await self.send_error("Failed to send message")
    
    async def chat_message(self, event):
        """Send chat message to WebSocket"""
        await self.send_json({
            'type': 'chat_message',
            'message': event['message'],
            'user': event['user'],
            'user_id': event['user_id'],
            'timestamp': event['timestamp'],
            'message_id': event['message_id']
        })
    
    @database_sync_to_async
    def save_chat_message(self, message, project_id=None):
        """Save chat message to database"""
        project = None
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                pass
        
        return ChatMessage.objects.create(
            user=self.scope['user'],
            content=message,
            channel=self.channel_name_param,
            project=project
        )


class NotificationConsumer(BaseConsumer):
    """Handle user notifications"""
    
    async def connect(self):
        self.user_group_name = f'notifications_{self.scope["user"].id}'
        
        # Join user's notification group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Send recent unread notifications
        await self.send_recent_notifications()
    
    async def disconnect(self, close_code):
        # Leave notification group
        await self.channel_layer.group_discard(
            self.user_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def handle_message(self, data):
        """Handle notification actions"""
        try:
            action = data.get('action')
            notification_id = data.get('notification_id')
            
            if action == 'mark_read' and notification_id:
                await self.mark_notification_read(notification_id)
            elif action == 'mark_all_read':
                await self.mark_all_notifications_read()
                
        except Exception as e:
            logger.error(f"Error in NotificationConsumer.handle_message: {e}")
    
    async def notification_message(self, event):
        """Send notification to WebSocket"""
        await self.send_json({
            'type': 'notification',
            'notification': event['notification'],
            'timestamp': event['timestamp']
        })
        
        # Also send browser notification if supported
        await self.send_browser_notification(event['notification'])
    
    async def send_recent_notifications(self):
        """Send recent unread notifications"""
        notifications = await self.get_recent_notifications()
        for notification in notifications:
            await self.send_json({
                'type': 'notification',
                'notification': {
                    'id': str(notification.id),
                    'title': notification.title,
                    'message': notification.message,
                    'type': notification.notification_type,
                    'priority': notification.priority,
                    'action_url': notification.action_url,
                    'action_label': notification.action_label,
                    'created_at': notification.created_at.isoformat()
                }
            })
    
    @database_sync_to_async
    def get_recent_notifications(self):
        """Get recent unread notifications"""
        return list(
            self.scope['user'].notifications.filter(
                is_read=False
            ).order_by('-created_at')[:10]
        )
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """Mark single notification as read"""
        try:
            notification = self.scope['user'].notifications.get(id=notification_id)
            notification.mark_read()
        except Notification.DoesNotExist:
            pass
    
    @database_sync_to_async
    def mark_all_notifications_read(self):
        """Mark all notifications as read"""
        self.scope['user'].notifications.filter(is_read=False).update(
            is_read=True,
            read_at=timezone.now()
        )
    
    async def send_browser_notification(self, notification_data):
        """Send browser push notification"""
        await self.send_json({
            'type': 'browser_notification',
            'notification': notification_data,
            'show_toast': True  # Tell frontend to show toast notification
        })


class DashboardConsumer(BaseConsumer):
    """Handle dashboard real-time updates"""
    
    async def connect(self):
        self.user_group_name = f'dashboard_{self.scope["user"].id}'
        
        # Join user's dashboard group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Send initial dashboard data
        await self.send_dashboard_stats()
    
    async def disconnect(self, close_code):
        # Leave dashboard group
        await self.channel_layer.group_discard(
            self.user_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def dashboard_update(self, event):
        """Send dashboard update to WebSocket"""
        await self.send_json({
            'type': 'dashboard_update',
            'data': event['data'],
            'timestamp': event['timestamp']
        })
    
    async def send_dashboard_stats(self):
        """Send current dashboard statistics"""
        stats = await self.get_dashboard_stats()
        await self.send_json({
            'type': 'dashboard_stats',
            'stats': stats,
            'timestamp': timezone.now().isoformat()
        })
    
    @database_sync_to_async
    def get_dashboard_stats(self):
        """Get dashboard statistics"""
        user = self.scope['user']
        
        # Get user's projects
        projects = Project.objects.filter(
            egis_project_manager=user.username
        )
        
        return {
            'total_projects': projects.count(),
            'active_conflicts': Conflict.objects.filter(
                project__in=projects,
                status='open'
            ).count(),
            'pending_tasks': Task.objects.filter(
                assigned_to=user,
                completed=False
            ).count(),
            'unread_notifications': user.notifications.filter(read=False).count()
        }


class ConflictConsumer(BaseConsumer):
    """Handle conflict detection updates"""
    
    async def connect(self):
        self.project_id = self.scope['url_route']['kwargs']['project_id']
        self.room_group_name = f'conflicts_{self.project_id}'
        
        # Check project access
        has_access = await self.check_project_access()
        if not has_access:
            await self.close()
            return
        
        # Join conflict group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave conflict group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def conflict_update(self, event):
        """Send conflict update to WebSocket"""
        await self.send_json({
            'type': 'conflict_update',
            'conflict': event['conflict'],
            'action': event['action'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def check_project_access(self):
        """Check if user has access to the project"""
        try:
            project = Project.objects.get(id=self.project_id)
            user = self.scope['user']
            
            return (
                project.egis_project_manager == user.username or
                project.coordinator_id == str(user.id) or
                user.is_admin
            )
        except Project.DoesNotExist:
            return False


class TaskConsumer(BaseConsumer):
    """Handle task updates"""
    
    async def connect(self):
        self.project_id = self.scope['url_route']['kwargs']['project_id']
        self.room_group_name = f'tasks_{self.project_id}'
        
        # Check project access
        has_access = await self.check_project_access()
        if not has_access:
            await self.close()
            return
        
        # Join task group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave task group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def task_update(self, event):
        """Send task update to WebSocket"""
        await self.send_json({
            'type': 'task_update',
            'task': event['task'],
            'action': event['action'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def check_project_access(self):
        """Check if user has access to the project"""
        try:
            project = Project.objects.get(id=self.project_id)
            user = self.scope['user']
            
            return (
                project.egis_project_manager == user.username or
                project.coordinator_id == str(user.id) or
                user.is_admin
            )
        except Project.DoesNotExist:
            return False


class UtilityConsumer(BaseConsumer):
    """Handle utility coordination updates"""
    
    async def connect(self):
        self.project_id = self.scope['url_route']['kwargs']['project_id']
        self.room_group_name = f'utilities_{self.project_id}'
        
        # Check project access
        has_access = await self.check_project_access()
        if not has_access:
            await self.close()
            return
        
        # Join utility group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave utility group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def utility_update(self, event):
        """Send utility update to WebSocket"""
        await self.send_json({
            'type': 'utility_update',
            'utility': event['utility'],
            'action': event['action'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def check_project_access(self):
        """Check if user has access to the project"""
        try:
            project = Project.objects.get(id=self.project_id)
            user = self.scope['user']
            
            return (
                project.egis_project_manager == user.username or
                project.coordinator_id == str(user.id) or
                user.is_admin
            )
        except Project.DoesNotExist:
            return False


class GISConsumer(BaseConsumer):
    """Handle GIS layer updates"""
    
    async def connect(self):
        self.project_id = self.scope['url_route']['kwargs']['project_id']
        self.room_group_name = f'gis_{self.project_id}'
        
        # Check project access
        has_access = await self.check_project_access()
        if not has_access:
            await self.close()
            return
        
        # Join GIS group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave GIS group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def gis_update(self, event):
        """Send GIS update to WebSocket"""
        await self.send_json({
            'type': 'gis_update',
            'layer': event['layer'],
            'action': event['action'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def check_project_access(self):
        """Check if user has access to the project"""
        try:
            project = Project.objects.get(id=self.project_id)
            user = self.scope['user']
            
            return (
                project.egis_project_manager == user.username or
                project.coordinator_id == str(user.id) or
                user.is_admin
            )
        except Project.DoesNotExist:
            return False


class WhisperConsumer(BaseConsumer):
    """Handle ephemeral whisper messages"""
    
    async def connect(self):
        self.user_group_name = f'whispers_{self.scope["user"].id}'
        
        # Join user's whisper group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave whisper group
        await self.channel_layer.group_discard(
            self.user_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def handle_message(self, data):
        """Handle whisper message sending"""
        try:
            message = data.get('message', '')
            to_user_id = data.get('to_user_id')
            
            if not message.strip() or not to_user_id:
                await self.send_error("Message and recipient required")
                return
            
            # Save whisper message
            whisper = await self.save_whisper_message(message, to_user_id)
            
            if whisper:
                # Send to recipient
                await self.channel_layer.group_send(
                    f'whispers_{to_user_id}',
                    {
                        'type': 'whisper_message',
                        'whisper_id': str(whisper.id),
                        'message': message,
                        'from_user': self.scope['user'].username,
                        'timestamp': whisper.created_at.isoformat()
                    }
                )
                
        except Exception as e:
            logger.error(f"Error in WhisperConsumer.handle_message: {e}")
            await self.send_error("Failed to send whisper")
    
    async def whisper_message(self, event):
        """Send whisper message to WebSocket"""
        await self.send_json({
            'type': 'whisper',
            'whisper_id': event['whisper_id'],
            'message': event['message'],
            'from_user': event['from_user'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def save_whisper_message(self, message, to_user_id):
        """Save whisper message to database"""
        try:
            recipient = User.objects.get(id=to_user_id)
            
            return WhisperMessage.objects.create(
                sender=self.scope['user'],
                recipient=recipient,
                message=message
            )
        except User.DoesNotExist:
            return None


class SystemConsumer(BaseConsumer):
    """Handle system-wide updates (admin only)"""
    
    async def connect(self):
        # Only allow admin users
        if not (self.scope['user'].is_admin or self.scope['user'].is_superuser):
            await self.close()
            return
        
        self.room_group_name = 'system_updates'
        
        # Join system group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        # Leave system group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def system_update(self, event):
        """Send system update to WebSocket"""
        await self.send_json({
            'type': 'system_update',
            'update': event['update'],
            'timestamp': event['timestamp']
        })


class DocumentCollaborationConsumer(BaseConsumer):
    """Handle real-time document collaboration"""
    
    async def connect(self):
        self.document_id = self.scope['url_route']['kwargs']['document_id']
        self.room_group_name = f'document_{self.document_id}'
        
        # Check document access
        has_access = await self.check_document_access()
        if not has_access:
            await self.close()
            return
        
        # Join document collaboration group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Notify others of user presence
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_joined',
                'user_id': self.scope['user'].id,
                'username': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def disconnect(self, close_code):
        # Notify others of user leaving
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_left',
                'user_id': self.scope['user'].id,
                'username': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
        
        # Leave document group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def handle_message(self, data):
        """Handle incoming WebSocket messages"""
        try:
            message_type = data.get('type')
            
            if message_type == 'discussion_update':
                await self.handle_discussion_update(data)
            elif message_type == 'comment_update':
                await self.handle_comment_update(data)
            elif message_type == 'activity_update':
                await self.handle_activity_update(data)
            elif message_type == 'typing_status':
                await self.handle_typing_status(data)
            elif message_type == 'document_view':
                await self.handle_document_view(data)
                
        except Exception as e:
            logger.error(f"Error in DocumentCollaborationConsumer.handle_message: {e}")
            await self.send_error("Internal error")
    
    async def handle_discussion_update(self, data):
        """Handle discussion creation/update events"""
        discussion_id = data.get('discussion_id')
        action = data.get('action', 'update')  # create, update, resolve, message
        
        # Log activity
        await self.log_document_activity('discussion', f"Discussion {action}")
        
        # Broadcast to all document collaborators
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'discussion_updated',
                'discussion_id': discussion_id,
                'action': action,
                'user': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_comment_update(self, data):
        """Handle comment creation/update events"""
        comment_id = data.get('comment_id')
        action = data.get('action', 'created')
        
        await self.log_document_activity('comment', f"Comment {action}")
        
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'comment_updated',
                'comment_id': comment_id,
                'action': action,
                'user': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_activity_update(self, data):
        """Handle document activity events"""
        activity_type = data.get('activity_type')
        description = data.get('description', '')
        
        await self.log_document_activity(activity_type, description)
        
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'activity_updated',
                'activity_type': activity_type,
                'description': description,
                'user': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_typing_status(self, data):
        """Handle typing indicators"""
        is_typing = data.get('is_typing', False)
        location = data.get('location', '')  # discussion, comment, etc.
        
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status_update',
                'user_id': self.scope['user'].id,
                'username': self.scope['user'].username,
                'is_typing': is_typing,
                'location': location,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_document_view(self, data):
        """Handle document view tracking"""
        await self.log_document_activity('viewed', 'Document viewed')
        
        # Optional: broadcast view activity
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'document_viewed',
                'user': self.scope['user'].username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    # WebSocket message handlers
    async def user_joined(self, event):
        """Send user joined notification"""
        if event['user_id'] != self.scope['user'].id:  # Don't notify self
            await self.send_json({
                'type': 'user_joined',
                'user_id': event['user_id'],
                'username': event['username'],
                'timestamp': event['timestamp']
            })
    
    async def user_left(self, event):
        """Send user left notification"""
        if event['user_id'] != self.scope['user'].id:  # Don't notify self
            await self.send_json({
                'type': 'user_left',
                'user_id': event['user_id'],
                'username': event['username'],
                'timestamp': event['timestamp']
            })
    
    async def discussion_updated(self, event):
        """Send discussion update to WebSocket"""
        await self.send_json({
            'type': 'discussion_updated',
            'discussion_id': event['discussion_id'],
            'action': event['action'],
            'user': event['user'],
            'timestamp': event['timestamp']
        })
    
    async def comment_updated(self, event):
        """Send comment update to WebSocket"""
        await self.send_json({
            'type': 'comment_updated',
            'comment_id': event['comment_id'],
            'action': event['action'],
            'user': event['user'],
            'timestamp': event['timestamp']
        })
    
    async def activity_updated(self, event):
        """Send activity update to WebSocket"""
        await self.send_json({
            'type': 'activity_updated',
            'activity_type': event['activity_type'],
            'description': event['description'],
            'user': event['user'],
            'timestamp': event['timestamp']
        })
    
    async def typing_status_update(self, event):
        """Send typing status update"""
        if event['user_id'] != self.scope['user'].id:  # Don't notify self
            await self.send_json({
                'type': 'typing_status',
                'user_id': event['user_id'],
                'username': event['username'],
                'is_typing': event['is_typing'],
                'location': event['location'],
                'timestamp': event['timestamp']
            })
    
    async def document_viewed(self, event):
        """Send document view notification"""
        await self.send_json({
            'type': 'document_viewed',
            'user': event['user'],
            'timestamp': event['timestamp']
        })
    
    @database_sync_to_async
    def check_document_access(self):
        """Check if user has access to the document"""
        try:
            document = Document.objects.get(id=self.document_id)
            return document.can_user_access(self.scope['user'])
        except Document.DoesNotExist:
            return False
    
    @database_sync_to_async
    def log_document_activity(self, activity_type, description):
        """Log document activity to database"""
        try:
            document = Document.objects.get(id=self.document_id)
            document.log_activity(
                user=self.scope['user'],
                activity_type=activity_type,
                description=description
            )
        except Exception as e:
            logger.error(f"Failed to log document activity: {e}")


class SpatialCollaborationConsumer(BaseConsumer):
    """Handle real-time spatial collaboration for mapping interface"""
    
    async def connect(self):
        self.project_id = self.scope['url_route']['kwargs']['project_id']
        self.session_id = self.scope['url_route']['kwargs'].get('session_id', 'default')
        self.room_group_name = f'spatial_collaboration_{self.project_id}'
        
        # Check project access
        has_access = await self.check_project_access()
        if not has_access:
            await self.close()
            return
        
        # Join spatial collaboration group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Initialize collaboration service and join session
        collaboration_result = await self.join_collaboration_session()
        if collaboration_result['success']:
            # Send initial data to the user
            await self.send_initial_collaboration_data()
    
    async def disconnect(self, close_code):
        # Leave collaboration session
        await self.leave_collaboration_session()
        
        # Leave spatial collaboration group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def handle_message(self, data):
        """Handle incoming WebSocket messages for spatial collaboration"""
        try:
            message_type = data.get('type')
            
            if message_type == 'cursor_update':
                await self.handle_cursor_update(data)
            elif message_type == 'annotation_create':
                await self.handle_annotation_create(data)
            elif message_type == 'annotation_update':
                await self.handle_annotation_update(data)
            elif message_type == 'annotation_delete':
                await self.handle_annotation_delete(data)
            elif message_type == 'drawing_create':
                await self.handle_drawing_create(data)
            elif message_type == 'drawing_update':
                await self.handle_drawing_update(data)
            elif message_type == 'tool_change':
                await self.handle_tool_change(data)
                
        except Exception as e:
            logger.error(f"Error in SpatialCollaborationConsumer.handle_message: {e}")
            await self.send_error("Internal error")
    
    async def handle_cursor_update(self, data):
        """Handle real-time cursor position updates"""
        lng = data.get('lng')
        lat = data.get('lat')
        
        if lng is None or lat is None:
            await self.send_error("Invalid cursor coordinates")
            return
        
        # Update cursor position via service
        result = await self.update_cursor_position(lng, lat)
        
        if result['success']:
            # Broadcast cursor update to other users (throttled by service)
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'cursor_update',
                    'message': {
                        'type': 'cursor_update',
                        'user_id': self.scope['user'].id,
                        'username': self.scope['user'].username,
                        'coordinates': [lng, lat],
                        'session_id': self.session_id,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )
    
    async def handle_annotation_create(self, data):
        """Handle creation of spatial annotations"""
        geometry_data = data.get('geometry')
        annotation_data = data.get('annotation', {})
        
        if not geometry_data:
            await self.send_error("Geometry data required for annotation")
            return
        
        # Create annotation via service
        result = await self.create_spatial_annotation(geometry_data, annotation_data)
        
        if result['success']:
            # Send confirmation to creator
            await self.send_json({
                'type': 'annotation_created',
                'annotation_id': result['annotation_id'],
                'message': result['message']
            })
        else:
            await self.send_error(result['error'])
    
    async def handle_annotation_update(self, data):
        """Handle annotation updates"""
        annotation_id = data.get('annotation_id')
        updates = data.get('updates', {})
        
        if not annotation_id:
            await self.send_error("Annotation ID required")
            return
        
        # Update annotation via service
        result = await self.update_spatial_annotation(annotation_id, updates)
        
        if result['success']:
            await self.send_json({
                'type': 'annotation_updated',
                'annotation_id': annotation_id,
                'message': result['message']
            })
        else:
            await self.send_error(result['error'])
    
    async def handle_annotation_delete(self, data):
        """Handle annotation deletion"""
        annotation_id = data.get('annotation_id')
        
        if not annotation_id:
            await self.send_error("Annotation ID required")
            return
        
        # Delete annotation via service
        result = await self.delete_spatial_annotation(annotation_id)
        
        if result['success']:
            await self.send_json({
                'type': 'annotation_deleted',
                'annotation_id': annotation_id,
                'message': result['message']
            })
        else:
            await self.send_error(result['error'])
    
    async def handle_drawing_create(self, data):
        """Handle collaborative drawing creation"""
        drawing_data = data.get('drawing', {})
        
        if not drawing_data.get('geometry'):
            await self.send_error("Drawing geometry required")
            return
        
        # Create drawing via service
        result = await self.create_collaborative_drawing(drawing_data)
        
        if result['success']:
            await self.send_json({
                'type': 'drawing_created',
                'drawing_id': result['drawing_id'],
                'message': result['message']
            })
        else:
            await self.send_error(result['error'])
    
    async def handle_drawing_update(self, data):
        """Handle collaborative drawing updates"""
        drawing_id = data.get('drawing_id')
        updates = data.get('updates', {})
        
        if not drawing_id:
            await self.send_error("Drawing ID required")
            return
        
        # Note: Drawing updates would be handled by the service
        # For now, broadcast the update
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'drawing_update',
                'message': {
                    'type': 'drawing_updated',
                    'drawing_id': drawing_id,
                    'updates': updates,
                    'user_id': self.scope['user'].id,
                    'username': self.scope['user'].username,
                    'timestamp': timezone.now().isoformat()
                }
            }
        )
    
    async def handle_tool_change(self, data):
        """Handle user tool selection changes"""
        tool = data.get('tool', '')
        
        # Update user presence with current tool
        await self.update_user_tool(tool)
        
        # Broadcast tool change
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'tool_change',
                'message': {
                    'type': 'tool_change',
                    'user_id': self.scope['user'].id,
                    'username': self.scope['user'].username,
                    'tool': tool,
                    'timestamp': timezone.now().isoformat()
                }
            }
        )
    
    
    # WebSocket message type handlers for group broadcasts
    async def presence_update(self, event):
        """Send presence update to WebSocket"""
        await self.send_json(event['message'])
    
    async def cursor_update(self, event):
        """Send cursor update to WebSocket"""
        # Don't send cursor updates to the user who moved the cursor
        if event['message']['user_id'] != self.scope['user'].id:
            await self.send_json(event['message'])
    
    async def annotation_update(self, event):
        """Send annotation update to WebSocket"""
        await self.send_json(event['message'])
    
    async def drawing_update(self, event):
        """Send drawing update to WebSocket"""
        await self.send_json(event['message'])
    
    async def tool_change(self, event):
        """Send tool change update to WebSocket"""
        # Don't send tool change to the user who changed tools
        if event['message']['user_id'] != self.scope['user'].id:
            await self.send_json(event['message'])
    
    # Database operations (async wrappers)
    @database_sync_to_async
    def check_project_access(self):
        """Check if user has access to the project"""
        try:
            project = Project.objects.get(id=self.project_id)
            user = self.scope['user']
            
            return (
                project.egis_project_manager == user.username or
                project.coordinator_id == str(user.id) or
                user.is_admin
            )
        except Project.DoesNotExist:
            return False
    
    @database_sync_to_async
    def join_collaboration_session(self):
        """Join collaboration session via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.join_collaboration(self.session_id)
        except Exception as e:
            logger.error(f"Error joining collaboration session: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def leave_collaboration_session(self):
        """Leave collaboration session via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.leave_collaboration(self.session_id)
        except Exception as e:
            logger.error(f"Error leaving collaboration session: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def update_cursor_position(self, lng, lat):
        """Update cursor position via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.update_cursor_position(lng, lat, self.session_id)
        except Exception as e:
            logger.error(f"Error updating cursor position: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def create_spatial_annotation(self, geometry_data, annotation_data):
        """Create spatial annotation via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.create_annotation(geometry_data, annotation_data)
        except Exception as e:
            logger.error(f"Error creating annotation: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def update_spatial_annotation(self, annotation_id, updates):
        """Update spatial annotation via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.update_annotation(annotation_id, updates)
        except Exception as e:
            logger.error(f"Error updating annotation: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def delete_spatial_annotation(self, annotation_id):
        """Delete spatial annotation via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.delete_annotation(annotation_id)
        except Exception as e:
            logger.error(f"Error deleting annotation: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def create_collaborative_drawing(self, drawing_data):
        """Create collaborative drawing via service"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return service.create_collaborative_drawing(drawing_data)
        except Exception as e:
            logger.error(f"Error creating drawing: {e}")
            return {'success': False, 'error': str(e)}
    
    @database_sync_to_async
    def update_user_tool(self, tool):
        """Update user's current tool in presence"""
        try:
            UserPresence.objects.filter(
                project_id=self.project_id,
                user=self.scope['user'],
                session_id=self.session_id,
                is_active=True
            ).update(
                current_tool=tool,
                last_seen=timezone.now()
            )
            return True
        except Exception as e:
            logger.error(f"Error updating user tool: {e}")
            return False
    
    @database_sync_to_async
    def get_initial_collaboration_data(self):
        """Get initial collaboration data for the user"""
        
        try:
            service = SpatialCollaborationService(self.project_id, self.scope['user'])
            return {
                'active_collaborators': service.get_active_collaborators(),
                'project_annotations': service.get_project_annotations(),
                'session_id': self.session_id
            }
        except Exception as e:
            logger.error(f"Error getting initial collaboration data: {e}")
            return {}
    
    async def send_initial_collaboration_data(self):
        """Send initial collaboration data to the user"""
        initial_data = await self.get_initial_collaboration_data()
        
        await self.send_json({
            'type': 'collaboration_initialized',
            'data': initial_data,
            'timestamp': timezone.now().isoformat()
        })