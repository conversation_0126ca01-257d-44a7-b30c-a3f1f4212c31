"""
Spatial and GIS-related models for CLEAR application.

This module contains all models related to spatial data, GIS operations,
coordinate systems, utilities, conflicts, and collaborative mapping features.
"""

import uuid
from django.contrib.gis.db import models
from django.utils import timezone
from .models import Comment, Organization, Project, User
        from .models import UserPresence  # Import here to avoid circular imports

"""



# Import base models that spatial models depend on


class CoordinateSystem(models.Model):
    """Coordinate systems for spatial data"""
    name = models.CharField(max_length=255)
    srid = models.IntegerField()
    type = models.CharField(max_length=50)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    state = models.Char<PERSON>ield(max_length=50, default="Indiana")
    zone_name = models.CharField(max_length=100, blank=True, null=True)
    county_code = models.CharField(max_length=10, blank=True, null=True)
    projection_group = models.Char<PERSON><PERSON>(max_length=50, blank=True, null=True)
    lat_origin_deg = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    lat_origin_min = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    lat_origin_sec = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    central_meridian_deg = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    central_meridian_min = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    central_meridian_sec = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    central_meridian_scale = models.DecimalField(max_digits=10, decimal_places=6, blank=True, null=True)
    false_easting = models.DecimalField(max_digits=15, decimal_places=3, blank=True, null=True)
    false_northing = models.DecimalField(max_digits=15, decimal_places=3, blank=True, null=True)
    validation_point_easting = models.DecimalField(max_digits=15, decimal_places=3, blank=True, null=True)
    validation_point_northing = models.DecimalField(max_digits=15, decimal_places=3, blank=True, null=True)
    indiana_2digit_county = models.CharField(max_length=5, blank=True, null=True)
    projection_group_abbreviation = models.CharField(max_length=10, blank=True, null=True)

    def __str__(self):
        return f"{self.name} (SRID: {self.srid})"


class Utility(models.Model):
    """Utility companies and infrastructure"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='utilities')
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=100)
    status = models.CharField(max_length=50, default="active")
    contact_name = models.CharField(max_length=255, blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    last_response = models.DateField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    initiation_date = models.DateField(blank=True, null=True)
    depth_notes = models.TextField(blank=True, null=True)
    installation_depth = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    vertical_clearance = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    vertical_datum = models.CharField(max_length=50, blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.type})"
    
    def get_comment_count(self):
        """Get total number of comments for this utility"""
        return Comment.objects.filter(
            commentable_type='utility',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).count()
    
    def get_recent_comments(self, limit=5):
        """Get recent comments for this utility"""
        return Comment.objects.filter(
            commentable_type='utility',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).select_related('user').order_by('-created_at')[:limit]


class LineStyle(models.Model):
    """CAD line styles for utilities"""
    utility_type = models.CharField(max_length=50)
    installation_type = models.CharField(max_length=50)
    line_color = models.CharField(max_length=7)
    line_weight = models.FloatField()
    line_pattern = models.CharField(max_length=100)
    letter_symbols = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    color_name = models.CharField(max_length=50, blank=True, null=True)
    dash_pattern = models.CharField(max_length=100, blank=True, null=True)
    letter_spacing = models.IntegerField(blank=True, null=True)
    standard_811 = models.BooleanField(default=True)

    class Meta:
        unique_together = ['utility_type', 'installation_type']

    def __str__(self):
        return f"{self.utility_type} - {self.installation_type}"


class UtilityLineData(models.Model):
    """Spatial data for utility lines"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    utility = models.ForeignKey(Utility, on_delete=models.CASCADE, blank=True, null=True, related_name='line_data')
    name = models.CharField(max_length=255)
    line_type = models.CharField(max_length=100)
    utility_type = models.CharField(max_length=100)
    installation_type = models.CharField(max_length=100)
    coordinate_system = models.ForeignKey(CoordinateSystem, on_delete=models.CASCADE)
    geometry = models.GeometryField(geography=True, blank=True, null=True)
    properties = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    line_style = models.ForeignKey(LineStyle, on_delete=models.SET_NULL, blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.utility_type})"


class Conflict(models.Model):
    """Utility conflicts and issues"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='conflicts')
    utility = models.ForeignKey(Utility, on_delete=models.CASCADE, blank=True, null=True, related_name='conflicts')
    utility2 = models.ForeignKey(Utility, on_delete=models.CASCADE, blank=True, null=True, related_name='conflicts2')
    description = models.TextField()
    status = models.CharField(max_length=50, default="open")
    priority = models.CharField(max_length=20)
    location = models.CharField(max_length=255, blank=True, null=True)
    resolution_notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confidence_score = models.IntegerField(blank=True, null=True)
    conflict_3d_geometry = models.GeometryField(geography=True, blank=True, null=True)
    conflict_elevation = models.DecimalField(max_digits=10, decimal_places=3, blank=True, null=True)
    conflict_type = models.CharField(max_length=100, blank=True, null=True)
    detected_timestamp = models.DateTimeField(blank=True, null=True)
    detection_method = models.CharField(max_length=50, default="manual")
    impact_score = models.IntegerField(blank=True, null=True)
    is_vertical_conflict = models.BooleanField(default=False)
    likelihood_score = models.IntegerField(blank=True, null=True)
    reviewed_by = models.CharField(max_length=255, blank=True, null=True)
    reviewed_timestamp = models.DateTimeField(blank=True, null=True)
    risk_score = models.IntegerField(blank=True, null=True)
    vertical_clearance_violation = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)

    def __str__(self):
        return f"Conflict: {self.description[:50]}..."
    
    def get_comment_count(self):
        """Get total number of comments for this conflict"""
        return Comment.objects.filter(
            commentable_type='conflict',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).count()
    
    def get_recent_comments(self, limit=5):
        """Get recent comments for this conflict"""
        return Comment.objects.filter(
            commentable_type='conflict',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).select_related('user').order_by('-created_at')[:limit]


class UtilityPhaseStatus(models.Model):
    """Status tracking for utility phases"""
    utility = models.ForeignKey(Utility, on_delete=models.CASCADE, related_name='phase_statuses')
    phase_name = models.CharField(max_length=255)
    status = models.CharField(max_length=50)
    status_date = models.DateField()
    notes = models.TextField(blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='utility_status_updates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['utility', 'phase_name']
        indexes = [
            models.Index(fields=['utility', 'phase_name']),
            models.Index(fields=['status_date']),
        ]
    
    def __str__(self):
        return f"{self.utility.name} - {self.phase_name}: {self.status}"


class GISLayer(models.Model):
    """GIS layer management"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    layer_type = models.CharField(max_length=50)  # utility, base, analysis, etc.
    data_source = models.CharField(max_length=500)
    style_config = models.JSONField(default=dict)
    visibility = models.BooleanField(default=True)
    opacity = models.FloatField(default=1.0)
    z_index = models.IntegerField(default=0)
    is_public = models.BooleanField(default=False)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='gis_layers')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_layers')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['z_index', 'name']
    
    def __str__(self):
        return self.name


class SpatialAnnotation(models.Model):
    """Collaborative spatial annotations for mapping"""
    
    ANNOTATION_TYPES = [
        ('note', 'Note'),
        ('warning', 'Warning'),
        ('conflict', 'Conflict'),
        ('measurement', 'Measurement'),
        ('markup', 'Markup'),
    ]
    
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='spatial_annotations')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='spatial_annotations')
    annotation_text = models.TextField(blank=True, null=True)
    annotation_type = models.CharField(max_length=50, choices=ANNOTATION_TYPES, default='note')
    color = models.CharField(max_length=7, default='#ff0000')  # Hex color
    geometry = models.GeometryField(srid=4326)
    is_visible = models.BooleanField(default=True)
    is_persistent = models.BooleanField(default=True)  # Temporary vs permanent annotations
    metadata = models.JSONField(default=dict)  # Additional properties
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        db_table = 'spatial_annotations'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project', 'user']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.annotation_type.title()} by {self.user.username} on {self.project.name}"
    
    def soft_delete(self):
        """Soft delete annotation"""
        self.deleted_at = timezone.now()
        self.save()


class CollaborativeDrawing(models.Model):
    """Collaborative drawing elements"""
    
    DRAWING_TYPES = [
        ('line', 'Line'),
        ('polygon', 'Polygon'),
        ('point', 'Point'),
        ('circle', 'Circle'),
        ('rectangle', 'Rectangle'),
        ('freehand', 'Freehand'),
    ]
    
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='collaborative_drawings')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='drawings')
    annotation = models.ForeignKey(SpatialAnnotation, on_delete=models.CASCADE, 
                                 related_name='drawings', blank=True, null=True)
    drawing_type = models.CharField(max_length=20, choices=DRAWING_TYPES)
    geometry = models.GeometryField(srid=4326)
    style = models.JSONField(default=dict)  # Stroke, fill, opacity, etc.
    is_temporary = models.BooleanField(default=False)  # Temporary drawing vs saved
    is_locked = models.BooleanField(default=False)  # Prevent editing by others
    version = models.IntegerField(default=1)  # Version control for conflict resolution
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        db_table = 'collaborative_drawings'
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['project', 'is_temporary']),
        ]
    
    def __str__(self):
        return f"{self.drawing_type.title()} by {self.user.username}"
    
    def soft_delete(self):
        """Soft delete drawing"""
        self.deleted_at = timezone.now()
        self.save()
    
    @property
    def is_deleted(self):
        return self.deleted_at is not None


class CollaborationSession(models.Model):
    """Manage collaborative mapping sessions"""
    
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='collaboration_sessions')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_collaboration_sessions')
    participants = models.ManyToManyField(User, related_name='collaboration_sessions')
    session_name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_public = models.BooleanField(default=False)  # Public vs private sessions
    max_participants = models.IntegerField(default=10)
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(blank=True, null=True)
    settings = models.JSONField(default=dict)  # Session-specific settings
    
    class Meta:
        db_table = 'collaboration_sessions'
        ordering = ['-started_at']
    
    def __str__(self):
        return f"Collaboration: {self.session_name}"
    
    def end_session(self):
        """End the collaboration session"""
        self.is_active = False
        self.ended_at = timezone.now()
        self.save()
    
    @property
    def active_participants(self):
        """Get currently active participants"""
        return UserPresence.get_active_users(self.project).filter(
            user__in=self.participants.all()
        )
"""