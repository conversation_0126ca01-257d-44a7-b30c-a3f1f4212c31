"""
Task Management Views

This module contains views related to task management, task boards, workflows, 
and task-related HTMX endpoints. Extracted from the main views.py file for better 
organization and maintainability.

Contains:
- Class-based views for task CRUD operations
- Kanban board and data grid views
- HTMX endpoints for dynamic task interactions
- Timeline and workflow management views

from datetime import datetime, timedelta
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, DetailView, ListView, TemplateView
from ..models import Project, Task, User
from ..models.financial import TimeEntry
    from datetime import timedelta
    import csv
    from django.http import HttpResponse


# Django imports


# Local imports

# ========== CLASS-BASED TASK VIEWS ==========

class TaskListView(LoginRequiredMixin, ListView):
    """List tasks for a project"""
    model = Task
    template_name = 'CLEAR/tasks/list.html'
    context_object_name = 'tasks'

    def get_queryset(self):
        self.project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        return self.project.tasks.order_by('due_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['project'] = self.project
        return context


class TaskDetailView(LoginRequiredMixin, DetailView):
    """Detailed task view"""
    model = Task
    template_name = 'CLEAR/tasks/detail.html'
    context_object_name = 'task'


class TaskCreateView(LoginRequiredMixin, CreateView):
    """Create new task for a project"""
    model = Task
    template_name = 'CLEAR/tasks/create.html'
    fields = ['title', 'description', 'priority', 'due_date', 'assigned_to']

    def form_valid(self, form):
        project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        form.instance.project = project
        return super().form_valid(form)


class TaskUpdateView(LoginRequiredMixin, TemplateView):
    """Update task view (alias for compatibility)"""
    template_name = 'CLEAR/tasks/update.html'
    
def task_update_htmx(request, task_id):
    """Update a single task field via HTMX"""
    try:
        task = get_object_or_404(Task, id=task_id)
        field = request.POST.get('field')
        
        if field == 'title':
            task.title = request.POST.get('title', task.title)
        elif field == 'status':
            task.status = request.POST.get('status', task.status)
        elif field == 'priority':
            task.priority = request.POST.get('priority', task.priority)
        elif field == 'start_date':
            start_date = request.POST.get('start_date')
            task.start_date = start_date if start_date else None
        elif field == 'end_date':
            end_date = request.POST.get('end_date')
            task.end_date = end_date if end_date else None
        elif field == 'due_date':
            due_date = request.POST.get('due_date')
            task.due_date = due_date if due_date else None
        elif field == 'progress_percentage':
            progress = request.POST.get('progress_percentage', 0)
            task.progress_percentage = int(progress) if progress.isdigit() else 0
        elif field == 'estimated_hours':
            hours = request.POST.get('estimated_hours')
            task.estimated_hours = float(hours) if hours else None
        elif field == 'actual_hours':
            hours = request.POST.get('actual_hours')
            task.actual_hours = float(hours) if hours else None
        elif field == 'phase':
            task.phase = request.POST.get('phase', task.phase)
        elif field == 'task_type':
            task.task_type = request.POST.get('task_type', task.task_type)
        elif field == 'completed':
            task.completed = request.POST.get('completed') == 'on'
            
        task.save()
        
        # Return success response (HTMX expects 200)
        return HttpResponse(status=200)
        
    except Exception as e:
        return HttpResponse(f"Error: {str(e)}", status=400)


@require_http_methods(["POST"])
@login_required
def task_list_htmx(request):
    """Filter tasks and return updated table body"""
    # Get filter parameters
    status = request.POST.get('status_filter')
    priority = request.POST.get('priority_filter')
    assignee = request.POST.get('assignee_filter')
    project = request.POST.get('project_filter')
    
    # Build queryset with filters
    queryset = Task.objects.select_related('project', 'assigned_to')
    
    if status:
        queryset = queryset.filter(status=status)
    if priority:
        queryset = queryset.filter(priority=priority)
    if assignee:
        queryset = queryset.filter(assigned_to_id=assignee)
    if project:
        queryset = queryset.filter(project_id=project)
    
    tasks = queryset.order_by('-created_at')
    
    # Return partial template with just the table body
    return render(request, 'components/task_datagrid_body.html', {'tasks': tasks})


@require_http_methods(["POST"])
@login_required
def task_create_htmx(request):
    """Create task via HTMX"""
    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description')
        project_id = request.POST.get('project_id')

        if title and project_id:
            project = get_object_or_404(Project, pk=project_id)
            task = Task.objects.create(
                title=title,
                description=description,
                project=project,
                assigned_to=request.user,
                created_by=request.user
            )

            return render(request, 'partials/task_item.html', {
                'task': task
            })

    return HttpResponse('Error creating task', status=400)


@require_http_methods(["POST"])
@login_required
def task_complete_htmx(request, task_id):
    """Update task status via HTMX"""
    task = get_object_or_404(Task, pk=task_id)
    new_status = request.POST.get('status')

    if new_status:
        task.status = new_status
        if new_status == 'Completed':
            task.completed = True
        task.save()

        return render(request, 'partials/task_item.html', {
            'task': task
        })

    return HttpResponse('Error updating task', status=400)


@require_http_methods(["DELETE"])
@login_required
def task_delete_htmx(request, task_id):
    """Delete task via HTMX"""
    try:
        task = get_object_or_404(Task, id=task_id)
        task.delete()
        return HttpResponse(status=200)
    except Exception as e:
        return HttpResponse(f"Error: {str(e)}", status=400)


@require_http_methods(["POST"])
@login_required
def tasks_sort(request):
    """Sort tasks and return updated table body"""
    sort_by = request.POST.get('sort_by', 'created_at')
    current_order = request.session.get(f'task_sort_{sort_by}', 'desc')
    
    # Toggle sort order
    new_order = 'asc' if current_order == 'desc' else 'desc'
    request.session[f'task_sort_{sort_by}'] = new_order
    
    # Build queryset with sorting
    queryset = Task.objects.select_related('project', 'assigned_to')
    
    if new_order == 'asc':
        queryset = queryset.order_by(sort_by)
    else:
        queryset = queryset.order_by(f'-{sort_by}')
    
    tasks = queryset
    
    # Return partial template with just the table body
    return render(request, 'components/task_datagrid_body.html', {'tasks': tasks})


@require_http_methods(["POST"])
@login_required
def quick_task_create(request):
    """Quick task creation (HTMX endpoint)"""
    title = request.POST.get('title')
    project_id = request.POST.get('project_id')
    priority = request.POST.get('priority', 'Medium')

    if title and project_id:
        project = get_object_or_404(Project, pk=project_id)
        task = Task.objects.create(
            title=title,
            priority=priority,
            project=project,
            assigned_to=request.user,
            created_by=request.user
        )

        return render(request, 'partials/task_item.html', {
            'task': task,
            'show_project': True
        })

    return HttpResponse('Error creating task', status=400)


@login_required
def tasks_list_partial(request):
    """Render tasks list component for dashboard"""
    user = request.user
    show_completed = request.GET.get('show_completed', 'false').lower() == 'true'

    # Get user's tasks
    tasks = Task.objects.filter(assigned_to=user).select_related('project')

    if not show_completed:
        tasks = tasks.filter(completed=False)

    tasks = tasks.order_by('completed', '-priority', 'due_date')

    # Process tasks for display
    processed_tasks = []
    for task in tasks:
        is_overdue = False
        if task.due_date and not task.completed:
            is_overdue = task.due_date < timezone.now().date()

        processed_tasks.append({
            'id': task.id,
            'title': task.title,
            'description': task.description,
            'priority': task.priority,
            'due_date': task.due_date,
            'is_overdue': is_overdue,
            'completed': task.completed,
            'project_name': task.project.name if task.project else 'No Project',
            'assigned_to_initials': user.get_initials(),
            'comment_count': task.comments.count() if hasattr(task, 'comments') else 0,
        })

    pending_tasks = [t for t in processed_tasks if not t['completed']]
    completed_tasks = [t for t in processed_tasks if t['completed']]

    context = {
        'tasks': processed_tasks,
        'pending_tasks': pending_tasks,
        'completed_tasks': completed_tasks,
        'pending_count': len(pending_tasks),
        'completed_count': len(completed_tasks),
        'show_completed': show_completed,
    }

    return render(request, 'components/tasks_list_partial.html', context)


# ========== TIMELINE AND WORKFLOW VIEWS ==========

@require_http_methods(["POST"])
def task_timeline_update_htmx(request):
    """HTMX endpoint for updating task timeline data"""
    task_id = request.POST.get('task_id')
    field = request.POST.get('field')
    value = request.POST.get('value')
    
    try:
        task = get_object_or_404(Task, id=task_id)
        
        # Update the specific field
        if field == 'start_date':
            task.start_date = datetime.strptime(value, '%Y-%m-%d').date() if value else None
        elif field == 'end_date':
            task.end_date = datetime.strptime(value, '%Y-%m-%d').date() if value else None
        elif field == 'progress_percentage':
            task.progress_percentage = int(value) if value else 0
        elif field == 'duration_days':
            task.duration_days = int(value) if value else None
        elif field == 'milestone':
            task.milestone = value.lower() == 'true'
        elif field == 'assigned_to':
            if value:
                user = get_object_or_404(User, id=value)
                task.assigned_to = user
            else:
                task.assigned_to = None
        
        task.save()
        
        # Return updated task row
        context = {'task': task, 'project': task.project}
        return render(request, 'components/timeline/task_bar.html', context)
        
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=400)


@require_http_methods(["POST"])
def timeline_task_move_htmx(request):
    """HTMX endpoint for drag-and-drop task date changes"""
    task_id = request.POST.get('task_id')
    new_start = request.POST.get('new_start')
    new_end = request.POST.get('new_end')
    
    try:
        task = get_object_or_404(Task, id=task_id)
        
        if new_start:
            task.start_date = datetime.strptime(new_start, '%Y-%m-%d').date()
        if new_end:
            task.end_date = datetime.strptime(new_end, '%Y-%m-%d').date()
            
        task.save()
        
        # Return updated task representation
        context = {'task': task, 'project': task.project}
        return render(request, 'components/timeline/task_bar.html', context)
        
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=400)


# ========== TIMELINE AND PROJECT MANAGEMENT VIEWS ==========

class ProjectTimelineView(LoginRequiredMixin, TemplateView):
    """Project timeline and Gantt chart view"""
    template_name = 'CLEAR/projects/timeline.html'
    
def update_time_entry(request):
    """Update time entry via HTMX"""
    if request.method == 'POST':
        request.POST.get('entry_id')
        request.POST.get('hours')
        request.POST.get('description')
        
        try:
            # Basic stub implementation
            return JsonResponse({
                'success': True,
                'message': 'Time entry updated'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request'})


@login_required
def timesheet_week_summary(request):
    """Get timesheet summary for the week"""
    try:
        # Calculate week totals
        week_hours = 40.0  # Stub value
        
        context = {
            'week_hours': week_hours,
            'target_hours': 40.0,
            'entries': [],
        }
        
        return render(request, 'components/timesheet_week_summary.html', context)
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
def timesheet_summary_partial(request):
    """Timesheet summary partial for dashboard"""
    try:
        
        # Get current week data
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        
        context = {
            'week_start': week_start,
            'total_hours': 0,
            'target_hours': 40,
            'projects_worked': [],
            'recent_entries': [],
        }
        
        return render(request, 'components/timesheet_summary_partial.html', context)
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


# ========== TIMELINE HTMX ENDPOINTS ==========

@login_required
def timeline_data_htmx(request, project_id):
    """Get timeline data for project"""
    try:
        project = get_object_or_404(Project, id=project_id)
        tasks = project.tasks.all()
        
        context = {
            'project': project,
            'tasks': tasks,
        }
        
        return render(request, 'components/timeline/timeline_data.html', context)
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
def dependency_management_htmx(request):
    """Manage task dependencies"""
    try:
        return render(request, 'components/timeline/dependency_management.html', {})
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
def timeline_metrics_htmx(request, project_id):
    """Get timeline metrics for project"""
    try:
        project = get_object_or_404(Project, id=project_id)
        
        context = {
            'project': project,
            'completion_percentage': 0,
            'on_track': True,
            'critical_path_length': 0,
        }
        
        return render(request, 'components/timeline/timeline_metrics.html', context)
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
def calculate_critical_path_htmx(request, project_id):
    """Calculate and display critical path"""
    try:
        project = get_object_or_404(Project, id=project_id)
        
        context = {
            'project': project,
            'critical_path': [],
        }
        
        return render(request, 'components/timeline/critical_path.html', context)
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
def timeline_export_htmx(request, project_id):
    """Export timeline data"""
    try:
        project = get_object_or_404(Project, id=project_id)
        
        # Return export options
        context = {
            'project': project,
        }
        
        return render(request, 'components/timeline/export_options.html', context)
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


# ========== LEGACY COMPATIBILITY ALIASES ==========

# Maintain compatibility with existing URL patterns
task_update = task_update_htmx
tasks_filter = task_list_htmx
create_task_htmx = task_create_htmx
update_task_status_htmx = task_complete_htmx


# ========== TIMESHEET VIEWS ==========

@login_required
def timesheet_view(request):
    """Main timesheet interface"""
    # Get users time entries for current week
    
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    
    try:
        time_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=week_start,
            start_time__date__lte=week_end
        ).select_related("project").order_by("start_time")
    except Exception:
        time_entries = []
    
    # Get users projects for dropdown
    user_projects = Project.objects.filter(
        Q(manager=request.user) |
        Q(coordinator_id=str(request.user.id)) |
        Q(egis_project_manager=request.user.username)
    )[:20]
    
    context = {
        "time_entries": time_entries,
        "user_projects": user_projects,
        "week_start": week_start,
        "week_end": week_end,
    }
    
    return render(request, "CLEAR/timesheet.html", context)


@login_required
@require_http_methods(["POST"])
def save_timesheet(request):
    """Save timesheet entries"""
    try:
        project_id = request.POST.get("project_id")
        hours = float(request.POST.get("hours", 0))
        description = request.POST.get("description", "")
        request.POST.get("date")
        
        if not project_id or hours <= 0:
            return JsonResponse({"error": "Project and hours are required"}, status=400)
        
        project = Project.objects.get(id=project_id)
        
        # Create time entry
        time_entry = TimeEntry.objects.create(
            user=request.user,
            project=project,
            duration_minutes=hours * 60,
            description=description,
            start_time=timezone.now(),
            billable=True
        )
        
        return JsonResponse({
            "success": True,
            "entry_id": time_entry.id,
            "message": "Time entry saved successfully"
        })
        
    except Project.DoesNotExist:
        return JsonResponse({"error": "Project not found"}, status=404)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@login_required
@require_http_methods(["POST"])
def submit_timesheet(request):
    """Submit timesheet for approval"""
    try:
        request.POST.get("week_start")
        
        # Mark timesheet as submitted (simplified implementation)
        # In a real app, youd have a Timesheet model
        
        return JsonResponse({
            "success": True,
            "message": "Timesheet submitted for approval"
        })
        
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@login_required
@require_http_methods(["POST"])
def export_timesheet(request):
    """Export timesheet data"""

    
    # Get date range
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")
    
    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = "attachment; filename=\"timesheet.csv\""
    
    writer = csv.writer(response)
    writer.writerow(["Date", "Project", "Hours", "Description", "Billable"])
    
    try:
        time_entries = TimeEntry.objects.filter(
            user=request.user
        ).select_related("project").order_by("start_time")
        
        if start_date:
            time_entries = time_entries.filter(start_time__date__gte=start_date)
        if end_date:
            time_entries = time_entries.filter(start_time__date__lte=end_date)
        
        for entry in time_entries:
            writer.writerow([
                entry.start_time.date(),
                entry.project.name if entry.project else "No Project",
                entry.duration_minutes / 60,
                entry.description,
                "Yes" if entry.billable else "No"
            ])
    except Exception as e:
        writer.writerow(["Error exporting data", str(e), "", "", ""])
    
    return response


@login_required
@require_http_methods(["POST"])
def task_update(request):
    """Update task details"""
    try:
        task_id = request.POST.get("task_id")
        title = request.POST.get("title")
        description = request.POST.get("description")
        status = request.POST.get("status")
        priority = request.POST.get("priority")
        
        task = Task.objects.get(id=task_id)
        
        # Check if user can update this task
        if task.assigned_to != request.user and not request.user.is_staff:
            return JsonResponse({"error": "Permission denied"}, status=403)
        
        if title:
            task.title = title
        if description:
            task.description = description
        if status:
            task.status = status
            if status == "completed":
                task.completed = True
                task.completed_at = timezone.now()
        if priority:
            task.priority = priority
        
        task.save()
        
        return JsonResponse({
            "success": True,
            "message": "Task updated successfully"
        })
        
    except Task.DoesNotExist:
        return JsonResponse({"error": "Task not found"}, status=404)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)

"""