"""
HTMX endpoint views for the CLEAR application.

This module contains all HTMX-specific views that provide dynamic content updates
without full page reloads. These views return HTML fragments that are inserted
into the DOM via HTMX requests.

Key Features:
- Dynamic search functionality for various entities
- Real-time dashboard components
- Interactive project statistics
- User activity feeds and notifications
- Enhanced timesheet management with charts
- Task and project management updates

import csv
import json
import logging
import random
import time
from datetime import datetime, timedelta
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db.models import Count, Q, Sum
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView
from django.views import View
from ..mixins import HTMXResponseMixin
from ..models import (
    import os
    from django.conf import settings
    from django.http import FileResponse
    from django.http import HttpResponse
                from django.contrib.gis.db.models.functions import Distance
                    from django.contrib.gis.geos import LineString
        from ..services.enhanced_spatial_analysis import run_conflict_detection
            from datetime import datetime
        from CLEAR.services.timeline_service import TimelineService
        from ..views.project_views import ProjectDetailView
    from ..services.ai_communication import AICommunicationService
    from django.db.models import Q
    from ..models import Project
        from ..models import Notification
        from ..models import Task, TimeEntry
        import re
        from django.contrib.auth import authenticate, login
        from django.contrib.auth.forms import AuthenticationForm
        from django.contrib.auth import logout
    from ..forms import DocumentForm


logger = logging.getLogger(__name__)

# from ..forms import ProjectForm, UtilityForm, TaskForm, CommentForm

    Activity,
    AnalyticsReport,
    ChatMessage,
    Comment,
    Conflict,
    Conversation,
    GISLayer,
    InternalEmail,
    Invoice,
    MessageRead,
    Note,
    Notification,
    Project,
    ProjectTemplate,
    ReportExecution,
    Stakeholder,
    Task,
    TimeEntry,
    User,
    Utility,
    UtilityLineData,
    WhisperMessage,
)

# ========== NAVIGATION DROPDOWN MANAGEMENT ==========

@login_required
@require_http_methods(["GET"])
def toggle_dropdown_htmx(request, dropdown_id):
    """
    Toggle dropdown state via HTMX - Pure server-side dropdown management
    
    This maintains dropdown state on the server (in session) following the
    "HTML IS the state" principle. No client-side JavaScript needed.
    session_key = f'dropdown_{dropdown_id}_open'
    
    # Toggle the dropdown state in session
    current_state = request.session.get(session_key, False)
    new_state = not current_state
    
    # Close all other dropdowns (only one can be open at a time)
    dropdown_keys = [key for key in request.session.keys() if key.startswith('dropdown_') and key.endswith('_open')]
    for key in dropdown_keys:
        request.session[key] = False
    
    # Set the new state for this dropdown
    request.session[session_key] = new_state
    
    # Return the dropdown HTML with correct CSS class
    css_class = 'dropdown-menu-open' if new_state else 'dropdown-menu-closed'
    
    # Get dropdown content based on dropdown_id
    if dropdown_id == 'myhub':
        return render(request, 'components/navigation/myhub_dropdown.html', {
            'css_class': css_class,
            'is_open': new_state
        })
    elif dropdown_id == 'project':
        return render(request, 'components/navigation/project_dropdown.html', {
            'css_class': css_class,
            'is_open': new_state
        })
    elif dropdown_id == 'help':
        return render(request, 'components/navigation/help_dropdown.html', {
            'css_class': css_class,
            'is_open': new_state
        })
    elif dropdown_id == 'admin':
        return render(request, 'components/navigation/admin_dropdown.html', {
            'css_class': css_class,
            'is_open': new_state
        })
    elif dropdown_id == 'user':
        return render(request, 'components/navigation/user_dropdown.html', {
            'css_class': css_class,
            'is_open': new_state,
            'user': request.user
        })
    else:
        return HttpResponse('Invalid dropdown ID', status=400)


# ========== TIMER UTILITY FUNCTIONS ==========

@login_required
@require_http_methods(["GET"])
def timer_form_toggle(request):
    """Toggle timer form visibility via HTMX"""
    # For now, return the same form - could expand to show/hide different controls
    user_projects = Project.objects.filter(
        Q(manager_id=request.user.id) | 
        Q(coordinator_id=str(request.user.id)) | 
        Q(egis_project_manager=request.user.username)
    ).order_by('name')[:20]
    
    return render(request, 'components/timer/timer_controls.html', {
        'user_projects': user_projects
    })


@login_required
@require_http_methods(["GET"])
def quick_entry_modal(request):
    """Show quick time entry modal via HTMX"""
    user_projects = Project.objects.filter(
        Q(manager_id=request.user.id) | 
        Q(coordinator_id=str(request.user.id)) | 
        Q(egis_project_manager=request.user.username)
    ).order_by('name')[:20]
    
    return render(request, 'components/timer/quick_entry_modal.html', {
        'user_projects': user_projects,
        'today': timezone.now().date()
    })


@login_required
@require_http_methods(["GET"])
def current_time(request):
    """Return current time for dashboard polling"""
    return HttpResponse(timezone.now().strftime("%b %d, %I:%M %p"))


# ========== MAIN DASHBOARD VIEW ==========

@login_required
def dashboard(request):
    """
    Main dashboard view - renders the updated React-style dashboard

    This view has been fully migrated with 100% visual parity to the original React dashboard.
    The dashboard includes:
    - Timer Controls and Active Timer
    - Team Chat section
    - Timesheet Summary section
    - Tasks List
    - Meetings List
    - My Projects section

    All components use HTMX for dynamic content loading and real-time updates.
    try:
        # Get user's projects for timer components
        user_projects = Project.objects.filter(
            Q(manager_id=request.user.id) | 
            Q(coordinator_id=str(request.user.id)) | 
            Q(egis_project_manager=request.user.username)
        ).order_by('name')[:20]  # Limit to most recent 20 projects

        context = {
            'now': timezone.now(),
            'today': timezone.now().date(),
            'user_projects': user_projects,
        }
        return render(request, 'CLEAR/dashboard.html', context)
    except Exception as e:
        logger.error(f"Error in HTMX dashboard view: {str(e)}")
        return render(request, 'error/500.html', {
            'error_message': f"An error occurred while loading the dashboard: {str(e)}"
        }, status=500)


@login_required
def whispers_count(request):
    """Get count of unread whisper messages (HTMX endpoint)"""
    count = WhisperMessage.objects.filter(
        recipient=request.user,
        read_at__isnull=True
    ).count()

    if request.headers.get('HX-Request'):
        return render(request, 'components/whisper_count.html', {
            'count': count,
            'hidden': count == 0
        })

    return JsonResponse({'count': count})


@login_required
@require_http_methods(["GET"])
def notifications_dropdown(request):
    """Get notifications dropdown content (HTMX endpoint)"""
    notifications = request.user.notifications.filter(
        is_read=False
    ).order_by('-created_at')[:10]

    return render(request, 'components/notifications_dropdown.html', {
        'notifications': notifications
    })


@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """Mark a notification as read (HTMX endpoint)"""
    try:
        notification = request.user.notifications.get(id=notification_id)
        notification.mark_read()

        if request.headers.get('HX-Request'):
            return render(request, 'components/notifications/notification_item.html', {
                'notification': notification
            })

        return JsonResponse({'success': True})
    except Notification.DoesNotExist:
        return JsonResponse({'error': 'Notification not found'}, status=404)

@login_required
def dashboard_stats(request):
    """Get dashboard statistics (HTMX endpoint)"""
    try:
        user = request.user

        # Recalculate stats
        user_projects = Project.objects.filter(
            Q(manager_id=user.id) | Q(coordinator_id=str(user.id))
        )

        stats = {
            'total_projects': user_projects.count(),
            'active_conflicts': Conflict.objects.filter(
                project__in=user_projects,
                status__in=['open', 'pending']
            ).count(),
            'pending_tasks': Task.objects.filter(
                assigned_to=user,
                status__in=['pending', 'in_progress']
            ).count(),
            'unread_notifications': user.notifications.filter(is_read=False).count(),
            'active_whispers': WhisperMessage.objects.filter(
                recipient=user,
                read_at__isnull=True
            ).count(),
            'recent_messages': ChatMessage.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
        }

        return render(request, 'components/dashboard_stats.html', {'stats': stats})
    except Exception as e:
        logger.error(f"Error in HTMX dashboard_stats view: {str(e)}")
        return render(request, 'error/500.html', {
            'error_message': f"An error occurred while loading dashboard statistics: {str(e)}"
        }, status=500)

@login_required
def recent_activity(request):
    """Get recent activity feed (HTMX endpoint)"""
    activities = Activity.objects.filter(user=request.user).order_by('-timestamp')[:10]

    return render(request, 'components/recent_activity.html', {
        'activities': activities
    })

# ========== ENHANCED HTMX ENDPOINTS FOR DYNAMIC CONTENT ==========

@login_required
@require_http_methods(["GET"])
def notebook_search(request):
    """Search notebook entries (HTMX endpoint)"""
    query = request.GET.get('q', '')
    user = request.user

    notes = Note.objects.filter(
        Q(author=user) | Q(shared_with=user),
        Q(title__icontains=query) | Q(content__icontains=query)
    ).order_by('-updated_at')[:10]

    return render(request, 'partials/notebook_search_results.html', {
        'notes': notes,
        'query': query
    })


@login_required
@require_http_methods(["GET"])
def stakeholder_search(request):
    """Search stakeholders (HTMX endpoint)"""
    query = request.GET.get('q', '')

    stakeholders = Stakeholder.objects.filter(
        Q(full_name__icontains=query) |
        Q(contact_company__icontains=query) |
        Q(email__icontains=query)
    ).order_by('contact_company', 'full_name')[:10]

    return render(request, 'partials/stakeholder_search_results.html', {
        'stakeholders': stakeholders,
        'query': query
    })


@login_required
@require_http_methods(["GET"])
def template_search(request):
    """Search project templates (HTMX endpoint)"""
    query = request.GET.get('q', '')

    templates = ProjectTemplate.objects.filter(
        Q(name__icontains=query) | Q(description__icontains=query),
        is_active=True
    ).order_by('name')[:10]

    return render(request, 'partials/template_search_results.html', {
        'templates': templates,
        'query': query
    })


@login_required
@require_http_methods(["GET"])
def project_quick_stats(request, project_id):
    """Get quick project stats (HTMX endpoint)"""
    project = get_object_or_404(Project, pk=project_id)

    stats = {
        'utilities_count': project.utilities.count(),
        'conflicts_count': project.conflicts.filter(status__in=['open', 'pending']).count(),
        'tasks_count': project.tasks.filter(completed=False).count(),
        'completion_percentage': 75,  # Calculate based on actual completion logic
        'next_milestone': project.last_milestone or 'Phase 1 Complete',
    }

    return render(request, 'partials/project_quick_stats.html', {
        'project': project,
        'stats': stats
    })


@login_required 
@require_http_methods(["GET"])
def user_activity_feed(request):
    """Get user activity feed (HTMX endpoint)"""
    activities = Activity.objects.filter(
        user=request.user
    ).select_related('project').order_by('-timestamp')[:15]

    return render(request, 'partials/activity_feed.html', {
        'activities': activities
    })


@login_required
@require_http_methods(["POST"])
def toggle_notification_read(request, notification_id):
    """Toggle notification read status (HTMX endpoint)"""
    try:
        notification = request.user.notifications.get(id=notification_id)
        notification.read = not notification.read
        if notification.read:
            notification.read_at = timezone.now()
        else:
            notification.read_at = None
        notification.save()

        return render(request, 'partials/notification_item.html', {
            'notification': notification
        })
    except Notification.DoesNotExist:
        return HttpResponse('Notification not found', status=404)


@login_required
@require_http_methods(["GET"])
def project_utilities_list(request, project_id):
    """Get project utilities list (HTMX endpoint)"""
    project = get_object_or_404(Project, pk=project_id)
    utilities = project.utilities.select_related().order_by('type', 'name')

    return render(request, 'partials/utilities_list.html', {
        'project': project,
        'utilities': utilities
    })


@login_required
@require_http_methods(["GET"])
def project_conflicts_summary(request, project_id):
    """Get project conflicts summary (HTMX endpoint)"""
    project = get_object_or_404(Project, pk=project_id)
    conflicts = project.conflicts.select_related('utility', 'utility2').order_by('-created_at')[:5]

    conflict_stats = {
        'total': project.conflicts.count(),
        'open': project.conflicts.filter(status='open').count(),
        'pending': project.conflicts.filter(status='pending').count(),
        'resolved': project.conflicts.filter(status='resolved').count(),
    }

    return render(request, 'partials/conflicts_summary.html', {
        'project': project,
        'conflicts': conflicts,
        'stats': conflict_stats
    })


@login_required
@require_http_methods(["GET"])
def timesheet_week_summary(request):
    """Get timesheet week summary (HTMX endpoint)"""
    user = request.user
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    time_entries = TimeEntry.objects.filter(
        user=user,
        start_time__date__range=[week_start, week_end]
    ).select_related('project')

    total_hours = sum(entry.duration_minutes for entry in time_entries) / 60
    billable_hours = sum(entry.duration_minutes for entry in time_entries if entry.billable) / 60

    return render(request, 'partials/timesheet_summary.html', {
        'time_entries': time_entries,
        'total_hours': total_hours,
        'billable_hours': billable_hours,
        'week_start': week_start,
        'week_end': week_end
    })


@login_required
@require_http_methods(["POST"])
def quick_task_create(request):
    """Quick task creation (HTMX endpoint)"""
    title = request.POST.get('title')
    project_id = request.POST.get('project_id')
    priority = request.POST.get('priority', 'Medium')

    if title and project_id:
        project = get_object_or_404(Project, pk=project_id)
        task = Task.objects.create(
            title=title,
            priority=priority,
            project=project,
            assigned_to=request.user,
            id=f"{project_id}-{timezone.now().strftime('%Y%m%d%H%M%S')}"
        )

        return render(request, 'partials/task_item.html', {
            'task': task,
            'show_project': True
        })

    return HttpResponse('Error creating task', status=400)


@login_required
@require_http_methods(["GET"])
def invoice_summary(request):
    """Get invoice summary (HTMX endpoint)"""
    user_projects = Project.objects.filter(
        Q(egis_project_manager=request.user.username) |
        Q(coordinator_id=str(request.user.id))
    )

    invoices = Invoice.objects.filter(
        project__in=user_projects
    ).order_by('-invoice_date')[:5]

    invoice_stats = {
        'total_amount': sum(inv.total_amount for inv in invoices),
        'pending_count': invoices.filter(status='pending').count(),
        'paid_count': invoices.filter(status='paid').count(),
    }

    return render(request, 'partials/invoice_summary.html', {
        'invoices': invoices,
        'stats': invoice_stats
    })


@login_required
@require_http_methods(["GET"])
def gis_layers_list(request):
    """Get available GIS layers (HTMX endpoint)"""
    layers = GISLayer.objects.filter(
        Q(is_public=True) | Q(created_by=request.user)
    ).order_by('z_index', 'name')

    return render(request, 'partials/gis_layers_list.html', {
        'layers': layers
    })


@login_required
@require_http_methods(["POST"])
def toggle_gis_layer(request, layer_id):
    """Toggle GIS layer visibility (HTMX endpoint)"""
    try:
        layer = GISLayer.objects.get(id=layer_id)
        layer.visibility = not layer.visibility
        layer.save()

        return render(request, 'partials/gis_layer_item.html', {
            'layer': layer
        })
    except GISLayer.DoesNotExist:
        return HttpResponse('Layer not found', status=404)


@login_required
@require_http_methods(["GET"])
def template_versioning_js(request):
    """Serve the template versioning JavaScript file"""


    js_file_path = os.path.join(
        settings.BASE_DIR, 
        'templates', 
        'templates', 
        'versioning_functions.js'
    )

    try:
        response = FileResponse(
            open(js_file_path, 'rb'),
            content_type='application/javascript'
        )
        response['Cache-Control'] = 'max-age=3600'  # Cache for 1 hour
        return response
    except FileNotFoundError:
        return HttpResponse('// Versioning functions not found', content_type='application/javascript')


# Dashboard Component HTMX Views

@login_required
def team_chat_partial(request):
    """Render enhanced team chat component for dashboard"""
    user = request.user

    # Update user's last activity for online status
    user.last_activity = timezone.now()
    user.save(update_fields=['last_activity'])

    # Get recent messages with enhanced processing
    messages = ChatMessage.objects.select_related('user').order_by('-created_at')[:50]

    # Get online users (users active in last 15 minutes)
    online_users = User.objects.filter(
        last_activity__gte=timezone.now() - timedelta(minutes=15)
    ).exclude(id=user.id)[:10]  # Exclude current user from online list

    # Process messages for display
    processed_messages = []
    for msg in reversed(messages):
        time_diff = timezone.now() - msg.created_at
        if time_diff.days > 0:
            time_ago = f"{time_diff.days}d ago"
        elif time_diff.seconds > 3600:
            time_ago = f"{time_diff.seconds // 3600}h ago"
        elif time_diff.seconds > 60:
            time_ago = f"{time_diff.seconds // 60}m ago"
        else:
            time_ago = "Just now"

        processed_messages.append({
            'id': msg.id,
            'content': msg.content,
            'user_initials': msg.user.get_initials(),
            'is_own': msg.user == user,
            'time_ago': time_ago,
        })

    online_users_data = [{
        'id': u.id,
        'first_name': u.first_name or u.username,
        'initials': u.get_initials(),
        'last_activity': u.last_activity,
    } for u in online_users]

    context = {
        'messages': processed_messages,
        'online_users': online_users_data,
        'online_users_count': len(online_users_data) + 1,  # +1 for current user
        'current_user': user,
    }

    return render(request, 'components/dashboard/team_chat.html', context)


@login_required
@require_http_methods(["POST"])
def send_team_message(request):
    """Send a new enhanced team chat message"""
    content = request.POST.get('content', '').strip()
    request.POST.get('reply_to', '').strip()

    if content and len(content) <= 500:  # Character limit
        # Update user activity
        request.user.last_activity = timezone.now()
        request.user.save(update_fields=['last_activity'])

        # Create the message
        message = ChatMessage.objects.create(
            user=request.user,
            content=content
        )

        # TODO: Handle reply_to_id for message threading (future enhancement)

        # Return just the new message HTML
        time_ago = "Just now"
        context = {
            'message': {
                'id': message.id,
                'content': message.content,
                'user_initials': message.user.get_initials(),
                'is_own': True,
                'time_ago': time_ago,
            }
        }
        return render(request, 'components/dashboard/team_chat_message.html', context)

    return HttpResponse('')


@login_required
def timesheet_summary_partial(request):
    """Render enhanced timesheet summary component for dashboard"""
    try:
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        
        # Basic context with safe defaults
        context = {
            'week_start': week_start,
            'week_total': 0.0,
            'billable_hours': 0.0,
            'overhead_hours': 0.0,
            'weekly_target': 40,
            'week_progress': 0.0,
            'hours_remaining': 40.0,
            'hours_over': 0.0,
            'daily_breakdown': [],
            'project_summary': [],
            'active_timer': None,
            'entries_count': 0,
            'approved_entries': 0,
            'pending_approval': 0,
            'avg_daily_hours': 0.0,
            'billable_rate': 0.0,
            'current_month_hours': 0.0,
            'daily_chart_data': json.dumps({'labels': [], 'datasets': []}),
            'project_chart_data': json.dumps({'labels': [], 'datasets': []}),
            'monthly_trend_data': json.dumps({'labels': [], 'datasets': []}),
        }
        
        return render(request, 'components/dashboard/timesheet_summary.html', context)
        
    except Exception as e:
        logger.error(f"Error in timesheet_summary_partial: {str(e)}")
        return render(request, 'components/dashboard/timesheet_summary.html', {
            'week_total': 0.0,
            'billable_hours': 0.0,
            'weekly_target': 40,
            'week_progress': 0.0,
            'daily_breakdown': [],
            'project_summary': [],
            'daily_chart_data': json.dumps({'labels': [], 'datasets': []}),
            'project_chart_data': json.dumps({'labels': [], 'datasets': []}),
            'monthly_trend_data': json.dumps({'labels': [], 'datasets': []}),
        })


@login_required
def tasks_list_partial(request):
    """Render tasks list component for dashboard"""
    try:
        show_completed = request.GET.get('show_completed', 'false').lower() == 'true'

        # Basic context with safe defaults  
        context = {
            'tasks': [],
            'pending_tasks': [],
            'completed_tasks': [],
            'total_tasks': 0,
            'completed_count': 0,
            'pending_count': 0,
            'overdue_count': 0,
            'today_tasks': [],
            'this_week_tasks': [],
            'show_completed': show_completed,
        }
        
        return render(request, 'components/dashboard/tasks_list.html', context)
        
    except Exception as e:
        logger.error(f"Error in tasks_list_partial: {str(e)}")
        return render(request, 'components/dashboard/tasks_list.html', {
            'tasks': [],
            'pending_tasks': [],
            'completed_tasks': [],
            'total_tasks': 0,
            'completed_count': 0,
            'pending_count': 0,
            'overdue_count': 0,
            'today_tasks': [],
            'this_week_tasks': [],
            'show_completed': False,
        })


@login_required
def export_timesheet_summary_csv(request):
    """Export timesheet summary data as CSV"""

    user = request.user
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    # Get time entries for the week
    time_entries = TimeEntry.objects.filter(
        user=user,
        start_time__date__gte=week_start,
        start_time__date__lte=week_end
    ).select_related('project', 'task').order_by('start_time')

    # Create HTTP response with CSV content type
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="timesheet_summary_{week_start.strftime("%Y_%m_%d")}.csv"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'Date', 'Project', 'Task', 'Description', 'Start Time', 'End Time', 
        'Duration (Hours)', 'Billable', 'Approved'
    ])

    # Write data rows
    for entry in time_entries:
        duration_hours = (entry.duration_minutes or 0) / 60.0
        writer.writerow([
            entry.start_time.date().strftime('%Y-%m-%d'),
            entry.project.name if entry.project else 'Administrative',
            entry.task.title if entry.task else '',
            entry.description or '',
            entry.start_time.strftime('%H:%M') if entry.start_time else '',
            entry.end_time.strftime('%H:%M') if entry.end_time else '',
            f"{duration_hours:.2f}",
            'Yes' if entry.billable else 'No',
            'Yes' if entry.approved else 'No'
        ])

    return response


@login_required
def export_timesheet_summary_json(request):
    """Export timesheet summary data as JSON for API use"""
    user = request.user
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    # Get time entries for the week
    time_entries = TimeEntry.objects.filter(
        user=user,
        start_time__date__gte=week_start,
        start_time__date__lte=week_end
    ).select_related('project', 'task')

    # Calculate summary statistics
    total_minutes = time_entries.aggregate(total=Sum('duration_minutes'))['total'] or 0
    total_hours = total_minutes / 60.0
    billable_minutes = time_entries.filter(billable=True).aggregate(total=Sum('duration_minutes'))['total'] or 0
    billable_hours = billable_minutes / 60.0

    # Prepare export data
    export_data = {
        'week_period': {
            'start_date': week_start.strftime('%Y-%m-%d'),
            'end_date': week_end.strftime('%Y-%m-%d'),
        },
        'summary': {
            'total_hours': round(total_hours, 2),
            'billable_hours': round(billable_hours, 2),
            'overhead_hours': round(total_hours - billable_hours, 2),
            'entries_count': time_entries.count(),
            'approved_entries': time_entries.filter(approved=True).count(),
        },
        'entries': []
    }

    # Add individual entries
    for entry in time_entries.order_by('start_time'):
        duration_hours = (entry.duration_minutes or 0) / 60.0
        export_data['entries'].append({
            'date': entry.start_time.date().strftime('%Y-%m-%d'),
            'project': entry.project.name if entry.project else 'Administrative',
            'task': entry.task.title if entry.task else None,
            'description': entry.description,
            'start_time': entry.start_time.strftime('%Y-%m-%d %H:%M:%S') if entry.start_time else None,
            'end_time': entry.end_time.strftime('%Y-%m-%d %H:%M:%S') if entry.end_time else None,
            'duration_hours': round(duration_hours, 2),
            'billable': entry.billable,
            'approved': entry.approved,
        })

    return JsonResponse(export_data)


@login_required
@login_required
def meetings_list_partial(request):
    """Render meetings list component for dashboard"""

    # For now, return empty meetings since Meeting model might not exist
    # In production, this would query actual meetings
    meetings = []

    # Example meeting data structure
    # meetings = Meeting.objects.filter(
    #     Q(attendees=user) | Q(organizer=user),
    #     start_time__gte=timezone.now()
    # ).order_by('start_time')[:5]

    context = {
        'meetings': meetings,
    }

    return render(request, 'components/dashboard/meetings_list.html', context)


@login_required
def my_projects_list_partial(request):
    """Render my projects list component for dashboard"""
    user = request.user

    # Get user's projects
    projects = Project.objects.filter(
        Q(manager_id=user.id) | 
        Q(coordinator_id=str(user.id)) | 
        Q(egis_project_manager=user.username)
    ).select_related('template').order_by('-updated_at')[:10]

    # Process projects for display
    processed_projects = []
    for project in projects:
        processed_projects.append({
            'id': project.id,
            'name': project.name,
            'rag_status': project.rag_status or 'Unknown',
            'current_phase': getattr(project, 'current_phase', 'Not started'),
            'updated_at': project.updated_at,
        })

    context = {
        'projects': processed_projects,
    }

    return render(request, 'components/dashboard/my_projects_list.html', context)


@login_required
def dashboard_stats_cards(request):
    """Enhanced dashboard stats cards with real-time calculations and trends"""
    try:
        user = request.user
        
        # Get user's projects for stats calculations
        user_projects = Project.objects.filter(
            Q(manager_id=user.id) | 
            Q(coordinator_id=str(user.id)) | 
            Q(egis_project_manager=user.username)
        )
        
        # Calculate basic stats with enhanced details
        stats = {
            'active_projects': user_projects.filter(
                project_health_rag__in=['green', 'yellow']
            ).count(),
            'total_projects': user_projects.count(),
            
            'pending_conflicts': Conflict.objects.filter(
                project__in=user_projects,
                status__in=['open', 'pending', 'identified']
            ).count(),
            
            'month_hours': 0.0,  # Calculated below
            'week_hours': 0.0,   # Calculated below
            'billable_hours': 0.0, # Calculated below
            
            'pending_tasks': Task.objects.filter(
                assigned_to=user,
                status__in=['pending', 'in_progress']
            ).count(),
            
            'unread_notifications': user.notifications.filter(is_read=False).count(),
            
            'recent_activities': Activity.objects.filter(
                user=user
            ).order_by('-timestamp')[:3],
            
            'project_health': {
                'green': user_projects.filter(project_health_rag='green').count(),
                'yellow': user_projects.filter(project_health_rag='yellow').count(),
                'red': user_projects.filter(project_health_rag='red').count(),
            }
        }
        
        # Calculate time-based statistics (current month)
        today = timezone.now().date()
        month_start = today.replace(day=1)
        week_start = today - timedelta(days=today.weekday())
        
        # Get month hours
        month_entries = TimeEntry.objects.filter(
            user=user,
            start_time__date__gte=month_start,
            start_time__date__lte=today
        )
        
        if month_entries.exists():
            month_minutes = month_entries.aggregate(
                total=Sum('duration_minutes')
            )['total'] or 0
            stats['month_hours'] = round(month_minutes / 60.0, 1)
            
            # Calculate billable hours for month
            billable_minutes = month_entries.filter(billable=True).aggregate(
                total=Sum('duration_minutes')
            )['total'] or 0
            stats['billable_hours'] = round(billable_minutes / 60.0, 1)
        
        # Get week hours
        week_entries = TimeEntry.objects.filter(
            user=user,
            start_time__date__gte=week_start,
            start_time__date__lte=today
        )
        
        if week_entries.exists():
            week_minutes = week_entries.aggregate(
                total=Sum('duration_minutes')
            )['total'] or 0
            stats['week_hours'] = round(week_minutes / 60.0, 1)
        
        # Performance metrics
        active_timer = TimeEntry.get_active_timer(user) if hasattr(TimeEntry, 'get_active_timer') else None
        stats['active_timer'] = active_timer
        
        # Weekly target progress
        weekly_target = getattr(user, 'weekly_hours_target', 40)
        if weekly_target > 0:
            stats['week_progress'] = min(100, (stats['week_hours'] / weekly_target) * 100)
            stats['hours_remaining'] = max(0, weekly_target - stats['week_hours'])
        else:
            stats['week_progress'] = 0
            stats['hours_remaining'] = 0
        
        return render(request, 'components/dashboard/stats_cards_content.html', {
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error in dashboard_stats_cards: {str(e)}")
        # Return empty stats with safe defaults
        default_stats = {
            'active_projects': 0,
            'total_projects': 0,
            'pending_conflicts': 0,
            'month_hours': 0.0,
            'week_hours': 0.0,
            'billable_hours': 0.0,
            'pending_tasks': 0,
            'unread_notifications': 0,
            'recent_activities': [],
            'project_health': {'green': 0, 'yellow': 0, 'red': 0},
            'active_timer': None,
            'week_progress': 0,
            'hours_remaining': 40,
        }
        return render(request, 'components/dashboard/stats_cards_content.html', {
            'stats': default_stats
        })


@login_required
@require_http_methods(["GET"])
def htmx_notification_count(request):
    """Get notification count for badge update"""
    count = request.user.notifications.filter(is_read=False).count()
    
    if count > 0:
        return HttpResponse(f'<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">{count if count < 100 else "99+"}</span>')
    else:
        return HttpResponse('')


@login_required
def notifications_dropdown_enhanced(request):
    """Enhanced notifications dropdown with better organization"""
    notifications = request.user.notifications.filter(
        is_read=False
    ).order_by('-created_at')[:10]

    context = {
        'notifications': notifications,
        'unread_count': request.user.notifications.filter(is_read=False).count(),
    }

    return render(request, 'components/notifications/notification_dropdown.html', context)


@login_required
@require_http_methods(["POST"])
def mark_all_notifications_read_htmx(request):
    """Mark all notifications as read via HTMX"""
    try:
        request.user.notifications.filter(is_read=False).update(
            is_read=True,
            read_at=timezone.now()
        )

        # Return updated dropdown
        return notifications_dropdown_enhanced(request)

    except Exception:
        return HttpResponse('Error marking notifications as read', status=400)


@login_required
def test_notification_htmx(request):
    """Send a test notification to the user"""
    try:
        # Create a simple test notification
        Notification.objects.create(
            recipient=request.user,
            notification_type='system',
            title='Test Notification',
            message='This is a test notification to verify your notification settings are working correctly.',
            is_read=False
        )

        return JsonResponse({'success': True, 'message': 'Test notification sent!'})

    except Exception:
        return JsonResponse({'success': False, 'message': 'Failed to send test notification'})


@login_required
@require_http_methods(["POST"])
def notification_mark_read_toggle_htmx(request, notification_id):
    """Toggle notification read status via HTMX"""
    try:
        notification = request.user.notifications.get(id=notification_id)
        notification.is_read = not notification.is_read
        if notification.is_read:
            notification.read_at = timezone.now()
        else:
            notification.read_at = None
        notification.save()

        # Return updated notification item
        return render(request, 'components/notifications/notification_item.html', {
            'notification': notification
        })

    except Notification.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Notification not found</div>', status=404)


@login_required
def notification_settings_view(request):
    """Display notification settings page"""
    # For now, return a simple settings page
    context = {
        'user': request.user,
    }

    return render(request, 'components/notifications/notification_settings.html', context)


@login_required
@require_http_methods(["POST"])
def notification_settings_save_htmx(request):
    """Save notification settings via HTMX"""
    try:
        # For now, just return success
        return JsonResponse({'success': True, 'message': 'Settings saved successfully!'})

    except Exception:
        return JsonResponse({'success': False, 'message': 'Failed to save settings'})


# ======= NEWLY MIGRATED HTMX FUNCTIONS =======

# ======= COMMENT MANAGEMENT =======

@require_http_methods(["GET"])
def comment_count_htmx(request, commentable_type, commentable_id):
    """Get comment count for an entity via HTMX"""
    commentable_type = commentable_type.lower()
    count = Comment.objects.count_for_entity(commentable_type, commentable_id)

    return render(request, 'components/comments/comment_count.html', {
        'count': count,
        'commentable_type': commentable_type,
        'commentable_id': commentable_id,
    })


@require_http_methods(["POST"])
def comment_create_htmx(request):
    """Create a new comment via HTMX"""
    content = request.POST.get('content', '').strip()
    commentable_type = request.POST.get('commentable_type', '').lower()
    commentable_id = request.POST.get('commentable_id', '')
    parent_id = request.POST.get('parent_id')  # For replies

    if not content or not commentable_type or not commentable_id:
        return HttpResponse('<div class="alert alert-danger">Missing required fields</div>', status=400)

    # Validate commentable_type
    valid_types = ['project', 'task', 'utility', 'conflict', 'document', 'note']
    if commentable_type not in valid_types:
        return HttpResponse('<div class="alert alert-danger">Invalid entity type</div>', status=400)

    # Create the comment
    comment_data = {
        'commentable_type': commentable_type,
        'commentable_id': commentable_id,
        'user': request.user,
        'content': content,
    }

    if parent_id:
        try:
            parent_comment = Comment.objects.get(id=parent_id, deleted_at__isnull=True)
            comment_data['parent'] = parent_comment
        except Comment.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Parent comment not found</div>', status=400)

    comment = Comment.objects.create(**comment_data)

    # Create activity log if possible
    try:
        if commentable_type == 'project':
            Project.objects.get(id=commentable_id)
            # Create activity using the existing Activity model
            Activity.objects.create(
                user=request.user,
                action_type='comment_added',
                description=f'Added a comment: {content[:100]}...' if len(content) > 100 else f'Added a comment: {content}',
                target_id=str(comment.id)
            )
    except Exception:
        pass  # Don't fail comment creation if activity logging fails

    # Return the new comment HTML
    return render(request, 'components/comments/comment_item.html', {
        'comment': comment,
        'can_edit': True,
        'can_delete': True,
    })


@require_http_methods(["POST"])
def comment_delete_htmx(request, comment_id):
    """Soft delete a comment via HTMX"""
    try:
        comment = Comment.objects.get(id=comment_id, deleted_at__isnull=True)
    except Comment.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Comment not found</div>', status=404)

    # Check permissions - user can delete their own comments or admin can delete any
    if comment.user != request.user and not request.user.is_staff:
        return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

    comment.soft_delete()

    return HttpResponse('')  # Return empty response to remove the comment from DOM


@require_http_methods(["GET"])
def comment_list_htmx(request, commentable_type, commentable_id):
    """Load comments for an entity via HTMX"""
    commentable_type = commentable_type.lower()

    # Validate commentable_type
    valid_types = ['project', 'task', 'utility', 'conflict', 'document', 'note']
    if commentable_type not in valid_types:
        return HttpResponse('<div class="alert alert-danger">Invalid entity type</div>', status=400)

    # Get comments (top-level only, replies are loaded separately)
    comments = Comment.objects.top_level_for_entity(commentable_type, commentable_id)

    # Pagination
    page_num = request.GET.get('page', 1)
    paginator = Paginator(comments, 20)  # 20 comments per page
    page_obj = paginator.get_page(page_num)

    return render(request, 'components/comments/comment_list.html', {
        'comments': page_obj,
        'commentable_type': commentable_type,
        'commentable_id': commentable_id,
        'user': request.user,
    })


@require_http_methods(["GET"])
def comment_replies_htmx(request, comment_id):
    """Load replies for a comment via HTMX"""
    try:
        parent_comment = Comment.objects.get(id=comment_id, deleted_at__isnull=True)
    except Comment.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Comment not found</div>', status=404)

    replies = parent_comment.get_replies()

    return render(request, 'components/comments/comment_replies.html', {
        'replies': replies,
        'parent_comment': parent_comment,
        'user': request.user,
    })


@require_http_methods(["POST"])
def comment_update_htmx(request, comment_id):
    """Update a comment via HTMX"""
    try:
        comment = Comment.objects.get(id=comment_id, deleted_at__isnull=True)
    except Comment.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Comment not found</div>', status=404)

    # Check permissions - user can only edit their own comments
    if comment.user != request.user:
        return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

    content = request.POST.get('content', '').strip()
    if not content:
        return HttpResponse('<div class="alert alert-danger">Content cannot be empty</div>', status=400)

    comment.content = content
    comment.save()

    return render(request, 'components/comments/comment_item.html', {
        'comment': comment,
        'can_edit': True,
        'can_delete': True,
        'edit_mode': False,
    })


# ======= USER PROFILE AND SETTINGS =======

@login_required
def avatar_upload_htmx(request):
    """HTMX endpoint for avatar upload (placeholder for future implementation)"""
    if request.method != 'POST':
        return HttpResponse('<div class="alert alert-danger">Invalid request method</div>', status=405)

    # TODO: Implement avatar upload using Document Management patterns
    # For now, return success message
    return HttpResponse('<div class="alert alert-info">Avatar upload coming soon</div>')


@login_required  
def profile_activity_htmx(request):
    """HTMX endpoint for loading more profile activity"""
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 10))

    activities = Activity.objects.filter(
        user=request.user
    ).order_by('-created_at')[offset:offset+limit]

    context = {
        'activities': activities,
        'show_load_more': len(activities) == limit
    }

    return render(request, 'components/profile/activity_list.html', context)


@login_required
def profile_stats_htmx(request):
    """HTMX endpoint for refreshing profile statistics"""
    user = request.user

    # Recalculate stats based on existing Project model structure
    user_projects = Project.objects.filter(
        Q(manager_id=user.id) | Q(coordinator_id=str(user.id))
    ).distinct()
    user_tasks = Task.objects.filter(assigned_to=user)
    completed_tasks = user_tasks.filter(status='completed')

    context = {
        'active_projects': user_projects.filter(status__in=['active', 'in_progress']).count(),
        'completed_tasks': completed_tasks.count(),
        'completion_rate': (completed_tasks.count() / user_tasks.count() * 100) if user_tasks.exists() else 0,
    }

    return render(request, 'components/profile/stats_cards.html', context)


@login_required
@require_http_methods(["POST"])
def settings_save_htmx(request):
    """HTMX endpoint for saving user settings"""
    try:
        # Update basic user fields
        user = request.user
        user.first_name = request.POST.get('first_name', user.first_name)
        user.last_name = request.POST.get('last_name', user.last_name)
        user.email = request.POST.get('email', user.email)

        # Update custom fields if they exist
        if hasattr(user, 'phone'):
            user.phone = request.POST.get('phone', getattr(user, 'phone', ''))
        if hasattr(user, 'job_title'):
            user.job_title = request.POST.get('job_title', getattr(user, 'job_title', ''))
        if hasattr(user, 'department'):
            user.department = request.POST.get('department', getattr(user, 'department', ''))

        # Update custom_settings JSON field
        if hasattr(user, 'custom_settings') and user.custom_settings:
            settings_dict = user.custom_settings
        else:
            settings_dict = {}

        # Update various settings
        settings_dict.update({
            'unit_preference': request.POST.get('unit_preference', 'imperial'),
            'language': request.POST.get('language', 'en'),
            'timezone': request.POST.get('timezone', 'America/New_York'),
            'date_format': request.POST.get('date_format', 'MM/DD/YYYY'),
        })

        # Update notification preferences
        notifications = settings_dict.get('notifications', {})
        notifications.update({
            'email_project_updates': request.POST.get('email_project_updates') == 'on',
            'email_conflict_alerts': request.POST.get('email_conflict_alerts') == 'on',
            'email_task_assignments': request.POST.get('email_task_assignments') == 'on',
            'browser_notifications': request.POST.get('browser_notifications') == 'on',
            'sound_alerts': request.POST.get('sound_alerts') == 'on',
        })
        settings_dict['notifications'] = notifications

        user.custom_settings = settings_dict
        user.save()

        return HttpResponse('<div class="alert alert-success">Settings saved successfully!</div>')

    except Exception as e:
        logger.error(f"Error saving settings: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error saving settings</div>', status=500)


@require_http_methods(["GET"])
def settings_tab_htmx(request):
    """HTMX endpoint for dynamically loading settings tabs"""
    tab = request.GET.get('tab', 'general')

    # Prepare settings data structure
    user = request.user
    custom_settings = getattr(user, 'custom_settings', {}) or {}

    settings_data = {
        'general': {
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'phone': getattr(user, 'phone', ''),
            'display_name': getattr(user, 'display_name', ''),
            'department': getattr(user, 'department', ''),
            'job_title': getattr(user, 'job_title', ''),
            'unit_preference': custom_settings.get('unit_preference', 'imperial')
        },
        'notifications': custom_settings.get('notifications', {
            'email_project_updates': True,
            'email_conflict_alerts': True,
            'email_task_assignments': True,
            'browser_notifications': False,
            'sound_alerts': False
        }),
        'security': {
            'two_factor_enabled': False,  # Placeholder
            'active_sessions': []  # Placeholder
        },
        'appearance': custom_settings.get('appearance', {
            'theme': 'light',
            'compact_mode': False,
            'animations': True,
            'high_contrast': False
        }),
        'integrations': custom_settings.get('integrations', {
            'connected_services': ['arcgis', 'autocad']
        }),
        'advanced': custom_settings.get('advanced', {
            'map_quality': 'high',
            'auto_refresh': '30',
            'hardware_acceleration': True,
            'debug_mode': False,
            'api_access': True
        })
    }

    # Options for dropdowns
    context = {
        'settings_data': settings_data,
        'tab': tab,
        'language_options': [('en', 'English'), ('es', 'Spanish'), ('fr', 'French')],
        'timezone_options': [
            ('America/New_York', 'Eastern Time'), 
            ('America/Chicago', 'Central Time'), 
            ('America/Denver', 'Mountain Time'), 
            ('America/Los_Angeles', 'Pacific Time')
        ],
        'date_format_options': [
            ('MM/DD/YYYY', 'MM/DD/YYYY'), 
            ('DD/MM/YYYY', 'DD/MM/YYYY'), 
            ('YYYY-MM-DD', 'YYYY-MM-DD')
        ],
        'units_options': [('imperial', 'Imperial'), ('metric', 'Metric')],
        'quality_options': [('low', 'Low'), ('medium', 'Medium'), ('high', 'High')],
    }

    return render(request, f'components/settings/{tab}_settings.html', context)


# ======= MESSAGING =======

@require_http_methods(["POST"])
def message_mark_read_htmx(request, message_id):
    """Mark a message as read via HTMX"""
    try:
        message = ChatMessage.objects.get(id=message_id)

        # Check permissions - only recipient can mark as read
        if message.recipient != request.user:
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

        message.is_read = True
        message.read_at = timezone.now()
        message.save()

        return HttpResponse('<span class="badge badge-success">Read</span>')

    except ChatMessage.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Message not found</div>', status=404)
    except Exception as e:
        logger.error(f"Error marking message as read: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error updating message</div>', status=500)


@require_http_methods(["GET"])
def message_search_htmx(request):
    """Search messages via HTMX"""
    query = request.GET.get('q', '').strip()

    if not query or len(query) < 2:
        return HttpResponse('<div class="text-muted">Enter at least 2 characters to search</div>')

    try:
        # Search in messages where user is sender or recipient
        messages = ChatMessage.objects.filter(
            Q(sender=request.user) | Q(recipient=request.user)
        ).filter(
            Q(message__icontains=query) | Q(subject__icontains=query)
        ).order_by('-timestamp')[:20]

        context = {
            'messages': messages,
            'query': query,
            'user': request.user,
        }

        return render(request, 'components/messaging/search_results.html', context)

    except Exception as e:
        logger.error(f"Error searching messages: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error searching messages</div>')


@require_http_methods(["POST"])
def message_send_htmx(request):
    """Send a message via HTMX"""
    try:
        recipient_id = request.POST.get('recipient_id')
        subject = request.POST.get('subject', '').strip()
        message_content = request.POST.get('message', '').strip()

        if not recipient_id or not message_content:
            return HttpResponse('<div class="alert alert-danger">Recipient and message are required</div>', status=400)

        try:
            recipient = User.objects.get(id=recipient_id)
        except User.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Recipient not found</div>', status=400)

        # Create the message
        ChatMessage.objects.create(
            sender=request.user,
            recipient=recipient,
            subject=subject or 'No Subject',
            message=message_content,
            timestamp=timezone.now()
        )

        return HttpResponse(
            '<div class="alert alert-success">Message sent successfully!</div>',
            headers={'HX-Trigger': 'messageSent'}
        )

    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error sending message</div>', status=500)


@require_http_methods(["GET"])
def messages_list_htmx(request):
    """Load messages list via HTMX"""
    try:
        # Get messages for the user (both sent and received)
        messages = ChatMessage.objects.filter(
            Q(sender=request.user) | Q(recipient=request.user)
        ).order_by('-timestamp')

        # Pagination
        page_num = request.GET.get('page', 1)
        paginator = Paginator(messages, 20)
        page_obj = paginator.get_page(page_num)

        context = {
            'messages': page_obj,
            'user': request.user,
        }

        return render(request, 'components/messaging/messages_list.html', context)

    except Exception as e:
        logger.error(f"Error loading messages: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error loading messages</div>')


# ======= PROJECT MANAGEMENT =======

@require_http_methods(["GET"])
def project_tab_htmx(request):
    """HTMX endpoint for loading project tab content"""
    tab = request.GET.get('tab', 'overview')
    project_id = request.GET.get('project_id')

    if not project_id:
        return HttpResponse('<div class="alert alert-danger">Project ID required</div>', status=400)

    try:
        project = get_object_or_404(Project, id=project_id)

        # Check permissions
        if not (project.manager_id == request.user.id or 
                project.coordinator_id == str(request.user.id) or
                request.user.is_staff):
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

        context = {
            'project': project,
            'tab': tab,
            'user': request.user,
        }

        # Add tab-specific data
        if tab == 'tasks':
            context['tasks'] = Task.objects.filter(project=project).order_by('-created_at')[:20]
        elif tab == 'team':
            # Get team members - adjust based on your Project model structure
            context['team_members'] = []  # Placeholder for now
        elif tab == 'files':
            # Get project files - adjust based on your Document model
            context['documents'] = []  # Placeholder for now

        return render(request, f'components/project/{tab}_tab.html', context)

    except Project.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Project not found</div>', status=404)
    except Exception as e:
        logger.error(f"Error loading project tab: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error loading project data</div>')


# ======= TIME TRACKING =======

@require_http_methods(["GET"]) 
def quick_time_entry_htmx(request):
    """HTMX endpoint for quick time entry form"""
    user_projects = Project.objects.filter(
        Q(manager_id=request.user.id) | 
        Q(coordinator_id=str(request.user.id))
    ).order_by('name')[:20]

    context = {
        'user_projects': user_projects,
        'today': timezone.now().date(),
    }

    return render(request, 'components/timesheet/quick_entry_form.html', context)


@require_http_methods(["POST"])
def start_timer_htmx(request):
    """Start a timer for time tracking via HTMX"""
    try:
        project_id = request.POST.get('project_id')
        description = request.POST.get('description', '').strip()

        if not project_id:
            return HttpResponse('<div class="alert alert-danger">Project is required</div>', status=400)

        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Project not found</div>', status=400)

        # Check if user already has an active timer
        active_timer = TimeEntry.objects.filter(
            user=request.user,
            end_time__isnull=True
        ).first()

        if active_timer:
            return HttpResponse('<div class="alert alert-warning">You already have an active timer</div>', status=400)

        # Create new time entry
        time_entry = TimeEntry.objects.create(
            user=request.user,
            project=project,
            description=description,
            start_time=timezone.now()
        )

        context = {
            'time_entry': time_entry,
            'project': project,
        }

        return render(request, 'components/timesheet/active_timer.html', context)

    except Exception as e:
        logger.error(f"Error starting timer: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error starting timer</div>', status=500)


@require_http_methods(["POST"])
def stop_timer_htmx(request):
    """Stop active timer via HTMX"""
    try:
        timer_id = request.POST.get('timer_id')

        if timer_id:
            # Stop specific timer
            try:
                timer = TimeEntry.objects.get(id=timer_id, user=request.user, end_time__isnull=True)
            except TimeEntry.DoesNotExist:
                return HttpResponse('<div class="alert alert-danger">Timer not found</div>', status=404)
        else:
            # Stop any active timer for this user
            timer = TimeEntry.objects.filter(
                user=request.user,
                end_time__isnull=True
            ).first()

            if not timer:
                return HttpResponse('<div class="alert alert-warning">No active timer found</div>', status=400)

        # Stop the timer
        timer.end_time = timezone.now()
        timer.save()

        # Calculate duration
        duration = timer.end_time - timer.start_time
        hours = duration.total_seconds() / 3600

        return HttpResponse(
            f'<div class="alert alert-success">Timer stopped. Logged {hours:.2f} hours.</div>',
            headers={'HX-Trigger': 'timerStopped'}
        )

    except Exception as e:
        logger.error(f"Error stopping timer: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error stopping timer</div>', status=500)


@require_http_methods(["GET"])
def timer_status_htmx(request):
    """Get current timer status via HTMX"""
    try:
        active_timer = TimeEntry.objects.filter(
            user=request.user,
            end_time__isnull=True
        ).first()

        context = {
            'active_timer': active_timer,
            'has_active_timer': active_timer is not None,
        }

        return render(request, 'components/timesheet/timer_status.html', context)

    except Exception as e:
        logger.error(f"Error getting timer status: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error loading timer status</div>')


# ======= UTILITY AND SPATIAL ANALYSIS HTMX FUNCTIONS =======

@login_required
@require_http_methods(["GET"])
def mapping_utility_data_geojson(request, project_id):
    """Return utility data as GeoJSON for OpenLayers"""
    try:
        project = get_object_or_404(Project, pk=project_id)
        utility_type = request.GET.get('type', 'all')

        # Get utilities within project area
        utilities = Utility.objects.all()
        if project.project_area:
            utilities = utilities.filter(location__intersects=project.project_area)

        if utility_type != 'all':
            utilities = utilities.filter(utility_type__iexact=utility_type)

        features = []
        for utility in utilities:
            if utility.location:
                # Convert to GeoJSON format
                feature = {
                    "type": "Feature",
                    "geometry": json.loads(utility.location.geojson),
                    "properties": {
                        "id": utility.id,
                        "name": utility.name or 'Unnamed Utility',
                        "utility_type": utility.utility_type,
                        "company": utility.company,
                        "description": utility.description or f"{utility.utility_type} - {utility.company}",
                        "status": getattr(utility, 'status', 'active'),
                        "style": {
                            "color": getattr(utility, 'line_color', '#666666'),
                            "weight": getattr(utility, 'line_weight', 2),
                        }
                    }
                }
                features.append(feature)

        geojson_data = {
            "type": "FeatureCollection",
            "features": features,
            "metadata": {
                "project_id": project_id,
                "utility_type": utility_type,
                "feature_count": len(features),
                "coordinate_system": "EPSG:4326"  # GeoJSON standard
            }
        }

        return JsonResponse(geojson_data)

    except Exception as e:
        logger.error(f"GeoJSON error: {str(e)}")
        return JsonResponse({'error': f'GeoJSON error: {str(e)}'}, status=400)


@login_required
@require_http_methods(["GET"])
def map_layers_htmx(request, project_id):
    """HTMX endpoint for layer management"""
    project = get_object_or_404(Project, id=project_id)

    # Get layer visibility preferences
    show_utilities = request.GET.get('show_utilities', 'true').lower() == 'true'
    show_conflicts = request.GET.get('show_conflicts', 'true').lower() == 'true'
    show_boundaries = request.GET.get('show_boundaries', 'true').lower() == 'true'
    utility_company_filter = request.GET.get('utility_company', '')

    # Filter utilities based on preferences
    utilities = Utility.objects.all()
    if project.project_area:
        utilities = utilities.filter(location__intersects=project.project_area)

    if utility_company_filter:
        utilities = utilities.filter(company__icontains=utility_company_filter)

    # Get conflicts if requested
    conflicts = []
    if show_conflicts:
        conflicts = Conflict.objects.filter(project=project).select_related('utility1', 'utility2')

    # Get GIS layers
    layers = GISLayer.objects.filter(
        Q(project=project) | Q(is_public=True)
    ).order_by('z_index', 'name')

    context = {
        'project': project,
        'utilities': utilities if show_utilities else [],
        'conflicts': conflicts,
        'layers': layers,
        'show_utilities': show_utilities,
        'show_conflicts': show_conflicts,
        'show_boundaries': show_boundaries,
        'utility_company_filter': utility_company_filter,
    }

    return render(request, 'components/mapping/layer_control.html', context)


@login_required
@require_http_methods(["GET"])
def utility_conflicts_htmx(request, project_id):
    """HTMX endpoint for conflict detection results"""
    project = get_object_or_404(Project, id=project_id)

    # Get conflict detection parameters
    buffer_distance = float(request.GET.get('buffer_distance', '10'))  # Default 10 feet
    conflict_type = request.GET.get('conflict_type', 'all')

    # Perform conflict detection using PostGIS spatial queries
    conflicts = Conflict.objects.filter(project=project)

    if conflict_type != 'all':
        conflicts = conflicts.filter(conflict_type=conflict_type)

    # Get conflicts with spatial analysis
    conflicts_with_analysis = []
    for conflict in conflicts.select_related('utility1', 'utility2'):
        analysis = {
            'conflict': conflict,
            'distance': None,
            'severity': conflict.severity or 'medium',
            'resolution_status': conflict.resolution_status or 'pending',
        }

        # Calculate distance if both utilities have locations
        if hasattr(conflict, 'utility1') and hasattr(conflict, 'utility2'):
            if conflict.utility1.location and conflict.utility2.location:
                # Use PostGIS to calculate distance
                distance_query = Utility.objects.filter(
                    id=conflict.utility1.id
                ).annotate(
                    distance_to_other=Distance('location', conflict.utility2.location)
                ).first()

                if distance_query and distance_query.distance_to_other:
                    analysis['distance'] = distance_query.distance_to_other.ft  # Convert to feet

        conflicts_with_analysis.append(analysis)

    # Group conflicts by severity
    conflicts_by_severity = {
        'high': [c for c in conflicts_with_analysis if c['severity'] == 'high'],
        'medium': [c for c in conflicts_with_analysis if c['severity'] == 'medium'],
        'low': [c for c in conflicts_with_analysis if c['severity'] == 'low'],
    }

    context = {
        'project': project,
        'conflicts_with_analysis': conflicts_with_analysis,
        'conflicts_by_severity': conflicts_by_severity,
        'total_conflicts': len(conflicts_with_analysis),
        'buffer_distance': buffer_distance,
        'conflict_type': conflict_type,
    }

    return render(request, 'components/mapping/conflict_panel.html', context)


@login_required
@require_http_methods(["GET"])
def spatial_search_htmx(request, project_id):
    """HTMX endpoint for spatial searches"""
    project = get_object_or_404(Project, id=project_id)

    search_query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'all')  # all, utilities, conflicts, layers

    results = {
        'utilities': [],
        'conflicts': [],
        'layers': [],
        'total_count': 0,
    }

    if search_query:
        # Search utilities
        if search_type in ['all', 'utilities']:
            utility_results = Utility.objects.filter(
                Q(company__icontains=search_query) |
                Q(utility_type__icontains=search_query) |
                Q(description__icontains=search_query)
            )

            # Filter by project area if available
            if project.project_area:
                utility_results = utility_results.filter(location__intersects=project.project_area)

            results['utilities'] = utility_results[:20]  # Limit results

        # Search conflicts
        if search_type in ['all', 'conflicts']:
            conflict_results = Conflict.objects.filter(
                project=project
            ).filter(
                Q(description__icontains=search_query) |
                Q(conflict_type__icontains=search_query)
            ).select_related('utility1', 'utility2')

            results['conflicts'] = conflict_results[:20]

        # Search GIS layers
        if search_type in ['all', 'layers']:
            layer_results = GISLayer.objects.filter(
                Q(project=project) | Q(is_public=True)
            ).filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

            results['layers'] = layer_results[:20]

        results['total_count'] = len(results['utilities']) + len(results['conflicts']) + len(results['layers'])

    context = {
        'project': project,
        'search_query': search_query,
        'search_type': search_type,
        'results': results,
    }

    return render(request, 'components/mapping/search_results.html', context)


@login_required
@require_http_methods(["GET"])
def map_geojson_data(request, project_id):
    """API endpoint to provide GeoJSON data for map layers"""
    project = get_object_or_404(Project, id=project_id)

    layer_type = request.GET.get('layer', 'utilities')

    features = []

    if layer_type == 'utilities':
        # Get utilities within project area
        utilities = Utility.objects.all()
        if project.project_area:
            utilities = utilities.filter(location__intersects=project.project_area)

        for utility in utilities:
            if utility.location:
                feature = {
                    'type': 'Feature',
                    'geometry': json.loads(utility.location.geojson),
                    'properties': {
                        'id': utility.id,
                        'company': utility.company,
                        'utility_type': utility.utility_type,
                        'description': utility.description,
                        'status': getattr(utility, 'status', 'active'),
                    }
                }
                features.append(feature)

    elif layer_type == 'conflicts':
        # Get conflicts with spatial data
        conflicts = Conflict.objects.filter(project=project).select_related('utility1', 'utility2')

        for conflict in conflicts:
            # Create point at center of conflict if utilities have locations
            if (hasattr(conflict, 'utility1') and hasattr(conflict, 'utility2') and 
                conflict.utility1.location and conflict.utility2.location):

                # Calculate centroid between utilities

                try:
                    # Create line between utilities and get centroid
                    line = LineString(conflict.utility1.location, conflict.utility2.location)
                    centroid = line.centroid

                    feature = {
                        'type': 'Feature',
                        'geometry': json.loads(centroid.geojson),
                        'properties': {
                            'id': conflict.id,
                            'description': conflict.description,
                            'severity': conflict.severity or 'medium',
                            'conflict_type': conflict.conflict_type,
                            'utility1': conflict.utility1.company,
                            'utility2': conflict.utility2.company,
                        }
                    }
                    features.append(feature)
                except Exception:
                    pass  # Skip conflicts without valid geometry

    geojson_data = {
        'type': 'FeatureCollection',
        'features': features
    }

    return JsonResponse(geojson_data)


@login_required
@require_http_methods(["POST"])
def run_conflict_detection_htmx(request, project_id):
    """Run conflict detection and return results via HTMX"""
    try:
        project = get_object_or_404(Project, id=project_id)

        # Get detection parameters
        buffer_distance = float(request.POST.get('buffer_distance', '10'))
        utility_types = request.POST.getlist('utility_types')

        # Run spatial conflict detection

        results = run_conflict_detection(
            project=project,
            buffer_distance=buffer_distance,
            utility_types=utility_types
        )

        context = {
            'project': project,
            'results': results,
            'conflicts_found': results.get('conflicts_found', 0),
            'new_conflicts': results.get('new_conflicts', []),
        }

        return render(request, 'components/mapping/conflict_detection_results.html', context)

    except Exception as e:
        logger.error(f"Conflict detection error: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error running conflict detection</div>', status=500)


@login_required
@require_http_methods(["GET"])
def project_conflicts_3d_htmx(request, project_id):
    """HTMX endpoint for 3D conflict visualization data"""
    project = get_object_or_404(Project, id=project_id)

    # Get conflicts with 3D data
    conflicts = Conflict.objects.filter(
        project=project,
        utility1__location__isnull=False,
        utility2__location__isnull=False
    ).select_related('utility1', 'utility2')

    # Process conflicts for 3D visualization
    conflicts_3d = []
    for conflict in conflicts:
        conflict_data = {
            'id': conflict.id,
            'description': conflict.description,
            'severity': conflict.severity or 'medium',
            'utility1': {
                'name': conflict.utility1.company,
                'type': conflict.utility1.utility_type,
                'geometry': json.loads(conflict.utility1.location.geojson),
                'depth': getattr(conflict.utility1, 'depth', 3),  # Default depth
            },
            'utility2': {
                'name': conflict.utility2.company,
                'type': conflict.utility2.utility_type,
                'geometry': json.loads(conflict.utility2.location.geojson),
                'depth': getattr(conflict.utility2, 'depth', 3),  # Default depth
            }
        }
        conflicts_3d.append(conflict_data)

    context = {
        'project': project,
        'conflicts_3d': conflicts_3d,
        'total_conflicts': len(conflicts_3d),
    }

    return render(request, 'components/mapping/3d_conflict_list.html', context)


# ======= SEARCH AND FILTER HTMX FUNCTIONS =======

@login_required
@require_http_methods(["GET"])
def projects_search(request):
    """Search projects via HTMX"""
    query = request.GET.get('q', '').strip()
    user = request.user

    if not query:
        return HttpResponse('<div class="text-muted">Enter search terms</div>')

    # Search user's projects
    projects = Project.objects.filter(
        Q(manager_id=user.id) | 
        Q(coordinator_id=str(user.id)) |
        Q(egis_project_manager=user.username)
    ).filter(
        Q(name__icontains=query) |
        Q(description__icontains=query) |
        Q(project_number__icontains=query)
    ).order_by('-updated_at')[:20]

    context = {
        'projects': projects,
        'query': query,
        'user': user,
    }

    return render(request, 'components/search/project_results.html', context)


@login_required
@require_http_methods(["GET"])
def projects_filter(request):
    """Filter projects by status via HTMX"""
    status_filter = request.GET.get('status', 'all')
    rag_filter = request.GET.get('rag_status', 'all')
    user = request.user

    # Get user's projects
    projects = Project.objects.filter(
        Q(manager_id=user.id) | 
        Q(coordinator_id=str(user.id)) |
        Q(egis_project_manager=user.username)
    )

    # Apply status filter
    if status_filter != 'all':
        projects = projects.filter(status=status_filter)

    # Apply RAG status filter
    if rag_filter != 'all':
        projects = projects.filter(rag_status=rag_filter)

    projects = projects.order_by('-updated_at')[:50]

    context = {
        'projects': projects,
        'status_filter': status_filter,
        'rag_filter': rag_filter,
        'user': user,
    }

    return render(request, 'components/projects/filtered_list.html', context)


@login_required
@require_http_methods(["POST"])
def projects_sort(request):
    """Sort projects and return updated list"""
    sort_by = request.POST.get('sort_by', 'updated_at')
    current_order = request.session.get(f'project_sort_{sort_by}', 'desc')

    # Toggle sort order
    new_order = 'asc' if current_order == 'desc' else 'desc'
    request.session[f'project_sort_{sort_by}'] = new_order

    user = request.user

    # Get user's projects
    projects = Project.objects.filter(
        Q(manager_id=user.id) | 
        Q(coordinator_id=str(user.id)) |
        Q(egis_project_manager=user.username)
    )

    # Apply sorting
    if new_order == 'asc':
        projects = projects.order_by(sort_by)
    else:
        projects = projects.order_by(f'-{sort_by}')

    projects = projects[:50]

    context = {
        'projects': projects,
        'sort_by': sort_by,
        'sort_order': new_order,
        'user': user,
    }

    return render(request, 'components/projects/sorted_list.html', context)


@login_required
@require_http_methods(["GET"])
def tasks_filter(request):
    """Filter tasks by various criteria via HTMX"""
    status_filter = request.GET.get('status', 'all')
    priority_filter = request.GET.get('priority', 'all')
    project_filter = request.GET.get('project', 'all')
    user = request.user

    # Get user's tasks
    tasks = Task.objects.filter(assigned_to=user)

    # Apply filters
    if status_filter == 'completed':
        tasks = tasks.filter(completed=True)
    elif status_filter == 'pending':
        tasks = tasks.filter(completed=False)

    if priority_filter != 'all':
        tasks = tasks.filter(priority=priority_filter)

    if project_filter != 'all':
        tasks = tasks.filter(project_id=project_filter)

    tasks = tasks.select_related('project').order_by('-created_at')[:50]

    # Get available projects for filter dropdown
    user_projects = Project.objects.filter(
        Q(manager_id=user.id) | 
        Q(coordinator_id=str(user.id))
    ).order_by('name')

    context = {
        'tasks': tasks,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'project_filter': project_filter,
        'user_projects': user_projects,
        'user': user,
    }

    return render(request, 'components/tasks/filtered_list.html', context)


@login_required
@require_http_methods(["POST"])
def create_task_htmx(request):
    """Create a new task via HTMX"""
    try:
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        priority = request.POST.get('priority', 'medium')
        project_id = request.POST.get('project_id')
        due_date = request.POST.get('due_date')

        if not title:
            return HttpResponse('<div class="alert alert-danger">Task title is required</div>', status=400)

        # Create task data
        task_data = {
            'title': title,
            'description': description,
            'priority': priority,
            'assigned_to': request.user,
            'completed': False,
        }

        # Add project if specified
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
                task_data['project'] = project
            except Project.DoesNotExist:
                pass

        # Add due date if specified
        if due_date:
            try:
                task_data['due_date'] = datetime.strptime(due_date, '%Y-%m-%d').date()
            except ValueError:
                pass

        task = Task.objects.create(**task_data)

        context = {
            'task': task,
            'user': request.user,
        }

        return render(request, 'components/tasks/task_item.html', context)

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error creating task</div>', status=500)


@login_required
@require_http_methods(["POST"])
def update_task_status_htmx(request, task_id):
    """Update task status via HTMX"""
    try:
        task = get_object_or_404(Task, id=task_id, assigned_to=request.user)

        new_status = request.POST.get('status')
        if new_status == 'completed':
            task.completed = True
            task.completed_at = timezone.now()
        elif new_status == 'pending':
            task.completed = False
            task.completed_at = None

        task.save()

        context = {
            'task': task,
            'user': request.user,
        }

        return render(request, 'components/tasks/task_item.html', context)

    except Task.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Task not found</div>', status=404)
    except Exception as e:
        logger.error(f"Error updating task: {str(e)}")
        return HttpResponse('<div class="alert alert-danger">Error updating task</div>', status=500)


# ======= FINANCIAL AND EXPORT HTMX FUNCTIONS =======

@login_required
@require_http_methods(["GET"])
def project_financial_dashboard_htmx(request, project_id):
    """HTMX endpoint for project financial dashboard"""
    project = get_object_or_404(Project, id=project_id)

    # Calculate financial metrics
    financial_data = {
        'contract_amount': project.contract_amount or 0,
        'hours_logged': 0,  # Calculate from TimeEntry
        'expenses_total': 0,  # Calculate from expenses if available
        'profit_margin': 0,  # Calculate based on costs vs revenue
    }

    # Get time entries for this project
    time_entries = TimeEntry.objects.filter(project=project)
    total_hours = time_entries.aggregate(
        total=Sum('duration_minutes')
    )['total'] or 0
    financial_data['hours_logged'] = total_hours / 60.0  # Convert to hours

    # Calculate labor costs (estimate $100/hour)
    labor_cost = financial_data['hours_logged'] * 100

    if financial_data['contract_amount'] > 0:
        financial_data['profit_margin'] = (
            (financial_data['contract_amount'] - labor_cost) / 
            financial_data['contract_amount'] * 100
        )

    # Get recent financial activities
    recent_activities = [
        # This would typically come from invoice or expense models
        {
            'date': timezone.now().date(),
            'type': 'time_entry',
            'description': f'Time logged: {financial_data["hours_logged"]:.1f} hours',
            'amount': labor_cost,
        }
    ]

    context = {
        'project': project,
        'financial_data': financial_data,
        'recent_activities': recent_activities,
        'labor_cost': labor_cost,
    }

    return render(request, 'components/projects/financial_dashboard.html', context)


@login_required
@require_http_methods(["GET"])
def project_financial_export_htmx(request, project_id):
    """Export project financial data via HTMX"""
    project = get_object_or_404(Project, id=project_id)
    export_format = request.GET.get('format', 'csv')

    # Get financial data
    time_entries = TimeEntry.objects.filter(project=project).select_related('user')

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{project.name}_financial_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)
        writer.writerow(['Date', 'User', 'Hours', 'Description', 'Billable', 'Cost Estimate'])

        for entry in time_entries:
            hours = (entry.duration_minutes or 0) / 60.0
            cost = hours * 100  # $100/hour estimate
            writer.writerow([
                entry.start_time.date(),
                entry.user.get_full_name(),
                f'{hours:.2f}',
                entry.description or '',
                'Yes' if entry.billable else 'No',
                f'${cost:.2f}'
            ])

        return response

    elif export_format == 'json':
        export_data = {
            'project': {
                'name': project.name,
                'contract_amount': project.contract_amount,
                'export_date': timezone.now().isoformat(),
            },
            'time_entries': [],
            'summary': {
                'total_hours': 0,
                'total_cost': 0,
                'billable_hours': 0,
            }
        }

        total_hours = 0
        total_cost = 0
        billable_hours = 0

        for entry in time_entries:
            hours = (entry.duration_minutes or 0) / 60.0
            cost = hours * 100

            export_data['time_entries'].append({
                'date': entry.start_time.date().isoformat(),
                'user': entry.user.get_full_name(),
                'hours': round(hours, 2),
                'description': entry.description,
                'billable': entry.billable,
                'cost_estimate': round(cost, 2),
            })

            total_hours += hours
            total_cost += cost
            if entry.billable:
                billable_hours += hours

        export_data['summary'] = {
            'total_hours': round(total_hours, 2),
            'total_cost': round(total_cost, 2),
            'billable_hours': round(billable_hours, 2),
        }

        return JsonResponse(export_data)

    return HttpResponse('Invalid export format', status=400)

# ========== ADDITIONAL HTMX FUNCTIONS - FINAL MIGRATION BATCH ==========

@login_required
def timeline_data_htmx(request, project_id):
    """HTMX endpoint for timeline data"""
    project = get_object_or_404(Project, pk=project_id)
    tasks = project.tasks.select_related('assigned_to').order_by('start_date', 'created_at')

    # Get view mode from request
    view_mode = request.GET.get('view_mode', 'gantt')

    # Filter tasks based on view mode
    if view_mode == 'critical':
        timeline_service = TimelineService(project)
        critical_path = timeline_service.calculate_critical_path(tasks)
        tasks = tasks.filter(id__in=critical_path)

    context = {
        'tasks': tasks,
        'project': project,
        'view_mode': view_mode
    }

    return render(request, 'components/timeline/timeline_chart.html', context)


@login_required 
def task_timeline_update_htmx(request):
    """HTMX endpoint for updating task timeline data"""
    task_id = request.POST.get('task_id')
    field = request.POST.get('field')
    value = request.POST.get('value')

    try:
        task = get_object_or_404(Task, id=task_id)

        # Update the specific field
        if field == 'start_date':
            task.start_date = datetime.strptime(value, '%Y-%m-%d').date() if value else None
        elif field == 'end_date':
            task.end_date = datetime.strptime(value, '%Y-%m-%d').date() if value else None
        elif field == 'progress_percentage':
            task.progress_percentage = int(value) if value else 0
        elif field == 'duration_days':
            task.duration_days = int(value) if value else None
        elif field == 'milestone':
            task.milestone = value.lower() == 'true'
        elif field == 'assigned_to':
            if value:
                user = get_object_or_404(User, id=value)
                task.assigned_to = user
            else:
                task.assigned_to = None

        task.save()

        # Return updated task row
        context = {'task': task, 'project': task.project}
        return render(request, 'components/timeline/task_bar.html', context)

    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=400)


@login_required
def timeline_task_move_htmx(request):
    """HTMX endpoint for drag-and-drop task date changes"""
    task_id = request.POST.get('task_id')
    new_start = request.POST.get('new_start')
    new_end = request.POST.get('new_end')

    try:
        task = get_object_or_404(Task, id=task_id)

        if new_start:
            task.start_date = datetime.strptime(new_start, '%Y-%m-%d').date()
        if new_end:
            task.end_date = datetime.strptime(new_end, '%Y-%m-%d').date()

        # Validate dependencies
        timeline_service = TimelineService(task.project)

        # Check for conflicts
        conflicts = timeline_service.get_task_conflicts(task.project.tasks.all())
        task_conflicts = [c for c in conflicts if c.get('task_id') == task_id]

        if task_conflicts:
            return HttpResponse(
                f'Schedule conflict: {task_conflicts[0]["message"]}', 
                status=400
            )

        task.save()

        # Return success response with updated timeline
        return render(request, 'components/timeline/task_bar.html', {
            'task': task, 
            'project': task.project
        })

    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=400)


@login_required
def dependency_management_htmx(request):
    """HTMX endpoint for managing task dependencies"""
    task_id = request.POST.get('task_id')
    dependency_id = request.POST.get('dependency_id')
    action = request.POST.get('action')  # 'add' or 'remove'

    try:
        task = get_object_or_404(Task, id=task_id)

        if action == 'add':
            dependencies = list(task.dependencies) if task.dependencies else []
            if dependency_id not in dependencies:
                # Validate the dependency won't create a cycle
                timeline_service = TimelineService(task.project)

                new_deps = dependencies + [dependency_id]
                is_valid, error_msg = timeline_service.validate_dependencies(task_id, new_deps)

                if not is_valid:
                    return HttpResponse(f'Dependency error: {error_msg}', status=400)

                dependencies.append(dependency_id)
                task.dependencies = dependencies
                task.save()

        elif action == 'remove':
            dependencies = list(task.dependencies) if task.dependencies else []
            if dependency_id in dependencies:
                dependencies.remove(dependency_id)
                task.dependencies = dependencies
                task.save()

        # Return updated dependency list
        context = {'task': task, 'project': task.project}
        return render(request, 'components/timeline/dependency_line.html', context)

    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=400)


@login_required
def timeline_metrics_htmx(request, project_id):
    """HTMX endpoint for timeline metrics panel"""
    project = get_object_or_404(Project, pk=project_id)

    timeline_service = TimelineService(project)

    tasks = project.tasks.all()
    metrics = timeline_service.calculate_project_metrics(tasks)
    critical_path = timeline_service.calculate_critical_path(tasks)
    conflicts = timeline_service.get_task_conflicts(tasks)

    context = {
        'project': project,
        'metrics': metrics,
        'critical_path_count': len(critical_path),
        'conflicts_count': len(conflicts),
        'conflicts': conflicts[:3]  # Show top 3 conflicts
    }

    return render(request, 'components/timeline/metrics_panel.html', context)


@login_required
def calculate_critical_path_htmx(request, project_id):
    """HTMX endpoint for recalculating critical path"""
    project = get_object_or_404(Project, pk=project_id)

    try:
        timeline_service = TimelineService(project)

        tasks = project.tasks.all()
        critical_path = timeline_service.calculate_critical_path(tasks)

        # Get critical path tasks for display
        critical_tasks = tasks.filter(id__in=critical_path)

        context = {
            'project': project,
            'critical_tasks': critical_tasks,
            'critical_path_count': len(critical_path),
            'calculation_time': timezone.now()
        }

        # Trigger timeline update
        response = render(request, 'components/timeline/critical_path_result.html', context)
        response['HX-Trigger'] = 'timeline-update'
        return response

    except Exception as e:
        logger.error(f"Error calculating critical path: {e}")
        return HttpResponse(f'Error calculating critical path: {str(e)}', status=500)


@login_required
def timeline_export_htmx(request, project_id):
    """HTMX endpoint for timeline data export"""
    project = get_object_or_404(Project, pk=project_id)
    export_format = request.GET.get('format', 'csv')

    try:
        TimelineService(project)

        tasks = project.tasks.select_related('assigned_to').order_by('start_date', 'created_at')

        if export_format == 'csv':
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="{project.name}_timeline.csv"'

            writer = csv.writer(response)
            writer.writerow([
                'Task ID', 'Title', 'Description', 'Status', 'Priority',
                'Start Date', 'End Date', 'Duration (Days)', 'Progress (%)',
                'Assigned To', 'Critical Path', 'Milestone', 'Dependencies'
            ])

            for task in tasks:
                writer.writerow([
                    task.id,
                    task.title,
                    task.description or '',
                    task.status,
                    task.priority,
                    task.start_date.strftime('%Y-%m-%d') if task.start_date else '',
                    task.end_date.strftime('%Y-%m-%d') if task.end_date else '',
                    task.calculated_duration,
                    task.progress_percentage,
                    task.assigned_to.get_full_name() if task.assigned_to else '',
                    'Yes' if task.critical_path else 'No',
                    'Yes' if task.milestone else 'No',
                    '; '.join(task.dependencies) if task.dependencies else ''
                ])

            return response

        elif export_format == 'json':
            timeline_data = {
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'export_date': timezone.now().isoformat()
                },
                'tasks': []
            }

            for task in tasks:
                timeline_data['tasks'].append({
                    'id': task.id,
                    'title': task.title,
                    'description': task.description,
                    'status': task.status,
                    'priority': task.priority,
                    'start_date': task.start_date.isoformat() if task.start_date else None,
                    'end_date': task.end_date.isoformat() if task.end_date else None,
                    'duration_days': task.calculated_duration,
                    'progress_percentage': task.progress_percentage,
                    'assigned_to': task.assigned_to.get_full_name() if task.assigned_to else None,
                    'critical_path': task.critical_path,
                    'milestone': task.milestone,
                    'dependencies': task.dependencies or []
                })

            response = HttpResponse(
                json.dumps(timeline_data, indent=2),
                content_type='application/json'
            )
            response['Content-Disposition'] = f'attachment; filename="{project.name}_timeline.json"'
            return response

        else:
            return HttpResponse('Unsupported export format', status=400)

    except Exception as e:
        logger.error(f"Error exporting timeline: {e}")
        return HttpResponse(f'Error exporting timeline: {str(e)}', status=500)


@login_required
@require_http_methods(["POST"])
def run_conflict_detection_htmx(request, project_id):
    """Run conflict detection via HTMX"""
    project = get_object_or_404(Project, pk=project_id)

    # Simulate conflict detection (replace with actual algorithm)
    conflicts_found = random.randint(0, 5)

    return render(request, 'partials/conflict_results.html', {
        'project': project,
        'conflicts_found': conflicts_found,
        'timestamp': timezone.now()
    })


@login_required
def project_team_activity_htmx(request, project_id):
    """HTMX endpoint for project team activity timeline with enhanced activity generation"""
    try:
        project = get_object_or_404(Project, pk=project_id)

        # Get real activities from database
        real_activities = project.activities.select_related('user').order_by('-timestamp')[:5]

        # Generate comprehensive activity data for demonstration
        activities = list(real_activities)

        # If we don't have enough real activities, add sample activities
        if len(activities) < 10:

            # Sample activity types with realistic timestamps
            sample_activity_types = [
                {
                    'action': 'updated',
                    'target': 'project timeline',
                    'user_name': 'Sarah Johnson',
                    'timestamp': timezone.now() - timedelta(minutes=15),
                    'description': 'Updated project timeline with new milestones'
                },
                {
                    'action': 'completed',
                    'target': 'utility survey for Block 4',
                    'user_name': 'Mike Chen',
                    'timestamp': timezone.now() - timedelta(hours=1),
                    'description': 'Completed field survey and uploaded CAD files'
                },
                {
                    'action': 'commented',
                    'target': 'conflict resolution for gas line',
                    'user_name': 'Jessica Davis',
                    'timestamp': timezone.now() - timedelta(hours=2),
                    'description': 'Added notes about utility company response'
                },
                {
                    'action': 'uploaded',
                    'target': 'revised site plan (Rev C)',
                    'user_name': 'Alex Rivera',
                    'timestamp': timezone.now() - timedelta(hours=3),
                    'description': 'Uploaded updated drawings with client revisions'
                },
                {
                    'action': 'created',
                    'target': 'new coordination task',
                    'user_name': 'Taylor Wong',
                    'timestamp': timezone.now() - timedelta(hours=4),
                    'description': 'Created task for water main relocation coordination'
                },
                {
                    'action': 'updated',
                    'target': 'project budget',
                    'user_name': 'Sarah Johnson',
                    'timestamp': timezone.now() - timedelta(hours=6),
                    'description': 'Updated budget with additional coordination costs'
                },
                {
                    'action': 'completed',
                    'target': 'permit application review',
                    'user_name': 'Jessica Davis',
                    'timestamp': timezone.now() - timedelta(hours=8),
                    'description': 'Reviewed and approved permit applications'
                },
                {
                    'action': 'commented',
                    'target': 'traffic control plan',
                    'user_name': 'Mike Chen',
                    'timestamp': timezone.now() - timedelta(hours=10),
                    'description': 'Provided feedback on traffic management strategy'
                },
                {
                    'action': 'uploaded',
                    'target': 'field photos from site visit',
                    'user_name': 'Alex Rivera',
                    'timestamp': timezone.now() - timedelta(hours=12),
                    'description': 'Uploaded photos from morning site inspection'
                },
                {
                    'action': 'created',
                    'target': 'weekly progress report',
                    'user_name': 'Sarah Johnson',
                    'timestamp': timezone.now() - timedelta(days=1),
                    'description': 'Generated weekly progress report for client'
                }
            ]

            # Create mock activity objects for the ones we need
            for i, activity_data in enumerate(sample_activity_types[:(10 - len(activities))]):
                # Create a mock activity object that behaves like the real one
                class MockActivity:
                    def __init__(self, action, target, user_name, timestamp, description):
                        self.action = action
                        self.target = target
                        self.timestamp = timestamp
                        self.description = description

                        # Create a mock user object
                        class MockUser:
                            def __init__(self, name):
                                parts = name.split()
                                self.first_name = parts[0] if parts else ''
                                self.last_name = parts[-1] if len(parts) > 1 else ''
                                self.username = name.lower().replace(' ', '.')

                        self.user = MockUser(user_name)

                mock_activity = MockActivity(**activity_data)
                activities.append(mock_activity)

        # Get enhanced team data
        view = ProjectDetailView()
        team_data = view._get_team_data(project)

        context = {
            'project': project,
            'activities': activities,
            'team_members': team_data['team_members'],
            'team_count': team_data['team_count'],
            'online_count': team_data['online_count'],
            'active_count': team_data['active_count']
        }

        return render(request, 'components/projects/team_activity_timeline.html', context)

    except Exception as e:
        return HttpResponse(f'Error loading team activity: {str(e)}', status=400)


@login_required
def project_team_member_action_htmx(request, project_id):
    """HTMX endpoint for team member quick actions"""
    try:
        project = get_object_or_404(Project, pk=project_id)
        action = request.POST.get('action')
        member_id = request.POST.get('member_id')

        if action == 'send_message':
            message_content = request.POST.get('message', '')

            # In a real implementation, this would send a message through the chat system
            # For now, we'll create a notification or activity
            Activity.objects.create(
                user=request.user,
                project=project,
                action='sent message',
                target=f'team member (ID: {member_id})',
                description=f'Sent message: "{message_content[:50]}..."' if len(message_content) > 50 else f'Sent message: "{message_content}"'
            )

            return HttpResponse(
                '<div class="text-green-600 text-sm">✓ Message sent successfully</div>',
                headers={'HX-Trigger': 'team-activity-refresh'}
            )

        elif action == 'assign_task':
            task_title = request.POST.get('task_title', '')

            # Create a new task assignment
            Task.objects.create(
                project=project,
                title=task_title,
                description=f'Task assigned to team member (ID: {member_id})',
                assigned_to=request.user,  # In real implementation, would assign to the target member
                created_by=request.user,
                due_date=timezone.now() + timedelta(days=7)
            )

            return HttpResponse(
                '<div class="text-green-600 text-sm">✓ Task assigned successfully</div>',
                headers={'HX-Trigger': 'team-activity-refresh'}
            )

        elif action == 'view_profile':
            # Return a profile modal content
            return render(request, 'components/projects/team_member_profile_modal.html', {
                'member_id': member_id,
                'project': project
            })

        return HttpResponse('<div class="text-red-600 text-sm">Action not recognized</div>')

    except Exception as e:
        return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>')


@login_required
def project_team_invite_htmx(request, project_id):
    """HTMX endpoint for team member invitation modal"""
    try:
        project = get_object_or_404(Project, pk=project_id)

        # Get available users who are not already on the project
        available_users = User.objects.filter(is_active=True).exclude(
            id__in=project.team_members.values_list('user_id', flat=True) if hasattr(project, 'team_members') else []
        )[:10]  # Limit to first 10 for performance

        context = {
            'project': project,
            'available_users': available_users,
        }

        return render(request, 'components/projects/team_invite_modal.html', context)

    except Exception as e:
        return HttpResponse(f'<div class="text-red-600 text-sm">Error loading invite form: {str(e)}</div>')


@login_required
def project_budget_chart_data_htmx(request, project_id):
    """HTMX endpoint for budget chart data (real-time updates)"""
    try:
        project = get_object_or_404(Project, pk=project_id)

        # Get updated financial data
        view = ProjectDetailView()
        financial_data = view._calculate_financial_metrics(project)
        metrics = financial_data['financial_metrics']

        # Prepare chart data for real-time updates
        chart_data = {
            'budget_chart': {
                'labels': ['Billed to Date', 'Work in Progress', 'Remaining Budget'],
                'datasets': [{
                    'data': [
                        metrics['billed_to_date'],
                        metrics['wip'],
                        metrics['remaining_budget']
                    ],
                    'backgroundColor': ['#10B981', '#F59E0B', '#E5E7EB'],
                    'borderWidth': 2,
                    'borderColor': '#FFFFFF'
                }]
            },
            'timeline_chart': {
                'labels': ['Contract Value', 'Current Costs', 'Projected Final Cost'],
                'datasets': [{
                    'label': 'Financial Timeline',
                    'data': [
                        metrics['contract_amount'],
                        metrics['current_cost'],
                        metrics['estimated_final_cost']
                    ],
                    'backgroundColor': ['#3B82F6', '#EF4444', '#8B5CF6'],
                    'borderColor': ['#2563EB', '#DC2626', '#7C3AED'],
                    'borderWidth': 2
                }]
            }
        }

        return JsonResponse(chart_data)

    except Exception as e:
        return JsonResponse({'error': f'Chart data error: {str(e)}'}, status=400)


@login_required
def project_conversation_create_htmx(request, project_id):
    """HTMX endpoint for creating a new project conversation"""
    try:
        project = get_object_or_404(Project, pk=project_id)

        # Get project team members or available users
        available_users = User.objects.filter(is_active=True).exclude(id=request.user.id)[:20]

        context = {
            'project': project,
            'available_users': available_users,
        }

        return render(request, 'components/projects/conversation_create_modal.html', context)

    except Exception as e:
        return HttpResponse(f'<div class="text-red-600">Error: {str(e)}</div>')


@login_required
@require_http_methods(["POST"])
def project_conversation_create_post_htmx(request, project_id):
    """HTMX endpoint for creating a new project conversation (POST)"""
    try:
        project = get_object_or_404(Project, pk=project_id)

        conversation_name = request.POST.get('name', '').strip()
        conversation_type = request.POST.get('type', 'project')
        participant_ids = request.POST.getlist('participants')

        # Create the conversation
        conversation = Conversation.objects.create(
            name=conversation_name or f"{project.name} Discussion",
            conversation_type=conversation_type,
            project=project,
            created_by=request.user
        )

        # Add creator as participant
        conversation.add_participant(request.user)

        # Add selected participants
        for user_id in participant_ids:
            try:
                user = User.objects.get(id=user_id)
                conversation.add_participant(user)
            except User.DoesNotExist:
                continue

        # Return success and trigger refresh
        return HttpResponse(
            '<div class="text-green-600">Conversation created successfully!</div>',
            headers={
                'HX-Trigger': 'conversationCreated',
                'HX-Refresh': 'true'
            }
        )

    except Exception as e:
        return HttpResponse(f'<div class="text-red-600">Error creating conversation: {str(e)}</div>')


@login_required
def project_conversation_select_htmx(request, project_id, conversation_id):
    """HTMX endpoint for selecting a project conversation and loading its messages"""
    try:
        project = get_object_or_404(Project, pk=project_id)
        conversation = get_object_or_404(Conversation, pk=conversation_id, project=project)

        # Ensure user is a participant
        conversation.add_participant(request.user)

        # Mark conversation as read
        member = conversation.members.filter(user=request.user).first()
        if member:
            member.mark_read()

        # Get messages
        messages = conversation.messages.select_related('user').order_by('-created_at')[:50]
        messages = list(reversed(messages))  # Show oldest first

        context = {
            'conversation': conversation,
            'messages': messages,
            'user': request.user,
            'project': project,
        }

        return render(request, 'components/messages/message_thread.html', context)

    except Exception as e:
        return HttpResponse(f'<div class="text-red-600">Error loading conversation: {str(e)}</div>')


@login_required
def project_conversations_search_htmx(request, project_id):
    """HTMX endpoint for searching project conversations"""
    try:
        project = get_object_or_404(Project, pk=project_id)
        search_query = request.GET.get('search', '').strip()

        conversations = project.conversations.filter(is_active=True)

        if search_query:
            conversations = conversations.filter(
                Q(name__icontains=search_query) | 
                Q(messages__content__icontains=search_query)
            ).distinct()

        conversations = conversations.order_by('-last_message_at')

        # Add unread counts
        for conversation in conversations:
            conversation.unread_count = conversation.get_unread_count_for_user(request.user)

        context = {
            'conversations': conversations,
            'project': project,
            'user': request.user,
        }

        return render(request, 'components/projects/conversation_list.html', context)

    except Exception as e:
        return HttpResponse(f'<div class="text-red-600">Error searching conversations: {str(e)}</div>')


@login_required
def messages_list_htmx(request):
    """HTMX endpoint for loading conversation list"""
    request.GET.get('category', 'all')
    search_query = request.GET.get('search', '')

    # Get user's conversations
    conversations_qs = Conversation.objects.filter(
        participants=request.user,
        is_active=True
    ).select_related('created_by', 'project').prefetch_related('participants')

    # Apply search filter
    if search_query:
        conversations_qs = conversations_qs.filter(
            Q(name__icontains=search_query) |
            Q(participants__first_name__icontains=search_query) |
            Q(participants__last_name__icontains=search_query) |
            Q(project__name__icontains=search_query)
        ).distinct()

    conversations = conversations_qs.order_by('-last_message_at', '-created_at')[:20]

    # Add unread counts
    conversations_with_unread = []
    for conv in conversations:
        unread_count = conv.get_unread_count_for_user(request.user)
        conversations_with_unread.append({
            'conversation': conv,
            'unread_count': unread_count,
            'last_message': conv.get_last_message(),
        })

    context = {
        'conversations': conversations_with_unread,
        'search_query': search_query,
    }

    return render(request, 'components/messages/conversation_list.html', context)


@login_required
def message_thread_htmx(request, conversation_id):
    """HTMX endpoint for loading message thread"""
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )

        # Mark conversation as read
        member = conversation.members.filter(user=request.user).first()
        if member:
            member.mark_read()

        # Get messages with pagination
        page = request.GET.get('page', 1)
        messages = conversation.messages.select_related('user').order_by('created_at')
        paginator = Paginator(messages, 50)
        page_obj = paginator.get_page(page)

        context = {
            'conversation': conversation,
            'messages': page_obj,
            'user': request.user,
        }

        return render(request, 'components/messages/message_thread.html', context)

    except Conversation.DoesNotExist:
        return HttpResponse('Conversation not found', status=404)


@login_required
@require_http_methods(["POST"])
def message_send_htmx(request):
    """HTMX endpoint for sending messages"""
    conversation_id = request.POST.get('conversation_id')
    content = request.POST.get('content', '').strip()

    if not content or not conversation_id:
        return HttpResponse('<div class="alert alert-warning">Message content is required</div>', status=400)

    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
    except Conversation.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Conversation not found</div>', status=404)

    # Create the message
    message = ChatMessage.objects.create(
        user=request.user,
        conversation=conversation,
        content=content,
        message_type='text'
    )

    # Mark conversation as read for sender
    member = conversation.members.filter(user=request.user).first()
    if member:
        member.mark_read()

    # Prepare message for rendering
    message_data = {
        'id': message.id,
        'content': message.content,
        'user': message.user,
        'user_initials': message.user.get_initials(),
        'is_own': True,
        'created_at': message.created_at,
        'message_type': message.message_type,
    }

    context = {
        'message': message_data,
    }

    # Return the new message HTML
    return render(request, 'components/messages/message_item.html', context)


@login_required
def message_search_htmx(request):
    """HTMX endpoint for searching messages, conversations, and users"""
    query = request.GET.get('q', '').strip()

    if not query or len(query) < 2:
        return render(request, 'components/messages/search_results.html', {'query': ''})

    # Search conversations
    conversations = Conversation.objects.filter(
        participants=request.user,
        is_active=True
    ).filter(
        Q(name__icontains=query) |
        Q(project__name__icontains=query)
    ).select_related('project').prefetch_related('participants')[:5]

    # Search messages
    messages = ChatMessage.objects.filter(
        conversation__participants=request.user,
        content__icontains=query
    ).select_related('user', 'conversation', 'conversation__project').order_by('-created_at')[:10]

    # Search users (for starting new conversations)
    users = User.objects.filter(
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(username__icontains=query) |
        Q(email__icontains=query)
    ).exclude(id=request.user.id)[:5]

    context = {
        'query': query,
        'conversations': conversations,
        'messages': messages,
        'users': users,
    }

    return render(request, 'components/messages/search_results.html', context)


@login_required
@require_http_methods(["POST"])
def conversation_create_htmx(request):
    """HTMX endpoint for creating new conversations"""
    name = request.POST.get('name', '').strip()
    conversation_type = request.POST.get('type', 'group')
    project_id = request.POST.get('project_id')
    participant_ids = request.POST.getlist('participants')

    # Validate type
    valid_types = ['direct', 'group', 'project', 'channel']
    if conversation_type not in valid_types:
        conversation_type = 'group'

    # Create conversation
    conversation_data = {
        'name': name if name else None,
        'conversation_type': conversation_type,
        'created_by': request.user,
    }

    # Handle project association
    if project_id:
        try:
            project = Project.objects.get(id=project_id)
            conversation_data['project'] = project
            if not name:
                conversation_data['name'] = f"{project.name} Discussion"
        except Project.DoesNotExist:
            pass

    conversation = Conversation.objects.create(**conversation_data)

    # Add creator as participant
    conversation.add_participant(request.user)

    # Add other participants
    for participant_id in participant_ids:
        try:
            participant = User.objects.get(id=participant_id)
            conversation.add_participant(participant)
        except User.DoesNotExist:
            continue

    # Return the conversation list item
    context = {
        'conversation': conversation,
        'user': request.user,
    }

    response = render(request, 'components/messages/conversation_item.html', context)
    response['HX-Trigger'] = 'conversationCreated'
    return response


@login_required
@require_http_methods(["POST"])
def message_mark_read_htmx(request, message_id):
    """HTMX endpoint for marking a specific message as read"""
    try:
        message = ChatMessage.objects.get(
            id=message_id,
            conversation__participants=request.user
        )

        # Create or update read receipt
        read_receipt, created = MessageRead.objects.get_or_create(
            message=message,
            user=request.user,
            defaults={'read_at': timezone.now()}
        )

        if not created and not read_receipt.read_at:
            read_receipt.read_at = timezone.now()
            read_receipt.save()

        return HttpResponse('')  # Success response

    except ChatMessage.DoesNotExist:
        return HttpResponse('Message not found', status=404)


@login_required
def map_layers_htmx(request, project_id):
    """HTMX endpoint for dynamic layer toggling"""
    project = get_object_or_404(Project, pk=project_id)
    layer_type = request.GET.get('layer', 'all')

    utilities_geojson = []
    conflicts_geojson = []

    if layer_type in ['all', 'utilities']:
        utility_lines = UtilityLineData.objects.filter(project=project).select_related(
            'utility', 'line_style'
        )

        for utility_line in utility_lines:
            if utility_line.geometry:
                try:
                    geom = utility_line.geometry.transform(4326, clone=True)
                    feature = {
                        'type': 'Feature',
                        'id': utility_line.id,
                        'geometry': json.loads(geom.json),
                        'properties': {
                            'name': utility_line.name,
                            'utility_type': utility_line.utility_type,
                            'installation_type': utility_line.installation_type,
                            'style': {
                                'color': utility_line.line_style.line_color if utility_line.line_style else '#333333',
                                'weight': utility_line.line_style.line_weight if utility_line.line_style else 2
                            }
                        }
                    }
                    utilities_geojson.append(feature)
                except Exception as e:
                    logger.warning(f"Error processing geometry: {e}")

    if layer_type in ['all', 'conflicts']:
        conflicts = Conflict.objects.filter(project=project)

        for conflict in conflicts:
            if conflict.conflict_3d_geometry:
                try:
                    geom = conflict.conflict_3d_geometry.transform(4326, clone=True)
                    feature = {
                        'type': 'Feature',
                        'id': conflict.id,
                        'geometry': json.loads(geom.json),
                        'properties': {
                            'description': conflict.description,
                            'status': conflict.status,
                            'priority': conflict.priority,
                            'risk_score': conflict.risk_score
                        }
                    }
                    conflicts_geojson.append(feature)
                except Exception as e:
                    logger.warning(f"Error processing conflict geometry: {e}")

    return JsonResponse({
        'utilities': utilities_geojson,
        'conflicts': conflicts_geojson,
        'layer_type': layer_type
    })


@login_required
def internal_email_compose_htmx(request):
    """HTMX endpoint for composing internal emails"""

    if request.method == "GET":
        # Get available email addresses for dropdown
        available_emails = InternalEmail.objects.filter(is_active=True)

        # Get projects for quick addressing
        projects = Project.objects.filter(
            Q(created_by=request.user) | Q(members=request.user)
        ).distinct()[:20]

        # Get team members for addressing
        team_members = User.objects.filter(is_active=True).exclude(id=request.user.id)[:20]

        context = {
            'available_emails': available_emails,
            'projects': projects,
            'team_members': team_members,
        }

        return render(request, 'components/ai_communication/email_compose_form.html', context)

    elif request.method == "POST":
        try:
            ai_service = AICommunicationService()

            from_address = request.POST.get('from_address', '')
            to_addresses = request.POST.getlist('to_addresses')
            cc_addresses = request.POST.getlist('cc_addresses', [])
            subject = request.POST.get('subject', '')
            body = request.POST.get('body', '')
            html_body = request.POST.get('html_body', '')

            # Send internal email
            ai_service.send_internal_email(
                from_address=from_address,
                to_addresses=to_addresses,
                subject=subject,
                body=body,
                user=request.user,
                cc_addresses=cc_addresses,
                html_body=html_body
            )

            return HttpResponse(
                '<div class="text-green-600">Email sent successfully!</div>',
                headers={'HX-Trigger': 'emailSent'}
            )

        except Exception as e:
            logger.error(f"Error sending internal email: {str(e)}")
            return HttpResponse(
                f'<div class="text-red-600">Error sending email: {str(e)}</div>',
                status=400
            )


@login_required
@require_http_methods(["POST"])
def ai_command_process_htmx(request):
    """HTMX endpoint for processing AI commands"""

    try:
        command_id = request.POST.get('command_id')
        action = request.POST.get('action')  # 'confirm', 'reject', 'modify'

        if not command_id:
            return HttpResponse('<div class="text-red-600">Command ID required</div>', status=400)

        # For now, return a placeholder response since AICommand model might not exist
        if action == 'confirm':
            return HttpResponse(
                '<div class="text-green-600">Command executed successfully!</div>',
                headers={'HX-Trigger': 'commandExecuted'}
            )
        elif action == 'reject':
            return HttpResponse(
                '<div class="text-gray-600">Command rejected</div>',
                headers={'HX-Trigger': 'commandRejected'}
            )
        else:
            return HttpResponse('<div class="text-red-600">Invalid action</div>', status=400)

    except Exception as e:
        logger.error(f"Error processing AI command: {str(e)}")
        return HttpResponse(f'<div class="text-red-600">Error: {str(e)}</div>', status=400)


@login_required
def communication_insights_htmx(request):
    """HTMX endpoint for communication insights panel"""
    try:
        # Get insights for different time periods
        insights_7d = {'summary': 'Last 7 days insights'}
        insights_30d = {'summary': 'Last 30 days insights'}

        # Get project-specific insights if project_id provided
        project_id = request.GET.get('project_id')
        project_insights = None
        if project_id:
            project_insights = {'summary': f'Project {project_id} insights'}

        context = {
            'insights_7d': insights_7d,
            'insights_30d': insights_30d,
            'project_insights': project_insights,
            'project_id': project_id
        }

        return render(request, 'components/ai_communication/insights_panel.html', context)

    except Exception as e:
        logger.error(f"Error generating communication insights: {str(e)}")
        return HttpResponse('<div class="text-red-600">Error loading insights</div>')


@login_required
def scheduled_report_create_htmx(request):
    """HTMX endpoint for creating scheduled reports"""
    if not (request.user.is_staff or request.user.role in ['admin', 'project_manager']):
        return HttpResponse('Unauthorized', status=403)

    if request.method == 'GET':
        # Return the form
        context = {
            'recurrence_options': [
                ('hourly', 'Hourly'),
                ('daily', 'Daily'),
                ('weekly', 'Weekly'),
                ('monthly', 'Monthly'),
            ],
            'report_types': [
                ('scheduled', 'Stakeholder Analytics'),
                ('summary', 'Summary Report'),
                ('detailed', 'Detailed Report'),
            ]
        }
        return render(request, 'components/reports/scheduled_report_form.html', context)

    # Handle POST - create the report
    try:
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        report_type = request.POST.get('report_type', 'scheduled')
        recurrence = request.POST.get('recurrence', 'daily')
        recipients = request.POST.get('recipients', '').strip()

        if not name:
            return HttpResponse('Report name is required', status=400)

        # Parse recipients
        recipient_list = []
        if recipients:
            recipient_list = [email.strip() for email in recipients.split(',') if email.strip()]

        # Create schedule configuration
        schedule_config = {
            'recurrence': recurrence,
            'send_email': bool(recipient_list),
            'recipients': recipient_list,
            'include_allowed_users': False,
        }

        # Calculate next execution time
        now = timezone.now()
        if recurrence == 'hourly':
            next_execution = now + timedelta(hours=1)
        elif recurrence == 'daily':
            next_execution = now + timedelta(days=1)
        elif recurrence == 'weekly':
            next_execution = now + timedelta(weeks=1)
        elif recurrence == 'monthly':
            next_execution = now + timedelta(days=30)
        else:
            next_execution = now + timedelta(days=1)

        # Create the report
        AnalyticsReport.objects.create(
            name=name,
            description=description,
            report_type=report_type,
            created_by=request.user,
            is_scheduled=True,
            is_public=False,
            schedule_config=schedule_config,
            next_execution_at=next_execution,
            configuration={
                'created_via': 'web_interface',
                'auto_generated': True,
            }
        )

        # Return success message with redirect instruction
        return HttpResponse(
            '<div class="alert alert-success">Report scheduled successfully!</div>'
            '<script>setTimeout(() => window.location.reload(), 1500);</script>'
        )

    except Exception as e:
        logger.error(f"Error creating scheduled report: {str(e)}")
        return HttpResponse(f'Error creating report: {str(e)}', status=500)


@login_required
@require_http_methods(["POST"])
def scheduled_report_toggle_htmx(request, report_id):
    """HTMX endpoint for enabling/disabling scheduled reports"""
    try:
        report = AnalyticsReport.objects.get(id=report_id)

        # Check permissions
        if not (request.user.is_staff or request.user.role in ['admin', 'project_manager'] or report.created_by == request.user):
            return HttpResponse('Unauthorized', status=403)

        # Toggle the scheduled status
        report.is_scheduled = not report.is_scheduled
        report.save(update_fields=['is_scheduled'])

        status_text = "enabled" if report.is_scheduled else "disabled"
        status_class = "success" if report.is_scheduled else "warning"

        return HttpResponse(
            f'<div class="alert alert-{status_class}">Report {status_text} successfully!</div>'
            '<script>setTimeout(() => window.location.reload(), 1000);</script>'
        )

    except AnalyticsReport.DoesNotExist:
        return HttpResponse('Report not found', status=404)
    except Exception as e:
        logger.error(f"Error toggling scheduled report: {str(e)}")
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
@require_http_methods(["POST"])
def scheduled_report_test_htmx(request, report_id):
    """HTMX endpoint for testing scheduled report execution"""
    try:
        report = AnalyticsReport.objects.get(id=report_id)

        # Check permissions
        if not (request.user.is_staff or request.user.role in ['admin', 'project_manager'] or report.created_by == request.user):
            return HttpResponse('Unauthorized', status=403)

        # In a real implementation, this would queue the report for execution
        # For now, we'll simulate the task queueing
        task_id = f"test_{report_id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}"

        return HttpResponse(
            '<div class="alert alert-info">'
            f'Test execution queued! Task ID: {task_id[:8]}...<br>'
            'Check execution history for results.'
            '</div>'
        )

    except AnalyticsReport.DoesNotExist:
        return HttpResponse('Report not found', status=404)
    except Exception as e:
        logger.error(f"Error testing scheduled report: {str(e)}")
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
def scheduled_report_executions_htmx(request, report_id):
    """HTMX endpoint for viewing report execution history"""
    try:
        report = AnalyticsReport.objects.get(id=report_id)

        # Get recent executions
        executions = ReportExecution.objects.filter(
            report=report
        ).order_by('-started_at')[:10]

        context = {
            'report': report,
            'executions': executions,
        }

        return render(request, 'components/reports/execution_history.html', context)

    except AnalyticsReport.DoesNotExist:
        return HttpResponse('Report not found', status=404)
    except Exception as e:
        logger.error(f"Error loading execution history: {str(e)}")
        return HttpResponse(f'Error: {str(e)}', status=500)


# ========== UTILITY FUNCTIONS ==========

def user_has_project_access(user, project):
    """Check if user has access to the project"""
    return (
        user.is_staff or
        user == project.created_by or
        user in project.members.all()
    )


def get_user_conversations(user, category='all', search_query=''):
    """Get user conversations with filtering"""
    conversations = Conversation.objects.filter(
        participants=user,
        is_active=True
    ).select_related('project', 'created_by').prefetch_related('participants')

    if category != 'all':
        conversations = conversations.filter(conversation_type=category)

    if search_query:
        conversations = conversations.filter(
            Q(name__icontains=search_query) |
            Q(participants__first_name__icontains=search_query) |
            Q(participants__last_name__icontains=search_query)
        ).distinct()

    return conversations.order_by('-last_message_at')[:20]


def execute_ai_command(command):
    """Execute an AI command - placeholder implementation"""
    # This would contain the actual AI command execution logic
    return True



@login_required
@require_http_methods(["GET"])
def project_list_enhanced(request):
    """Enhanced project list view with filtering and sorting capabilities"""


    # Get query parameters
    search = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    sort_by = request.GET.get('sort', 'name')
    sort_order = request.GET.get('order', 'asc')

    # Base queryset with annotations
    projects = Project.objects.annotate(
        utility_count=Count('utilities'),
        conflict_count=Count('conflicts'),
        task_count=Count('tasks')
    )

    # Apply search filter
    if search:
        projects = projects.filter(
            Q(name__icontains=search) |
            Q(client__icontains=search) |
            Q(description__icontains=search)
        )

    # Apply status filter
    if status_filter:
        projects = projects.filter(rag_status=status_filter)

    # Apply sorting
    order_prefix = '' if sort_order == 'asc' else '-'
    if sort_by in ['name', 'client', 'created_at', 'updated_at', 'rag_status']:
        projects = projects.order_by(f'{order_prefix}{sort_by}')
    else:
        projects = projects.order_by('-updated_at')

    # Limit results for performance
    projects = projects[:50]

    context = {
        'projects': projects,
        'search': search,
        'status_filter': status_filter,
        'sort_by': sort_by,
        'sort_order': sort_order,
    }

    return render(request, 'components/projects/enhanced_list.html', context)



@login_required
def notification_list_view(request):
    """Main notifications list view"""
    try:
    except ImportError:
        # Fallback if Notification model doesnt exist
        notifications = []
        unread_count = 0
    else:
        # Get users notifications
        notifications = Notification.objects.filter(
            user=request.user
        ).order_by("-created_at")[:50]

        # Mark notifications as read when viewed
        unread_notifications = notifications.filter(is_read=False)
        for notification in unread_notifications:
            notification.is_read = True
            notification.save(update_fields=["is_read"])

        unread_count = 0  # Now 0 since we marked them as read

    context = {
        "notifications": notifications,
        "unread_count": unread_count,
    }

    return render(request, "CLEAR/notifications.html", context)


class ProjectStatsPartial(LoginRequiredMixin, TemplateView):
    """HTMX partial for project statistics"""
    template_name = "components/project_stats_partial.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add basic project stats
        context.update({
            "total_projects": Project.objects.count(),
            "active_projects": Project.objects.exclude(rag_status="Complete").count(),
            "completed_projects": Project.objects.filter(rag_status="Complete").count(),
        })
        return context


class NotificationCountPartial(LoginRequiredMixin, TemplateView):
    """HTMX partial for notification count"""
    template_name = "components/notification_count_partial.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            unread_count = Notification.objects.filter(
                user=self.request.user, is_read=False
            ).count()
        except ImportError:
            unread_count = 0
        context["unread_count"] = unread_count
        return context


class ConflictSummaryPartial(LoginRequiredMixin, TemplateView):
    """HTMX partial for conflict summaries"""
    template_name = "components/conflict_summary_partial.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            conflicts = Conflict.objects.filter(
                project__in=Project.objects.filter(
                    Q(manager=self.request.user) |
                    Q(coordinator_id=str(self.request.user.id))
                )
            )[:10]
        except Exception:
            conflicts = []
        context["conflicts"] = conflicts
        return context


class UtilityFormPartial(LoginRequiredMixin, TemplateView):
    """HTMX partial for utility forms"""
    template_name = "components/utility_form_partial.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["utility_types"] = ["Electric", "Gas", "Water", "Telecom", "Fiber"]
        return context


@login_required
def ai_communication_dashboard(request):
    """AI communication interface"""
    try:
        context = {
            "user": request.user,
            "ai_enabled": True,
            "recent_queries": [],
        }
        return render(request, "CLEAR/ai_dashboard.html", context)
    except Exception as e:
        logger.error(f"Error in AI communication dashboard view: {str(e)}")
        return render(request, 'error/500.html', {
            'error_message': f"An error occurred while loading the AI dashboard: {str(e)}"
        }, status=500)


@login_required
def generic(request):
    """Generic utility view"""
    return render(request, "CLEAR/generic.html", {"user": request.user})


# ============================================================================
# MISSING TIMESHEET FUNCTIONS - ADDED TO UNBLOCK SERVER STARTUP
# ============================================================================

@login_required
def timesheet_view(request):
    """Main timesheet view"""
    context = {
        'user': request.user,
        'time_entries': [],
    }
    return render(request, 'CLEAR/timesheet.html', context)


@login_required 
@require_http_methods(["POST"])
def save_timesheet(request):
    """Save timesheet data"""
    try:
        # Basic stub implementation
        return JsonResponse({'success': True, 'message': 'Timesheet saved'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"]) 
def submit_timesheet(request):
    """Submit timesheet for approval"""
    try:
        # Basic stub implementation
        return JsonResponse({'success': True, 'message': 'Timesheet submitted'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def update_time_entry(request):
    """Update a time entry"""
    try:
        # Basic stub implementation
        return JsonResponse({'success': True, 'message': 'Time entry updated'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def export_timesheet(request):
    """Export timesheet data"""
    try:
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="timesheet.csv"'

        writer = csv.writer(response)
        writer.writerow(['Date', 'Project', 'Hours', 'Description'])
        # Add actual data here in real implementation

        return response
    except Exception as e:
        return HttpResponse(f'Error exporting timesheet: {e}', status=500)


# ============================================================================
# AI DASHBOARD FUNCTION MOVED TO CONSOLIDATED IMPLEMENTATION ABOVE
# ============================================================================

@login_required
def current_time(request):
    """Return current time for HTMX polling updates"""
    now = timezone.now()
    return HttpResponse(now.strftime("%b %d, %I:%M %p"))

@login_required
def timer_form_toggle(request):
    """Toggle timer form visibility"""
    # Simple toggle - return empty div to collapse or form to expand
    return HttpResponse('<div id="timer-form-container"></div>')

@login_required
def quick_entry_modal(request):
    """Display quick time entry modal"""
    return render(request, 'components/timer/quick_entry_modal.html', {
        'user_projects': Project.objects.filter(
            team_members__user=request.user,
            team_members__is_active=True
        )[:10]
    })


# ========== WEBSOCKET TEST VIEW ==========

@login_required
def test_websocket_view(request):
    """
    HTMX-enhanced WebSocket test view with progressive enhancement
    Supports connection management, message testing, logging, and metrics
    # Handle HTMX-specific requests
    if request.headers.get('HX-Request'):
        return handle_websocket_htmx_request(request)
    
    # Regular page load for progressive enhancement
    return render(request, 'test_websocket.html', {
        'user': request.user,
        'websocket_url': get_websocket_url(request)
    })


def handle_websocket_htmx_request(request):
    """Handle HTMX-specific WebSocket test requests"""
    
    # Connection status check
    if request.GET.get('check') == 'status':
        return render(request, 'components/websocket_status.html', {
            'status': get_websocket_connection_status(request),
            'websocket_url': get_websocket_url(request)
        })
    
    # Message log retrieval
    if request.GET.get('check') == 'log':
        return render(request, 'components/websocket_log.html', {
            'messages': get_websocket_message_log(request)
        })
    
    # Performance metrics
    if request.GET.get('check') == 'metrics':
        return render(request, 'components/websocket_metrics.html', {
            'metrics': get_websocket_performance_metrics(request)
        })
    
    # Handle POST actions
    if request.method == 'POST':
        action = request.GET.get('action')
        
        if action == 'connect':
            return handle_websocket_connect(request)
        elif action == 'disconnect':
            return handle_websocket_disconnect(request)
        elif action == 'send':
            return handle_websocket_send_message(request)
        elif action == 'clear_log':
            return handle_websocket_clear_log(request)
    
    # Default response
    return HttpResponse('<div class="alert alert-info">WebSocket test request received</div>')


def get_websocket_url(request):
    """Get WebSocket URL for the current request"""
    protocol = 'wss' if request.is_secure() else 'ws'
    host = request.get_host()
    return f"{protocol}://{host}/ws/chat/team/"


def get_websocket_connection_status(request):
    """Get current WebSocket connection status"""
    # In a real implementation, this would check actual connection status
    # For testing, we'll simulate different states
    
    statuses = [
        {'status': 'connected', 'type': 'success', 'message': 'WebSocket connected successfully'},
        {'status': 'disconnected', 'type': 'secondary', 'message': 'WebSocket not connected'},
        {'status': 'connecting', 'type': 'warning', 'message': 'Connecting to WebSocket...'},
        {'status': 'error', 'type': 'danger', 'message': 'Connection failed - check server status'}
    ]
    
    # Simulate status based on session state or random for demo
    session_status = request.session.get('websocket_status', 'disconnected')
    
    for status in statuses:
        if status['status'] == session_status:
            return status
    
    return statuses[1]  # Default to disconnected


def get_websocket_message_log(request):
    """Get WebSocket message log from session"""
    messages = request.session.get('websocket_messages', [])
    # Keep only last 50 messages for performance
    return messages[-50:] if len(messages) > 50 else messages


def get_websocket_performance_metrics(request):
    """Get WebSocket performance metrics"""
    
    # Simulate metrics - in real implementation, this would come from monitoring
    time.time()
    
    metrics = {
        'connection_time': random.randint(50, 200),  # milliseconds
        'message_latency': random.randint(10, 100),
        'messages_sent': request.session.get('ws_messages_sent', 0),
        'messages_received': request.session.get('ws_messages_received', 0),
        'bytes_transferred': random.randint(1024, 10240),
        'uptime': '00:05:23',  # mock uptime
        'last_heartbeat': time.strftime('%H:%M:%S'),
        'connection_quality': random.choice(['excellent', 'good', 'fair', 'poor'])
    }
    
    return metrics


def handle_websocket_connect(request):
    """Handle WebSocket connection request"""
    try:
        # Simulate connection process
        request.session['websocket_status'] = 'connecting'
        
        # Add connection message to log
        add_websocket_message(request, 'System', 'Initiating WebSocket connection...', 'info')
        
        # Simulate successful connection after a delay (in real app, this would be immediate)
        time.sleep(0.1)  # Brief delay to simulate connection time
        
        request.session['websocket_status'] = 'connected'
        add_websocket_message(request, 'System', 'WebSocket connected successfully!', 'success')
        
        return render(request, 'components/websocket_status.html', {
            'status': {
                'status': 'connected',
                'type': 'success',
                'message': 'WebSocket connected successfully'
            },
            'websocket_url': get_websocket_url(request)
        })
        
    except Exception as e:
        request.session['websocket_status'] = 'error'
        add_websocket_message(request, 'System', f'Connection failed: {str(e)}', 'error')
        
        return render(request, 'components/websocket_status.html', {
            'status': {
                'status': 'error',
                'type': 'danger',
                'message': f'Connection failed: {str(e)}'
            },
            'websocket_url': get_websocket_url(request)
        })


def handle_websocket_disconnect(request):
    """Handle WebSocket disconnection request"""
    request.session['websocket_status'] = 'disconnected'
    add_websocket_message(request, 'System', 'WebSocket disconnected by user', 'warning')
    
    return render(request, 'components/websocket_status.html', {
        'status': {
            'status': 'disconnected',
            'type': 'secondary',
            'message': 'WebSocket disconnected'
        },
        'websocket_url': get_websocket_url(request)
    })


def handle_websocket_send_message(request):
    """Handle sending test message via WebSocket"""
    try:
        # Get message from form or URL parameter
        message = request.POST.get('message') or request.GET.get('message', 'Test message')
        
        # Check if connected
        if request.session.get('websocket_status') != 'connected':
            return HttpResponse(
                '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>WebSocket not connected</div>'
            )
        
        # Simulate sending message
        sent_count = request.session.get('ws_messages_sent', 0) + 1
        request.session['ws_messages_sent'] = sent_count
        
        # Add to message log
        add_websocket_message(request, request.user.username, f'Sent: {message}', 'sent')
        
        # Simulate received response (echo or heartbeat response)
        time.sleep(0.05)  # Brief delay to simulate network roundtrip
        
        received_count = request.session.get('ws_messages_received', 0) + 1
        request.session['ws_messages_received'] = received_count
        
        if message.lower() == 'heartbeat':
            response_msg = '{"type":"heartbeat_pong","timestamp":"' + str(time.time()) + '"}'
        elif message.lower() == 'ping':
            response_msg = 'pong'
        elif message.lower() == 'test_json':
            response_msg = '{"status":"success","data":{"test":true,"timestamp":"' + str(time.time()) + '"}}'
        else:
            response_msg = f'Echo: {message}'
        
        add_websocket_message(request, 'Server', f'Received: {response_msg}', 'received')
        
        return HttpResponse(
            f'<div class="alert alert-success"><i class="fas fa-check me-2"></i>Message sent: "{message}"</div>'
        )
        
    except Exception as e:
        return HttpResponse(
            f'<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Error: {str(e)}</div>'
        )


def handle_websocket_clear_log(request):
    """Clear WebSocket message log"""
    request.session['websocket_messages'] = []
    request.session['ws_messages_sent'] = 0
    request.session['ws_messages_received'] = 0
    
    return HttpResponse(
        '<div class="p-3 text-center text-muted">Message log cleared. New messages will appear here.</div>'
    )


def add_websocket_message(request, sender, message, message_type):
    """Add a message to the WebSocket log"""
    
    messages = request.session.get('websocket_messages', [])
    
    new_message = {
        'timestamp': time.strftime('%H:%M:%S'),
        'sender': sender,
        'message': message,
        'type': message_type
    }
    
    messages.append(new_message)
    request.session['websocket_messages'] = messages


# ========== DASHBOARD LAYOUT MANAGEMENT ==========

@login_required
@require_http_methods(["POST"])
def save_dashboard_layout(request):
    """Save user's dashboard layout preferences via HTMX"""
    try:
        data = json.loads(request.body)
        layout = data.get('layout', {})
        
        # Validate layout structure
        if not isinstance(layout, dict) or 'components' not in layout:
            return JsonResponse({'error': 'Invalid layout structure'}, status=400)
        
        # Update user's custom_settings
        if not request.user.custom_settings:
            request.user.custom_settings = {}
        
        request.user.custom_settings['dashboard_layout'] = layout
        request.user.save(update_fields=['custom_settings'])
        
        return JsonResponse({'success': True, 'message': 'Layout saved successfully'})
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error saving dashboard layout for user {request.user.id}: {str(e)}")
        return JsonResponse({'error': 'Failed to save layout'}, status=500)


@login_required
@require_http_methods(["GET"])
def load_dashboard_layout(request):
    """Load user's dashboard layout preferences"""
    try:
        layout = request.user.custom_settings.get('dashboard_layout', None) if request.user.custom_settings else None
        
        if layout:
            return JsonResponse({'success': True, 'layout': layout})
        else:
            # Return default layout if none saved
            default_layout = {
                'components': [
                    {'id': 'welcome-hero', 'size': 'full', 'order': 0, 'visible': True},
                    {'id': 'team-chat', 'size': 'large', 'order': 1, 'visible': True},
                    {'id': 'my-time', 'size': 'medium', 'order': 2, 'visible': True},
                    {'id': 'my-tasks', 'size': 'medium', 'order': 3, 'visible': True},
                    {'id': 'my-meetings', 'size': 'medium', 'order': 4, 'visible': True},
                    {'id': 'my-projects', 'size': 'medium', 'order': 5, 'visible': True}
                ],
                'gridCols': {
                    'small': 1,
                    'medium': 2,
                    'large': 3,
                    'xl': 4
                }
            }
            return JsonResponse({'success': True, 'layout': default_layout, 'is_default': True})
            
    except Exception as e:
        logger.error(f"Error loading dashboard layout for user {request.user.id}: {str(e)}")
        return JsonResponse({'error': 'Failed to load layout'}, status=500)


@login_required
@require_http_methods(["POST"])
def reset_dashboard_layout(request):
    """Reset dashboard layout to default"""
    try:
        # Remove dashboard layout from custom settings
        if request.user.custom_settings and 'dashboard_layout' in request.user.custom_settings:
            del request.user.custom_settings['dashboard_layout']
            request.user.save(update_fields=['custom_settings'])
        
        return JsonResponse({'success': True, 'message': 'Layout reset to default'})
        
    except Exception as e:
        logger.error(f"Error resetting dashboard layout for user {request.user.id}: {str(e)}")
        return JsonResponse({'error': 'Failed to reset layout'}, status=500)


@login_required
@require_http_methods(["GET"])
def dashboard_welcome_stats(request):
    """Get updated stats for welcome hero component"""
    try:
        
        # Get user's projects
        user_projects = Project.objects.filter(
            Q(manager_id=request.user.id) | 
            Q(coordinator_id=str(request.user.id)) | 
            Q(egis_project_manager=request.user.username)
        )
        
        # Get tasks for today
        today = timezone.now().date()
        tasks_today = Task.objects.filter(
            assigned_to=request.user,
            due_date=today,
            status__in=['pending', 'in_progress']
        ).count()
        
        # Get hours this week
        week_start = today - timedelta(days=today.weekday())
        week_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=week_start,
            start_time__date__lte=today
        )
        
        total_minutes = week_entries.aggregate(Sum('duration_minutes'))['duration_minutes__sum'] or 0
        hours_this_week = round(total_minutes / 60.0, 1)
        
        # Calculate on track percentage (assuming 40 hour work week)
        expected_hours = (today.weekday() + 1) * 8  # 8 hours per day
        on_track_percentage = min(100, round((hours_this_week / expected_hours) * 100, 0)) if expected_hours > 0 else 0
        
        stats = {
            'active_projects': user_projects.count(),
            'tasks_today': tasks_today,
            'hours_this_week': hours_this_week,
            'on_track': int(on_track_percentage)
        }
        
        return JsonResponse({'success': True, 'stats': stats})
        
    except Exception as e:
        logger.error(f"Error getting dashboard stats for user {request.user.id}: {str(e)}")
        return JsonResponse({'error': 'Failed to get stats'}, status=500)


# ========== BROWSER COMPATIBILITY TEST VIEW ==========

@login_required
def browser_compatibility_test_view(request):
    """
    HTMX-enhanced browser compatibility test view with progressive enhancement
    Supports comprehensive browser testing, feature detection, and performance metrics
    # Handle HTMX-specific requests
    if request.headers.get('HX-Request'):
        return handle_browser_compatibility_htmx_request(request)
    
    # Regular page load for progressive enhancement
    return render(request, 'tests/browser_compatibility.html', {
        'user': request.user,
        'test_session_id': request.session.session_key
    })


def handle_browser_compatibility_htmx_request(request):
    """Handle HTMX-specific browser compatibility test requests"""
    
    # Browser information check
    if request.GET.get('check') == 'browser_info':
        return render(request, 'components/browser_info.html', {
            'browser_info': get_browser_info(request)
        })
    
    # Test progress check
    if request.GET.get('check') == 'progress':
        return render(request, 'components/test_progress.html', {
            'progress': get_test_progress(request)
        })
    
    # Test log retrieval
    if request.GET.get('check') == 'log':
        return render(request, 'components/browser_test_log.html', {
            'log_entries': get_browser_test_log(request)
        })
    
    # Performance metrics
    if request.GET.get('check') == 'metrics':
        return render(request, 'components/browser_metrics.html', {
            'metrics': get_browser_performance_metrics(request)
        })
    
    # Handle POST actions
    if request.method == 'POST':
        action = request.GET.get('action')
        
        if action == 'run_all':
            return handle_run_all_tests(request)
        elif action == 'htmx_tests':
            return handle_htmx_tests(request)
        elif action == 'websocket_tests':
            return handle_websocket_tests(request)
        elif action == 'performance_tests':
            return handle_performance_tests(request)
        elif action == 'clear_results':
            return handle_clear_test_results(request)
        elif action == 'clear_log':
            return handle_clear_test_log(request)
    
    # Default response
    return HttpResponse('<div class="alert alert-info">Browser compatibility test request received</div>')


def get_browser_info(request):
    """Get browser information from request headers"""
    user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
    
    # Parse user agent for browser detection
    browser_info = {
        'user_agent': user_agent,
        'browser_name': 'Unknown',
        'browser_version': 'Unknown',
        'is_mobile': False,
        'operating_system': 'Unknown',
        'features_supported': [],
        'last_detected': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    # Simple browser detection
    if 'Chrome' in user_agent:
        browser_info['browser_name'] = 'Chrome'
        version_match = re.search(r'Chrome/([0-9.]+)', user_agent)
        if version_match:
            browser_info['browser_version'] = version_match.group(1)
    elif 'Firefox' in user_agent:
        browser_info['browser_name'] = 'Firefox'
        version_match = re.search(r'Firefox/([0-9.]+)', user_agent)
        if version_match:
            browser_info['browser_version'] = version_match.group(1)
    elif 'Safari' in user_agent:
        browser_info['browser_name'] = 'Safari'
        version_match = re.search(r'Version/([0-9.]+)', user_agent)
        if version_match:
            browser_info['browser_version'] = version_match.group(1)
    elif 'Edge' in user_agent:
        browser_info['browser_name'] = 'Edge'
        version_match = re.search(r'Edge/([0-9.]+)', user_agent)
        if version_match:
            browser_info['browser_version'] = version_match.group(1)
    
    # Mobile detection
    browser_info['is_mobile'] = any(mobile in user_agent.lower() for mobile in [
        'mobile', 'android', 'iphone', 'ipad', 'windows phone'
    ])
    
    # OS detection
    if 'Windows' in user_agent:
        browser_info['operating_system'] = 'Windows'
    elif 'Mac' in user_agent:
        browser_info['operating_system'] = 'macOS'
    elif 'Linux' in user_agent:
        browser_info['operating_system'] = 'Linux'
    elif 'Android' in user_agent:
        browser_info['operating_system'] = 'Android'
    elif 'iOS' in user_agent:
        browser_info['operating_system'] = 'iOS'
    
    # Feature detection (simulated based on browser)
    if browser_info['browser_name'] in ['Chrome', 'Firefox', 'Safari', 'Edge']:
        browser_info['features_supported'] = [
            'WebSocket', 'HTMX', 'LocalStorage', 'IndexedDB', 
            'ServiceWorker', 'PushAPI', 'NotificationAPI'
        ]
    
    return browser_info


def get_test_progress(request):
    """Get current test progress from session"""
    progress = request.session.get('test_progress', {
        'current_test': 0,
        'total_tests': 0,
        'percentage': 0,
        'current_test_name': 'Ready to run tests...',
        'status': 'ready'
    })
    
    return progress


def get_browser_test_log(request):
    """Get browser test log from session"""
    log_entries = request.session.get('browser_test_log', [])
    return log_entries[-100:]  # Keep only last 100 entries


def get_browser_performance_metrics(request):
    """Get browser performance metrics"""
    
    # Simulate performance metrics - in real implementation, these would be actual measurements
    metrics = {
        'htmx_load_time': random.randint(20, 100),  # milliseconds
        'dom_ready_time': random.randint(50, 200),
        'websocket_connection_time': random.randint(30, 150),
        'ajax_request_latency': random.randint(10, 50),
        'page_render_time': random.randint(100, 500),
        'memory_usage': random.randint(50, 200),  # MB
        'tests_passed': request.session.get('tests_passed', 0),
        'tests_failed': request.session.get('tests_failed', 0),
        'tests_total': request.session.get('tests_total', 0),
        'success_rate': 0,
        'last_updated': time.strftime('%H:%M:%S')
    }
    
    # Calculate success rate
    if metrics['tests_total'] > 0:
        metrics['success_rate'] = round((metrics['tests_passed'] / metrics['tests_total']) * 100, 1)
    
    return metrics


def handle_run_all_tests(request):
    """Handle running all browser compatibility tests"""
    try:
        # Initialize test session
        request.session['test_progress'] = {
            'current_test': 0,
            'total_tests': 25,  # Total number of tests
            'percentage': 0,
            'current_test_name': 'Starting comprehensive test suite...',
            'status': 'running'
        }
        
        add_browser_test_log(request, 'Starting comprehensive browser compatibility test suite', 'info')
        
        # Simulate running different test categories
        test_categories = [
            ('HTMX Core Functionality', run_htmx_tests_simulation),
            ('WebSocket Connectivity', run_websocket_tests_simulation),
            ('Performance & Load Times', run_performance_tests_simulation),
            ('Browser Feature Detection', run_feature_tests_simulation),
            ('Mobile Compatibility', run_mobile_tests_simulation)
        ]
        
        all_results = []
        for category_name, test_function in test_categories:
            add_browser_test_log(request, f'Running {category_name} tests...', 'info')
            category_results = test_function(request)
            all_results.extend(category_results)
        
        # Update final test progress
        request.session['test_progress'] = {
            'current_test': 25,
            'total_tests': 25,
            'percentage': 100,
            'current_test_name': 'All tests completed!',
            'status': 'completed'
        }
        
        # Update test statistics
        passed_tests = sum(1 for result in all_results if result['passed'])
        failed_tests = len(all_results) - passed_tests
        
        request.session['tests_passed'] = passed_tests
        request.session['tests_failed'] = failed_tests
        request.session['tests_total'] = len(all_results)
        
        add_browser_test_log(request, f'Test suite completed! {passed_tests} passed, {failed_tests} failed', 'success')
        
        return render(request, 'components/browser_test_results.html', {
            'test_results': all_results,
            'test_summary': {
                'total': len(all_results),
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': round((passed_tests / len(all_results)) * 100, 1) if all_results else 0
            }
        })
        
    except Exception as e:
        add_browser_test_log(request, f'Error running test suite: {str(e)}', 'error')
        return HttpResponse(
            f'<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Error: {str(e)}</div>'
        )


def handle_htmx_tests(request):
    """Handle HTMX-specific tests"""
    add_browser_test_log(request, 'Running HTMX compatibility tests...', 'info')
    test_results = run_htmx_tests_simulation(request)
    
    return render(request, 'components/browser_test_results.html', {
        'test_results': test_results,
        'test_category': 'HTMX Tests'
    })


def handle_websocket_tests(request):
    """Handle WebSocket tests"""
    add_browser_test_log(request, 'Running WebSocket compatibility tests...', 'info')
    test_results = run_websocket_tests_simulation(request)
    
    return render(request, 'components/browser_test_results.html', {
        'test_results': test_results,
        'test_category': 'WebSocket Tests'
    })


def handle_performance_tests(request):
    """Handle performance tests"""
    add_browser_test_log(request, 'Running performance tests...', 'info')
    test_results = run_performance_tests_simulation(request)
    
    return render(request, 'components/browser_test_results.html', {
        'test_results': test_results,
        'test_category': 'Performance Tests'
    })


def handle_clear_test_results(request):
    """Clear test results"""
    request.session['test_progress'] = {
        'current_test': 0,
        'total_tests': 0,
        'percentage': 0,
        'current_test_name': 'Ready to run tests...',
        'status': 'ready'
    }
    
    request.session['tests_passed'] = 0
    request.session['tests_failed'] = 0
    request.session['tests_total'] = 0
    
    add_browser_test_log(request, 'Test results cleared', 'info')
    
    return HttpResponse(
        '<div class="text-center text-muted p-4">'
        '<i class="fas fa-clipboard-list fs-1 mb-3 d-block"></i>'
        '<h5>Test results cleared</h5>'
        '<p class="mb-0">Ready to run new tests.</p>'
        '</div>'
    )


def handle_clear_test_log(request):
    """Clear test log"""
    request.session['browser_test_log'] = []
    add_browser_test_log(request, 'Test log cleared', 'info')
    
    return HttpResponse(
        '<div class="p-3 text-center text-muted">'
        '<i class="fas fa-broom me-2"></i>'
        'Test log cleared. New entries will appear here.'
        '</div>'
    )


def run_htmx_tests_simulation(request):
    """Simulate HTMX compatibility tests"""
    
    tests = [
        {'name': 'HTMX Library Loading', 'description': 'Verify HTMX library loads correctly'},
        {'name': 'hx-get Requests', 'description': 'Test GET requests via HTMX'},
        {'name': 'hx-post Requests', 'description': 'Test POST requests via HTMX'},
        {'name': 'hx-swap Functionality', 'description': 'Test DOM swapping capabilities'},
        {'name': 'hx-trigger Events', 'description': 'Test event-driven updates'},
        {'name': 'hx-target Selection', 'description': 'Test target element selection'},
        {'name': 'hx-indicator Loading', 'description': 'Test loading indicators'},
        {'name': 'Progressive Enhancement', 'description': 'Test fallback without JavaScript'}
    ]
    
    results = []
    for i, test in enumerate(tests):
        # Simulate test execution
        passed = random.choice([True, True, True, False])  # 75% pass rate
        duration = random.randint(50, 300)
        
        result = {
            'name': test['name'],
            'description': test['description'],
            'passed': passed,
            'duration': duration,
            'critical': True,
            'category': 'HTMX'
        }
        
        results.append(result)
        add_browser_test_log(request, f"HTMX Test: {test['name']} - {'PASS' if passed else 'FAIL'} ({duration}ms)", 
                           'success' if passed else 'warning')
    
    return results


def run_websocket_tests_simulation(request):
    """Simulate WebSocket compatibility tests"""
    
    tests = [
        {'name': 'WebSocket Constructor', 'description': 'Test WebSocket object availability'},
        {'name': 'Connection Establishment', 'description': 'Test WebSocket connection'},
        {'name': 'Message Sending', 'description': 'Test sending messages'},
        {'name': 'Message Receiving', 'description': 'Test receiving messages'},
        {'name': 'Binary Data Support', 'description': 'Test binary message handling'},
        {'name': 'Connection Recovery', 'description': 'Test reconnection handling'}
    ]
    
    results = []
    for test in tests:
        passed = random.choice([True, True, False])  # 67% pass rate
        duration = random.randint(100, 800)
        
        result = {
            'name': test['name'],
            'description': test['description'],
            'passed': passed,
            'duration': duration,
            'critical': True,
            'category': 'WebSocket'
        }
        
        results.append(result)
        add_browser_test_log(request, f"WebSocket Test: {test['name']} - {'PASS' if passed else 'FAIL'} ({duration}ms)", 
                           'success' if passed else 'warning')
    
    return results


def run_performance_tests_simulation(request):
    """Simulate performance tests"""
    
    tests = [
        {'name': 'Page Load Time', 'description': 'Measure page loading performance'},
        {'name': 'HTMX Request Latency', 'description': 'Measure HTMX request speed'},
        {'name': 'DOM Manipulation Speed', 'description': 'Test DOM update performance'},
        {'name': 'Memory Usage', 'description': 'Monitor memory consumption'},
        {'name': 'Network Efficiency', 'description': 'Test bandwidth usage'}
    ]
    
    results = []
    for test in tests:
        passed = random.choice([True, True, True, True, False])  # 80% pass rate
        duration = random.randint(200, 1000)
        
        result = {
            'name': test['name'],
            'description': test['description'],
            'passed': passed,
            'duration': duration,
            'critical': False,
            'category': 'Performance'
        }
        
        results.append(result)
        add_browser_test_log(request, f"Performance Test: {test['name']} - {'PASS' if passed else 'FAIL'} ({duration}ms)", 
                           'success' if passed else 'info')
    
    return results


def run_feature_tests_simulation(request):
    """Simulate browser feature detection tests"""
    
    tests = [
        {'name': 'Local Storage', 'description': 'Test localStorage availability'},
        {'name': 'Session Storage', 'description': 'Test sessionStorage availability'},
        {'name': 'IndexedDB', 'description': 'Test IndexedDB support'},
        {'name': 'Service Workers', 'description': 'Test service worker support'},
        {'name': 'Push API', 'description': 'Test push notification support'},
        {'name': 'Geolocation API', 'description': 'Test geolocation functionality'}
    ]
    
    results = []
    for test in tests:
        passed = random.choice([True, True, False])  # 67% pass rate
        duration = random.randint(20, 100)
        
        result = {
            'name': test['name'],
            'description': test['description'],
            'passed': passed,
            'duration': duration,
            'critical': False,
            'category': 'Features'
        }
        
        results.append(result)
        add_browser_test_log(request, f"Feature Test: {test['name']} - {'PASS' if passed else 'FAIL'} ({duration}ms)", 
                           'success' if passed else 'info')
    
    return results


def run_mobile_tests_simulation(request):
    """Simulate mobile compatibility tests"""
    
    # Check if request is from mobile device
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    is_mobile = any(mobile in user_agent.lower() for mobile in ['mobile', 'android', 'iphone'])
    
    if not is_mobile:
        add_browser_test_log(request, 'Skipping mobile tests - desktop browser detected', 'info')
        return []
    
    tests = [
        {'name': 'Touch Events', 'description': 'Test touch interaction support'},
        {'name': 'Viewport Scaling', 'description': 'Test responsive viewport handling'},
        {'name': 'Orientation Change', 'description': 'Test screen orientation changes'},
        {'name': 'Background Handling', 'description': 'Test app backgrounding behavior'}
    ]
    
    results = []
    for test in tests:
        passed = random.choice([True, True, False])  # 67% pass rate
        duration = random.randint(50, 300)
        
        result = {
            'name': test['name'],
            'description': test['description'],
            'passed': passed,
            'duration': duration,
            'critical': False,
            'category': 'Mobile'
        }
        
        results.append(result)
        add_browser_test_log(request, f"Mobile Test: {test['name']} - {'PASS' if passed else 'FAIL'} ({duration}ms)", 
                           'success' if passed else 'info')
    
    return results


def add_browser_test_log(request, message, log_type):
    """Add a message to the browser test log"""
    
    log_entries = request.session.get('browser_test_log', [])
    
    new_entry = {
        'timestamp': time.strftime('%H:%M:%S'),
        'message': message,
        'type': log_type
    }
    
    log_entries.append(new_entry)
    request.session['browser_test_log'] = log_entries


# ========== AUTHENTICATION HTMX VIEWS ==========

class LoginHTMXView(HTMXResponseMixin, View):
    """HTMX endpoint for user authentication"""
    
    def post(self, request):
        
        form = AuthenticationForm(request, data=request.POST)
        
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            
            if user:
                login(request, user)
                # Return user menu fragment for header update
                return self.htmx_render(
                    'components/user_menu.html',
                    {'user': user},
                    trigger_events={'userLoggedIn': {'username': user.username}}
                )
        
        # Return form with errors
        return self.htmx_form_errors(form)


class LogoutHTMXView(HTMXResponseMixin, View):
    """HTMX endpoint for user logout"""
    
    def post(self, request):
        
        logout(request)
        return self.htmx_redirect('/login/')


@login_required
def user_menu_htmx(request):
    """Get user menu fragment for header updates"""
    return render(request, 'components/user_menu.html', {
        'user': request.user
    })


# ========== PROJECT HTMX VIEWS ==========

class ProjectListHTMXView(LoginRequiredMixin, HTMXResponseMixin, View):
    """HTMX endpoint for project list"""
    
    def get(self, request):
        # Filter projects by user access
        projects = Project.objects.filter(
            Q(owner=request.user) | 
            Q(team_members=request.user) |
            Q(stakeholders__user=request.user)
        ).distinct().order_by('-created_at')
        
        # Search functionality
        search_query = request.GET.get('search', '')
        if search_query:
            projects = projects.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(project_number__icontains=search_query)
            )
        
        # Pagination
        paginator = Paginator(projects, 10)
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)
        
        return self.htmx_render('components/project_list.html', {
            'projects': page_obj,
            'search_query': search_query
        })


class ProjectCreateHTMXView(LoginRequiredMixin, HTMXResponseMixin, View):
    """HTMX endpoint for creating projects"""
    
    def get(self, request):
        form = ProjectForm()
        return self.htmx_render('components/project_form.html', {
            'form': form,
            'action': 'create'
        })
    
    def post(self, request):
        form = ProjectForm(request.POST)
        
        if form.is_valid():
            project = form.save(commit=False)
            project.owner = request.user
            project.save()
            form.save_m2m()
            
            # Return the new project card
            return self.htmx_render(
                'components/project_card.html',
                {'project': project},
                target='#project-list',
                swap='afterbegin',
                trigger_events={'projectCreated': {'id': str(project.id), 'name': project.name}}
            )
        
        return self.htmx_render('components/project_form.html', {
            'form': form,
            'action': 'create'
        })


class ProjectUpdateHTMXView(LoginRequiredMixin, HTMXResponseMixin, View):
    """HTMX endpoint for updating projects"""
    
    def get(self, request, pk):
        project = get_object_or_404(Project, pk=pk)
        
        # Check permissions
        if not project.can_edit(request.user):
            raise PermissionDenied
        
        form = ProjectForm(instance=project)
        return self.htmx_render('components/project_form.html', {
            'form': form,
            'project': project,
            'action': 'update'
        })
    
    def post(self, request, pk):
        project = get_object_or_404(Project, pk=pk)
        
        # Check permissions
        if not project.can_edit(request.user):
            raise PermissionDenied
        
        form = ProjectForm(request.POST, instance=project)
        
        if form.is_valid():
            project = form.save()
            
            # Return updated project card
            return self.htmx_render(
                'components/project_card.html',
                {'project': project},
                target=f'#project-{project.id}',
                swap='outerHTML',
                trigger_events={'projectUpdated': {'id': str(project.id), 'name': project.name}}
            )
        
        return self.htmx_render('components/project_form.html', {
            'form': form,
            'project': project,
            'action': 'update'
        })


class ProjectDeleteHTMXView(LoginRequiredMixin, HTMXResponseMixin, View):
    """HTMX endpoint for deleting projects"""
    
    def delete(self, request, pk):
        project = get_object_or_404(Project, pk=pk)
        
        # Check permissions
        if not project.can_delete(request.user):
            raise PermissionDenied
        
        project_id = project.id
        project_name = project.name
        project.delete()
        
        # Return empty response to remove the element
        return self.htmx_trigger(
            'projectDeleted',
            {'id': str(project_id), 'name': project_name}
        )


# Function-based views for simpler endpoints

@login_required
def project_detail_htmx(request, pk):
    """Get project detail fragment"""
    project = get_object_or_404(Project, pk=pk)
    
    # Check permissions
    if not project.can_view(request.user):
        raise PermissionDenied
    
    return render(request, 'components/project_detail.html', {
        'project': project
    })


# ========== UTILITY HTMX VIEWS ==========

@login_required
def utility_list_htmx(request):
    """Get utility list fragment"""
    utilities = Utility.objects.filter(
        project__in=Project.objects.filter(
            Q(owner=request.user) | 
            Q(team_members=request.user)
        )
    ).distinct()
    
    # Search
    search_query = request.GET.get('search', '')
    if search_query:
        utilities = utilities.filter(
            Q(name__icontains=search_query) |
            Q(type__icontains=search_query)
        )
    
    return render(request, 'components/utility_list.html', {
        'utilities': utilities,
        'search_query': search_query
    })


@login_required
@require_http_methods(["GET", "POST"])
def utility_create_htmx(request):
    """Create utility via HTMX"""
    if request.method == 'POST':
        form = UtilityForm(request.POST)
        if form.is_valid():
            utility = form.save()
            return render(request, 'components/utility_card.html', {
                'utility': utility
            })
    else:
        form = UtilityForm()
    
    return render(request, 'components/utility_form.html', {
        'form': form,
        'action': 'create'
    })


# ========== TASK HTMX VIEWS ==========

@login_required
def task_list_htmx(request):
    """Get task list fragment"""
    tasks = Task.objects.filter(
        Q(assigned_to=request.user) |
        Q(created_by=request.user) |
        Q(project__team_members=request.user)
    ).distinct().order_by('-created_at')
    
    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        tasks = tasks.filter(status=status_filter)
    
    return render(request, 'components/task_list.html', {
        'tasks': tasks,
        'status_filter': status_filter
    })


@login_required
@require_http_methods(["POST"])
def task_status_htmx(request, pk):
    """Update task status via HTMX"""
    task = get_object_or_404(Task, pk=pk)
    
    # Check permissions
    if not (task.assigned_to == request.user or task.created_by == request.user):
        raise PermissionDenied
    
    new_status = request.POST.get('status')
    if new_status in dict(Task.STATUS_CHOICES):
        task.status = new_status
        task.save()
    
    return render(request, 'components/task_card.html', {
        'task': task
    })


# ========== NOTIFICATION HTMX VIEWS ==========

@login_required
def notification_list_htmx(request):
    """Get notification list fragment"""
    notifications = Notification.objects.filter(
        user=request.user
    ).order_by('-created_at')[:10]
    
    return render(request, 'components/notification_list.html', {
        'notifications': notifications,
        'unread_count': notifications.filter(is_read=False).count()
    })


@login_required
@require_http_methods(["POST"])
def notification_mark_read_htmx(request, pk):
    """Mark notification as read via HTMX"""
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.is_read = True
    notification.save()
    
    # Return updated notification item
    return render(request, 'components/notification_item.html', {
        'notification': notification
    })


# ========== DASHBOARD HTMX VIEWS ==========

@login_required
def dashboard_stats_htmx(request):
    """Get dashboard statistics fragment"""
    user = request.user
    
    stats = {
        'active_projects': Project.objects.filter(
            Q(owner=user) | Q(team_members=user),
            status='active'
        ).count(),
        'pending_tasks': Task.objects.filter(
            assigned_to=user,
            status__in=['pending', 'in_progress']
        ).count(),
        'conflicts_detected': Conflict.objects.filter(
            project__in=Project.objects.filter(
                Q(owner=user) | Q(team_members=user)
            ),
            status='active'
        ).count(),
        'team_members': User.objects.filter(
            project_teams__in=Project.objects.filter(owner=user)
        ).distinct().count()
    }
    
    return render(request, 'components/dashboard_stats.html', {
        'stats': stats
    })


@login_required
def recent_activity_htmx(request):
    """Get recent activity feed fragment"""
    activities = Activity.objects.filter(
        project__in=Project.objects.filter(
            Q(owner=request.user) | Q(team_members=request.user)
        )
    ).order_by('-created_at')[:20]
    
    return render(request, 'components/recent_activity.html', {
        'activities': activities
    })


# ========== SEARCH HTMX VIEWS ==========

@login_required
def global_search_htmx(request):
    """Global search across all entities"""
    query = request.GET.get('q', '')
    
    if len(query) < 2:
        return HttpResponse('')
    
    results = {
        'projects': Project.objects.filter(
            Q(owner=request.user) | Q(team_members=request.user),
            Q(name__icontains=query) | Q(description__icontains=query)
        )[:5],
        'tasks': Task.objects.filter(
            Q(assigned_to=request.user) | Q(created_by=request.user),
            Q(title__icontains=query) | Q(description__icontains=query)
        )[:5],
        'utilities': Utility.objects.filter(
            project__in=Project.objects.filter(
                Q(owner=request.user) | Q(team_members=request.user)
            ).distinct()
        ).filter(
            Q(name__icontains=query) | Q(type__icontains=query)
        )[:5]
    }
    
    return render(request, 'components/search_results.html', {
        'results': results,
        'query': query
    })


# ========== FILE UPLOAD HTMX VIEWS ==========

@login_required
@require_http_methods(["POST"])
def document_upload_htmx(request):
    """Handle document upload via HTMX"""
    
    form = DocumentForm(request.POST, request.FILES)
    
    if form.is_valid():
        document = form.save(commit=False)
        document.uploaded_by = request.user
        document.save()
        
        # Return document card
        return render(request, 'components/document_card.html', {
            'document': document
        })
    
    return render(request, 'components/document_form.html', {
        'form': form
    })


# ========== REAL-TIME POLLING FALLBACKS ==========

@login_required
def realtime_notifications_htmx(request):
    """Poll for new notifications (WebSocket fallback)"""
    last_check = request.GET.get('last_check')
    
    if last_check:
        # Convert timestamp
        last_check = datetime.fromisoformat(last_check)
        notifications = Notification.objects.filter(
            user=request.user,
            created_at__gt=last_check
        )
    else:
        notifications = []
    
    if notifications:
        return render(request, 'components/notification_items.html', {
            'notifications': notifications
        })
    
    # Return 286 status code for no updates (HTMX polling)
    return HttpResponse(status=286)


# Export the views for URL configuration
login_htmx = LoginHTMXView.as_view()
logout_htmx = LogoutHTMXView.as_view()
project_list_htmx = ProjectListHTMXView.as_view()
project_create_htmx = ProjectCreateHTMXView.as_view()
project_update_htmx = ProjectUpdateHTMXView.as_view()
project_delete_htmx = ProjectDeleteHTMXView.as_view()


"""