# HTMX Stub Views Added

This document lists all the HTMX stub views that were added to make the undefined URL references functional.

## Added to project_views.py

### Project List Page HTMX Endpoints
1. `project_stats_htmx` - Real-time project statistics for header
2. `projects_view_mode_htmx` - Handle view mode switching (cards/grid)
3. `project_stats_cards_htmx` - Real-time project statistics cards
4. `projects_filter_htmx` - Enhanced HTMX endpoint for filtering and searching projects
5. `projects_bulk_export_htmx` - Handle bulk export of selected projects
6. `projects_bulk_archive_htmx` - Handle bulk archiving of selected projects
7. `projects_bulk_delete_htmx` - Handle bulk deletion of selected projects

### Project Detail Page HTMX Endpoints
8. `project_header_metadata_htmx` - Project header metadata
9. `project_favorite_toggle_htmx` - Toggle project favorite status
10. `project_share_modal_htmx` - Project share modal
11. `project_status_htmx` - Project status display
12. `project_overview_edit_modal_htmx` - Project overview edit modal
13. `project_overview_content_htmx` - Project overview content
14. `project_conflict_detection_htmx` - Project conflict detection status
15. `project_communication_panel_htmx` - Project communication panel
16. `project_activity_feed_htmx` - Project activity feed
17. `project_team_add_modal_htmx` - Add team member modal
18. `project_team_list_htmx` - Project team list
19. `project_deadline_add_modal_htmx` - Add deadline modal
20. `project_deadlines_htmx` - Project deadlines list
21. `project_recent_documents_htmx` - Recent project documents
22. `project_stats_overview_htmx` - Project statistics overview

### Project Grid/Card HTMX Endpoints
23. `project_communication_modal_htmx` - Project communication modal
24. `project_status_update_modal_htmx` - Project status update modal
25. `project_stats_inline_htmx` - Inline project statistics for individual project cards

## Added URL Patterns

All stub views have corresponding URL patterns added to `CLEAR/urls.py`:

### In project_patterns:
- `projects/<str:project_id>/htmx/header-metadata/`
- `projects/<str:project_id>/htmx/favorite-toggle/`
- `projects/<str:project_id>/htmx/share-modal/`
- `projects/<str:project_id>/htmx/status/`
- `projects/<str:project_id>/htmx/communication-modal/`
- `projects/<str:project_id>/htmx/status-update-modal/`
- `projects/<str:project_id>/htmx/stats-overview/`
- `projects/<str:project_id>/htmx/overview-edit-modal/`
- `projects/<str:project_id>/htmx/overview-content/`
- `projects/<str:project_id>/htmx/conflict-detection/`
- `projects/<str:project_id>/htmx/communication-panel/`
- `projects/<str:project_id>/htmx/activity-feed/`
- `projects/<str:project_id>/htmx/team-add-modal/`
- `projects/<str:project_id>/htmx/team-list/`
- `projects/<str:project_id>/htmx/deadline-add-modal/`
- `projects/<str:project_id>/htmx/deadlines/`
- `projects/<str:project_id>/htmx/recent-documents/`
- `projects/<str:project_id>/htmx/stats-inline/`

### In htmx_patterns:
- `htmx/projects/stats/`
- `htmx/projects/stats-cards/`
- `htmx/projects/view-mode/`
- `htmx/projects/filter/`
- `htmx/projects/bulk-export/`
- `htmx/projects/bulk-archive/`
- `htmx/projects/bulk-delete/`

## Implementation Status

All views are currently implemented as stubs that return simple HTML responses indicating they are placeholders. Each stub:

1. Has proper `@login_required` decorator for authentication
2. Uses `@require_http_methods` to specify allowed HTTP methods
3. Returns a basic HTML response with placeholder content
4. Includes TODO comments where appropriate

## Next Steps

These stub implementations should be replaced with actual functionality as the features are developed. The stubs serve to:

1. Make all HTMX URL references functional (no 404 errors)
2. Provide a clear list of what needs to be implemented
3. Show the expected HTTP methods and response types
4. Allow the UI to be tested even with placeholder content