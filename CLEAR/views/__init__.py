"""
CLEAR Views Package

This package contains all Django views for the CLEAR application, organized by domain
for improved maintainability and reduced file sizes.

Domain modules:
- admin_views.py: Administrative functions and system monitoring
- auth_views.py: Authentication, login, logout, and MFA
- project_views.py: Core project management views
- task_views.py: Task management and workflow views
- analytics_views.py: Business analytics and reporting
- dashboard_views.py: Dashboard views with enhanced error handling
- document_views.py: Document management and collaboration
- messaging_views.py: Communication and messaging systems
- stakeholder_views.py: Stakeholder management
- mapping_views.py: GIS, spatial analysis, and mapping
- profile_views.py: User profile and settings
- htmx_views.py: HTMX endpoints for dynamic content updates
- knowledge_views.py: Knowledge base management
- reporting_views.py: Scheduled reports and advanced reporting

This structure follows industry standards for file size (200-500 lines per file)
and improves code maintainability, development velocity, and team collaboration.
"""

from .auth_views import *  # type: ignore
from .admin_views import *  # type: ignore
from .project_views import *  # type: ignore
from .gis_views import *  # type: ignore
from .task_views import *  # type: ignore
from .document_views import *  # type: ignore
from .messaging_views import *  # type: ignore
from .dashboard_views import *  # Import dashboard views with error handling  # type: ignore
from .htmx_views import *  # type: ignore
from .analytics_views import *  # type: ignore
from .knowledge_views import *  # type: ignore
from .mapping_views import *  # type: ignore
from .profile_views import *  # type: ignore
from .reporting_views import *  # type: ignore
from .stakeholder_views import *  # type: ignore
from . import test_auth  # Import test_auth module


# Import all views from domain modules to maintain backward compatibility
