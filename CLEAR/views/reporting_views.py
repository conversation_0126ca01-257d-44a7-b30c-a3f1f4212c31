"""
Reporting Views

This module contains views related to reports, analytics, and data exports.

import logging
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views.generic import (
from .imports_base import *
from ..services.analytics import AnalyticsEngine


    CreateView,
    DeleteView,
    DetailView,
    TemplateView,
    UpdateView,
)

logger = logging.getLogger(__name__)



class ReportDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'reporting/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user's reports
        reports = AnalyticsReport.objects.filter(
            Q(created_by=self.request.user) |
            Q(shared_with=self.request.user)
        ).distinct().order_by('-created_at')
        
        # Get recent report executions
        executions = ReportExecution.objects.filter(
            report__in=reports
        ).order_by('-executed_at')[:5]
        
        # Get scheduled reports
        scheduled_reports = reports.filter(
            is_scheduled=True
        )
        
        context.update({
            'reports': reports,
            'executions': executions,
            'scheduled_reports': scheduled_reports,
            'report_count': reports.count()
        })
        
        return context

class ReportDetailView(LoginRequiredMixin, DetailView):
    model = AnalyticsReport
    template_name = 'reporting/report_detail.html'
    context_object_name = 'report'
    
    def get_object(self, queryset=None):
        report = super().get_object(queryset)
        
        # Check if user has access to this report
        if not (self.request.user.is_staff or 
                report.created_by == self.request.user or 
                report.shared_with.filter(id=self.request.user.id).exists()):
            raise PermissionDenied
        
        return report
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report = self.get_object()
        
        # Get report executions
        executions = ReportExecution.objects.filter(
            report=report
        ).order_by('-executed_at')[:10]
        
        # Get latest execution
        latest_execution = executions.first()
        
        context.update({
            'executions': executions,
            'latest_execution': latest_execution
        })
        
        return context

class ReportCreateView(LoginRequiredMixin, CreateView):
    model = AnalyticsReport
    template_name = 'reporting/report_form.html'
    fields = ['name', 'description', 'query', 'parameters', 'is_scheduled', 'schedule_frequency', 'schedule_time']
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        
        # Create activity record
        response = super().form_valid(form)
        
        Activity.objects.create(
            user=self.request.user,
            action='created',
            target_model='AnalyticsReport',
            target_id=self.object.id,
            target_name=self.object.name,
            description=f"Created report: {self.object.name}"
        )
        
        return response
    
    def get_success_url(self):
        return reverse_lazy('report_detail', kwargs={'pk': self.object.pk})

class ReportUpdateView(LoginRequiredMixin, UpdateView):
    model = AnalyticsReport
    template_name = 'reporting/report_form.html'
    fields = ['name', 'description', 'query', 'parameters', 'is_scheduled', 'schedule_frequency', 'schedule_time']
    
    def get_object(self, queryset=None):
        report = super().get_object(queryset)
        
        # Check if user has access to edit this report
        if not (self.request.user.is_staff or report.created_by == self.request.user):
            raise PermissionDenied
        
        return report
    
    def form_valid(self, form):
        # Create activity record
        response = super().form_valid(form)
        
        Activity.objects.create(
            user=self.request.user,
            action='updated',
            target_model='AnalyticsReport',
            target_id=self.object.id,
            target_name=self.object.name,
            description=f"Updated report: {self.object.name}"
        )
        
        return response
    
    def get_success_url(self):
        return reverse_lazy('report_detail', kwargs={'pk': self.object.pk})

class ReportDeleteView(LoginRequiredMixin, DeleteView):
    model = AnalyticsReport
    template_name = 'reporting/report_confirm_delete.html'
    success_url = reverse_lazy('report_dashboard')
    
    def get_object(self, queryset=None):
        report = super().get_object(queryset)
        
        # Check if user has access to delete this report
        if not (self.request.user.is_staff or report.created_by == self.request.user):
            raise PermissionDenied
        
        return report
    
    def delete(self, request, *args, **kwargs):
        report = self.get_object()
        
        # Create activity record before deletion
        Activity.objects.create(
            user=self.request.user,
            action='deleted',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Deleted report: {report.name}"
        )
        
        return super().delete(request, *args, **kwargs)

@login_required
def report_dashboard(request):
    """View the reporting dashboard."""
    # Get user's reports
    reports = AnalyticsReport.objects.filter(
        Q(created_by=request.user) |
        Q(shared_with=request.user)
    ).distinct().order_by('-created_at')
    
    # Get recent report executions
    executions = ReportExecution.objects.filter(
        report__in=reports
    ).order_by('-executed_at')[:5]
    
    # Get scheduled reports
    scheduled_reports = reports.filter(
        is_scheduled=True
    )
    
    context = {
        'reports': reports,
        'executions': executions,
        'scheduled_reports': scheduled_reports,
        'report_count': reports.count()
    }
    
    return render(request, 'reporting/dashboard.html', context)

@login_required
def report_detail(request, report_id):
    """View a report's details."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Get report executions
    executions = ReportExecution.objects.filter(
        report=report
    ).order_by('-executed_at')[:10]
    
    # Get latest execution
    latest_execution = executions.first()
    
    context = {
        'report': report,
        'executions': executions,
        'latest_execution': latest_execution
    }
    
    return render(request, 'reporting/report_detail.html', context)

@login_required
def report_create(request):
    """Create a new report."""
    if request.method == 'POST':
        # Process form data
        name = request.POST.get('name')
        description = request.POST.get('description')
        query = request.POST.get('query')
        parameters = request.POST.get('parameters')
        is_scheduled = request.POST.get('is_scheduled') == 'on'
        schedule_frequency = request.POST.get('schedule_frequency')
        schedule_time = request.POST.get('schedule_time')
        
        if not name or not query:
            messages.error(request, 'Name and query are required.')
            return redirect('report_create')
        
        # Create report
        report = AnalyticsReport.objects.create(
            name=name,
            description=description,
            query=query,
            parameters=parameters,
            is_scheduled=is_scheduled,
            schedule_frequency=schedule_frequency,
            schedule_time=schedule_time,
            created_by=request.user
        )
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='created',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Created report: {report.name}"
        )
        
        messages.success(request, 'Report created successfully.')
        return redirect('report_detail', report_id=report.id)
    
    context = {
        'schedule_frequencies': [
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ]
    }
    
    return render(request, 'reporting/report_form.html', context)

@login_required
def report_update(request, report_id):
    """Update a report."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to edit this report
    if not (request.user.is_staff or report.created_by == request.user):
        return render(request, '403.html', status=403)
    
    if request.method == 'POST':
        # Process form data
        name = request.POST.get('name')
        description = request.POST.get('description')
        query = request.POST.get('query')
        parameters = request.POST.get('parameters')
        is_scheduled = request.POST.get('is_scheduled') == 'on'
        schedule_frequency = request.POST.get('schedule_frequency')
        schedule_time = request.POST.get('schedule_time')
        
        if not name or not query:
            messages.error(request, 'Name and query are required.')
            return redirect('report_update', report_id=report.id)
        
        # Update report
        report.name = name
        report.description = description
        report.query = query
        report.parameters = parameters
        report.is_scheduled = is_scheduled
        report.schedule_frequency = schedule_frequency
        report.schedule_time = schedule_time
        report.save()
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='updated',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Updated report: {report.name}"
        )
        
        messages.success(request, 'Report updated successfully.')
        return redirect('report_detail', report_id=report.id)
    
    context = {
        'report': report,
        'schedule_frequencies': [
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ]
    }
    
    return render(request, 'reporting/report_form.html', context)

@login_required
def report_delete(request, report_id):
    """Delete a report."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to delete this report
    if not (request.user.is_staff or report.created_by == request.user):
        return render(request, '403.html', status=403)
    
    if request.method == 'POST':
        # Create activity record before deletion
        Activity.objects.create(
            user=request.user,
            action='deleted',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Deleted report: {report.name}"
        )
        
        # Delete report
        report.delete()
        
        messages.success(request, 'Report deleted successfully.')
        return redirect('report_dashboard')
    
    context = {
        'report': report
    }
    
    return render(request, 'reporting/report_confirm_delete.html', context)

@login_required
def report_execute(request, report_id):
    """Execute a report."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Get parameters from request
    parameters = {}
    for key, value in request.GET.items():
        if key.startswith('param_'):
            param_name = key[6:]  # Remove 'param_' prefix
            parameters[param_name] = value
    
    try:
        # Execute report
        engine = AnalyticsEngine()
        result = engine.execute_report(report, parameters)
        
        # Create execution record
        execution = ReportExecution.objects.create(
            report=report,
            executed_by=request.user,
            parameters=parameters,
            result=result,
            status='completed'
        )
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='executed',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Executed report: {report.name}"
        )
        
        context = {
            'report': report,
            'execution': execution,
            'result': result
        }
        
        return render(request, 'reporting/report_result.html', context)
        
    except Exception as e:
        # Create failed execution record
        execution = ReportExecution.objects.create(
            report=report,
            executed_by=request.user,
            parameters=parameters,
            result={'error': str(e)},
            status='failed'
        )
        
        messages.error(request, f'Error executing report: {str(e)}')
        return redirect('report_detail', report_id=report.id)

@login_required
def report_execution_detail(request, execution_id):
    """View a report execution's details."""
    execution = get_object_or_404(ReportExecution, pk=execution_id)
    report = execution.report
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    context = {
        'report': report,
        'execution': execution,
        'result': execution.result
    }
    
    return render(request, 'reporting/execution_detail.html', context)

@login_required
def report_share(request, report_id):
    """Share a report with other users."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to share this report
    if not (request.user.is_staff or report.created_by == request.user):
        return render(request, '403.html', status=403)
    
    if request.method == 'POST':
        # Get users to share with
        user_ids = request.POST.getlist('users')
        users = User.objects.filter(id__in=user_ids)
        
        # Share report with selected users
        for user in users:
            report.shared_with.add(user)
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='shared',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Shared report: {report.name} with {len(users)} users"
        )
        
        messages.success(request, f'Report shared with {len(users)} users.')
        return redirect('report_detail', report_id=report.id)
    
    # Get users to potentially share with
    if request.user.is_staff:
        # Staff can share with any user
        users = User.objects.exclude(
            id__in=report.shared_with.values_list('id', flat=True)
        ).exclude(
            id=report.created_by.id
        ).order_by('username')
    else:
        # Regular users can only share with users in their organization
        if request.user.organization:
            users = User.objects.filter(
                organization=request.user.organization
            ).exclude(
                id__in=report.shared_with.values_list('id', flat=True)
            ).exclude(
                id=report.created_by.id
            ).order_by('username')
        else:
            users = User.objects.none()
    
    context = {
        'report': report,
        'users': users,
        'shared_with': report.shared_with.all()
    }
    
    return render(request, 'reporting/report_share.html', context)

@login_required
def report_unshare(request, report_id, user_id):
    """Remove a user's access to a report."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    user = get_object_or_404(User, pk=user_id)
    
    # Check if user has access to unshare this report
    if not (request.user.is_staff or report.created_by == request.user):
        return render(request, '403.html', status=403)
    
    if request.method == 'POST':
        # Remove user from shared_with
        report.shared_with.remove(user)
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='unshared',
            target_model='AnalyticsReport',
            target_id=report.id,
            target_name=report.name,
            description=f"Removed sharing for report: {report.name} with user {user.username}"
        )
        
        messages.success(request, f'Report access removed for {user.username}.')
        return redirect('report_share', report_id=report.id)
    
    context = {
        'report': report,
        'user_to_remove': user
    }
    
    return render(request, 'reporting/report_unshare_confirm.html', context)

@login_required
def report_export_csv(request, execution_id):
    """Export a report execution result as CSV."""
    execution = get_object_or_404(ReportExecution, pk=execution_id)
    report = execution.report
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Check if execution was successful
    if execution.status != 'completed':
        messages.error(request, 'Cannot export failed execution.')
        return redirect('report_execution_detail', execution_id=execution.id)
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{report.name}_{execution.executed_at.strftime("%Y%m%d_%H%M%S")}.csv"'
    
    # Get result data
    result_data = execution.result.get('data', [])
    
    if not result_data:
        messages.error(request, 'No data to export.')
        return redirect('report_execution_detail', execution_id=execution.id)
    
    # Create CSV writer
    writer = csv.writer(response)
    
    # Write header row
    if isinstance(result_data[0], dict):
        # Result is a list of dictionaries
        headers = result_data[0].keys()
        writer.writerow(headers)
        
        # Write data rows
        for row in result_data:
            writer.writerow([row.get(header, '') for header in headers])
    else:
        # Result is a list of lists
        writer.writerow(result_data[0])
        writer.writerows(result_data[1:])
    
    # Create activity record
    Activity.objects.create(
        user=request.user,
        action='exported',
        target_model='ReportExecution',
        target_id=execution.id,
        target_name=f"{report.name} execution",
        description=f"Exported report execution as CSV: {report.name}"
    )
    
    return response

@login_required
def report_export_json(request, execution_id):
    """Export a report execution result as JSON."""
    execution = get_object_or_404(ReportExecution, pk=execution_id)
    report = execution.report
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Check if execution was successful
    if execution.status != 'completed':
        messages.error(request, 'Cannot export failed execution.')
        return redirect('report_execution_detail', execution_id=execution.id)
    
    # Create JSON response
    response = HttpResponse(
        json.dumps(execution.result, indent=2),
        content_type='application/json'
    )
    response['Content-Disposition'] = f'attachment; filename="{report.name}_{execution.executed_at.strftime("%Y%m%d_%H%M%S")}.json"'
    
    # Create activity record
    Activity.objects.create(
        user=request.user,
        action='exported',
        target_model='ReportExecution',
        target_id=execution.id,
        target_name=f"{report.name} execution",
        description=f"Exported report execution as JSON: {report.name}"
    )
    
    return response

@login_required
def report_schedule(request, report_id):
    """Schedule a report for automatic execution."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to schedule this report
    if not (request.user.is_staff or report.created_by == request.user):
        return render(request, '403.html', status=403)
    
    if request.method == 'POST':
        # Process form data
        is_scheduled = request.POST.get('is_scheduled') == 'on'
        schedule_frequency = request.POST.get('schedule_frequency')
        schedule_time = request.POST.get('schedule_time')
        
        # Update report
        report.is_scheduled = is_scheduled
        report.schedule_frequency = schedule_frequency
        report.schedule_time = schedule_time
        report.save()
        
        # Create activity record
        if is_scheduled:
            Activity.objects.create(
                user=request.user,
                action='scheduled',
                target_model='AnalyticsReport',
                target_id=report.id,
                target_name=report.name,
                description=f"Scheduled report: {report.name} ({schedule_frequency} at {schedule_time})"
            )
            messages.success(request, f'Report scheduled for {schedule_frequency} execution at {schedule_time}.')
        else:
            Activity.objects.create(
                user=request.user,
                action='unscheduled',
                target_model='AnalyticsReport',
                target_id=report.id,
                target_name=report.name,
                description=f"Removed schedule for report: {report.name}"
            )
            messages.success(request, 'Report schedule removed.')
        
        return redirect('report_detail', report_id=report.id)
    
    context = {
        'report': report,
        'schedule_frequencies': [
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ]
    }
    
    return render(request, 'reporting/report_schedule.html', context)

@login_required
def htmx_report_list(request):
    """HTMX endpoint for listing reports."""
    # Get user's reports
    reports = AnalyticsReport.objects.filter(
        Q(created_by=request.user) |
        Q(shared_with=request.user)
    ).distinct().order_by('-created_at')
    
    # Apply filters if provided
    search_query = request.GET.get('search')
    if search_query:
        reports = reports.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Filter by creator
    creator_filter = request.GET.get('creator')
    if creator_filter:
        if creator_filter == 'me':
            reports = reports.filter(created_by=request.user)
        elif creator_filter == 'others':
            reports = reports.exclude(created_by=request.user)
    
    # Filter by schedule
    schedule_filter = request.GET.get('scheduled')
    if schedule_filter:
        if schedule_filter == 'yes':
            reports = reports.filter(is_scheduled=True)
        elif schedule_filter == 'no':
            reports = reports.filter(is_scheduled=False)
    
    # Paginate results
    paginator = Paginator(reports, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'creator_filter': creator_filter,
        'schedule_filter': schedule_filter
    }
    
    return render(request, 'reporting/partials/report_list.html', context)

@login_required
def htmx_report_executions(request, report_id):
    """HTMX endpoint for listing report executions."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get report executions
    executions = ReportExecution.objects.filter(
        report=report
    ).order_by('-executed_at')
    
    # Apply filters if provided
    status_filter = request.GET.get('status')
    if status_filter:
        executions = executions.filter(status=status_filter)
    
    # Filter by executor
    executor_filter = request.GET.get('executor')
    if executor_filter:
        if executor_filter == 'me':
            executions = executions.filter(executed_by=request.user)
        elif executor_filter == 'others':
            executions = executions.exclude(executed_by=request.user)
        elif executor_filter == 'system':
            executions = executions.filter(executed_by__isnull=True)
    
    # Paginate results
    paginator = Paginator(executions, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'report': report,
        'page_obj': page_obj,
        'status_filter': status_filter,
        'executor_filter': executor_filter
    }
    
    return render(request, 'reporting/partials/execution_list.html', context)

@login_required
def htmx_report_parameters_form(request, report_id):
    """HTMX endpoint for generating a report parameters form."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Parse parameters from report
    parameters = {}
    if report.parameters:
        try:
            parameters = json.loads(report.parameters)
        except json.JSONDecodeError:
            parameters = {}
    
    context = {
        'report': report,
        'parameters': parameters
    }
    
    return render(request, 'reporting/partials/parameters_form.html', context)

@login_required
def htmx_report_result_preview(request, execution_id):
    """HTMX endpoint for previewing a report execution result."""
    execution = get_object_or_404(ReportExecution, pk=execution_id)
    report = execution.report
    
    # Check if user has access to this report
    if not (request.user.is_staff or 
            report.created_by == request.user or 
            report.shared_with.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    context = {
        'report': report,
        'execution': execution,
        'result': execution.result
    }
    
    return render(request, 'reporting/partials/result_preview.html', context)

@login_required
def htmx_report_shared_users(request, report_id):
    """HTMX endpoint for listing users a report is shared with."""
    report = get_object_or_404(AnalyticsReport, pk=report_id)
    
    # Check if user has access to view sharing info
    if not (request.user.is_staff or report.created_by == request.user):
        return HttpResponse("Permission denied", status=403)
    
    # Get users the report is shared with
    shared_with = report.shared_with.all().order_by('username')
    
    context = {
        'report': report,
        'shared_with': shared_with
    }
    
    return render(request, 'reporting/partials/shared_users.html', context)

@login_required
def business_metrics_dashboard(request):
    """View the business metrics dashboard."""
    # Check if user has access to business metrics
    if not (request.user.is_staff or request.user.has_perm('CLEAR.view_businessmetric')):
        return render(request, '403.html', status=403)
    
    # Get all business metrics
    metrics = BusinessMetric.objects.all().order_by('name')
    
    # Group metrics by category
    metrics_by_category = {}
    for metric in metrics:
        category = metric.category or 'Uncategorized'
        if category not in metrics_by_category:
            metrics_by_category[category] = []
        metrics_by_category[category].append(metric)
    
    context = {
        'metrics_by_category': metrics_by_category
    }
    
    return render(request, 'reporting/business_metrics.html', context)

@login_required
def business_metric_detail(request, metric_id):
    """View a business metric's details."""
    metric = get_object_or_404(BusinessMetric, pk=metric_id)
    
    # Check if user has access to business metrics
    if not (request.user.is_staff or request.user.has_perm('CLEAR.view_businessmetric')):
        return render(request, '403.html', status=403)
    
    # Get historical data for the metric
    historical_data = metric.get_historical_data()
    
    # Calculate trend
    trend = None
    if len(historical_data) >= 2:
        current_value = historical_data[-1]['value']
        previous_value = historical_data[-2]['value']
        if current_value > previous_value:
            trend = 'up'
        elif current_value < previous_value:
            trend = 'down'
        else:
            trend = 'flat'
    
    context = {
        'metric': metric,
        'historical_data': historical_data,
        'trend': trend
    }
    
    return render(request, 'reporting/business_metric_detail.html', context)

@login_required
def project_report_dashboard(request, project_id):
    """View the reporting dashboard for a specific project."""
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Get project-specific reports
    reports = AnalyticsReport.objects.filter(
        Q(created_by=request.user) |
        Q(shared_with=request.user),
        parameters__contains=f'"project_id": {project_id}'
    ).distinct().order_by('-created_at')
    
    context = {
        'project': project,
        'reports': reports,
        'report_count': reports.count()
    }
    
    return render(request, 'reporting/project_reports.html', context)

@login_required
def generate_project_report(request, project_id):
    """Generate a standard report for a project."""
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    if request.method == 'POST':
        # Get report type
        report_type = request.POST.get('report_type')
        
        if report_type == 'summary':
            # Generate project summary report
            report_data = generate_project_summary_report(project)
            template = 'reporting/project_summary_report.html'
        elif report_type == 'activity':
            # Generate project activity report
            start_date = request.POST.get('start_date')
            end_date = request.POST.get('end_date')
            report_data = generate_project_activity_report(project, start_date, end_date)
            template = 'reporting/project_activity_report.html'
        elif report_type == 'conflicts':
            # Generate project conflicts report
            report_data = generate_project_conflicts_report(project)
            template = 'reporting/project_conflicts_report.html'
        else:
            messages.error(request, 'Invalid report type.')
            return redirect('project_report_dashboard', project_id=project.id)
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='generated',
            target_model='ProjectReport',
            target_id=project.id,
            target_name=f"{project.name} {report_type} report",
            description=f"Generated {report_type} report for project: {project.name}",
            project=project
        )
        
        context = {
            'project': project,
            'report_data': report_data,
            'report_type': report_type,
            'generated_at': timezone.now(),
            'generated_by': request.user
        }
        
        return render(request, template, context)
    
    context = {
        'project': project,
        'report_types': [
            ('summary', 'Project Summary'),
            ('activity', 'Project Activity'),
            ('conflicts', 'Project Conflicts')
        ]
    }
    
    return render(request, 'reporting/generate_project_report.html', context)

def generate_project_summary_report(project):
    """Generate a summary report for a project."""
    # Get project statistics
    members_count = project.members.count()
    tasks_count = Task.objects.filter(project=project).count()
    completed_tasks_count = Task.objects.filter(project=project, status='completed').count()
    documents_count = Document.objects.filter(project=project).count()
    utilities_count = UtilityLineData.objects.filter(project=project).count()
    conflicts_count = Conflict.objects.filter(project=project).count()
    open_conflicts_count = Conflict.objects.filter(project=project, status='open').count()
    
    # Calculate task completion rate
    task_completion_rate = 0
    if tasks_count > 0:
        task_completion_rate = (completed_tasks_count / tasks_count) * 100
    
    # Get recent activities
    recent_activities = Activity.objects.filter(
        project=project
    ).order_by('-timestamp')[:10]
    
    # Get top contributors
    top_contributors = Activity.objects.filter(
        project=project
    ).values('user__username').annotate(
        count=Count('id')
    ).order_by('-count')[:5]
    
    return {
        'members_count': members_count,
        'tasks_count': tasks_count,
        'completed_tasks_count': completed_tasks_count,
        'task_completion_rate': task_completion_rate,
        'documents_count': documents_count,
        'utilities_count': utilities_count,
        'conflicts_count': conflicts_count,
        'open_conflicts_count': open_conflicts_count,
        'recent_activities': recent_activities,
        'top_contributors': top_contributors
    }

def generate_project_activity_report(project, start_date=None, end_date=None):
    """Generate an activity report for a project."""
    # Parse date range
    try:
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        else:
            start_date = (timezone.now() - timedelta(days=30)).date()
        
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        else:
            end_date = timezone.now().date()
    except ValueError:
        # Default to last 30 days if date parsing fails
        start_date = (timezone.now() - timedelta(days=30)).date()
        end_date = timezone.now().date()
    
    # Get activities in date range
    activities = Activity.objects.filter(
        project=project,
        timestamp__date__gte=start_date,
        timestamp__date__lte=end_date
    ).order_by('-timestamp')
    
    # Group activities by date
    activities_by_date = {}
    for activity in activities:
        date_str = activity.timestamp.date().strftime('%Y-%m-%d')
        if date_str not in activities_by_date:
            activities_by_date[date_str] = []
        activities_by_date[date_str].append(activity)
    
    # Group activities by user
    activities_by_user = {}
    for activity in activities:
        username = activity.user.username if activity.user else 'System'
        if username not in activities_by_user:
            activities_by_user[username] = []
        activities_by_user[username].append(activity)
    
    # Group activities by action
    activities_by_action = {}
    for activity in activities:
        action = activity.action
        if action not in activities_by_action:
            activities_by_action[action] = []
        activities_by_action[action].append(activity)
    
    return {
        'start_date': start_date,
        'end_date': end_date,
        'activities': activities,
        'activities_by_date': activities_by_date,
        'activities_by_user': activities_by_user,
        'activities_by_action': activities_by_action,
        'total_activities': activities.count()
    }

def generate_project_conflicts_report(project):
    """Generate a conflicts report for a project."""
    # Get conflicts
    conflicts = Conflict.objects.filter(project=project)
    
    # Group conflicts by status
    conflicts_by_status = {}
    for conflict in conflicts:
        status = conflict.status
        if status not in conflicts_by_status:
            conflicts_by_status[status] = []
        conflicts_by_status[status].append(conflict)
    
    # Group conflicts by severity
    conflicts_by_severity = {}
    for conflict in conflicts:
        severity = conflict.severity
        if severity not in conflicts_by_severity:
            conflicts_by_severity[severity] = []
        conflicts_by_severity[severity].append(conflict)
    
    # Group conflicts by utility type
    conflicts_by_utility_type = {}
    for conflict in conflicts:
        if conflict.utility1 and conflict.utility2:
            utility_types = f"{conflict.utility1.utility_type} & {conflict.utility2.utility_type}"
            if utility_types not in conflicts_by_utility_type:
                conflicts_by_utility_type[utility_types] = []
            conflicts_by_utility_type[utility_types].append(conflict)
    
    return {
        'conflicts': conflicts,
        'conflicts_by_status': conflicts_by_status,
        'conflicts_by_severity': conflicts_by_severity,
        'conflicts_by_utility_type': conflicts_by_utility_type,
        'total_conflicts': conflicts.count(),
        'open_conflicts': conflicts.filter(status='open').count(),
        'resolved_conflicts': conflicts.filter(status='resolved').count(),
        'critical_conflicts': conflicts.filter(severity='critical').count()
    }

@login_required
def export_project_report_csv(request, project_id):
    """Export a project report as CSV."""
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Get report type
    report_type = request.GET.get('type', 'summary')
    
    # Generate report data
    if report_type == 'summary':
        report_data = generate_project_summary_report(project)
        filename = f"{project.name}_summary_report.csv"
    elif report_type == 'activity':
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        report_data = generate_project_activity_report(project, start_date, end_date)
        filename = f"{project.name}_activity_report.csv"
    elif report_type == 'conflicts':
        report_data = generate_project_conflicts_report(project)
        filename = f"{project.name}_conflicts_report.csv"
    else:
        return HttpResponse("Invalid report type", status=400)
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    # Create CSV writer
    writer = csv.writer(response)
    
    # Write CSV content based on report type
    if report_type == 'summary':
        writer.writerow(['Project Summary Report', project.name])
        writer.writerow(['Generated At', timezone.now()])
        writer.writerow(['Generated By', request.user.username])
        writer.writerow([])
        
        writer.writerow(['Metric', 'Value'])
        writer.writerow(['Members Count', report_data['members_count']])
        writer.writerow(['Tasks Count', report_data['tasks_count']])
        writer.writerow(['Completed Tasks', report_data['completed_tasks_count']])
        writer.writerow(['Task Completion Rate', f"{report_data['task_completion_rate']:.2f}%"])
        writer.writerow(['Documents Count', report_data['documents_count']])
        writer.writerow(['Utilities Count', report_data['utilities_count']])
        writer.writerow(['Conflicts Count', report_data['conflicts_count']])
        writer.writerow(['Open Conflicts', report_data['open_conflicts_count']])
        
        writer.writerow([])
        writer.writerow(['Top Contributors'])
        writer.writerow(['Username', 'Activity Count'])
        for contributor in report_data['top_contributors']:
            writer.writerow([contributor['user__username'], contributor['count']])
        
        writer.writerow([])
        writer.writerow(['Recent Activities'])
        writer.writerow(['Timestamp', 'User', 'Action', 'Description'])
        for activity in report_data['recent_activities']:
            writer.writerow([
                activity.timestamp,
                activity.user.username if activity.user else 'System',
                activity.action,
                activity.description
            ])
    
    elif report_type == 'activity':
        writer.writerow(['Project Activity Report', project.name])
        writer.writerow(['Generated At', timezone.now()])
        writer.writerow(['Generated By', request.user.username])
        writer.writerow(['Date Range', f"{report_data['start_date']} to {report_data['end_date']}"])
        writer.writerow([])
        
        writer.writerow(['Activities'])
        writer.writerow(['Timestamp', 'User', 'Action', 'Target', 'Description'])
        for activity in report_data['activities']:
            writer.writerow([
                activity.timestamp,
                activity.user.username if activity.user else 'System',
                activity.action,
                f"{activity.target_model}: {activity.target_name}",
                activity.description
            ])
    
    elif report_type == 'conflicts':
        writer.writerow(['Project Conflicts Report', project.name])
        writer.writerow(['Generated At', timezone.now()])
        writer.writerow(['Generated By', request.user.username])
        writer.writerow([])
        
        writer.writerow(['Conflict Statistics'])
        writer.writerow(['Total Conflicts', report_data['total_conflicts']])
        writer.writerow(['Open Conflicts', report_data['open_conflicts']])
        writer.writerow(['Resolved Conflicts', report_data['resolved_conflicts']])
        writer.writerow(['Critical Conflicts', report_data['critical_conflicts']])
        
        writer.writerow([])
        writer.writerow(['Conflicts'])
        writer.writerow(['ID', 'Description', 'Status', 'Severity', 'Utility 1', 'Utility 2', 'Detected By', 'Detected At'])
        for conflict in report_data['conflicts']:
            writer.writerow([
                conflict.id,
                conflict.description,
                conflict.status,
                conflict.severity,
                conflict.utility1.name if conflict.utility1 else 'N/A',
                conflict.utility2.name if conflict.utility2 else 'N/A',
                conflict.detected_by.username if conflict.detected_by else 'System',
                conflict.created_at
            ])
    
    # Create activity record
    Activity.objects.create(
        user=request.user,
        action='exported',
        target_model='ProjectReport',
        target_id=project.id,
        target_name=f"{project.name} {report_type} report",
        description=f"Exported {report_type} report as CSV for project: {project.name}",
        project=project
    )
    
    return response
"""