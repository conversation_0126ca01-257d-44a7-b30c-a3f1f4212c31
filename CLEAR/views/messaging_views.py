"""
Messaging Views - Updated with additional whisper and conversation views

This module contains views related to messaging, chat, and communication features.
Includes whisper messaging, conversation management, and chat interfaces.

"""
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView, TemplateView
from ..models.auth import User
from ..models.messaging import (
from .imports_base import *
from ..services.notifications import message_notification_service


    ChatMessage,
    Conversation,
    ConversationMember,
    MessageRead,
    WhisperMessage,
)


@login_required
def messaging_interface(request):
    """Main messaging interface"""
    # Get user's conversations
    conversations = Conversation.objects.filter(
        participants=request.user,
        is_active=True
    ).select_related('project', 'created_by').prefetch_related('participants')[:20]
    
    # Get recent messages from active conversation or first conversation
    active_conversation_id = request.GET.get('conversation')
    active_conversation = None
    messages = []
    
    if active_conversation_id:
        try:
            active_conversation = conversations.get(id=active_conversation_id)
            messages = active_conversation.messages.select_related('user').order_by('created_at')[:50]
        except Conversation.DoesNotExist:
            pass
    elif conversations.exists():
        active_conversation = conversations.first()
        messages = active_conversation.messages.select_related('user').order_by('created_at')[:50]
    
    context = {
        'conversations': conversations,
        'active_conversation': active_conversation,
        'messages': messages,
        'user': request.user,
    }
    
    return render(request, 'messaging/chat_interface.html', context)


@login_required
@require_http_methods(["GET"])
def conversation_list_htmx(request):
    """HTMX endpoint for conversation list"""
    conversations = Conversation.objects.filter(
        participants=request.user,
        is_active=True
    ).select_related('project', 'created_by').prefetch_related('participants')[:20]
    
    # Add unread count for each conversation
    for conversation in conversations:
        conversation.unread_count = conversation.get_unread_count_for_user(request.user)
    
    context = {
        'conversations': conversations,
        'user': request.user,
    }
    
    return render(request, 'components/messages/conversation_list.html', context)


@login_required
@require_http_methods(["GET"])
def message_thread_htmx(request, conversation_id):
    """HTMX endpoint for message thread"""
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
        
        # Mark conversation as read
        member = conversation.members.filter(user=request.user).first()
        if member:
            member.mark_read()
        
        # Get messages with pagination
        page = request.GET.get('page', 1)
        messages = conversation.messages.select_related('user').order_by('created_at')
        paginator = Paginator(messages, 50)
        page_obj = paginator.get_page(page)
        
        context = {
            'conversation': conversation,
            'messages': page_obj,
            'user': request.user,
        }
        
        return render(request, 'components/messages/message_thread.html', context)
        
    except Conversation.DoesNotExist:
        return HttpResponse('Conversation not found', status=404)


@login_required
@require_http_methods(["POST"])
def conversation_create_htmx(request):
    """HTMX endpoint for creating new conversations"""
    name = request.POST.get('name', '').strip()
    participant_ids = request.POST.getlist('participants')
    project_id = request.POST.get('project_id')
    is_group = request.POST.get('is_group', 'false').lower() == 'true'
    
    if not participant_ids:
        return HttpResponse('Participants required', status=400)
    
    try:
        # Get participants
        participants = User.objects.filter(id__in=participant_ids)
        if not participants.exists():
            return HttpResponse('Invalid participants', status=400)
        
        # Get project if specified
        project = None
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return HttpResponse('Project not found', status=404)
        
        # Create conversation
        conversation = Conversation.objects.create(
            name=name or 'New Conversation',
            created_by=request.user,
            project=project,
            is_group=is_group,
            is_active=True
        )
        
        # Add participants
        conversation.participants.add(request.user)
        conversation.participants.add(*participants)
        
        # Create conversation members
        for participant in participants:
            ConversationMember.objects.create(
                conversation=conversation,
                user=participant,
                joined_at=timezone.now()
            )
        
        ConversationMember.objects.create(
            conversation=conversation,
            user=request.user,
            joined_at=timezone.now()
        )
        
        # Return updated conversation list
        return conversation_list_htmx(request)
        
    except Exception as e:
        return HttpResponse(f'Error creating conversation: {str(e)}', status=400)


@login_required
@require_http_methods(["POST"])
def message_create_htmx(request):
    """HTMX endpoint for creating new messages"""
    conversation_id = request.POST.get('conversation_id')
    content = request.POST.get('content', '').strip()
    
    if not content:
        return HttpResponse('Message content required', status=400)
    
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
        
        # Create the message
        ChatMessage.objects.create(
            user=request.user,
            conversation=conversation,
            content=content,
            created_at=timezone.now()
        )
        
        # Update conversation last activity
        conversation.updated_at = timezone.now()
        conversation.save()
        
        # Return updated message thread
        return message_thread_htmx(request, conversation_id)
        
    except Conversation.DoesNotExist:
        return HttpResponse('Conversation not found', status=404)
    except Exception as e:
        return HttpResponse(f'Error creating message: {str(e)}', status=400)

class MessagesView(LoginRequiredMixin, TemplateView):
    template_name = 'messaging/messages.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user's conversations
        conversations = get_user_conversations(self.request.user)
        
        # Get unread counts
        unread_counts = get_unread_message_counts(self.request.user)
        
        context.update({
            'conversations': conversations,
            'unread_counts': unread_counts,
            'active_tab': 'messages'
        })
        
        return context

@login_required
def messages_view(request):
    # Get user's conversations
    conversations = get_user_conversations(request.user)
    
    # Get unread counts
    unread_counts = get_unread_message_counts(request.user)
    
    # Get active conversation if specified
    active_conversation_id = request.GET.get('conversation')
    active_conversation = None
    messages = []
    
    if active_conversation_id:
        try:
            active_conversation = Conversation.objects.get(
                id=active_conversation_id,
                members=request.user
            )
            messages = ChatMessage.objects.filter(
                conversation=active_conversation
            ).order_by('created_at')
            
            # Mark messages as read
            unread_messages = messages.filter(
                
                message_reads__isnull=True
            )
            
            for message in unread_messages:
                MessageRead.objects.create(
                    message=message,
                    user=request.user,
                    read_at=timezone.now()
                )
                
        except Conversation.DoesNotExist:
            pass
    
    context = {
        'conversations': conversations,
        'active_conversation': active_conversation,
        'messages': messages,
        'unread_counts': unread_counts,
        'active_tab': 'messages'
    }
    
    return render(request, 'messaging/messages.html', context)

@login_required
def messages_list_htmx(request):
    # Get category filter
    category = request.GET.get('category', 'all')
    
    # Get search query
    search_query = request.GET.get('search', '')
    
    # Get user's conversations
    conversations = get_user_conversations(request.user, category, search_query)
    
    # Get unread counts
    unread_counts = get_unread_message_counts(request.user)
    
    context = {
        'conversations': conversations,
        'unread_counts': unread_counts,
        'active_category': category
    }
    
    return render(request, 'messaging/partials/conversation_list.html', context)

@login_required
def message_thread_htmx(request, conversation_id):
    try:
        # Get conversation and verify user has access
        conversation = Conversation.objects.get(
            id=conversation_id,
            members=request.user
        )
        
        # Get messages
        messages = ChatMessage.objects.filter(
            conversation=conversation
        ).order_by('created_at')
        
        # Mark messages as read
        unread_messages = messages.filter(
            
            message_reads__isnull=True
        )
        
        for message in unread_messages:
            MessageRead.objects.create(
                message=message,
                user=request.user,
                read_at=timezone.now()
            )
        
        # Get conversation members
        members = conversation.members.all()
        
        context = {
            'conversation': conversation,
            'messages': messages,
            'members': members
        }
        
        return render(request, 'messaging/partials/message_thread.html', context)
        
    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)

@login_required
def message_send_htmx(request):
    if request.method == 'POST':
        conversation_id = request.POST.get('conversation_id')
        content = request.POST.get('content')
        
        if not content or not conversation_id:
            return HttpResponse("Message content and conversation ID are required", status=400)
        
        try:
            # Get conversation and verify user has access
            conversation = Conversation.objects.get(
                id=conversation_id,
                members=request.user
            )
            
            # Create message
            message = ChatMessage.objects.create(
                sender=request.user,
                conversation=conversation,
                content=content
            )
            
            # Process mentions
            mention_pattern = r'@(\w+)'
            mentions = re.findall(mention_pattern, content)
            
            for username in mentions:
                try:
                    mentioned_user = User.objects.get(username=username)
                    if mentioned_user in conversation.members.all():
                        MessageMention.objects.create(
                            message=message,
                            user=mentioned_user
                        )
                        
                        # Create notification for mention
                        Notification.objects.create(
                            user=mentioned_user,
                            sender=request.user,
                            notification_type='mention',
                            content=f"{request.user.get_full_name()} mentioned you in a message",
                            link=f"/messages/?conversation={conversation.id}"
                        )
                except User.DoesNotExist:
                    pass
            
            # Process reactions (emoji)
            emoji_pattern = r':([a-z_]+):'
            emojis = re.findall(emoji_pattern, content)
            
            for emoji in emojis:
                MessageReaction.objects.create(
                    message=message,
                    user=request.user,
                    reaction=emoji
                )
            
            # Update conversation last activity
            conversation.last_activity = timezone.now()
            conversation.save()
            
            # Create activity record
            if conversation.project:
                Activity.objects.create(
                    user=request.user,
                    action='sent',
                    target_model='Message',
                    target_id=message.id,
                    target_name='message',
                    project=conversation.project
                )
            
            # Return the new message
            return render(request, 'messaging/partials/message_item.html', {
                'message': message,
                'conversation': conversation
            })
            
        except Conversation.DoesNotExist:
            return HttpResponse("Conversation not found", status=404)
    
    return HttpResponse("Invalid request", status=400)

@login_required
def message_search_htmx(request):
    search_query = request.GET.get('query', '')
    
    if not search_query or len(search_query) < 3:
        return render(request, 'messaging/partials/message_search_results.html', {
            'results': [],
            'query': search_query
        })
    
    # Search for messages
    messages = ChatMessage.objects.filter(
        Q(content__icontains=search_query),
        Q(conversation__members=request.user)
    ).order_by('-created_at')[:20]
    
    # Group by conversation
    results = {}
    
    for message in messages:
        if message.conversation_id not in results:
            results[message.conversation_id] = {
                'conversation': message.conversation,
                'messages': []
            }
        
        results[message.conversation_id]['messages'].append(message)
    
    context = {
        'results': results.values(),
        'query': search_query
    }
    
    return render(request, 'messaging/partials/message_search_results.html', context)

@login_required
def conversation_create_htmx(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        member_ids = request.POST.getlist('members')
        project_id = request.POST.get('project_id')
        
        if not member_ids:
            return HttpResponse("At least one member is required", status=400)
        
        # Create conversation
        conversation = Conversation.objects.create(
            name=name,
            created_by=request.user,
            is_group=len(member_ids) > 1
        )
        
        # Add members
        conversation.members.add(request.user)
        
        for member_id in member_ids:
            try:
                member = User.objects.get(id=member_id)
                conversation.members.add(member)
                
                # Create conversation member record
                ConversationMember.objects.create(
                    conversation=conversation,
                    user=member,
                    added_by=request.user
                )
                
                # Create notification for new conversation
                if member != request.user:
                    Notification.objects.create(
                        user=member,
                        sender=request.user,
                        notification_type='conversation',
                        content=f"{request.user.get_full_name()} added you to a conversation",
                        link=f"/messages/?conversation={conversation.id}"
                    )
            except User.DoesNotExist:
                pass
        
        # Add project if specified
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
                conversation.project = project
                conversation.save()
            except Project.DoesNotExist:
                pass
        
        # Create welcome message
        ChatMessage.objects.create(
            sender=request.user,
            conversation=conversation,
            content=f"Conversation started by {request.user.get_full_name()}"
        )
        
        # Return the new conversation
        return render(request, 'messaging/partials/conversation_item.html', {
            'conversation': conversation,
            'unread_count': 0
        })
    
    # GET request - show form
    # Get potential members (users the current user has interacted with)
    potential_members = User.objects.filter(
        Q(created_projects__members=request.user) |
        Q(project_memberships__project__created_by=request.user) |
        Q(conversation_memberships__conversation__members=request.user)
    ).distinct().exclude(id=request.user.id)
    
    # Get user's projects
    projects = Project.objects.filter(
        Q(members=request.user) | 
        Q(created_by=request.user)
    ).distinct()
    
    context = {
        'potential_members': potential_members,
        'projects': projects
    }
    
    return render(request, 'messaging/partials/conversation_create_form.html', context)

@login_required
def team_chat_partial(request):
    # Get project ID
    project_id = request.GET.get('project_id')
    
    if not project_id:
        return HttpResponse("Project ID is required", status=400)
    
    try:
        # Get project and verify user has access
        project = Project.objects.get(id=project_id)
        
        if not user_has_project_access(request.user, project):
            return HttpResponse("Access denied", status=403)
        
        # Get or create project conversation
        conversation, created = Conversation.objects.get_or_create(
            project=project,
            is_project_conversation=True,
            defaults={
                'name': f"{project.name} Team Chat",
                'created_by': request.user
            }
        )
        
        # If conversation was just created, add project members
        if created:
            # Add project members to conversation
            for member in project.members.all():
                conversation.members.add(member)
                
                # Create conversation member record
                ConversationMember.objects.create(
                    conversation=conversation,
                    user=member,
                    added_by=request.user
                )
            
            # Add project creator if not already a member
            if project.created_by not in project.members.all():
                conversation.members.add(project.created_by)
                
                # Create conversation member record
                ConversationMember.objects.create(
                    conversation=conversation,
                    user=project.created_by,
                    added_by=request.user
                )
            
            # Create welcome message
            ChatMessage.objects.create(
                sender=request.user,
                conversation=conversation,
                content=f"Project team chat started by {request.user.get_full_name()}"
            )
        
        # Get messages
        messages = ChatMessage.objects.filter(
            conversation=conversation
        ).order_by('created_at')
        
        # Mark messages as read
        unread_messages = messages.filter(
            
            message_reads__isnull=True
        )
        
        for message in unread_messages:
            MessageRead.objects.create(
                message=message,
                user=request.user,
                read_at=timezone.now()
            )
        
        context = {
            'conversation': conversation,
            'messages': messages,
            'project': project
        }
        
        return render(request, 'projects/partials/team_chat.html', context)
        
    except Project.DoesNotExist:
        return HttpResponse("Project not found", status=404)

@login_required
def send_team_message(request):
    if request.method == 'POST':
        conversation_id = request.POST.get('conversation_id')
        content = request.POST.get('content')
        
        if not content or not conversation_id:
            return HttpResponse("Message content and conversation ID are required", status=400)
        
        try:
            # Get conversation and verify user has access
            conversation = Conversation.objects.get(
                id=conversation_id,
                members=request.user
            )
            
            # Create message
            message = ChatMessage.objects.create(
                sender=request.user,
                conversation=conversation,
                content=content
            )
            
            # Process mentions
            mention_pattern = r'@(\w+)'
            mentions = re.findall(mention_pattern, content)
            
            for username in mentions:
                try:
                    mentioned_user = User.objects.get(username=username)
                    if mentioned_user in conversation.members.all():
                        MessageMention.objects.create(
                            message=message,
                            user=mentioned_user
                        )
                        
                        # Create notification for mention
                        Notification.objects.create(
                            user=mentioned_user,
                            sender=request.user,
                            notification_type='mention',
                            content=f"{request.user.get_full_name()} mentioned you in a message",
                            link=f"/projects/{conversation.project.id}/chat" if conversation.project else f"/messages/?conversation={conversation.id}"
                        )
                except User.DoesNotExist:
                    pass
            
            # Update conversation last activity
            conversation.last_activity = timezone.now()
            conversation.save()
            
            # Create activity record
            if conversation.project:
                Activity.objects.create(
                    user=request.user,
                    action='sent',
                    target_model='Message',
                    target_id=message.id,
                    target_name='team message',
                    project=conversation.project
                )
            
            # Return the new message
            return render(request, 'projects/partials/team_message_item.html', {
                'message': message
            })
            
        except Conversation.DoesNotExist:
            return HttpResponse("Conversation not found", status=404)
    
    return HttpResponse("Invalid request", status=400)

@login_required
def message_mark_read_htmx(request, message_id):
    try:
        # Get message and verify user has access
        message = ChatMessage.objects.get(
            id=message_id,
            conversation__members=request.user
        )
        
        # Check if already read
        if MessageRead.objects.filter(message=message, user=request.user).exists():
            return HttpResponse("Message already marked as read", status=200)
        
        # Mark as read
        MessageRead.objects.create(
            message=message,
            user=request.user,
            read_at=timezone.now()
        )
        
        return HttpResponse("Message marked as read", status=200)
        
    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found", status=404)

@login_required
def conversation_mark_read_htmx(request, conversation_id):
    try:
        # Get conversation and verify user has access
        conversation = Conversation.objects.get(
            id=conversation_id,
            members=request.user
        )
        
        # Get unread messages
        unread_messages = ChatMessage.objects.filter(
            conversation=conversation,
            message_reads__isnull=True
        ).exclude(sender=request.user)
        
        # Mark all as read
        for message in unread_messages:
            MessageRead.objects.create(
                message=message,
                user=request.user,
                read_at=timezone.now()
            )
        
        # Get updated unread counts
        unread_counts = get_unread_message_counts(request.user)
        
        return JsonResponse({
            'status': 'success',
            'marked_read': unread_messages.count(),
            'unread_counts': unread_counts
        })
        
    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)

@login_required
def message_reaction_add_htmx(request):
    if request.method == 'POST':
        message_id = request.POST.get('message_id')
        reaction = request.POST.get('reaction')
        
        if not message_id or not reaction:
            return HttpResponse("Message ID and reaction are required", status=400)
        
        try:
            # Get message and verify user has access
            message = ChatMessage.objects.get(
                id=message_id,
                conversation__members=request.user
            )
            
            # Check if reaction already exists
            existing_reaction = MessageReaction.objects.filter(
                message=message,
                user=request.user,
                reaction=reaction
            ).first()
            
            if existing_reaction:
                # Remove reaction if it already exists
                existing_reaction.delete()
            else:
                # Add reaction
                MessageReaction.objects.create(
                    message=message,
                    user=request.user,
                    reaction=reaction
                )
            
            # Get updated reactions
            reactions = MessageReaction.objects.filter(
                message=message
            ).values('reaction').annotate(count=Count('id'))
            
            # Format reactions for response
            formatted_reactions = []
            for r in reactions:
                formatted_reactions.append({
                    'reaction': r['reaction'],
                    'count': r['count'],
                    'users': list(MessageReaction.objects.filter(
                        message=message,
                        reaction=r['reaction']
                    ).values_list('user__username', flat=True))
                })
            
            return render(request, 'messaging/partials/message_reactions.html', {
                'message': message,
                'reactions': formatted_reactions
            })
            
        except ChatMessage.DoesNotExist:
            return HttpResponse("Message not found", status=404)
    
    return HttpResponse("Invalid request", status=400)

@login_required
def message_reaction_picker_htmx(request):
    message_id = request.GET.get('message_id')
    
    if not message_id:
        return HttpResponse("Message ID is required", status=400)
    
    try:
        # Get message and verify user has access
        message = ChatMessage.objects.get(
            id=message_id,
            conversation__members=request.user
        )
        
        # Get common reactions
        common_reactions = [
            {'code': 'thumbs_up', 'emoji': '👍'},
            {'code': 'thumbs_down', 'emoji': '👎'},
            {'code': 'heart', 'emoji': '❤️'},
            {'code': 'laugh', 'emoji': '😂'},
            {'code': 'surprised', 'emoji': '😮'},
            {'code': 'sad', 'emoji': '😢'},
            {'code': 'angry', 'emoji': '😡'},
            {'code': 'clap', 'emoji': '👏'},
            {'code': 'fire', 'emoji': '🔥'},
            {'code': 'tada', 'emoji': '🎉'}
        ]
        
        return render(request, 'messaging/partials/reaction_picker.html', {
            'message': message,
            'common_reactions': common_reactions
        })
        
    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found", status=404)

@login_required
def message_file_upload_htmx(request):
    if request.method == 'POST':
        conversation_id = request.POST.get('conversation_id')
        file = request.FILES.get('file')
        
        if not conversation_id or not file:
            return HttpResponse("Conversation ID and file are required", status=400)
        
        try:
            # Get conversation and verify user has access
            conversation = Conversation.objects.get(
                id=conversation_id,
                members=request.user
            )
            
            # Create document
            document = Document.objects.create(
                name=file.name,
                file=file,
                uploaded_by=request.user,
                project=conversation.project
            )
            
            # Create message with document reference
            message = ChatMessage.objects.create(
                sender=request.user,
                conversation=conversation,
                content=f"Shared a file: {file.name}",
                document=document
            )
            
            # Update conversation last activity
            conversation.last_activity = timezone.now()
            conversation.save()
            
            # Create activity record
            if conversation.project:
                Activity.objects.create(
                    user=request.user,
                    action='shared',
                    target_model='Document',
                    target_id=document.id,
                    target_name=document.name,
                    project=conversation.project
                )
            
            # Return the new message
            return render(request, 'messaging/partials/message_item.html', {
                'message': message,
                'conversation': conversation
            })
            
        except Conversation.DoesNotExist:
            return HttpResponse("Conversation not found", status=404)
    
    return render(request, 'messaging/partials/file_upload_form.html')

@login_required
def message_thread_create_htmx(request):
    if request.method == 'POST':
        message_id = request.POST.get('message_id')
        
        if not message_id:
            return HttpResponse("Message ID is required", status=400)
        
        try:
            # Get message and verify user has access
            message = ChatMessage.objects.get(
                id=message_id,
                conversation__members=request.user
            )
            
            # Create thread if it doesn't exist
            if not message.thread:
                thread = MessageThread.objects.create(
                    original_message=message,
                    created_by=request.user
                )
                message.thread = thread
                message.save()
            
            return render(request, 'messaging/partials/message_thread_form.html', {
                'message': message
            })
            
        except ChatMessage.DoesNotExist:
            return HttpResponse("Message not found", status=404)
    
    return HttpResponse("Invalid request", status=400)

@login_required
def message_thread_reply_htmx(request):
    if request.method == 'POST':
        thread_id = request.POST.get('thread_id')
        content = request.POST.get('content')
        
        if not thread_id or not content:
            return HttpResponse("Thread ID and content are required", status=400)
        
        try:
            # Get thread and verify user has access
            thread = MessageThread.objects.get(
                id=thread_id,
                original_message__conversation__members=request.user
            )
            
            # Create reply
            reply = ChatMessage.objects.create(
                sender=request.user,
                conversation=thread.original_message.conversation,
                content=content,
                parent_thread=thread
            )
            
            # Notify original message sender if different from current user
            if thread.original_message.sender != request.user:
                Notification.objects.create(
                    user=thread.original_message.sender,
                    sender=request.user,
                    notification_type='thread_reply',
                    content=f"{request.user.get_full_name()} replied to your message",
                    link=f"/messages/?conversation={thread.original_message.conversation.id}&thread={thread.id}"
                )
            
            # Return the new reply
            return render(request, 'messaging/partials/thread_reply_item.html', {
                'reply': reply,
                'thread': thread
            })
            
        except MessageThread.DoesNotExist:
            return HttpResponse("Thread not found", status=404)
    
    return HttpResponse("Invalid request", status=400)

@login_required
def message_thread_view_htmx(request, thread_id):
    try:
        # Get thread and verify user has access
        thread = MessageThread.objects.get(
            id=thread_id,
            original_message__conversation__members=request.user
        )
        
        # Get replies
        replies = ChatMessage.objects.filter(
            parent_thread=thread
        ).order_by('created_at')
        
        context = {
            'thread': thread,
            'original_message': thread.original_message,
            'replies': replies
        }
        
        return render(request, 'messaging/partials/message_thread_view.html', context)
        
    except MessageThread.DoesNotExist:
        return HttpResponse("Thread not found", status=404)

@login_required
def message_thread_toggle_htmx(request):
    thread_id = request.GET.get('thread_id')
    
    if not thread_id:
        return HttpResponse("Thread ID is required", status=400)
    
    try:
        # Get thread and verify user has access
        thread = MessageThread.objects.get(
            id=thread_id,
            original_message__conversation__members=request.user
        )
        
        # Get replies
        replies = ChatMessage.objects.filter(
            parent_thread=thread
        ).order_by('created_at')
        
        context = {
            'thread': thread,
            'original_message': thread.original_message,
            'replies': replies
        }
        
        return render(request, 'messaging/partials/message_thread_collapsed.html', context)
        
    except MessageThread.DoesNotExist:
        return HttpResponse("Thread not found", status=404)

@login_required
def user_mention_search_htmx(request):
    query = request.GET.get('query', '')
    conversation_id = request.GET.get('conversation_id')
    
    if not conversation_id:
        return HttpResponse("Conversation ID is required", status=400)
    
    try:
        # Get conversation and verify user has access
        conversation = Conversation.objects.get(
            id=conversation_id,
            members=request.user
        )
        
        # Search for members
        if query:
            members = conversation.members.filter(
                Q(username__icontains=query) |
                Q(first_name__icontains=query) |
                Q(last_name__icontains=query)
            ).exclude(id=request.user.id)[:10]
        else:
            members = conversation.members.exclude(id=request.user.id)[:10]
        
        return render(request, 'messaging/partials/mention_suggestions.html', {
            'members': members
        })
        
    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)

@login_required
def message_mentions_list_htmx(request):
    # Get mentions for the current user
    mentions = MessageMention.objects.filter(
        user=request.user
    ).order_by('-message__created_at')[:20]
    
    return render(request, 'messaging/partials/mentions_list.html', {
        'mentions': mentions
    })

# Helper functions
def get_user_conversations(user, category='all', search_query=''):
    """Get conversations for a user with optional filtering."""
    # Base queryset
    queryset = Conversation.objects.filter(members=user)
    
    # Apply category filter
    if category == 'direct':
        queryset = queryset.filter(is_group=False)
    elif category == 'group':
        queryset = queryset.filter(is_group=True)
    elif category == 'project':
        queryset = queryset.filter(project__isnull=False)
    
    # Apply search filter
    if search_query:
        queryset = queryset.filter(
            Q(name__icontains=search_query) |
            Q(members__first_name__icontains=search_query) |
            Q(members__last_name__icontains=search_query) |
            Q(members__username__icontains=search_query) |
            Q(chatmessage__content__icontains=search_query)
        ).distinct()
    
    # Order by last activity
    queryset = queryset.order_by('-last_activity')
    
    # Annotate with last message
    conversations = []
    
    for conversation in queryset:
        # Get last message
        last_message = ChatMessage.objects.filter(
            conversation=conversation
        ).order_by('-created_at').first()
        
        # Get unread count
        unread_count = ChatMessage.objects.filter(
            conversation=conversation,
            message_reads__isnull=True
        ).exclude(sender=user).count()
        
        # Get other members (for direct messages)
        other_members = conversation.members.exclude(id=user.id)
        
        conversations.append({
            'conversation': conversation,
            'last_message': last_message,
            'unread_count': unread_count,
            'other_members': other_members
        })
    
    return conversations

def get_last_message(user, channel, project_id=None):
    """Get the last message for a user in a specific channel."""
    if channel == 'project' and project_id:
        # Get project conversation
        conversation = Conversation.objects.filter(
            project_id=project_id,
            is_project_conversation=True,
            members=user
        ).first()
        
        if conversation:
            return ChatMessage.objects.filter(
                conversation=conversation
            ).order_by('-created_at').first()
    
    return None

def get_unread_count(user, channel, project_id=None):
    """Get unread message count for a user in a specific channel."""
    if channel == 'project' and project_id:
        # Get project conversation
        conversation = Conversation.objects.filter(
            project_id=project_id,
            is_project_conversation=True,
            members=user
        ).first()
        
        if conversation:
            return ChatMessage.objects.filter(
                conversation=conversation,
                
                message_reads__isnull=True
            ).count()
    
    return 0

def get_unread_message_counts(user):
    """Get unread message counts for a user across different categories."""
    return {
        'all': ChatMessage.objects.filter(
            conversation__members=user,
            
            message_reads__isnull=True
        ).count(),
        'direct': ChatMessage.objects.filter(
            conversation__members=user,
            conversation__is_group=False,
            
            message_reads__isnull=True
        ).count(),
        'group': ChatMessage.objects.filter(
            conversation__members=user,
            conversation__is_group=True,
            
            message_reads__isnull=True
        ).count(),
        'project': ChatMessage.objects.filter(
            conversation__members=user,
            conversation__project__isnull=False,
            
            message_reads__isnull=True
        ).count()
    }

@login_required
@require_http_methods(["GET"])
def conversation_members_htmx(request, conversation_id):
    """HTMX endpoint for conversation members"""
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
        
        members = conversation.members.select_related('user').order_by('joined_at')
        
        context = {
            'conversation': conversation,
            'members': members,
            'user': request.user,
        }
        
        return render(request, 'components/messages/conversation_members.html', context)
        
    except Conversation.DoesNotExist:
        return HttpResponse('Conversation not found', status=404)


@login_required
@require_http_methods(["POST"])
def conversation_leave_htmx(request, conversation_id):
    """HTMX endpoint for leaving a conversation"""
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
        
        # Remove user from conversation
        member = conversation.members.filter(user=request.user).first()
        if member:
            member.delete()
        
        response = HttpResponse('')
        response['HX-Trigger'] = 'conversationLeft'
        return response
        
    except Conversation.DoesNotExist:
        return HttpResponse('Conversation not found', status=404)


@login_required
@require_http_methods(["DELETE", "POST"])
def message_delete_htmx(request, message_id):
    """HTMX endpoint for deleting messages"""
    try:
        message = ChatMessage.objects.get(
            id=message_id,
            user=request.user  # Only allow users to delete their own messages
        )
        
        message.delete()
        
        return HttpResponse('')  # Empty response removes the message from DOM
        
    except ChatMessage.DoesNotExist:
        return HttpResponse('Message not found', status=404)


@login_required
def collaboration_settings_htmx(request):
    """Update user collaboration settings"""
    if request.method == 'POST':
        try:
            settings, created = CollaborationSettings.objects.get_or_create(
                user=request.user
            )
            
            # Update settings from form data
            settings.auto_collapse_threads = request.POST.get('auto_collapse_threads') == 'on'
            settings.show_thread_previews = request.POST.get('show_thread_previews') == 'on'
            settings.mention_notifications = request.POST.get('mention_notifications') == 'on'
            settings.mention_sound = request.POST.get('mention_sound') == 'on'
            settings.mention_email = request.POST.get('mention_email') == 'on'
            settings.show_reaction_tooltips = request.POST.get('show_reaction_tooltips') == 'on'
            settings.reaction_notifications = request.POST.get('reaction_notifications') == 'on'
            settings.custom_emoji_set = request.POST.get('custom_emoji_set', 'default')
            
            settings.save()
            
            return render(request, 'components/messages/settings_saved.html', {
                'settings': settings,
                'message': 'Collaboration settings saved successfully!'
            })
            
        except Exception as e:
            logger.error(f"Error saving collaboration settings: {e}")
            return JsonResponse({'error': 'Failed to save settings'}, status=500)
    
    # GET request - show settings form
    try:
        settings = request.user.collaboration_settings
    except Exception:
        settings = CollaborationSettings.objects.create(user=request.user)
    
    return render(request, 'components/messages/collaboration_settings.html', {
        'settings': settings,
        'current_user': request.user,
    })


def user_has_project_access(user, project):
    """Check if a user has access to a project."""
    return (
        user.is_staff or
        project.created_by == user or
        project.members.filter(id=user.id).exists()
    )


def create_message_notification(request):
    """Create notification for new message (called by messaging system)"""
    try:
        
        message_id = request.POST.get('message_id')
        conversation_id = request.POST.get('conversation_id')
        
        if message_id:
            try:
                message = ChatMessage.objects.get(id=message_id)
                
                if conversation_id:
                    try:
                        conversation = Conversation.objects.get(id=conversation_id)
                        message_notification_service.notify_new_message(message, conversation)
                    except Conversation.DoesNotExist:
                        message_notification_service.notify_new_message(message)
                else:
                    message_notification_service.notify_new_message(message)
                
                return JsonResponse({'success': True})
                
            except ChatMessage.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Message not found'})
        
        return JsonResponse({'success': False, 'error': 'Message ID required'})
        
    except Exception as e:
        logger.error(f"Error creating message notification: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


# ========== REQUESTED MESSAGING VIEWS ==========
# The following 7 views were specifically requested for extraction:

@login_required
def messaging_chat_interface(request):
    """Main messaging chat interface"""
    # Get user's conversations with better organization
    conversations = Conversation.objects.filter(
        participants=request.user,
        is_active=True
    ).select_related('project', 'created_by').prefetch_related('participants').order_by('-last_message_at')[:50]
    
    # Get active conversation from URL parameter
    active_conversation_id = request.GET.get('conversation')
    active_conversation = None
    messages = []
    
    if active_conversation_id:
        try:
            active_conversation = conversations.get(id=active_conversation_id)
            # Mark conversation as read
            member = active_conversation.members.filter(user=request.user).first()
            if member:
                member.mark_read()
            
            # Get messages
            messages = active_conversation.messages.select_related('user').order_by('created_at')
        except Conversation.DoesNotExist:
            pass
    elif conversations.exists():
        active_conversation = conversations.first()
        messages = active_conversation.messages.select_related('user').order_by('created_at')
    
    # Get unread whisper count
    unread_whispers = WhisperMessage.objects.filter(
        recipient=request.user,
        is_read=False
    ).count()
    
    context = {
        'conversations': conversations,
        'active_conversation': active_conversation,
        'messages': messages,
        'unread_whispers': unread_whispers,
        'user': request.user,
    }
    
    return render(request, 'messaging/chat_interface.html', context)


@login_required
@require_http_methods(["POST"])
def messaging_whisper_create_htmx(request):
    """Create a new whisper message (HTMX endpoint)"""
    recipient_id = request.POST.get('recipient_id')
    message_text = request.POST.get('message', '').strip()
    
    if not recipient_id or not message_text:
        return HttpResponse('Recipient and message content are required', status=400)
    
    try:
        recipient = User.objects.get(id=recipient_id)
        
        # Create the whisper message
        whisper = WhisperMessage.objects.create(
            sender=request.user,
            recipient=recipient,
            message=message_text
        )
        
        # Return success response
        return render(request, 'components/whisper_created.html', {
            'whisper': whisper,
            'success_message': f'Whisper sent to {recipient.get_full_name()}'
        })
        
    except User.DoesNotExist:
        return HttpResponse('Recipient not found', status=404)
    except Exception as e:
        return HttpResponse(f'Error creating whisper: {str(e)}', status=400)


@login_required
@require_http_methods(["DELETE", "POST"])
def messaging_whisper_delete_htmx(request, whisper_id):
    """Delete a whisper message (HTMX endpoint)"""
    try:
        whisper = WhisperMessage.objects.filter(
            Q(sender=request.user) | Q(recipient=request.user)
        ).get(id=whisper_id)
        
        whisper.delete()
        
        return HttpResponse('')  # Empty response removes the element from DOM
        
    except WhisperMessage.DoesNotExist:
        return HttpResponse('Whisper not found', status=404)
    except Exception as e:
        return HttpResponse(f'Error deleting whisper: {str(e)}', status=400)


@login_required
@require_http_methods(["GET"])
def messaging_whisper_list_htmx(request):
    """Get list of whisper messages (HTMX endpoint)"""
    # Get filter parameters
    filter_type = request.GET.get('filter', 'received')  # 'received', 'sent', 'all'
    page = request.GET.get('page', 1)
    
    # Build queryset based on filter
    if filter_type == 'sent':
        whispers = WhisperMessage.objects.filter(
            sender=request.user
        ).select_related('recipient')
    elif filter_type == 'all':
        whispers = WhisperMessage.objects.filter(
            Q(sender=request.user) | Q(recipient=request.user)
        ).select_related('sender', 'recipient')
    else:  # default to 'received'
        whispers = WhisperMessage.objects.filter(
            recipient=request.user
        ).select_related('sender')
    
    whispers = whispers.order_by('-created_at')
    
    # Paginate
    paginator = Paginator(whispers, 20)
    page_obj = paginator.get_page(page)
    
    context = {
        'whispers': page_obj,
        'filter_type': filter_type,
        'user': request.user,
    }
    
    return render(request, 'components/whisper_list.html', context)


@login_required
@require_http_methods(["POST"])
def messaging_whisper_read_htmx(request, whisper_id):
    """Mark a whisper message as read (HTMX endpoint)"""
    try:
        whisper = WhisperMessage.objects.get(
            id=whisper_id,
            recipient=request.user
        )
        
        if not whisper.is_read:
            whisper.mark_read()
        
        return render(request, 'components/whisper_item.html', {
            'whisper': whisper,
            'show_read_status': True
        })
        
    except WhisperMessage.DoesNotExist:
        return HttpResponse('Whisper not found', status=404)
    except Exception as e:
        return HttpResponse(f'Error marking whisper as read: {str(e)}', status=400)


class ConversationListView(LoginRequiredMixin, ListView):
    """List view for user's conversations"""
    model = Conversation
    template_name = 'messaging/conversation_list.html'
    context_object_name = 'conversations'
    paginate_by = 20
    
    def get_queryset(self):
        """Get conversations for the current user"""
        queryset = Conversation.objects.filter(
            participants=self.request.user,
            is_active=True
        ).select_related(
            'project', 'created_by'
        ).prefetch_related(
            'participants'
        ).order_by('-last_message_at')
        
        # Apply search filter if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(participants__first_name__icontains=search) |
                Q(participants__last_name__icontains=search) |
                Q(participants__username__icontains=search)
            ).distinct()
        
        # Apply type filter if provided
        conv_type = self.request.GET.get('type')
        if conv_type and conv_type in dict(Conversation.CONVERSATION_TYPES):
            queryset = queryset.filter(conversation_type=conv_type)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add unread counts for each conversation
        for conversation in context['conversations']:
            conversation.unread_count = conversation.get_unread_count_for_user(self.request.user)
        
        # Add filter options
        context.update({
            'conversation_types': Conversation.CONVERSATION_TYPES,
            'current_search': self.request.GET.get('search', ''),
            'current_type': self.request.GET.get('type', ''),
            'total_unread': sum(
                conv.unread_count for conv in context['conversations']
            )
        })
        
        return context


class ConversationDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a specific conversation"""
    model = Conversation
    template_name = 'messaging/conversation_detail.html'
    context_object_name = 'conversation'
    
    def get_queryset(self):
        """Ensure user has access to the conversation"""
        return Conversation.objects.filter(
            participants=self.request.user
        ).select_related(
            'project', 'created_by'
        ).prefetch_related(
            'participants', 'members'
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        conversation = context['conversation']
        
        # Mark conversation as read
        member = conversation.members.filter(user=self.request.user).first()
        if member:
            member.mark_read()
        
        # Get messages with pagination
        page = self.request.GET.get('page', 1)
        messages = conversation.messages.select_related(
            'user'
        ).prefetch_related(
            'attachments', 'reactions', 'mentions'
        ).order_by('created_at')
        
        paginator = Paginator(messages, 50)
        page_obj = paginator.get_page(page)
        
        # Get conversation members
        members = conversation.members.select_related('user').order_by('joined_at')
        
        # Check if user can manage conversation
        user_member = conversation.members.filter(user=self.request.user).first()
        can_manage = (
            user_member and user_member.is_admin
        ) or conversation.created_by == self.request.user
        
        context.update({
            'messages': page_obj,
            'members': members,
            'can_manage': can_manage,
            'user_member': user_member,
            'other_participants': conversation.participants.exclude(id=self.request.user.id),
        })
        
        return context


def send_missed_message_notifications(request):
    """Send missed message notifications (typically called by cron job)"""
    try:
        
        hours_ago = int(request.POST.get('hours_ago', 1))
        
        # Send notifications for users who haven't been active
        for user in User.objects.filter(is_active=True):
            # Check if user has been inactive
            if hasattr(user, 'last_activity') and user.last_activity:
                time_since_activity = timezone.now() - user.last_activity
                if time_since_activity.total_seconds() > hours_ago * 3600:
                    message_notification_service.notify_missed_messages(user, hours_ago)
        
        return JsonResponse({'success': True})
        
    except Exception as e:
        logger.error(f"Error sending missed message notifications: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


# ========== PROJECT-SPECIFIC MESSAGING VIEWS ==========

class ProjectChatView(LoginRequiredMixin, TemplateView):
    """Project-specific chat interface"""
    template_name = 'messaging/project_chat.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs['project_id']
        
        try:
            project = Project.objects.get(id=project_id)
            
            # Check user access
            if not user_has_project_access(self.request.user, project):
                raise PermissionDenied("You don't have access to this project chat.")
            
            # Get or create project conversation
            conversation, created = Conversation.objects.get_or_create(
                project=project,
                conversation_type='project',
                defaults={
                    'name': f"{project.name} Team Chat",
                    'created_by': self.request.user,
                    'is_active': True
                }
            )
            
            # Add project team members to conversation if just created
            if created:
                if hasattr(project, 'team_members'):
                    conversation.participants.add(*project.team_members.all())
                conversation.participants.add(self.request.user)
                
                # Create conversation members
                for user in conversation.participants.all():
                    ConversationMember.objects.get_or_create(
                        conversation=conversation,
                        user=user,
                        defaults={'joined_at': timezone.now()}
                    )
            
            # Get messages
            messages = conversation.messages.select_related('user').order_by('created_at')
            
            # Get conversation members
            members = conversation.members.select_related('user').order_by('joined_at')
            
            context.update({
                'project': project,
                'conversation': conversation,
                'messages': messages,
                'members': members,
                'user': self.request.user,
            })
            
        except Project.DoesNotExist:
            raise Http404("Project not found")
        
        return context


class ProjectCommentsView(LoginRequiredMixin, TemplateView):
    """Project comments and discussion view"""
    template_name = 'messaging/project_comments.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs['project_id']
        
        try:
            project = Project.objects.get(id=project_id)
            
            # Check user access
            if not user_has_project_access(self.request.user, project):
                raise PermissionDenied("You don't have access to this project.")
            
            # Get project comments
            comments = Comment.objects.filter(
                project=project
            ).select_related('user').order_by('-created_at')
            
            # Paginate comments
            page = self.request.GET.get('page', 1)
            paginator = Paginator(comments, 20)
            page_obj = paginator.get_page(page)
            
            context.update({
                'project': project,
                'comments': page_obj,
                'user': self.request.user,
            })
            
        except Project.DoesNotExist:
            raise Http404("Project not found")
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle new comment creation"""
        project_id = self.kwargs['project_id']
        
        try:
            project = Project.objects.get(id=project_id)
            
            # Check user access
            if not user_has_project_access(request.user, project):
                raise PermissionDenied("You don't have access to this project.")
            
            content = request.POST.get('content', '').strip()
            if content:
                Comment.objects.create(
                    user=request.user,
                    project=project,
                    content=content,
                    created_at=timezone.now()
                )
                
                # Create activity record
                Activity.objects.create(
                    user=request.user,
                    action='commented',
                    target_model='Project',
                    target_id=project.id,
                    target_name=project.name,
                    project=project,
                    details=f"Added comment: {content[:50]}..."
                )
            
            return redirect('CLEAR:project_comments', project_id=project_id)
            
        except Project.DoesNotExist:
            raise Http404("Project not found")


class MeetingsView(LoginRequiredMixin, TemplateView):
    """Meeting management interface"""
    template_name = 'messaging/meetings.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user's meetings (simplified implementation)
        # In a real implementation, you'd have a Meeting model
        meetings = [
            {
                'id': 1,
                'title': 'Project Kickoff',
                'date': timezone.now() + timezone.timedelta(days=1),
                'participants': 5,
                'project': 'Infrastructure Project A',
            },
            {
                'id': 2,
                'title': 'Weekly Standup',
                'date': timezone.now() + timezone.timedelta(days=7),
                'participants': 8,
                'project': 'Utility Coordination B',
            }
        ]
        
        context.update({
            'meetings': meetings,
            'upcoming_count': len(meetings),
        })
        
        return context


# ========== ADDITIONAL WHISPER MESSAGING VIEWS ==========

@login_required
@require_http_methods(["GET"])
def whisper_search_htmx(request):
    """Search for users to whisper to (HTMX endpoint)"""
    query = request.GET.get('q', '').strip()
    
    if not query or len(query) < 2:
        return render(request, 'components/whispers/user_search_results.html', {
            'users': [],
            'query': query
        })
    
    # Search for users excluding current user
    users = User.objects.filter(
        Q(username__icontains=query) |
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(email__icontains=query)
    ).exclude(id=request.user.id).distinct()[:10]
    
    context = {
        'users': users,
        'query': query
    }
    
    return render(request, 'components/whispers/user_search_results.html', context)


@login_required
@require_http_methods(["POST"])
def whisper_send_htmx(request):
    """Send a whisper message to a user (HTMX endpoint)"""
    recipient_id = request.POST.get('recipient_id')
    message_text = request.POST.get('message', '').strip()
    
    if not recipient_id or not message_text:
        return HttpResponse('Recipient and message are required', status=400)
    
    try:
        recipient = User.objects.get(id=recipient_id)
        
        # Create the whisper
        whisper = WhisperMessage.objects.create(
            sender=request.user,
            recipient=recipient,
            message=message_text
        )
        
        # Return the new whisper item
        return render(request, 'components/whispers/whisper_item.html', {
            'whisper': whisper,
            'user': request.user
        })
        
    except User.DoesNotExist:
        return HttpResponse('Recipient not found', status=404)
    except Exception as e:
        return HttpResponse(f'Error sending whisper: {str(e)}', status=400)


@login_required
@require_http_methods(["GET"])
def whisper_conversation_htmx(request, user_id):
    """Get whisper conversation with a specific user (HTMX endpoint)"""
    try:
        other_user = User.objects.get(id=user_id)
        
        # Get all whispers between the two users
        whispers = WhisperMessage.objects.filter(
            Q(sender=request.user, recipient=other_user) |
            Q(sender=other_user, recipient=request.user)
        ).order_by('created_at')
        
        # Mark received whispers as read
        WhisperMessage.objects.filter(
            sender=other_user,
            recipient=request.user,
            is_read=False
        ).update(is_read=True, read_at=timezone.now())
        
        context = {
            'whispers': whispers,
            'other_user': other_user,
            'user': request.user
        }
        
        return render(request, 'components/whispers/whisper_conversation.html', context)
        
    except User.DoesNotExist:
        return HttpResponse('User not found', status=404)


@login_required
@require_http_methods(["DELETE", "POST"])
def whisper_conversation_delete_htmx(request, user_id):
    """Delete entire whisper conversation with a user (HTMX endpoint)"""
    try:
        other_user = User.objects.get(id=user_id)
        
        # Delete all whispers between the two users
        WhisperMessage.objects.filter(
            Q(sender=request.user, recipient=other_user) |
            Q(sender=other_user, recipient=request.user)
        ).delete()
        
        # Return empty response to remove from UI
        return HttpResponse('')
        
    except User.DoesNotExist:
        return HttpResponse('User not found', status=404)
    except Exception as e:
        return HttpResponse(f'Error deleting conversation: {str(e)}', status=400)


@login_required
@require_http_methods(["GET"])
def user_list_for_whisper_htmx(request):
    """Get list of users available for whispering (HTMX endpoint)"""
    search_query = request.GET.get('search', '').strip()
    
    # Get users the current user has interacted with or can whisper to
    users_queryset = User.objects.exclude(id=request.user.id).filter(is_active=True)
    
    # Apply search filter if provided
    if search_query:
        users_queryset = users_queryset.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    # Get users with recent whisper activity
    recent_whisper_users = User.objects.filter(
        Q(sent_whispers__recipient=request.user) |
        Q(received_whispers__sender=request.user)
    ).distinct().exclude(id=request.user.id)[:5]
    
    # Get all active users for the full list
    all_users = users_queryset.order_by('first_name', 'last_name')[:20]
    
    context = {
        'recent_users': recent_whisper_users,
        'all_users': all_users,
        'search_query': search_query,
        'user': request.user
    }
    
    return render(request, 'components/whispers/user_list.html', context)