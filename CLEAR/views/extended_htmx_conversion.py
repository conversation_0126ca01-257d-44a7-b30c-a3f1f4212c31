"""
Extended HTMX conversion views for additional ViewSets.

This module contains HTMX equivalents for the remaining ViewSets:
- Projects, Tasks, Comments, Reports, Time Entries, etc.

from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin, UserPassesTestMixin
from django.views.generic import (
    View, TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView,
    FormView, RedirectView
)
from django.views.generic.edit import FormMixin, ModelFormMixin, ProcessFormView, DeletionMixin
from django.views.generic.base import ContextMixin, TemplateResponseMixin
from django.contrib.auth.decorators import login_required, permission_required
from django.views.decorators.http import require_http_methods, require_GET, require_POST
from django.views.decorators.csrf import csrf_exempt, csrf_protect
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect, Http404
from django.urls import reverse, reverse_lazy
from django.contrib import messages
from django.db.models import Q, F, Count, Sum, Avg, Max, Min
from django.utils import timezone
from django.core.exceptions import ValidationError, PermissionDenied, ObjectDoesNotExist
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import transaction, connection
from django.conf import settings
import logging
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from datetime import timedelta
from django.http import HttpResponse
from django.forms import model_to_dict
from ..models import (
from ..mixins.htmx_responses import HTMXResponseMixin
        from datetime import datetime, time

    View, TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView,
    FormView, RedirectView
)

logger = logging.getLogger(__name__)



    Project, Task, Comment, Report, TimeEntry, FeatureRequest, 
    KnowledgeArticle, Note, Workflow, Stakeholder, Invoice, 
    GISLayer, Organization, CoordinateSystem
)


# ========== PROJECT MANAGEMENT ==========

@login_required
def project_list_htmx(request):
    """List projects with search and filtering."""
    queryset = Project.objects.all()
    
    # Filter by user access
    user_projects = Project.objects.filter(
        Q(egis_project_manager=request.user.username) |
        Q(members__user=request.user)
    ).distinct()
    queryset = user_projects
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(client__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Status filter
    status = request.GET.get('status')
    if status:
        queryset = queryset.filter(rag_status=status)
    
    # Ordering
    queryset = queryset.order_by('-created_at')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 25)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'projects': page_obj,
        'total_count': paginator.count,
        'search': search,
        'status': status
    }
    
    return render(request, 'components/projects/project_list.html', context)


@login_required
def project_detail_htmx(request, project_id):
    """Get project details as HTML fragment."""
    project = get_object_or_404(Project, id=project_id)
    
    # Check access permissions
    if not (request.user.is_admin or 
            project.egis_project_manager == request.user.username or
            project.members.filter(user=request.user).exists()):
        return HttpResponse("Unauthorized", status=403)
    
    # Get project statistics
    utilities_count = project.utilities.count()
    conflicts_count = project.conflicts.count()
    open_conflicts = project.conflicts.filter(status='open').count()
    tasks_count = project.tasks.count()
    pending_tasks = project.tasks.filter(completed=False).count()
    
    context = {
        'project': project,
        'utilities_count': utilities_count,
        'conflicts_count': conflicts_count,
        'open_conflicts': open_conflicts,
        'tasks_count': tasks_count,
        'pending_tasks': pending_tasks,
        'can_edit': request.user.is_admin or project.egis_project_manager == request.user.username
    }
    
    return render(request, 'components/projects/project_detail.html', context)


@login_required
@require_http_methods(["POST"])
def project_update_htmx(request, project_id):
    """Update project information."""
    project = get_object_or_404(Project, id=project_id)
    
    # Check permissions
    if not (request.user.is_admin or project.egis_project_manager == request.user.username):
        return HttpResponse("Unauthorized", status=403)
    
    # Update fields
    project.name = request.POST.get('name', project.name)
    project.client = request.POST.get('client', project.client)
    project.description = request.POST.get('description', project.description)
    project.rag_status = request.POST.get('rag_status', project.rag_status)
    project.budget = request.POST.get('budget') or project.budget
    
    project.save()
    
    messages.success(request, f\"Project '{project.name}' updated successfully.\")
    
    return project_detail_htmx(request, project_id)


# ========== TASK MANAGEMENT ==========

@login_required
def task_list_htmx(request, project_id=None):
    \"\"\"List tasks with filtering and pagination.\"\"\"
    queryset = Task.objects.select_related('project', 'assigned_to')
    
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        queryset = queryset.filter(project=project)
        context_project = project
    else:
        context_project = None
        # Filter by user's accessible projects
        user_projects = Project.objects.filter(
            Q(egis_project_manager=request.user.username) |
            Q(members__user=request.user)
        ).distinct()
        queryset = queryset.filter(project__in=user_projects)
    
    # My tasks filter
    if request.GET.get('my_tasks') == 'true':
        queryset = queryset.filter(assigned_to=request.user)
    
    # Status filter
    status = request.GET.get('status')
    if status == 'completed':
        queryset = queryset.filter(completed=True)
    elif status == 'pending':
        queryset = queryset.filter(completed=False)
    
    # Priority filter
    priority = request.GET.get('priority')
    if priority:
        queryset = queryset.filter(priority=priority)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(title__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Ordering
    queryset = queryset.order_by('due_date', '-created_at')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 25)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'tasks': page_obj,
        'project': context_project,
        'total_count': paginator.count,
        'search': search,
        'status': status,
        'priority': priority,
        'my_tasks': request.GET.get('my_tasks') == 'true'
    }
    
    return render(request, 'components/tasks/task_list.html', context)


@login_required
@require_http_methods(["POST"])
def task_toggle_complete_htmx(request, task_id):
    \"\"\"Toggle task completion status.\"\"\"
    task = get_object_or_404(Task, id=task_id)
    
    # Check permissions
    if not (request.user.is_admin or 
            task.assigned_to == request.user or
            task.project.egis_project_manager == request.user.username):
        return HttpResponse(\"Unauthorized\", status=403)
    
    task.completed = not task.completed
    if task.completed:
        task.completed_at = timezone.now()
    else:
        task.completed_at = None
    task.save()
    
    status_text = \"completed\" if task.completed else \"reopened\"
    messages.success(request, f\"Task '{task.title}' {status_text}.\")
    
    return render(request, 'components/tasks/task_item.html', {
        'task': task,
        'can_edit': True
    })


# ========== COMMENT MANAGEMENT ==========

@login_required
def comment_list_htmx(request):
    \"\"\"List comments for a specific object.\"\"\"
    commentable_type = request.GET.get('commentable_type')
    commentable_id = request.GET.get('commentable_id')
    
    if not commentable_type or not commentable_id:
        return HttpResponse(\"commentable_type and commentable_id are required\", status=400)
    
    queryset = Comment.objects.filter(
        commentable_type=commentable_type,
        commentable_id=commentable_id
    ).select_related('user').order_by('-created_at')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 10)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'comments': page_obj,
        'commentable_type': commentable_type,
        'commentable_id': commentable_id,
        'total_count': paginator.count
    }
    
    return render(request, 'components/comments/comment_list.html', context)


@login_required
@require_http_methods(["POST"])
def comment_create_htmx(request):
    \"\"\"Create a new comment.\"\"\"
    content = request.POST.get('content')
    commentable_type = request.POST.get('commentable_type')
    commentable_id = request.POST.get('commentable_id')
    
    if not all([content, commentable_type, commentable_id]):
        return HttpResponse(\"Missing required fields\", status=400)
    
    comment = Comment.objects.create(
        user=request.user,
        content=content,
        commentable_type=commentable_type,
        commentable_id=commentable_id
    )
    
    messages.success(request, \"Comment added successfully.\")
    
    return render(request, 'components/comments/comment_item.html', {
        'comment': comment,
        'can_edit': True
    })


# ========== TIME TRACKING ==========

@login_required
def time_entry_list_htmx(request):
    \"\"\"List time entries for the current user.\"\"\"
    queryset = TimeEntry.objects.filter(user=request.user).select_related('project')
    
    # Date range filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        queryset = queryset.filter(start_time__date__gte=date_from)
    if date_to:
        queryset = queryset.filter(start_time__date__lte=date_to)
    
    # Project filter
    project_id = request.GET.get('project_id')
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    
    # Ordering
    queryset = queryset.order_by('-start_time')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 20)
    page_obj = paginator.get_page(page_number)
    
    # Calculate totals
    total_hours = sum((entry.end_time - entry.start_time).total_seconds() / 3600 
                     for entry in queryset if entry.end_time) or 0
    
    context = {
        'time_entries': page_obj,
        'total_count': paginator.count,
        'total_hours': round(total_hours, 2),
        'date_from': date_from,
        'date_to': date_to,
        'project_id': project_id
    }
    
    return render(request, 'components/timesheet/time_entry_list.html', context)


@login_required
@require_http_methods(["POST"])
def time_entry_create_htmx(request):
    \"\"\"Create a new time entry.\"\"\"
    project_id = request.POST.get('project_id')
    description = request.POST.get('description')
    hours = request.POST.get('hours')
    date = request.POST.get('date')
    
    if not all([project_id, description, hours, date]):
        return HttpResponse(\"Missing required fields\", status=400)
    
    try:
        project = Project.objects.get(id=project_id)
        hours_float = float(hours)
        
        # Create start and end times based on date and hours
        start_datetime = datetime.combine(
            datetime.strptime(date, '%Y-%m-%d').date(),
            time(9, 0)  # Default to 9 AM
        )
        end_datetime = start_datetime + timedelta(hours=hours_float)
        
        time_entry = TimeEntry.objects.create(
            user=request.user,
            project=project,
            description=description,
            start_time=start_datetime,
            end_time=end_datetime
        )
        
        messages.success(request, f\"Time entry for {hours} hours added successfully.\")
        
        return render(request, 'components/timesheet/time_entry_item.html', {
            'time_entry': time_entry,
            'can_edit': True
        })
        
    except (Project.DoesNotExist, ValueError) as e:
        return HttpResponse(f\"Error: {str(e)}\", status=400)


# ========== REPORTS ==========

@login_required
def report_list_htmx(request):
    \"\"\"List available reports.\"\"\"
    queryset = Report.objects.filter(
        Q(created_by=request.user) |
        Q(is_public=True)
    ).order_by('-created_at')
    
    # Type filter
    report_type = request.GET.get('type')
    if report_type:
        queryset = queryset.filter(report_type=report_type)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 20)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'reports': page_obj,
        'total_count': paginator.count,
        'search': search,
        'report_type': report_type
    }
    
    return render(request, 'components/reports/report_list.html', context)


@login_required
@require_http_methods(["POST"])
def report_execute_htmx(request, report_id):
    \"\"\"Execute a report and return results.\"\"\"
    report = get_object_or_404(Report, id=report_id)
    
    # Check permissions
    if not (report.is_public or report.created_by == request.user or request.user.is_admin):
        return HttpResponse(\"Unauthorized\", status=403)
    
    # TODO: Implement actual report execution logic
    # This is a placeholder implementation
    
    messages.info(request, f\"Report '{report.name}' execution started. Results will be available shortly.\")
    
    return render(request, 'components/reports/report_execution.html', {
        'report': report,
        'status': 'running'
    })


# ========== STAKEHOLDER MANAGEMENT ==========

@login_required
def stakeholder_list_htmx(request, project_id=None):
    \"\"\"List stakeholders with filtering.\"\"\"
    queryset = Stakeholder.objects.select_related('organization')
    
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        queryset = queryset.filter(projects=project)
        context_project = project
    else:
        context_project = None
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(organization__name__icontains=search)
        )
    
    # Role filter
    role = request.GET.get('role')
    if role:
        queryset = queryset.filter(role=role)
    
    # Ordering
    queryset = queryset.order_by('name')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 25)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'stakeholders': page_obj,
        'project': context_project,
        'total_count': paginator.count,
        'search': search,
        'role': role
    }
    
    return render(request, 'components/stakeholders/stakeholder_list.html', context)


# ========== FEATURE REQUESTS ==========

@login_required
def feature_request_list_htmx(request):
    \"\"\"List feature requests with voting.\"\"\"
    queryset = FeatureRequest.objects.select_related('submitted_by').order_by('-created_at')
    
    # Status filter
    status = request.GET.get('status')
    if status:
        queryset = queryset.filter(status=status)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(title__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 15)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'feature_requests': page_obj,
        'total_count': paginator.count,
        'search': search,
        'status': status
    }
    
    return render(request, 'components/feature_requests/request_list.html', context)


@login_required
@require_http_methods(["POST"])
def feature_request_vote_htmx(request, feature_id):
    \"\"\"Vote for a feature request.\"\"\"
    feature = get_object_or_404(FeatureRequest, id=feature_id)
    
    # TODO: Implement voting logic with a many-to-many relationship
    # For now, just increment the vote count
    feature.votes += 1
    feature.save()
    
    messages.success(request, \"Thank you for your vote!\")
    
    return render(request, 'components/feature_requests/vote_count.html', {
        'feature_request': feature
    })