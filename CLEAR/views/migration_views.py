"""
Migration views that switch between JSON API and HTMX based on feature flags.

This module provides wrapper views that can serve either JSON API responses
or HTMX HTML fragments based on the feature flag configuration.

from django.contrib.auth.mixins import LoginRequiredMixin
from django.views import View
from ..feature_flags import FeatureFlags, use_htmx_endpoint
from ..mixins import HTMXResponseMixin
from . import api_views, htmx_views





class MigrationViewMixin:
    """
    Mixin to handle feature flag-based view switching.
    
    Provides methods to determine whether to use HTMX or JSON responses.
    
    def should_use_htmx(self, request):
        """
        Determine if HTMX should be used based on feature flags and request headers.
        
        Args:
            request: Django request object
            
        Returns:
            bool: True if HTMX should be used
        # Check feature flag first
        if not FeatureFlags.is_enabled('USE_HTMX_ENDPOINTS'):
            return False
        
        # Also check if this is an HTMX request
        return request.headers.get('HX-Request') == 'true'


# Example migration view for projects
class ProjectListMigrationView(LoginRequiredMixin, MigrationViewMixin, HTMXResponseMixin, View):
    """
    Migration view that serves either JSON API or HTMX responses for project list.
    
    def get(self, request):
        if self.should_use_htmx(request):
            # Use HTMX view
            return htmx_views.project_list_htmx(request)
        else:
            # Use JSON API view
            view = api_views.ProjectViewSet.as_view({'get': 'list'})
            return view(request)
    
    def post(self, request):
        if self.should_use_htmx(request):
            # Use HTMX view
            return htmx_views.project_create_htmx(request)
        else:
            # Use JSON API view
            view = api_views.ProjectViewSet.as_view({'post': 'create'})
            return view(request)


# Wrapper functions for common endpoints
@use_htmx_endpoint(htmx_views.dashboard_stats_htmx, api_views.DashboardStatsAPIView.as_view())
def dashboard_stats_migration(request):
    """Migration wrapper for dashboard stats endpoint."""
    pass


@use_htmx_endpoint(htmx_views.recent_activity_htmx, api_views.RecentActivityAPIView.as_view())
def recent_activity_migration(request):
    """Migration wrapper for recent activity endpoint."""
    pass


# Template tag helper for feature flags
def get_endpoint_url(endpoint_name, htmx_url, api_url):
    """
    Get the appropriate endpoint URL based on feature flags.
    
    Args:
        endpoint_name: Name of the endpoint
        htmx_url: HTMX endpoint URL
        api_url: API endpoint URL
        
    Returns:
        str: The appropriate URL based on feature flags
    if FeatureFlags.is_enabled('USE_HTMX_ENDPOINTS'):
        return htmx_url
    return api_url
"""