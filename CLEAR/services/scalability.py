"""
Scalability features for messaging system including rate limiting,
bulk operations, load balancing support, and horizontal scaling capabilities.
"""

import json
import logging
import threading
import time
from typing import Any, Dict, List, Optional
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q
from django.http import HttpResponse
from django.utils import timezone
from ..models import ChatMessage, Conversation, MessageRead, User
        import os
        import socket
            import psutil

"""



logger = logging.getLogger(__name__)


class RateLimiter:
    """Advanced rate limiting with multiple strategies and user tiers"""

    def __init__(self):
        self.cache_prefix = "rate_limit"
        self.default_limits = {
            'messages_per_minute': 30,
            'messages_per_hour': 500,
            'messages_per_day': 2000,
            'api_calls_per_minute': 100,
            'websocket_connections_per_user': 5,
            'bulk_operations_per_hour': 10
        }

        # User tier limits (premium users get higher limits)
        self.tier_multipliers = {
            'basic': 1.0,
            'premium': 3.0,
            'admin': 10.0,
            'system': float('inf')
        }

    def get_user_tier(self, user) -> str:
        """Determine user tier for rate limiting"""
        if user.is_superuser:
            return 'system'
        elif user.is_staff:
            return 'admin'
        elif hasattr(user, 'subscription_tier'):
            return user.subscription_tier
        else:
            return 'basic'

    def get_rate_limit_key(self, user_id: int, action: str, window: str) -> str:
        """Generate cache key for rate limit tracking"""
        return f"{self.cache_prefix}:{user_id}:{action}:{window}"

    def is_rate_limited(self, user, action: str, increment: bool = True) -> Dict[str, Any]:
        """
        Check if user is rate limited for specific action.

        Args:
            user: Django user object
            action: Action being performed (e.g., 'send_message', 'api_call')
            increment: Whether to increment the counter

        Returns:
            Dict with rate limit status and metadata
        """
        user_tier = self.get_user_tier(user)
        tier_multiplier = self.tier_multipliers.get(user_tier, 1.0)

        # Check different time windows
        windows = {
            'minute': {'seconds': 60, 'limit_key': f'{action}_per_minute'},
            'hour': {'seconds': 3600, 'limit_key': f'{action}_per_hour'},
            'day': {'seconds': 86400, 'limit_key': f'{action}_per_day'}
        }

        current_time = int(time.time())
        rate_limit_info = {
            'is_limited': False,
            'limits': {},
            'current_usage': {},
            'reset_times': {},
            'user_tier': user_tier
        }

        for window_name, window_config in windows.items():
            limit_key = window_config['limit_key']
            base_limit = self.default_limits.get(limit_key, 0)

            if base_limit == 0:
                continue  # No limit defined for this action/window

            # Apply tier multiplier
            user_limit = int(base_limit * tier_multiplier)

            # Calculate window start time
            window_seconds = window_config['seconds']
            window_start = current_time - (current_time % window_seconds)

            # Get current usage
            cache_key = self.get_rate_limit_key(user.id, action, f"{window_name}_{window_start}")
            current_usage = cache.get(cache_key, 0)

            # Check if limit exceeded
            if current_usage >= user_limit:
                rate_limit_info['is_limited'] = True
                rate_limit_info['violated_window'] = window_name
                rate_limit_info['violated_limit'] = user_limit
                break

            # Increment counter if requested
            if increment:
                new_usage = cache.get(cache_key, 0) + 1
                cache.set(cache_key, new_usage, window_seconds)
                current_usage = new_usage

            # Store info for this window
            rate_limit_info['limits'][window_name] = user_limit
            rate_limit_info['current_usage'][window_name] = current_usage
            rate_limit_info['reset_times'][window_name] = window_start + window_seconds

        return rate_limit_info

    def get_rate_limit_headers(self, user, action: str) -> Dict[str, str]:
        """Get rate limit headers for HTTP responses"""
        rate_info = self.is_rate_limited(user, action, increment=False)

        headers = {}
        if 'minute' in rate_info['limits']:
            headers['X-RateLimit-Limit'] = str(rate_info['limits']['minute'])
            headers['X-RateLimit-Remaining'] = str(max(0, rate_info['limits']['minute'] - rate_info['current_usage']['minute']))
            headers['X-RateLimit-Reset'] = str(rate_info['reset_times']['minute'])

        if rate_info['is_limited']:
            headers['X-RateLimit-Limited'] = 'true'
            headers['Retry-After'] = str(rate_info['reset_times'].get(rate_info.get('violated_window', 'minute'), 60))

        return headers

    def create_rate_limit_response(self, rate_info: Dict[str, Any]) -> HttpResponse:
        """Create HTTP 429 response for rate limited requests"""
        retry_after = rate_info['reset_times'].get(rate_info.get('violated_window', 'minute'), 60) - int(time.time())

        response = HttpResponse(
            json.dumps({
                'error': 'Rate limit exceeded',
                'message': f'Rate limit of {rate_info["violated_limit"]} exceeded for {rate_info["violated_window"]} window',
                'retry_after': retry_after,
                'user_tier': rate_info['user_tier']
            }),
            status=429,
            content_type='application/json'
        )

        response['Retry-After'] = str(retry_after)
        response['X-RateLimit-Limited'] = 'true'

        return response


class BulkOperationsManager:
    """Manage bulk operations for improved performance"""

    def __init__(self):
        self.batch_size = 100
        self.max_bulk_size = 1000

    def bulk_create_messages(
        self, 
        messages_data: List[Dict[str, Any]], 
        user: User,
        conversation: Optional[Conversation] = None
    ) -> Dict[str, Any]:
        """
        Bulk create messages with validation and optimization.

        Args:
            messages_data: List of message dictionaries
            user: User creating the messages
            conversation: Optional conversation for all messages

        Returns:
            Dict with creation results and statistics
        """
        if len(messages_data) > self.max_bulk_size:
            raise ValidationError(f"Bulk operation too large. Maximum {self.max_bulk_size} messages allowed.")

        created_messages = []
        errors = []

        try:
            with transaction.atomic():
                # Prepare message objects
                message_objects = []

                for i, msg_data in enumerate(messages_data):
                    try:
                        # Validate message data
                        self._validate_message_data(msg_data, user, conversation)

                        # Create message object
                        message = ChatMessage(
                            user=user,
                            conversation=conversation or msg_data.get('conversation'),
                            content=msg_data['content'],
                            project_id=msg_data.get('project_id'),
                            channel=msg_data.get('channel', 'general'),
                            message_type=msg_data.get('message_type', 'text'),
                            is_urgent=msg_data.get('is_urgent', False),
                            reply_to_id=msg_data.get('reply_to_id')
                        )

                        message_objects.append(message)

                    except Exception as e:
                        errors.append({
                            'index': i,
                            'error': str(e),
                            'data': msg_data
                        })

                # Bulk create messages
                if message_objects:
                    created_messages = ChatMessage.objects.bulk_create(
                        message_objects,
                        batch_size=self.batch_size
                    )

                # Update conversation timestamps
                if conversation and created_messages:
                    conversation.last_message_at = timezone.now()
                    conversation.save(update_fields=['last_message_at'])

        except Exception as e:
            logger.error(f"Error in bulk message creation: {e}")
            errors.append({
                'index': -1,
                'error': f"Transaction error: {str(e)}",
                'data': None
            })

        return {
            'created_count': len(created_messages),
            'error_count': len(errors),
            'total_submitted': len(messages_data),
            'created_message_ids': [msg.id for msg in created_messages],
            'errors': errors
        }

    def _validate_message_data(self, msg_data: Dict[str, Any], user: User, conversation: Optional[Conversation]):
        """Validate individual message data"""
        required_fields = ['content']
        for field in required_fields:
            if field not in msg_data or not msg_data[field]:
                raise ValidationError(f"Missing required field: {field}")

        # Content length validation
        if len(msg_data['content']) > 5000:  # Adjust as needed
            raise ValidationError("Message content too long")

        # Conversation access validation
        if conversation and not conversation.participants.filter(id=user.id).exists():
            raise ValidationError("User not authorized for this conversation")

    def bulk_mark_messages_read(
        self, 
        message_ids: List[int], 
        user: User
    ) -> Dict[str, Any]:
        """
        Bulk mark messages as read for a user.

        Args:
            message_ids: List of message IDs to mark as read
            user: User marking messages as read

        Returns:
            Dict with operation results
        """
        if len(message_ids) > self.max_bulk_size:
            raise ValidationError(f"Bulk operation too large. Maximum {self.max_bulk_size} messages allowed.")

        created_count = 0
        existing_count = 0
        errors = []

        try:
            with transaction.atomic():
                # Get messages user has access to
                accessible_messages = ChatMessage.objects.filter(
                    id__in=message_ids
                ).filter(
                    Q(conversation__participants=user) |
                    Q(conversation__isnull=True)  # Legacy messages
                ).values_list('id', flat=True)

                # Create MessageRead records in batches
                read_records = []
                existing_reads = set(
                    MessageRead.objects.filter(
                        message_id__in=accessible_messages,
                        user=user
                    ).values_list('message_id', flat=True)
                )

                for message_id in accessible_messages:
                    if message_id not in existing_reads:
                        read_records.append(
                            MessageRead(
                                message_id=message_id,
                                user=user,
                                read_at=timezone.now()
                            )
                        )
                    else:
                        existing_count += 1

                # Bulk create read records
                if read_records:
                    MessageRead.objects.bulk_create(
                        read_records,
                        batch_size=self.batch_size,
                        ignore_conflicts=True
                    )
                    created_count = len(read_records)

        except Exception as e:
            logger.error(f"Error in bulk mark messages read: {e}")
            errors.append(str(e))

        return {
            'processed_count': created_count + existing_count,
            'new_reads_count': created_count,
            'already_read_count': existing_count,
            'total_requested': len(message_ids),
            'errors': errors
        }

    def bulk_update_conversation_members(
        self,
        conversation: Conversation,
        updates: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Bulk update conversation member settings.

        Args:
            conversation: Conversation to update
            updates: List of member updates (user_id, settings)

        Returns:
            Dict with update results
        """
        if len(updates) > self.max_bulk_size:
            raise ValidationError(f"Bulk operation too large. Maximum {self.max_bulk_size} updates allowed.")

        updated_count = 0
        errors = []

        try:
            with transaction.atomic():
                for update in updates:
                    try:
                        user_id = update['user_id']
                        settings_update = update.get('settings', {})

                        # Update conversation member
                        member, created = conversation.members.get_or_create(
                            user_id=user_id,
                            defaults={'joined_at': timezone.now()}
                        )

                        # Apply updates
                        if 'notifications_enabled' in settings_update:
                            member.notifications_enabled = settings_update['notifications_enabled']

                        if 'is_admin' in settings_update:
                            member.is_admin = settings_update['is_admin']

                        if 'last_read_at' in settings_update:
                            member.last_read_at = settings_update['last_read_at']

                        member.save()
                        updated_count += 1

                    except Exception as e:
                        errors.append({
                            'user_id': update.get('user_id'),
                            'error': str(e)
                        })

        except Exception as e:
            logger.error(f"Error in bulk conversation member update: {e}")
            errors.append({'error': str(e)})

        return {
            'updated_count': updated_count,
            'error_count': len(errors),
            'total_requested': len(updates),
            'errors': errors
        }


class LoadBalancingSupport:
    """Support for horizontal scaling and load balancing"""

    def __init__(self):
        self.server_id = self._generate_server_id()
        self.heartbeat_interval = 30  # seconds
        self.server_timeout = 120     # seconds

    def _generate_server_id(self) -> str:
        """Generate unique server identifier"""
        hostname = socket.gethostname()
        pid = os.getpid()
        timestamp = int(time.time())
        return f"{hostname}_{pid}_{timestamp}"

    def register_server(self) -> None:
        """Register this server instance in distributed cache"""
        try:
            server_info = {
                'server_id': self.server_id,
                'hostname': socket.gethostname(),
                'pid': os.getpid(),
                'last_heartbeat': time.time(),
                'active_connections': 0,
                'memory_usage': self._get_memory_usage()
            }

            cache.set(f"server_registry:{self.server_id}", server_info, self.server_timeout)

        except Exception as e:
            logger.error(f"Error registering server: {e}")

    def heartbeat(self, active_connections: int = 0) -> None:
        """Send heartbeat to indicate server is alive"""
        try:
            server_info = {
                'server_id': self.server_id,
                'last_heartbeat': time.time(),
                'active_connections': active_connections,
                'memory_usage': self._get_memory_usage()
            }

            cache.set(f"server_registry:{self.server_id}", server_info, self.server_timeout)

        except Exception as e:
            logger.error(f"Error sending heartbeat: {e}")

    def get_active_servers(self) -> List[Dict[str, Any]]:
        """Get list of active servers"""
        try:
            # This is a simplified implementation
            # In production, you'd use Redis SCAN to find all server keys
            current_time = time.time()
            active_servers = []

            # For demo purposes, just return this server
            server_info = cache.get(f"server_registry:{self.server_id}")
            if server_info and (current_time - server_info['last_heartbeat']) < self.server_timeout:
                active_servers.append(server_info)

            return active_servers

        except Exception as e:
            logger.error(f"Error getting active servers: {e}")
            return []

    def _get_memory_usage(self) -> float:
        """Get current memory usage percentage"""
        try:
            return psutil.virtual_memory().percent
        except Exception:
            return 0.0

    def distribute_websocket_connections(self, user_id: int) -> str:
        """Determine which server should handle a WebSocket connection"""
        try:
            active_servers = self.get_active_servers()

            if not active_servers:
                return self.server_id

            # Simple load balancing: choose server with least connections
            best_server = min(active_servers, key=lambda s: s.get('active_connections', 0))
            return best_server['server_id']

        except Exception as e:
            logger.error(f"Error distributing WebSocket connection: {e}")
            return self.server_id

    def notify_all_servers(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Send notification to all active servers"""
        try:
            notification = {
                'event_type': event_type,
                'event_data': event_data,
                'timestamp': time.time(),
                'source_server': self.server_id
            }

            # Publish to all servers via cache
            cache.set(f"server_notification:{int(time.time())}", notification, 60)

        except Exception as e:
            logger.error(f"Error notifying servers: {e}")


class MessageQueueManager:
    """Manage message queues for async processing"""

    def __init__(self):
        self.queue_prefix = "msg_queue"
        self.processing_timeout = 300  # 5 minutes

    def queue_message_for_processing(
        self, 
        message_id: int, 
        processing_type: str,
        priority: int = 1
    ) -> bool:
        """
        Queue a message for background processing.

        Args:
            message_id: ID of message to process
            processing_type: Type of processing (e.g., 'notification', 'indexing')
            priority: Priority level (1=high, 5=low)

        Returns:
            True if queued successfully
        """
        try:
            queue_item = {
                'message_id': message_id,
                'processing_type': processing_type,
                'priority': priority,
                'queued_at': time.time(),
                'attempts': 0,
                'max_attempts': 3
            }

            queue_key = f"{self.queue_prefix}:{processing_type}:priority_{priority}"

            # Add to queue (in production, use proper message queue like Celery/RQ)
            queue_data = cache.get(queue_key, [])
            queue_data.append(queue_item)
            cache.set(queue_key, queue_data, 3600)  # 1 hour TTL

            return True

        except Exception as e:
            logger.error(f"Error queuing message {message_id}: {e}")
            return False

    def process_message_queue(self, processing_type: str, max_items: int = 10) -> Dict[str, Any]:
        """
        Process items from message queue.

        Args:
            processing_type: Type of processing to handle
            max_items: Maximum items to process in this batch

        Returns:
            Dict with processing results
        """
        processed_count = 0
        error_count = 0

        try:
            # Process high priority first
            for priority in range(1, 6):
                queue_key = f"{self.queue_prefix}:{processing_type}:priority_{priority}"
                queue_data = cache.get(queue_key, [])

                items_to_process = queue_data[:max_items - processed_count]
                remaining_items = queue_data[max_items - processed_count:]

                for item in items_to_process:
                    try:
                        # Process the item based on type
                        if processing_type == 'notification':
                            self._process_notification_queue_item(item)
                        elif processing_type == 'indexing':
                            self._process_indexing_queue_item(item)

                        processed_count += 1

                    except Exception as e:
                        logger.error(f"Error processing queue item: {e}")
                        item['attempts'] += 1

                        if item['attempts'] < item['max_attempts']:
                            # Re-queue for retry
                            remaining_items.append(item)

                        error_count += 1

                # Update queue
                cache.set(queue_key, remaining_items, 3600)

                if processed_count >= max_items:
                    break

        except Exception as e:
            logger.error(f"Error processing message queue: {e}")
            error_count += 1

        return {
            'processed_count': processed_count,
            'error_count': error_count,
            'processing_type': processing_type
        }

    def _process_notification_queue_item(self, item: Dict[str, Any]) -> None:
        """Process notification queue item"""
        message_id = item['message_id']

        try:
            message = ChatMessage.objects.get(id=message_id)

            # Send notifications to conversation participants
            if message.conversation:
                participants = message.conversation.participants.exclude(id=message.user_id)

                for participant in participants:
                    # Create notification (simplified)
                    logger.info(f"Sending notification for message {message_id} to user {participant.id}")

        except ChatMessage.DoesNotExist:
            logger.warning(f"Message {message_id} not found for notification processing")

    def _process_indexing_queue_item(self, item: Dict[str, Any]) -> None:
        """Process search indexing queue item"""
        message_id = item['message_id']

        try:
            message = ChatMessage.objects.get(id=message_id)

            # Index message for search (simplified)
            # TODO: Implement actual search indexing logic here
            # For now, we'll use the message content for indexing
            logger.info(f"Indexing message {message_id} for search: {message.content[:50]}...")

        except ChatMessage.DoesNotExist:
            logger.warning(f"Message {message_id} not found for indexing")


# Global instances
rate_limiter = RateLimiter()
bulk_operations = BulkOperationsManager()
load_balancer = LoadBalancingSupport()
message_queue = MessageQueueManager()


# Decorator for rate limiting views
def rate_limit(action: str):
    """Decorator to apply rate limiting to views"""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return view_func(request, *args, **kwargs)

            rate_info = rate_limiter.is_rate_limited(request.user, action)

            if rate_info['is_limited']:
                return rate_limiter.create_rate_limit_response(rate_info)

            # Add rate limit headers to response
            response = view_func(request, *args, **kwargs)
            headers = rate_limiter.get_rate_limit_headers(request.user, action)

            for header, value in headers.items():
                response[header] = value

            return response

        return wrapper
    return decorator


# Utility functions
def start_load_balancing_services():
    """Start load balancing and scaling services"""
    try:
        load_balancer.register_server()

        def heartbeat_loop():
            while True:
                try:
                    # Get current connection count (would be from WebSocket manager)
                    active_connections = 0  # Placeholder
                    load_balancer.heartbeat(active_connections)
                    time.sleep(load_balancer.heartbeat_interval)
                except Exception as e:
                    logger.error(f"Error in heartbeat loop: {e}")
                    time.sleep(load_balancer.heartbeat_interval)

        heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
        heartbeat_thread.start()

        logger.info("Load balancing services started")

    except Exception as e:
        logger.error(f"Error starting load balancing services: {e}")


def process_background_queues():
    """Process background message queues"""
    try:
        # Process different queue types
        notification_results = message_queue.process_message_queue('notification', 50)
        indexing_results = message_queue.process_message_queue('indexing', 25)

        logger.info(f"Queue processing results - Notifications: {notification_results}, Indexing: {indexing_results}")

    except Exception as e:
        logger.error(f"Error processing background queues: {e}")


def get_scalability_metrics() -> Dict[str, Any]:
    """Get current scalability and performance metrics"""
    return {
        'active_servers': load_balancer.get_active_servers(),
        'server_id': load_balancer.server_id,
        'rate_limits': {
            'default_limits': rate_limiter.default_limits,
            'tier_multipliers': rate_limiter.tier_multipliers
        },
        'bulk_operations': {
            'max_bulk_size': bulk_operations.max_bulk_size,
            'batch_size': bulk_operations.batch_size
        }
    }

"""