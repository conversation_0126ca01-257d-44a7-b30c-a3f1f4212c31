"""
Timeline Service for Project Timeline & Gantt Chart functionality
Handles critical path calculation, dependency validation, and schedule analysis
"""

import logging
from datetime import timed<PERSON><PERSON>
from typing import Dict, List, Tuple
from django.db.models import QuerySet
from django.utils import timezone
            from CLEAR.models import Task

"""



logger = logging.getLogger(__name__)


class TimelineService:
    """Service for timeline calculations and dependency management"""
    
    def __init__(self, project=None):
        self.project = project
    
    def calculate_critical_path(self, tasks: QuerySet) -> List[str]:
        """
        Calculate the critical path using Critical Path Method (CPM)
        Returns list of task IDs that form the critical path
        """
        try:
            # Convert tasks to dictionary for easier processing
            task_dict = {task.id: task for task in tasks}
            
            # Calculate forward pass (earliest start/finish times)
            earliest_times = self._calculate_forward_pass(task_dict)
            
            # Calculate backward pass (latest start/finish times)
            latest_times = self._calculate_backward_pass(task_dict, earliest_times)
            
            # Identify critical path tasks (zero slack)
            critical_tasks = []
            for task_id, task in task_dict.items():
                slack = latest_times[task_id]['latest_start'] - earliest_times[task_id]['earliest_start']
                if slack == 0:
                    critical_tasks.append(task_id)
                    # Update task critical_path flag
                    task.critical_path = True
                else:
                    task.critical_path = False
            
            # Bulk update critical path flags
            Task.objects.bulk_update(tasks, ['critical_path'])
            
            return critical_tasks
            
        except Exception as e:
            logger.error(f"Error calculating critical path: {e}")
            return []
    
    def _calculate_forward_pass(self, task_dict: Dict) -> Dict:
        """Calculate earliest start and finish times for all tasks"""
        earliest_times = {}
        processed = set()
        
        def process_task(task_id: str):
            if task_id in processed:
                return
            
            task = task_dict[task_id]
            
            # Process all dependencies first
            for dep_id in task.dependencies:
                if dep_id in task_dict:
                    process_task(dep_id)
            
            # Calculate earliest start time
            earliest_start = task.start_date or timezone.now().date()
            
            # Check dependency constraints
            for dep_id in task.dependencies:
                if dep_id in earliest_times:
                    dep_finish = earliest_times[dep_id]['earliest_finish']
                    if dep_finish and dep_finish >= earliest_start:
                        earliest_start = dep_finish + timedelta(days=1)
            
            # Calculate earliest finish time
            duration = task.calculated_duration or 1
            earliest_finish = earliest_start + timedelta(days=duration - 1)
            
            earliest_times[task_id] = {
                'earliest_start': earliest_start,
                'earliest_finish': earliest_finish
            }
            processed.add(task_id)
        
        # Process all tasks
        for task_id in task_dict:
            process_task(task_id)
        
        return earliest_times
    
    def _calculate_backward_pass(self, task_dict: Dict, earliest_times: Dict) -> Dict:
        """Calculate latest start and finish times for all tasks"""
        latest_times = {}
        
        # Find project end date (latest finish time)
        project_end = max(
            earliest_times[task_id]['earliest_finish'] 
            for task_id in earliest_times
        )
        
        # Process tasks in reverse dependency order
        processed = set()
        
        def process_task(task_id: str):
            if task_id in processed:
                return
            
            task = task_dict[task_id]
            
            # Process all successors first
            successors = [
                tid for tid, t in task_dict.items() 
                if task_id in t.dependencies
            ]
            
            for succ_id in successors:
                process_task(succ_id)
            
            # Calculate latest finish time
            latest_finish = project_end
            
            # Check successor constraints
            for succ_id in successors:
                if succ_id in latest_times:
                    succ_start = latest_times[succ_id]['latest_start']
                    candidate_finish = succ_start - timedelta(days=1)
                    if candidate_finish < latest_finish:
                        latest_finish = candidate_finish
            
            # For tasks with no successors, use project end date
            if not successors:
                latest_finish = earliest_times[task_id]['earliest_finish']
            
            # Calculate latest start time
            duration = task.calculated_duration or 1
            latest_start = latest_finish - timedelta(days=duration - 1)
            
            latest_times[task_id] = {
                'latest_start': latest_start,
                'latest_finish': latest_finish
            }
            processed.add(task_id)
        
        # Process all tasks
        for task_id in task_dict:
            process_task(task_id)
        
        return latest_times
    
    def validate_dependencies(self, task_id: str, new_dependencies: List[str]) -> Tuple[bool, str]:
        """
        Validate that adding dependencies won't create circular references
        Returns (is_valid, error_message)
        """
        try:
            
            # Check for direct circular dependency
            if task_id in new_dependencies:
                return False, "Task cannot depend on itself"
            
            # Build dependency graph
            from ..models import Task
            tasks = Task.objects.filter(project=self.project) if self.project else Task.objects.all()
            dependency_graph = {}
            
            for task in tasks:
                deps = list(task.dependencies) if task.dependencies else []
                if task.id == task_id:
                    deps = new_dependencies  # Use proposed new dependencies
                dependency_graph[task.id] = deps
            
            # Check for circular dependencies using DFS
            visited = set()
            rec_stack = set()
            
            def has_cycle(node: str) -> bool:
                if node in rec_stack:
                    return True
                if node in visited:
                    return False
                
                visited.add(node)
                rec_stack.add(node)
                
                for neighbor in dependency_graph.get(node, []):
                    if has_cycle(neighbor):
                        return True
                
                rec_stack.remove(node)
                return False
            
            for node in dependency_graph:
                if has_cycle(node):
                    return False, "Adding these dependencies would create a circular reference"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"Error validating dependencies: {e}")
            return False, f"Validation error: {str(e)}"
    
    def calculate_project_metrics(self, tasks: QuerySet) -> Dict:
        """Calculate overall project timeline metrics"""
        try:
            if not tasks.exists():
                return {}
            
            # Basic counts
            total_tasks = tasks.count()
            completed_tasks = tasks.filter(completed=True).count()
            overdue_tasks = sum(1 for task in tasks if task.is_overdue)
            critical_tasks = tasks.filter(critical_path=True).count()
            milestones = tasks.filter(milestone=True).count()
            
            # Date calculations
            start_dates = [task.start_date for task in tasks if task.start_date]
            end_dates = [task.end_date for task in tasks if task.end_date]
            
            project_start = min(start_dates) if start_dates else None
            project_end = max(end_dates) if end_dates else None
            
            # Progress calculation
            total_progress = sum(task.progress_percentage for task in tasks)
            average_progress = total_progress / total_tasks if total_tasks > 0 else 0
            
            # Duration calculation
            project_duration = None
            if project_start and project_end:
                project_duration = (project_end - project_start).days + 1
            
            return {
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'completion_percentage': (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                'overdue_tasks': overdue_tasks,
                'critical_tasks': critical_tasks,
                'milestones': milestones,
                'project_start': project_start,
                'project_end': project_end,
                'project_duration': project_duration,
                'average_progress': round(average_progress, 2),
                'on_track': overdue_tasks == 0,
            }
            
        except Exception as e:
            logger.error(f"Error calculating project metrics: {e}")
            return {}
    
    def get_task_conflicts(self, tasks: QuerySet) -> List[Dict]:
        """Identify scheduling conflicts and resource constraints"""
        conflicts = []
        
        try:
            # Check for date conflicts
            for task in tasks:
                if task.start_date and task.end_date and task.start_date > task.end_date:
                    conflicts.append({
                        'type': 'date_conflict',
                        'task_id': task.id,
                        'task_title': task.title,
                        'message': 'Start date is after end date'
                    })
                
                # Check dependency conflicts
                for dep_id in task.dependencies:
                    try:
                        dep_task = tasks.get(id=dep_id)
                        if (task.start_date and dep_task.end_date and 
                            task.start_date <= dep_task.end_date):
                            conflicts.append({
                                'type': 'dependency_conflict',
                                'task_id': task.id,
                                'task_title': task.title,
                                'dependency_id': dep_id,
                                'dependency_title': dep_task.title,
                                'message': f'Task starts before dependency "{dep_task.title}" ends'
                            })
                    except Exception:
                        # Dependency task not found
                        conflicts.append({
                            'type': 'missing_dependency',
                            'task_id': task.id,
                            'task_title': task.title,
                            'dependency_id': dep_id,
                            'message': f'Dependency task "{dep_id}" not found'
                        })
                
                # Check resource conflicts (same assignee, overlapping dates)
                if task.assigned_to and task.start_date and task.end_date:
                    overlapping = tasks.filter(
                        assigned_to=task.assigned_to,
                        start_date__lte=task.end_date,
                        end_date__gte=task.start_date
                    ).exclude(id=task.id)
                    
                    for overlap_task in overlapping:
                        conflicts.append({
                            'type': 'resource_conflict',
                            'task_id': task.id,
                            'task_title': task.title,
                            'conflict_task_id': overlap_task.id,
                            'conflict_task_title': overlap_task.title,
                            'assignee': task.assigned_to.get_full_name() or task.assigned_to.username,
                            'message': f'Resource "{task.assigned_to.get_full_name()}" is double-booked'
                        })
            
            return conflicts
            
        except Exception as e:
            logger.error(f"Error checking task conflicts: {e}")
            return []
    
    def suggest_schedule_optimization(self, tasks: QuerySet) -> List[Dict]:
        """Suggest optimizations for project schedule"""
        suggestions = []
        
        try:
            # Calculate critical path first
            critical_path = self.calculate_critical_path(tasks)
            
            # Analyze critical path for optimization opportunities
            critical_tasks = tasks.filter(id__in=critical_path).order_by('start_date')
            
            for task in critical_tasks:
                # Suggest breaking down long tasks
                if task.calculated_duration > 10:
                    suggestions.append({
                        'type': 'break_down_task',
                        'task_id': task.id,
                        'task_title': task.title,
                        'priority': 'high',
                        'message': f'Consider breaking down this {task.calculated_duration}-day task for better control'
                    })
                
                # Suggest adding resources for critical tasks
                if not task.assigned_to:
                    suggestions.append({
                        'type': 'assign_resource',
                        'task_id': task.id,
                        'task_title': task.title,
                        'priority': 'high',
                        'message': 'Critical path task needs resource assignment'
                    })
            
            # Check for optimization opportunities
            metrics = self.calculate_project_metrics(tasks)
            if metrics.get('overdue_tasks', 0) > 0:
                suggestions.append({
                    'type': 'reschedule_overdue',
                    'priority': 'high',
                    'message': f'{metrics["overdue_tasks"]} overdue tasks need rescheduling'
                })
            
            # Suggest parallel execution opportunities
            non_critical = tasks.exclude(id__in=critical_path)
            for task in non_critical:
                if not task.dependencies:
                    suggestions.append({
                        'type': 'parallel_execution',
                        'task_id': task.id,
                        'task_title': task.title,
                        'priority': 'medium',
                        'message': 'This task could be started earlier in parallel'
                    })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating schedule suggestions: {e}")
            return []
"""