"""
System Monitoring Service for CLEAR Platform
Provides real-time system health metrics, performance monitoring, and administrative oversight.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List
import psutil
import redis
from django.conf import settings
from django.contrib.sessions.models import Session
from django.core.cache import cache
from django.db import connection
from django.utils import timezone
            from ..models import ChatMessage, Document, Project, Task, User
            from ..models import User

"""



logger = logging.getLogger(__name__)


class SystemMonitoringService:
    """
    Comprehensive system monitoring service providing real-time metrics
    for administrative oversight and system health monitoring.
    """
    
    def __init__(self):
        self.cache_timeout = 60  # Cache metrics for 1 minute
        
    def get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system overview metrics"""
        cache_key = 'system_overview_metrics'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
            
        try:
            overview = {
                'timestamp': timezone.now().isoformat(),
                'system_health': self._get_system_health(),
                'database_metrics': self._get_database_metrics(),
                'application_metrics': self._get_application_metrics(),
                'user_activity': self._get_user_activity_metrics(),
                'performance_metrics': self._get_performance_metrics(),
                'alerts': self._get_system_alerts()
            }
            
            cache.set(cache_key, overview, self.cache_timeout)
            return overview
            
        except Exception as e:
            logger.error(f"Error getting system overview: {e}")
            return self._get_fallback_metrics()
    
    def _get_system_health(self) -> Dict[str, Any]:
        """Get basic system health metrics"""
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available // (1024**3)  # GB
            
            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_free = disk.free // (1024**3)  # GB
            
            # Overall health score
            health_score = self._calculate_health_score(cpu_percent, memory_percent, disk_percent)
            
            return {
                'overall_status': 'healthy' if health_score > 80 else 'warning' if health_score > 60 else 'critical',
                'health_score': health_score,
                'cpu_usage': cpu_percent,
                'memory_usage': memory_percent,
                'memory_available_gb': memory_available,
                'disk_usage': disk_percent,
                'disk_free_gb': disk_free,
                'uptime': self._get_system_uptime()
            }
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                'overall_status': 'unknown',
                'health_score': 0,
                'error': str(e)
            }
    
    def _get_database_metrics(self) -> Dict[str, Any]:
        """Get database performance and health metrics"""
        try:
            with connection.cursor() as cursor:
                # Get database size
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as db_size,
                           pg_database_size(current_database()) as db_size_bytes
                """)
                db_size_info = cursor.fetchone()
                
                # Get connection count
                cursor.execute("""
                    SELECT count(*) as active_connections,
                           (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections
                """)
                connection_info = cursor.fetchone()
                
                # Get table statistics
                cursor.execute("""
                    SELECT schemaname, tablename, n_tup_ins + n_tup_upd + n_tup_del as total_operations,
                           n_live_tup as live_tuples
                    FROM pg_stat_user_tables 
                    WHERE schemaname = 'public'
                    ORDER BY total_operations DESC
                    LIMIT 10
                """)
                table_stats = cursor.fetchall()
                
                # Calculate connection usage percentage
                connection_usage = (connection_info[0] / connection_info[1]) * 100 if connection_info[1] > 0 else 0
                
                return {
                    'status': 'healthy' if connection_usage < 80 else 'warning',
                    'size_formatted': db_size_info[0],
                    'size_bytes': db_size_info[1],
                    'active_connections': connection_info[0],
                    'max_connections': connection_info[1],
                    'connection_usage_percent': round(connection_usage, 2),
                    'top_tables': [
                        {
                            'schema': row[0],
                            'table': row[1],
                            'operations': row[2],
                            'live_tuples': row[3]
                        } for row in table_stats
                    ]
                }
        except Exception as e:
            logger.error(f"Error getting database metrics: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _get_application_metrics(self) -> Dict[str, Any]:
        """Get Django application-specific metrics"""
        try:
            
            # Get model counts
            total_users = User.objects.count()
            active_users = User.objects.filter(status='active').count()
            total_projects = Project.objects.count()
            active_projects = Project.objects.filter(status='active').count()
            total_tasks = Task.objects.count()
            total_documents = Document.objects.count()
            
            # Get recent activity (last 24 hours)
            yesterday = timezone.now() - timedelta(days=1)
            recent_logins = User.objects.filter(last_login__gte=yesterday).count()
            recent_messages = ChatMessage.objects.filter(created_at__gte=yesterday).count()
            
            # Get active sessions
            active_sessions = Session.objects.filter(expire_date__gte=timezone.now()).count()
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'user_activity_rate': round((active_users / total_users) * 100, 2) if total_users > 0 else 0,
                'total_projects': total_projects,
                'active_projects': active_projects,
                'project_activity_rate': round((active_projects / total_projects) * 100, 2) if total_projects > 0 else 0,
                'total_tasks': total_tasks,
                'total_documents': total_documents,
                'recent_logins_24h': recent_logins,
                'recent_messages_24h': recent_messages,
                'active_sessions': active_sessions
            }
        except Exception as e:
            logger.error(f"Error getting application metrics: {e}")
            return {
                'error': str(e)
            }
    
    def _get_user_activity_metrics(self) -> Dict[str, Any]:
        """Get detailed user activity and engagement metrics"""
        try:
            
            now = timezone.now()
            
            # Users active in different time periods
            active_now = User.objects.filter(last_activity__gte=now - timedelta(minutes=5)).count()
            active_1h = User.objects.filter(last_activity__gte=now - timedelta(hours=1)).count()
            active_24h = User.objects.filter(last_activity__gte=now - timedelta(days=1)).count()
            active_7d = User.objects.filter(last_activity__gte=now - timedelta(days=7)).count()
            
            # Get user engagement metrics
            total_users = User.objects.filter(status='active').count()
            
            return {
                'online_now': active_now,
                'active_1_hour': active_1h,
                'active_24_hours': active_24h,
                'active_7_days': active_7d,
                'total_active_users': total_users,
                'engagement_rates': {
                    'hourly': round((active_1h / total_users) * 100, 2) if total_users > 0 else 0,
                    'daily': round((active_24h / total_users) * 100, 2) if total_users > 0 else 0,
                    'weekly': round((active_7d / total_users) * 100, 2) if total_users > 0 else 0
                }
            }
        except Exception as e:
            logger.error(f"Error getting user activity metrics: {e}")
            return {
                'error': str(e)
            }
    
    def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get application performance metrics"""
        try:
            # Get average response times from cache if available
            avg_response_time = cache.get('avg_response_time', 245)  # Default 245ms
            
            # Get Redis metrics if available
            redis_metrics = self._get_redis_metrics()
            
            # Get request metrics
            request_metrics = self._get_request_metrics()
            
            return {
                'average_response_time_ms': avg_response_time,
                'redis': redis_metrics,
                'requests': request_metrics,
                'cache_hit_rate': self._get_cache_hit_rate()
            }
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {
                'error': str(e)
            }
    
    def _get_redis_metrics(self) -> Dict[str, Any]:
        """Get Redis cache metrics"""
        try:
            redis_client = redis.from_url(settings.REDIS_URL)
            info = redis_client.info()
            
            return {
                'status': 'connected',
                'used_memory_human': info.get('used_memory_human', 'Unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': round(
                    (info.get('keyspace_hits', 0) / 
                     (info.get('keyspace_hits', 0) + info.get('keyspace_misses', 1))) * 100, 2
                )
            }
        except Exception as e:
            logger.warning(f"Redis metrics unavailable: {e}")
            return {
                'status': 'disconnected',
                'error': str(e)
            }
    
    def _get_request_metrics(self) -> Dict[str, Any]:
        """Get HTTP request metrics"""
        try:
            # These would typically come from request middleware or logging
            # For now, return cached/estimated values
            return {
                'requests_per_minute': cache.get('requests_per_minute', 42),
                'error_rate_percent': cache.get('error_rate_percent', 0.5),
                'slow_requests_percent': cache.get('slow_requests_percent', 2.1)
            }
        except Exception as e:
            logger.error(f"Error getting request metrics: {e}")
            return {
                'error': str(e)
            }
    
    def _get_system_alerts(self) -> List[Dict[str, Any]]:
        """Get current system alerts and warnings"""
        alerts = []
        
        try:
            # Check disk space
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 90:
                alerts.append({
                    'level': 'critical',
                    'type': 'disk_space',
                    'message': f'Disk usage critical: {disk_percent:.1f}%',
                    'timestamp': timezone.now().isoformat()
                })
            elif disk_percent > 80:
                alerts.append({
                    'level': 'warning',
                    'type': 'disk_space',
                    'message': f'Disk usage high: {disk_percent:.1f}%',
                    'timestamp': timezone.now().isoformat()
                })
            
            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                alerts.append({
                    'level': 'critical',
                    'type': 'memory',
                    'message': f'Memory usage critical: {memory.percent:.1f}%',
                    'timestamp': timezone.now().isoformat()
                })
            elif memory.percent > 80:
                alerts.append({
                    'level': 'warning',
                    'type': 'memory',
                    'message': f'Memory usage high: {memory.percent:.1f}%',
                    'timestamp': timezone.now().isoformat()
                })
            
            # Check database connections
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT count(*) as active_connections,
                           (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections
                """)
                conn_info = cursor.fetchone()
                conn_usage = (conn_info[0] / conn_info[1]) * 100 if conn_info[1] > 0 else 0
                
                if conn_usage > 90:
                    alerts.append({
                        'level': 'critical',
                        'type': 'database',
                        'message': f'Database connections critical: {conn_usage:.1f}%',
                        'timestamp': timezone.now().isoformat()
                    })
                elif conn_usage > 80:
                    alerts.append({
                        'level': 'warning',
                        'type': 'database',
                        'message': f'Database connections high: {conn_usage:.1f}%',
                        'timestamp': timezone.now().isoformat()
                    })
            
        except Exception as e:
            logger.error(f"Error generating system alerts: {e}")
            alerts.append({
                'level': 'error',
                'type': 'monitoring',
                'message': f'Monitoring system error: {str(e)}',
                'timestamp': timezone.now().isoformat()
            })
        
        return alerts
    
    def _calculate_health_score(self, cpu: float, memory: float, disk: float) -> int:
        """Calculate overall system health score (0-100)"""
        try:
            # Weight different metrics
            cpu_score = max(0, 100 - cpu)
            memory_score = max(0, 100 - memory)
            disk_score = max(0, 100 - disk)
            
            # Weighted average (CPU and memory more important than disk)
            overall_score = (cpu_score * 0.4) + (memory_score * 0.4) + (disk_score * 0.2)
            
            return int(round(overall_score))
        except Exception:
            return 0
    
    def _get_system_uptime(self) -> str:
        """Get system uptime in human-readable format"""
        try:
            uptime_seconds = psutil.boot_time()
            uptime_datetime = datetime.fromtimestamp(uptime_seconds)
            uptime_delta = datetime.now() - uptime_datetime
            
            days = uptime_delta.days
            hours = uptime_delta.seconds // 3600
            minutes = (uptime_delta.seconds % 3600) // 60
            
            if days > 0:
                return f"{days}d {hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"
        except Exception:
            return "Unknown"
    
    def _get_cache_hit_rate(self) -> float:
        """Get cache hit rate percentage"""
        try:
            # This would typically be tracked by middleware
            return cache.get('cache_hit_rate', 85.2)
        except Exception:
            return 0.0
    
    def _get_fallback_metrics(self) -> Dict[str, Any]:
        """Return fallback metrics when monitoring is unavailable"""
        return {
            'timestamp': timezone.now().isoformat(),
            'system_health': {
                'overall_status': 'unknown',
                'health_score': 0,
                'error': 'Monitoring unavailable'
            },
            'database_metrics': {
                'status': 'unknown'
            },
            'application_metrics': {
                'error': 'Metrics unavailable'
            },
            'user_activity': {
                'error': 'Activity tracking unavailable'
            },
            'performance_metrics': {
                'error': 'Performance monitoring unavailable'
            },
            'alerts': [{
                'level': 'warning',
                'type': 'monitoring',
                'message': 'System monitoring is currently unavailable',
                'timestamp': timezone.now().isoformat()
            }]
        }

    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time metrics for dashboard updates (lightweight)"""
        try:
            # CPU and memory (quick checks)
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # Active users (cached)
            now = timezone.now()
            online_users = User.objects.filter(
                last_activity__gte=now - timedelta(minutes=5)
            ).count()
            
            # Active sessions
            active_sessions = Session.objects.filter(
                expire_date__gte=timezone.now()
            ).count()
            
            return {
                'timestamp': timezone.now().isoformat(),
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'online_users': online_users,
                'active_sessions': active_sessions,
                'system_status': 'healthy' if cpu_percent < 80 and memory.percent < 80 else 'warning'
            }
        except Exception as e:
            logger.error(f"Error getting real-time metrics: {e}")
            return {
                'timestamp': timezone.now().isoformat(),
                'error': str(e),
                'system_status': 'error'
            }
"""