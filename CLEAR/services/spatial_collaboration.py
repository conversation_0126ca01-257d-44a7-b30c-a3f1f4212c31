"""
Spatial Collaboration Service for Enhanced Mapping Interface Phase 2

Provides real-time collaborative mapping capabilities including cursor tracking,
annotation sharing, and collaborative drawing tools.
"""

import json
import logging
from datetime import timedelta
from typing import Any, Dict, List
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.contrib.gis.geos import GEOSGeometry, Point
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone
from ..models import (

"""



    CollaborativeDrawing,
    Project,
    SpatialAnnotation,
    User,
    UserPresence,
)

logger = logging.getLogger(__name__)


class SpatialCollaborationService:
    """Service for managing collaborative spatial operations"""
    
    def __init__(self, project_id: int, user: User):
        self.project_id = project_id
        self.user = user
        self.project = Project.objects.get(id=project_id)
        self.channel_layer = get_channel_layer()
    
    def join_collaboration(self, session_id: str) -> Dict[str, Any]:
        """Join collaborative mapping session"""
        try:
            # Create or update user presence
            presence, created = UserPresence.objects.update_or_create(
                project=self.project,
                user=self.user,
                session_id=session_id,
                defaults={
                    'is_active': True,
                    'last_seen': timezone.now(),
                    'left_at': None
                }
            )
            
            # Get active collaborators
            active_users = self.get_active_collaborators()
            
            # Broadcast user joined
            self._broadcast_presence_update({
                'type': 'user_joined',
                'user_id': self.user.id,
                'username': self.user.username,
                'user_initials': self.user.get_initials(),
                'session_id': session_id,
                'timestamp': timezone.now().isoformat()
            })
            
            return {
                'success': True,
                'presence_id': presence.id,
                'active_users': active_users,
                'message': 'Successfully joined collaboration session'
            }
            
        except Exception as e:
            logger.error(f"Error joining collaboration: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def leave_collaboration(self, session_id: str) -> Dict[str, Any]:
        """Leave collaborative mapping session"""
        try:
            # Mark user as inactive
            UserPresence.objects.filter(
                project=self.project,
                user=self.user,
                session_id=session_id
            ).update(
                is_active=False,
                left_at=timezone.now()
            )
            
            # Broadcast user left
            self._broadcast_presence_update({
                'type': 'user_left',
                'user_id': self.user.id,
                'username': self.user.username,
                'session_id': session_id,
                'timestamp': timezone.now().isoformat()
            })
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error leaving collaboration: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def update_cursor_position(self, lng: float, lat: float, session_id: str) -> Dict[str, Any]:
        """Update user cursor position and broadcast to collaborators"""
        try:
            # Validate coordinates
            if not (-180 <= lng <= 180) or not (-90 <= lat <= 90):
                return {'success': False, 'error': 'Invalid coordinates'}
            
            cursor_point = Point(lng, lat, srid=4326)
            
            # Update user presence
            UserPresence.objects.filter(
                project=self.project,
                user=self.user,
                session_id=session_id
            ).update(
                last_cursor_position=cursor_point,
                last_seen=timezone.now()
            )
            
            # Broadcast cursor update (throttled)
            cache_key = f"cursor_throttle_{self.user.id}_{self.project_id}"
            if not cache.get(cache_key):
                self._broadcast_cursor_update({
                    'type': 'cursor_update',
                    'user_id': self.user.id,
                    'username': self.user.username,
                    'coordinates': [lng, lat],
                    'session_id': session_id,
                    'timestamp': timezone.now().isoformat()
                })
                # Throttle cursor updates to 10 per second
                cache.set(cache_key, True, timeout=0.1)
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error updating cursor position: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def create_annotation(self, geometry_data: Dict, annotation_data: Dict) -> Dict[str, Any]:
        """Create collaborative spatial annotation"""
        try:
            with transaction.atomic():
                # Parse geometry
                geometry = GEOSGeometry(json.dumps(geometry_data))
                
                # Create annotation
                annotation = SpatialAnnotation.objects.create(
                    project=self.project,
                    user=self.user,
                    geometry=geometry,
                    annotation_text=annotation_data.get('text', ''),
                    annotation_type=annotation_data.get('type', 'note'),
                    color=annotation_data.get('color', '#ff0000'),
                    metadata=annotation_data.get('metadata', {})
                )
                
                # Broadcast new annotation
                self._broadcast_annotation_update({
                    'type': 'annotation_created',
                    'annotation_id': annotation.id,
                    'user_id': self.user.id,
                    'username': self.user.username,
                    'geometry': geometry_data,
                    'annotation_type': annotation.annotation_type,
                    'text': annotation.annotation_text,
                    'color': annotation.color,
                    'timestamp': annotation.created_at.isoformat()
                })
                
                return {
                    'success': True,
                    'annotation_id': annotation.id,
                    'message': 'Annotation created successfully'
                }
                
        except Exception as e:
            logger.error(f"Error creating annotation: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def update_annotation(self, annotation_id: int, updates: Dict) -> Dict[str, Any]:
        """Update existing annotation"""
        try:
            with transaction.atomic():
                annotation = SpatialAnnotation.objects.select_for_update().get(
                    id=annotation_id,
                    project=self.project
                )
                
                # Check permissions (user can only edit their own annotations)
                if annotation.user != self.user:
                    return {'success': False, 'error': 'Permission denied'}
                
                # Update annotation
                for field, value in updates.items():
                    if hasattr(annotation, field):
                        setattr(annotation, field, value)
                
                annotation.save()
                
                # Broadcast update
                self._broadcast_annotation_update({
                    'type': 'annotation_updated',
                    'annotation_id': annotation.id,
                    'user_id': self.user.id,
                    'username': self.user.username,
                    'updates': updates,
                    'timestamp': annotation.updated_at.isoformat()
                })
                
                return {'success': True, 'message': 'Annotation updated successfully'}
                
        except SpatialAnnotation.DoesNotExist:
            return {'success': False, 'error': 'Annotation not found'}
        except Exception as e:
            logger.error(f"Error updating annotation: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def delete_annotation(self, annotation_id: int) -> Dict[str, Any]:
        """Soft delete annotation"""
        try:
            annotation = SpatialAnnotation.objects.get(
                id=annotation_id,
                project=self.project
            )
            
            # Check permissions
            if annotation.user != self.user:
                return {'success': False, 'error': 'Permission denied'}
            
            # Soft delete
            annotation.soft_delete()
            
            # Broadcast deletion
            self._broadcast_annotation_update({
                'type': 'annotation_deleted',
                'annotation_id': annotation.id,
                'user_id': self.user.id,
                'username': self.user.username,
                'timestamp': timezone.now().isoformat()
            })
            
            return {'success': True, 'message': 'Annotation deleted successfully'}
            
        except SpatialAnnotation.DoesNotExist:
            return {'success': False, 'error': 'Annotation not found'}
        except Exception as e:
            logger.error(f"Error deleting annotation: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def create_collaborative_drawing(self, drawing_data: Dict) -> Dict[str, Any]:
        """Create collaborative drawing element"""
        try:
            with transaction.atomic():
                # Parse geometry
                geometry = GEOSGeometry(json.dumps(drawing_data['geometry']))
                
                # Create drawing
                drawing = CollaborativeDrawing.objects.create(
                    project=self.project,
                    user=self.user,
                    drawing_type=drawing_data.get('type', 'line'),
                    geometry=geometry,
                    style=drawing_data.get('style', {}),
                    is_temporary=drawing_data.get('is_temporary', False)
                )
                
                # Broadcast new drawing
                self._broadcast_drawing_update({
                    'type': 'drawing_created',
                    'drawing_id': drawing.id,
                    'user_id': self.user.id,
                    'username': self.user.username,
                    'drawing_type': drawing.drawing_type,
                    'geometry': drawing_data['geometry'],
                    'style': drawing.style,
                    'is_temporary': drawing.is_temporary,
                    'timestamp': drawing.created_at.isoformat()
                })
                
                return {
                    'success': True,
                    'drawing_id': drawing.id,
                    'message': 'Drawing created successfully'
                }
                
        except Exception as e:
            logger.error(f"Error creating drawing: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def get_active_collaborators(self) -> List[Dict[str, Any]]:
        """Get list of active collaborators"""
        try:
            active_users = UserPresence.get_active_users(self.project)
            
            collaborators = []
            for presence in active_users:
                user_data = {
                    'user_id': presence.user.id,
                    'username': presence.user.username,
                    'display_name': presence.user.get_full_name() or presence.user.username,
                    'initials': presence.user.get_initials(),
                    'current_tool': presence.current_tool,
                    'last_seen': presence.last_seen.isoformat(),
                    'session_id': presence.session_id
                }
                
                # Include cursor position if available
                if presence.last_cursor_position:
                    user_data['cursor_position'] = [
                        presence.last_cursor_position.x,
                        presence.last_cursor_position.y
                    ]
                
                collaborators.append(user_data)
            
            return collaborators
            
        except Exception as e:
            logger.error(f"Error getting active collaborators: {str(e)}")
            return []
    
    def get_project_annotations(self, include_deleted: bool = False) -> List[Dict[str, Any]]:
        """Get all project annotations"""
        try:
            queryset = SpatialAnnotation.objects.filter(project=self.project)
            
            if not include_deleted:
                queryset = queryset.filter(deleted_at__isnull=True)
            
            annotations = []
            for annotation in queryset.select_related('user'):
                annotations.append({
                    'id': annotation.id,
                    'user_id': annotation.user.id,
                    'username': annotation.user.username,
                    'text': annotation.annotation_text,
                    'type': annotation.annotation_type,
                    'color': annotation.color,
                    'geometry': json.loads(annotation.geometry.geojson),
                    'is_visible': annotation.is_visible,
                    'metadata': annotation.metadata,
                    'created_at': annotation.created_at.isoformat(),
                    'updated_at': annotation.updated_at.isoformat()
                })
            
            return annotations
            
        except Exception as e:
            logger.error(f"Error getting project annotations: {str(e)}")
            return []
    
    def cleanup_inactive_users(self) -> Dict[str, Any]:
        """Clean up inactive user presence records"""
        try:
            cutoff_time = timezone.now() - timedelta(minutes=10)
            
            # Mark old presences as inactive
            updated = UserPresence.objects.filter(
                project=self.project,
                last_seen__lt=cutoff_time,
                is_active=True
            ).update(
                is_active=False,
                left_at=timezone.now()
            )
            
            return {
                'success': True,
                'cleaned_up': updated,
                'message': f'Cleaned up {updated} inactive presence records'
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up inactive users: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _broadcast_presence_update(self, message: Dict[str, Any]) -> None:
        """Broadcast presence update to project group"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"spatial_collaboration_{self.project_id}",
                {
                    'type': 'presence_update',
                    'message': message
                }
            )
    
    def _broadcast_cursor_update(self, message: Dict[str, Any]) -> None:
        """Broadcast cursor update to project group"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"spatial_collaboration_{self.project_id}",
                {
                    'type': 'cursor_update',
                    'message': message
                }
            )
    
    def _broadcast_annotation_update(self, message: Dict[str, Any]) -> None:
        """Broadcast annotation update to project group"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"spatial_collaboration_{self.project_id}",
                {
                    'type': 'annotation_update',
                    'message': message
                }
            )
    
    def _broadcast_drawing_update(self, message: Dict[str, Any]) -> None:
        """Broadcast drawing update to project group"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"spatial_collaboration_{self.project_id}",
                {
                    'type': 'drawing_update',
                    'message': message
                }
            )


class CollaborativeConflictResolution:
    """Handle conflicts in collaborative editing"""
    
    @staticmethod
    def resolve_annotation_conflict(annotation_id: int, user_version: int, server_version: int) -> Dict[str, Any]:
        """Resolve conflicts when multiple users edit the same annotation"""
        try:
            annotation = SpatialAnnotation.objects.get(id=annotation_id)
            
            if user_version < server_version:
                # User version is outdated, reject changes
                return {
                    'success': False,
                    'conflict': True,
                    'error': 'Annotation was modified by another user',
                    'latest_version': server_version,
                    'latest_data': {
                        'text': annotation.annotation_text,
                        'type': annotation.annotation_type,
                        'color': annotation.color,
                        'updated_at': annotation.updated_at.isoformat()
                    }
                }
            
            return {'success': True, 'conflict': False}
            
        except SpatialAnnotation.DoesNotExist:
            return {'success': False, 'error': 'Annotation not found'}
    
    @staticmethod
    def merge_drawing_operations(drawing_id: int, operations: List[Dict]) -> Dict[str, Any]:
        """Merge conflicting drawing operations using operational transformation"""
        try:
            # Simplified operational transformation for drawing operations
            # In a production environment, this would be more sophisticated
            
            drawing = CollaborativeDrawing.objects.get(id=drawing_id)
            
            # Sort operations by timestamp
            sorted_ops = sorted(operations, key=lambda x: x.get('timestamp', 0))
            
            # Apply operations in order
            for operation in sorted_ops:
                if operation['type'] == 'move':
                    # Transform move operations
                    pass
                elif operation['type'] == 'style':
                    # Apply style changes
                    drawing.style.update(operation.get('style_changes', {}))
                elif operation['type'] == 'geometry':
                    # Apply geometry changes (last one wins for now)
                    if 'geometry' in operation:
                        drawing.geometry = GEOSGeometry(json.dumps(operation['geometry']))
            
            drawing.version += 1
            drawing.save()
            
            return {
                'success': True,
                'new_version': drawing.version,
                'resolved_geometry': json.loads(drawing.geometry.geojson),
                'resolved_style': drawing.style
            }
            
        except CollaborativeDrawing.DoesNotExist:
            return {'success': False, 'error': 'Drawing not found'}
        except Exception as e:
            logger.error(f"Error merging drawing operations: {str(e)}")
            return {'success': False, 'error': str(e)}
"""