"""
Document Analytics Service for CLEAR application.

Provides comprehensive analytics for document usage, collaboration patterns,
search trends, and performance insights.
"""

import logging
from collections import Counter
from datetime import datetime, timedelta
from typing import Any, Dict, List
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import Avg, Count, F, Max, Min, Q, Sum
from django.db.models.functions import <PERSON>runcDate, <PERSON>runcHour, TruncMonth, TruncWeek
from django.utils import timezone
from ..models import (
        import math
        import os

"""



    Document,
    DocumentActivity,
    DocumentCollaborationSession,
    DocumentDiscussion,
    DocumentVersion,
)

User = get_user_model()
logger = logging.getLogger(__name__)


class DocumentAnalyticsService:
    """
    Comprehensive document analytics service providing insights into:
    - Document usage patterns
    - Collaboration metrics
    - Search analytics
    - Performance insights
    - User behavior analysis
    """
    
    def __init__(self, user: User = None):
        self.user = user
        self.cache_timeout = 3600  # 1 hour
        
    def get_dashboard_analytics(self, timeframe_days: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive dashboard analytics.
        
        Args:
            timeframe_days: Number of days to analyze
            
        Returns:
            Dictionary with dashboard analytics data
        """
        cache_key = f"document_analytics:dashboard:{self.user.id if self.user else 'all'}:{timeframe_days}"
        cached_data = cache.get(cache_key)
        if cached_data:
            return cached_data
        
        try:
            since_date = timezone.now() - timedelta(days=timeframe_days)
            
            # Get base querysets
            documents_qs = self._get_user_documents()
            activities_qs = DocumentActivity.objects.filter(
                document__in=documents_qs,
                created_at__gte=since_date
            )
            
            # Compile analytics data
            analytics_data = {
                'overview': self._get_overview_metrics(documents_qs, activities_qs, since_date),
                'activity_trends': self._get_activity_trends(activities_qs, timeframe_days),
                'document_metrics': self._get_document_metrics(documents_qs, since_date),
                'collaboration_insights': self._get_collaboration_insights(documents_qs, since_date),
                'user_activity': self._get_user_activity_metrics(activities_qs, since_date),
                'performance_metrics': self._get_performance_metrics(documents_qs, since_date),
                'search_insights': self._get_search_insights(since_date),
                'popular_content': self._get_popular_content(documents_qs, activities_qs),
                'timeframe_days': timeframe_days,
                'generated_at': timezone.now().isoformat()
            }
            
            # Cache the results
            cache.set(cache_key, analytics_data, self.cache_timeout)
            
            return analytics_data
            
        except Exception as e:
            logger.error(f"Error generating dashboard analytics: {e}")
            return self._get_empty_analytics_data()
    
    def _get_overview_metrics(self, documents_qs, activities_qs, since_date: datetime) -> Dict[str, Any]:
        """Get high-level overview metrics."""
        try:
            # Basic counts
            total_documents = documents_qs.count()
            total_activities = activities_qs.count()
            
            # Recent activity
            recent_documents = documents_qs.filter(created_at__gte=since_date).count()
            recent_activities = activities_qs.count()
            
            # Active users
            active_users = activities_qs.values('user').distinct().count()
            
            # Collaboration metrics
            collaboration_sessions = DocumentCollaborationSession.objects.filter(
                document__in=documents_qs,
                started_at__gte=since_date
            ).count()
            
            discussions = DocumentDiscussion.objects.filter(
                document__in=documents_qs,
                created_at__gte=since_date
            ).count()
            
            # Storage metrics
            total_storage = documents_qs.aggregate(
                total_size=Sum('file_size')
            )['total_size'] or 0
            
            return {
                'total_documents': total_documents,
                'recent_documents': recent_documents,
                'total_activities': total_activities,
                'recent_activities': recent_activities,
                'active_users': active_users,
                'collaboration_sessions': collaboration_sessions,
                'discussions': discussions,
                'total_storage_bytes': total_storage,
                'total_storage_human': self._format_file_size(total_storage),
                'growth_rate': self._calculate_growth_rate(documents_qs, since_date)
            }
            
        except Exception as e:
            logger.error(f"Error getting overview metrics: {e}")
            return {}
    
    def _get_activity_trends(self, activities_qs, timeframe_days: int) -> Dict[str, Any]:
        """Get activity trends over time."""
        try:
            # Determine granularity based on timeframe
            if timeframe_days <= 7:
                granularity = 'hour'
                trunc_func = TruncHour
            elif timeframe_days <= 30:
                granularity = 'day'
                trunc_func = TruncDate
            elif timeframe_days <= 90:
                granularity = 'week'
                trunc_func = TruncWeek
            else:
                granularity = 'month'
                trunc_func = TruncMonth
            
            # Get activity trends
            activity_trends = activities_qs.annotate(
                period=trunc_func('created_at')
            ).values('period').annotate(
                count=Count('id')
            ).order_by('period')
            
            # Get activity type breakdown
            activity_types = activities_qs.values('activity_type').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # Convert to chart-friendly format
            trend_data = []
            type_data = []
            
            for item in activity_trends:
                trend_data.append({
                    'date': item['period'].isoformat(),
                    'count': item['count']
                })
            
            for item in activity_types:
                type_data.append({
                    'type': item['activity_type'],
                    'count': item['count']
                })
            
            return {
                'granularity': granularity,
                'trend_data': trend_data,
                'activity_types': type_data,
                'peak_activity': max([t['count'] for t in trend_data]) if trend_data else 0,
                'total_periods': len(trend_data)
            }
            
        except Exception as e:
            logger.error(f"Error getting activity trends: {e}")
            return {}
    
    def _get_document_metrics(self, documents_qs, since_date: datetime) -> Dict[str, Any]:
        """Get document-specific metrics."""
        try:
            # File type distribution
            file_types = documents_qs.extra(
                select={'file_type': "LOWER(SUBSTRING(name FROM '\\.([^.]*)$'))"}
            ).values('file_type').annotate(
                count=Count('id'),
                total_size=Sum('file_size')
            ).order_by('-count')
            
            # Document sizes
            size_stats = documents_qs.aggregate(
                avg_size=Avg('file_size'),
                min_size=Min('file_size'),
                max_size=Max('file_size'),
                total_size=Sum('file_size')
            )
            
            # Version statistics
            version_stats = DocumentVersion.objects.filter(
                document__in=documents_qs,
                created_at__gte=since_date
            ).aggregate(
                total_versions=Count('id'),
                avg_versions_per_doc=Avg('version_number')
            )
            
            # Archive status
            archive_stats = documents_qs.aggregate(
                active_count=Count('id', filter=Q(is_archived=False)),
                archived_count=Count('id', filter=Q(is_archived=True))
            )
            
            return {
                'file_types': [
                    {
                        'type': ft['file_type'] or 'unknown',
                        'count': ft['count'],
                        'total_size': ft['total_size'] or 0,
                        'total_size_human': self._format_file_size(ft['total_size'] or 0)
                    }
                    for ft in file_types[:10]  # Top 10 file types
                ],
                'size_stats': {
                    'average_size': size_stats['avg_size'] or 0,
                    'average_size_human': self._format_file_size(size_stats['avg_size'] or 0),
                    'largest_size': size_stats['max_size'] or 0,
                    'largest_size_human': self._format_file_size(size_stats['max_size'] or 0),
                    'smallest_size': size_stats['min_size'] or 0,
                    'total_size': size_stats['total_size'] or 0,
                    'total_size_human': self._format_file_size(size_stats['total_size'] or 0)
                },
                'version_stats': version_stats,
                'archive_stats': archive_stats
            }
            
        except Exception as e:
            logger.error(f"Error getting document metrics: {e}")
            return {}
    
    def _get_collaboration_insights(self, documents_qs, since_date: datetime) -> Dict[str, Any]:
        """Get collaboration insights and patterns."""
        try:
            # Collaboration sessions
            sessions = DocumentCollaborationSession.objects.filter(
                document__in=documents_qs,
                started_at__gte=since_date
            )
            
            session_stats = sessions.aggregate(
                total_sessions=Count('id'),
                unique_users=Count('user', distinct=True),
                avg_duration=Avg(
                    F('ended_at') - F('started_at'),
                    filter=Q(ended_at__isnull=False)
                )
            )
            
            # Discussion metrics
            discussions = DocumentDiscussion.objects.filter(
                document__in=documents_qs,
                created_at__gte=since_date
            )
            
            discussion_stats = discussions.aggregate(
                total_discussions=Count('id'),
                resolved_discussions=Count('id', filter=Q(status='resolved')),
                avg_comments_per_discussion=Avg(
                    Count('comments')
                )
            )
            
            # Most collaborative documents
            collaborative_docs = documents_qs.annotate(
                session_count=Count('documentcollaborationsession', 
                                  filter=Q(documentcollaborationsession__started_at__gte=since_date)),
                discussion_count=Count('documentdiscussion',
                                     filter=Q(documentdiscussion__created_at__gte=since_date))
            ).filter(
                Q(session_count__gt=0) | Q(discussion_count__gt=0)
            ).order_by('-session_count', '-discussion_count')[:5]
            
            # Most active collaborators
            active_collaborators = sessions.values(
                'user__username', 'user__first_name', 'user__last_name'
            ).annotate(
                session_count=Count('id'),
                total_time=Sum(F('ended_at') - F('started_at'), 
                             filter=Q(ended_at__isnull=False))
            ).order_by('-session_count')[:10]
            
            return {
                'session_stats': session_stats,
                'discussion_stats': discussion_stats,
                'collaborative_documents': [
                    {
                        'id': str(doc.id),
                        'name': doc.name,
                        'sessions': doc.session_count,
                        'discussions': doc.discussion_count
                    }
                    for doc in collaborative_docs
                ],
                'active_collaborators': [
                    {
                        'username': collab['user__username'],
                        'name': f"{collab['user__first_name']} {collab['user__last_name']}".strip(),
                        'sessions': collab['session_count'],
                        'total_time_minutes': (
                            collab['total_time'].total_seconds() / 60 
                            if collab['total_time'] else 0
                        )
                    }
                    for collab in active_collaborators
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting collaboration insights: {e}")
            return {}
    
    def _get_user_activity_metrics(self, activities_qs, since_date: datetime) -> Dict[str, Any]:
        """Get user activity patterns and metrics."""
        try:
            # Most active users
            active_users = activities_qs.values(
                'user__username', 'user__first_name', 'user__last_name'
            ).annotate(
                activity_count=Count('id'),
                unique_documents=Count('document', distinct=True),
                last_activity=Max('created_at')
            ).order_by('-activity_count')[:10]
            
            # Activity by hour of day
            hourly_activity = activities_qs.extra(
                select={'hour': 'EXTRACT(hour FROM created_at)'}
            ).values('hour').annotate(
                count=Count('id')
            ).order_by('hour')
            
            # Activity by day of week
            weekly_activity = activities_qs.extra(
                select={'dow': 'EXTRACT(dow FROM created_at)'}
            ).values('dow').annotate(
                count=Count('id')
            ).order_by('dow')
            
            # User engagement patterns
            engagement_data = self._analyze_user_engagement(activities_qs)
            
            return {
                'most_active_users': [
                    {
                        'username': user['user__username'],
                        'name': f"{user['user__first_name']} {user['user__last_name']}".strip(),
                        'activity_count': user['activity_count'],
                        'unique_documents': user['unique_documents'],
                        'last_activity': user['last_activity'].isoformat()
                    }
                    for user in active_users
                ],
                'hourly_patterns': [
                    {
                        'hour': int(item['hour']),
                        'count': item['count']
                    }
                    for item in hourly_activity
                ],
                'weekly_patterns': [
                    {
                        'day': int(item['dow']),
                        'count': item['count']
                    }
                    for item in weekly_activity
                ],
                'engagement_metrics': engagement_data
            }
            
        except Exception as e:
            logger.error(f"Error getting user activity metrics: {e}")
            return {}
    
    def _get_performance_metrics(self, documents_qs, since_date: datetime) -> Dict[str, Any]:
        """Get performance and efficiency metrics."""
        try:
            # Document creation velocity
            creation_velocity = documents_qs.filter(
                created_at__gte=since_date
            ).extra(
                select={'date': 'DATE(created_at)'}
            ).values('date').annotate(
                count=Count('id')
            ).aggregate(
                avg_daily_creation=Avg('count'),
                max_daily_creation=Max('count')
            )
            
            # Version update frequency
            version_frequency = DocumentVersion.objects.filter(
                document__in=documents_qs,
                created_at__gte=since_date
            ).count()
            
            # Response time metrics (from collaboration sessions)
            response_times = DocumentCollaborationSession.objects.filter(
                document__in=documents_qs,
                started_at__gte=since_date,
                ended_at__isnull=False
            ).aggregate(
                avg_session_duration=Avg(F('ended_at') - F('started_at')),
                max_session_duration=Max(F('ended_at') - F('started_at'))
            )
            
            # Storage efficiency
            storage_efficiency = self._calculate_storage_efficiency(documents_qs)
            
            return {
                'creation_metrics': {
                    'avg_daily_creation': creation_velocity['avg_daily_creation'] or 0,
                    'max_daily_creation': creation_velocity['max_daily_creation'] or 0,
                    'total_versions': version_frequency
                },
                'session_metrics': {
                    'avg_duration_minutes': (
                        response_times['avg_session_duration'].total_seconds() / 60
                        if response_times['avg_session_duration'] else 0
                    ),
                    'max_duration_minutes': (
                        response_times['max_session_duration'].total_seconds() / 60
                        if response_times['max_session_duration'] else 0
                    )
                },
                'storage_efficiency': storage_efficiency
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}
    
    def _get_search_insights(self, since_date: datetime) -> Dict[str, Any]:
        """Get search analytics and insights."""
        try:
            # This would integrate with the search service
            # For now, return basic structure
            search_data = cache.get('recent_search_queries', [])
            
            # Analyze search patterns
            if search_data:
                search_terms = []
                for query in search_data:
                    search_terms.extend(query.lower().split())
                
                term_frequency = Counter(search_terms)
                popular_terms = term_frequency.most_common(10)
                
                return {
                    'total_searches': len(search_data),
                    'unique_queries': len(set(search_data)),
                    'popular_terms': [
                        {'term': term, 'count': count}
                        for term, count in popular_terms
                    ],
                    'avg_query_length': (
                        sum(len(q.split()) for q in search_data) / len(search_data)
                        if search_data else 0
                    )
                }
            
            return {
                'total_searches': 0,
                'unique_queries': 0,
                'popular_terms': [],
                'avg_query_length': 0
            }
            
        except Exception as e:
            logger.error(f"Error getting search insights: {e}")
            return {}
    
    def _get_popular_content(self, documents_qs, activities_qs) -> Dict[str, Any]:
        """Get popular content analysis."""
        try:
            # Most viewed documents
            most_viewed = activities_qs.filter(
                activity_type='viewed'
            ).values('document').annotate(
                view_count=Count('id')
            ).order_by('-view_count')[:10]
            
            # Most downloaded documents
            most_downloaded = activities_qs.filter(
                activity_type='downloaded'
            ).values('document').annotate(
                download_count=Count('id')
            ).order_by('-download_count')[:10]
            
            # Most collaborated documents
            most_collaborated = documents_qs.annotate(
                collaboration_score=Count('documentcollaborationsession') + 
                                  Count('documentdiscussion') * 2
            ).filter(collaboration_score__gt=0).order_by('-collaboration_score')[:10]
            
            # Get document details
            def get_document_details(doc_activities, score_field):
                results = []
                for item in doc_activities:
                    try:
                        doc = Document.objects.get(id=item['document'])
                        results.append({
                            'id': str(doc.id),
                            'name': doc.name,
                            'score': item[score_field],
                            'file_type': self._get_file_type(doc.name),
                            'created_at': doc.created_at.isoformat()
                        })
                    except Document.DoesNotExist:
                        continue
                return results
            
            return {
                'most_viewed': get_document_details(most_viewed, 'view_count'),
                'most_downloaded': get_document_details(most_downloaded, 'download_count'),
                'most_collaborated': [
                    {
                        'id': str(doc.id),
                        'name': doc.name,
                        'score': doc.collaboration_score,
                        'file_type': self._get_file_type(doc.name),
                        'created_at': doc.created_at.isoformat()
                    }
                    for doc in most_collaborated
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting popular content: {e}")
            return {}
    
    def _get_user_documents(self):
        """Get documents accessible to the current user."""
        if self.user:
            return Document.objects.filter(
                Q(project__egis_project_manager=self.user.username) |
                Q(project__coordinator_id=str(self.user.id)) |
                Q(created_by=self.user) |
                Q(is_public=True)
            ).distinct()
        else:
            return Document.objects.filter(is_public=True)
    
    def _calculate_growth_rate(self, documents_qs, since_date: datetime) -> float:
        """Calculate document growth rate."""
        try:
            total_docs = documents_qs.count()
            recent_docs = documents_qs.filter(created_at__gte=since_date).count()
            
            if total_docs == 0:
                return 0.0
            
            return (recent_docs / total_docs) * 100
            
        except Exception:
            return 0.0
    
    def _analyze_user_engagement(self, activities_qs) -> Dict[str, Any]:
        """Analyze user engagement patterns."""
        try:
            # Calculate engagement metrics
            total_users = activities_qs.values('user').distinct().count()
            total_activities = activities_qs.count()
            
            if total_users == 0:
                return {'avg_activities_per_user': 0, 'engagement_score': 0}
            
            avg_activities = total_activities / total_users
            
            # Engagement score based on activity diversity
            activity_diversity = activities_qs.values('activity_type').distinct().count()
            engagement_score = min(100, (avg_activities * activity_diversity / 10) * 10)
            
            return {
                'avg_activities_per_user': round(avg_activities, 2),
                'engagement_score': round(engagement_score, 1),
                'activity_diversity': activity_diversity
            }
            
        except Exception:
            return {'avg_activities_per_user': 0, 'engagement_score': 0}
    
    def _calculate_storage_efficiency(self, documents_qs) -> Dict[str, Any]:
        """Calculate storage efficiency metrics."""
        try:
            # Basic storage stats
            storage_stats = documents_qs.aggregate(
                total_size=Sum('file_size'),
                avg_size=Avg('file_size'),
                doc_count=Count('id')
            )
            
            # Calculate efficiency score
            total_size = storage_stats['total_size'] or 0
            doc_count = storage_stats['doc_count'] or 0
            
            if doc_count == 0:
                return {'efficiency_score': 100, 'utilization_rate': 0}
            
            # Simple efficiency calculation
            avg_size = storage_stats['avg_size'] or 0
            efficiency_score = min(100, max(0, 100 - (avg_size / (1024 * 1024)) * 2))  # Penalty for large files
            
            return {
                'efficiency_score': round(efficiency_score, 1),
                'utilization_rate': round((doc_count / max(1, total_size / (1024 * 1024))) * 100, 1),
                'avg_file_size_mb': round(avg_size / (1024 * 1024), 2) if avg_size else 0
            }
            
        except Exception:
            return {'efficiency_score': 0, 'utilization_rate': 0}
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def _get_file_type(self, filename: str) -> str:
        """Extract file type from filename."""
        return os.path.splitext(filename)[1].lower().lstrip('.')
    
    def _get_empty_analytics_data(self) -> Dict[str, Any]:
        """Return empty analytics data structure."""
        return {
            'overview': {},
            'activity_trends': {},
            'document_metrics': {},
            'collaboration_insights': {},
            'user_activity': {},
            'performance_metrics': {},
            'search_insights': {},
            'popular_content': {},
            'timeframe_days': 30,
            'generated_at': timezone.now().isoformat(),
            'error': 'Failed to generate analytics'
        }
    
    def export_analytics_report(self, timeframe_days: int = 30, format: str = 'json') -> Dict[str, Any]:
        """
        Export comprehensive analytics report.
        
        Args:
            timeframe_days: Number of days to analyze
            format: Export format ('json', 'csv', 'excel')
            
        Returns:
            Export data and metadata
        """
        try:
            analytics_data = self.get_dashboard_analytics(timeframe_days)
            
            if format == 'json':
                return {
                    'success': True,
                    'data': analytics_data,
                    'format': 'json',
                    'export_time': timezone.now().isoformat()
                }
            elif format == 'csv':
                # Convert to CSV-friendly format
                csv_data = self._convert_to_csv_format(analytics_data)
                return {
                    'success': True,
                    'data': csv_data,
                    'format': 'csv',
                    'export_time': timezone.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': f'Unsupported format: {format}'
                }
                
        except Exception as e:
            logger.error(f"Error exporting analytics report: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _convert_to_csv_format(self, analytics_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert analytics data to CSV-friendly format."""
        csv_rows = []
        
        # Overview metrics
        if 'overview' in analytics_data:
            for key, value in analytics_data['overview'].items():
                csv_rows.append({
                    'category': 'overview',
                    'metric': key,
                    'value': value,
                    'type': 'numeric' if isinstance(value, (int, float)) else 'text'
                })
        
        # Activity trends
        if 'activity_trends' in analytics_data and 'trend_data' in analytics_data['activity_trends']:
            for item in analytics_data['activity_trends']['trend_data']:
                csv_rows.append({
                    'category': 'activity_trend',
                    'metric': 'daily_activity',
                    'date': item['date'],
                    'value': item['count'],
                    'type': 'numeric'
                })
        
        return csv_rows


# Global service instance
def get_document_analytics_service(user: User = None) -> DocumentAnalyticsService:
    """Get document analytics service instance."""
    return DocumentAnalyticsService(user=user)
"""