"""
Analytics Engine service for CLEAR.
"""

import logging
from datetime import timed<PERSON>ta
from typing import Any, Dict
from django.db.models import Sum
from django.utils import timezone
        from ..models import Project, Task
        from ..models.financial import TimeEntry

"""


logger = logging.getLogger(__name__)

class AnalyticsEngine:
    """Analytics engine for generating reports and insights."""
    
    def __init__(self, user):
        self.user = user
    
    def get_project_metrics(self) -> Dict[str, Any]:
        """Get project-related metrics."""
        
        return {
            'total_projects': Project.objects.filter(is_active=True).count(),
            'active_tasks': Task.objects.filter(status='in_progress').count(),
            'completed_tasks': Task.objects.filter(status='completed').count(),
        }
    
    def get_time_metrics(self) -> Dict[str, Any]:
        """Get time tracking metrics."""
        
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        
        return {
            'week_hours': TimeEntry.objects.filter(
                user=self.user,
                start_time__date__gte=week_start
            ).aggregate(total=Sum('duration_minutes'))['total'] or 0,
        }
    
    def generate_report(self, report_type: str) -> Dict[str, Any]:
        """Generate a specific type of report."""
        if report_type == 'project':
            return self.get_project_metrics()
        elif report_type == 'time':
            return self.get_time_metrics()
        else:
            return {}

"""