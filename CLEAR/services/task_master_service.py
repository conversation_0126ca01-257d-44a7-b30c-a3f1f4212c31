"""
CLEAR TaskMaster Service - AI-Powered Task Management Integration
Based on Claude Task Master for intelligent project planning and task management
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from ..models.auth import User
from ..models.projects import Project, ProjectActivity, ProjectPhase, Task
from .ai_provider_service import AIProviderService

"""




logger = logging.getLogger(__name__)

User = get_user_model()


class TaskMasterService:
    """
    AI-powered task management service inspired by Claude Task Master
    
    Provides intelligent task generation, complexity analysis, and project planning
    capabilities while maintaining CLEAR's Django + HTMX architecture.
    """
    
    def __init__(self):
        self.ai_provider = AIProviderService()
        self.complexity_factors = {
            'spatial_analysis': 1.5,
            'utility_coordination': 1.3,
            'regulatory_compliance': 1.4,
            'stakeholder_management': 1.2,
            'cad_processing': 1.6,
            'conflict_resolution': 1.8,
            'document_management': 1.1,
            'basic_administrative': 0.8
        }
    
    def generate_tasks_from_requirements(
        self, 
        project: Project, 
        requirements_text: str, 
        user: User,
        ai_provider: str = 'anthropic'
    ) -> Dict[str, Any]:
        """
        Generate project tasks from requirements document using AI analysis
        
        Args:
            project: CLEAR Project instance
            requirements_text: Requirements or PRD text
            user: User creating the tasks
            ai_provider: AI provider to use for analysis
            
        Returns:
            Dictionary with generated tasks and analysis
        """
        try:
            # Create AI prompt for task generation
            prompt = self._build_task_generation_prompt(project, requirements_text)
            
            # Get AI analysis
            ai_response = self.ai_provider.generate_response(
                prompt=prompt,
                provider=ai_provider,
                max_tokens=4000
            )
            
            # Parse AI response
            parsed_tasks = self._parse_ai_task_response(ai_response)
            
            # Create tasks in database
            created_tasks = []
            with transaction.atomic():
                for task_data in parsed_tasks.get('tasks', []):
                    task = self._create_task_from_ai_data(
                        project=project,
                        task_data=task_data,
                        user=user
                    )
                    created_tasks.append(task)
                
                # Log activity
                ProjectActivity.objects.create(
                    project=project,
                    user=user,
                    action_type='task_created',
                    description=f"Generated {len(created_tasks)} tasks from requirements using AI",
                    metadata={'ai_provider': ai_provider, 'source': 'requirements_analysis'}
                )
            
            return {
                'success': True,
                'tasks': created_tasks,
                'analysis': parsed_tasks.get('analysis', {}),
                'recommendations': parsed_tasks.get('recommendations', [])
            }
            
        except Exception as e:
            logger.error(f"Error generating tasks from requirements: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'tasks': []
            }
    
    def analyze_task_complexity(
        self, 
        task: Task, 
        project_context: Optional[Dict] = None,
        ai_provider: str = 'anthropic'
    ) -> Dict[str, Any]:
        """
        Analyze task complexity using AI and CLEAR-specific factors
        
        Args:
            task: Task instance to analyze
            project_context: Additional project context
            ai_provider: AI provider for analysis
            
        Returns:
            Dictionary with complexity analysis and recommendations
        """
        try:
            # Build context for analysis
            context = self._build_task_context(task, project_context)
            
            # Create AI prompt for complexity analysis
            prompt = self._build_complexity_analysis_prompt(task, context)
            
            # Get AI analysis
            ai_response = self.ai_provider.generate_response(
                prompt=prompt,
                provider=ai_provider,
                max_tokens=2000
            )
            
            # Parse AI response
            complexity_analysis = self._parse_complexity_response(ai_response)
            
            # Apply CLEAR-specific complexity factors
            adjusted_complexity = self._apply_clear_complexity_factors(
                complexity_analysis, 
                task
            )
            
            # Update task with complexity data
            task.estimated_hours = adjusted_complexity.get('estimated_hours')
            task.tags = list(set(task.tags + adjusted_complexity.get('complexity_tags', [])))
            task.save()
            
            return {
                'success': True,
                'complexity_score': adjusted_complexity.get('complexity_score'),
                'estimated_hours': adjusted_complexity.get('estimated_hours'),
                'risk_factors': adjusted_complexity.get('risk_factors', []),
                'recommendations': adjusted_complexity.get('recommendations', []),
                'dependencies': adjusted_complexity.get('suggested_dependencies', [])
            }
            
        except Exception as e:
            logger.error(f"Error analyzing task complexity: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def research_technical_solution(
        self, 
        query: str, 
        project: Project,
        context_type: str = 'general',
        ai_provider: str = 'anthropic'
    ) -> Dict[str, Any]:
        """
        Perform AI-powered research for technical solutions
        
        Args:
            query: Research query
            project: Project context
            context_type: Type of research (spatial, regulatory, technical, etc.)
            ai_provider: AI provider for research
            
        Returns:
            Dictionary with research results and recommendations
        """
        try:
            # Build research prompt with CLEAR context
            prompt = self._build_research_prompt(query, project, context_type)
            
            # Get AI research response
            ai_response = self.ai_provider.generate_response(
                prompt=prompt,
                provider=ai_provider,
                max_tokens=3000
            )
            
            # Parse research response
            research_results = self._parse_research_response(ai_response)
            
            # Log research activity
            ProjectActivity.objects.create(
                project=project,
                user=project.team_members.filter(role='manager').first().user if project.team_members.exists() else None,
                action_type='research_conducted',
                description=f"AI research conducted: {query[:100]}...",
                metadata={
                    'query': query,
                    'context_type': context_type,
                    'ai_provider': ai_provider
                }
            )
            
            return {
                'success': True,
                'query': query,
                'results': research_results.get('results', []),
                'recommendations': research_results.get('recommendations', []),
                'sources': research_results.get('sources', []),
                'next_steps': research_results.get('next_steps', [])
            }
            
        except Exception as e:
            logger.error(f"Error conducting research: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def suggest_next_tasks(
        self, 
        project: Project, 
        current_progress: Optional[Dict] = None,
        ai_provider: str = 'anthropic'
    ) -> Dict[str, Any]:
        """
        AI-recommended next tasks based on project state and progress
        
        Args:
            project: Project instance
            current_progress: Current project progress data
            ai_provider: AI provider for recommendations
            
        Returns:
            Dictionary with suggested tasks and prioritization
        """
        try:
            # Analyze current project state
            project_state = self._analyze_project_state(project)
            
            # Build suggestion prompt
            prompt = self._build_task_suggestion_prompt(project, project_state, current_progress)
            
            # Get AI suggestions
            ai_response = self.ai_provider.generate_response(
                prompt=prompt,
                provider=ai_provider,
                max_tokens=2500
            )
            
            # Parse suggestions
            suggestions = self._parse_task_suggestions(ai_response)
            
            return {
                'success': True,
                'suggested_tasks': suggestions.get('tasks', []),
                'prioritization': suggestions.get('prioritization', {}),
                'reasoning': suggestions.get('reasoning', ''),
                'project_state': project_state
            }
            
        except Exception as e:
            logger.error(f"Error suggesting next tasks: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def break_down_complex_task(
        self, 
        task: Task, 
        breakdown_level: int = 3,
        ai_provider: str = 'anthropic'
    ) -> Dict[str, Any]:
        """
        Break down complex tasks into manageable subtasks
        
        Args:
            task: Task to break down
            breakdown_level: Level of breakdown (1-5)
            ai_provider: AI provider for analysis
            
        Returns:
            Dictionary with subtasks and breakdown analysis
        """
        try:
            # Build breakdown prompt
            prompt = self._build_task_breakdown_prompt(task, breakdown_level)
            
            # Get AI breakdown
            ai_response = self.ai_provider.generate_response(
                prompt=prompt,
                provider=ai_provider,
                max_tokens=3000
            )
            
            # Parse breakdown response
            breakdown = self._parse_task_breakdown(ai_response)
            
            # Create subtasks
            subtasks = []
            with transaction.atomic():
                for subtask_data in breakdown.get('subtasks', []):
                    subtask = Task.objects.create(
                        project=task.project,
                        title=subtask_data.get('title'),
                        description=subtask_data.get('description'),
                        priority=subtask_data.get('priority', 'medium'),
                        assigned_to=task.assigned_to,
                        created_by=task.created_by,
                        estimated_hours=subtask_data.get('estimated_hours'),
                        tags=subtask_data.get('tags', []),
                        due_date=self._calculate_subtask_due_date(task, subtask_data)
                    )
                    
                    # Add dependency to parent task
                    subtask.dependencies.add(task)
                    subtasks.append(subtask)
                
                # Update parent task status
                task.status = 'in_progress'
                task.save()
            
            return {
                'success': True,
                'parent_task': task,
                'subtasks': subtasks,
                'breakdown_analysis': breakdown.get('analysis', {}),
                'recommendations': breakdown.get('recommendations', [])
            }
            
        except Exception as e:
            logger.error(f"Error breaking down task: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_project_phases(
        self, 
        project: Project, 
        requirements: str,
        ai_provider: str = 'anthropic'
    ) -> Dict[str, Any]:
        """
        Generate project phases based on requirements and CLEAR workflows
        
        Args:
            project: Project instance
            requirements: Project requirements
            ai_provider: AI provider for analysis
            
        Returns:
            Dictionary with generated phases and timeline
        """
        try:
            # Build phase generation prompt
            prompt = self._build_phase_generation_prompt(project, requirements)
            
            # Get AI phase analysis
            ai_response = self.ai_provider.generate_response(
                prompt=prompt,
                provider=ai_provider,
                max_tokens=3500
            )
            
            # Parse phase response
            phases_data = self._parse_phase_response(ai_response)
            
            # Create project phases
            created_phases = []
            with transaction.atomic():
                for i, phase_data in enumerate(phases_data.get('phases', [])):
                    phase = ProjectPhase.objects.create(
                        project=project,
                        name=phase_data.get('name'),
                        description=phase_data.get('description'),
                        order=i + 1,
                        start_date=phase_data.get('start_date'),
                        end_date=phase_data.get('end_date'),
                        deliverables=phase_data.get('deliverables', []),
                        milestones=phase_data.get('milestones', [])
                    )
                    created_phases.append(phase)
            
            return {
                'success': True,
                'phases': created_phases,
                'timeline': phases_data.get('timeline', {}),
                'recommendations': phases_data.get('recommendations', [])
            }
            
        except Exception as e:
            logger.error(f"Error generating project phases: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    # Private helper methods
    
    def _build_task_generation_prompt(self, project: Project, requirements: str) -> str:
        """Build AI prompt for task generation"""
        return f"""
        You are an expert project manager specializing in utility coordination and infrastructure projects.
        
        Project Context:
        - Name: {project.name}
        - Client: {project.client}
        - Type: {project.work_type or 'Utility Coordination'}
        - Description: {project.description or 'No description provided'}
        
        Requirements:
        {requirements}
        
        Please generate a comprehensive task breakdown for this project. Focus on:
        1. Utility coordination tasks
        2. Spatial analysis requirements
        3. Stakeholder management
        4. Document management
        5. Regulatory compliance
        6. Conflict detection and resolution
        
        For each task, provide:
        - Title (concise and actionable)
        - Description (detailed requirements)
        - Priority (low, medium, high, urgent)
        - Estimated hours
        - Tags (for categorization)
        - Suggested dependencies
        
        Return the response as a JSON object with:
        {{
            "tasks": [
                {{
                    "title": "Task Title",
                    "description": "Detailed description",
                    "priority": "high",
                    "estimated_hours": 8,
                    "tags": ["spatial", "analysis"],
                    "dependencies": ["Task Title 1", "Task Title 2"]
                }}
            ],
            "analysis": {{
                "total_estimated_hours": 120,
                "complexity_score": 7,
                "risk_factors": ["High stakeholder count", "Complex spatial requirements"]
            }},
            "recommendations": ["Recommendation 1", "Recommendation 2"]
        }}
        """
    
    def _build_complexity_analysis_prompt(self, task: Task, context: Dict) -> str:
        """Build AI prompt for complexity analysis"""
        return f"""
        Analyze the complexity of this utility coordination task:
        
        Task: {task.title}
        Description: {task.description or 'No description'}
        Project: {task.project.name} ({task.project.work_type or 'Utility Coordination'})
        
        Context:
        {json.dumps(context, indent=2)}
        
        Please analyze complexity considering:
        1. Spatial analysis requirements
        2. Number of utilities involved
        3. Regulatory compliance needs
        4. Stakeholder coordination complexity
        5. Technical skill requirements
        6. Risk factors
        
        Provide complexity score (1-10), estimated hours, risk factors, and recommendations.
        
        Return as JSON:
        {{
            "complexity_score": 7,
            "estimated_hours": 16,
            "risk_factors": ["Complex spatial analysis", "Multiple stakeholders"],
            "recommendations": ["Break into subtasks", "Assign senior coordinator"],
            "complexity_tags": ["high-complexity", "spatial-analysis"]
        }}
        """
    
    def _build_research_prompt(self, query: str, project: Project, context_type: str) -> str:
        """Build AI prompt for research"""
        context_info = {
            'spatial': 'Focus on GIS, spatial analysis, and mapping technologies',
            'regulatory': 'Focus on utility regulations, permits, and compliance',
            'technical': 'Focus on technical implementation and best practices',
            'stakeholder': 'Focus on stakeholder management and communication'
        }
        
        return f"""
        Conduct research for a utility coordination project:
        
        Query: {query}
        Project: {project.name} ({project.client})
        Context: {context_info.get(context_type, 'General research')}
        
        Please provide:
        1. Research findings and insights
        2. Best practices and recommendations
        3. Potential challenges and solutions
        4. Relevant tools and technologies
        5. Next steps and action items
        
        Return as JSON:
        {{
            "results": ["Finding 1", "Finding 2"],
            "recommendations": ["Recommendation 1", "Recommendation 2"],
            "sources": ["Source 1", "Source 2"],
            "next_steps": ["Step 1", "Step 2"]
        }}
        """
    
    def _parse_ai_task_response(self, response: str) -> Dict:
        """Parse AI response for task generation"""
        try:
            # Try to extract JSON from response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_str = response[json_start:json_end].strip()
            else:
                json_str = response.strip()
            
            return json.loads(json_str)
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return {'tasks': [], 'analysis': {}, 'recommendations': []}
    
    def _create_task_from_ai_data(self, project: Project, task_data: Dict, user: User) -> Task:
        """Create Task instance from AI-generated data"""
        # Calculate due date based on estimated hours
        due_date = None
        if task_data.get('estimated_hours'):
            due_date = timezone.now() + timedelta(
                days=int(task_data['estimated_hours'] / 8) + 1
            )
        
        return Task.objects.create(
            project=project,
            title=task_data.get('title', 'Untitled Task'),
            description=task_data.get('description', ''),
            priority=task_data.get('priority', 'medium'),
            created_by=user,
            estimated_hours=task_data.get('estimated_hours'),
            due_date=due_date,
            tags=task_data.get('tags', [])
        )
    
    def _apply_clear_complexity_factors(self, analysis: Dict, task: Task) -> Dict:
        """Apply CLEAR-specific complexity factors"""
        base_complexity = analysis.get('complexity_score', 5)
        base_hours = analysis.get('estimated_hours', 8)
        
        # Apply factors based on task tags and description
        multiplier = 1.0
        for factor_type, factor_value in self.complexity_factors.items():
            if factor_type in str(task.description).lower() or factor_type in task.tags:
                multiplier *= factor_value
        
        # Apply project-specific factors
        if task.project.work_type:
            if 'complex' in task.project.work_type.lower():
                multiplier *= 1.2
        
        adjusted_hours = int(base_hours * multiplier)
        adjusted_complexity = min(10, int(base_complexity * multiplier))
        
        analysis['complexity_score'] = adjusted_complexity
        analysis['estimated_hours'] = adjusted_hours
        analysis['complexity_multiplier'] = multiplier
        
        return analysis
    
    def _analyze_project_state(self, project: Project) -> Dict:
        """Analyze current project state for task suggestions"""
        tasks = project.tasks.all()
        
        return {
            'total_tasks': tasks.count(),
            'completed_tasks': tasks.filter(status='completed').count(),
            'in_progress_tasks': tasks.filter(status='in_progress').count(),
            'overdue_tasks': tasks.filter(
                due_date__lt=timezone.now(),
                status__in=['pending', 'in_progress']
            ).count(),
            'team_size': project.team_members.filter(is_active=True).count(),
            'project_age_days': (timezone.now().date() - project.created_at.date()).days,
            'recent_activity': project.project_activities.count(),
            'phases': list(project.phases.values('name', 'status')),
            'utilities_count': project.utilities.count() if hasattr(project, 'utilities') else 0,
            'conflicts_count': project.conflicts.count() if hasattr(project, 'conflicts') else 0
        }
    
    def _build_task_context(self, task: Task, additional_context: Optional[Dict] = None) -> Dict:
        """Build comprehensive context for task analysis"""
        context = {
            'project_name': task.project.name,
            'project_type': task.project.work_type,
            'project_client': task.project.client,
            'task_tags': task.tags,
            'project_team_size': task.project.team_members.count(),
            'project_duration': (task.project.end_date - task.project.start_date).days if task.project.end_date and task.project.start_date else None
        }
        
        if additional_context:
            context.update(additional_context)
        
        return context
    
    def _parse_complexity_response(self, response: str) -> Dict:
        """Parse AI complexity analysis response"""
        try:
            return self._parse_ai_task_response(response)
        except Exception:
            return {'complexity_score': 5, 'estimated_hours': 8}
    
    def _parse_research_response(self, response: str) -> Dict:
        """Parse AI research response"""
        try:
            return self._parse_ai_task_response(response)
        except Exception:
            return {'results': [], 'recommendations': []}
    
    def _parse_task_suggestions(self, response: str) -> Dict:
        """Parse AI task suggestions response"""
        try:
            return self._parse_ai_task_response(response)
        except Exception:
            return {'tasks': [], 'prioritization': {}}
    
    def _parse_task_breakdown(self, response: str) -> Dict:
        """Parse AI task breakdown response"""
        try:
            return self._parse_ai_task_response(response)
        except Exception:
            return {'subtasks': [], 'analysis': {}}
    
    def _parse_phase_response(self, response: str) -> Dict:
        """Parse AI phase generation response"""
        try:
            return self._parse_ai_task_response(response)
        except Exception:
            return {'phases': [], 'timeline': {}}
    
    def _calculate_subtask_due_date(self, parent_task: Task, subtask_data: Dict) -> Optional[datetime]:
        """Calculate due date for subtask based on parent task"""
        if parent_task.due_date and subtask_data.get('estimated_hours'):
            # Distribute subtasks across parent task timeline
            return parent_task.due_date - timedelta(
                days=max(1, int(subtask_data['estimated_hours'] / 8))
            )
        return None
    
    def _build_task_suggestion_prompt(self, project: Project, state: Dict, progress: Optional[Dict]) -> str:
        """Build prompt for task suggestions"""
        return f"""
        Based on the current project state, suggest the next most important tasks.
        
        Project: {project.name}
        State: {json.dumps(state, indent=2)}
        Progress: {json.dumps(progress or {}, indent=2)}
        
        Consider:
        1. Critical path dependencies
        2. Resource availability
        3. Project deadlines
        4. Risk mitigation
        5. Stakeholder priorities
        
        Suggest 3-5 high-priority next tasks with reasoning.
        
        Return as JSON with tasks and prioritization logic.
        """
    
    def _build_task_breakdown_prompt(self, task: Task, level: int) -> str:
        """Build prompt for task breakdown"""
        return f"""
        Break down this complex task into {level} manageable subtasks:
        
        Task: {task.title}
        Description: {task.description}
        Project: {task.project.name}
        
        Create subtasks that are:
        1. Specific and actionable
        2. Properly sequenced
        3. Appropriately sized (4-16 hours each)
        4. Clearly defined deliverables
        
        Return as JSON with subtasks array and analysis.
        """
    
    def _build_phase_generation_prompt(self, project: Project, requirements: str) -> str:
        """Build prompt for phase generation"""
        return f"""
        Generate project phases for this utility coordination project:
        
        Project: {project.name}
        Client: {project.client}
        Requirements: {requirements}
        
        Create 4-6 logical phases covering:
        1. Planning and setup
        2. Data collection and analysis
        3. Stakeholder coordination
        4. Implementation
        5. Testing and validation
        6. Delivery and closure
        
        Return as JSON with phases, timeline, and recommendations.
        """
"""