"""
Entity Linking Service

Advanced entity linking and semantic analysis for AI Communication Intelligence.
Handles complex entity relationships, semantic parsing, and knowledge graph operations.
"""

import logging
import re
from collections import Counter
from typing import Any, Dict, List, Optional
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from ..models import (
            from ..models import Utility
        from ..models import User

"""



    Conflict,
    Document,
    EntityMention,
    EntityRelationship,
    KnowledgeGraphNode,
    Note,
    Project,
    Stakeholder,
    Task,
    User,
    Utility,
)

logger = logging.getLogger(__name__)


class EntityLinkingService:
    """Advanced entity linking and semantic analysis service"""
    
    def __init__(self):
        # Enhanced entity patterns with variations
        self.entity_patterns = {
            'project': [
                r'@project[s]?[-_]?(\w+)',
                r'project\s+(\w+)',
                r'proj[s]?\s+(\w+)'
            ],
            'task': [
                r'@task[s]?[-_]?(\w+)',
                r'task\s+(\w+)',
                r'ticket[s]?\s+(\w+)'
            ],
            'utility': [
                r'@utility[-_]?(\w+)',
                r'@util[-_]?(\w+)',
                r'utility\s+(\w+)',
                r'util\s+(\w+)'
            ],
            'conflict': [
                r'@conflict[s]?[-_]?(\w+)',
                r'@conflictid[-_]?(\w+)',
                r'conflict\s+(\w+)',
                r'issue\s+(\w+)'
            ],
            'stakeholder': [
                r'@stakeholder[s]?[-_]?(\w+)',
                r'@company[-_]?(\w+)',
                r'stakeholder\s+(\w+)'
            ],
            'user': [
                r'@user[-_]?(\w+)',
                r'@(\w+)(?:\s|$)',  # Simple @username
                r'user\s+(\w+)'
            ],
            'document': [
                r'@doc[s]?[-_]?(\w+)',
                r'@document[s]?[-_]?(\w+)',
                r'document\s+(\w+)',
                r'file\s+(\w+)'
            ],
            'note': [
                r'@note[s]?[-_]?(\w+)',
                r'note\s+(\w+)',
                r'memo\s+(\w+)'
            ]
        }
        
        # Relationship inference patterns
        self.relationship_patterns = {
            'depends_on': [
                r'depends\s+on',
                r'requires',
                r'needs',
                r'blocked\s+by',
                r'waiting\s+for'
            ],
            'conflicts_with': [
                r'conflicts?\s+with',
                r'interferes?\s+with',
                r'blocks?',
                r'prevents?'
            ],
            'assigned_to': [
                r'assigned\s+to',
                r'belongs\s+to',
                r'owned\s+by',
                r'responsible[:]\s*'
            ],
            'related_to': [
                r'related\s+to',
                r'connected\s+to',
                r'similar\s+to',
                r'linked\s+to'
            ],
            'coordinates_with': [
                r'coordinates?\s+with',
                r'works?\s+with',
                r'collaborates?\s+with'
            ]
        }
    
    # ========================================
    # ADVANCED ENTITY DETECTION
    # ========================================
    
    def detect_entities_with_context(self, text: str, content_object: Any, user: User, 
                                   confidence_threshold: float = 0.7) -> List[EntityMention]:
        """Advanced entity detection with context analysis and confidence scoring"""
        mentions = []
        text.lower()
        
        try:
            for entity_type, patterns in self.entity_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, text, re.IGNORECASE)
                    
                    for match in matches:
                        entity_id = match.group(1)
                        mention_text = match.group(0)
                        start_pos = match.start()
                        end_pos = match.end()
                        
                        # Calculate confidence based on pattern specificity and context
                        confidence = self._calculate_mention_confidence(
                            pattern, mention_text, text, start_pos, end_pos, entity_type
                        )
                        
                        if confidence >= confidence_threshold:
                            # Validate entity exists
                            if self._validate_entity_exists(entity_type, entity_id):
                                # Extract rich context
                                context_data = self._extract_rich_context(text, start_pos, end_pos)
                                
                                # Determine sentiment
                                sentiment = self._analyze_sentiment(context_data['full_context'])
                                
                                # Create entity mention
                                mention = self._create_entity_mention(
                                    content_object=content_object,
                                    entity_type=entity_type,
                                    entity_id=entity_id,
                                    mention_text=mention_text,
                                    start_pos=start_pos,
                                    end_pos=end_pos,
                                    context_data=context_data,
                                    confidence=confidence,
                                    sentiment=sentiment,
                                    user=user
                                )
                                
                                if mention:
                                    mentions.append(mention)
            
            # Post-process mentions for relationships and validation
            self._post_process_mentions(mentions, text, user)
            
            return mentions
            
        except Exception as e:
            logger.error(f"Error in advanced entity detection: {str(e)}")
            return []
    
    def _calculate_mention_confidence(self, pattern: str, mention_text: str, 
                                    full_text: str, start_pos: int, end_pos: int, 
                                    entity_type: str) -> float:
        """Calculate confidence score for entity mention based on various factors"""
        confidence = 0.5  # Base confidence
        
        # Pattern specificity bonus
        if pattern.startswith('@'):
            confidence += 0.3  # Explicit @ mentions are more confident
        
        # Context analysis
        context_before = full_text[max(0, start_pos - 20):start_pos].lower()
        context_after = full_text[end_pos:min(len(full_text), end_pos + 20)].lower()
        
        # Boost confidence for context keywords
        context_keywords = {
            'project': ['project', 'proj', 'initiative'],
            'task': ['task', 'todo', 'ticket', 'item'],
            'utility': ['utility', 'line', 'service', 'infrastructure'],
            'conflict': ['conflict', 'issue', 'problem', 'clash'],
            'stakeholder': ['company', 'stakeholder', 'client', 'vendor'],
            'user': ['user', 'person', 'team', 'member'],
            'document': ['document', 'file', 'doc', 'report'],
            'note': ['note', 'memo', 'comment']
        }
        
        relevant_keywords = context_keywords.get(entity_type, [])
        for keyword in relevant_keywords:
            if keyword in context_before or keyword in context_after:
                confidence += 0.1
                break
        
        # Length and format validation
        if len(mention_text) > 3:  # Reasonable length
            confidence += 0.1
        
        # Penalty for common false positives
        false_positive_patterns = ['@media', '@import', '@charset']
        if any(fp in mention_text.lower() for fp in false_positive_patterns):
            confidence -= 0.5
        
        return min(1.0, max(0.0, confidence))
    
    def _extract_rich_context(self, text: str, start_pos: int, end_pos: int) -> Dict[str, str]:
        """Extract rich contextual information around entity mention"""
        # Extended context for analysis
        extended_start = max(0, start_pos - 100)
        extended_end = min(len(text), end_pos + 100)
        
        # Immediate context
        immediate_start = max(0, start_pos - 30)
        immediate_end = min(len(text), end_pos + 30)
        
        # Sentence context
        sentence_start = self._find_sentence_boundary(text, start_pos, direction='backward')
        sentence_end = self._find_sentence_boundary(text, end_pos, direction='forward')
        
        return {
            'context_before': text[immediate_start:start_pos].strip(),
            'context_after': text[end_pos:immediate_end].strip(),
            'extended_context': text[extended_start:extended_end].strip(),
            'sentence_context': text[sentence_start:sentence_end].strip(),
            'full_context': text[immediate_start:immediate_end].strip()
        }
    
    def _find_sentence_boundary(self, text: str, pos: int, direction: str = 'forward') -> int:
        """Find sentence boundary in specified direction"""
        sentence_endings = '.!?'
        
        if direction == 'backward':
            for i in range(pos - 1, -1, -1):
                if text[i] in sentence_endings:
                    return i + 1
            return 0
        else:  # forward
            for i in range(pos, len(text)):
                if text[i] in sentence_endings:
                    return i + 1
            return len(text)
    
    def _analyze_sentiment(self, context: str) -> str:
        """Simple sentiment analysis of mention context"""
        positive_words = ['good', 'great', 'excellent', 'success', 'complete', 'resolved', 'working', 'progress']
        negative_words = ['bad', 'issue', 'problem', 'error', 'failed', 'blocked', 'conflict', 'delay']
        
        context_lower = context.lower()
        positive_count = sum(1 for word in positive_words if word in context_lower)
        negative_count = sum(1 for word in negative_words if word in context_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _create_entity_mention(self, content_object: Any, entity_type: str, entity_id: str,
                             mention_text: str, start_pos: int, end_pos: int,
                             context_data: Dict[str, str], confidence: float,
                             sentiment: str, user: User) -> Optional[EntityMention]:
        """Create or update entity mention with rich context data"""
        try:
            mention, created = EntityMention.objects.get_or_create(
                content_type=ContentType.objects.get_for_model(content_object),
                object_id=str(content_object.id),
                mention_text=mention_text,
                position_start=start_pos,
                defaults={
                    'mentioned_entity_type': entity_type,
                    'mentioned_entity_id': entity_id,
                    'position_end': end_pos,
                    'context_before': context_data['context_before'],
                    'context_after': context_data['context_after'],
                    'confidence': confidence,
                    'sentiment': sentiment,
                    'detected_by': user,
                }
            )
            
            return mention if created else None
            
        except Exception as e:
            logger.error(f"Error creating entity mention: {str(e)}")
            return None
    
    # ========================================
    # RELATIONSHIP ANALYSIS
    # ========================================
    
    def analyze_entity_relationships(self, mentions: List[EntityMention], 
                                   context_text: str, user: User) -> List[EntityRelationship]:
        """Analyze and create relationships between mentioned entities"""
        relationships = []
        
        try:
            # Analyze pairwise relationships
            for i, mention1 in enumerate(mentions):
                for mention2 in mentions[i+1:]:
                    relationship_data = self._infer_relationship(
                        mention1, mention2, context_text
                    )
                    
                    if relationship_data:
                        relationship = self._create_or_update_relationship(
                            mention1, mention2, relationship_data, user
                        )
                        if relationship:
                            relationships.append(relationship)
            
            # Analyze temporal and spatial relationships
            self._analyze_temporal_relationships(mentions, context_text, user)
            self._analyze_spatial_relationships(mentions, user)
            
            return relationships
            
        except Exception as e:
            logger.error(f"Error analyzing entity relationships: {str(e)}")
            return []
    
    def _infer_relationship(self, mention1: EntityMention, mention2: EntityMention, 
                          context_text: str) -> Optional[Dict[str, Any]]:
        """Infer relationship type between two entity mentions"""
        # Get context around both mentions
        start_pos = min(mention1.position_start, mention2.position_start)
        end_pos = max(mention1.position_end, mention2.position_end)
        
        # Expand context for relationship analysis
        context_start = max(0, start_pos - 50)
        context_end = min(len(context_text), end_pos + 50)
        relationship_context = context_text[context_start:context_end].lower()
        
        # Check for explicit relationship patterns
        for rel_type, patterns in self.relationship_patterns.items():
            for pattern in patterns:
                if re.search(pattern, relationship_context, re.IGNORECASE):
                    return {
                        'type': rel_type,
                        'strength': 0.8,
                        'confidence': 0.9,
                        'evidence': pattern,
                        'context': relationship_context
                    }
        
        # Infer implicit relationships based on entity types
        implicit_rel = self._infer_implicit_relationship(
            mention1.mentioned_entity_type, mention2.mentioned_entity_type
        )
        
        if implicit_rel:
            return {
                'type': implicit_rel,
                'strength': 0.5,
                'confidence': 0.7,
                'evidence': 'co-mention',
                'context': relationship_context
            }
        
        return None
    
    def _infer_implicit_relationship(self, entity_type1: str, entity_type2: str) -> Optional[str]:
        """Infer implicit relationships based on entity types"""
        # Common relationship patterns between entity types
        type_relationships = {
            ('task', 'project'): 'related_to',
            ('task', 'user'): 'assigned_to',
            ('conflict', 'utility'): 'conflicts_with',
            ('stakeholder', 'project'): 'coordinates_with',
            ('document', 'project'): 'related_to',
            ('note', 'task'): 'related_to',
            ('utility', 'utility'): 'spatial_proximity',
        }
        
        # Check both directions
        rel = type_relationships.get((entity_type1, entity_type2))
        if not rel:
            rel = type_relationships.get((entity_type2, entity_type1))
        
        return rel
    
    def _create_or_update_relationship(self, mention1: EntityMention, mention2: EntityMention,
                                     relationship_data: Dict[str, Any], user: User) -> Optional[EntityRelationship]:
        """Create or update entity relationship"""
        try:
            relationship, created = EntityRelationship.objects.get_or_create(
                source_entity_type=mention1.mentioned_entity_type,
                source_entity_id=mention1.mentioned_entity_id,
                target_entity_type=mention2.mentioned_entity_type,
                target_entity_id=mention2.mentioned_entity_id,
                relationship_type=relationship_data['type'],
                defaults={
                    'strength': relationship_data['strength'],
                    'confidence': relationship_data['confidence'],
                    'evidence_count': 1,
                    'discovered_through': 'communication_analysis',
                    'discovered_by': user,
                }
            )
            
            if not created:
                # Update existing relationship
                relationship.evidence_count += 1
                relationship.strength = min(1.0, relationship.strength + 0.1)
                relationship.confidence = max(relationship.confidence, relationship_data['confidence'])
                relationship.last_confirmed_at = timezone.now()
                relationship.save()
            
            # Add supporting evidence
            relationship.supporting_communications.add(mention1, mention2)
            
            return relationship
            
        except Exception as e:
            logger.error(f"Error creating relationship: {str(e)}")
            return None
    
    def _analyze_temporal_relationships(self, mentions: List[EntityMention], 
                                      context_text: str, user: User):
        """Analyze temporal relationships between entities"""
        temporal_keywords = ['before', 'after', 'then', 'next', 'following', 'previous', 'later', 'earlier']
        
        for keyword in temporal_keywords:
            pattern = rf'\b{keyword}\b'
            matches = list(re.finditer(pattern, context_text, re.IGNORECASE))
            
            for match in matches:
                # Find mentions before and after temporal keyword
                before_mentions = [m for m in mentions if m.position_end < match.start()]
                after_mentions = [m for m in mentions if m.position_start > match.end()]
                
                # Create temporal relationships
                for before_mention in before_mentions[-2:]:  # Last 2 mentions before
                    for after_mention in after_mentions[:2]:  # First 2 mentions after
                        self._create_or_update_relationship(
                            before_mention, after_mention,
                            {
                                'type': 'temporal_sequence',
                                'strength': 0.6,
                                'confidence': 0.8,
                                'evidence': keyword
                            },
                            user
                        )
    
    def _analyze_spatial_relationships(self, mentions: List[EntityMention], user: User):
        """Analyze spatial relationships between entities with geographic data"""
        try:
            # Get utility entities with spatial data
            utility_mentions = [m for m in mentions if m.mentioned_entity_type == 'utility']
            
            for i, mention1 in enumerate(utility_mentions):
                for mention2 in utility_mentions[i+1:]:
                    # Check if utilities have spatial proximity
                    if self._check_spatial_proximity(mention1.mentioned_entity_id, mention2.mentioned_entity_id):
                        self._create_or_update_relationship(
                            mention1, mention2,
                            {
                                'type': 'spatial_proximity',
                                'strength': 0.7,
                                'confidence': 0.9,
                                'evidence': 'geographic_analysis'
                            },
                            user
                        )
        
        except Exception as e:
            logger.error(f"Error analyzing spatial relationships: {str(e)}")
    
    def _check_spatial_proximity(self, utility_id1: str, utility_id2: str, 
                               distance_threshold: float = 100.0) -> bool:
        """Check if two utilities are spatially proximate"""
        try:
            
            utility1 = Utility.objects.get(id=utility_id1)
            utility2 = Utility.objects.get(id=utility_id2)
            
            if utility1.geometry and utility2.geometry:
                distance = utility1.geometry.distance(utility2.geometry)
                return distance <= distance_threshold
            
            return False
            
        except Exception:
            return False
    
    # ========================================
    # KNOWLEDGE GRAPH OPERATIONS
    # ========================================
    
    def update_knowledge_graph_advanced(self, mentions: List[EntityMention], 
                                       relationships: List[EntityRelationship],
                                       user: User) -> List[KnowledgeGraphNode]:
        """Advanced knowledge graph updates with centrality analysis"""
        updated_nodes = []
        
        try:
            # Update nodes for each mention
            for mention in mentions:
                node = self._update_knowledge_node(mention, user)
                if node:
                    updated_nodes.append(node)
            
            # Update node connections based on relationships
            for relationship in relationships:
                self._update_node_connections(relationship)
            
            # Recalculate graph metrics
            self._recalculate_graph_metrics(updated_nodes)
            
            return updated_nodes
            
        except Exception as e:
            logger.error(f"Error updating knowledge graph: {str(e)}")
            return []
    
    def _update_knowledge_node(self, mention: EntityMention, user: User) -> Optional[KnowledgeGraphNode]:
        """Update or create knowledge graph node for entity mention"""
        try:
            node, created = KnowledgeGraphNode.objects.get_or_create(
                entity_type=mention.mentioned_entity_type,
                entity_id=mention.mentioned_entity_id,
                defaults={
                    'entity_name': self._get_entity_name(mention),
                    'first_mentioned': mention.detected_at,
                    'importance_score': 0.1,
                    'centrality_score': 0.0,
                    'activity_level': 1.0,
                }
            )
            
            # Update node statistics
            node.total_mentions += 1
            node.last_activity = mention.detected_at
            
            # Update activity level (decay over time, boost with recent activity)
            time_diff = timezone.now() - node.last_activity
            decay_factor = max(0.1, 1.0 - (time_diff.days / 30.0))
            node.activity_level = min(1.0, node.activity_level * decay_factor + 0.1)
            
            # Update importance score based on mention frequency and context
            mention_boost = 0.01 * (1 + mention.confidence)
            node.importance_score = min(1.0, node.importance_score + mention_boost)
            
            # Extract and update keywords
            context_keywords = self._extract_context_keywords(mention)
            for keyword in context_keywords:
                if keyword not in node.keywords:
                    node.keywords.append(keyword)
                    if len(node.keywords) > 20:  # Limit keywords
                        node.keywords = node.keywords[-20:]
            
            # Update topics based on sentiment and context
            self._update_node_topics(node, mention)
            
            node.save()
            return node
            
        except Exception as e:
            logger.error(f"Error updating knowledge node: {str(e)}")
            return None
    
    def _get_entity_name(self, mention: EntityMention) -> str:
        """Get display name for entity from mention"""
        # This would typically fetch from the actual entity
        # For now, return a formatted name
        return f"{mention.mentioned_entity_type.title()} {mention.mentioned_entity_id}"
    
    def _extract_context_keywords(self, mention: EntityMention) -> List[str]:
        """Extract keywords from mention context"""
        context = f"{mention.context_before} {mention.context_after}".lower()
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 
            'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through'
        }
        
        # Extract meaningful words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', context)
        keywords = [word for word in words if word not in stop_words]
        
        # Return most frequent keywords
        counter = Counter(keywords)
        return [word for word, count in counter.most_common(5)]
    
    def _update_node_topics(self, node: KnowledgeGraphNode, mention: EntityMention):
        """Update node topics based on mention context and sentiment"""
        # Simple topic extraction based on entity type and context
        topic_mapping = {
            'project': ['project_management', 'coordination'],
            'task': ['task_management', 'work_items'],
            'utility': ['infrastructure', 'utilities'],
            'conflict': ['issues', 'problems'],
            'stakeholder': ['communication', 'coordination'],
            'user': ['team', 'personnel'],
            'document': ['documentation', 'files'],
            'note': ['notes', 'comments']
        }
        
        base_topics = topic_mapping.get(mention.mentioned_entity_type, [])
        
        # Add sentiment-based topics
        if mention.sentiment:
            base_topics.append(f"{mention.sentiment}_sentiment")
        
        # Update node topics
        for topic in base_topics:
            if topic not in node.topics:
                node.topics.append(topic)
                if len(node.topics) > 10:  # Limit topics
                    node.topics = node.topics[-10:]
    
    def _update_node_connections(self, relationship: EntityRelationship):
        """Update knowledge graph node connections based on relationship"""
        try:
            # Update source node connections
            source_node = KnowledgeGraphNode.objects.get(
                entity_type=relationship.source_entity_type,
                entity_id=relationship.source_entity_id
            )
            
            target_key = f"{relationship.target_entity_type}:{relationship.target_entity_id}"
            source_node.connections[target_key] = {
                'type': relationship.relationship_type,
                'strength': relationship.strength,
                'last_updated': timezone.now().isoformat()
            }
            source_node.save()
            
            # Update target node connections (bidirectional)
            target_node = KnowledgeGraphNode.objects.get(
                entity_type=relationship.target_entity_type,
                entity_id=relationship.target_entity_id
            )
            
            source_key = f"{relationship.source_entity_type}:{relationship.source_entity_id}"
            target_node.connections[source_key] = {
                'type': relationship.relationship_type,
                'strength': relationship.strength,
                'last_updated': timezone.now().isoformat()
            }
            target_node.save()
            
        except KnowledgeGraphNode.DoesNotExist:
            logger.warning(f"Knowledge graph node not found for relationship: {relationship}")
        except Exception as e:
            logger.error(f"Error updating node connections: {str(e)}")
    
    def _recalculate_graph_metrics(self, nodes: List[KnowledgeGraphNode]):
        """Recalculate centrality and importance metrics for knowledge graph"""
        try:
            # Build adjacency information
            node_connections = {}
            for node in nodes:
                node_id = f"{node.entity_type}:{node.entity_id}"
                node_connections[node_id] = list(node.connections.keys())
            
            # Calculate centrality scores (simple degree centrality)
            for node in nodes:
                node_id = f"{node.entity_type}:{node.entity_id}"
                degree = len(node_connections.get(node_id, []))
                
                # Normalize by total possible connections
                max_connections = len(nodes) - 1
                node.centrality_score = degree / max(1, max_connections)
                
                # Update importance based on centrality and activity
                node.importance_score = min(1.0, 
                    0.5 * node.centrality_score + 
                    0.3 * node.activity_level + 
                    0.2 * (node.total_mentions / 100.0)
                )
                
                node.save()
                
        except Exception as e:
            logger.error(f"Error recalculating graph metrics: {str(e)}")
    
    # ========================================
    # UTILITY METHODS
    # ========================================
    
    def _validate_entity_exists(self, entity_type: str, entity_id: str) -> bool:
        """Validate that the mentioned entity exists in the database"""
        
        model_mapping = {
            'project': Project,
            'task': Task,
            'utility': Utility,
            'conflict': Conflict,
            'stakeholder': Stakeholder,
            'user': User,
            'document': Document,
            'note': Note,
        }
        
        model = model_mapping.get(entity_type)
        if not model:
            return False
        
        try:
            model.objects.get(id=entity_id)
            return True
        except (model.DoesNotExist, ValueError):
            return False
    
    def _post_process_mentions(self, mentions: List[EntityMention], context_text: str, user: User):
        """Post-process detected mentions for validation and enhancement"""
        if len(mentions) > 1:
            # Analyze relationships between mentions
            relationships = self.analyze_entity_relationships(mentions, context_text, user)
            
            # Update knowledge graph
            self.update_knowledge_graph_advanced(mentions, relationships, user)
        
        # Update individual mention validation
        for mention in mentions:
            mention.is_validated = True
            mention.save()
"""