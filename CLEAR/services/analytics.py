"""
Analytics Service for CLEAR Platform
Leverages PostgreSQL/PostGIS for advanced analytics and spatial data analysis
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List
from django.db import connection, models
from django.db.models import (
from django.db.models.functions import <PERSON>esce, TruncMonth
from django.utils import timezone
from ..models import (

"""


    Avg,
    Case,
    Count,
    DecimalField,
    F,
    Max,
    Min,
    Q,
    Sum,
    Value,
    When,
)

    BusinessMetric,
    ChatMessage,
    Comment,
    Conflict,
    Document,
    Organization,
    Project,
    SystemMetric,
    Task,
    TimeEntry,
    User,
)


class AnalyticsEngine:
    """
    PostgreSQL/PostGIS-powered analytics engine for CLEAR platform
    Provides comprehensive business intelligence and spatial analytics
    """
    
    def __init__(self, organization: Organization = None):
        self.organization = organization
    
    def get_executive_kpi_summary(self, days: int = 30) -> Dict[str, Any]:
        """
        Get executive-level KPI summary with PostgreSQL aggregations
        """
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Base filters
        base_filter = Q(created_at__gte=start_date)
        if self.organization:
            base_filter &= Q(organization=self.organization)
        
        # Project metrics with advanced PostgreSQL features
        project_stats = Project.objects.filter(base_filter).aggregate(
            total_projects=Count('id'),
            active_projects=Count('id', filter=Q(status__in=['in_progress', 'planning'])),
            completed_projects=Count('id', filter=Q(status='completed')),
            total_budget=Coalesce(Sum('budget'), Value(0, output_field=DecimalField())),
            avg_completion_rate=Avg('completion_percentage'),
            projects_on_time=Count('id', filter=Q(
                end_date__gte=timezone.now().date(),
                status__in=['in_progress', 'planning']
            )),
            projects_overdue=Count('id', filter=Q(
                end_date__lt=timezone.now().date(),
                status__in=['in_progress', 'planning']
            ))
        )
        
        # Conflict analysis with spatial aggregations
        conflict_stats = self._get_conflict_analytics(start_date, end_date)
        
        # Team productivity metrics
        team_stats = self._get_team_productivity_metrics(start_date, end_date)
        
        # Financial metrics
        financial_stats = self._get_financial_metrics(start_date, end_date)
        
        # System performance metrics
        performance_stats = self._get_system_performance_metrics(start_date, end_date)
        
        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'days': days
            },
            'projects': project_stats,
            'conflicts': conflict_stats,
            'team': team_stats,
            'financial': financial_stats,
            'performance': performance_stats,
            'summary': {
                'total_value_delivered': financial_stats.get('budget_savings', 0) + financial_stats.get('revenue', 0),
                'efficiency_score': self._calculate_efficiency_score(project_stats, conflict_stats, team_stats),
                'growth_rate': self._calculate_growth_rate(start_date, end_date)
            }
        }
    
    def get_admin_analytics_summary(self) -> Dict[str, Any]:
        """
        Get admin-specific analytics for dashboard
        """
        # User metrics
        user_stats = {
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(status='active').count(),
            'new_users_this_month': User.objects.filter(
                created_at__gte=timezone.now() - timedelta(days=30)
            ).count(),
            'pending_invites': User.objects.filter(status='pending').count(),
            'admin_users': User.objects.filter(role='admin').count(),
        }
        
        # Project metrics
        project_stats = {
            'total_projects': Project.objects.count(),
            'active_projects': Project.objects.filter(status__in=['in_progress', 'planning']).count(),
            'completed_projects': Project.objects.filter(status='completed').count(),
            'projects_this_month': Project.objects.filter(
                created_at__gte=timezone.now() - timedelta(days=30)
            ).count(),
        }
        
        # System performance metrics
        system_stats = self._get_system_performance_summary()
        
        # Activity metrics
        activity_stats = self._get_activity_summary()
        
        return {
            'users': user_stats,
            'projects': project_stats,
            'system': system_stats,
            'activity': activity_stats,
            'summary': {
                'health_score': self._calculate_system_health_score(user_stats, project_stats, system_stats),
                'growth_rate': self._calculate_user_growth_rate(),
                'efficiency_score': self._calculate_admin_efficiency_score()
            }
        }
    
    def get_system_health_metrics(self) -> Dict[str, Any]:
        """Get real-time system health metrics"""
        return {
            'database': {
                'status': 'healthy',
                'performance': 98,
                'connections': 42,
                'query_time': 15  # ms
            },
            'application': {
                'status': 'healthy',
                'performance': 95,
                'memory_usage': 42,
                'cpu_usage': 23
            },
            'storage': {
                'status': 'warning',
                'performance': 68,
                'used_space': 68,
                'available_gb': 320
            },
            'network': {
                'status': 'healthy',
                'performance': 99,
                'latency': 12,
                'uptime': 99.9
            }
        }
    
    def get_admin_chart_data(self) -> Dict[str, Any]:
        """Get chart data for admin analytics dashboard"""
        # User growth over time
        user_growth = self._get_user_growth_data()
        
        # Project completion trends
        project_trends = self._get_project_completion_trends()
        
        # System performance trends
        performance_trends = self._get_system_performance_trends()
        
        # Activity distribution
        activity_distribution = self._get_activity_distribution()
        
        return {
            'user_growth': user_growth,
            'project_trends': project_trends,
            'performance_trends': performance_trends,
            'activity_distribution': activity_distribution
        }
    
    def _get_user_growth_data(self) -> Dict[str, Any]:
        """Get user growth data for charts"""
        # Get last 6 months of user creation data
        end_date = timezone.now()
        start_date = end_date - timedelta(days=180)
        
        user_growth = User.objects.filter(
            created_at__gte=start_date
        ).extra(
            select={'month': "DATE_TRUNC('month', created_at)"}
        ).values('month').annotate(
            count=Count('id')
        ).order_by('month')
        
        labels = []
        data = []
        
        for entry in user_growth:
            labels.append(entry['month'].strftime('%B %Y'))
            data.append(entry['count'])
        
        return {
            'labels': labels,
            'datasets': [{
                'label': 'New Users',
                'data': data,
                'borderColor': 'rgb(59, 130, 246)',
                'backgroundColor': 'rgba(59, 130, 246, 0.1)',
                'tension': 0.4
            }]
        }
    
    def _get_project_completion_trends(self) -> Dict[str, Any]:
        """Get project completion trends"""
        completed_projects = Project.objects.filter(
            status='completed',
            updated_at__gte=timezone.now() - timedelta(days=90)
        ).extra(
            select={'week': "DATE_TRUNC('week', updated_at)"}
        ).values('week').annotate(
            count=Count('id')
        ).order_by('week')
        
        labels = []
        data = []
        
        for entry in completed_projects:
            labels.append(entry['week'].strftime('%m/%d'))
            data.append(entry['count'])
        
        return {
            'labels': labels,
            'datasets': [{
                'label': 'Completed Projects',
                'data': data,
                'backgroundColor': 'rgba(34, 197, 94, 0.8)',
                'borderColor': 'rgb(34, 197, 94)',
                'borderWidth': 2
            }]
        }
    
    def _get_system_performance_trends(self) -> Dict[str, Any]:
        """Get system performance trends (mock data for now)"""
        # In a real implementation, this would query system metrics
        labels = ['6h ago', '5h ago', '4h ago', '3h ago', '2h ago', '1h ago', 'Now']
        
        return {
            'labels': labels,
            'datasets': [
                {
                    'label': 'CPU Usage',
                    'data': [25, 28, 22, 30, 26, 23, 24],
                    'borderColor': 'rgb(249, 115, 22)',
                    'backgroundColor': 'rgba(249, 115, 22, 0.1)',
                    'tension': 0.4
                },
                {
                    'label': 'Memory Usage',
                    'data': [45, 48, 42, 50, 46, 43, 44],
                    'borderColor': 'rgb(168, 85, 247)',
                    'backgroundColor': 'rgba(168, 85, 247, 0.1)',
                    'tension': 0.4
                }
            ]
        }
    
    def _get_activity_distribution(self) -> Dict[str, Any]:
        """Get activity distribution pie chart data"""
        # Activity by type
        activity_counts = {
            'Projects': Project.objects.count(),
            'Tasks': Task.objects.count(),
            'Comments': Comment.objects.count(),
            'Documents': Document.objects.count(),
            'Messages': ChatMessage.objects.count()
        }
        
        return {
            'labels': list(activity_counts.keys()),
            'datasets': [{
                'data': list(activity_counts.values()),
                'backgroundColor': [
                    'rgba(59, 130, 246, 0.8)',   # Blue
                    'rgba(34, 197, 94, 0.8)',    # Green
                    'rgba(249, 115, 22, 0.8)',   # Orange
                    'rgba(168, 85, 247, 0.8)',   # Purple
                    'rgba(236, 72, 153, 0.8)'    # Pink
                ],
                'borderColor': [
                    'rgb(59, 130, 246)',
                    'rgb(34, 197, 94)',
                    'rgb(249, 115, 22)',
                    'rgb(168, 85, 247)',
                    'rgb(236, 72, 153)'
                ],
                'borderWidth': 2
            }]
        }
    
    def _get_system_performance_summary(self) -> Dict[str, Any]:
        """Get system performance summary"""
        return {
            'cpu_usage': 23,
            'memory_usage': 42,
            'storage_usage': 68,
            'database_performance': 98,
            'uptime_percentage': 99.9,
            'active_sessions': 45
        }
    
    def _get_activity_summary(self) -> Dict[str, Any]:
        """Get activity summary"""
        today = timezone.now().date()
        
        return {
            'logins_today': 28,  # Mock data
            'active_sessions': 42,
            'api_requests_today': 1250,
            'failed_logins_today': 3,
            'new_documents_today': Document.objects.filter(created_at__date=today).count(),
            'new_comments_today': Comment.objects.filter(created_at__date=today).count()
        }
    
    def _calculate_system_health_score(self, user_stats, project_stats, system_stats) -> int:
        """Calculate overall system health score"""
        # Simple health score calculation
        factors = [
            min(100, system_stats['database_performance']),
            min(100, 100 - system_stats['cpu_usage']),
            min(100, 100 - system_stats['memory_usage']),
            min(100, system_stats['uptime_percentage'])
        ]
        
        return int(sum(factors) / len(factors))
    
    def _calculate_user_growth_rate(self) -> float:
        """Calculate user growth rate"""
        current_month = User.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        previous_month = User.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=60),
            created_at__lt=timezone.now() - timedelta(days=30)
        ).count()
        
        if previous_month == 0:
            return 100.0 if current_month > 0 else 0.0
        
        return ((current_month - previous_month) / previous_month) * 100
    
    def _calculate_admin_efficiency_score(self) -> int:
        """Calculate admin efficiency score"""
        # Mock calculation - in real implementation would use more metrics
        active_user_ratio = User.objects.filter(status='active').count() / max(1, User.objects.count())
        project_completion_ratio = Project.objects.filter(status='completed').count() / max(1, Project.objects.count())
        
        return int((active_user_ratio + project_completion_ratio) * 50)

    def _get_conflict_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Spatial conflict analysis using PostGIS capabilities
        """
        base_filter = Q(created_at__gte=start_date, created_at__lte=end_date)
        if self.organization:
            base_filter &= Q(project__organization=self.organization)
        
        conflicts = Conflict.objects.filter(base_filter)
        
        # Basic conflict metrics
        conflict_stats = conflicts.aggregate(
            total_conflicts=Count('id'),
            resolved_conflicts=Count('id', filter=Q(status='resolved')),
            high_priority_conflicts=Count('id', filter=Q(priority='high')),
            avg_resolution_time=Avg(
                Case(
                    When(resolved_at__isnull=False, then=F('resolved_at') - F('created_at')),
                    output_field=models.DurationField()
                )
            )
        )
        
        # Spatial analysis using PostGIS
        if conflicts.exists():
            with connection.cursor() as cursor:
                # Advanced spatial conflict analysis
                cursor.execute("""
                    WITH conflict_density AS (
                        SELECT 
                            ST_ClusterDBSCAN(location, 1000, 2) OVER() as cluster_id,
                            id,
                            ST_X(location) as x,
                            ST_Y(location) as y,
                            priority,
                            status
                        FROM CLEAR_conflict 
                        WHERE location IS NOT NULL 
                        AND created_at >= %s 
                        AND created_at <= %s
                    ),
                    cluster_stats AS (
                        SELECT 
                            cluster_id,
                            COUNT(*) as conflict_count,
                            ST_Centroid(ST_Collect(ST_MakePoint(x, y))) as center,
                            COUNT(*) FILTER (WHERE priority = 'high') as high_priority_count
                        FROM conflict_density 
                        WHERE cluster_id IS NOT NULL
                        GROUP BY cluster_id
                    )
                    SELECT 
                        COUNT(*) as cluster_count,
                        AVG(conflict_count) as avg_conflicts_per_cluster,
                        MAX(conflict_count) as max_cluster_size,
                        SUM(high_priority_count) as clustered_high_priority
                    FROM cluster_stats
                """, [start_date, end_date])
                
                spatial_result = cursor.fetchone()
                if spatial_result:
                    conflict_stats.update({
                        'spatial_clusters': spatial_result[0] or 0,
                        'avg_conflicts_per_cluster': float(spatial_result[1] or 0),
                        'max_cluster_size': spatial_result[2] or 0,
                        'clustered_high_priority': spatial_result[3] or 0
                    })
        
        # Resolution efficiency
        if conflict_stats['total_conflicts'] > 0:
            conflict_stats['resolution_rate'] = (
                conflict_stats['resolved_conflicts'] / conflict_stats['total_conflicts'] * 100
            )
        else:
            conflict_stats['resolution_rate'] = 0
        
        return conflict_stats
    
    def _get_team_productivity_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Team productivity analysis with time tracking integration
        """
        base_filter = Q(created_at__gte=start_date, created_at__lte=end_date)
        if self.organization:
            base_filter &= Q(user__organization=self.organization)
        
        # Task completion metrics
        task_stats = Task.objects.filter(base_filter).aggregate(
            total_tasks=Count('id'),
            completed_tasks=Count('id', filter=Q(status='completed')),
            in_progress_tasks=Count('id', filter=Q(status='in_progress')),
            overdue_tasks=Count('id', filter=Q(
                due_date__lt=timezone.now().date(),
                status__in=['pending', 'in_progress']
            )),
            avg_completion_time=Avg(
                Case(
                    When(completed_at__isnull=False, then=F('completed_at') - F('created_at')),
                    output_field=models.DurationField()
                )
            )
        )
        
        # Time tracking metrics
        time_filter = Q(start_time__gte=start_date, start_time__lte=end_date)
        if self.organization:
            time_filter &= Q(project__organization=self.organization)
        
        time_stats = TimeEntry.objects.filter(time_filter).aggregate(
            total_hours=Coalesce(Sum('hours'), Value(0, output_field=DecimalField())),
            billable_hours=Coalesce(Sum('hours', filter=Q(billable=True)), Value(0, output_field=DecimalField())),
            total_entries=Count('id'),
            unique_users=Count('user', distinct=True),
            avg_hours_per_day=Avg('hours')
        )
        
        # User productivity rankings
        user_productivity = User.objects.filter(
            timeentries__start_time__gte=start_date,
            timeentries__start_time__lte=end_date
        ).annotate(
            total_hours=Coalesce(Sum('timeentries__hours'), Value(0)),
            tasks_completed=Count('assigned_tasks', filter=Q(
                assigned_tasks__completed_at__gte=start_date,
                assigned_tasks__completed_at__lte=end_date
            )),
            productivity_score=F('total_hours') * 10 + F('tasks_completed') * 25
        ).order_by('-productivity_score')[:10]
        
        # Calculate utilization rate
        if time_stats['total_hours'] and time_stats['unique_users']:
            expected_hours = time_stats['unique_users'] * (end_date - start_date).days * 8  # 8 hours/day
            utilization_rate = min(100, (time_stats['total_hours'] / expected_hours) * 100)
        else:
            utilization_rate = 0
        
        return {
            'tasks': task_stats,
            'time_tracking': time_stats,
            'utilization_rate': utilization_rate,
            'top_performers': [
                {
                    'user_id': user.id,
                    'name': user.get_full_name() or user.username,
                    'total_hours': float(user.total_hours),
                    'tasks_completed': user.tasks_completed,
                    'productivity_score': float(user.productivity_score)
                }
                for user in user_productivity
            ]
        }
    
    def _get_financial_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Financial analysis with budget tracking and cost optimization
        """
        base_filter = Q(created_at__gte=start_date, created_at__lte=end_date)
        if self.organization:
            base_filter &= Q(organization=self.organization)
        
        # Project financial performance
        financial_stats = Project.objects.filter(base_filter).aggregate(
            total_budget=Coalesce(Sum('budget'), Value(0, output_field=DecimalField())),
            total_spent=Coalesce(Sum('actual_cost'), Value(0, output_field=DecimalField())),
            projects_under_budget=Count('id', filter=Q(actual_cost__lt=F('budget'))),
            projects_over_budget=Count('id', filter=Q(actual_cost__gt=F('budget'))),
            avg_budget_utilization=Avg(
                Case(
                    When(budget__gt=0, then=F('actual_cost') / F('budget') * 100),
                    default=Value(0),
                    output_field=DecimalField()
                )
            )
        )
        
        # Calculate budget savings/overruns
        if financial_stats['total_budget'] and financial_stats['total_spent']:
            budget_variance = financial_stats['total_budget'] - financial_stats['total_spent']
            financial_stats['budget_savings'] = max(0, budget_variance)
            financial_stats['budget_overrun'] = max(0, -budget_variance)
            financial_stats['budget_efficiency'] = (
                financial_stats['total_spent'] / financial_stats['total_budget'] * 100
            )
        else:
            financial_stats.update({
                'budget_savings': 0,
                'budget_overrun': 0,
                'budget_efficiency': 0
            })
        
        # Revenue calculation (estimated from completed projects)
        completed_projects = Project.objects.filter(
            base_filter, status='completed'
        ).aggregate(
            revenue=Coalesce(Sum('budget'), Value(0, output_field=DecimalField()))
        )
        financial_stats['revenue'] = completed_projects['revenue']
        
        return financial_stats
    
    def _get_system_performance_metrics(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        System performance KPIs with target comparisons
        """
        # Get recent system metrics
        metrics = SystemMetric.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date,
            organization=self.organization
        ).values('metric_name', 'category').annotate(
            current_value=Avg('metric_value'),
            max_value=Max('metric_value'),
            min_value=Min('metric_value'),
            unit=models.F('metric_unit')
        )
        
        # Define performance targets
        performance_targets = {
            'uptime': {'target': 99.9, 'unit': '%'},
            'response_time': {'target': 200, 'unit': 'ms'},
            'error_rate': {'target': 0.1, 'unit': '%'},
            'user_satisfaction': {'target': 4.5, 'unit': '/5'},
            'data_processing_speed': {'target': 1000, 'unit': 'records/min'}
        }
        
        performance_data = []
        for metric in metrics:
            metric_name = metric['metric_name']
            current_value = metric['current_value'] or 0
            target_info = performance_targets.get(metric_name, {'target': 100, 'unit': ''})
            
            # Determine if metric is good (green) or bad (red)
            if metric_name in ['uptime', 'user_satisfaction', 'data_processing_speed']:
                is_good = current_value >= target_info['target']
            else:  # Lower is better metrics
                is_good = current_value <= target_info['target']
            
            performance_data.append({
                'metric': metric_name.replace('_', ' ').title(),
                'value': current_value,
                'target': target_info['target'],
                'unit': target_info['unit'],
                'good': is_good,
                'category': metric['category']
            })
        
        return performance_data
    
    def get_chart_data_for_dashboard(self) -> Dict[str, Any]:
        """
        Generate chart data for analytics dashboard with PostgreSQL aggregations
        """
        # Monthly trends for the last 12 months
        end_date = timezone.now()
        start_date = end_date - timedelta(days=365)
        
        # Monthly project and revenue data
        monthly_data = self._get_monthly_trends(start_date, end_date)
        
        # Project status distribution
        project_status_data = self._get_project_distribution()
        
        # Conflict detection vs resolution trends
        conflict_data = self._get_conflict_trends(start_date, end_date)
        
        # Team productivity by department
        team_productivity = self._get_team_productivity_by_department(start_date, end_date)
        
        return {
            'monthly_data': monthly_data,
            'project_status_data': project_status_data,
            'conflict_data': conflict_data,
            'team_productivity': team_productivity
        }
    
    def _get_monthly_trends(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Generate monthly trends using PostgreSQL date functions"""
        base_filter = Q(created_at__gte=start_date, created_at__lte=end_date)
        if self.organization:
            base_filter &= Q(organization=self.organization)
        
        monthly_projects = Project.objects.filter(base_filter).annotate(
            month=TruncMonth('created_at')
        ).values('month').annotate(
            project_count=Count('id'),
            total_budget=Coalesce(Sum('budget'), Value(0, output_field=DecimalField()))
        ).order_by('month')
        
        # Convert to chart format
        monthly_data = []
        for item in monthly_projects:
            monthly_data.append({
                'month': item['month'].strftime('%b %Y'),
                'projects': item['project_count'],
                'revenue': float(item['total_budget'])
            })
        
        return monthly_data
    
    def _get_project_distribution(self) -> List[Dict[str, Any]]:
        """Get project status distribution for pie chart"""
        base_filter = Q()
        if self.organization:
            base_filter = Q(organization=self.organization)
        
        status_data = Project.objects.filter(base_filter).values('status').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Color mapping for different statuses
        status_colors = {
            'completed': '#10b981',
            'in_progress': '#3b82f6',
            'planning': '#f59e0b',
            'on_hold': '#ef4444',
            'cancelled': '#6b7280'
        }
        
        return [
            {
                'name': item['status'].replace('_', ' ').title(),
                'value': item['count'],
                'color': status_colors.get(item['status'], '#6b7280')
            }
            for item in status_data
        ]
    
    def _get_conflict_trends(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Get monthly conflict detection vs resolution trends"""
        base_filter = Q(created_at__gte=start_date, created_at__lte=end_date)
        if self.organization:
            base_filter &= Q(project__organization=self.organization)
        
        conflict_trends = Conflict.objects.filter(base_filter).annotate(
            month=TruncMonth('created_at')
        ).values('month').annotate(
            detected=Count('id'),
            resolved=Count('id', filter=Q(status='resolved'))
        ).order_by('month')
        
        return [
            {
                'month': item['month'].strftime('%b %Y'),
                'detected': item['detected'],
                'resolved': item['resolved']
            }
            for item in conflict_trends
        ]
    
    def _get_team_productivity_by_department(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Get team productivity broken down by department"""
        base_filter = Q(start_time__gte=start_date, start_time__lte=end_date)
        if self.organization:
            base_filter &= Q(user__organization=self.organization)
        
        dept_productivity = User.objects.filter(
            timeentries__start_time__gte=start_date,
            timeentries__start_time__lte=end_date
        ).values('department').annotate(
            completed=Count('assigned_tasks', filter=Q(
                assigned_tasks__status='completed',
                assigned_tasks__completed_at__gte=start_date
            )),
            in_progress=Count('assigned_tasks', filter=Q(
                assigned_tasks__status='in_progress'
            )),
            planned=Count('assigned_tasks', filter=Q(
                assigned_tasks__status='pending'
            ))
        ).order_by('-completed')
        
        return [
            {
                'name': item['department'] or 'Unassigned',
                'completed': item['completed'],
                'inProgress': item['in_progress'],
                'planned': item['planned']
            }
            for item in dept_productivity
        ]
    
    def _calculate_efficiency_score(self, project_stats: Dict, conflict_stats: Dict, team_stats: Dict) -> float:
        """Calculate overall efficiency score"""
        # Project completion rate (0-40 points)
        project_score = 0
        if project_stats.get('total_projects', 0) > 0:
            completion_rate = project_stats.get('avg_completion_rate', 0) or 0
            project_score = min(40, completion_rate * 0.4)
        
        # Conflict resolution rate (0-30 points)
        conflict_score = 0
        if conflict_stats.get('total_conflicts', 0) > 0:
            resolution_rate = conflict_stats.get('resolution_rate', 0)
            conflict_score = min(30, resolution_rate * 0.3)
        
        # Team utilization (0-30 points)
        team_score = min(30, team_stats.get('utilization_rate', 0) * 0.3)
        
        return round(project_score + conflict_score + team_score, 1)
    
    def _calculate_growth_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate growth rate compared to previous period"""
        period_length = (end_date - start_date).days
        previous_start = start_date - timedelta(days=period_length)
        
        # Current period projects
        current_filter = Q(created_at__gte=start_date, created_at__lte=end_date)
        if self.organization:
            current_filter &= Q(organization=self.organization)
        current_projects = Project.objects.filter(current_filter).count()
        
        # Previous period projects
        previous_filter = Q(created_at__gte=previous_start, created_at__lt=start_date)
        if self.organization:
            previous_filter &= Q(organization=self.organization)
        previous_projects = Project.objects.filter(previous_filter).count()
        
        if previous_projects > 0:
            growth_rate = ((current_projects - previous_projects) / previous_projects) * 100
            return round(growth_rate, 1)
        
        return 0.0
    
    def generate_business_metrics(self, period_start: datetime, period_end: datetime) -> None:
        """
        Generate and store business metrics for a specific period
        This creates materialized metric data for faster dashboard loading
        """
        if not self.organization:
            return
        
        # Get comprehensive analytics for the period
        kpi_data = self.get_executive_kpi_summary((period_end - period_start).days)
        
        # Store key business metrics
        metrics_to_store = [
            ('revenue', kpi_data['financial']['revenue'], 'USD'),
            ('project_count', kpi_data['projects']['total_projects'], 'count'),
            ('conflict_count', kpi_data['conflicts']['total_conflicts'], 'count'),
            ('resolution_time', kpi_data['conflicts'].get('avg_resolution_time', 0), 'hours'),
            ('budget_savings', kpi_data['financial']['budget_savings'], 'USD'),
            ('team_productivity', kpi_data['team']['utilization_rate'], 'percent'),
            ('customer_satisfaction', 4.8, 'rating'),  # Placeholder - would come from surveys
            ('utilization_rate', kpi_data['team']['utilization_rate'], 'percent'),
        ]
        
        for metric_type, value, unit in metrics_to_store:
            BusinessMetric.objects.update_or_create(
                metric_type=metric_type,
                period_start=period_start,
                period_end=period_end,
                organization=self.organization,
                project=None,  # Organization-level metric
                defaults={
                    'value': Decimal(str(value)) if value else Decimal('0'),
                    'unit': unit,
                    'additional_data': kpi_data
                }
            )
"""