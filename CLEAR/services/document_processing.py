"""
Document processing service for CLEAR application.

Handles file processing tasks like thumbnail generation, preview creation,
text extraction, and file validation.
"""

import io
import logging
import os
from pathlib import Path
from typing import Any, Dict, Tuple
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from PIL import Image
            import time
        from ..models import Document

"""



logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Service for processing document files"""
    
    # Supported image formats for thumbnail generation
    IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}
    
    # Supported preview formats
    PREVIEW_FORMATS = {'.pdf', '.txt', '.md', '.csv'} | IMAGE_FORMATS
    
    # Maximum thumbnail dimensions
    THUMBNAIL_MAX_SIZE = (300, 300)
    PREVIEW_MAX_SIZE = (800, 600)
    
    def __init__(self):
        self.ensure_directories()
    
    def ensure_directories(self):
        """Ensure required directories exist"""
        directories = [
            'thumbnails',
            'previews',
            'processed'
        ]
        
        for directory in directories:
            path = Path(settings.MEDIA_ROOT) / directory
            path.mkdir(parents=True, exist_ok=True)
    
    def process_document(self, document) -> Dict[str, Any]:
        """
        Process a document for thumbnails and previews
        
        Args:
            document: Document model instance
            
        Returns:
            Dict with processing results
        """
        results = {
            'thumbnail_generated': False,
            'preview_generated': False,
            'text_extracted': False,
            'metadata_extracted': False,
            'errors': []
        }
        
        try:
            file_ext = Path(document.name).suffix.lower()
            
            # Generate thumbnail if supported
            if file_ext in self.IMAGE_FORMATS:
                results['thumbnail_generated'] = self.generate_image_thumbnail(document)
                results['preview_generated'] = self.generate_image_preview(document)
            elif file_ext == '.pdf':
                results['thumbnail_generated'] = self.generate_pdf_thumbnail(document)
                results['preview_generated'] = self.generate_pdf_preview(document)
            
            # Extract text content if possible
            if file_ext in {'.txt', '.md', '.csv'}:
                results['text_extracted'] = self.extract_text_content(document)
            elif file_ext == '.pdf':
                results['text_extracted'] = self.extract_pdf_text(document)
            
            # Extract metadata
            results['metadata_extracted'] = self.extract_metadata(document)
            
        except Exception as e:
            logger.error(f"Error processing document {document.id}: {e}")
            results['errors'].append(str(e))
        
        return results
    
    def generate_image_thumbnail(self, document) -> bool:
        """Generate thumbnail for image files"""
        try:
            # Open the image file
            if hasattr(document, 'file') and document.file:
                image = Image.open(document.file.path)
            else:
                # Fallback to file_path
                image = Image.open(document.file_path)
            
            # Convert RGBA to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # Create thumbnail
            image.thumbnail(self.THUMBNAIL_MAX_SIZE, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            thumbnail_buffer = io.BytesIO()
            image.save(thumbnail_buffer, format='JPEG', quality=85, optimize=True)
            thumbnail_buffer.seek(0)
            
            # Generate thumbnail path
            thumbnail_name = f"thumbnails/{document.id}_thumb.jpg"
            
            # Save to storage
            default_storage.save(thumbnail_name, ContentFile(thumbnail_buffer.getvalue()))
            
            # Update document model if it has thumbnail field
            if hasattr(document, 'thumbnail_path'):
                document.thumbnail_path = thumbnail_name
                document.save(update_fields=['thumbnail_path'])
            
            logger.info(f"Generated thumbnail for document {document.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for document {document.id}: {e}")
            return False
    
    def generate_image_preview(self, document) -> bool:
        """Generate preview for image files"""
        try:
            # For images, preview is just a larger version of the thumbnail
            if hasattr(document, 'file') and document.file:
                image = Image.open(document.file.path)
            else:
                image = Image.open(document.file_path)
            
            # Convert RGBA to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # Create preview (larger than thumbnail)
            preview_size = (min(image.width, self.PREVIEW_MAX_SIZE[0]), 
                          min(image.height, self.PREVIEW_MAX_SIZE[1]))
            image.thumbnail(preview_size, Image.Resampling.LANCZOS)
            
            # Save preview
            preview_buffer = io.BytesIO()
            image.save(preview_buffer, format='JPEG', quality=90, optimize=True)
            preview_buffer.seek(0)
            
            # Generate preview path
            preview_name = f"previews/{document.id}_preview.jpg"
            
            # Save to storage
            default_storage.save(preview_name, ContentFile(preview_buffer.getvalue()))
            
            # Update document model if it has preview field
            if hasattr(document, 'preview_path'):
                document.preview_path = preview_name
                document.save(update_fields=['preview_path'])
            
            logger.info(f"Generated preview for document {document.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate preview for document {document.id}: {e}")
            return False
    
    def generate_pdf_thumbnail(self, document) -> bool:
        """Generate thumbnail for PDF files"""
        try:
            # This would require pdf2image or similar library
            # For now, return a placeholder
            logger.info(f"PDF thumbnail generation not implemented for document {document.id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to generate PDF thumbnail for document {document.id}: {e}")
            return False
    
    def generate_pdf_preview(self, document) -> bool:
        """Generate preview for PDF files"""
        try:
            # This would require pdf2image or similar library
            # For now, return a placeholder
            logger.info(f"PDF preview generation not implemented for document {document.id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to generate PDF preview for document {document.id}: {e}")
            return False
    
    def extract_text_content(self, document) -> bool:
        """Extract text content from text files"""
        try:
            if hasattr(document, 'file') and document.file:
                with open(document.file.path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
            else:
                with open(document.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
            
            # Store extracted content if document has content field
            if hasattr(document, 'extracted_content'):
                # Limit content size to prevent database bloat
                max_content_size = 10000  # 10KB
                if len(content) > max_content_size:
                    content = content[:max_content_size] + "... [truncated]"
                
                document.extracted_content = content
                document.save(update_fields=['extracted_content'])
            
            logger.info(f"Extracted text content for document {document.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to extract text content for document {document.id}: {e}")
            return False
    
    def extract_pdf_text(self, document) -> bool:
        """Extract text content from PDF files"""
        try:
            # This would require PyPDF2, pdfplumber, or similar library
            # For now, return a placeholder
            logger.info(f"PDF text extraction not implemented for document {document.id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to extract PDF text for document {document.id}: {e}")
            return False
    
    def extract_metadata(self, document) -> bool:
        """Extract metadata from files"""
        try:
            metadata = {}
            
            # Get file stats
            if hasattr(document, 'file') and document.file:
                file_path = document.file.path
            else:
                file_path = document.file_path
            
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                metadata.update({
                    'file_size': stat.st_size,
                    'created_time': stat.st_ctime,
                    'modified_time': stat.st_mtime,
                })
            
            # Extract image metadata if it's an image
            file_ext = Path(document.name).suffix.lower()
            if file_ext in self.IMAGE_FORMATS:
                metadata.update(self.extract_image_metadata(file_path))
            
            # Store metadata if document has metadata field
            if hasattr(document, 'metadata'):
                document.metadata = metadata
                document.save(update_fields=['metadata'])
            
            logger.info(f"Extracted metadata for document {document.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to extract metadata for document {document.id}: {e}")
            return False
    
    def extract_image_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from image files"""
        try:
            with Image.open(file_path) as image:
                metadata = {
                    'width': image.width,
                    'height': image.height,
                    'format': image.format,
                    'mode': image.mode,
                }
                
                # Extract EXIF data if available
                if hasattr(image, '_getexif') and image._getexif():
                    exif = image._getexif()
                    metadata['exif'] = {k: v for k, v in exif.items() if isinstance(v, (str, int, float))}
                
                return metadata
                
        except Exception as e:
            logger.error(f"Failed to extract image metadata: {e}")
            return {}
    
    def validate_file(self, file_path: str, allowed_extensions: set = None) -> Tuple[bool, str]:
        """
        Validate uploaded file
        
        Args:
            file_path: Path to the file
            allowed_extensions: Set of allowed file extensions
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not os.path.exists(file_path):
                return False, "File does not exist"
            
            # Check file size
            max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 50 * 1024 * 1024)  # 50MB default
            file_size = os.path.getsize(file_path)
            if file_size > max_size:
                return False, f"File size ({file_size} bytes) exceeds maximum allowed size ({max_size} bytes)"
            
            # Check file extension
            file_ext = Path(file_path).suffix.lower()
            if allowed_extensions and file_ext not in allowed_extensions:
                return False, f"File extension '{file_ext}' is not allowed"
            
            # Check for malicious content (basic check)
            if self.scan_for_malicious_content(file_path):
                return False, "File appears to contain malicious content"
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def scan_for_malicious_content(self, file_path: str) -> bool:
        """
        Basic scan for malicious content
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if malicious content detected
        """
        # This is a basic implementation
        # In production, you might want to integrate with antivirus APIs
        
        suspicious_patterns = [
            b'<script',
            b'javascript:',
            b'eval(',
            b'exec(',
        ]
        
        try:
            with open(file_path, 'rb') as f:
                # Read first 1KB for pattern matching
                content = f.read(1024)
                for pattern in suspicious_patterns:
                    if pattern in content.lower():
                        return True
            return False
            
        except Exception:
            # If we can't read the file, be conservative
            return True
    
    def cleanup_processed_files(self, days_old: int = 30):
        """
        Clean up old processed files (thumbnails, previews)
        
        Args:
            days_old: Remove files older than this many days
        """
        try:
            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            
            directories = ['thumbnails', 'previews', 'processed']
            for directory in directories:
                dir_path = Path(settings.MEDIA_ROOT) / directory
                if dir_path.exists():
                    for file_path in dir_path.iterdir():
                        if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                            file_path.unlink()
                            logger.info(f"Cleaned up old processed file: {file_path}")
                            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Global instance
document_processor = DocumentProcessor()


def process_document_async(document_id):
    """
    Async function to process document (for use with Celery or similar)
    
    Args:
        document_id: ID of the document to process
    """
    try:
        document = Document.objects.get(id=document_id)
        results = document_processor.process_document(document)
        logger.info(f"Document {document_id} processing completed: {results}")
        return results
        
    except Document.DoesNotExist:
        logger.error(f"Document {document_id} not found for processing")
        return {'error': 'Document not found'}
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {e}")
        return {'error': str(e)}
"""