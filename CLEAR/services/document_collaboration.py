"""
Document Collaboration Service for CLEAR application.

Provides real-time document collaboration, conflict resolution,
and document activity tracking.
"""

import logging
from datetime import timedelta
from typing import Any, Dict, List, Optional
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone
from ..models import (

"""



# Import models (will be created if they don't exist)
    Document,
    DocumentActivity,
    DocumentCollaborationSession,
    DocumentComment,
    DocumentDiscussion,
)

User = get_user_model()
logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()


class DocumentCollaborationService:
    """
    Service for managing real-time document collaboration features.
    
    Features:
    - Real-time document editing with conflict resolution
    - User presence tracking and cursor positions
    - Document locking and concurrent editing management
    - Activity logging and change tracking
    - Discussion threads and comments
    """
    
    def __init__(self, document_id: str, user: User):
        self.document_id = document_id
        self.user = user
        self.cache_prefix = f"doc_collab:{document_id}"
        self.session_timeout = getattr(settings, 'DOCUMENT_SESSION_TIMEOUT', 300)  # 5 minutes
        
    def start_collaboration_session(self, session_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Start a new collaboration session for the document.
        
        Args:
            session_data: Optional session configuration data
            
        Returns:
            Dictionary with session information and status
        """
        try:
            with transaction.atomic():
                # Get or create document
                document = self._get_document()
                if not document:
                    return {
                        'success': False,
                        'error': 'Document not found or access denied'
                    }
                
                # Create or update collaboration session
                session, created = DocumentCollaborationSession.objects.get_or_create(
                    document=document,
                    user=self.user,
                    defaults={
                        'session_id': f"{self.user.id}_{timezone.now().timestamp()}",
                        'started_at': timezone.now(),
                        'last_activity': timezone.now(),
                        'is_active': True,
                        'session_data': session_data or {}
                    }
                )
                
                if not created:
                    # Update existing session
                    session.last_activity = timezone.now()
                    session.is_active = True
                    session.session_data.update(session_data or {})
                    session.save()
                
                # Cache session data for quick access
                cache.set(
                    f"{self.cache_prefix}:session:{self.user.id}",
                    {
                        'session_id': session.session_id,
                        'user_id': self.user.id,
                        'username': self.user.username,
                        'started_at': session.started_at.isoformat(),
                        'last_activity': session.last_activity.isoformat()
                    },
                    self.session_timeout
                )
                
                # Log activity
                self._log_activity('session_started', 'Started collaboration session')
                
                # Get current active collaborators
                active_collaborators = self._get_active_collaborators()
                
                # Notify other collaborators of new user
                self._broadcast_presence_update('user_joined', {
                    'user_id': self.user.id,
                    'username': self.user.username,
                    'session_id': session.session_id
                })
                
                return {
                    'success': True,
                    'session_id': session.session_id,
                    'active_collaborators': active_collaborators,
                    'document_id': self.document_id,
                    'user_permissions': self._get_user_permissions(document)
                }
                
        except Exception as e:
            logger.error(f"Error starting collaboration session: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def end_collaboration_session(self) -> Dict[str, Any]:
        """
        End the current collaboration session.
        
        Returns:
            Dictionary with operation status
        """
        try:
            with transaction.atomic():
                # Update database session
                DocumentCollaborationSession.objects.filter(
                    document_id=self.document_id,
                    user=self.user,
                    is_active=True
                ).update(
                    is_active=False,
                    ended_at=timezone.now(),
                    last_activity=timezone.now()
                )
                
                # Remove from cache
                cache.delete(f"{self.cache_prefix}:session:{self.user.id}")
                
                # Log activity
                self._log_activity('session_ended', 'Ended collaboration session')
                
                # Notify other collaborators
                self._broadcast_presence_update('user_left', {
                    'user_id': self.user.id,
                    'username': self.user.username
                })
                
                return {'success': True}
                
        except Exception as e:
            logger.error(f"Error ending collaboration session: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_user_activity(self, activity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user activity and maintain session keepalive.
        
        Args:
            activity_data: Activity information (cursor position, current action, etc.)
            
        Returns:
            Dictionary with operation status
        """
        try:
            # Update session activity timestamp
            DocumentCollaborationSession.objects.filter(
                document_id=self.document_id,
                user=self.user,
                is_active=True
            ).update(last_activity=timezone.now())
            
            # Update cached session data
            session_cache_key = f"{self.cache_prefix}:session:{self.user.id}"
            session_data = cache.get(session_cache_key)
            if session_data:
                session_data.update({
                    'last_activity': timezone.now().isoformat(),
                    'current_activity': activity_data.get('activity_type', 'active')
                })
                cache.set(session_cache_key, session_data, self.session_timeout)
            
            # Store cursor position if provided
            if 'cursor_position' in activity_data:
                cursor_key = f"{self.cache_prefix}:cursor:{self.user.id}"
                cache.set(cursor_key, {
                    'position': activity_data['cursor_position'],
                    'timestamp': timezone.now().isoformat(),
                    'user_id': self.user.id,
                    'username': self.user.username
                }, 30)  # Short TTL for cursor positions
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error updating user activity: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_discussion_thread(self, discussion_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new discussion thread for the document.
        
        Args:
            discussion_data: Discussion thread information
            
        Returns:
            Dictionary with created discussion information
        """
        try:
            with transaction.atomic():
                document = self._get_document()
                if not document:
                    return {
                        'success': False,
                        'error': 'Document not found or access denied'
                    }
                
                # Create discussion thread
                discussion = DocumentDiscussion.objects.create(
                    document=document,
                    created_by=self.user,
                    title=discussion_data.get('title', 'Untitled Discussion'),
                    description=discussion_data.get('description', ''),
                    discussion_type=discussion_data.get('type', 'general'),
                    priority=discussion_data.get('priority', 'medium'),
                    status='open',
                    metadata=discussion_data.get('metadata', {})
                )
                
                # Log activity
                self._log_activity('discussion_created', f'Created discussion: {discussion.title}')
                
                # Broadcast to collaborators
                self._broadcast_discussion_update('discussion_created', {
                    'discussion_id': str(discussion.id),
                    'title': discussion.title,
                    'created_by': self.user.username,
                    'type': discussion.discussion_type,
                    'priority': discussion.priority
                })
                
                return {
                    'success': True,
                    'discussion_id': str(discussion.id),
                    'discussion': {
                        'id': str(discussion.id),
                        'title': discussion.title,
                        'description': discussion.description,
                        'type': discussion.discussion_type,
                        'priority': discussion.priority,
                        'status': discussion.status,
                        'created_by': self.user.username,
                        'created_at': discussion.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error creating discussion thread: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def add_discussion_comment(self, discussion_id: str, comment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a comment to a discussion thread.
        
        Args:
            discussion_id: ID of the discussion thread
            comment_data: Comment information
            
        Returns:
            Dictionary with created comment information
        """
        try:
            with transaction.atomic():
                # Get discussion
                discussion = DocumentDiscussion.objects.filter(
                    id=discussion_id,
                    document_id=self.document_id
                ).first()
                
                if not discussion:
                    return {
                        'success': False,
                        'error': 'Discussion not found'
                    }
                
                # Create comment
                comment = DocumentComment.objects.create(
                    discussion=discussion,
                    user=self.user,
                    content=comment_data.get('content', ''),
                    comment_type=comment_data.get('type', 'comment'),
                    parent_comment_id=comment_data.get('parent_id'),
                    metadata=comment_data.get('metadata', {})
                )
                
                # Update discussion activity
                discussion.last_activity = timezone.now()
                discussion.save()
                
                # Log activity
                self._log_activity('comment_added', f'Added comment to discussion: {discussion.title}')
                
                # Broadcast to collaborators
                self._broadcast_discussion_update('comment_added', {
                    'discussion_id': discussion_id,
                    'comment_id': str(comment.id),
                    'content': comment.content,
                    'user': self.user.username,
                    'parent_id': comment.parent_comment_id
                })
                
                return {
                    'success': True,
                    'comment_id': str(comment.id),
                    'comment': {
                        'id': str(comment.id),
                        'content': comment.content,
                        'user': self.user.username,
                        'created_at': comment.created_at.isoformat(),
                        'parent_id': comment.parent_comment_id
                    }
                }
                
        except Exception as e:
            logger.error(f"Error adding discussion comment: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def resolve_discussion(self, discussion_id: str, resolution_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Resolve a discussion thread.
        
        Args:
            discussion_id: ID of the discussion thread
            resolution_data: Optional resolution information
            
        Returns:
            Dictionary with operation status
        """
        try:
            with transaction.atomic():
                discussion = DocumentDiscussion.objects.filter(
                    id=discussion_id,
                    document_id=self.document_id
                ).first()
                
                if not discussion:
                    return {
                        'success': False,
                        'error': 'Discussion not found'
                    }
                
                # Update discussion status
                discussion.status = 'resolved'
                discussion.resolved_at = timezone.now()
                discussion.resolved_by = self.user
                discussion.resolution_notes = resolution_data.get('notes', '') if resolution_data else ''
                discussion.save()
                
                # Log activity
                self._log_activity('discussion_resolved', f'Resolved discussion: {discussion.title}')
                
                # Broadcast to collaborators
                self._broadcast_discussion_update('discussion_resolved', {
                    'discussion_id': discussion_id,
                    'resolved_by': self.user.username,
                    'resolution_notes': discussion.resolution_notes
                })
                
                return {'success': True}
                
        except Exception as e:
            logger.error(f"Error resolving discussion: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_document_discussions(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Get document discussions with optional filters.
        
        Args:
            filters: Optional filters (status, type, user, etc.)
            
        Returns:
            List of discussion dictionaries
        """
        try:
            discussions_qs = DocumentDiscussion.objects.filter(
                document_id=self.document_id
            ).select_related('created_by', 'resolved_by').prefetch_related('comments')
            
            # Apply filters
            if filters:
                if 'status' in filters:
                    discussions_qs = discussions_qs.filter(status=filters['status'])
                if 'type' in filters:
                    discussions_qs = discussions_qs.filter(discussion_type=filters['type'])
                if 'priority' in filters:
                    discussions_qs = discussions_qs.filter(priority=filters['priority'])
            
            discussions = []
            for discussion in discussions_qs.order_by('-created_at'):
                discussions.append({
                    'id': str(discussion.id),
                    'title': discussion.title,
                    'description': discussion.description,
                    'type': discussion.discussion_type,
                    'priority': discussion.priority,
                    'status': discussion.status,
                    'created_by': discussion.created_by.username,
                    'created_at': discussion.created_at.isoformat(),
                    'resolved_by': discussion.resolved_by.username if discussion.resolved_by else None,
                    'resolved_at': discussion.resolved_at.isoformat() if discussion.resolved_at else None,
                    'comments_count': discussion.comments.count(),
                    'last_activity': discussion.last_activity.isoformat() if discussion.last_activity else None
                })
            
            return discussions
            
        except Exception as e:
            logger.error(f"Error getting document discussions: {e}")
            return []
    
    def get_active_collaborators(self) -> List[Dict[str, Any]]:
        """
        Get list of currently active collaborators.
        
        Returns:
            List of active collaborator information
        """
        return self._get_active_collaborators()
    
    def get_collaboration_analytics(self, timeframe_days: int = 30) -> Dict[str, Any]:
        """
        Get collaboration analytics for the document.
        
        Args:
            timeframe_days: Number of days to analyze
            
        Returns:
            Dictionary with analytics data
        """
        try:
            since_date = timezone.now() - timedelta(days=timeframe_days)
            
            # Get activity data
            activities = DocumentActivity.objects.filter(
                document_id=self.document_id,
                created_at__gte=since_date
            ).select_related('user').order_by('-created_at')
            
            # Get session data
            sessions = DocumentCollaborationSession.objects.filter(
                document_id=self.document_id,
                started_at__gte=since_date
            ).select_related('user').order_by('-started_at')
            
            # Calculate analytics
            unique_collaborators = set()
            activity_by_type = {}
            activity_by_user = {}
            daily_activity = {}
            
            for activity in activities:
                unique_collaborators.add(activity.user.username)
                
                # Count by type
                activity_type = activity.activity_type
                activity_by_type[activity_type] = activity_by_type.get(activity_type, 0) + 1
                
                # Count by user
                username = activity.user.username
                activity_by_user[username] = activity_by_user.get(username, 0) + 1
                
                # Count by day
                day_key = activity.created_at.date().isoformat()
                daily_activity[day_key] = daily_activity.get(day_key, 0) + 1
            
            # Session analytics
            total_sessions = sessions.count()
            avg_session_duration = 0
            if total_sessions > 0:
                total_duration = sum([
                    (session.ended_at or timezone.now()) - session.started_at
                    for session in sessions
                ], timedelta())
                avg_session_duration = total_duration.total_seconds() / total_sessions / 60  # minutes
            
            return {
                'timeframe_days': timeframe_days,
                'unique_collaborators': len(unique_collaborators),
                'total_activities': activities.count(),
                'total_sessions': total_sessions,
                'avg_session_duration_minutes': round(avg_session_duration, 2),
                'activity_by_type': activity_by_type,
                'activity_by_user': activity_by_user,
                'daily_activity': daily_activity,
                'most_active_users': sorted(
                    activity_by_user.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:5]
            }
            
        except Exception as e:
            logger.error(f"Error getting collaboration analytics: {e}")
            return {}
    
    # Private helper methods
    
    def _get_document(self) -> Optional[Document]:
        """Get document if user has access."""
        try:
            document = Document.objects.get(id=self.document_id)
            if document.can_user_access(self.user):
                return document
            return None
        except Document.DoesNotExist:
            return None
    
    def _get_user_permissions(self, document: Document) -> Dict[str, bool]:
        """Get user permissions for the document."""
        return {
            'can_edit': document.can_user_edit(self.user),
            'can_comment': document.can_user_comment(self.user),
            'can_manage': document.can_user_manage(self.user),
            'can_delete': document.can_user_delete(self.user)
        }
    
    def _get_active_collaborators(self) -> List[Dict[str, Any]]:
        """Get list of active collaborators."""
        try:
            # Get from cache first
            cache_key = f"{self.cache_prefix}:collaborators"
            cached_collaborators = cache.get(cache_key)
            if cached_collaborators:
                return cached_collaborators
            
            # Get from database
            active_sessions = DocumentCollaborationSession.objects.filter(
                document_id=self.document_id,
                is_active=True,
                last_activity__gte=timezone.now() - timedelta(minutes=10)
            ).select_related('user')
            
            collaborators = []
            for session in active_sessions:
                collaborators.append({
                    'user_id': session.user.id,
                    'username': session.user.username,
                    'session_id': session.session_id,
                    'last_activity': session.last_activity.isoformat(),
                    'started_at': session.started_at.isoformat()
                })
            
            # Cache for 1 minute
            cache.set(cache_key, collaborators, 60)
            
            return collaborators
            
        except Exception as e:
            logger.error(f"Error getting active collaborators: {e}")
            return []
    
    def _log_activity(self, activity_type: str, description: str, metadata: Dict[str, Any] = None):
        """Log document activity."""
        try:
            DocumentActivity.objects.create(
                document_id=self.document_id,
                user=self.user,
                activity_type=activity_type,
                description=description,
                metadata=metadata or {}
            )
        except Exception as e:
            logger.error(f"Error logging activity: {e}")
    
    def _broadcast_presence_update(self, update_type: str, data: Dict[str, Any]):
        """Broadcast presence update to document collaborators."""
        if not channel_layer:
            return
        
        try:
            async_to_sync(channel_layer.group_send)(
                f'document_{self.document_id}',
                {
                    'type': 'presence_update',
                    'update_type': update_type,
                    'data': data,
                    'timestamp': timezone.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Error broadcasting presence update: {e}")
    
    def _broadcast_discussion_update(self, update_type: str, data: Dict[str, Any]):
        """Broadcast discussion update to document collaborators."""
        if not channel_layer:
            return
        
        try:
            async_to_sync(channel_layer.group_send)(
                f'document_{self.document_id}',
                {
                    'type': 'discussion_update',
                    'update_type': update_type,
                    'data': data,
                    'timestamp': timezone.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Error broadcasting discussion update: {e}")


class DocumentConflictResolver:
    """
    Service for resolving document editing conflicts.
    
    Implements operational transformation for concurrent editing.
    """
    
    def __init__(self, document_id: str):
        self.document_id = document_id
        self.conflict_cache_prefix = f"doc_conflicts:{document_id}"
    
    def detect_conflicts(self, edit_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detect potential conflicts in document edits.
        
        Args:
            edit_data: Edit operation data
            
        Returns:
            Dictionary with conflict detection results
        """
        try:
            # Get pending edits from cache
            pending_edits_key = f"{self.conflict_cache_prefix}:pending"
            pending_edits = cache.get(pending_edits_key, [])
            
            # Simple conflict detection based on overlapping ranges
            conflicts = []
            edit_start = edit_data.get('start_position', 0)
            edit_end = edit_data.get('end_position', edit_start)
            
            for pending_edit in pending_edits:
                pending_start = pending_edit.get('start_position', 0)
                pending_end = pending_edit.get('end_position', pending_start)
                
                # Check for overlap
                if not (edit_end < pending_start or edit_start > pending_end):
                    conflicts.append({
                        'edit_id': pending_edit.get('edit_id'),
                        'user': pending_edit.get('user'),
                        'conflict_type': 'overlapping_edit',
                        'overlap_range': [max(edit_start, pending_start), min(edit_end, pending_end)]
                    })
            
            return {
                'has_conflicts': len(conflicts) > 0,
                'conflicts': conflicts,
                'resolution_required': len(conflicts) > 0
            }
            
        except Exception as e:
            logger.error(f"Error detecting conflicts: {e}")
            return {
                'has_conflicts': False,
                'conflicts': [],
                'resolution_required': False
            }
    
    def resolve_conflicts(self, conflicts: List[Dict[str, Any]], resolution_strategy: str = 'last_writer_wins') -> Dict[str, Any]:
        """
        Resolve document editing conflicts.
        
        Args:
            conflicts: List of detected conflicts
            resolution_strategy: Strategy for resolving conflicts
            
        Returns:
            Dictionary with resolution results
        """
        try:
            resolved_conflicts = []
            
            for conflict in conflicts:
                if resolution_strategy == 'last_writer_wins':
                    # Simple strategy: last edit wins
                    resolved_conflicts.append({
                        'conflict_id': conflict.get('edit_id'),
                        'resolution': 'accepted',
                        'strategy': resolution_strategy
                    })
                elif resolution_strategy == 'merge':
                    # More complex merging would go here
                    resolved_conflicts.append({
                        'conflict_id': conflict.get('edit_id'),
                        'resolution': 'merged',
                        'strategy': resolution_strategy
                    })
            
            return {
                'success': True,
                'resolved_conflicts': resolved_conflicts,
                'strategy_used': resolution_strategy
            }
            
        except Exception as e:
            logger.error(f"Error resolving conflicts: {e}")
            return {
                'success': False,
                'error': str(e)
            }


def cleanup_inactive_sessions():
    """
    Utility function to clean up inactive collaboration sessions.
    This should be called periodically (e.g., via a cron job or Celery task).
    """
    try:
        cutoff_time = timezone.now() - timedelta(minutes=30)
        
        # Mark inactive sessions as ended
        updated_count = DocumentCollaborationSession.objects.filter(
            is_active=True,
            last_activity__lt=cutoff_time
        ).update(
            is_active=False,
            ended_at=timezone.now()
        )
        
        logger.info(f"Cleaned up {updated_count} inactive collaboration sessions")
        
        return {
            'success': True,
            'cleaned_up_sessions': updated_count
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up inactive sessions: {e}")
        return {
            'success': False,
            'error': str(e)
        }
"""