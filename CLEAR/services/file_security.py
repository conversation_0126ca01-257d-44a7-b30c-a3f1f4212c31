"""
File security validation service for CLEAR platform.

Provides comprehensive security scanning for uploaded files including:
- Malware detection patterns
- Suspicious content analysis  
- File structure validation
- Archive content scanning
"""

import logging
import re
import zipfile
from typing import Any, Dict
import magic
                import io

"""


logger = logging.getLogger(__name__)


class FileSecurityValidator:
    """
    Comprehensive file security validator for document uploads.
    
    Performs multiple layers of security validation:
    1. Content-based malware pattern detection
    2. File structure integrity checks
    3. Archive content scanning
    4. Suspicious filename detection
    """
    
    def __init__(self):
        # Suspicious file patterns that may indicate malware
        self.malware_signatures = [
            b'\x4d\x5a',  # PE executable header
            b'\x7f\x45\x4c\x46',  # ELF executable header
            b'\xfe\xed\xfa',  # Mach-O executable
            b'\xca\xfe\xba\xbe',  # Java class file
            b'<script',  # JavaScript in files
            b'javascript:',  # JavaScript URLs
            b'vbscript:',  # VBScript URLs
            b'eval(',  # Code evaluation
            b'exec(',  # Code execution
        ]
        
        # Suspicious filename patterns
        self.suspicious_filename_patterns = [
            r'\.exe$',
            r'\.bat$', 
            r'\.cmd$',
            r'\.com$',
            r'\.scr$',
            r'\.pif$',
            r'\.vbs$',
            r'\.js$',
            r'\.jar$',
            r'\.app$',
            r'\.dmg$',
            r'\.pkg$',
            r'\.deb$',
            r'\.rpm$',
            r'autorun\.inf$',
            r'desktop\.ini$',
        ]
        
        # Maximum archive depth to prevent zip bombs
        self.max_archive_depth = 3
        self.max_archive_files = 1000
        self.max_extracted_size = 500 * 1024 * 1024  # 500MB
    
    def scan_file(self, file) -> Dict[str, Any]:
        """
        Perform comprehensive security scan on uploaded file.
        
        Args:
            file: Django UploadedFile object
            
        Returns:
            Dict with 'safe' boolean and 'reason' string
        """
        try:
            file.seek(0)
            
            # Read file content for analysis
            content = file.read()
            file.seek(0)  # Reset file pointer
            
            # Get file info
            filename = file.name.lower()
            detected_mime = magic.from_buffer(content[:1024], mime=True)
            
            # 1. Check for suspicious filenames
            filename_result = self._check_suspicious_filename(filename)
            if not filename_result['safe']:
                return filename_result
            
            # 2. Check for malware signatures
            malware_result = self._check_malware_signatures(content)
            if not malware_result['safe']:
                return malware_result
            
            # 3. Check for embedded executables
            executable_result = self._check_embedded_executables(content)
            if not executable_result['safe']:
                return executable_result
            
            # 4. Validate file structure based on MIME type
            structure_result = self._validate_file_structure(content, detected_mime, filename)
            if not structure_result['safe']:
                return structure_result
            
            # 5. Special handling for archives
            if self._is_archive_file(detected_mime, filename):
                archive_result = self._scan_archive_content(content, detected_mime)
                if not archive_result['safe']:
                    return archive_result
            
            # 6. Check for suspicious content patterns
            content_result = self._check_suspicious_content(content, detected_mime)
            if not content_result['safe']:
                return content_result
            
            return {'safe': True, 'reason': 'File passed all security checks'}
            
        except Exception as e:
            logger.error(f"Error during file security scan: {e}")
            return {'safe': False, 'reason': f'Security scan failed: {str(e)}'}
    
    def _check_suspicious_filename(self, filename: str) -> Dict[str, Any]:
        """Check for suspicious filename patterns"""
        for pattern in self.suspicious_filename_patterns:
            if re.search(pattern, filename, re.IGNORECASE):
                return {
                    'safe': False,
                    'reason': f'Suspicious filename pattern detected: {pattern}'
                }
        
        # Check for double extensions (e.g., document.pdf.exe)
        if filename.count('.') > 1:
            parts = filename.split('.')
            if len(parts) > 2 and parts[-1].lower() in ['exe', 'bat', 'cmd', 'scr']:
                return {
                    'safe': False,
                    'reason': 'Double extension detected with executable type'
                }
        
        return {'safe': True, 'reason': 'Filename passed checks'}
    
    def _check_malware_signatures(self, content: bytes) -> Dict[str, Any]:
        """Check for known malware signatures in file content"""
        for signature in self.malware_signatures:
            if signature in content:
                return {
                    'safe': False,
                    'reason': f'Malware signature detected: {signature.hex()}'
                }
        
        return {'safe': True, 'reason': 'No malware signatures detected'}
    
    def _check_embedded_executables(self, content: bytes) -> Dict[str, Any]:
        """Check for embedded executable content"""
        # Look for PE header deep in the file (not just at the beginning)
        pe_header = b'\x4d\x5a'
        pe_positions = []
        start = 0
        while True:
            pos = content.find(pe_header, start)
            if pos == -1:
                break
            pe_positions.append(pos)
            start = pos + 1
        
        # If PE headers found beyond the beginning, it might be embedded
        if len(pe_positions) > 1 or (len(pe_positions) == 1 and pe_positions[0] > 1024):
            return {
                'safe': False,
                'reason': 'Embedded executable content detected'
            }
        
        return {'safe': True, 'reason': 'No embedded executables detected'}
    
    def _validate_file_structure(self, content: bytes, mime_type: str, filename: str) -> Dict[str, Any]:
        """Validate file structure matches expected format"""
        try:
            if mime_type == 'application/pdf':
                return self._validate_pdf_structure(content)
            elif mime_type.startswith('image/'):
                return self._validate_image_structure(content, mime_type)
            elif 'office' in mime_type or 'msword' in mime_type:
                return self._validate_office_structure(content)
            elif mime_type == 'application/zip':
                return self._validate_zip_structure(content)
            
            return {'safe': True, 'reason': 'File structure validation passed'}
            
        except Exception as e:
            return {
                'safe': False,
                'reason': f'File structure validation failed: {str(e)}'
            }
    
    def _validate_pdf_structure(self, content: bytes) -> Dict[str, Any]:
        """Validate PDF file structure"""
        if not content.startswith(b'%PDF-'):
            return {'safe': False, 'reason': 'Invalid PDF header'}
        
        # Check for suspicious JavaScript in PDF
        if b'/JavaScript' in content or b'/JS' in content:
            return {'safe': False, 'reason': 'PDF contains JavaScript'}
        
        # Check for form actions
        if b'/Action' in content and b'/URI' in content:
            return {'safe': False, 'reason': 'PDF contains external URI actions'}
        
        return {'safe': True, 'reason': 'PDF structure valid'}
    
    def _validate_image_structure(self, content: bytes, mime_type: str) -> Dict[str, Any]:
        """Validate image file structure"""
        if mime_type == 'image/jpeg':
            if not content.startswith(b'\xff\xd8\xff'):
                return {'safe': False, 'reason': 'Invalid JPEG header'}
        elif mime_type == 'image/png':
            if not content.startswith(b'\x89PNG\r\n\x1a\n'):
                return {'safe': False, 'reason': 'Invalid PNG header'}
        elif mime_type == 'image/gif':
            if not (content.startswith(b'GIF87a') or content.startswith(b'GIF89a')):
                return {'safe': False, 'reason': 'Invalid GIF header'}
        
        return {'safe': True, 'reason': 'Image structure valid'}
    
    def _validate_office_structure(self, content: bytes) -> Dict[str, Any]:
        """Validate Microsoft Office document structure"""
        # Modern Office documents are ZIP-based
        if content.startswith(b'PK'):
            try:
                with zipfile.ZipFile(io.BytesIO(content), 'r') as zip_file:
                    # Check for macro-enabled content
                    for name in zip_file.namelist():
                        if 'macro' in name.lower() or 'vba' in name.lower():
                            return {'safe': False, 'reason': 'Document contains macros'}
            except Exception:
                return {'safe': False, 'reason': 'Corrupted Office document'}
        
        return {'safe': True, 'reason': 'Office document structure valid'}
    
    def _validate_zip_structure(self, content: bytes) -> Dict[str, Any]:
        """Validate ZIP file structure and check for zip bombs"""
        try:
            with zipfile.ZipFile(io.BytesIO(content), 'r') as zip_file:
                total_size = 0
                file_count = 0
                
                for info in zip_file.infolist():
                    file_count += 1
                    total_size += info.file_size
                    
                    # Check for excessive compression ratio (potential zip bomb)
                    if info.compress_size > 0:
                        ratio = info.file_size / info.compress_size
                        if ratio > 100:  # Compression ratio over 100:1
                            return {'safe': False, 'reason': 'Suspicious compression ratio detected'}
                    
                    # Check file count limit
                    if file_count > self.max_archive_files:
                        return {'safe': False, 'reason': 'Archive contains too many files'}
                    
                    # Check total extracted size
                    if total_size > self.max_extracted_size:
                        return {'safe': False, 'reason': 'Archive extracted size too large'}
                
                return {'safe': True, 'reason': 'ZIP structure valid'}
                
        except Exception as e:
            return {'safe': False, 'reason': f'ZIP validation failed: {str(e)}'}
    
    def _is_archive_file(self, mime_type: str, filename: str) -> bool:
        """Check if file is an archive format"""
        archive_mimes = [
            'application/zip',
            'application/x-tar',
            'application/x-gzip',
            'application/x-bzip2',
            'application/x-7z-compressed'
        ]
        
        archive_extensions = ['.zip', '.tar', '.gz', '.bz2', '.7z']
        
        return (mime_type in archive_mimes or 
                any(filename.endswith(ext) for ext in archive_extensions))
    
    def _scan_archive_content(self, content: bytes, mime_type: str) -> Dict[str, Any]:
        """Scan contents of archive files for threats"""
        try:
            
            if mime_type == 'application/zip':
                return self._scan_zip_content(content)
            elif 'tar' in mime_type or 'gzip' in mime_type:
                return self._scan_tar_content(content)
            
            return {'safe': True, 'reason': 'Archive content scan completed'}
            
        except Exception as e:
            return {'safe': False, 'reason': f'Archive scan failed: {str(e)}'}
    
    def _scan_zip_content(self, content: bytes) -> Dict[str, Any]:
        """Scan ZIP archive contents"""
        try:
            with zipfile.ZipFile(io.BytesIO(content), 'r') as zip_file:
                for name in zip_file.namelist():
                    # Check for suspicious files in archive
                    filename_result = self._check_suspicious_filename(name.lower())
                    if not filename_result['safe']:
                        return {
                            'safe': False,
                            'reason': f'Suspicious file in archive: {name}'
                        }
                    
                    # Check for directory traversal attempts
                    if '..' in name or name.startswith('/'):
                        return {
                            'safe': False,
                            'reason': f'Directory traversal attempt in archive: {name}'
                        }
                
                return {'safe': True, 'reason': 'ZIP content scan passed'}
                
        except Exception as e:
            return {'safe': False, 'reason': f'ZIP content scan failed: {str(e)}'}
    
    def _scan_tar_content(self, content: bytes) -> Dict[str, Any]:
        """Scan TAR archive contents"""
        # For now, be conservative with TAR files
        return {'safe': False, 'reason': 'TAR archives not currently supported'}
    
    def _check_suspicious_content(self, content: bytes, mime_type: str) -> Dict[str, Any]:
        """Check for suspicious content patterns"""
        # Convert to string for text-based checks (if possible)
        try:
            text_content = content.decode('utf-8', errors='ignore').lower()
        except Exception:
            text_content = ""
        
        # Check for suspicious scripts
        script_patterns = [
            'javascript:',
            'vbscript:',
            '<script',
            'eval(',
            'document.write',
            'window.location',
            'shell.application',
            'wscript.shell',
            'activexobject'
        ]
        
        for pattern in script_patterns:
            if pattern in text_content:
                return {
                    'safe': False,
                    'reason': f'Suspicious script pattern detected: {pattern}'
                }
        
        # Check for suspicious URLs
        url_patterns = [
            'http://bit.ly',
            'http://tinyurl.com',
            'javascript:',
            'data:',
            'vbscript:'
        ]
        
        for pattern in url_patterns:
            if pattern in text_content:
                return {
                    'safe': False,
                    'reason': f'Suspicious URL pattern detected: {pattern}'
                }
        
        return {'safe': True, 'reason': 'Content scan passed'}
"""