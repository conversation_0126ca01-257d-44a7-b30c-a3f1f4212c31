"""
Performance monitoring and metrics collection for messaging system.

Provides comprehensive monitoring of message delivery, WebSocket connections,
database queries, and memory usage with alerting and reporting capabilities.
"""

import logging
import statistics
import threading
import time
from collections import defaultdict, deque
from typing import Any, Callable, Dict, List, Optional
import psutil
from django.conf import settings
from django.db import connection
from django.utils import timezone
from .connection_pool import get_connection_health
        import re

"""



logger = logging.getLogger(__name__)


class MetricsCollector:
    """Collects and stores performance metrics with time-series data"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.metrics = defaultdict(lambda: deque(maxlen=max_history_size))
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.timers = defaultdict(list)
        self.lock = threading.Lock()
        
    def record_timer(self, metric_name: str, duration: float, tags: Dict[str, str] = None):
        """Record timing metric"""
        with self.lock:
            timestamp = time.time()
            metric_data = {
                'value': duration,
                'timestamp': timestamp,
                'tags': tags or {}
            }
            self.metrics[f"timer.{metric_name}"].append(metric_data)
            self.timers[metric_name].append(duration)
            
            # Keep only last 100 timer values for statistics
            if len(self.timers[metric_name]) > 100:
                self.timers[metric_name] = self.timers[metric_name][-100:]
    
    def increment_counter(self, metric_name: str, value: int = 1, tags: Dict[str, str] = None):
        """Increment counter metric"""
        with self.lock:
            self.counters[metric_name] += value
            timestamp = time.time()
            metric_data = {
                'value': self.counters[metric_name],
                'timestamp': timestamp,
                'tags': tags or {}
            }
            self.metrics[f"counter.{metric_name}"].append(metric_data)
    
    def set_gauge(self, metric_name: str, value: float, tags: Dict[str, str] = None):
        """Set gauge metric value"""
        with self.lock:
            self.gauges[metric_name] = value
            timestamp = time.time()
            metric_data = {
                'value': value,
                'timestamp': timestamp,
                'tags': tags or {}
            }
            self.metrics[f"gauge.{metric_name}"].append(metric_data)
    
    def get_timer_stats(self, metric_name: str) -> Dict[str, float]:
        """Get statistical summary of timer metrics"""
        with self.lock:
            values = self.timers.get(metric_name, [])
            if not values:
                return {}
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'p95': self._percentile(values, 95),
                'p99': self._percentile(values, 99)
            }
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of values"""
        if not values:
            return 0.0
        sorted_values = sorted(values)
        index = int((percentile / 100.0) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def get_recent_metrics(self, metric_name: str, duration_minutes: int = 10) -> List[Dict]:
        """Get recent metrics within time window"""
        with self.lock:
            cutoff_time = time.time() - (duration_minutes * 60)
            recent_metrics = []
            
            for metric in self.metrics[metric_name]:
                if metric['timestamp'] > cutoff_time:
                    recent_metrics.append(metric)
            
            return recent_metrics
    
    def clear_old_metrics(self, hours_to_keep: int = 24):
        """Clear metrics older than specified hours"""
        with self.lock:
            cutoff_time = time.time() - (hours_to_keep * 3600)
            
            for metric_name, metric_data in self.metrics.items():
                # Remove old entries
                while metric_data and metric_data[0]['timestamp'] < cutoff_time:
                    metric_data.popleft()


class MessagePerformanceMonitor:
    """Monitor message delivery and processing performance"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.active_requests = {}
        self.websocket_connections = set()
        
    def start_message_timer(self, request_id: str, operation: str) -> str:
        """Start timing a message operation"""
        timer_id = f"{request_id}_{operation}_{int(time.time() * 1000)}"
        self.active_requests[timer_id] = {
            'start_time': time.time(),
            'operation': operation,
            'request_id': request_id
        }
        return timer_id
    
    def end_message_timer(self, timer_id: str, success: bool = True, tags: Dict[str, str] = None):
        """End timing a message operation"""
        if timer_id not in self.active_requests:
            return
        
        request_info = self.active_requests.pop(timer_id)
        duration = time.time() - request_info['start_time']
        
        operation = request_info['operation']
        metric_tags = {
            'operation': operation,
            'success': str(success),
            **(tags or {})
        }
        
        self.metrics.record_timer(f"message.{operation}.duration", duration, metric_tags)
        
        if success:
            self.metrics.increment_counter(f"message.{operation}.success", tags=metric_tags)
        else:
            self.metrics.increment_counter(f"message.{operation}.error", tags=metric_tags)
    
    def record_message_delivery(self, delivery_time: float, message_type: str = "text"):
        """Record message delivery metrics"""
        self.metrics.record_timer("message.delivery.time", delivery_time, {'type': message_type})
        self.metrics.increment_counter("message.delivered.total", tags={'type': message_type})
    
    def record_message_processing(self, processing_time: float, operation: str):
        """Record message processing metrics"""
        self.metrics.record_timer(f"message.processing.{operation}", processing_time)
        self.metrics.increment_counter(f"message.processed.{operation}")
    
    def record_websocket_connection(self, connection_id: str, event: str):
        """Record WebSocket connection events"""
        if event == "connect":
            self.websocket_connections.add(connection_id)
            self.metrics.increment_counter("websocket.connections.opened")
        elif event == "disconnect":
            self.websocket_connections.discard(connection_id)
            self.metrics.increment_counter("websocket.connections.closed")
        
        self.metrics.set_gauge("websocket.connections.active", len(self.websocket_connections))
    
    def record_pagination_metrics(self, page_size: int, fetch_time: float, total_count: int):
        """Record message pagination performance"""
        self.metrics.record_timer("message.pagination.fetch_time", fetch_time)
        self.metrics.set_gauge("message.pagination.page_size", page_size)
        self.metrics.set_gauge("message.pagination.total_count", total_count)
    
    def get_delivery_metrics(self) -> Dict[str, Any]:
        """Get message delivery performance summary"""
        return {
            'delivery_time_stats': self.metrics.get_timer_stats("message.delivery.time"),
            'total_delivered': self.metrics.counters.get("message.delivered.total", 0),
            'delivery_errors': self.metrics.counters.get("message.delivery.error", 0),
            'active_websockets': len(self.websocket_connections),
            'websocket_opens': self.metrics.counters.get("websocket.connections.opened", 0),
            'websocket_closes': self.metrics.counters.get("websocket.connections.closed", 0)
        }


class DatabasePerformanceMonitor:
    """Monitor database query performance and connection health"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.slow_query_threshold = 1.0  # seconds
        
    def record_query_execution(self, query: str, execution_time: float, query_type: str = "unknown"):
        """Record database query execution metrics"""
        self.metrics.record_timer("database.query.execution_time", execution_time, {
            'type': query_type,
            'slow': str(execution_time > self.slow_query_threshold)
        })
        
        if execution_time > self.slow_query_threshold:
            self.metrics.increment_counter("database.query.slow", tags={'type': query_type})
            logger.warning(f"Slow query detected ({execution_time:.2f}s): {query[:100]}...")
        
        self.metrics.increment_counter("database.query.total", tags={'type': query_type})
    
    def analyze_django_queries(self) -> Dict[str, Any]:
        """Analyze Django's query log for performance insights"""
        if not settings.DEBUG:
            return {"error": "Query analysis only available in DEBUG mode"}
        
        queries = connection.queries
        if not queries:
            return {"total_queries": 0}
        
        total_time = sum(float(q['time']) for q in queries)
        query_times = [float(q['time']) for q in queries]
        
        # Categorize queries
        slow_queries = [q for q in queries if float(q['time']) > self.slow_query_threshold]
        duplicate_queries = self._find_duplicate_queries(queries)
        
        analysis = {
            'total_queries': len(queries),
            'total_time': total_time,
            'average_time': total_time / len(queries) if queries else 0,
            'slowest_query_time': max(query_times) if query_times else 0,
            'slow_queries_count': len(slow_queries),
            'duplicate_queries_count': len(duplicate_queries),
            'queries_by_table': self._categorize_queries_by_table(queries)
        }
        
        # Record metrics
        self.metrics.set_gauge("database.queries.total", len(queries))
        self.metrics.set_gauge("database.queries.total_time", total_time)
        self.metrics.set_gauge("database.queries.slow_count", len(slow_queries))
        
        return analysis
    
    def _find_duplicate_queries(self, queries: List[Dict]) -> List[Dict]:
        """Find duplicate queries in the query log"""
        query_counts = defaultdict(int)
        for query in queries:
            # Normalize query by removing parameter values
            normalized = self._normalize_query(query['sql'])
            query_counts[normalized] += 1
        
        return [{'query': q, 'count': c} for q, c in query_counts.items() if c > 1]
    
    def _normalize_query(self, sql: str) -> str:
        """Normalize SQL query by removing parameter values"""
        # Replace numeric parameters
        sql = re.sub(r'\b\d+\b', '?', sql)
        # Replace string parameters (basic)
        sql = re.sub(r"'[^']*'", "'?'", sql)
        return sql
    
    def _categorize_queries_by_table(self, queries: List[Dict]) -> Dict[str, int]:
        """Categorize queries by affected table"""
        table_counts = defaultdict(int)
        for query in queries:
            sql = query['sql'].lower()
            # Extract table names (basic pattern matching)
            if 'from ' in sql:
                parts = sql.split('from ')[1].split()
                if parts:
                    table_name = parts[0].strip('`"').split('.')[1] if '.' in parts[0] else parts[0].strip('`"')
                    table_counts[table_name] += 1
            elif 'insert into ' in sql:
                parts = sql.split('insert into ')[1].split()
                if parts:
                    table_name = parts[0].strip('`"').split('.')[1] if '.' in parts[0] else parts[0].strip('`"')
                    table_counts[table_name] += 1
            elif 'update ' in sql:
                parts = sql.split('update ')[1].split()
                if parts:
                    table_name = parts[0].strip('`"').split('.')[1] if '.' in parts[0] else parts[0].strip('`"')
                    table_counts[table_name] += 1
        
        return dict(table_counts)
    
    def get_connection_health(self) -> Dict[str, Any]:
        """Get database connection health metrics"""
        try:
            health_data = get_connection_health()
            
            # Record health metrics
            for alias, db_health in health_data.get('database_health', {}).items():
                if 'response_time_ms' in db_health:
                    self.metrics.record_timer(
                        "database.connection.response_time",
                        db_health['response_time_ms'] / 1000,  # Convert to seconds
                        {'alias': alias}
                    )
                
                status = db_health.get('status', 'unknown')
                self.metrics.set_gauge("database.connection.healthy", 1 if status == 'healthy' else 0, {'alias': alias})
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting database connection health: {e}")
            return {'error': str(e)}


class SystemResourceMonitor:
    """Monitor system resources like memory, CPU, and disk usage"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        
    def collect_system_metrics(self):
        """Collect current system resource metrics"""
        try:
            # Memory usage
            memory = psutil.virtual_memory()
            self.metrics.set_gauge("system.memory.total", memory.total)
            self.metrics.set_gauge("system.memory.available", memory.available)
            self.metrics.set_gauge("system.memory.percent", memory.percent)
            self.metrics.set_gauge("system.memory.used", memory.used)
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics.set_gauge("system.cpu.percent", cpu_percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.metrics.set_gauge("system.disk.total", disk.total)
            self.metrics.set_gauge("system.disk.used", disk.used)
            self.metrics.set_gauge("system.disk.free", disk.free)
            self.metrics.set_gauge("system.disk.percent", (disk.used / disk.total) * 100)
            
            # Process-specific metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            self.metrics.set_gauge("process.memory.rss", process_memory.rss)
            self.metrics.set_gauge("process.memory.vms", process_memory.vms)
            self.metrics.set_gauge("process.cpu.percent", process.cpu_percent())
            
            # Network I/O
            network = psutil.net_io_counters()
            self.metrics.set_gauge("system.network.bytes_sent", network.bytes_sent)
            self.metrics.set_gauge("system.network.bytes_recv", network.bytes_recv)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get current resource usage summary"""
        try:
            return {
                'memory_percent': self.metrics.gauges.get("system.memory.percent", 0),
                'cpu_percent': self.metrics.gauges.get("system.cpu.percent", 0),
                'disk_percent': self.metrics.gauges.get("system.disk.percent", 0),
                'process_memory_mb': self.metrics.gauges.get("process.memory.rss", 0) / (1024 * 1024),
                'process_cpu_percent': self.metrics.gauges.get("process.cpu.percent", 0)
            }
        except Exception as e:
            logger.error(f"Error getting resource summary: {e}")
            return {'error': str(e)}


class PerformanceAlertManager:
    """Manage performance alerts and thresholds"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.alert_thresholds = {
            'message_delivery_time_p95': 2.0,  # seconds
            'database_query_time_p95': 1.0,    # seconds
            'memory_usage_percent': 85.0,       # percent
            'cpu_usage_percent': 80.0,          # percent
            'websocket_error_rate': 5.0,        # percent
            'slow_query_rate': 10.0,            # percent
        }
        self.alert_cooldown = 300  # 5 minutes between similar alerts
        self.last_alerts = {}
        
    def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """Check for performance issues and generate alerts"""
        alerts = []
        current_time = time.time()
        
        try:
            # Check message delivery performance
            delivery_stats = self.metrics.get_timer_stats("message.delivery.time")
            if delivery_stats and delivery_stats.get('p95', 0) > self.alert_thresholds['message_delivery_time_p95']:
                alert = self._create_alert(
                    'message_delivery_slow',
                    'high',
                    f"Message delivery P95 time ({delivery_stats['p95']:.2f}s) exceeds threshold",
                    {'p95_time': delivery_stats['p95']}
                )
                if alert:
                    alerts.append(alert)
            
            # Check database query performance
            query_stats = self.metrics.get_timer_stats("database.query.execution_time")
            if query_stats and query_stats.get('p95', 0) > self.alert_thresholds['database_query_time_p95']:
                alert = self._create_alert(
                    'database_queries_slow',
                    'high',
                    f"Database query P95 time ({query_stats['p95']:.2f}s) exceeds threshold",
                    {'p95_time': query_stats['p95']}
                )
                if alert:
                    alerts.append(alert)
            
            # Check memory usage
            memory_percent = self.metrics.gauges.get("system.memory.percent", 0)
            if memory_percent > self.alert_thresholds['memory_usage_percent']:
                alert = self._create_alert(
                    'high_memory_usage',
                    'medium',
                    f"Memory usage ({memory_percent:.1f}%) exceeds threshold",
                    {'memory_percent': memory_percent}
                )
                if alert:
                    alerts.append(alert)
            
            # Check CPU usage
            cpu_percent = self.metrics.gauges.get("system.cpu.percent", 0)
            if cpu_percent > self.alert_thresholds['cpu_usage_percent']:
                alert = self._create_alert(
                    'high_cpu_usage',
                    'medium',
                    f"CPU usage ({cpu_percent:.1f}%) exceeds threshold",
                    {'cpu_percent': cpu_percent}
                )
                if alert:
                    alerts.append(alert)
            
            # Check slow query rate
            total_queries = self.metrics.counters.get("database.query.total", 0)
            slow_queries = self.metrics.counters.get("database.query.slow", 0)
            if total_queries > 0:
                slow_query_rate = (slow_queries / total_queries) * 100
                if slow_query_rate > self.alert_thresholds['slow_query_rate']:
                    alert = self._create_alert(
                        'high_slow_query_rate',
                        'high',
                        f"Slow query rate ({slow_query_rate:.1f}%) exceeds threshold",
                        {'slow_query_rate': slow_query_rate}
                    )
                    if alert:
                        alerts.append(alert)
            
        except Exception as e:
            logger.error(f"Error checking performance alerts: {e}")
            alerts.append({
                'type': 'system_error',
                'severity': 'high',
                'message': f"Error in alert checking: {str(e)}",
                'timestamp': current_time
            })
        
        return alerts
    
    def _create_alert(self, alert_type: str, severity: str, message: str, metadata: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Create alert if not in cooldown period"""
        current_time = time.time()
        last_alert_time = self.last_alerts.get(alert_type, 0)
        
        if current_time - last_alert_time < self.alert_cooldown:
            return None  # Still in cooldown
        
        self.last_alerts[alert_type] = current_time
        
        alert = {
            'type': alert_type,
            'severity': severity,
            'message': message,
            'timestamp': current_time,
            'metadata': metadata or {}
        }
        
        # Log the alert
        log_level = logging.ERROR if severity == 'high' else logging.WARNING
        logger.log(log_level, f"Performance Alert [{severity.upper()}]: {message}")
        
        return alert


class PerformanceReporter:
    """Generate performance reports and summaries"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        
    def generate_hourly_report(self) -> Dict[str, Any]:
        """Generate hourly performance report"""
        current_time = timezone.now()
        
        report = {
            'report_time': current_time.isoformat(),
            'period': 'hourly',
            'message_metrics': {
                'delivery_stats': self.metrics.get_timer_stats("message.delivery.time"),
                'total_delivered': self.metrics.counters.get("message.delivered.total", 0),
                'processing_stats': self.metrics.get_timer_stats("message.processing.send"),
            },
            'database_metrics': {
                'query_stats': self.metrics.get_timer_stats("database.query.execution_time"),
                'total_queries': self.metrics.counters.get("database.query.total", 0),
                'slow_queries': self.metrics.counters.get("database.query.slow", 0),
            },
            'system_metrics': {
                'memory_percent': self.metrics.gauges.get("system.memory.percent", 0),
                'cpu_percent': self.metrics.gauges.get("system.cpu.percent", 0),
                'disk_percent': self.metrics.gauges.get("system.disk.percent", 0),
            },
            'websocket_metrics': {
                'active_connections': self.metrics.gauges.get("websocket.connections.active", 0),
                'connections_opened': self.metrics.counters.get("websocket.connections.opened", 0),
                'connections_closed': self.metrics.counters.get("websocket.connections.closed", 0),
            }
        }
        
        return report
    
    def generate_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Generate performance summary for specified time period"""
        cutoff_time = time.time() - (hours * 3600)
        
        # Get recent metrics
        recent_delivery_times = []
        recent_query_times = []
        
        for metric in self.metrics.metrics.get("timer.message.delivery.time", []):
            if metric['timestamp'] > cutoff_time:
                recent_delivery_times.append(metric['value'])
        
        for metric in self.metrics.metrics.get("timer.database.query.execution_time", []):
            if metric['timestamp'] > cutoff_time:
                recent_query_times.append(metric['value'])
        
        summary = {
            'period_hours': hours,
            'summary_time': timezone.now().isoformat(),
            'message_delivery': {
                'count': len(recent_delivery_times),
                'avg_time': statistics.mean(recent_delivery_times) if recent_delivery_times else 0,
                'max_time': max(recent_delivery_times) if recent_delivery_times else 0,
                'p95_time': self._percentile(recent_delivery_times, 95) if recent_delivery_times else 0,
            },
            'database_queries': {
                'count': len(recent_query_times),
                'avg_time': statistics.mean(recent_query_times) if recent_query_times else 0,
                'max_time': max(recent_query_times) if recent_query_times else 0,
                'p95_time': self._percentile(recent_query_times, 95) if recent_query_times else 0,
            },
            'system_health': {
                'memory_usage': self.metrics.gauges.get("system.memory.percent", 0),
                'cpu_usage': self.metrics.gauges.get("system.cpu.percent", 0),
                'disk_usage': self.metrics.gauges.get("system.disk.percent", 0),
            }
        }
        
        return summary
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of values"""
        if not values:
            return 0.0
        sorted_values = sorted(values)
        index = int((percentile / 100.0) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]


# Global performance monitoring instances
metrics_collector = MetricsCollector()
message_monitor = MessagePerformanceMonitor(metrics_collector)
database_monitor = DatabasePerformanceMonitor(metrics_collector)
system_monitor = SystemResourceMonitor(metrics_collector)
alert_manager = PerformanceAlertManager(metrics_collector)
performance_reporter = PerformanceReporter(metrics_collector)


# Decorator for timing message operations
def monitor_message_operation(operation_name: str):
    """Decorator to monitor message operation performance"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            request_id = f"{func.__name__}_{int(time.time() * 1000)}"
            timer_id = message_monitor.start_message_timer(request_id, operation_name)
            
            try:
                result = func(*args, **kwargs)
                message_monitor.end_message_timer(timer_id, success=True)
                return result
            except Exception as e:
                message_monitor.end_message_timer(timer_id, success=False, tags={'error': str(e)})
                raise
        
        return wrapper
    return decorator


# Context manager for timing database operations
class DatabaseOperationTimer:
    """Context manager for timing database operations"""
    
    def __init__(self, operation_type: str):
        self.operation_type = operation_type
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            database_monitor.record_query_execution(
                query=f"Operation: {self.operation_type}",
                execution_time=duration,
                query_type=self.operation_type
            )


# Utility functions
def start_performance_monitoring():
    """Start background performance monitoring"""
    def monitoring_loop():
        while True:
            try:
                # Collect system metrics every 60 seconds
                system_monitor.collect_system_metrics()
                
                # Check for alerts every 30 seconds
                alert_manager.check_performance_alerts()
                
                # Clean old metrics every hour
                metrics_collector.clear_old_metrics(24)
                
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(60)
    
    monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
    monitor_thread.start()
    logger.info("Performance monitoring started")


def get_performance_dashboard_data() -> Dict[str, Any]:
    """Get comprehensive performance data for dashboard"""
    return {
        'message_metrics': message_monitor.get_delivery_metrics(),
        'database_health': database_monitor.get_connection_health(),
        'system_resources': system_monitor.get_resource_summary(),
        'recent_alerts': alert_manager.check_performance_alerts(),
        'performance_summary': performance_reporter.generate_performance_summary(1)  # Last hour
    }
"""