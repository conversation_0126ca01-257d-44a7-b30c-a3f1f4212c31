"""
Comprehensive caching strategies for messaging system.

Provides intelligent caching for messages, conversations, user status,
and conversation lists with automatic invalidation and performance optimization.
"""

import hashlib
import logging
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional
from django.core.cache import cache, caches
from django.utils import timezone
from ..models import ChatMessage, Conversation

"""



logger = logging.getLogger(__name__)


class MessageCacheManager:
    """Advanced caching manager for messaging system with intelligent invalidation"""
    
    def __init__(self):
        # Use dedicated message cache if available
        try:
            self.cache = caches['messages']
        except Exception:
            self.cache = cache
        
        self.default_timeout = 1800  # 30 minutes
        self.short_timeout = 300     # 5 minutes
        self.long_timeout = 3600     # 1 hour
        
        # Cache key prefixes for organized management
        self.prefixes = {
            'message': 'msg',
            'conversation': 'conv',
            'conversation_list': 'conv_list',
            'user_status': 'user_status',
            'unread_count': 'unread',
            'typing': 'typing',
            'thread': 'thread',
            'search': 'search'
        }
    
    def get_message_cache_key(self, message_id: int) -> str:
        """Generate cache key for individual message"""
        return f"{self.prefixes['message']}_{message_id}"
    
    def get_conversation_cache_key(self, conversation_id: str, user_id: int, params: str = "") -> str:
        """Generate cache key for conversation messages"""
        params_hash = hashlib.md5(params.encode()).hexdigest()[:8] if params else ""
        return f"{self.prefixes['conversation']}_{conversation_id}_{user_id}_{params_hash}"
    
    def get_conversation_list_key(self, user_id: int, filters: str = "") -> str:
        """Generate cache key for conversation list"""
        filters_hash = hashlib.md5(filters.encode()).hexdigest()[:8] if filters else ""
        return f"{self.prefixes['conversation_list']}_{user_id}_{filters_hash}"
    
    def get_user_status_key(self, user_id: int) -> str:
        """Generate cache key for user status"""
        return f"{self.prefixes['user_status']}_{user_id}"
    
    def get_unread_count_key(self, user_id: int, conversation_id: str = None) -> str:
        """Generate cache key for unread counts"""
        if conversation_id:
            return f"{self.prefixes['unread_count']}_{user_id}_{conversation_id}"
        return f"{self.prefixes['unread_count']}_{user_id}_total"
    
    def get_typing_key(self, conversation_id: str) -> str:
        """Generate cache key for typing indicators"""
        return f"{self.prefixes['typing']}_{conversation_id}"
    
    def get_thread_key(self, parent_message_id: int, user_id: int) -> str:
        """Generate cache key for message threads"""
        return f"{self.prefixes['thread']}_{parent_message_id}_{user_id}"
    
    def get_search_key(self, query: str, user_id: int, filters: str = "") -> str:
        """Generate cache key for search results"""
        query_hash = hashlib.md5(f"{query}_{filters}".encode()).hexdigest()[:12]
        return f"{self.prefixes['search']}_{user_id}_{query_hash}"
    
    # Message Caching Methods
    
    def cache_message(self, message: ChatMessage, timeout: int = None) -> None:
        """Cache individual message with processed data"""
        timeout = timeout or self.default_timeout
        
        try:
            message_data = {
                'id': message.id,
                'content': message.content,
                'user_id': message.user_id,
                'conversation_id': str(message.conversation_id) if message.conversation_id else None,
                'project_id': message.project_id,
                'created_at': message.created_at.isoformat(),
                'updated_at': message.updated_at.isoformat(),
                'message_type': message.message_type,
                'is_urgent': message.is_urgent,
                'reply_to_id': message.reply_to_id,
                'channel': message.channel,
                # Pre-computed counts for performance
                'reply_count': message.get_thread_count(),
                'read_count': message.get_read_count(),
            }
            
            # Cache user data for display
            message_data['user'] = {
                'id': message.user.id,
                'username': message.user.username,
                'full_name': message.user.get_full_name(),
                'initials': message.user.get_initials(),
            }
            
            # Cache attachment data if any
            if hasattr(message, 'attachments'):
                message_data['attachments'] = [
                    {
                        'id': att.id,
                        'name': att.name,
                        'file_type': att.file_type,
                        'file_size': att.file_size,
                    }
                    for att in message.attachments.all()
                ]
            
            cache_key = self.get_message_cache_key(message.id)
            self.cache.set(cache_key, message_data, timeout)
            
        except Exception as e:
            logger.error(f"Error caching message {message.id}: {e}")
    
    def get_cached_message(self, message_id: int) -> Optional[Dict]:
        """Get cached message data"""
        try:
            cache_key = self.get_message_cache_key(message_id)
            return self.cache.get(cache_key)
        except Exception as e:
            logger.error(f"Error getting cached message {message_id}: {e}")
            return None
    
    def invalidate_message(self, message_id: int) -> None:
        """Invalidate cached message"""
        try:
            cache_key = self.get_message_cache_key(message_id)
            self.cache.delete(cache_key)
        except Exception as e:
            logger.error(f"Error invalidating message {message_id}: {e}")
    
    # Conversation Caching Methods
    
    def cache_conversation_messages(
        self, 
        conversation_id: str, 
        user_id: int, 
        messages: List[Dict], 
        pagination_info: Dict,
        params: str = "",
        timeout: int = None
    ) -> None:
        """Cache conversation messages with pagination info"""
        timeout = timeout or self.default_timeout
        
        try:
            cache_data = {
                'messages': messages,
                'pagination': pagination_info,
                'cached_at': timezone.now().isoformat(),
                'params': params
            }
            
            cache_key = self.get_conversation_cache_key(conversation_id, user_id, params)
            self.cache.set(cache_key, cache_data, timeout)
            
        except Exception as e:
            logger.error(f"Error caching conversation {conversation_id}: {e}")
    
    def get_cached_conversation_messages(
        self, 
        conversation_id: str, 
        user_id: int, 
        params: str = ""
    ) -> Optional[Dict]:
        """Get cached conversation messages"""
        try:
            cache_key = self.get_conversation_cache_key(conversation_id, user_id, params)
            return self.cache.get(cache_key)
        except Exception as e:
            logger.error(f"Error getting cached conversation {conversation_id}: {e}")
            return None
    
    def invalidate_conversation(self, conversation_id: str) -> None:
        """Invalidate all cached data for a conversation"""
        try:
            # This is a simplified invalidation - in production, you might want
            # to use Redis SCAN to find all keys matching the pattern
            [
                f"{self.prefixes['conversation']}_{conversation_id}_*",
                f"{self.prefixes['unread_count']}_*_{conversation_id}",
                f"{self.prefixes['typing']}_{conversation_id}"
            ]
            
            # For now, we'll invalidate specific known patterns
            # In a full Redis implementation, you'd use pattern deletion
            
        except Exception as e:
            logger.error(f"Error invalidating conversation {conversation_id}: {e}")
    
    # User Status Caching Methods
    
    def cache_user_status(self, user_id: int, status_data: Dict, timeout: int = None) -> None:
        """Cache user online/offline status and activity"""
        timeout = timeout or self.short_timeout
        
        try:
            status_data['last_updated'] = timezone.now().isoformat()
            cache_key = self.get_user_status_key(user_id)
            self.cache.set(cache_key, status_data, timeout)
            
        except Exception as e:
            logger.error(f"Error caching user status {user_id}: {e}")
    
    def get_cached_user_status(self, user_id: int) -> Optional[Dict]:
        """Get cached user status"""
        try:
            cache_key = self.get_user_status_key(user_id)
            return self.cache.get(cache_key)
        except Exception as e:
            logger.error(f"Error getting cached user status {user_id}: {e}")
            return None
    
    def get_multiple_user_statuses(self, user_ids: List[int]) -> Dict[int, Dict]:
        """Get multiple user statuses efficiently"""
        try:
            cache_keys = [self.get_user_status_key(uid) for uid in user_ids]
            cached_data = self.cache.get_many(cache_keys)
            
            # Map cache keys back to user IDs
            result = {}
            for user_id in user_ids:
                key = self.get_user_status_key(user_id)
                if key in cached_data:
                    result[user_id] = cached_data[key]
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting multiple user statuses: {e}")
            return {}
    
    # Unread Count Caching Methods
    
    def cache_unread_count(
        self, 
        user_id: int, 
        count: int, 
        conversation_id: str = None,
        timeout: int = None
    ) -> None:
        """Cache unread message counts"""
        timeout = timeout or self.default_timeout
        
        try:
            cache_key = self.get_unread_count_key(user_id, conversation_id)
            count_data = {
                'count': count,
                'last_updated': timezone.now().isoformat()
            }
            self.cache.set(cache_key, count_data, timeout)
            
        except Exception as e:
            logger.error(f"Error caching unread count for user {user_id}: {e}")
    
    def get_cached_unread_count(self, user_id: int, conversation_id: str = None) -> Optional[int]:
        """Get cached unread count"""
        try:
            cache_key = self.get_unread_count_key(user_id, conversation_id)
            count_data = self.cache.get(cache_key)
            return count_data['count'] if count_data else None
        except Exception as e:
            logger.error(f"Error getting cached unread count for user {user_id}: {e}")
            return None
    
    def invalidate_unread_counts(self, user_id: int, conversation_id: str = None) -> None:
        """Invalidate unread counts for user"""
        try:
            if conversation_id:
                # Invalidate specific conversation unread count
                cache_key = self.get_unread_count_key(user_id, conversation_id)
                self.cache.delete(cache_key)
            
            # Always invalidate total unread count
            total_key = self.get_unread_count_key(user_id)
            self.cache.delete(total_key)
            
        except Exception as e:
            logger.error(f"Error invalidating unread counts for user {user_id}: {e}")
    
    # Conversation List Caching Methods
    
    def cache_conversation_list(
        self, 
        user_id: int, 
        conversations: List[Dict], 
        filters: str = "",
        timeout: int = None
    ) -> None:
        """Cache user's conversation list"""
        timeout = timeout or self.default_timeout
        
        try:
            list_data = {
                'conversations': conversations,
                'cached_at': timezone.now().isoformat(),
                'filters': filters
            }
            
            cache_key = self.get_conversation_list_key(user_id, filters)
            self.cache.set(cache_key, list_data, timeout)
            
        except Exception as e:
            logger.error(f"Error caching conversation list for user {user_id}: {e}")
    
    def get_cached_conversation_list(self, user_id: int, filters: str = "") -> Optional[List[Dict]]:
        """Get cached conversation list"""
        try:
            cache_key = self.get_conversation_list_key(user_id, filters)
            list_data = self.cache.get(cache_key)
            return list_data['conversations'] if list_data else None
        except Exception as e:
            logger.error(f"Error getting cached conversation list for user {user_id}: {e}")
            return None
    
    def invalidate_conversation_list(self, user_id: int) -> None:
        """Invalidate conversation list for user"""
        try:
            # Invalidate all conversation list variations for this user
            # In production, you'd use pattern matching to delete all variations
            f"{self.prefixes['conversation_list']}_{user_id}_"
            # For now, just delete the main list
            main_key = self.get_conversation_list_key(user_id)
            self.cache.delete(main_key)
            
        except Exception as e:
            logger.error(f"Error invalidating conversation list for user {user_id}: {e}")
    
    # Typing Indicator Caching Methods
    
    def set_typing_status(self, conversation_id: str, user_id: int, is_typing: bool) -> None:
        """Set typing status for user in conversation"""
        try:
            cache_key = self.get_typing_key(conversation_id)
            typing_users = self.cache.get(cache_key, [])
            
            # Remove user from typing list
            typing_users = [u for u in typing_users if u['user_id'] != user_id]
            
            if is_typing:
                # Add user to typing list
                typing_users.append({
                    'user_id': user_id,
                    'timestamp': timezone.now().isoformat()
                })
            
            # Cache for short time (typing indicators are ephemeral)
            self.cache.set(cache_key, typing_users, 10)  # 10 seconds
            
        except Exception as e:
            logger.error(f"Error setting typing status for user {user_id}: {e}")
    
    def get_typing_users(self, conversation_id: str) -> List[Dict]:
        """Get users currently typing in conversation"""
        try:
            cache_key = self.get_typing_key(conversation_id)
            typing_users = self.cache.get(cache_key, [])
            
            # Filter out old typing indicators (older than 10 seconds)
            current_time = timezone.now()
            cutoff_time = current_time - timedelta(seconds=10)
            
            active_typing = []
            for user_typing in typing_users:
                timestamp = timezone.datetime.fromisoformat(user_typing['timestamp'].replace('Z', '+00:00'))
                if timestamp > cutoff_time:
                    active_typing.append(user_typing)
            
            # Update cache with filtered list
            if len(active_typing) != len(typing_users):
                self.cache.set(cache_key, active_typing, 10)
            
            return active_typing
            
        except Exception as e:
            logger.error(f"Error getting typing users for conversation {conversation_id}: {e}")
            return []
    
    # Search Result Caching Methods
    
    def cache_search_results(
        self, 
        query: str, 
        user_id: int, 
        results: List[Dict],
        filters: str = "",
        timeout: int = None
    ) -> None:
        """Cache search results"""
        timeout = timeout or self.short_timeout  # Search results cached for shorter time
        
        try:
            search_data = {
                'results': results,
                'query': query,
                'filters': filters,
                'cached_at': timezone.now().isoformat()
            }
            
            cache_key = self.get_search_key(query, user_id, filters)
            self.cache.set(cache_key, search_data, timeout)
            
        except Exception as e:
            logger.error(f"Error caching search results for query '{query}': {e}")
    
    def get_cached_search_results(
        self, 
        query: str, 
        user_id: int, 
        filters: str = ""
    ) -> Optional[List[Dict]]:
        """Get cached search results"""
        try:
            cache_key = self.get_search_key(query, user_id, filters)
            search_data = self.cache.get(cache_key)
            return search_data['results'] if search_data else None
        except Exception as e:
            logger.error(f"Error getting cached search results for query '{query}': {e}")
            return None
    
    # Bulk Cache Operations
    
    def warm_user_cache(self, user_id: int) -> None:
        """Pre-warm cache with user's frequently accessed data"""
        try:
            # Cache user's active conversations
            conversations = Conversation.objects.filter(
                participants__id=user_id,
                is_active=True
            ).select_related('created_by').prefetch_related('participants').order_by('-last_message_at')[:10]
            
            conversation_data = []
            for conv in conversations:
                # Pre-cache conversation metadata
                conv_data = {
                    'id': str(conv.id),
                    'name': str(conv),
                    'type': conv.conversation_type,
                    'last_message_at': conv.last_message_at.isoformat() if conv.last_message_at else None,
                    'participant_count': conv.participants.count(),
                    'unread_count': conv.get_unread_count_for_user_id(user_id)
                }
                conversation_data.append(conv_data)
                
                # Cache unread count for each conversation
                self.cache_unread_count(user_id, conv_data['unread_count'], str(conv.id))
            
            # Cache conversation list
            self.cache_conversation_list(user_id, conversation_data)
            
            # Cache total unread count
            total_unread = sum(conv['unread_count'] for conv in conversation_data)
            self.cache_unread_count(user_id, total_unread)
            
            # Cache user status
            self.cache_user_status(user_id, {
                'is_online': True,
                'last_seen': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error warming cache for user {user_id}: {e}")
    
    def bulk_invalidate_user_cache(self, user_id: int) -> None:
        """Bulk invalidate all cache entries for a user"""
        try:
            self.invalidate_conversation_list(user_id)
            self.invalidate_unread_counts(user_id)
            
            # Invalidate user status
            cache_key = self.get_user_status_key(user_id)
            self.cache.delete(cache_key)
            
        except Exception as e:
            logger.error(f"Error bulk invalidating cache for user {user_id}: {e}")
    
    # Cache Statistics and Health Methods
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache usage statistics"""
        try:
            if hasattr(self.cache, '_cache') and hasattr(self.cache._cache, 'info'):
                # Redis cache stats
                info = self.cache._cache.info()
                return {
                    'cache_type': 'redis',
                    'used_memory': info.get('used_memory_human', 'Unknown'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'connected_clients': info.get('connected_clients', 0),
                }
            else:
                # Local memory cache or other type
                return {
                    'cache_type': 'local_memory',
                    'status': 'active'
                }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}
    
    def clear_all_message_cache(self) -> None:
        """Clear all message-related cache (use with caution)"""
        try:
            # In production, you'd use Redis SCAN to find and delete keys by pattern
            # For now, this is a placeholder
            logger.warning("Cache clear requested - implement pattern-based deletion for production")
        except Exception as e:
            logger.error(f"Error clearing message cache: {e}")


# Global cache manager instance
message_cache = MessageCacheManager()


# Helper methods for models
def add_cache_methods_to_models():
    """Add caching methods to model classes"""
    
    def get_unread_count_for_user_id(self, user_id: int) -> int:
        """Cached version of unread count calculation"""
        # Try cache first
        cached_count = message_cache.get_cached_unread_count(user_id, str(self.id))
        if cached_count is not None:
            return cached_count
        
        # Calculate and cache
        last_read = self.members.filter(user_id=user_id).first()
        if not last_read or not last_read.last_read_at:
            count = self.messages.count()
        else:
            count = self.messages.filter(created_at__gt=last_read.last_read_at).count()
        
        message_cache.cache_unread_count(user_id, count, str(self.id))
        return count
    
    # Add method to Conversation model
    Conversation.get_unread_count_for_user_id = get_unread_count_for_user_id


# Cache invalidation signal handlers
def invalidate_message_cache_on_save(sender, instance, created, **kwargs):
    """Invalidate relevant cache when message is saved"""
    if created:
        # New message - invalidate conversation and user caches
        if instance.conversation_id:
            message_cache.invalidate_conversation(str(instance.conversation_id))
            
            # Invalidate unread counts for all conversation participants
            participants = instance.conversation.participants.all()
            for user in participants:
                message_cache.invalidate_unread_counts(user.id, str(instance.conversation_id))
                message_cache.invalidate_conversation_list(user.id)
        
        # Cache the new message
        message_cache.cache_message(instance)
    else:
        # Message updated - just invalidate the specific message
        message_cache.invalidate_message(instance.id)


def invalidate_conversation_cache_on_save(sender, instance, created, **kwargs):
    """Invalidate conversation cache when conversation is updated"""
    message_cache.invalidate_conversation(str(instance.id))
    
    # Invalidate conversation lists for all participants
    for user in instance.participants.all():
        message_cache.invalidate_conversation_list(user.id)


# Initialize caching methods
add_cache_methods_to_models()
"""