from typing import List, Dict, Any, Optional
from typing import Any, Dict, List
import logging
import os
import threading
import time
from contextlib import contextmanager
from typing import Optional
import redis
from django.conf import settings
from django.core.cache import cache
from django.db import connections
from redis.connection import ConnectionPool
        from urllib.parse import urlparse


"""
Connection pooling service for database and Redis connections.

Provides optimized connection management for high-volume messaging operations
with health monitoring and automatic failover capabilities.
"""


logger = logging.getLogger(__name__)


class DatabaseConnectionPoolManager:
    """Manages database connections with health monitoring and optimization"""
    
    def __init__(self):
        self.pool_stats = {}
        self.health_check_interval = 30  # seconds
        self.last_health_check = 0
        self.connection_timeout = 30
        self.max_retries = 3
        
    def optimize_connection_settings(self):
        """Optimize database connection settings for high-volume operations"""
        db_settings = {
            'default': {
                'CONN_MAX_AGE': 600,  # 10 minutes
                'CONN_HEALTH_CHECKS': True,
                'OPTIONS': {
                    'MAX_CONNS': 20,
                    'MIN_CONNS': 5,
                    'connect_timeout': self.connection_timeout,
                    'server_side_binding': True,
                }
            }
        }
        
        # Apply PostgreSQL specific optimizations
        if 'postgresql' in settings.DATABASES['default']['ENGINE']:
            db_settings['default']['OPTIONS'].update({
                'sslmode': 'prefer',
                'application_name': 'CLEAR_HTMX_Messaging',
                'statement_timeout': '30000',  # 30 seconds
                'lock_timeout': '10000',       # 10 seconds
                'idle_in_transaction_session_timeout': '60000',  # 1 minute
            })
        
        return db_settings
    
    def get_connection_stats(self, alias='default') -> Dict[str, Any]:
        """Get connection pool statistics"""
        try:
            db_connection = connections[alias]
            
            stats = {
                'alias': alias,
                'vendor': db_connection.vendor,
                'is_usable': db_connection.is_usable(),
                'queries_count': len(db_connection.queries) if settings.DEBUG else 0,
                'last_health_check': self.last_health_check,
                'connection_age': getattr(db_connection, 'connection_age', 0)
            }
            
            # Add PostgreSQL specific stats
            if db_connection.vendor == 'postgresql':
                stats.update(self._get_postgresql_stats(db_connection))
            
            return stats
        except Exception as e:
            logger.error(f"Error getting connection stats for {alias}: {e}")
            return {'error': str(e)}
    
    def _get_postgresql_stats(self, connection) -> Dict[str, Any]:
        """Get PostgreSQL specific connection statistics"""
        try:
            with connection.cursor() as cursor:
                # Get connection count
                cursor.execute("""
                    SELECT count(*) as active_connections 
                    FROM pg_stat_activity 
                    WHERE state = 'active' AND application_name = 'CLEAR_HTMX_Messaging'
                """)
                active_connections = cursor.fetchone()[0]
                
                # Get database size
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
                """)
                db_size = cursor.fetchone()[0]
                
                # Get cache hit ratio
                cursor.execute("""
                    SELECT 
                        round(
                            (sum(heap_blks_hit) / sum(heap_blks_hit + heap_blks_read)) * 100, 
                            2
                        ) as cache_hit_ratio
                    FROM pg_statio_user_tables
                """)
                cache_hit_ratio = cursor.fetchone()[0] or 0
                
                return {
                    'active_connections': active_connections,
                    'database_size': db_size,
                    'cache_hit_ratio': cache_hit_ratio
                }
        except Exception as e:
            logger.error(f"Error getting PostgreSQL stats: {e}")
            return {}
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on database connections"""
        current_time = time.time()
        
        if current_time - self.last_health_check < self.health_check_interval:
            return self.pool_stats
        
        health_status = {}
        
        for alias in connections:
            try:
                db_connection = connections[alias]
                
                # Test connection
                start_time = time.time()
                with db_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                
                response_time = (time.time() - start_time) * 1000  # ms
                
                health_status[alias] = {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'last_checked': current_time,
                    'connection_stats': self.get_connection_stats(alias)
                }
                
            except Exception as e:
                health_status[alias] = {
                    'status': 'unhealthy',
                    'error': str(e),
                    'last_checked': current_time
                }
                logger.error(f"Database health check failed for {alias}: {e}")
        
        self.pool_stats = health_status
        self.last_health_check = current_time
        
        return health_status
    
    @contextmanager
    def get_connection(self, alias='default'):
        """Get a database connection with automatic cleanup"""
        db_connection = None
        try:
            db_connection = connections[alias]
            
            # Ensure connection is usable
            if not db_connection.is_usable():
                db_connection.connect()
            
            yield db_connection
            
        except Exception as e:
            logger.error(f"Database connection error for {alias}: {e}")
            if db_connection:
                db_connection.close()
            raise
        
        finally:
            # Connection cleanup is handled by Django's connection management
            pass
    
    def close_all_connections(self):
        """Close all database connections"""
        try:
            connections.close_all()
            logger.info("All database connections closed")
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")


class RedisConnectionPoolManager:
    """Manages Redis connections with failover and health monitoring"""
    
    def __init__(self):
        self.pools = {}
        self.sentinels = {}
        self.health_check_interval = 30
        self.last_health_check = 0
        self.connection_timeout = 5
        self.socket_timeout = 5
        self.max_connections = 50
        self.retry_on_timeout = True
        
        self._initialize_pools()
    
    def _initialize_pools(self):
        """Initialize Redis connection pools"""
        redis_url = os.environ.get('REDIS_URL')
        
        if redis_url:
            self._setup_single_redis(redis_url)
        else:
            self._setup_fallback_redis()
    
    def _setup_single_redis(self, redis_url: str):
        """Setup single Redis instance connection pool"""
        try:
            # Parse Redis URL
            redis_config = self._parse_redis_url(redis_url)
            
            # Create connection pool
            pool = ConnectionPool(
                host=redis_config['host'],
                port=redis_config['port'],
                db=redis_config['db'],
                password=redis_config.get('password'),
                connection_class=redis.Connection,
                max_connections=self.max_connections,
                socket_timeout=self.socket_timeout,
                socket_connect_timeout=self.connection_timeout,
                retry_on_timeout=self.retry_on_timeout,
                health_check_interval=self.health_check_interval
            )
            
            self.pools['default'] = {
                'pool': pool,
                'client': redis.Redis(connection_pool=pool),
                'config': redis_config,
                'type': 'single'
            }
            
            logger.info(f"Redis connection pool initialized: {redis_config['host']}:{redis_config['port']}")
            
        except Exception as e:
            logger.error(f"Error setting up Redis connection pool: {e}")
            self._setup_fallback_redis()
    
    def _setup_fallback_redis(self):
        """Setup fallback in-memory cache when Redis is not available"""
        logger.warning("Redis not available, using in-memory cache fallback")
        self.pools['fallback'] = {
            'type': 'memory',
            'client': None
        }
    
    def _parse_redis_url(self, url: str) -> Dict[str, Any]:
        """Parse Redis URL into connection parameters"""
        
        parsed = urlparse(url)
        
        return {
            'host': parsed.hostname or 'localhost',
            'port': parsed.port or 6379,
            'db': int(parsed.path.lstrip('/')) if parsed.path else 0,
            'password': parsed.password,
            'ssl': parsed.scheme == 'rediss'
        }
    
    def get_redis_client(self, pool_name='default') -> Optional[redis.Redis]:
        """Get Redis client from connection pool"""
        try:
            pool_config = self.pools.get(pool_name)
            
            if not pool_config:
                logger.error(f"Redis pool '{pool_name}' not found")
                return None
            
            if pool_config['type'] == 'memory':
                return None  # Use Django's cache framework
            
            return pool_config['client']
            
        except Exception as e:
            logger.error(f"Error getting Redis client for pool '{pool_name}': {e}")
            return None
    
    def get_connection_stats(self, pool_name='default') -> Dict[str, Any]:
        """Get Redis connection pool statistics"""
        try:
            pool_config = self.pools.get(pool_name, {})
            
            if pool_config.get('type') == 'memory':
                return {
                    'pool_name': pool_name,
                    'type': 'memory_fallback',
                    'status': 'active'
                }
            
            client = pool_config.get('client')
            pool = pool_config.get('pool')
            
            if not client or not pool:
                return {'error': 'Pool not initialized'}
            
            # Get Redis info
            redis_info = client.info()
            
            stats = {
                'pool_name': pool_name,
                'type': pool_config['type'],
                'connected_clients': redis_info.get('connected_clients', 0),
                'used_memory_human': redis_info.get('used_memory_human', '0B'),
                'keyspace_hits': redis_info.get('keyspace_hits', 0),
                'keyspace_misses': redis_info.get('keyspace_misses', 0),
                'total_commands_processed': redis_info.get('total_commands_processed', 0),
                'uptime_in_seconds': redis_info.get('uptime_in_seconds', 0),
                'pool_created_connections': pool.created_connections,
                'pool_available_connections': len(pool._available_connections),
                'pool_in_use_connections': len(pool._in_use_connections)
            }
            
            # Calculate hit ratio
            hits = stats['keyspace_hits']
            misses = stats['keyspace_misses']
            if hits + misses > 0:
                stats['hit_ratio'] = round((hits / (hits + misses)) * 100, 2)
            else:
                stats['hit_ratio'] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting Redis stats for pool '{pool_name}': {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on Redis connections"""
        current_time = time.time()
        
        health_status = {}
        
        for pool_name, pool_config in self.pools.items():
            try:
                if pool_config.get('type') == 'memory':
                    health_status[pool_name] = {
                        'status': 'healthy',
                        'type': 'memory_fallback',
                        'last_checked': current_time
                    }
                    continue
                
                client = pool_config.get('client')
                if not client:
                    health_status[pool_name] = {
                        'status': 'unhealthy',
                        'error': 'Client not initialized',
                        'last_checked': current_time
                    }
                    continue
                
                # Test connection
                start_time = time.time()
                pong = client.ping()
                response_time = (time.time() - start_time) * 1000  # ms
                
                if pong:
                    health_status[pool_name] = {
                        'status': 'healthy',
                        'response_time_ms': round(response_time, 2),
                        'last_checked': current_time,
                        'connection_stats': self.get_connection_stats(pool_name)
                    }
                else:
                    health_status[pool_name] = {
                        'status': 'unhealthy',
                        'error': 'Ping failed',
                        'last_checked': current_time
                    }
                
            except Exception as e:
                health_status[pool_name] = {
                    'status': 'unhealthy',
                    'error': str(e),
                    'last_checked': current_time
                }
                logger.error(f"Redis health check failed for pool '{pool_name}': {e}")
        
        return health_status
    
    @contextmanager
    def get_connection(self, pool_name='default'):
        """Get Redis connection with automatic cleanup"""
        client = None
        try:
            client = self.get_redis_client(pool_name)
            
            if client is None:
                # Use Django's cache as fallback
                yield cache
            else:
                yield client
                
        except Exception as e:
            logger.error(f"Redis connection error for pool '{pool_name}': {e}")
            # Fallback to Django's cache
            yield cache
    
    def close_all_connections(self):
        """Close all Redis connections"""
        try:
            for pool_name, pool_config in self.pools.items():
                if pool_config.get('type') != 'memory':
                    client = pool_config.get('client')
                    if client:
                        client.close()
            
            logger.info("All Redis connections closed")
        except Exception as e:
            logger.error(f"Error closing Redis connections: {e}")


class ConnectionHealthMonitor:
    """Monitors connection health and provides alerts"""
    
    def __init__(self):
        self.db_manager = DatabaseConnectionPoolManager()
        self.redis_manager = RedisConnectionPoolManager()
        self.alert_thresholds = {
            'response_time_ms': 1000,  # 1 second
            'error_rate': 0.05,        # 5%
            'connection_usage': 0.8    # 80%
        }
        self.monitoring_enabled = True
    
    def get_overall_health(self) -> Dict[str, Any]:
        """Get overall health status of all connections"""
        db_health = self.db_manager.health_check()
        redis_health = self.redis_manager.health_check()
        
        overall_status = 'healthy'
        issues = []
        
        # Check database health
        for alias, status in db_health.items():
            if status.get('status') != 'healthy':
                overall_status = 'degraded'
                issues.append(f"Database {alias}: {status.get('error', 'Unknown error')}")
        
        # Check Redis health
        for pool_name, status in redis_health.items():
            if status.get('status') != 'healthy':
                overall_status = 'degraded'
                issues.append(f"Redis {pool_name}: {status.get('error', 'Unknown error')}")
        
        return {
            'overall_status': overall_status,
            'issues': issues,
            'database_health': db_health,
            'redis_health': redis_health,
            'timestamp': time.time()
        }
    
    def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """Check for performance issues and generate alerts"""
        alerts = []
        
        try:
            health_status = self.get_overall_health()
            
            # Check database response times
            for alias, db_status in health_status['database_health'].items():
                response_time = db_status.get('response_time_ms', 0)
                if response_time > self.alert_thresholds['response_time_ms']:
                    alerts.append({
                        'type': 'performance',
                        'severity': 'warning',
                        'component': f'database_{alias}',
                        'message': f'Database {alias} response time ({response_time}ms) exceeds threshold',
                        'threshold': self.alert_thresholds['response_time_ms'],
                        'current_value': response_time
                    })
            
            # Check Redis response times
            for pool_name, redis_status in health_status['redis_health'].items():
                response_time = redis_status.get('response_time_ms', 0)
                if response_time > self.alert_thresholds['response_time_ms']:
                    alerts.append({
                        'type': 'performance',
                        'severity': 'warning',
                        'component': f'redis_{pool_name}',
                        'message': f'Redis {pool_name} response time ({response_time}ms) exceeds threshold',
                        'threshold': self.alert_thresholds['response_time_ms'],
                        'current_value': response_time
                    })
            
        except Exception as e:
            logger.error(f"Error checking performance alerts: {e}")
            alerts.append({
                'type': 'system',
                'severity': 'error',
                'component': 'health_monitor',
                'message': f'Health monitoring error: {str(e)}'
            })
        
        return alerts
    
    def start_monitoring(self, interval=60):
        """Start background monitoring thread"""
        if not self.monitoring_enabled:
            return
        
        def monitor_loop():
            while self.monitoring_enabled:
                try:
                    alerts = self.check_performance_alerts()
                    
                    if alerts:
                        for alert in alerts:
                            logger.warning(f"Performance Alert: {alert['message']}")
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    logger.error(f"Monitoring loop error: {e}")
                    time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("Connection health monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.monitoring_enabled = False
        logger.info("Connection health monitoring stopped")


# Initialize global connection managers
db_pool_manager = DatabaseConnectionPoolManager()
redis_pool_manager = RedisConnectionPoolManager()
connection_health_monitor = ConnectionHealthMonitor()


# Utility functions for easy access
def get_db_connection(alias='default'):
    """Get database connection with automatic cleanup"""
    return db_pool_manager.get_connection(alias)


def get_redis_connection(pool_name='default'):
    """Get Redis connection with automatic cleanup"""
    return redis_pool_manager.get_connection(pool_name)


def get_connection_health():
    """Get overall connection health status"""
    return connection_health_monitor.get_overall_health()


def start_connection_monitoring():
    """Start connection health monitoring"""
    connection_health_monitor.start_monitoring()


def stop_connection_monitoring():
    """Stop connection health monitoring"""
    connection_health_monitor.stop_monitoring()