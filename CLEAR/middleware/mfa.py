from django.shortcuts import redirect
from django.urls import reverse

from ..models import MFASession


class MFARequiredMiddleware:
    """
    Middleware to enforce MFA verification for users with MFA enabled.
    
    This middleware checks if authenticated users have MFA enabled and
    verifies they have a valid MFA session.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # URLs that don't require MFA verification
        self.exempt_urls = [
            reverse('CLEAR:mfa_verify'),
            reverse('CLEAR:mfa_backup_login'),
            reverse('CLEAR:login'),
            reverse('CLEAR:logout'),
            '/admin/',  # Django admin
            '/static/',  # Static files
            '/media/',   # Media files
        ]
    
    def __call__(self, request):
        if self.should_check_mfa(request):
            if not self.has_valid_mfa_session(request):
                # Clear any existing session and redirect to MFA verification
                if hasattr(request, 'user') and request.user.is_authenticated:
                    request.session['mfa_user_id'] = request.user.id
                return redirect('CLEAR:mfa_verify')
        
        response = self.get_response(request)
        return response
    
    def should_check_mfa(self, request):
        """Check if this request should be subject to MFA verification"""
        # Skip for exempt URLs
        for exempt_url in self.exempt_urls:
            if request.path.startswith(exempt_url):
                return False
        
        # Skip for unauthenticated users
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return False
        
        # Skip for users without MFA enabled
        if not request.user.is_mfa_enabled:
            return False
        
        return True
    
    def has_valid_mfa_session(self, request):
        """Check if the user has a valid MFA session"""
        if not hasattr(request, 'session') or not request.session.session_key:
            return False
        
        try:
            mfa_session = MFASession.objects.get(
                user=request.user,
                session_key=request.session.session_key,
                is_verified=True
            )
            
            # Check if session is expired
            if mfa_session.is_expired():
                mfa_session.delete()
                return False
            
            return True
            
        except MFASession.DoesNotExist:
            return False