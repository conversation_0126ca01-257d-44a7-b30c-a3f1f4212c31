import logging
import time

from django.contrib.auth import get_user_model
from django.http import HttpResponseForbidden
from django.utils import timezone

# MFA middleware package


logger = logging.getLogger(__name__)
User = get_user_model()

class UserActivityMiddleware:
    """Track user activity and update last seen timestamps"""
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Track activity before processing request
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                # Update user's last activity timestamp
                User.objects.filter(id=request.user.id).update(
                    last_activity=timezone.now()
                )
                
                # Track session activity
                request.session['last_activity'] = timezone.now().isoformat()
                
                # Log significant activities
                if request.method in ['POST', 'PUT', 'DELETE']:
                    logger.info(f"User {request.user.username} performed {request.method} on {request.path}")
            except Exception as e:
                logger.warning(f"Failed to track user activity: {e}")
        
        response = self.get_response(request)
        return response

class SecurityMiddleware:
    """Security enhancements and protection"""
    def __init__(self, get_response):
        self.get_response = get_response
        self.blocked_ips = set()  # Could be loaded from database
        self.rate_limits = {}  # Simple rate limiting storage
    
    def __call__(self, request):
        # Get client IP
        client_ip = self.get_client_ip(request)
        
        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            logger.warning(f"Blocked request from IP: {client_ip}")
            return HttpResponseForbidden("Access denied")
        
        # Simple rate limiting (100 requests per minute per IP)
        if self.is_rate_limited(client_ip):
            logger.warning(f"Rate limited request from IP: {client_ip}")
            return HttpResponseForbidden("Rate limit exceeded")
        
        # Process request
        response = self.get_response(request)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSP header for HTMX compatibility
        if not response.get('Content-Security-Policy'):
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.tailwindcss.com; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdn.tailwindcss.com https://fonts.googleapis.com; "
                "font-src 'self' https://fonts.gstatic.com; "
                "img-src 'self' data: https:; "
                "connect-src 'self';"
            )
        
        return response
    
    def get_client_ip(self, request):
        """Get the real client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def is_rate_limited(self, ip):
        """Simple rate limiting implementation"""
        now = time.time()
        minute = int(now / 60)
        
        if ip not in self.rate_limits:
            self.rate_limits[ip] = {}
        
        # Clean old entries
        for old_minute in list(self.rate_limits[ip].keys()):
            if old_minute < minute - 1:
                del self.rate_limits[ip][old_minute]
        
        # Count requests in current minute
        current_count = self.rate_limits[ip].get(minute, 0)
        
        if current_count >= 100:  # 100 requests per minute limit
            return True
        
        # Increment counter
        self.rate_limits[ip][minute] = current_count + 1
        return False