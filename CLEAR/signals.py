"""
Django signals for automatic document activity logging.
"""

from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver
from .models import Comment, Document, DocumentShare
        from .services.notifications import message_notification_service

"""



@receiver(post_save, sender=Document)
def log_document_upload_activity(sender, instance, created, **kwargs):
    """Log document upload activity when a new document is created"""
    if created:
        instance.log_activity(
            user=instance.uploaded_by,
            activity_type='uploaded',
            description=f"Uploaded document: {instance.name}",
            metadata={
                'file_size': instance.file_size,
                'file_type': instance.file_type,
                'folder': instance.folder
            }
        )


@receiver(post_save, sender=DocumentShare)
def log_document_share_activity(sender, instance, created, **kwargs):
    """Log document sharing activity when a document is shared"""
    if created:
        instance.document.log_activity(
            user=instance.shared_by,
            activity_type='shared',
            description=f"Shared document with {instance.shared_with.username}",
            metadata={
                'shared_with': [instance.shared_with.username],
                'permission_level': instance.permission_level
            }
        )


@receiver(post_delete, sender=DocumentShare)
def log_document_unshare_activity(sender, instance, **kwargs):
    """Log document unsharing activity when sharing is removed"""
    instance.document.log_activity(
        user=instance.shared_by,  # Assuming the person who shared can unshare
        activity_type='shared',
        description=f"Removed sharing with {instance.shared_with.username}",
        metadata={
            'action': 'removed_sharing',
            'previously_shared_with': instance.shared_with.username,
            'permission_level': instance.permission_level
        }
    )


@receiver(post_save, sender=Comment)
def log_document_comment_activity(sender, instance, created, **kwargs):
    """Log comment activity for documents"""
    if created and instance.commentable_type == 'document':
        try:
            document = Document.objects.get(id=instance.commentable_id)
            document.log_activity(
                user=instance.user,
                activity_type='commented',
                description=f"Added a comment: {instance.content[:50]}{'...' if len(instance.content) > 50 else ''}",
                metadata={
                    'comment_id': str(instance.id),
                    'is_reply': instance.parent is not None,
                    'content_preview': instance.content[:100]
                }
            )
        except Document.DoesNotExist:
            pass


@receiver(post_delete, sender=Comment)
def log_document_comment_delete_activity(sender, instance, **kwargs):
    """Log comment deletion activity for documents"""
    if instance.commentable_type == 'document':
        try:
            document = Document.objects.get(id=instance.commentable_id)
            document.log_activity(
                user=instance.user,
                activity_type='commented',
                description="Deleted a comment",
                metadata={
                    'action': 'deleted_comment',
                    'comment_id': str(instance.id)
                }
            )
        except Document.DoesNotExist:
            pass


@receiver(post_save, sender='CLEAR.ChatMessage')
def create_message_notification(sender, instance, created, **kwargs):
    """Create notifications for new chat messages"""
    if created:
        
        # Get conversation if exists
        conversation = None
        if hasattr(instance, 'conversation') and instance.conversation:
            conversation = instance.conversation
        
        # Create notification for message
        message_notification_service.notify_new_message(instance, conversation)
"""