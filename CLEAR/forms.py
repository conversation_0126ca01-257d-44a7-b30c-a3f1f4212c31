import math
from decimal import Decimal
from django import forms
from django.contrib.auth.forms import AuthenticationForm
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from .models import Project, TimeEntry, WorkType
from django.contrib.auth import authenticate
from .models import User





class MFASetupForm(forms.Form):
    """Form for MFA setup"""
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm your password'
        }),
        help_text='Please confirm your password to set up MFA'
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_password(self):
        password = self.cleaned_data.get('password')
        if not self.user.check_password(password):
            raise ValidationError('Incorrect password.')
        return password


class MFAVerificationForm(forms.Form):
    """Form for MFA verification during login"""
    token = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6',
            'pattern': '[0-9]*',
            'inputmode': 'numeric',
            'autocomplete': 'one-time-code'
        }),
        help_text='Enter the 6-digit code from your authenticator app'
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_token(self):
        token = self.cleaned_data.get('token')
        if not token.isdigit():
            raise ValidationError('Token must contain only digits.')
        if not self.user.verify_totp(token):
            raise ValidationError('Invalid verification code.')
        return token


class MFABackupForm(forms.Form):
    """Form for MFA backup token verification"""
    backup_token = forms.CharField(
        max_length=8,
        min_length=8,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': 'Enter backup code',
            'maxlength': '8',
            'autocomplete': 'one-time-code'
        }),
        help_text='Enter one of your backup codes'
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_backup_token(self):
        token = self.cleaned_data.get('backup_token')
        if not self.user.use_backup_token(token):
            raise ValidationError('Invalid or already used backup code.')
        return token


class MFADisableForm(forms.Form):
    """Form for disabling MFA"""
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm your password'
        }),
        help_text='Please confirm your password to disable MFA'
    )
    confirmation = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I understand that disabling MFA will make my account less secure'
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_password(self):
        password = self.cleaned_data.get('password')
        if not self.user.check_password(password):
            raise ValidationError('Incorrect password.')
        return password


class EnhancedLoginForm(AuthenticationForm):
    """Enhanced login form with MFA support"""
    remember_me = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='Remember me for 30 days'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Email or username'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Password'
        })
    
    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')
        
        print(f"DEBUG: Login attempt - Username: {username}")
        print(f"DEBUG: Password provided: {'Yes' if password else 'No'}")
        
        if username and password:
            self.user_cache = authenticate(
                self.request, 
                username=username, 
                password=password
            )
            print(f"DEBUG: Authentication result: {self.user_cache}")
            
            if self.user_cache is None:
                # Try to find the user to see if they exist
                try:
                    user = User.objects.get(email=username)
                    print(f"DEBUG: User exists in database: {user.username}")
                    password_check = user.check_password(password)
                    print(f"DEBUG: Password check result: {password_check}")
                except User.DoesNotExist:
                    print(f"DEBUG: User not found with email: {username}")
                
                raise forms.ValidationError(
                    self.error_messages['invalid_login'],
                    code='invalid_login',
                    params={'username': self.username_field.verbose_name},
                )
        
        return self.cleaned_data


class TimeEntryForm(forms.ModelForm):
    """Form for creating and editing time entries with decimal hours validation"""
    
    duration_hours = forms.DecimalField(
        max_digits=8,
        decimal_places=1,
        min_value=Decimal('0.1'),
        max_value=Decimal('24.0'),
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '8.0',
            'step': '0.1',
            'min': '0.1',
            'max': '24.0',
            'id': 'id_duration_hours'
        }),
        help_text='Enter hours in decimal format (e.g., 8.5 for 8 hours 30 minutes)'
    )
    
    project = forms.ModelChoiceField(
        queryset=Project.objects.none(),  # Will be set in __init__
        widget=forms.Select(attrs={
            'class': 'form-select',
            'required': True
        }),
        empty_label="Select a project..."
    )
    
    work_type = forms.ModelChoiceField(
        queryset=WorkType.objects.none(),  # Will be set in __init__
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        empty_label="Select work type...",
        required=False
    )
    
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Describe the work performed...'
        }),
        max_length=500
    )
    
    start_time = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        required=False,
        help_text='Optional: Specify start time if not using duration'
    )
    
    end_time = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        required=False,
        help_text='Optional: Specify end time if not using duration'
    )
    
    is_billable = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='Billable to client'
    )
    
    class Meta:
        model = TimeEntry
        fields = ['project', 'work_type', 'description', 'duration_hours', 
                 'start_time', 'end_time', 'is_billable', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Additional notes (optional)...'
            })
        }
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        
        if user:
            # Filter projects for the current user
            self.fields['project'].queryset = Project.objects.filter(
                models.Q(manager_id=user.id) | 
                models.Q(coordinator_id=str(user.id)) | 
                models.Q(egis_project_manager=user.username)
            ).order_by('name')
            
            # Filter work types for the user's organization
            if hasattr(user, 'organization') and user.organization:
                self.fields['work_type'].queryset = WorkType.objects.filter(
                    organization=user.organization,
                    is_active=True
                ).order_by('name')
            else:
                # If no organization, get all active work types
                self.fields['work_type'].queryset = WorkType.objects.filter(
                    is_active=True
                ).order_by('name')
    
    def clean_duration_hours(self):
        """Validate and format decimal hours according to requirements"""
        duration = self.cleaned_data.get('duration_hours')
        
        if duration is None:
            return duration
        
        # Convert to float for easier manipulation
        hours_float = float(duration)
        
        # Check if it's a whole number
        if hours_float == int(hours_float):
            # Add .0 to whole numbers
            return Decimal(f"{int(hours_float)}.0")
        
        # Check decimal places
        decimal_places = len(str(hours_float).split('.')[-1]) if '.' in str(hours_float) else 0
        
        if decimal_places > 1:
            # Round up to next tenth if more than 1 decimal place
            rounded_up = math.ceil(hours_float * 10) / 10
            return Decimal(f"{rounded_up:.1f}")
        
        # Already has 1 decimal place or less, format to 1 decimal
        return Decimal(f"{hours_float:.1f}")
    
    def clean(self):
        cleaned_data = super().clean()
        duration_hours = cleaned_data.get('duration_hours')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        
        # Either duration_hours OR start_time/end_time should be provided
        if not duration_hours and not (start_time and end_time):
            raise ValidationError(
                'Either specify duration in hours OR provide both start and end times.'
            )
        
        # If both start_time and end_time are provided, validate them
        if start_time and end_time:
            if end_time <= start_time:
                raise ValidationError('End time must be after start time.')
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Set the user
        if self.user:
            instance.user = self.user
        
        # If no work_type is selected, try to get a default one
        if not instance.work_type:
            if hasattr(self.user, 'organization') and self.user.organization:
                default_work_type = WorkType.objects.filter(
                    organization=self.user.organization,
                    is_active=True
                ).first()
            else:
                default_work_type = WorkType.objects.filter(
                    is_active=True
                ).first()
            
            if default_work_type:
                instance.work_type = default_work_type
        
        # Set today's date for start_time if not provided
        if not instance.start_time and instance.duration_hours:
            instance.start_time = timezone.now()
        
        if commit:
            instance.save()
        
        return instance


class QuickTimeEntryForm(forms.Form):
    """Simplified form for quick time entries"""
    
    project = forms.ModelChoiceField(
        queryset=Project.objects.none(),
        widget=forms.Select(attrs={
            'class': 'form-select form-select-sm',
            'required': True
        }),
        empty_label="Select project..."
    )
    
    hours = forms.DecimalField(
        max_digits=4,
        decimal_places=1,
        min_value=Decimal('0.1'),
        max_value=Decimal('12.0'),
        widget=forms.NumberInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': '8.0',
            'step': '0.1',
            'min': '0.1',
            'max': '12.0'
        })
    )
    
    description = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': 'Brief description...'
        })
    )
    
    date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control form-control-sm',
            'type': 'date'
        })
    )
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        
        # Set initial date to today
        self.fields['date'].initial = timezone.now().date()
        
        if user:
            self.fields['project'].queryset = Project.objects.filter(
                models.Q(manager_id=user.id) | 
                models.Q(coordinator_id=str(user.id)) | 
                models.Q(egis_project_manager=user.username)
            ).order_by('name')
    
    def clean_hours(self):
        """Apply the same decimal hours formatting as TimeEntryForm"""
        hours = self.cleaned_data.get('hours')
        
        if hours is None:
            return hours
        
        hours_float = float(hours)
        
        # Add .0 to whole numbers
        if hours_float == int(hours_float):
            return Decimal(f"{int(hours_float)}.0")
        
        # Round up if more than 1 decimal place
        decimal_places = len(str(hours_float).split('.')[-1]) if '.' in str(hours_float) else 0
        if decimal_places > 1:
            rounded_up = math.ceil(hours_float * 10) / 10
            return Decimal(f"{rounded_up:.1f}")
        
        return Decimal(f"{hours_float:.1f}")
