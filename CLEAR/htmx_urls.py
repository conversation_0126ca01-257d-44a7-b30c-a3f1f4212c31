"""
HTMX URL patterns for the CLEAR application.

Provides HTML fragment endpoints for dynamic UI updates following HDA principles.
"""

from django.urls import path
from .views import htmx_views



app_name = 'htmx'

urlpatterns = [
    # Working endpoints only - others commented out until functions are implemented
    path('dashboard/stats/', htmx_views.dashboard_stats, name='dashboard_stats'),
    path('recent-activity/', htmx_views.recent_activity, name='recent_activity'),
    path('notifications/dropdown/', htmx_views.notifications_dropdown, name='notifications_dropdown'),
    path('notifications/<str:notification_id>/mark-read/', htmx_views.mark_notification_read, name='mark_notification_read'),
    path('whispers/count/', htmx_views.whispers_count, name='whispers_count'),
    
    # These will be implemented later when the missing view functions are created:
    # path('auth/login/', htmx_views.login_htmx, name='login'),
    # path('projects/', htmx_views.project_list_htmx, name='project_list'),
    # ... other endpoints
]