"""
Django management command to generate schema migration instructions for Supabase to Django migration.

This command analyzes the current Django models and creates SQL export commands
that can be run against the Supabase database to export data in the correct format.

Usage:
    python manage.py export_schema_migration --output-dir /path/to/export/scripts
    python manage.py export_schema_migration --table-name chat_messages
"""

import os
from django.apps import apps
from django.core.management.base import BaseCommand

"""




class Command(BaseCommand):
    help = 'Generate Supabase export commands for Django migration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            type=str,
            default='./supabase_exports',
            help='Directory to create export scripts (default: ./supabase_exports)'
        )
        parser.add_argument(
            '--table-name',
            type=str,
            help='Specific table to export (optional, exports all if not specified)'
        )
        parser.add_argument(
            '--format',
            choices=['json', 'csv', 'sql'],
            default='json',
            help='Export format (default: json)'
        )

    def handle(self, *args, **options):
        output_dir = options['output_dir']
        table_name = options.get('table_name')
        export_format = options['format']

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        self.stdout.write(f'Generating export scripts in {output_dir}')

        # Get all models from CLEAR app
        clear_models = apps.get_app_config('CLEAR').get_models()

        # Supabase table mapping - maps Django model names to Supabase table names
        table_mapping = {
            'User': 'users',
            'Permission': 'permissions', 
            'RolePermission': 'role_permissions',
            'PasswordResetToken': 'password_reset_tokens',
            'CoordinateSystem': 'coordinate_systems',
            'ProjectTemplate': 'project_templates',
            'Project': 'projects',
            'Stakeholder': 'stakeholders',
            'Utility': 'utilities',
            'LineStyle': 'line_styles',
            'UtilityLineData': 'utility_line_data',
            'Conflict': 'conflicts',
            'Task': 'tasks',
            'Comment': 'comments',
            'ChatMessage': ['chat_messages', 'team_messages'],  # Multiple source tables
            'WhisperMessage': 'temporary_messages',  # Different name in Supabase
            'Activity': 'activities',
            'Notification': 'notifications',
        }

        # Generate export commands
        export_commands = []
        migration_notes = []

        for model in clear_models:
            model_name = model.__name__
            
            if table_name and model_name.lower() != table_name.lower():
                continue

            supabase_tables = table_mapping.get(model_name)
            if not supabase_tables:
                continue

            if isinstance(supabase_tables, str):
                supabase_tables = [supabase_tables]

            for supabase_table in supabase_tables:
                if export_format == 'json':
                    export_cmd = self.generate_json_export(model, supabase_table)
                elif export_format == 'csv':
                    export_cmd = self.generate_csv_export(model, supabase_table)
                else:  # sql
                    export_cmd = self.generate_sql_export(model, supabase_table)

                export_commands.append(export_cmd)

                # Add special notes for complex migrations
                if model_name == 'ChatMessage':
                    migration_notes.append(
                        f"-- SPECIAL: {supabase_table} will be merged into chat_messages with channel field"
                    )
                elif model_name == 'WhisperMessage':
                    migration_notes.append(
                        f"-- SPECIAL: {supabase_table} will become whisper_messages with enhanced features"
                    )

        # Write export script
        script_path = os.path.join(output_dir, f'export_data.{export_format}')
        with open(script_path, 'w') as f:
            if export_format in ['json', 'csv']:
                f.write('#!/bin/bash\n')
                f.write('# Supabase data export script\n')
                f.write('# Run this script in your Supabase project directory\n\n')
                
                for note in migration_notes:
                    f.write(f'{note}\n')
                f.write('\n')
                
                for cmd in export_commands:
                    f.write(f'{cmd}\n')
            else:  # SQL
                for note in migration_notes:
                    f.write(f'{note}\n')
                f.write('\n')
                
                for cmd in export_commands:
                    f.write(f'{cmd}\n\n')

        # Make script executable
        if export_format in ['json', 'csv']:
            os.chmod(script_path, 0o755)

        # Generate README
        readme_path = os.path.join(output_dir, 'README.md')
        self.generate_readme(readme_path, export_format)

        self.stdout.write(
            self.style.SUCCESS(f'Export scripts generated in {output_dir}')
        )
        self.stdout.write(f'Main script: {script_path}')
        self.stdout.write(f'Documentation: {readme_path}')

    def generate_json_export(self, model, table_name: str) -> str:
        """Generate JSON export command using Supabase CLI"""
        return f'npx supabase db dump --data-only --table={table_name} --format=json > {table_name}.json'

    def generate_csv_export(self, model, table_name: str) -> str:
        """Generate CSV export command"""
        return f'npx supabase db dump --data-only --table={table_name} --format=csv > {table_name}.csv'

    def generate_sql_export(self, model, table_name: str) -> str:
        """Generate SQL COPY command for export"""
        return f"\\copy (SELECT * FROM {table_name}) TO '{table_name}.csv' WITH CSV HEADER;"

    def generate_readme(self, readme_path: str, export_format: str):
        """Generate README with migration instructions"""
        readme_content = f"""# Supabase to Django Migration Guide

This directory contains scripts to export data from your Supabase database
for migration to the new Django + HTMX CLEAR platform.

## Prerequisites

1. Supabase CLI installed and configured
2. Access to your Supabase project
3. Django environment set up with new CLEAR models

## Export Process

### Step 1: Export Data from Supabase

Run the export script to extract data from your Supabase database:

```bash
./export_data.{export_format}
```

This will create individual {export_format.upper()} files for each table.

### Step 2: Import Data to Django

Use the Django management command to import the exported data:

```bash
# Import all tables
python manage.py migrate_supabase_data --export-dir ./supabase_exports

# Import specific table
python manage.py migrate_supabase_data --export-dir ./supabase_exports --table-name chat_messages

# Dry run to see what would be imported
python manage.py migrate_supabase_data --export-dir ./supabase_exports --dry-run
```

## Important Notes

### Messaging System Changes

The new Django implementation consolidates the messaging system:

1. **chat_messages** → Migrated to `ChatMessage` with `channel='general'`
2. **team_messages** → Migrated to `ChatMessage` with `channel='team'` 
3. **temporary_messages** → Migrated to `WhisperMessage` with enhanced features

### User References

- Supabase uses UUID references to auth.users
- Django uses integer IDs for the User model
- The migration command handles this mapping automatically

### Spatial Data

PostGIS geometry fields are preserved in the Django models.
Ensure PostGIS is enabled in your Django database.

### Troubleshooting

1. **Permission Errors**: Ensure Supabase user has read access to all tables
2. **Connection Issues**: Check your Supabase connection string
3. **Data Type Errors**: Review the migration command output for specific field issues

## Migration Order

Tables are migrated in dependency order:
1. coordinate_systems
2. users
3. permissions & roles
4. projects & templates
5. utilities & conflicts
6. messages & notifications

## Rollback

If you need to rollback the migration:

```bash
# Reset Django database (WARNING: This deletes all data)
python manage.py flush --no-input

# Re-run migrations
python manage.py migrate
```

## Support

If you encounter issues during migration:
1. Check the Django logs for detailed error messages
2. Use --dry-run to test before actual migration
3. Migrate tables individually to isolate issues
"""

        with open(readme_path, 'w') as f:
            f.write(readme_content)
"""