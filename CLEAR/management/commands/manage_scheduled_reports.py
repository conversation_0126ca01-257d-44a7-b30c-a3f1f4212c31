"""
Management command for scheduled reports administration.
Provides utilities to create, manage, and test scheduled reports.
"""

import json
from datetime import timed<PERSON><PERSON>
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from CLEAR.models.analytics import AnalyticsReport, ReportExecution
from CLEAR.models.projects import Organization
            from CLEAR.tasks import execute_scheduled_report

"""




User = get_user_model()


class Command(BaseCommand):
    help = 'Manage scheduled reports (create, list, test, enable/disable)'

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='action', help='Available actions')
        
        # List reports
        list_parser = subparsers.add_parser('list', help='List all scheduled reports')
        list_parser.add_argument('--organization', type=str, help='Filter by organization name')
        list_parser.add_argument('--active-only', action='store_true', help='Show only active scheduled reports')
        
        # Create report
        create_parser = subparsers.add_parser('create', help='Create a new scheduled report')
        create_parser.add_argument('--name', type=str, required=True, help='Report name')
        create_parser.add_argument('--organization', type=str, required=True, help='Organization name')
        create_parser.add_argument('--user', type=str, required=True, help='Creator username or email')
        create_parser.add_argument('--recurrence', type=str, choices=['hourly', 'daily', 'weekly', 'monthly'], 
                                 default='daily', help='Report recurrence')
        create_parser.add_argument('--recipients', type=str, nargs='*', help='Email recipients')
        create_parser.add_argument('--description', type=str, help='Report description')
        
        # Test report
        test_parser = subparsers.add_parser('test', help='Test execute a report')
        test_parser.add_argument('report_id', type=str, help='Report ID to test')
        test_parser.add_argument('--sync', action='store_true', help='Run synchronously (for testing)')
        
        # Enable/disable report
        toggle_parser = subparsers.add_parser('toggle', help='Enable or disable a scheduled report')
        toggle_parser.add_argument('report_id', type=str, help='Report ID to toggle')
        toggle_parser.add_argument('--enable', action='store_true', help='Enable the report')
        toggle_parser.add_argument('--disable', action='store_true', help='Disable the report')
        
        # Show report details
        show_parser = subparsers.add_parser('show', help='Show detailed report information')
        show_parser.add_argument('report_id', type=str, help='Report ID to show')
        
        # List executions
        executions_parser = subparsers.add_parser('executions', help='List report executions')
        executions_parser.add_argument('report_id', type=str, help='Report ID')
        executions_parser.add_argument('--limit', type=int, default=10, help='Number of executions to show')

    def handle(self, *args, **options):
        action = options.get('action')
        
        if not action:
            self.print_help('manage.py', 'manage_scheduled_reports')
            return
        
        try:
            if action == 'list':
                self.list_reports(options)
            elif action == 'create':
                self.create_report(options)
            elif action == 'test':
                self.test_report(options)
            elif action == 'toggle':
                self.toggle_report(options)
            elif action == 'show':
                self.show_report(options)
            elif action == 'executions':
                self.list_executions(options)
        except Exception as e:
            raise CommandError(f'Error: {str(e)}')

    def list_reports(self, options):
        """List scheduled reports."""
        queryset = AnalyticsReport.objects.select_related('organization', 'created_by')
        
        if options.get('organization'):
            try:
                org = Organization.objects.get(name__icontains=options['organization'])
                queryset = queryset.filter(organization=org)
            except Organization.DoesNotExist:
                raise CommandError(f"Organization '{options['organization']}' not found")
        
        if options.get('active_only'):
            queryset = queryset.filter(is_scheduled=True)
        
        reports = queryset.order_by('-created_at')
        
        if not reports.exists():
            self.stdout.write("No scheduled reports found.")
            return
        
        self.stdout.write(self.style.SUCCESS(f"\nFound {reports.count()} reports:\n"))
        
        for report in reports:
            status = "ACTIVE" if report.is_scheduled else "DISABLED"
            status_color = self.style.SUCCESS if report.is_scheduled else self.style.WARNING
            
            self.stdout.write(f"ID: {report.id}")
            self.stdout.write(f"Name: {report.name}")
            self.stdout.write(f"Organization: {report.organization.name}")
            self.stdout.write(f"Status: {status_color(status)}")
            self.stdout.write(f"Type: {report.report_type}")
            self.stdout.write(f"Created: {report.created_at.strftime('%Y-%m-%d %H:%M')}")
            if report.next_execution_at:
                self.stdout.write(f"Next Execution: {report.next_execution_at.strftime('%Y-%m-%d %H:%M')}")
            if report.last_executed_at:
                self.stdout.write(f"Last Executed: {report.last_executed_at.strftime('%Y-%m-%d %H:%M')}")
            self.stdout.write("-" * 50)

    def create_report(self, options):
        """Create a new scheduled report."""
        # Find organization
        try:
            organization = Organization.objects.get(name__icontains=options['organization'])
        except Organization.DoesNotExist:
            raise CommandError(f"Organization '{options['organization']}' not found")
        
        # Find user
        user_identifier = options['user']
        try:
            if '@' in user_identifier:
                user = User.objects.get(email=user_identifier)
            else:
                user = User.objects.get(username=user_identifier)
        except User.DoesNotExist:
            raise CommandError(f"User '{user_identifier}' not found")
        
        # Create schedule configuration
        schedule_config = {
            'recurrence': options['recurrence'],
            'send_email': True,
            'include_allowed_users': False,
        }
        
        if options.get('recipients'):
            schedule_config['recipients'] = options['recipients']
        
        # Calculate next execution time
        now = timezone.now()
        if options['recurrence'] == 'hourly':
            next_execution = now + timedelta(hours=1)
        elif options['recurrence'] == 'daily':
            next_execution = now + timedelta(days=1)
        elif options['recurrence'] == 'weekly':
            next_execution = now + timedelta(weeks=1)
        elif options['recurrence'] == 'monthly':
            next_execution = now + timedelta(days=30)
        else:
            next_execution = now + timedelta(days=1)
        
        # Create the report
        report = AnalyticsReport.objects.create(
            name=options['name'],
            description=options.get('description', ''),
            report_type='scheduled',
            organization=organization,
            created_by=user,
            is_scheduled=True,
            is_public=False,
            schedule_config=schedule_config,
            next_execution_at=next_execution,
            configuration={
                'created_via': 'management_command',
                'auto_generated': True,
            }
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created scheduled report '{report.name}' (ID: {report.id})\n"
                f"Next execution: {next_execution.strftime('%Y-%m-%d %H:%M:%S')}"
            )
        )

    def test_report(self, options):
        """Test execute a report."""
        try:
            report = AnalyticsReport.objects.get(id=options['report_id'])
        except AnalyticsReport.DoesNotExist:
            raise CommandError(f"Report '{options['report_id']}' not found")
        
        self.stdout.write(f"Testing execution of report: {report.name}")
        
        if options.get('sync'):
            # Run synchronously for testing
            try:
                result = execute_scheduled_report(str(report.id))
                self.stdout.write(self.style.SUCCESS(f"Test execution completed: {result}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Test execution failed: {str(e)}"))
        else:
            # Queue for async execution
            task = execute_scheduled_report.delay(str(report.id))
            self.stdout.write(
                self.style.SUCCESS(f"Report queued for execution. Task ID: {task.id}")
            )

    def toggle_report(self, options):
        """Enable or disable a scheduled report."""
        try:
            report = AnalyticsReport.objects.get(id=options['report_id'])
        except AnalyticsReport.DoesNotExist:
            raise CommandError(f"Report '{options['report_id']}' not found")
        
        if options.get('enable') and options.get('disable'):
            raise CommandError("Cannot specify both --enable and --disable")
        
        if options.get('enable'):
            report.is_scheduled = True
            action = "enabled"
        elif options.get('disable'):
            report.is_scheduled = False
            action = "disabled"
        else:
            # Toggle current state
            report.is_scheduled = not report.is_scheduled
            action = "enabled" if report.is_scheduled else "disabled"
        
        report.save()
        
        self.stdout.write(
            self.style.SUCCESS(f"Report '{report.name}' has been {action}")
        )

    def show_report(self, options):
        """Show detailed report information."""
        try:
            report = AnalyticsReport.objects.select_related(
                'organization', 'created_by'
            ).get(id=options['report_id'])
        except AnalyticsReport.DoesNotExist:
            raise CommandError(f"Report '{options['report_id']}' not found")
        
        self.stdout.write(self.style.SUCCESS("\nReport Details:\n"))
        self.stdout.write(f"ID: {report.id}")
        self.stdout.write(f"Name: {report.name}")
        self.stdout.write(f"Description: {report.description or 'N/A'}")
        self.stdout.write(f"Type: {report.report_type}")
        self.stdout.write(f"Organization: {report.organization.name}")
        self.stdout.write(f"Created By: {report.created_by.username}")
        self.stdout.write(f"Status: {'ACTIVE' if report.is_scheduled else 'DISABLED'}")
        self.stdout.write(f"Public: {'Yes' if report.is_public else 'No'}")
        self.stdout.write(f"Created: {report.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        self.stdout.write(f"Updated: {report.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if report.last_executed_at:
            self.stdout.write(f"Last Executed: {report.last_executed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            self.stdout.write("Last Executed: Never")
        
        if report.next_execution_at:
            self.stdout.write(f"Next Execution: {report.next_execution_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if report.schedule_config:
            self.stdout.write("\nSchedule Configuration:")
            self.stdout.write(json.dumps(report.schedule_config, indent=2))
        
        if report.configuration:
            self.stdout.write("\nReport Configuration:")
            self.stdout.write(json.dumps(report.configuration, indent=2))
        
        # Show recent executions
        executions = ReportExecution.objects.filter(report=report).order_by('-started_at')[:5]
        if executions.exists():
            self.stdout.write("\nRecent Executions:")
            for execution in executions:
                status_color = self.style.SUCCESS if execution.status == 'completed' else self.style.ERROR
                self.stdout.write(f"  {execution.started_at.strftime('%Y-%m-%d %H:%M')} - {status_color(execution.status.upper())}")

    def list_executions(self, options):
        """List report executions."""
        try:
            report = AnalyticsReport.objects.get(id=options['report_id'])
        except AnalyticsReport.DoesNotExist:
            raise CommandError(f"Report '{options['report_id']}' not found")
        
        executions = ReportExecution.objects.filter(
            report=report
        ).order_by('-started_at')[:options['limit']]
        
        if not executions.exists():
            self.stdout.write("No executions found for this report.")
            return
        
        self.stdout.write(self.style.SUCCESS(f"\nLast {len(executions)} executions for '{report.name}':\n"))
        
        for execution in executions:
            status_color = self.style.SUCCESS if execution.status == 'completed' else self.style.ERROR
            
            self.stdout.write(f"ID: {execution.id}")
            self.stdout.write(f"Status: {status_color(execution.status.upper())}")
            self.stdout.write(f"Started: {execution.started_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if execution.completed_at:
                self.stdout.write(f"Completed: {execution.completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if execution.execution_time_ms:
                self.stdout.write(f"Duration: {execution.execution_time_ms}ms")
            if execution.data_points_processed:
                self.stdout.write(f"Data Points: {execution.data_points_processed}")
            if execution.error_message:
                self.stdout.write(f"Error: {execution.error_message}")
            self.stdout.write("-" * 40)
"""