import json
import os
from decimal import Decimal, InvalidOperation
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from django.core.management.base import BaseCommand, CommandError
from django.utils.dateparse import parse_date, parse_datetime
from CLEAR.models import (
            from CLEAR.models import (



    Conflict,
    CoordinateSystem,
    LineStyle,
    Organization,
    Project,
    ProjectTemplate,
    Stakeholder,
    Utility,
    UtilityLineData,
)

User = get_user_model()


class Command(BaseCommand):
    help = '''Import Supabase JSON data from supabase_full_dump directory into Django models.
    
    This command reads JSON files exported from Supabase and imports them into the
    corresponding Django models. It handles foreign key relationships, data validation,
    and provides progress feedback.
    
    Files processed (in order):
    - organizations.json → Organization model
    - coordinate_systems.json → CoordinateSystem model  
    - auth_users.json → User model (from Supabase auth)
    - user_profiles.json → User model updates
    - project_templates.json → ProjectTemplate model
    - projects.json → Project model
    - stakeholders.json → Stakeholder model
    - line_styles.json → LineStyle model
    - utilities.json → Utility model
    - utility_line_data.json → UtilityLineData model
    - conflicts.json → Conflict model
    
    Examples:
      python manage.py import_supabase_data                    # Import all data
      python manage.py import_supabase_data --dry-run          # Show what would be imported
      python manage.py import_supabase_data --limit 10         # Import max 10 records per model
      python manage.py import_supabase_data --data-dir custom/ # Use custom directory
    '''

    def add_arguments(self, parser):
        parser.add_argument(
            '--data-dir',
            type=str,
            default='supabase_full_dump',
            help='Directory containing JSON files (default: supabase_full_dump)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be imported without actually importing'
        )
        parser.add_argument(
            '--limit',
            type=int,
            help='Limit number of records to import per model (for testing)'
        )

    def handle(self, *args, **options):
        self.data_dir = options['data_dir']
        self.dry_run = options['dry_run']
        self.limit = options.get('limit')
        
        if not os.path.exists(self.data_dir):
            raise CommandError(f'Data directory {self.data_dir} does not exist')

        if self.dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No data will be imported'))

        # Import order matters due to foreign key relationships
        import_order = [
            ('organizations.json', self.import_organizations),
            ('coordinate_systems.json', self.import_coordinate_systems),
            ('auth_users.json', self.import_auth_users),
            ('user_profiles.json', self.import_user_profiles),
            ('project_templates.json', self.import_project_templates),
            ('projects.json', self.import_projects),
            ('stakeholders.json', self.import_stakeholders),
            ('line_styles.json', self.import_line_styles),
            ('utilities.json', self.import_utilities),
            ('utility_line_data.json', self.import_utility_line_data),
            ('conflicts.json', self.import_conflicts),
        ]

        total_imported = 0
        for filename, import_func in import_order:
            filepath = os.path.join(self.data_dir, filename)
            if os.path.exists(filepath):
                self.stdout.write(f'\nProcessing {filename}...')
                try:
                    count = import_func(filepath)
                    total_imported += count
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully processed {count} records from {filename}')
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error processing {filename}: {str(e)}')
                    )
                    # Continue with other files instead of stopping
                    continue
            else:
                self.stdout.write(
                    self.style.WARNING(f'File {filename} not found, skipping...')
                )

        if self.dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'\nDRY RUN COMPLETE - Would have imported {total_imported} total records')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\nImport complete! Imported {total_imported} total records')
            )
            
            # Show final summary
            self.stdout.write('\n' + '='*50)
            self.stdout.write('IMPORT SUMMARY')
            self.stdout.write('='*50)
                CoordinateSystem,
                LineStyle,
                Organization,
                Project,
                ProjectTemplate,
                Stakeholder,
                User,
                Utility,
            )
            
            counts = {
                'Organizations': Organization.objects.count(),
                'Users': User.objects.count(), 
                'Projects': Project.objects.count(),
                'Project Templates': ProjectTemplate.objects.count(),
                'Stakeholders': Stakeholder.objects.count(),
                'Coordinate Systems': CoordinateSystem.objects.count(),
                'Line Styles': LineStyle.objects.count(),
                'Utilities': Utility.objects.count(),
            }
            
            for model_name, count in counts.items():
                self.stdout.write(f'{model_name}: {count}')
            self.stdout.write('='*50)

    def load_json_file(self, filepath):
        """Load and parse JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if self.limit:
                    data = data[:self.limit]
                return data
        except json.JSONDecodeError as e:
            raise CommandError(f'Invalid JSON in {filepath}: {str(e)}')
        except Exception as e:
            raise CommandError(f'Error reading {filepath}: {str(e)}')

    def safe_parse_datetime(self, value):
        """Safely parse datetime string"""
        if not value or value == 'null' or value is None:
            return None
        try:
            # Handle various datetime formats
            if isinstance(value, str):
                # Remove timezone info if present for simple parsing
                value = value.replace('+00', '').replace('Z', '')
                if '.' in value:
                    # Handle microseconds
                    value = value[:19]  # Keep only YYYY-MM-DD HH:MM:SS
                return parse_datetime(value + '+00:00')  # Add UTC timezone
            return value
        except Exception:
            return None

    def safe_parse_date(self, value):
        """Safely parse date string"""
        if not value or value == 'null' or value is None:
            return None
        try:
            if isinstance(value, str):
                return parse_date(value)
            return value
        except Exception:
            return None

    def safe_decimal(self, value):
        """Safely convert to Decimal"""
        if not value or value == 'null' or value is None:
            return None
        try:
            return Decimal(str(value))
        except (InvalidOperation, ValueError):
            return None

    def safe_int(self, value):
        """Safely convert to int"""
        if not value or value == 'null' or value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None

    def import_organizations(self, filepath):
        """Import organizations"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import organization: {item.get("name")}')
                count += 1
                continue

            # Handle setup_completed_by as integer reference
            if item.get('setup_completed_by'):
                # We'll need to map this later after users are imported
                pass

            org_data = {
                'id': item['id'],
                'name': item['name'],
                'logo_url': item.get('logo_url'),
                'website': item.get('website'),
                'primary_color': item.get('primary_color', '#3B82F6'),
                'secondary_color': item.get('secondary_color', '#10B981'),
                'contact_email': item.get('contact_email'),
                'contact_phone': item.get('contact_phone'),
                'address': item.get('address'),
                'city': item.get('city'),
                'state': item.get('state'),
                'zip_code': item.get('zip_code'),
                'country': item.get('country', 'USA'),
                'timezone': item.get('timezone', 'America/New_York'),
                'date_format': item.get('date_format', 'MM/DD/YYYY'),
                'currency': item.get('currency', 'USD'),
                'fiscal_year_start': self.safe_int(item.get('fiscal_year_start')) or 1,
                'setup_completed': item.get('setup_completed', False),
                'setup_completed_at': self.safe_parse_datetime(item.get('setup_completed_at')),
                'signin_image_url': item.get('signin_image_url'),
                'theme_config': item.get('theme_config', {}),
                'signin_footer_url': item.get('signin_footer_url'),
                'email_integration_config': item.get('email_integration_config'),
                'email_integration_enabled': item.get('email_integration_enabled', False),
                'email_provider': item.get('email_provider'),
                'email_sync_settings': item.get('email_sync_settings'),
                'email_redirect_uri': item.get('email_redirect_uri'),
            }

            Organization.objects.update_or_create(
                id=item['id'],
                defaults=org_data
            )
            count += 1

        return count

    def import_coordinate_systems(self, filepath):
        """Import coordinate systems"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import coordinate system: {item.get("name")}')
                count += 1
                continue

            cs_data = {
                'name': item['name'],
                'srid': item['srid'],
                'type': item.get('type', ''),
                'description': item.get('description'),
                'state': item.get('state') or 'Indiana',  # Ensure not null
                'zone_name': item.get('zone_name'),
                'county_code': item.get('county_code'),
                'projection_group': item.get('projection_group'),
                'lat_origin_deg': self.safe_decimal(item.get('lat_origin_deg')),
                'lat_origin_min': self.safe_decimal(item.get('lat_origin_min')),
                'lat_origin_sec': self.safe_decimal(item.get('lat_origin_sec')),
                'central_meridian_deg': self.safe_decimal(item.get('central_meridian_deg')),
                'central_meridian_min': self.safe_decimal(item.get('central_meridian_min')),
                'central_meridian_sec': self.safe_decimal(item.get('central_meridian_sec')),
                'central_meridian_scale': self.safe_decimal(item.get('central_meridian_scale')),
                'false_easting': self.safe_decimal(item.get('false_easting')),
                'false_northing': self.safe_decimal(item.get('false_northing')),
                'validation_point_easting': self.safe_decimal(item.get('validation_point_easting')),
                'validation_point_northing': self.safe_decimal(item.get('validation_point_northing')),
                'indiana_2digit_county': item.get('indiana_2digit_county'),
                'projection_group_abbreviation': item.get('projection_group_abbreviation'),
            }

            CoordinateSystem.objects.update_or_create(
                srid=item['srid'],
                defaults=cs_data
            )
            count += 1

        return count

    def import_auth_users(self, filepath):
        """Import users from auth_users"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import user: {item.get("email")}')
                count += 1
                continue

            # Extract metadata
            meta_data = item.get('raw_user_meta_data', {})
            
            # Create username from email if not provided
            username = meta_data.get('username', item['email'].split('@')[0])
            
            # Ensure username is unique
            base_username = username
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{base_username}_{counter}"
                counter += 1

            user_data = {
                'email': item['email'],
                'username': username,
                'first_name': meta_data.get('first_name', ''),
                'last_name': meta_data.get('last_name', ''),
                'is_active': True,
                'date_joined': self.safe_parse_datetime(item.get('created_at')),
                'last_login': self.safe_parse_datetime(item.get('last_sign_in_at')),
                'email_verified': meta_data.get('email_verified', False),
                'role': meta_data.get('role', 'user'),
                'unit_preference': meta_data.get('unit_preference', 'imperial'),
                'is_admin': meta_data.get('is_admin', False),
                'password': make_password(None),  # Set unusable password
            }

            # Try to link to organization if organization_id is provided
            org_id = meta_data.get('organization_id')
            if org_id and Organization.objects.filter(id=org_id).exists():
                user_data['organization_id'] = org_id

            User.objects.update_or_create(
                email=item['email'],
                defaults=user_data
            )
            count += 1

        return count

    def import_user_profiles(self, filepath):
        """Import and update user profiles"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would update user profile: {item.get("email")}')
                count += 1
                continue

            try:
                user = User.objects.get(email=item['email'])
                
                # Update user with profile data
                user.display_name = item.get('display_name')
                if item.get('first_name'):
                    user.first_name = item['first_name']
                if item.get('last_name'):
                    user.last_name = item['last_name']
                user.phone = item.get('phone')
                user.role = item.get('role', 'user')
                user.department = item.get('department')
                user.job_title = item.get('job_title')
                user.status = item.get('status', 'active')
                user.email_verified = item.get('email_verified', False)
                user.permissions = item.get('permissions', {})
                user.last_login_at = self.safe_parse_datetime(item.get('last_login_at'))
                user.last_seen_at = self.safe_parse_datetime(item.get('last_seen_at'))
                user.hourly_rate = self.safe_decimal(item.get('hourly_rate'))
                user.salary = self.safe_decimal(item.get('salary'))
                user.pay_rate_effective_date = self.safe_parse_date(item.get('pay_rate_effective_date'))
                user.pay_rate_notes = item.get('pay_rate_notes')
                user.email_domain = item.get('email_domain')

                # Link to organization if provided
                org_id = item.get('organization_id')
                if org_id:
                    try:
                        org = Organization.objects.get(id=org_id)
                        user.organization = org
                    except Organization.DoesNotExist:
                        pass

                user.save()
                count += 1

            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'User with email {item["email"]} not found, skipping profile update')
                )

        return count

    def import_project_templates(self, filepath):
        """Import project templates"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import template: {item.get("name")}')
                count += 1
                continue

            # Try to find created_by user
            created_by = None
            created_by_id = item.get('created_by_id')
            if created_by_id:
                # This might be a numeric ID, we need to handle the mapping
                pass

            # Try to find organization
            organization = None
            org_id = item.get('organization_id')
            if org_id:
                try:
                    organization = Organization.objects.get(id=org_id)
                except Organization.DoesNotExist:
                    pass

            template_data = {
                'name': item['name'],
                'description': item.get('description'),
                'icon': item.get('icon'),
                'color': item.get('color'),
                'is_active': item.get('is_active', True),
                'is_default': item.get('is_default', False),
                'organization': organization,
                'workflow_phases': item.get('workflow_phases', []),
                'settings': item.get('settings', {}),
                'monday_id': item.get('monday_id'),
            }

            # If we have a user to link as creator, add it
            if created_by:
                template_data['created_by'] = created_by

            # Set a default created_by user if none found
            if not created_by:
                first_admin = User.objects.filter(is_admin=True).first()
                if not first_admin:
                    # Create a default admin user if none exists
                    first_admin = User.objects.filter(is_superuser=True).first()
                if not first_admin:
                    # Use any user as fallback
                    first_admin = User.objects.first()
                if first_admin:
                    template_data['created_by'] = first_admin

            template, created = ProjectTemplate.objects.update_or_create(
                id=item['id'],
                defaults=template_data
            )

            count += 1

        return count

    def import_projects(self, filepath):
        """Import projects"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import project: {item.get("name")}')
                count += 1
                continue

            # Try to find organization
            organization = None
            org_id = item.get('organization_id')
            if org_id:
                try:
                    organization = Organization.objects.get(id=org_id)
                except Organization.DoesNotExist:
                    pass

            # Try to find template
            template = None
            template_id = item.get('template_id')
            if template_id:
                try:
                    template = ProjectTemplate.objects.get(id=template_id)
                except ProjectTemplate.DoesNotExist:
                    pass

            project_data = {
                'organization': organization,
                'monday_id': item.get('monday_id'),
                'name': item['name'],
                'client': item.get('client', ''),
                'description': item.get('description'),
                'start_date': self.safe_parse_date(item.get('start_date')),
                'end_date': self.safe_parse_date(item.get('end_date')),
                'manager_id': self.safe_int(item.get('manager_id')),
                'high_priority_items': self.safe_int(item.get('high_priority_items')),
                'medium_priority_items': self.safe_int(item.get('medium_priority_items')),
                'low_priority_items': self.safe_int(item.get('low_priority_items')),
                'record_id': item.get('record_id'),
                'client_job_number': item.get('client_job_number'),
                'work_type': item.get('work_type'),
                'rag_status': item.get('rag_status'),
                'project_id_only': item.get('project_id_only'),
                'phase_id_only': item.get('phase_id_only'),
                'last_milestone': item.get('last_milestone'),
                'coordination_type': item.get('coordination_type'),
                'project_funding': item.get('project_funding'),
                'ntp_date': self.safe_parse_date(item.get('ntp_date')),
                'letting_bid_date': self.safe_parse_date(item.get('letting_bid_date')),
                'this_month_status': item.get('this_month_status'),
                'status_update_date': self.safe_parse_date(item.get('status_update_date')),
                'client_pm': item.get('client_pm'),
                'hourly_rate': self.safe_decimal(item.get('hourly_rate')),
                'contract_amount': self.safe_decimal(item.get('contract_amount')),
                'billed_to_date': self.safe_decimal(item.get('billed_to_date')),
                'project_hours_for_billed': self.safe_decimal(item.get('project_hours_for_billed')),
                'wip': self.safe_decimal(item.get('wip')),
                'billed_plus_wip': self.safe_decimal(item.get('billed_plus_wip')),
                'current_cost': self.safe_decimal(item.get('current_cost')),
                'billed_percentage': self.safe_decimal(item.get('billed_percentage')),
                'profit_to_date': self.safe_decimal(item.get('profit_to_date')),
                'profit_percentage': self.safe_decimal(item.get('profit_percentage')),
                'project_priority': item.get('project_priority'),
                'egis_project_manager': item.get('egis_project_manager'),
                'egis_project_manager_email': item.get('egis_project_manager_email'),
                'client_contact': item.get('client_contact'),
                'project_health_rag': item.get('Project Health (RAG)', 'Project Health'),
                'current_phase': item.get('current_phase'),
                'coordinator_id': item.get('coordinator_id'),
                'template': template,
            }

            Project.objects.update_or_create(
                id=item['id'],
                defaults=project_data
            )
            count += 1

        return count

    def import_stakeholders(self, filepath):
        """Import stakeholders"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import stakeholder: {item.get("full_name")}')
                count += 1
                continue

            stakeholder_data = {
                'full_name': item.get('full_name', ''),
                'contact_company': item.get('contact_company', ''),
                'company_abbreviation': (item.get('company_abbreviation') or '')[:20] if item.get('company_abbreviation') else None,
                'type_delivery': item.get('type_delivery'),
                'stakeholder_type': item.get('stakeholder_type'),
                'business_phone': (item.get('business_phone') or '')[:20] if item.get('business_phone') else None,
                'business_fax': (item.get('business_fax') or '')[:20] if item.get('business_fax') else None,
                'mobile_phone': (item.get('mobile_phone') or '')[:20] if item.get('mobile_phone') else None,
                'email': item.get('email'),
                'is_utility_coordinator': item.get('is_utility_coordinator', False),
                'is_indot_authorized_rep': item.get('is_indot_authorized_rep', False),
                'aliases': item.get('aliases'),
                'company_logo': item.get('company_logo'),
                'client_company': item.get('client_company'),
                'full_name_company': item.get('full_name_company'),
            }

            # Use full_name_company and type_delivery for uniqueness if available
            unique_fields = {}
            if item.get('full_name_company'):
                unique_fields['full_name_company'] = item['full_name_company']
            else:
                unique_fields['full_name'] = item.get('full_name', '')
            
            if item.get('type_delivery'):
                unique_fields['type_delivery'] = item['type_delivery']

            if unique_fields:
                Stakeholder.objects.update_or_create(
                    **unique_fields,
                    defaults=stakeholder_data
                )
            else:
                # Fallback to create without unique constraint
                Stakeholder.objects.create(**stakeholder_data)
            
            count += 1

        return count

    def import_line_styles(self, filepath):
        """Import line styles"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import line style: {item.get("utility_type")}-{item.get("installation_type")}')
                count += 1
                continue

            line_style_data = {
                'utility_type': item.get('utility_type', ''),
                'installation_type': item.get('installation_type', ''),
                'line_color': item.get('line_color', '#000000'),
                'line_weight': float(item.get('line_weight', 1.0)),
                'line_pattern': item.get('line_pattern', 'solid'),
                'letter_symbols': item.get('letter_symbols'),
                'color_name': item.get('color_name'),
                'dash_pattern': item.get('dash_pattern'),
                'letter_spacing': self.safe_int(item.get('letter_spacing')),
                'standard_811': item.get('standard_811', True),
            }

            LineStyle.objects.update_or_create(
                utility_type=item.get('utility_type', ''),
                installation_type=item.get('installation_type', ''),
                defaults=line_style_data
            )
            count += 1

        return count

    def import_utilities(self, filepath):
        """Import utilities"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import utility: {item.get("name")}')
                count += 1
                continue

            # Try to find project
            project = None
            project_id = item.get('project_id')
            if project_id:
                try:
                    project = Project.objects.get(id=project_id)
                except Project.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Project {project_id} not found for utility {item.get("name")}, skipping')
                    )
                    continue

            if not project:
                self.stdout.write(
                    self.style.WARNING(f'No project specified for utility {item.get("name")}, skipping')
                )
                continue

            utility_data = {
                'project': project,
                'name': item.get('name', ''),
                'type': item.get('type', ''),
                'status': item.get('status', 'active'),
                'contact_name': item.get('contact_name'),
                'contact_email': item.get('contact_email'),
                'contact_phone': item.get('contact_phone'),
                'notes': item.get('notes'),
                'last_response': self.safe_parse_date(item.get('last_response')),
                'initiation_date': self.safe_parse_date(item.get('initiation_date')),
                'depth_notes': item.get('depth_notes'),
                'installation_depth': self.safe_decimal(item.get('installation_depth')),
                'vertical_clearance': self.safe_decimal(item.get('vertical_clearance')),
                'vertical_datum': item.get('vertical_datum'),
            }

            # Use a unique identifier if available, otherwise create new
            unique_fields = {'project': project, 'name': item.get('name', '')}
            if item.get('id'):
                # If we have an ID, try to use it for updates
                try:
                    utility = Utility.objects.get(id=item['id'])
                    for key, value in utility_data.items():
                        setattr(utility, key, value)
                    utility.save()
                except Utility.DoesNotExist:
                    Utility.objects.create(**utility_data)
            else:
                Utility.objects.update_or_create(
                    **unique_fields,
                    defaults=utility_data
                )
            
            count += 1

        return count

    def import_utility_line_data(self, filepath):
        """Import utility line data"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import utility line data: {item.get("name")}')
                count += 1
                continue

            # Try to find project
            project = None
            project_id = item.get('project_id')
            if project_id:
                try:
                    project = Project.objects.get(id=project_id)
                except Project.DoesNotExist:
                    continue

            if not project:
                continue

            # Try to find coordinate system
            coordinate_system = None
            cs_srid = item.get('coordinate_system_srid') or item.get('srid')
            if cs_srid:
                try:
                    coordinate_system = CoordinateSystem.objects.get(srid=cs_srid)
                except CoordinateSystem.DoesNotExist:
                    # Use a default coordinate system if available
                    coordinate_system = CoordinateSystem.objects.first()

            if not coordinate_system:
                self.stdout.write(
                    self.style.WARNING(f'No coordinate system found for utility line data {item.get("name")}, skipping')
                )
                continue

            # Try to find utility
            utility = None
            utility_id = item.get('utility_id')
            if utility_id:
                try:
                    utility = Utility.objects.get(id=utility_id)
                except Utility.DoesNotExist:
                    pass

            line_data = {
                'project': project,
                'utility': utility,
                'name': item.get('name', ''),
                'line_type': item.get('line_type', ''),
                'utility_type': item.get('utility_type', ''),
                'installation_type': item.get('installation_type', ''),
                'coordinate_system': coordinate_system,
                'properties': item.get('properties', {}),
                # Note: geometry field is not imported as it requires complex spatial data parsing
            }

            # Try to find line style
            line_style = None
            utility_type = item.get('utility_type')
            installation_type = item.get('installation_type')
            if utility_type and installation_type:
                try:
                    line_style = LineStyle.objects.get(
                        utility_type=utility_type,
                        installation_type=installation_type
                    )
                    line_data['line_style'] = line_style
                except LineStyle.DoesNotExist:
                    pass

            UtilityLineData.objects.create(**line_data)
            count += 1

        return count

    def import_conflicts(self, filepath):
        """Import conflicts"""
        data = self.load_json_file(filepath)
        count = 0
        
        for item in data:
            if self.dry_run:
                self.stdout.write(f'  Would import conflict: {item.get("description", "")[:50]}...')
                count += 1
                continue

            # Try to find project
            project = None
            project_id = item.get('project_id')
            if project_id:
                try:
                    project = Project.objects.get(id=project_id)
                except Project.DoesNotExist:
                    continue

            if not project:
                continue

            # Try to find utilities
            utility = None
            utility2 = None
            
            utility_id = item.get('utility_id')
            if utility_id:
                try:
                    utility = Utility.objects.get(id=utility_id)
                except Utility.DoesNotExist:
                    pass

            utility2_id = item.get('utility2_id')
            if utility2_id:
                try:
                    utility2 = Utility.objects.get(id=utility2_id)
                except Utility.DoesNotExist:
                    pass

            conflict_data = {
                'project': project,
                'utility': utility,
                'utility2': utility2,
                'description': item.get('description', ''),
                'status': item.get('status', 'open'),
                'priority': item.get('priority', 'medium'),
                'location': item.get('location'),
                'resolution_notes': item.get('resolution_notes'),
                'confidence_score': self.safe_int(item.get('confidence_score')),
                'conflict_elevation': self.safe_decimal(item.get('conflict_elevation')),
                'conflict_type': item.get('conflict_type'),
                'detected_timestamp': self.safe_parse_datetime(item.get('detected_timestamp')),
                'detection_method': item.get('detection_method', 'manual'),
                'impact_score': self.safe_int(item.get('impact_score')),
                'is_vertical_conflict': item.get('is_vertical_conflict', False),
                'likelihood_score': self.safe_int(item.get('likelihood_score')),
                'reviewed_by': item.get('reviewed_by'),
                'reviewed_timestamp': self.safe_parse_datetime(item.get('reviewed_timestamp')),
                'risk_score': self.safe_int(item.get('risk_score')),
                'vertical_clearance_violation': self.safe_decimal(item.get('vertical_clearance_violation')),
            }

            Conflict.objects.create(**conflict_data)
            count += 1

        return count