"""
Django management command for running MCP stdio server
"""

import asyncio
import sys
from django.core.management.base import BaseCommand
from django_mcp_server import mcp_app


class Command(BaseCommand):
    """
    Management command to run Django MCP server via stdio transport.
    This allows AI agents to connect to the Django application through MCP.
    """
    help = "Run Django MCP server via stdio transport"

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        """Run the MCP server using stdio transport"""
        verbosity = options.get('verbosity', 1)
        verbose = options.get('verbose', False)
        
        if verbose or verbosity > 1:
            self.stdout.write(
                self.style.SUCCESS('Starting Django MCP stdio server...')
            )
            # Get tool information from the app directly
            from CLEAR.mcp import mcp_app as clear_mcp_app
            tools = [func.__name__ for name, func in clear_mcp_app._tool_manager._tools.items()]
            self.stdout.write(f"Available tools: {tools}")
        
        try:
            # Run the stdio server asynchronously
            asyncio.run(mcp_app.run_stdio_async())
        except KeyboardInterrupt:
            if verbose or verbosity > 1:
                self.stdout.write(
                    self.style.WARNING('MCP server stopped by user')
                )
            sys.exit(0)
        except Exception as e:
            self.stderr.write(
                self.style.ERROR(f'MCP server error: {e}')
            )
            sys.exit(1)