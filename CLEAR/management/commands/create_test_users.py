"""
Management command to create test users for E2E testing
"""

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

"""

User = get_user_model()


class Command(BaseCommand):
    help = 'Create test users for E2E testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing test users first',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Deleting existing test users...')
            try:
                User.objects.filter(email__in=['<EMAIL>', '<EMAIL>']).delete()
                self.stdout.write('✅ Existing test users deleted')
            except Exception as e:
                self.stdout.write(f'⚠️ Could not delete existing users: {e}')

        # Create test user with minimal required fields
        try:
            testuser, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'username': 'testuser',
                    'first_name': 'Test',
                    'last_name': 'User',
                    'is_active': True,
                    'is_staff': False,
                    'is_superuser': False,
                }
            )
            testuser.set_password('testpass123')
            testuser.save()
            
            if created:
                self.stdout.write(f'✅ Created test user: {testuser.email}')
            else:
                self.stdout.write(f'✅ Updated existing test user: {testuser.email}')
        except Exception as e:
            self.stdout.write(f'❌ Failed to create test user: {e}')

        # Create admin user with minimal required fields
        try:
            admin, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'username': 'admin',
                    'first_name': 'Admin',
                    'last_name': 'User',
                    'is_active': True,
                    'is_staff': True,
                    'is_superuser': True,
                }
            )
            admin.set_password('adminpass123')
            admin.save()
            
            if created:
                self.stdout.write(f'✅ Created admin user: {admin.email}')
            else:
                self.stdout.write(f'✅ Updated existing admin user: {admin.email}')
        except Exception as e:
            self.stdout.write(f'❌ Failed to create admin user: {e}')

        self.stdout.write(
            self.style.SUCCESS('🎯 Test user creation completed!')
        )
"""