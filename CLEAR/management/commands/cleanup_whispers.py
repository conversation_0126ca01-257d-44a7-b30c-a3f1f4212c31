"""
Django management command to clean up expired WhisperMessage entries.

This command removes expired whisper messages from the database.
It should be run regularly via cron job or Celery periodic task.

Usage:
    python manage.py cleanup_whispers
    python manage.py cleanup_whispers --dry-run
    python manage.py cleanup_whispers --max-age-hours 24
"""

from datetime import timedelta
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone as django_timezone
from CLEAR.models import WhisperMessage

"""





class Command(BaseCommand):
    help = 'Clean up expired WhisperMessage entries'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without making changes'
        )
        parser.add_argument(
            '--max-age-hours',
            type=int,
            default=None,
            help='Delete messages older than specified hours (regardless of expires_at)'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Number of records to delete per batch (default: 1000)'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        max_age_hours = options.get('max_age_hours')
        batch_size = options['batch_size']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        now = django_timezone.now()
        
        # Build the query for expired messages
        query = WhisperMessage.objects.filter(expires_at__lt=now)
        
        # Add max age filter if specified
        if max_age_hours:
            cutoff_time = now - timedelta(hours=max_age_hours)
            query = query.filter(created_at__lt=cutoff_time)

        total_count = query.count()
        
        if total_count == 0:
            self.stdout.write('No expired whisper messages found.')
            return

        self.stdout.write(f'Found {total_count} expired whisper messages to clean up.')

        if dry_run:
            # Show sample of what would be deleted
            sample_messages = query.order_by('expires_at')[:10]
            self.stdout.write('\nSample messages that would be deleted:')
            for msg in sample_messages:
                self.stdout.write(
                    f'  ID: {msg.id}, From: {msg.from_user.username}, '
                    f'To: {msg.to_user.username}, Expired: {msg.expires_at}'
                )
            if total_count > 10:
                self.stdout.write(f'  ... and {total_count - 10} more messages')
            return

        # Delete in batches to avoid database locks
        deleted_count = 0
        batch_count = 0

        while True:
            with transaction.atomic():
                # Get IDs for this batch
                batch_ids = list(
                    query.values_list('id', flat=True)[:batch_size]
                )
                
                if not batch_ids:
                    break

                # Delete this batch
                batch_deleted = WhisperMessage.objects.filter(
                    id__in=batch_ids
                ).delete()[0]
                
                deleted_count += batch_deleted
                batch_count += 1

                self.stdout.write(
                    f'Batch {batch_count}: Deleted {batch_deleted} messages '
                    f'(Total: {deleted_count}/{total_count})'
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Cleanup complete! Deleted {deleted_count} expired whisper messages '
                f'in {batch_count} batches.'
            )
        )

        # Show statistics
        remaining_count = WhisperMessage.objects.count()
        self.stdout.write(f'Remaining whisper messages: {remaining_count}')
        
        # Show oldest and newest remaining messages
        if remaining_count > 0:
            oldest = WhisperMessage.objects.order_by('created_at').first()
            newest = WhisperMessage.objects.order_by('-created_at').first()
            
            self.stdout.write(f'Oldest remaining message: {oldest.created_at}')
            self.stdout.write(f'Newest remaining message: {newest.created_at}')
            
            # Count messages expiring in next 24 hours
            next_24h = now + timedelta(hours=24)
            expiring_soon = WhisperMessage.objects.filter(
                expires_at__lt=next_24h,
                expires_at__gte=now
            ).count()
            
            if expiring_soon > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f'{expiring_soon} messages will expire in the next 24 hours'
                    )
                )
"""