"""
Management command to generate sample analytics data for testing
"""

import random
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from CLEAR.models import (
from CLEAR.services.analytics import AnalyticsEngine

"""



    BusinessMetric,
    Conflict,
    Organization,
    Project,
    SystemMetric,
    Task,
    TimeEntry,
    User,
)


class Command(BaseCommand):
    help = 'Generate sample analytics data for testing the Reports & Analytics system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=90,
            help='Number of days of historical data to generate (default: 90)'
        )
        parser.add_argument(
            '--organization',
            type=str,
            help='Organization ID to generate data for (optional)'
        )

    def handle(self, *args, **options):
        days = options['days']
        org_id = options.get('organization')
        
        self.stdout.write(
            self.style.SUCCESS(f'Generating {days} days of analytics data...')
        )
        
        # Get or create organization
        if org_id:
            try:
                organization = Organization.objects.get(id=org_id)
            except Organization.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Organization {org_id} not found')
                )
                return
        else:
            organization = Organization.objects.first()
            if not organization:
                # Create a sample organization
                organization = Organization.objects.create(
                    id='sample_org',
                    name='Sample Organization',
                    primary_color='#3B82F6',
                    secondary_color='#10B981'
                )
                self.stdout.write(
                    self.style.WARNING('Created sample organization')
                )
        
        # Generate historical system metrics
        self._generate_system_metrics(organization, days)
        
        # Generate business metrics
        self._generate_business_metrics(organization, days)
        
        # Generate sample projects with realistic data
        self._generate_sample_projects(organization, days)
        
        # Generate time entries
        self._generate_time_entries(organization, days)
        
        # Generate conflicts
        self._generate_conflicts(organization, days)
        
        # Generate materialized business metrics using analytics engine
        self._generate_materialized_metrics(organization, days)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully generated analytics data for {organization.name}'
            )
        )

    def _generate_system_metrics(self, organization, days):
        """Generate system performance metrics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        metrics_config = [
            ('uptime', 'performance', 99.0, 99.9, '%'),
            ('response_time', 'performance', 150, 300, 'ms'),
            ('error_rate', 'performance', 0.05, 0.2, '%'),
            ('user_satisfaction', 'user_experience', 4.5, 5.0, '/5'),
            ('data_processing_speed', 'performance', 800, 1200, 'records/min'),
            ('memory_usage', 'system', 60, 85, '%'),
            ('cpu_usage', 'system', 45, 75, '%'),
            ('storage_usage', 'system', 70, 90, '%'),
        ]
        
        current_date = start_date
        while current_date <= end_date:
            for metric_name, category, min_val, max_val, unit in metrics_config:
                # Add some realistic variance
                base_value = min_val + (max_val - min_val) * 0.7
                variance = (max_val - min_val) * 0.2
                value = base_value + random.uniform(-variance, variance)
                
                SystemMetric.objects.create(
                    metric_name=metric_name,
                    metric_value=value,
                    metric_unit=unit,
                    category=category,
                    organization=organization,
                    timestamp=current_date,
                    aggregation_period='hour',
                    metadata={
                        'generated': True,
                        'environment': 'test'
                    }
                )
            
            current_date += timedelta(hours=6)  # Generate every 6 hours
        
        self.stdout.write('Generated system metrics')

    def _generate_business_metrics(self, organization, days):
        """Generate business-specific metrics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Generate monthly business metrics
        current_date = start_date.replace(day=1)  # Start of month
        month_count = 0
        
        while current_date <= end_date:
            month_end = (current_date.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)
            if month_end > end_date:
                month_end = end_date
            
            # Generate realistic growth trend
            growth_factor = 1 + (month_count * 0.05)  # 5% monthly growth
            base_projects = 15
            base_revenue = 180000
            
            metrics_data = [
                ('revenue', base_revenue * growth_factor + random.uniform(-20000, 30000), 'USD'),
                ('project_count', int(base_projects * growth_factor) + random.randint(-3, 5), 'count'),
                ('conflict_count', random.randint(8, 25), 'count'),
                ('resolution_time', random.uniform(2.5, 6.0), 'days'),
                ('budget_savings', random.uniform(15000, 45000), 'USD'),
                ('team_productivity', random.uniform(75, 95), 'percent'),
                ('customer_satisfaction', random.uniform(4.2, 4.9), 'rating'),
                ('utilization_rate', random.uniform(68, 88), 'percent'),
            ]
            
            for metric_type, value, unit in metrics_data:
                BusinessMetric.objects.update_or_create(
                    metric_type=metric_type,
                    period_start=current_date,
                    period_end=month_end,
                    organization=organization,
                    project=None,
                    defaults={
                        'value': Decimal(str(value)),
                        'unit': unit,
                        'additional_data': {
                            'generated': True,
                            'month': current_date.strftime('%B %Y')
                        }
                    }
                )
            
            # Move to next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
            month_count += 1
        
        self.stdout.write('Generated business metrics')

    def _generate_sample_projects(self, organization, days):
        """Generate sample projects with realistic data"""
        project_names = [
            'Downtown Infrastructure Upgrade',
            'Highway 65 Utility Coordination',
            'Residential Development Phase 2',
            'Municipal Water Line Extension',
            'Fiber Optic Network Expansion',
            'Shopping Center Utilities',
            'Industrial Park Development',
            'School District Infrastructure',
            'Hospital Campus Utilities',
            'Green Energy Initiative'
        ]
        
        statuses = ['planning', 'in_progress', 'completed', 'on_hold']
        
        # Get or create a sample user
        user = User.objects.filter(organization=organization).first()
        if not user:
            user = User.objects.create_user(
                username='analytics_user',
                email='<EMAIL>',
                first_name='Analytics',
                last_name='User',
                organization=organization
            )
        
        for i, name in enumerate(project_names):
            # Create projects with realistic timing
            created_days_ago = random.randint(10, days - 10)
            created_at = timezone.now() - timedelta(days=created_days_ago)
            
            project = Project.objects.create(
                name=name,
                description=f'Sample project for analytics testing: {name}',
                status=random.choice(statuses),
                budget=Decimal(random.uniform(50000, 500000)),
                actual_cost=Decimal(random.uniform(40000, 450000)),
                completion_percentage=random.randint(25, 95),
                organization=organization,
                created_by=user,
                created_at=created_at,
                start_date=(created_at + timedelta(days=random.randint(1, 14))).date(),
                end_date=(created_at + timedelta(days=random.randint(30, 180))).date()
            )
            
            # Add some tasks to projects
            for j in range(random.randint(5, 15)):
                Task.objects.create(
                    title=f'Task {j+1} for {name}',
                    description=f'Sample task for project {name}',
                    project=project,
                    assigned_to=user,
                    status=random.choice(['pending', 'in_progress', 'completed']),
                    priority=random.choice(['low', 'medium', 'high']),
                    created_at=created_at + timedelta(days=random.randint(0, created_days_ago - 5)),
                    due_date=(timezone.now() + timedelta(days=random.randint(5, 30))).date()
                )
        
        self.stdout.write('Generated sample projects and tasks')

    def _generate_time_entries(self, organization, days):
        """Generate realistic time entries"""
        users = User.objects.filter(organization=organization)
        projects = Project.objects.filter(organization=organization)
        
        if not users.exists() or not projects.exists():
            return
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        current_date = start_date
        while current_date <= end_date:
            # Skip weekends
            if current_date.weekday() < 5:  # Monday=0, Sunday=6
                for user in users:
                    # 70% chance user logs time each day
                    if random.random() < 0.7:
                        project = random.choice(projects)
                        hours = random.uniform(4, 9)  # 4-9 hours per day
                        
                        TimeEntry.objects.create(
                            user=user,
                            project=project,
                            hours=Decimal(str(hours)),
                            start_time=current_date.replace(
                                hour=random.randint(8, 10),
                                minute=random.randint(0, 59)
                            ),
                            end_time=current_date.replace(
                                hour=random.randint(16, 18),
                                minute=random.randint(0, 59)
                            ),
                            description=f'Work on {project.name}',
                            billable=random.choice([True, False]),
                            approved=True
                        )
            
            current_date += timedelta(days=1)
        
        self.stdout.write('Generated time entries')

    def _generate_conflicts(self, organization, days):
        """Generate sample utility conflicts"""
        projects = Project.objects.filter(organization=organization)
        if not projects.exists():
            return
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        conflict_types = [
            'Underground utilities interference',
            'Overhead power line conflict',
            'Water main relocation required',
            'Gas line proximity issue',
            'Fiber optic cable conflict',
            'Storm drain interference',
            'Telecom cable relocation',
            'Electric conduit conflict'
        ]
        
        # Generate 1-3 conflicts per week
        weeks = days // 7
        for week in range(weeks):
            week_start = start_date + timedelta(weeks=week)
            conflicts_this_week = random.randint(1, 3)
            
            for _ in range(conflicts_this_week):
                project = random.choice(projects)
                conflict_date = week_start + timedelta(days=random.randint(0, 6))
                
                conflict = Conflict.objects.create(
                    project=project,
                    conflict_type=random.choice(conflict_types),
                    description='Sample conflict for analytics testing',
                    priority=random.choice(['low', 'medium', 'high']),
                    status=random.choice(['detected', 'in_progress', 'resolved']),
                    estimated_cost=Decimal(random.uniform(5000, 50000)),
                    created_at=conflict_date
                )
                
                # Some conflicts are resolved
                if conflict.status == 'resolved':
                    resolution_days = random.randint(1, 14)
                    conflict.resolved_at = conflict_date + timedelta(days=resolution_days)
                    conflict.save()
        
        self.stdout.write('Generated utility conflicts')

    def _generate_materialized_metrics(self, organization, days):
        """Generate materialized business metrics using the analytics engine"""
        analytics = AnalyticsEngine(organization=organization)
        
        # Generate metrics for each month
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        current_date = start_date.replace(day=1)  # Start of month
        
        while current_date <= end_date:
            month_end = (current_date.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)
            if month_end > end_date:
                month_end = end_date
            
            # Generate materialized metrics for this period
            analytics.generate_business_metrics(current_date, month_end)
            
            # Move to next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        
        self.stdout.write('Generated materialized analytics metrics')
"""