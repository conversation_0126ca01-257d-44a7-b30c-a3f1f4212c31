"""
MCP (Model Context Protocol) Tools for CLEAR Application

This module provides comprehensive MCP tools using both django-mcp and django-mcp-server:
1. Auto-generated CRUD tools for all Django models via ModelQueryToolset
2. Custom enhanced tools for common workflows and analytics

AI agents can use these tools to:
- Perform CRUD operations on all CLEAR models with permission filtering
- Execute complex queries and analytics
- Access spatial data and conflict detection
- Manage documents, projects, utilities, and team coordination
"""

from django_mcp_server import mcp_app
from django_mcp_server import ModelQueryToolset
from django.db.models import Q, Count
from django.utils import timezone

# Import all models for auto-tool generation
from .models import (
    # Core models for enhanced tools
    Project, User, Utility, Conflict, Document,
    # All models for auto-generation
    CoordinateSystem, Stakeholder, Organization, Task, Workflow,
    DocumentVersion, DocumentShare, Conversation, ChatMessage,
    SystemMetric, AnalyticsReport, TimeEntry, KnowledgeArticle,
    UserActivity, ImplementationTask
)


def get_user_projects(user):
    """Get projects accessible by a user"""
    if not user or not user.is_authenticated:
        # For stdio transport with no authentication, return all projects
        return Project.objects.all()
    
    # Users can see projects they manage, coordinate, or are assigned to
    return Project.objects.filter(
        Q(manager=user) |
        Q(coordinator_id=str(user.id)) |
        Q(egis_project_manager=user.username)
    )


# =============================================================================
# AUTO-GENERATED MODEL TOOLS using django-mcp-server
# These provide full CRUD operations for all models with permission filtering
# =============================================================================

class ProjectQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Project model with permission filtering"""
    model = Project
    
    def get_queryset(self):
        """Filter projects based on user permissions"""
        user = getattr(self.request, 'user', None)
        return get_user_projects(user)


class UserQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for User model with permission filtering"""
    model = User
    
    def get_queryset(self):
        """Limit to users the current user can access"""
        user = getattr(self.request, 'user', None)
        if not user or not user.is_authenticated:
            return User.objects.all()
        
        if user.is_staff:
            return super().get_queryset()
        else:
            # Return users from projects the current user is involved in
            user_projects = get_user_projects(user)
            return super().get_queryset().filter(
                Q(managed_projects__in=user_projects) |
                Q(coordinated_projects__in=user_projects) |
                Q(id=user.id)
            ).distinct()


class UtilityQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Utility model with project filtering"""
    model = Utility
    
    def get_queryset(self):
        user = getattr(self.request, 'user', None)
        user_projects = get_user_projects(user)
        return super().get_queryset().filter(project__in=user_projects)


class ConflictQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Conflict model with project filtering"""
    model = Conflict
    
    def get_queryset(self):
        user = getattr(self.request, 'user', None)
        user_projects = get_user_projects(user)
        return super().get_queryset().filter(project__in=user_projects)


class DocumentQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Document model with permission filtering"""
    model = Document
    
    def get_queryset(self):
        user = getattr(self.request, 'user', None)
        user_projects = get_user_projects(user)
        if not user or not user.is_authenticated:
            return Document.objects.all()
        
        return super().get_queryset().filter(
            Q(project__in=user_projects) |
            Q(uploaded_by=user)
        )


# Additional auto-generated tools for other key models
class TaskQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Task model"""
    model = Task


class WorkflowQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Workflow model"""
    model = Workflow


class OrganizationQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Organization model"""
    model = Organization


class StakeholderQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Stakeholder model"""
    model = Stakeholder


class ConversationQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for Conversation model"""
    model = Conversation


class AnalyticsReportQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for AnalyticsReport model"""
    model = AnalyticsReport


class KnowledgeArticleQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for KnowledgeArticle model"""
    model = KnowledgeArticle


class ImplementationTaskQueryTool(ModelQueryToolset):
    """Auto-generated CRUD tools for ImplementationTask model"""
    model = ImplementationTask


# =============================================================================
# ENHANCED CUSTOM TOOLS using django-mcp for complex workflows
# These provide specialized functionality beyond basic CRUD
# =============================================================================

@mcp_app.tool()
def get_project_dashboard(project_id: str = None) -> dict:
    """
    Get comprehensive project dashboard with all key metrics and recent activity.
    
    Args:
        project_id: Optional specific project UUID, otherwise returns summary for all accessible projects
    
    Returns:
        Dashboard data with projects, conflicts, utilities, documents, and recent activity
    """
    user = mcp_app.get_context().get("user")
    user_projects = get_user_projects(user)
    
    if project_id:
        try:
            project = user_projects.get(id=project_id)
            projects = [project]
        except Project.DoesNotExist:
            return {"error": "Project not found or access denied"}
    else:
        projects = user_projects[:10]  # Limit to 10 for performance
    
    # Aggregate metrics across all accessible projects
    total_projects = user_projects.count()
    active_conflicts = Conflict.objects.filter(project__in=user_projects, status="active").count()
    total_utilities = Utility.objects.filter(project__in=user_projects).count()
    pending_documents = Document.objects.filter(project__in=user_projects, status="pending").count()
    
    # Recent activity (if available)
    recent_activities = []
    if hasattr(UserActivity, 'objects'):
        recent_activities = UserActivity.objects.filter(
            user=user
        ).order_by('-created_at')[:5].values('activity_type', 'description', 'created_at')
    
    return {
        "summary": {
            "total_projects": total_projects,
            "active_conflicts": active_conflicts,
            "total_utilities": total_utilities,
            "pending_documents": pending_documents,
        },
        "projects": [
            {
                "id": str(p.id),
                "name": p.name,
                "status": p.rag_status,
                "manager": p.manager.get_full_name() if p.manager else None,
                "conflicts": p.conflicts.filter(status="active").count() if hasattr(p, 'conflicts') else 0,
                "utilities": p.utilities.count() if hasattr(p, 'utilities') else 0,
            }
            for p in projects
        ],
        "recent_activity": list(recent_activities),
        "generated_at": timezone.now().isoformat()
    }


@mcp_app.tool()
def search_across_all_models(query: str, model_types: list = None, limit: int = 20) -> dict:
    """
    Search across multiple CLEAR models with a single query.
    
    Args:
        query: Search term to look for across models
        model_types: Optional list of model types to search (projects, utilities, documents, etc.)
        limit: Maximum results per model type
    
    Returns:
        Search results organized by model type
    """
    user = mcp_app.get_context().get("user")
    user_projects = get_user_projects(user)
    
    results = {}
    
    # Default model types if not specified
    if not model_types:
        model_types = ['projects', 'utilities', 'conflicts', 'documents', 'users', 'organizations']
    
    # Search projects
    if 'projects' in model_types:
        project_results = user_projects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query) |
            Q(address__icontains=query)
        )[:limit]
        results['projects'] = [
            {
                "id": str(p.id),
                "name": p.name,
                "type": "project",
                "description": p.description or "",
                "status": p.rag_status,
            }
            for p in project_results
        ]
    
    # Search utilities
    if 'utilities' in model_types:
        utility_results = Utility.objects.filter(
            project__in=user_projects
        ).filter(
            Q(name__icontains=query) |
            Q(utility_type__icontains=query) |
            Q(company_name__icontains=query)
        )[:limit]
        results['utilities'] = [
            {
                "id": str(u.id),
                "name": u.name,
                "type": "utility",
                "utility_type": u.utility_type,
                "company": u.company_name or "",
            }
            for u in utility_results
        ]
    
    # Search documents
    if 'documents' in model_types:
        document_results = Document.objects.filter(
            Q(project__in=user_projects) |
            Q(uploaded_by=user) if user and user.is_authenticated else Q()
        ).filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(file_name__icontains=query)
        )[:limit]
        results['documents'] = [
            {
                "id": str(d.id),
                "title": d.title,
                "type": "document",
                "file_name": d.file_name or "",
                "uploaded_by": d.uploaded_by.get_full_name() if d.uploaded_by else None,
            }
            for d in document_results
        ]
    
    # Search organizations
    if 'organizations' in model_types:
        org_results = Organization.objects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        )[:limit]
        results['organizations'] = [
            {
                "id": str(o.id),
                "name": o.name,
                "type": "organization",
                "description": o.description or "",
            }
            for o in org_results
        ]
    
    return {
        "query": query,
        "results": results,
        "total_results": sum(len(r) for r in results.values()),
        "searched_at": timezone.now().isoformat()
    }


@mcp_app.tool()
def get_spatial_conflicts_analysis(project_id: str = None, severity: str = None) -> dict:
    """
    Get detailed spatial conflict analysis with geographic and impact data.
    
    Args:
        project_id: Optional specific project UUID
        severity: Optional filter by conflict severity (high, medium, low)
    
    Returns:
        Spatial conflict analysis with locations, affected utilities, and resolution status
    """
    user = mcp_app.get_context().get("user")
    user_projects = get_user_projects(user)
    
    conflicts_qs = Conflict.objects.filter(project__in=user_projects)
    
    if project_id:
        try:
            project = user_projects.get(id=project_id)
            conflicts_qs = conflicts_qs.filter(project=project)
        except Project.DoesNotExist:
            return {"error": "Project not found or access denied"}
    
    if severity:
        conflicts_qs = conflicts_qs.filter(severity__icontains=severity)
    
    conflicts = conflicts_qs.select_related('project').prefetch_related('utilities')
    
    # Analyze conflicts by status and severity
    status_analysis = conflicts.values('status').annotate(count=Count('id'))
    severity_analysis = []
    if hasattr(Conflict, 'severity'):
        severity_analysis = conflicts.values('severity').annotate(count=Count('id'))
    
    # Geographic clustering if location data available
    geographic_clusters = []
    if hasattr(Conflict, 'location'):
        geographic_clusters = [
            {
                "conflict_id": str(c.id),
                "project": c.project.name,
                "location": {
                    "latitude": float(c.location.y) if c.location else None,
                    "longitude": float(c.location.x) if c.location else None,
                },
                "utilities_affected": [u.name for u in c.utilities.all()],
                "status": c.status,
                "severity": getattr(c, 'severity', 'unknown'),
            }
            for c in conflicts[:50]  # Limit for performance
        ]
    
    return {
        "analysis": {
            "total_conflicts": conflicts.count(),
            "status_breakdown": list(status_analysis),
            "severity_breakdown": list(severity_analysis),
        },
        "geographic_data": geographic_clusters,
        "project_scope": f"Project {project_id}" if project_id else "All accessible projects",
        "generated_at": timezone.now().isoformat()
    }


@mcp_app.tool()
def get_team_workload_analysis(include_inactive: bool = False) -> dict:
    """
    Analyze team workload across projects, tasks, and responsibilities.
    
    Args:
        include_inactive: Whether to include inactive users in analysis
    
    Returns:
        Team workload analysis with project assignments and activity metrics
    """
    user = mcp_app.get_context().get("user")
    
    # Get all users based on permissions
    if not user or not user.is_authenticated:
        users_qs = User.objects.all()
    elif user.is_staff:
        users_qs = User.objects.all()
    else:
        user_projects = get_user_projects(user)
        users_qs = User.objects.filter(
            Q(managed_projects__in=user_projects) |
            Q(coordinated_projects__in=user_projects) |
            Q(id=user.id)
        ).distinct()
    
    if not include_inactive:
        users_qs = users_qs.filter(is_active=True)
    
    team_analysis = []
    for team_member in users_qs[:50]:  # Limit for performance
        # Count projects managed and coordinated
        managed_projects = team_member.managed_projects.count() if hasattr(team_member, 'managed_projects') else 0
        
        # Count tasks if available
        assigned_tasks = 0
        if hasattr(Task, 'objects'):
            assigned_tasks = Task.objects.filter(assigned_to=team_member).count()
        
        # Recent activity
        recent_activity_count = 0
        if hasattr(UserActivity, 'objects'):
            recent_activity_count = UserActivity.objects.filter(
                user=team_member,
                created_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).count()
        
        team_analysis.append({
            "user_id": str(team_member.id),
            "name": team_member.get_full_name(),
            "username": team_member.username,
            "role": getattr(team_member, 'role', 'unknown'),
            "workload": {
                "managed_projects": managed_projects,
                "assigned_tasks": assigned_tasks,
                "recent_activity_count": recent_activity_count,
            },
            "last_login": team_member.last_login.isoformat() if team_member.last_login else None,
            "is_active": team_member.is_active,
        })
    
    # Summary statistics
    total_managed_projects = sum(t["workload"]["managed_projects"] for t in team_analysis)
    total_assigned_tasks = sum(t["workload"]["assigned_tasks"] for t in team_analysis)
    active_users = sum(1 for t in team_analysis if t["is_active"])
    
    return {
        "team_summary": {
            "total_team_members": len(team_analysis),
            "active_members": active_users,
            "total_managed_projects": total_managed_projects,
            "total_assigned_tasks": total_assigned_tasks,
        },
        "team_members": team_analysis,
        "analysis_date": timezone.now().isoformat()
    }


# MCP server configuration is handled by django-mcp and DJANGO_MCP_GLOBAL_SERVER_CONFIG in settings.py