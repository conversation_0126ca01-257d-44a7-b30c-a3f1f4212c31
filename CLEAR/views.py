"""
CLEAR Views Compatibility Module

This module maintains backward compatibility for URL imports while the views
are being migrated to a package structure. It imports all views from the
views package to ensure that existing URL patterns continue to work.

This is a temporary compatibility layer during the migration process.
"""

    from .views.admin_views import *
    from .views.analytics_views import *
    from .views.auth_views import *
    from .views.dashboard_views import *
    from .views.document_views import *
    from .views.gis_views import *
    from .views.htmx_views import *
    from .views.knowledge_views import *
    from .views.mapping_views import *
    from .views.messaging_views import *
    from .views.profile_views import *
    from .views.project_views import *
    from .views.reporting_views import *
    from .views.stakeholder_views import *
    from .views.task_views import *
    from django.contrib.auth import logout as auth_logout
    from django.contrib.auth.decorators import login_required
    from django.contrib.auth.forms import AuthenticationForm
    from django.http import HttpResponse
    from django.shortcuts import redirect, render

"""

# Import all views from the views package to maintain backward compatibility
try:
except ImportError:
    # If views package import fails, provide critical stub functions
    
    def login_view(request):
        """Stub login view"""
        if request.user.is_authenticated:
            return redirect('/dashboard/')
        return render(request, 'auth/login.html', {'form': AuthenticationForm()})
    
    def logout_view(request):
        """Stub logout view"""
        auth_logout(request)
        return redirect('/login/')
    
    @login_required
    def dashboard(request):
        """Main dashboard view with authentication required"""
        return render(request, 'dashboard/index.html', {'user': request.user})
    
    # Provide basic stub for other critical views
    @login_required
    def dashboard_stats(request):
        return HttpResponse("Dashboard stats not implemented", status=501)
    
    @login_required
    def recent_activity(request):
        return HttpResponse("Recent activity not implemented", status=501)
    
    @login_required
    def messaging_interface(request):
        return HttpResponse("Messaging interface not implemented", status=501)
    
    # Add stubs for other critical views as needed
    class SignupView:
        @staticmethod
        def as_view():
            def view(request):
                return HttpResponse("Signup not implemented", status=501)
            return view
    
    class ProjectListView:
        @staticmethod
        def as_view():
            def view(request):
                return HttpResponse("Project list not implemented", status=501)
            return view
    
    class ProjectDetailView:
        @staticmethod
        def as_view():
            def view(request):
                return HttpResponse("Project detail not implemented", status=501)
            return view
    
    class ProjectCreateView:
        @staticmethod
        def as_view():
            def view(request):
                return HttpResponse("Project create not implemented", status=501)
            return view
    
    class ProjectEditView:
        @staticmethod
        def as_view():
            def view(request):
                return HttpResponse("Project edit not implemented", status=501)
            return view
"""