"""
HTMX API URL patterns for converted endpoints.

These URLs map to HTMX views that return HTML fragments instead of JSON.
"""

from django.urls import path
from ..views import api_htmx_conversion, extended_htmx_conversion

"""



app_name = 'htmx-api'

urlpatterns = [
    # ========== USER MANAGEMENT ==========
    path('users/', api_htmx_conversion.user_list_htmx, name='user-list-htmx'),
    path('users/<uuid:user_id>/', api_htmx_conversion.user_profile_htmx, name='user-profile-htmx'),
    path('users/<uuid:user_id>/update/', api_htmx_conversion.user_update_htmx, name='user-update-htmx'),
    
    # ========== UTILITY DATA MANAGEMENT ==========
    path('utilities/', api_htmx_conversion.utility_list_htmx, name='utility-list-htmx'),
    path('utilities/project/<uuid:project_id>/', api_htmx_conversion.utility_list_htmx, name='utility-list-project-htmx'),
    path('utilities/<uuid:utility_id>/', api_htmx_conversion.utility_detail_htmx, name='utility-detail-htmx'),
    path('utilities/<uuid:utility_id>/update/', api_htmx_conversion.utility_update_htmx, name='utility-update-htmx'),
    path('utilities/<uuid:utility_id>/delete/', api_htmx_conversion.utility_delete_htmx, name='utility-delete-htmx'),
    path('utilities/create/<uuid:project_id>/', api_htmx_conversion.utility_create_htmx, name='utility-create-htmx'),
    
    # ========== CONFLICT MANAGEMENT ==========
    path('conflicts/', api_htmx_conversion.conflict_list_htmx, name='conflict-list-htmx'),
    path('conflicts/project/<uuid:project_id>/', api_htmx_conversion.conflict_list_htmx, name='conflict-list-project-htmx'),
    path('conflicts/<uuid:conflict_id>/', api_htmx_conversion.conflict_detail_htmx, name='conflict-detail-htmx'),
    path('conflicts/<uuid:conflict_id>/resolve/', api_htmx_conversion.conflict_resolve_htmx, name='conflict-resolve-htmx'),
    path('conflicts/detection/<uuid:project_id>/', api_htmx_conversion.conflict_detection_htmx, name='conflict-detection-htmx'),
    
    # ========== DOCUMENT MANAGEMENT ==========
    path('documents/', api_htmx_conversion.document_list_htmx, name='document-list-htmx'),
    path('documents/project/<uuid:project_id>/', api_htmx_conversion.document_list_htmx, name='document-list-project-htmx'),
    path('documents/upload/', api_htmx_conversion.document_upload_htmx, name='document-upload-htmx'),
    path('documents/<uuid:document_id>/delete/', api_htmx_conversion.document_delete_htmx, name='document-delete-htmx'),
    
    # ========== NOTIFICATION MANAGEMENT ==========
    path('notifications/', api_htmx_conversion.notification_list_htmx, name='notification-list-htmx'),
    path('notifications/<uuid:notification_id>/mark-read/', api_htmx_conversion.notification_mark_read_htmx, name='notification-mark-read-htmx'),
    path('notifications/mark-all-read/', api_htmx_conversion.notification_mark_all_read_htmx, name='notification-mark-all-read-htmx'),
    
    # ========== CHAT/MESSAGING ==========
    path('chat/messages/', api_htmx_conversion.chat_messages_htmx, name='chat-messages-htmx'),
    path('chat/send/', api_htmx_conversion.chat_send_message_htmx, name='chat-send-message-htmx'),
    
    # ========== DASHBOARD DATA ==========
    path('dashboard/stats/', api_htmx_conversion.dashboard_stats_htmx, name='dashboard-stats-htmx'),
    path('dashboard/recent-activity/', api_htmx_conversion.recent_activity_htmx, name='recent-activity-htmx'),
    
    # ========== EXTENDED CONVERSIONS ==========
    # Projects
    path('projects/', extended_htmx_conversion.project_list_htmx, name='project-list-htmx'),
    path('projects/<uuid:project_id>/', extended_htmx_conversion.project_detail_htmx, name='project-detail-htmx'),
    path('projects/<uuid:project_id>/update/', extended_htmx_conversion.project_update_htmx, name='project-update-htmx'),
    
    # Tasks
    path('tasks/', extended_htmx_conversion.task_list_htmx, name='task-list-htmx'),
    path('tasks/project/<uuid:project_id>/', extended_htmx_conversion.task_list_htmx, name='task-list-project-htmx'),
    path('tasks/<uuid:task_id>/toggle-complete/', extended_htmx_conversion.task_toggle_complete_htmx, name='task-toggle-complete-htmx'),
    
    # Comments
    path('comments/', extended_htmx_conversion.comment_list_htmx, name='comment-list-htmx'),
    path('comments/create/', extended_htmx_conversion.comment_create_htmx, name='comment-create-htmx'),
    
    # Time Tracking
    path('time-entries/', extended_htmx_conversion.time_entry_list_htmx, name='time-entry-list-htmx'),
    path('time-entries/create/', extended_htmx_conversion.time_entry_create_htmx, name='time-entry-create-htmx'),
    
    # Reports
    path('reports/', extended_htmx_conversion.report_list_htmx, name='report-list-htmx'),
    path('reports/<uuid:report_id>/execute/', extended_htmx_conversion.report_execute_htmx, name='report-execute-htmx'),
    
    # Stakeholders
    path('stakeholders/', extended_htmx_conversion.stakeholder_list_htmx, name='stakeholder-list-htmx'),
    path('stakeholders/project/<uuid:project_id>/', extended_htmx_conversion.stakeholder_list_htmx, name='stakeholder-list-project-htmx'),
    
    # Feature Requests
    path('feature-requests/', extended_htmx_conversion.feature_request_list_htmx, name='feature-request-list-htmx'),
    path('feature-requests/<uuid:feature_id>/vote/', extended_htmx_conversion.feature_request_vote_htmx, name='feature-request-vote-htmx'),
]
"""