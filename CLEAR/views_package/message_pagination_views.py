"""
HTMX views for paginated message loading with infinite scroll and load-more functionality.
"""

import json
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from ..models import ChatMessage, Conversation
from ..services.message_pagination import message_pagination_service

"""




@login_required
@require_http_methods(["GET"])
def conversation_messages_paginated(request, conversation_id):
    """
    HTMX endpoint for loading paginated conversation messages.
    Supports infinite scroll and load-more functionality.
    """
    cursor = request.GET.get('cursor')
    page_size = int(request.GET.get('page_size', 25))
    thread_id = request.GET.get('thread_id')
    load_type = request.GET.get('load_type', 'append')  # 'append' or 'prepend'
    
    # Get paginated messages
    result = message_pagination_service.get_conversation_messages(
        conversation_id=conversation_id,
        user_id=request.user.id,
        cursor=cursor,
        page_size=page_size,
        thread_id=int(thread_id) if thread_id else None
    )
    
    if 'error' in result:
        return HttpResponse(f"Error: {result['error']}", status=400)
    
    context = {
        'messages': result['messages'],
        'pagination': result['pagination'],
        'conversation': result['conversation'],
        'thread_info': result.get('thread_info'),
        'load_type': load_type,
        'user': request.user
    }
    
    # Return appropriate template based on load type
    if load_type == 'initial':
        template = 'components/messages/message_thread_with_pagination.html'
    else:
        template = 'components/messages/message_list_partial.html'
    
    return render(request, template, context)


@login_required
@require_http_methods(["GET"])
def channel_messages_paginated(request, channel):
    """
    HTMX endpoint for loading paginated channel messages (legacy support).
    """
    cursor = request.GET.get('cursor')
    page_size = int(request.GET.get('page_size', 25))
    project_id = request.GET.get('project_id')
    load_type = request.GET.get('load_type', 'append')
    
    # Get paginated messages
    result = message_pagination_service.get_channel_messages(
        channel=channel,
        user_id=request.user.id,
        project_id=int(project_id) if project_id else None,
        cursor=cursor,
        page_size=page_size
    )
    
    context = {
        'messages': result['messages'],
        'pagination': result['pagination'],
        'channel': result['channel'],
        'load_type': load_type,
        'user': request.user
    }
    
    if load_type == 'initial':
        template = 'components/messages/channel_thread_with_pagination.html'
    else:
        template = 'components/messages/message_list_partial.html'
    
    return render(request, template, context)


@login_required
@require_http_methods(["GET"])
def thread_messages_paginated(request, parent_message_id):
    """
    HTMX endpoint for loading paginated thread messages.
    """
    cursor = request.GET.get('cursor')
    page_size = int(request.GET.get('page_size', 25))
    load_type = request.GET.get('load_type', 'append')
    
    # Get paginated thread messages
    result = message_pagination_service.get_thread_messages(
        parent_message_id=parent_message_id,
        user_id=request.user.id,
        cursor=cursor,
        page_size=page_size
    )
    
    if 'error' in result:
        return HttpResponse(f"Error: {result['error']}", status=400)
    
    context = {
        'messages': result['messages'],
        'pagination': result['pagination'],
        'thread': result['thread'],
        'load_type': load_type,
        'user': request.user,
        'parent_message_id': parent_message_id
    }
    
    if load_type == 'initial':
        template = 'components/messages/thread_modal_with_pagination.html'
    else:
        template = 'components/messages/message_list_partial.html'
    
    return render(request, template, context)


@login_required
@require_http_methods(["GET"])
def message_search_paginated(request):
    """
    HTMX endpoint for paginated message search.
    """
    query = request.GET.get('q', '').strip()
    cursor = request.GET.get('cursor')
    page_size = int(request.GET.get('page_size', 20))
    conversation_id = request.GET.get('conversation_id')
    project_id = request.GET.get('project_id')
    load_type = request.GET.get('load_type', 'append')
    
    if not query:
        return render(request, 'components/messages/search_empty.html', {'query': ''})
    
    # Get paginated search results
    result = message_pagination_service.search_messages(
        query=query,
        user_id=request.user.id,
        conversation_id=conversation_id,
        project_id=int(project_id) if project_id else None,
        cursor=cursor,
        page_size=page_size
    )
    
    context = {
        'messages': result['messages'],
        'pagination': result['pagination'],
        'search': result['search'],
        'load_type': load_type,
        'user': request.user
    }
    
    if load_type == 'initial':
        template = 'components/messages/search_results_with_pagination.html'
    else:
        template = 'components/messages/message_list_partial.html'
    
    return render(request, template, context)


@login_required
@require_http_methods(["POST"])
def load_more_messages(request):
    """
    HTMX endpoint for load-more button functionality.
    """
    data = json.loads(request.body)
    
    message_type = data.get('type')  # 'conversation', 'channel', 'thread', 'search'
    cursor = data.get('cursor')
    page_size = int(data.get('page_size', 25))
    
    context = {'user': request.user, 'load_type': 'append'}
    
    if message_type == 'conversation':
        conversation_id = data.get('conversation_id')
        thread_id = data.get('thread_id')
        
        result = message_pagination_service.get_conversation_messages(
            conversation_id=conversation_id,
            user_id=request.user.id,
            cursor=cursor,
            page_size=page_size,
            thread_id=int(thread_id) if thread_id else None
        )
        
        context.update({
            'messages': result['messages'],
            'pagination': result['pagination']
        })
        
    elif message_type == 'channel':
        channel = data.get('channel')
        project_id = data.get('project_id')
        
        result = message_pagination_service.get_channel_messages(
            channel=channel,
            user_id=request.user.id,
            project_id=int(project_id) if project_id else None,
            cursor=cursor,
            page_size=page_size
        )
        
        context.update({
            'messages': result['messages'],
            'pagination': result['pagination']
        })
        
    elif message_type == 'thread':
        parent_message_id = data.get('parent_message_id')
        
        result = message_pagination_service.get_thread_messages(
            parent_message_id=parent_message_id,
            user_id=request.user.id,
            cursor=cursor,
            page_size=page_size
        )
        
        context.update({
            'messages': result['messages'],
            'pagination': result['pagination']
        })
        
    elif message_type == 'search':
        query = data.get('query')
        conversation_id = data.get('conversation_id')
        project_id = data.get('project_id')
        
        result = message_pagination_service.search_messages(
            query=query,
            user_id=request.user.id,
            conversation_id=conversation_id,
            project_id=int(project_id) if project_id else None,
            cursor=cursor,
            page_size=page_size
        )
        
        context.update({
            'messages': result['messages'],
            'pagination': result['pagination']
        })
    
    else:
        return HttpResponse("Invalid message type", status=400)
    
    return render(request, 'components/messages/message_list_partial.html', context)


@login_required
@require_http_methods(["GET"])
def message_count_summary(request):
    """
    HTMX endpoint for getting message count summary.
    Used for displaying unread counts and conversation stats.
    """
    conversation_id = request.GET.get('conversation_id')
    
    if conversation_id:
        # Get specific conversation stats
        conversation = get_object_or_404(Conversation, id=conversation_id)
        
        # Verify user access
        if not conversation.participants.filter(id=request.user.id).exists():
            return HttpResponse("Access denied", status=403)
        
        unread_count = conversation.get_unread_count_for_user(request.user)
        total_messages = ChatMessage.objects.filter(
            conversation=conversation,
            reply_to__isnull=True
        ).count()
        
        context = {
            'conversation': conversation,
            'unread_count': unread_count,
            'total_messages': total_messages,
            'participant_count': conversation.participants.count()
        }
        
        return render(request, 'components/messages/conversation_stats.html', context)
    
    else:
        # Get overall message stats for user
        user_conversations = request.user.conversations.filter(is_active=True)
        
        total_unread = sum(
            conv.get_unread_count_for_user(request.user) 
            for conv in user_conversations
        )
        
        total_conversations = user_conversations.count()
        
        context = {
            'total_unread': total_unread,
            'total_conversations': total_conversations,
            'active_conversations': user_conversations.filter(
                last_message_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).count()
        }
        
        return render(request, 'components/messages/overall_stats.html', context)


@login_required
@require_http_methods(["POST"])
def mark_conversation_read(request, conversation_id):
    """
    HTMX endpoint for marking all messages in a conversation as read.
    """
    conversation = get_object_or_404(Conversation, id=conversation_id)
    
    # Verify user access
    if not conversation.participants.filter(id=request.user.id).exists():
        return HttpResponse("Access denied", status=403)
    
    # Mark conversation as read
    member = conversation.members.filter(user=request.user).first()
    if member:
        member.mark_read()
    
    # Mark all unread messages as read
    unread_messages = ChatMessage.objects.filter(
        conversation=conversation
    ).exclude(
        read_by=request.user
    )
    
    for message in unread_messages:
        message.mark_read_by_user(request.user)
    
    # Invalidate cache
    cache.delete(f"message_count_{conversation_id}_main")
    
    return JsonResponse({
        'success': True,
        'unread_count': 0,
        'message': 'All messages marked as read'
    })


@login_required
@require_http_methods(["GET"])
def conversation_typing_status(request, conversation_id):
    """
    HTMX endpoint for getting typing status in a conversation.
    """
    conversation = get_object_or_404(Conversation, id=conversation_id)
    
    # Verify user access
    if not conversation.participants.filter(id=request.user.id).exists():
        return HttpResponse("Access denied", status=403)
    
    # Get typing users from cache (would be set by WebSocket consumer)
    typing_users_key = f"typing_{conversation_id}"
    typing_users = cache.get(typing_users_key, [])
    
    # Remove current user from typing list
    typing_users = [user for user in typing_users if user['id'] != request.user.id]
    
    context = {
        'typing_users': typing_users,
        'conversation_id': conversation_id
    }
    
    return render(request, 'components/messages/typing_indicator.html', context)


@login_required
@require_http_methods(["POST"])
def set_typing_status(request, conversation_id):
    """
    HTMX endpoint for setting typing status.
    """
    conversation = get_object_or_404(Conversation, id=conversation_id)
    
    # Verify user access
    if not conversation.participants.filter(id=request.user.id).exists():
        return HttpResponse("Access denied", status=403)
    
    is_typing = request.POST.get('is_typing', 'false').lower() == 'true'
    
    typing_users_key = f"typing_{conversation_id}"
    typing_users = cache.get(typing_users_key, [])
    
    user_data = {
        'id': request.user.id,
        'name': request.user.get_full_name() or request.user.username,
        'timestamp': timezone.now().isoformat()
    }
    
    if is_typing:
        # Add user to typing list
        typing_users = [user for user in typing_users if user['id'] != request.user.id]
        typing_users.append(user_data)
    else:
        # Remove user from typing list
        typing_users = [user for user in typing_users if user['id'] != request.user.id]
    
    # Cache for 10 seconds
    cache.set(typing_users_key, typing_users, 10)
    
    return JsonResponse({'success': True})


@login_required
@require_http_methods(["GET"])
def message_thread_preview(request, message_id):
    """
    HTMX endpoint for showing a preview of a message thread.
    """
    message = get_object_or_404(ChatMessage, id=message_id)
    
    # Get recent replies
    recent_replies = message.replies.select_related('user').order_by('-created_at')[:3]
    
    context = {
        'parent_message': message,
        'recent_replies': recent_replies,
        'total_replies': message.get_thread_count(),
        'user': request.user
    }
    
    return render(request, 'components/messages/thread_preview.html', context)
"""