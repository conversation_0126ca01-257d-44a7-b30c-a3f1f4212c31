{"version": "2.0.0", "tasks": [{"label": "Start Django Server", "type": "shell", "command": "python", "args": ["manage.py", "runserver", "0.0.0.0:8000"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^.*$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "^.*Performing system checks.*$", "endsPattern": "^.*Starting development server.*$"}}}, {"label": "Stop Django Server", "type": "shell", "command": "pkill", "args": ["-f", "manage.py runserver"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "<PERSON><PERSON><PERSON>", "type": "shell", "command": "python", "args": ["manage.py", "migrate"], "group": "build"}, {"label": "Django Make Migrations", "type": "shell", "command": "python", "args": ["manage.py", "makemigrations"], "group": "build"}, {"label": "Django Collect Static", "type": "shell", "command": "python", "args": ["manage.py", "collectstatic", "--noinput"], "group": "build"}]}