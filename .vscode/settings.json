{"dev.containers.defaultExtensions": ["ms-vscode-remote.remote-containers"], "dev.containers.forwardPortsToHost": true, "dev.containers.installDockerInWSL": false, "remote.WSL.defaultDistribution": "Ubuntu-24.04", "terminal.integrated.defaultProfile.windows": "Ubuntu-24.04 (WSL)", "terminal.integrated.profiles.windows": {"Ubuntu-24.04 (WSL)": {"path": "C:\\Windows\\system32\\wsl.exe", "args": ["-d", "Ubuntu-24.04"]}}, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true}