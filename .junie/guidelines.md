# CLEAR Project Development Guidelines

This document provides essential information for developers working on the CLEAR (Comprehensive Location-based Engineering and Analysis Resource) project, a Django-based utility infrastructure management platform with HTMX integration.

## Project Vision

CLEAR isn't just a name. It's a revolutionary approach to utility coordination:

**Connect**  
Bringing every stakeholder into a seamless collaborative environment. No more information silos. No more communication gaps.

**Locate**  
Transforming invisible infrastructure into vivid, actionable data. See what others can't see. Prevent what others can't prevent.

**Eliminate**  
Conflicts vanish before they exist. Predictive technology identifies and resolves issues at the earliest possible stage.

**Accelerate**  
Projects move at unprecedented speed when coordination barriers disappear. What once took weeks now takes moments.

**Resolve**  
Complex utility challenges become simple, structured workflows. The most difficult aspect of infrastructure development, made intuitive.

## Architecture Overview

CLEAR follows a modern, server-side architecture built on Django with HTMX:

```
┌─────────────────────────────────────────────────────────────────┐
│                    Frontend (Django Templates + HTMX)            │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Bootstrap 5 │  │ HTMX Dynamic │  │ Server-Rendered        │ │
│  │ Components  │  │ Interactions │  │ Templates              │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└─────────────────────────┬───────────────────────────────────────┘
                          │ HTTP/WebSocket
┌─────────────────────────┴───────────────────────────────────────┐
│                     Django Application Layer                     │
│  ┌──────────────┐  ┌──────────────┐  ┌────────────────────┐   │
│  │ Views &      │  │ Django Auth  │  │ Django ORM        │   │
│  │ URL Routing  │  │ + Sessions   │  │ + PostGIS         │   │
│  └──────────────┘  └──────────────┘  └────────────────────┘   │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────┴───────────────────────────────────────┐
│                    Data Layer (PostgreSQL)                       │
│  ┌──────────────┐  ┌──────────────┐  ┌────────────────────┐   │
│  │ PostGIS      │  │ Django       │  │ Redis Cache       │   │
│  │ Extension    │  │ Migrations   │  │ + Celery          │   │
│  └──────────────┘  └──────────────┘  └────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### Key Architectural Decisions

1. **Server-Side First**: HTML IS the state - server holds all state, HTMX provides dynamic updates
2. **Single Source of Truth**: Eliminate client-server state synchronization complexity
3. **PostGIS Powered**: Advanced spatial operations at the database level
4. **Security Hardened**: Django's built-in security with custom extensions
5. **Developer Experience**: Hot reload, comprehensive admin interface, and Django debugging tools

## Build/Configuration Instructions

### Environment Setup

1. **Python Requirements**:
   - Python 3.12+ is required
   - Install dependencies using one of the following methods:
     ```bash
     # For minimal deployment testing
     pip install -r requirements.txt
     
     # For full development environment
     pip install -e .
     ```

2. **Database Configuration**:
   - The project uses PostgreSQL with PostGIS for geospatial capabilities
   - Configure database connection in environment variables or use the defaults:
     ```
     DB_NAME=clear_htmx
     DB_USER=postgres
     DB_PASSWORD=postgres
     DB_HOST=localhost
     DB_PORT=5432
     ```
   - PostgreSQL with PostGIS is required for all spatial operations
   - **PostgreSQL/PostGIS Setup:**
     ```bash
     # Install PostgreSQL and PostGIS (Ubuntu/Debian)
     sudo apt update
     sudo apt install postgresql postgresql-contrib postgis

     # Start PostgreSQL service
     sudo systemctl start postgresql

     # Create database and enable PostGIS
     sudo -u postgres createdb clear_htmx
     sudo -u postgres psql -d clear_htmx -c "CREATE EXTENSION postgis;"

     # Create database user (optional)
     sudo -u postgres psql -c "CREATE USER myuser WITH PASSWORD 'mypassword';"
     sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE clear_htmx TO myuser;"
     ```

3. **Environment Variables**:
   - Key environment variables:
     - `SECRET_KEY`: Django secret key
     - `DEBUG`: Set to 'True' for development
     - `ALLOWED_HOSTS`: Comma-separated list of allowed hosts
     - `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`: Database connection parameters
     - `REDIS_URL`: Redis connection string (for caching and Channels)
     - `GDAL_LIBRARY_PATH`: Path to GDAL library (for GIS functionality)
     - `GEOS_LIBRARY_PATH`: Path to GEOS library (for GIS functionality)

4. **Running Migrations**:
   ```bash
   python manage.py migrate
   ```

5. **Collecting Static Files**:
   ```bash
   python manage.py collectstatic --noinput
   ```

6. **Development Server**:
   ```bash
   python manage.py runserver
   ```

7. **Docker Deployment**:
   - The project includes a Dockerfile for containerized deployment
   - Build and run the Docker container:
     ```bash
     docker build -t clear-htmx .
     docker run -p 8000:8000 -e PORT=8000 clear-htmx
     ```

8. **Local PostgreSQL/PostGIS Setup**:
   - Install PostgreSQL and PostGIS extension
   - Create database and enable PostGIS:
     ```bash
     sudo -u postgres createdb clear_htmx
     sudo -u postgres psql -d clear_htmx -c "CREATE EXTENSION postgis;"
     ```
   - Configure connection in your .env file or environment variables

## Testing Information

### Running Tests

1. **Running All Tests**:
   ```bash
   python manage.py test
   ```

2. **Running Specific Tests**:
   ```bash
   # Run tests in a specific app
   python manage.py test CLEAR
   
   # Run tests in a specific module
   python manage.py test CLEAR.tests.test_models
   
   # Run a specific test class
   python manage.py test CLEAR.tests.test_models.UserModelTest
   
   # Run a specific test method
   python manage.py test CLEAR.tests.test_models.UserModelTest.test_create_user
   ```

3. **Test Coverage**:
   ```bash
   # Install coverage
   pip install coverage
   
   # Run tests with coverage
   coverage run --source='.' manage.py test
   
   # Generate coverage report
   coverage report
   
   # Generate HTML coverage report
   coverage html
   ```

### Writing Tests

1. **Test Structure**:
   - Tests are located in the `CLEAR/tests/` directory
   - Follow Django's testing conventions using `TestCase`
   - Organize tests by functionality (models, views, API)

2. **Example Test**:
   ```python
   from django.test import TestCase

   class ExampleTest(TestCase):
       """Example test case to demonstrate testing in the CLEAR project."""
       
       def test_example(self):
           """A simple test that will always pass."""
           self.assertEqual(1 + 1, 2)
           
       def test_string_methods(self):
           """Test some string methods."""
           test_string = "clear"
           self.assertEqual(test_string.upper(), "CLEAR")
           self.assertTrue(test_string.islower())
           self.assertEqual(len(test_string), 5)
   ```

3. **Test Database**:
   - Django automatically creates a test database for running tests
   - The test database is destroyed after tests complete
   - For PostGIS tests, ensure your development environment has PostGIS installed

4. **Mocking External Services**:
   - Use Django's `unittest.mock` for mocking external services
   - For API tests, consider using `requests_mock` or similar libraries

## Additional Development Information

### Project Structure

- **CLEAR/**: Main Django application
  - **tests/**: Test files
  - **models/**: Database models (when models.py exceeds 500-800 lines, split into modules)
  - **views/**: View functions (when views.py exceeds 500-800 lines, split into modules)
  - **models.py**: Database models (for smaller applications)
  - **views.py**: View functions (for smaller applications)
  - **api_views.py**: API endpoints
  - **admin.py**: Admin interface configuration

- **clear_htmx/**: Project settings and configuration
  - **settings.py**: Django settings
  - **urls.py**: URL routing
  - **wsgi.py** and **asgi.py**: WSGI/ASGI application entry points

- **templates/**: HTML templates
- **static/**: Static files (CSS, JS, images)

### Key Features

1. **GIS Capabilities**:
   - The project uses Django's GeoDjango with PostGIS
   - Ensure GDAL and GEOS libraries are properly installed
   - Common spatial operations include:
     ```python
     # Distance calculation
     from django.contrib.gis.db.models.functions import Distance
     from django.contrib.gis.geos import Point

     # Find utilities within 500m of a point
     point = Point(x, y, srid=4326)  # WGS84 coordinates
     nearby = UtilityLineData.objects.annotate(
         distance=Distance('geometry_2d', point)
     ).filter(distance__lte=500)

     # Spatial intersections for conflict detection
     from django.contrib.gis.db.models import Collect, Extent, Union, MakeValid

     # Find intersecting utilities
     conflicts = UtilityLineData.objects.filter(
         geometry_2d__intersects=utility_line.geometry_2d
     ).exclude(id=utility_line.id)
     ```

2. **HTMX Integration**:
   - The project uses django-htmx for interactive UI
   - HTMX middleware is configured in settings.py

3. **Caching and Background Tasks**:
   - Redis is used for caching and as a Celery broker
   - Celery is configured for background tasks

4. **WebSockets**:
   - Django Channels is used for WebSocket support
   - Channel layers are configured to use Redis

### Debugging

1. **Django Debug Toolbar**:
   - Available in development mode
   - Provides insights into queries, templates, and more

2. **Logging**:
   - Logs are stored in the `logs/` directory
   - Configure log levels in settings.py

### Deployment Considerations

1. **Security Settings**:
   - Production deployments have enhanced security settings
   - HTTPS is enforced in production
   - CSP headers are configured for security

2. **Static Files**:
   - WhiteNoise is used for serving static files
   - Run `collectstatic` before deployment

3. **Database Migrations**:
   - Always run migrations during deployment
   - Consider using a migration script for complex deployments

4. **Environment Variables**:
   - Use environment variables for sensitive configuration
   - Store sensitive variables in a secure .env file that isn't committed to version control

   5. **Code Organization Best Practices**:
   - **Split Models**: When models.py exceeds 500-800 lines, split into a dedicated models/ directory with related models grouped by functionality
   - **Split Views**: When views.py exceeds 500-800 lines, split into a dedicated views/ directory with views organized by feature area
   - **Clear Imports**: Maintain proper imports and namespace management when splitting files
   - **Documentation**: Add docstrings to explain the purpose of each module after splitting
   - **Model Manager Extraction**: Move complex querysets to custom model managers
   - **Service Layer**: Extract complex business logic into dedicated service modules

   6. **Local Deployment Strategies**:
   - **Development Server**: `python manage.py runserver` for local development
   - **Gunicorn**: For more robust local testing
     ```bash
     pip install gunicorn
     gunicorn clear_htmx.wsgi:application --bind 0.0.0.0:8000
     ```
   - **Docker Compose**: Local containerized deployment
     ```bash
     docker-compose up -d
     ```
   - **Nginx + Gunicorn**: Production-like local setup
     ```bash
     # Sample Nginx configuration
     server {
         listen 80;
         server_name localhost;

         location /static/ {
             alias /path/to/static/;
         }

         location / {
             proxy_pass http://127.0.0.1:8000;
             proxy_set_header Host $host;
             proxy_set_header X-Real-IP $remote_addr;
         }
     }
     ```

     ## HTMX Implementation Patterns

     The CLEAR platform uses HTMX for dynamic UI updates without complex JavaScript. Here are the standard patterns to follow:

     ### 1. Basic HTMX Request

     ```html
     <!-- Button that loads content into a target div -->
     <button hx-get="/api/projects/" 
          hx-target="#projects-container"
          hx-swap="innerHTML"
          class="btn btn-primary">
      Load Projects
     </button>

     <div id="projects-container">
      <!-- Content will be replaced here -->
     </div>
     ```

     ### 2. Form Submission

     ```html
     <!-- Form that submits via HTMX and updates a list -->
     <form hx-post="/api/projects/create/" 
        hx-target="#project-list" 
        hx-swap="beforeend"
        hx-on::after-request="this.reset()">
      {% csrf_token %}
      {{ form.as_p }}
      <button type="submit" class="btn btn-primary">Create Project</button>
     </form>
     ```

     ### 3. Auto-loading Content

     ```html
     <!-- Div that loads content on page load -->
     <div id="activity-feed"
       hx-get="/api/activity/" 
       hx-trigger="load"
       hx-swap="innerHTML">
      <!-- Loading placeholder -->
      <p>Loading activity feed...</p>
     </div>
     ```

     ### 4. Polling for Updates

     ```html
     <!-- Div that refreshes every 30 seconds -->
     <div id="notifications"
       hx-get="/api/notifications/" 
       hx-trigger="load, every 30s"
       hx-swap="innerHTML">
     </div>
     ```

     ### 5. Click-to-Edit Pattern

     ```html
     <!-- Display with edit button that loads a form -->
     <div id="project-details">
      <h2>{{ project.name }}</h2>
      <p>{{ project.description }}</p>

      <button hx-get="/projects/{{ project.id }}/edit/"
              hx-target="#project-details"
              hx-swap="outerHTML"
              class="btn btn-sm btn-secondary">
          Edit
      </button>
     </div>
     ```

     ### 6. Confirmation Pattern

     ```html
     <!-- Delete button with confirmation -->
     <button hx-get="/projects/{{ project.id }}/confirm-delete/"
          hx-target="#confirm-modal"
          hx-swap="innerHTML"
          class="btn btn-danger">
      Delete
     </button>

     <div id="confirm-modal">
      <!-- Confirmation modal will be loaded here -->
     </div>
     ```

     ### 7. Loading Indicators

     ```html
     <!-- Button with loading indicator -->
     <button hx-get="/api/heavy-operation/"
          hx-target="#result-container"
          hx-indicator="#spinner"
          class="btn btn-primary">
      Perform Operation
     </button>

     <div id="spinner" class="htmx-indicator">
      <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
      </div>
     </div>
     ```

     ### 8. Partial Template Structure

     Partial templates should be organized in a `partials/` directory within their respective feature areas and should only contain the specific HTML fragment needed, not a complete page.

   6. **Performance Optimization**:
   - **Database Optimization**:
     - Use `select_related()` and `prefetch_related()` to minimize queries
     - Add appropriate indexes for frequently queried fields
     - Use PostgreSQL-specific indexes for special query patterns

   - **Spatial Query Optimization**:
     - Add spatial indexes to geometry fields
     - Use ST_Simplify for complex geometries when appropriate
     - Cache expensive spatial computations

   - **Template Optimization**:
     - Use Django's template fragment caching
     - Minimize template complexity and nesting

   - **HTMX Optimization**:
     - Use `hx-target` with specific IDs to minimize DOM updates
     - Add appropriate `hx-trigger` delays for non-critical updates
     - Consider `hx-boost` for navigational links instead of full HTMX