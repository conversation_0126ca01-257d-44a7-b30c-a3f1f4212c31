#\!/usr/bin/env python3
"""Check for syntax errors in specific files."""

import ast

def check_file_syntax(filepath):
    try:
        with open(filepath, 'r') as f:
            lines = f.readlines()
        
        # Try parsing progressively to find exact line
        for i in range(len(lines)):
            try:
                code = ''.join(lines[:i+1])
                ast.parse(code)
            except SyntaxError as e:
                print(f"Syntax error at line {i+1} in {filepath}")
                print(f"Error: {e}")
                print(f"\nLines {max(0, i-3)} to {i+3}:")
                for j in range(max(0, i-3), min(i+4, len(lines))):
                    marker = " >>> " if j == i else "     "
                    print(f"{marker}{j+1}: {lines[j].rstrip()}")
                return False
        
        print(f"✅ {filepath} syntax OK")
        return True
        
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return False

if __name__ == "__main__":
    files_to_check = [
        'CLEAR/views/htmx_views.py',
        'CLEAR/services/analytics.py', 
        'CLEAR/views/project_views.py'
    ]
    
    for filepath in files_to_check:
        check_file_syntax(filepath)
EOF < /dev/null
