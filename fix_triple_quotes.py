#!/usr/bin/env python3
"""
Fix extra triple quotes in view files
"""

import os
import re
import glob

def fix_extra_triple_quotes(content):
    """Fix cases where there are extra triple quotes after docstrings"""
    # Pattern to find docstring followed by extra triple quotes
    pattern = r'("""[^"]*?""")(\s*""")'
    return re.sub(pattern, r'\1', content, flags=re.DOTALL)

def fix_file(filepath):
    """Fix extra triple quotes in a file"""
    print(f"Checking {filepath}...")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fix
        content = fix_extra_triple_quotes(content)
        
        # Only write if changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✓ Fixed {filepath}")
            return True
        else:
            print(f"  - No changes needed for {filepath}")
            return False
            
    except Exception as e:
        print(f"  ✗ Error fixing {filepath}: {e}")
        return False

def main():
    """Main function to fix all view files"""
    view_files = glob.glob('CLEAR/views/*.py')
    service_files = glob.glob('CLEAR/services/*.py')
    
    all_files = view_files + service_files
    
    fixed_count = 0
    for filepath in all_files:
        if fix_file(filepath):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files out of {len(all_files)} total files")

if __name__ == "__main__":
    main()