{"version": "2.0.0", "tasks": [{"label": "dev: Start Next.js Development Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/utility-sync-t3"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${workspaceFolder}/utility-sync-t3"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": ".*Local:.*", "endsPattern": ".*ready.*|.*Local:.*"}}}, {"label": "build: Build Next.js Application", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/utility-sync-t3"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "test: Run All Tests", "type": "shell", "command": "npm", "args": ["run", "test"], "options": {"cwd": "${workspaceFolder}/utility-sync-t3"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "test: Run E2E Tests", "type": "shell", "command": "npm", "args": ["run", "test:e2e"], "options": {"cwd": "${workspaceFolder}/utility-sync-t3"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "database: Start PostgreSQL", "type": "shell", "command": "docker-compose", "args": ["up", "-d", "postgres"], "options": {"cwd": "${workspaceFolder}/utility-sync-t3"}, "group": "build"}, {"label": "database: Setup Database", "type": "shell", "command": "npm", "args": ["run", "db:push"], "options": {"cwd": "${workspaceFolder}/utility-sync-t3"}, "group": "build", "dependsOn": "database: Start PostgreSQL"}]}