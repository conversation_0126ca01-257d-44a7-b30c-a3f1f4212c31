{
  "recommendations": [
    // Core development extensions
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "prisma.prisma",
    "bradlc.vscode-tailwindcss",
    
    // Git & GitHub
    "github.vscode-pull-request-github",
    "eamodio.gitlens",
    "github.copilot",
    "github.copilot-chat",
    
    // Database
    "mtxr.sqltools",
    "mtxr.sqltools-driver-pg",
    
    // Testing
    "ms-playwright.playwright",
    "vitest.explorer",
    
    // TypeScript & JavaScript
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json",
    
    // Utilities
    "streetsidesoftware.code-spell-checker",
    "wayou.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    "yzhang.markdown-all-in-one",
    
    // Remote development
    "ms-vscode-remote.remote-containers",
    "ms-vscode-remote.remote-ssh",
    "github.codespaces",
    
    // Additional helpful extensions
    "ms-vscode.vscode-settings-sync",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "visualstudioexptteam.vscodeintellicode",
    "ms-vscode.vscode-folder-source-actions"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify"
  ]
}
