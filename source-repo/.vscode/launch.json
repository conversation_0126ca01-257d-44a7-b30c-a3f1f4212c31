{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "attach", "port": 9229, "restart": true, "cwd": "${workspaceFolder}/utility-sync-t3", "skipFiles": ["<node_internals>/**"]}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/utility-sync-t3", "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/utility-sync-t3/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}/utility-sync-t3", "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect"}}]}