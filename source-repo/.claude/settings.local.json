{"permissions": {"allow": ["Bash(export PATH=\"$HOME/.deno/bin:$PATH\")", "Bash(deno:*)", "Bash(npm install)", "Bash(npm run typecheck:*)", "Bash(node:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npx:*)", "Bash(ls:*)", "mcp__ide__getDiagnostics", "mcp__sparc2__analyze_code", "mcp__sparc2__modify_code", "<PERSON><PERSON>(env)", "WebFetch(domain:github.com)", "Bash(npm install:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(npm run build:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "mcp__memory__create_entities", "mcp__memory__create_relations", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(killall:*)", "Bash(grep:*)", "WebFetch(domain:supabase.com)", "Bash(./quick-start.sh:*)", "Bash(bash:*)", "Bash(kill:*)", "Bash(sudo systemctl start:*)", "<PERSON><PERSON>(curl:*)", "Bash(PORT=8024 timeout 5 deno run --allow-net --allow-read --allow-write --allow-env agent.ts)", "Bash(PORT=8025 timeout 5 deno run --allow-net --allow-read --allow-write --allow-env agent.ts)", "mcp__memory__read_graph", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(systemctl status:*)", "<PERSON><PERSON>(service docker:*)", "Bash(sudo service:*)", "Bash(npm run lint)", "Bash(npm run dev:*)", "Bash(supabase db remote:*)", "Bash(tsc --noEmit --incremental)", "<PERSON><PERSON>(sed:*)", "Bash(npm run:*)", "<PERSON><PERSON>(mv:*)", "Bash(git add:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(gh repo view:*)", "Bash(gh api:*)", "Bash(npm audit:*)", "Bash(npm view:*)", "Bash(npm update:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(git commit:*)", "mcp__playwright__playwright_navigate", "<PERSON><PERSON>(sudo npx playwright:*)", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_click", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_close", "mcp__memory__add_observations", "mcp__playwright__playwright_get_visible_html", "Bash(rg:*)", "mcp__playwright__start_codegen_session", "mcp__playwright__end_codegen_session", "<PERSON><PERSON>(printenv)", "<PERSON><PERSON>(cat:*)", "mcp__memory__search_nodes", "Bash(fuser:*)", "Bash(PORT=3001 npm run dev)", "Bash(vercel login:*)", "<PERSON><PERSON>(vercel:*)", "mcp__playwright__playwright_press_key", "<PERSON><PERSON>(jq:*)", "Bash(npm search @supabase/mcp)", "Bash(npm search:*)", "Bash(SUPABASE_URL=\"https://xeaimnfnaiuuaazovoxx.supabase.co\" SUPABASE_ANON_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhlYWltbmZuYWl1dWFhem92b3h4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyNTg4OTgsImV4cCI6MjA2NDgzNDg5OH0._imMhkI7hPc4CAldYazYvfpkgauR6E5xrzkbTMPW3Do\" timeout 10 npx -y @supabase/mcp-server-supabase)", "Bash(SUPABASE_URL=\"https://xeaimnfnaiuuaazovoxx.supabase.co\" SUPABASE_ACCESS_TOKEN=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhlYWltbmZuYWl1dWFhem92b3h4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTI1ODg5OCwiZXhwIjoyMDY0ODM0ODk4fQ.BUkvrd5mMLOxUyl1ukTPnx8aC4G9-LJWHYnxUS_5uvk\" timeout 5 npx -y @supabase/mcp-server-supabase)", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "mcp__supabase__list_projects", "Bash(supabase status:*)", "Bash(cp:*)", "Bash(git pull:*)", "<PERSON><PERSON>(python3:*)", "Bash(awk:*)", "mcp__supabase__apply_migration", "mcp__supabase__search_docs", "<PERSON><PERSON>(tail:*)", "Bash(psql:*)", "Bash(rm:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["sequential-thinking", "sparc2", "memory", "prisma-mcp-server", "playwright", "context7", "supabase"]}