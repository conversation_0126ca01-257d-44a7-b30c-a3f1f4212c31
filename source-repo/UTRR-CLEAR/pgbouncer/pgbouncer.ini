[databases]
# Database configuration for CLEAR application
# Production database connection through PostgreSQL container
clear = host=postgres port=5432 dbname=clear user=postgres
clear_dev = host=postgres port=5432 dbname=clear_dev user=postgres

# Read-only replica (if available)
clear_readonly = host=postgres port=5432 dbname=clear user=postgres_readonly

[pgbouncer]
# Connection pool settings optimized for CLEAR workload
listen_addr = 0.0.0.0
listen_port = 6432
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt

# Pool configuration optimized for Next.js/tRPC usage
pool_mode = transaction
max_client_conn = 2000
default_pool_size = 20
min_pool_size = 5
reserve_pool_size = 10
reserve_pool_timeout = 3
max_db_connections = 200
max_user_connections = 150

# Timeouts optimized for web application
server_lifetime = 7200      # 2 hours
server_idle_timeout = 300   # 5 minutes
server_connect_timeout = 10
server_login_retry = 15
query_timeout = 120         # 2 minutes for complex spatial queries
query_wait_timeout = 30     # 30 seconds queue wait
client_idle_timeout = 600   # 10 minutes
client_login_timeout = 30

# Low-level network settings
pkt_buf = 8192
tcp_keepalive = 1
tcp_keepcnt = 3
tcp_keepidle = 300
tcp_keepintvl = 30
tcp_user_timeout = 30000

# Logging and monitoring
log_connections = 1
log_disconnections = 1
log_pooler_errors = 1
log_stats = 1
stats_period = 60
verbose = 1

# Admin and monitoring access
admin_users = pgbouncer
stats_users = pgbouncer, monitor, prometheus

# Security
server_tls_sslmode = prefer
client_tls_sslmode = prefer

# Performance optimizations
server_reset_query = DISCARD ALL
server_reset_query_always = 0
ignore_startup_parameters = extra_float_digits,search_path
server_check_delay = 30

# Application identification
application_name_add_host = 1

# Query routing for read/write split (future enhancement)
# server_round_robin = 1