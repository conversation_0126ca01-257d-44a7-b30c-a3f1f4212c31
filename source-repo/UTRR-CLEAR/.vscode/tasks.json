{"version": "2.0.0", "tasks": [{"label": "Type Check", "type": "npm", "script": "typecheck", "group": {"kind": "test", "isDefault": false}, "problemMatcher": "$tsc", "presentation": {"reveal": "always", "panel": "shared"}}, {"label": "<PERSON><PERSON>", "type": "npm", "script": "lint", "group": "test", "problemMatcher": "$eslint-stylish", "presentation": {"reveal": "always", "panel": "shared"}}, {"label": "<PERSON><PERSON>", "type": "npm", "script": "scan:mock-data", "group": "test", "presentation": {"reveal": "always", "panel": "shared"}}, {"label": "Production Check (All)", "type": "npm", "script": "check:production", "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "shared"}}]}