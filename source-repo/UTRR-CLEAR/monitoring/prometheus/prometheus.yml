# Prometheus configuration for CLEAR application monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'clear-production'
    region: 'us-east-1'

# Rule files for alerting
rule_files:
  - "alerts/*.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 5s

  # CLEAR application metrics
  - job_name: 'clear-app'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 10s
    scrape_timeout: 8s

  # Spatial processor metrics
  - job_name: 'spatial-processor'
    static_configs:
      - targets: ['spatial-processor:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # WebSocket server metrics  
  - job_name: 'websocket-server'
    static_configs:
      - targets: ['websocket-server:8002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 10s

  # PgBouncer metrics
  - job_name: 'pgbouncer'
    static_configs:
      - targets: ['pgbouncer-exporter:9127']
    scrape_interval: 10s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 10s

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 10s

  # Application health checks
  - job_name: 'health-checks'
    static_configs:
      - targets: 
        - 'app:3000'
        - 'spatial-processor:8001'
        - 'websocket-server:8002'
    metrics_path: '/health'
    scrape_interval: 30s
    scrape_timeout: 5s

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     queue_config:
#       max_shards: 200
#       max_samples_per_send: 1000
#       batch_send_deadline: 5s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Storage configuration
storage:
  tsdb:
    retention.time: 90d
    retention.size: 50GB
    wal-compression: true