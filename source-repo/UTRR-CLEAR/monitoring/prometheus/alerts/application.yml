# Application-specific alerting rules for CLEAR
groups:
  - name: clear-application
    rules:
      # High error rate in API endpoints
      - alert: HighAPIErrorRate
        expr: rate(http_requests_total{job="clear-app",status=~"5.."}[5m]) / rate(http_requests_total{job="clear-app"}[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
          service: clear-app
        annotations:
          summary: "High API error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # High response time
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="clear-app"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: clear-app
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is {{ $value }}s"

      # Spatial processor performance
      - alert: SlowSpatialProcessing
        expr: histogram_quantile(0.95, rate(spatial_operation_duration_seconds_bucket{job="spatial-processor"}[5m])) > 30
        for: 3m
        labels:
          severity: warning
          service: spatial-processor
        annotations:
          summary: "Slow spatial processing detected"
          description: "95th percentile spatial operation time is {{ $value }}s"

      # WebSocket connection issues
      - alert: HighWebSocketConnectionDrops
        expr: rate(websocket_connections_dropped_total{job="websocket-server"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: websocket-server
        annotations:
          summary: "High WebSocket connection drop rate"
          description: "WebSocket connections are dropping at {{ $value }} per second"

      # Memory usage
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / process_virtual_memory_max_bytes) > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }}"

      # File upload failures
      - alert: HighFileUploadFailures
        expr: rate(file_upload_failures_total{job="clear-app"}[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          service: clear-app
        annotations:
          summary: "High file upload failure rate"
          description: "File uploads are failing at {{ $value }} per second"

      # Cache hit rate
      - alert: LowCacheHitRate
        expr: redis_cache_hit_rate{job="redis"} < 0.8
        for: 10m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Low cache hit rate"
          description: "Redis cache hit rate is {{ $value | humanizePercentage }}"

      # Conflict detection errors
      - alert: ConflictDetectionErrors
        expr: rate(conflict_detection_errors_total{job="spatial-processor"}[5m]) > 0.01
        for: 2m
        labels:
          severity: warning
          service: spatial-processor
        annotations:
          summary: "Conflict detection errors detected"
          description: "Conflict detection is failing at {{ $value }} per second"

  - name: clear-business-metrics
    rules:
      # Low user activity
      - alert: LowUserActivity
        expr: rate(user_sessions_total{job="clear-app"}[1h]) < 0.1
        for: 30m
        labels:
          severity: info
          service: clear-app
        annotations:
          summary: "Low user activity detected"
          description: "User session rate is {{ $value }} per hour"

      # High project creation rate (potential spam)
      - alert: HighProjectCreationRate
        expr: rate(projects_created_total{job="clear-app"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: clear-app
        annotations:
          summary: "Unusually high project creation rate"
          description: "Projects are being created at {{ $value }} per second"

      # Authentication failures
      - alert: HighAuthenticationFailures
        expr: rate(auth_failures_total{job="clear-app"}[5m]) > 1
        for: 5m
        labels:
          severity: warning
          service: clear-app
        annotations:
          summary: "High authentication failure rate"
          description: "Authentication failures at {{ $value }} per second - possible brute force attack"