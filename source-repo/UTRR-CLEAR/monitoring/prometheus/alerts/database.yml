groups:
  - name: database_alerts
    interval: 30s
    rules:
      # Too many connections
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends{datname="utility_sync"} > 80
        for: 5m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "High number of database connections"
          description: "Database has {{ $value }} active connections"
          
      # Database down
      - alert: DatabaseDown
        expr: up{job="postgresql"} == 0
        for: 1m
        labels:
          severity: critical
          team: oncall
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL has been down for more than 1 minute"
          
      # Low cache hit ratio
      - alert: DatabaseCacheHitRatioLow
        expr: |
          pg_stat_database_blks_hit{datname="utility_sync"} 
          / 
          (pg_stat_database_blks_hit{datname="utility_sync"} + pg_stat_database_blks_read{datname="utility_sync"})
          < 0.9
        for: 10m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "Low database cache hit ratio"
          description: "Cache hit ratio is {{ $value | humanizePercentage }}"
          
      # High rollback rate
      - alert: DatabaseHighRollbackRate
        expr: |
          rate(pg_stat_database_xact_rollback{datname="utility_sync"}[5m])
          /
          rate(pg_stat_database_xact_commit{datname="utility_sync"}[5m])
          > 0.1
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High database rollback rate"
          description: "Rollback rate is {{ $value | humanizePercentage }} of commits"
          
      # Replication lag (if using replication)
      - alert: DatabaseReplicationLag
        expr: pg_replication_lag > 10
        for: 5m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "Database replication lag detected"
          description: "Replication lag is {{ $value }} seconds"