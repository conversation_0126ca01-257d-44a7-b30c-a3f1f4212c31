{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "pg_stat_database_numbackends{datname=\"utility_sync\"}", "refId": "A"}], "title": "Active Connections", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 0}, "id": 2, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(pg_stat_database_xact_commit{datname=\"utility_sync\"}[5m])", "refId": "A", "legendFormat": "Commits"}, {"expr": "rate(pg_stat_database_xact_rollback{datname=\"utility_sync\"}[5m])", "refId": "B", "legendFormat": "Rollbacks"}], "title": "Transaction Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.95}, {"color": "red", "value": 0.99}]}, "unit": "percentunit"}}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 0}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "pg_stat_database_blks_hit{datname=\"utility_sync\"} / (pg_stat_database_blks_hit{datname=\"utility_sync\"} + pg_stat_database_blks_read{datname=\"utility_sync\"})", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 4, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, pg_query_duration_ms_bucket)", "refId": "A", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, pg_query_duration_ms_bucket)", "refId": "B", "legendFormat": "99th percentile"}], "title": "Query Latency", "type": "timeseries"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 5, "options": {"showHeader": true}, "pluginVersion": "8.0.0", "targets": [{"format": "table", "rawSql": "SELECT query, calls, total_time, mean_time, stddev_time \nFROM pg_stat_statements \nWHERE query NOT LIKE '%pg_stat_statements%' \nORDER BY mean_time DESC \nLIMIT 10", "refId": "A"}], "title": "Slowest Queries", "type": "table"}], "refresh": "10s", "schemaVersion": 30, "style": "dark", "tags": ["database", "postgresql"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Database Performance", "uid": "db-performance", "version": 0}