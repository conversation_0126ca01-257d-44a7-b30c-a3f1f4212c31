{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "Jaeger UI", "tooltip": "Open Jaeger UI", "type": "link", "url": "http://localhost:16686"}], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(traces_span_metrics_calls_total[5m])", "refId": "A", "legendFormat": "{{service_name}} - {{span_name}}"}], "title": "Span Rate by Service", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(traces_span_metrics_duration_milliseconds_bucket[5m])) by (service_name, span_name, le))", "refId": "A", "legendFormat": "{{service_name}} - {{span_name}}"}], "title": "95th Percentile Span Duration", "type": "timeseries"}, {"datasource": "<PERSON><PERSON><PERSON>", "fieldConfig": {"defaults": {"custom": {}}}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"query": {"service": "utilitysync-app", "operation": "", "tags": "", "limit": 20, "maxDuration": "", "minDuration": ""}}, "pluginVersion": "8.0.0", "targets": [{"queryType": "search", "refId": "A", "service": "utilitysync-app", "limit": 20}], "title": "Recent Traces", "type": "jaeger"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 18}, "id": 4, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(traces_span_metrics_calls_total{status_code=\"STATUS_CODE_ERROR\"}[5m])) / sum(rate(traces_span_metrics_calls_total[5m])) * 100", "refId": "A"}], "title": "Error Rate", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": []}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 18}, "id": 5, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum by (span_name) (increase(traces_span_metrics_calls_total{service_name=\"utilitysync-app\"}[1h]))", "refId": "A", "legendFormat": "{{span_name}}"}], "title": "Operations Distribution", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 18}, "id": 6, "options": {"showHeader": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "topk(10, sum by (service_name, span_name) (rate(traces_span_metrics_duration_milliseconds_sum[5m]) / rate(traces_span_metrics_duration_milliseconds_count[5m])))", "format": "table", "instant": true, "refId": "A"}], "title": "Slowest Operations", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value": "Avg Duration (ms)", "service_name": "Service", "span_name": "Operation"}}}], "type": "table"}], "refresh": "10s", "schemaVersion": 30, "style": "dark", "tags": ["tracing", "performance"], "templating": {"list": [{"current": {"selected": false, "text": "utilitysync-app", "value": "utilitysync-app"}, "datasource": "<PERSON><PERSON><PERSON>", "definition": "", "hide": 0, "includeAll": false, "label": "Service", "multi": false, "name": "service", "options": [], "query": {"query": "", "refId": "JaegerVariableQueryEditor-VariableQuery", "queryType": "services"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Distributed Tracing", "uid": "distributed-tracing", "version": 0}