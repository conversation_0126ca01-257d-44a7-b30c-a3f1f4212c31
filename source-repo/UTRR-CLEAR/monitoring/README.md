# Monitoring Setup

This directory contains the monitoring stack configuration for UtilitySync using Prometheus, Grafana, and Loki.

## Quick Start

1. Start the monitoring stack:
```bash
docker-compose -f monitoring/docker-compose.monitoring.yml up -d
```

2. Access the services:
- **<PERSON>ana**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Loki**: http://localhost:3100

## Architecture

```mermaid
graph TB
    subgraph "Application"
        APP[Next.js App]
        METRICS[/api/metrics]
    end
    
    subgraph "Exporters"
        NODE[Node Exporter]
        PG[PostgreSQL Exporter]
        REDIS[Redis Exporter]
    end
    
    subgraph "Collection"
        PROM[Prometheus]
        LOKI[Loki]
        PROMTAIL[Promtail]
    end
    
    subgraph "Visualization"
        GRAF[Grafana]
    end
    
    APP --> METRICS
    METRICS --> PROM
    NODE --> PROM
    PG --> PROM
    REDIS --> PROM
    
    APP --> PROMTAIL
    PROMTAIL --> LOKI
    
    PROM --> GRAF
    LOKI --> GRAF
```

## Components

### Prometheus
- Metrics collection and storage
- Alert rule evaluation
- Service discovery

### Grafana
- Visualization dashboards
- Alert management
- Data exploration

### Loki
- Log aggregation
- Log querying
- Integration with Grafana

### Exporters
- **Node Exporter**: System metrics (CPU, memory, disk)
- **PostgreSQL Exporter**: Database metrics
- **Redis Exporter**: Cache metrics

## Dashboards

### Application Overview
- Request rate and latency
- Error rates
- Active users
- System resources

### Database Performance
- Connection pool usage
- Query performance
- Cache hit ratios
- Slow queries

### Business Metrics
- Active projects
- User activity
- Conflict detection rates
- API usage

## Alerts

### Critical Alerts
- Application down
- Database down
- High error rate (>5%)
- Disk space critical (<10%)

### Warning Alerts
- High response time (>1s)
- High memory usage (>90%)
- High CPU usage (>85%)
- Low cache hit ratio (<90%)

## Custom Metrics

The application exposes custom metrics at `/api/metrics`:

- `utilitysync_http_requests_total`: Total HTTP requests
- `utilitysync_http_request_duration_ms`: Request duration histogram
- `utilitysync_active_users`: Number of active users
- `utilitysync_total_projects`: Total projects by status
- `utilitysync_total_conflicts`: Conflicts by severity and status

## Configuration

### Adding New Dashboards

1. Create dashboard in Grafana UI
2. Export as JSON
3. Save to `monitoring/grafana/dashboards/`
4. Restart Grafana

### Adding New Alerts

1. Add rules to `monitoring/prometheus/alerts/`
2. Reload Prometheus configuration:
```bash
curl -X POST http://localhost:9090/-/reload
```

### Retention Policies

- **Prometheus**: 15 days (configurable in prometheus.yml)
- **Loki**: 7 days (configurable in loki config)
- **Grafana**: Unlimited (database backed)

## Troubleshooting

### No metrics showing
1. Check if application is exposing metrics: `curl http://localhost:3000/api/metrics`
2. Verify Prometheus targets: http://localhost:9090/targets
3. Check Prometheus logs: `docker logs monitoring_prometheus_1`

### High memory usage
1. Reduce retention period
2. Increase scrape interval
3. Reduce cardinality of metrics

### Missing logs
1. Verify Promtail is running
2. Check log paths in promtail config
3. Ensure proper permissions on log files

## Production Considerations

1. **Security**:
   - Change default Grafana password
   - Use HTTPS for all endpoints
   - Implement authentication for Prometheus

2. **High Availability**:
   - Use Prometheus federation
   - Deploy multiple Grafana instances
   - Use external storage for Loki

3. **Scaling**:
   - Use remote storage for Prometheus
   - Implement metric aggregation
   - Use recording rules for expensive queries

4. **Backup**:
   - Regular Grafana database backups
   - Export dashboard configurations
   - Document custom queries