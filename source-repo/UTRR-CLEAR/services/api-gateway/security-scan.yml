# Security scanning configuration for CLEAR API Gateway
# This file configures automated security scans using various tools

name: Security Scan Configuration
version: "1.0"

# Static Application Security Testing (SAST)
sast:
  enabled: true
  tools:
    - name: "eslint-security"
      config:
        rules:
          - "security/detect-object-injection"
          - "security/detect-non-literal-regexp"
          - "security/detect-unsafe-regex"
          - "security/detect-buffer-noassert"
          - "security/detect-child-process"
          - "security/detect-disable-mustache-escape"
          - "security/detect-eval-with-expression"
          - "security/detect-no-csrf-before-method-override"
          - "security/detect-non-literal-fs-filename"
          - "security/detect-non-literal-require"
          - "security/detect-possible-timing-attacks"
          - "security/detect-pseudoRandomBytes"
        severity: "error"
    
    - name: "semgrep"
      config:
        rules:
          - "javascript.express.security"
          - "javascript.lang.security"
          - "javascript.node-js.security"
          - "generic.secrets"
        output_format: "json"
        severity: "WARNING"

# Dynamic Application Security Testing (DAST)
dast:
  enabled: true
  tools:
    - name: "owasp-zap"
      config:
        target_url: "http://localhost:8003"
        context_file: "zap-context.xml"
        scan_types:
          - "baseline"
          - "full"
        authentication:
          type: "api_key"
          header: "X-API-Key"
          test_key: "${TEST_API_KEY}"
        exclusions:
          - "/docs/*"
          - "/metrics"
          - "/health"
    
    - name: "nuclei"
      config:
        templates:
          - "cves"
          - "vulnerabilities"
          - "exposures"
          - "misconfiguration"
        severity: ["critical", "high", "medium"]
        rate_limit: "150"
        timeout: "5s"

# Software Composition Analysis (SCA)
sca:
  enabled: true
  tools:
    - name: "npm-audit"
      config:
        audit_level: "moderate"
        production_only: false
        exclude_dev_dependencies: false
    
    - name: "snyk"
      config:
        severity_threshold: "medium"
        fail_on: "upgradable"
        monitor: true
        include_dev_deps: true
    
    - name: "retire.js"
      config:
        severity: "medium"
        ignore_file: ".retireignore"

# Infrastructure as Code (IaC) Security
iac:
  enabled: true
  tools:
    - name: "checkov"
      config:
        framework: ["dockerfile", "kubernetes"]
        check_ids:
          - "CKV_DOCKER_2"  # Ensure HEALTHCHECK instruction
          - "CKV_DOCKER_3"  # Ensure non-root user
          - "CKV_DOCKER_9"  # Ensure apt is not used
        skip_checks: []
    
    - name: "hadolint"
      config:
        rules:
          - "DL3008"  # Pin versions in apt-get install
          - "DL3009"  # Delete apt-get cache
          - "DL3015"  # Avoid additional packages
          - "DL4006"  # Set SHELL option
        ignore: ["DL3018"]  # Allow latest tag for alpine

# Container Security
container:
  enabled: true
  tools:
    - name: "trivy"
      config:
        scan_types: ["vuln", "secret", "config"]
        severity: ["UNKNOWN", "LOW", "MEDIUM", "HIGH", "CRITICAL"]
        ignore_unfixed: false
        format: "json"
    
    - name: "grype"
      config:
        scope: "all-layers"
        output: "json"
        fail_on: "medium"

# Secret Detection
secrets:
  enabled: true
  tools:
    - name: "truffleHog"
      config:
        git_history: true
        entropy: true
        regex: true
        max_depth: 100
    
    - name: "detect-secrets"
      config:
        plugins:
          - "ArtifactoryDetector"
          - "AWSKeyDetector"
          - "AzureStorageKeyDetector"
          - "Base64HighEntropyString"
          - "BasicAuthDetector"
          - "CloudantDetector"
          - "HexHighEntropyString"
          - "IbmCloudIamDetector"
          - "IbmCosHmacDetector"
          - "JwtTokenDetector"
          - "KeywordDetector"
          - "MailchimpDetector"
          - "PrivateKeyDetector"
          - "SlackDetector"
          - "SoftlayerDetector"
          - "SquareOAuthDetector"
          - "StripeDetector"
          - "TwilioKeyDetector"

# Runtime Security
runtime:
  enabled: true
  tools:
    - name: "falco"
      config:
        rules:
          - "write_below_etc"
          - "write_below_root"
          - "read_sensitive_file_trusted_after_startup"
          - "modify_binary_dirs"
          - "spawn_shell_in_container"
          - "create_files_below_dev"
        priority: "WARNING"
    
    - name: "osquery"
      config:
        queries:
          - name: "network_connections"
            query: "SELECT * FROM process_open_sockets WHERE family = 2;"
          - name: "running_processes"
            query: "SELECT * FROM processes WHERE parent = 0;"
          - name: "file_changes"
            query: "SELECT * FROM file_events WHERE path LIKE '/app/%';"

# API Security Testing
api_security:
  enabled: true
  tools:
    - name: "apisec"
      config:
        openapi_spec: "/docs/openapi.json"
        auth_header: "X-API-Key"
        test_cases:
          - "injection"
          - "authentication"
          - "authorization"
          - "data_validation"
          - "rate_limiting"
          - "error_handling"
    
    - name: "postman-newman"
      config:
        collection: "tests/security/api-security-tests.json"
        environment: "tests/security/test-environment.json"
        reporters: ["cli", "json"]

# Compliance Checks
compliance:
  enabled: true
  standards:
    - name: "OWASP_TOP_10"
      checks:
        - "A01_Broken_Access_Control"
        - "A02_Cryptographic_Failures"
        - "A03_Injection"
        - "A04_Insecure_Design"
        - "A05_Security_Misconfiguration"
        - "A06_Vulnerable_Components"
        - "A07_Identification_Authentication_Failures"
        - "A08_Software_Data_Integrity_Failures"
        - "A09_Security_Logging_Monitoring_Failures"
        - "A10_Server_Side_Request_Forgery"
    
    - name: "CIS_DOCKER_BENCHMARK"
      checks:
        - "4.1_Image_User"
        - "4.2_Image_Sudo"
        - "4.5_Image_Updates"
        - "4.6_Image_Secrets"
        - "4.7_Image_Packages"

# Reporting Configuration
reporting:
  enabled: true
  formats: ["json", "html", "pdf", "sarif"]
  destinations:
    - type: "file"
      path: "./security-reports/"
    - type: "webhook"
      url: "${SECURITY_WEBHOOK_URL}"
      headers:
        Authorization: "Bearer ${SECURITY_WEBHOOK_TOKEN}"
    - type: "slack"
      webhook_url: "${SLACK_SECURITY_WEBHOOK}"
      channel: "#security-alerts"

# Scheduling
schedule:
  daily_scan:
    enabled: true
    time: "02:00"
    timezone: "UTC"
    scans: ["sast", "secrets", "sca"]
  
  weekly_scan:
    enabled: true
    day: "sunday"
    time: "01:00"
    timezone: "UTC"
    scans: ["dast", "container", "iac"]
  
  on_commit:
    enabled: true
    scans: ["sast", "secrets"]
  
  on_build:
    enabled: true
    scans: ["sast", "sca", "container", "secrets"]

# Thresholds and Alerts
thresholds:
  critical_vulnerabilities: 0
  high_vulnerabilities: 5
  medium_vulnerabilities: 20
  low_vulnerabilities: 50
  
  secrets_detected: 0
  license_violations: 0
  
  compliance_score_minimum: 85  # percentage

alerts:
  enabled: true
  channels:
    - type: "email"
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
      severity: ["critical", "high"]
    
    - type: "slack"
      webhook: "${SLACK_SECURITY_WEBHOOK}"
      channel: "#security-alerts"
      severity: ["critical", "high", "medium"]
    
    - type: "pagerduty"
      service_key: "${PAGERDUTY_SERVICE_KEY}"
      severity: ["critical"]

# Exclusions and Allowlists
exclusions:
  files:
    - "node_modules/**"
    - "*.test.js"
    - "*.spec.js"
    - "coverage/**"
    - ".git/**"
  
  vulnerabilities:
    # Example: Allow specific CVEs if they don't apply
    # - "CVE-2021-44906"  # minimist prototype pollution (not used in production)
  
  secrets:
    - "test_api_key_*"
    - "fake_secret_*"
    - "placeholder_*"

# Integration Configuration
integrations:
  github:
    enabled: true
    token: "${GITHUB_TOKEN}"
    repository: "${GITHUB_REPOSITORY}"
    create_issues: true
    assign_to: ["security-team"]
  
  jira:
    enabled: false
    url: "${JIRA_URL}"
    username: "${JIRA_USERNAME}"
    token: "${JIRA_TOKEN}"
    project: "SEC"
  
  sonarqube:
    enabled: false
    url: "${SONARQUBE_URL}"
    token: "${SONARQUBE_TOKEN}"
    project_key: "clear-api-gateway"

# Environment-specific Configuration
environments:
  development:
    sast: true
    secrets: true
    sca: true
    dast: false
    container: false
  
  staging:
    sast: true
    secrets: true
    sca: true
    dast: true
    container: true
  
  production:
    sast: false  # Already scanned in CI/CD
    secrets: false
    sca: false
    dast: true   # Safe DAST scans only
    container: true