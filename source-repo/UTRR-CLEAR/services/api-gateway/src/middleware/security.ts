import { FastifyRequest, FastifyReply } from 'fastify';
import Redis from 'ioredis';
import { Logger } from 'pino';
import crypto from 'crypto';

interface SecurityConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  blockDuration: number; // in seconds
  suspiciousPatterns: RegExp[];
  allowedUserAgents: RegExp[];
  blockedUserAgents: RegExp[];
  maxPayloadSize: number;
  ipWhitelist: string[];
  countryBlacklist: string[];
}

export class SecurityMiddleware {
  private redis: Redis;
  private logger: Logger;
  private config: SecurityConfig;
  
  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.config = {
      maxRequestsPerMinute: 100,
      maxRequestsPerHour: 1000,
      blockDuration: 3600, // 1 hour
      suspiciousPatterns: [
        /\b(union|select|insert|update|delete|drop|create|alter)\b/i,
        /<script[^>]*>.*?<\/script>/gi,
        /javascript:/i,
        /vbscript:/i,
        /on\w+\s*=/i,
        /\.\.\//g,
        /etc\/passwd/i,
        /\/proc\//i,
      ],
      allowedUserAgents: [
        /Mozilla\/\d+\.\d+/,
        /Chrome\/\d+\.\d+/,
        /Safari\/\d+\.\d+/,
        /Edge\/\d+\.\d+/,
        /Firefox\/\d+\.\d+/,
      ],
      blockedUserAgents: [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scraper/i,
        /curl/i,
        /wget/i,
        /python/i,
        /java/i,
        /go-http-client/i,
        /okhttp/i,
        /postman/i,
        /insomnia/i,
        /gpt/i,
        /claude/i,
        /openai/i,
        /anthropic/i,
      ],
      maxPayloadSize: 100 * 1024 * 1024, // 100MB
      ipWhitelist: [
        '127.0.0.1',
        '::1',
        '10.0.0.0/8',
        '**********/12',
        '***********/16',
      ],
      countryBlacklist: [], // Add country codes as needed
    };
  }
  
  async checkSecurity(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const clientIP = this.getClientIP(request);
    const userAgent = request.headers['user-agent'] || '';
    const requestId = request.id;
    
    try {
      // Check if IP is blocked
      if (await this.isIPBlocked(clientIP)) {
        this.logger.warn({ clientIP, requestId }, 'Blocked IP attempted access');
        reply.code(403).send({
          error: 'Forbidden',
          message: 'Your IP address has been blocked',
          requestId,
        });
        return;
      }
      
      // Check rate limits
      if (await this.checkRateLimits(clientIP)) {
        await this.blockIP(clientIP, 'Rate limit exceeded', this.config.blockDuration);
        this.logger.warn({ clientIP, requestId }, 'IP blocked due to rate limit exceeded');
        reply.code(429).send({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. IP temporarily blocked.',
          requestId,
        });
        return;
      }
      
      // Check user agent
      if (this.isSuspiciousUserAgent(userAgent)) {
        await this.recordSuspiciousActivity(clientIP, 'Suspicious user agent', userAgent);
        this.logger.warn({ clientIP, userAgent, requestId }, 'Suspicious user agent detected');
        reply.code(403).send({
          error: 'Forbidden',
          message: 'Access denied',
          requestId,
        });
        return;
      }
      
      // Check for malicious payloads
      if (await this.checkMaliciousPayload(request)) {
        await this.blockIP(clientIP, 'Malicious payload detected', this.config.blockDuration);
        this.logger.warn({ clientIP, requestId }, 'Malicious payload detected');
        reply.code(400).send({
          error: 'Bad Request',
          message: 'Malicious content detected',
          requestId,
        });
        return;
      }
      
      // Check payload size
      const contentLength = parseInt(request.headers['content-length'] || '0');
      if (contentLength > this.config.maxPayloadSize) {
        this.logger.warn({ clientIP, contentLength, requestId }, 'Payload too large');
        reply.code(413).send({
          error: 'Payload Too Large',
          message: `Request payload exceeds maximum size of ${this.config.maxPayloadSize} bytes`,
          requestId,
        });
        return;
      }
      
      // Record legitimate request
      await this.recordRequest(clientIP);
      
    } catch (error) {
      this.logger.error({ error, clientIP, requestId }, 'Security check error');
      // Don't block the request on security check errors
    }
  }
  
  private getClientIP(request: FastifyRequest): string {
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIP = request.headers['x-real-ip'] as string;
    const cfConnectingIP = request.headers['cf-connecting-ip'] as string;
    
    // Prefer CloudFlare connecting IP, then real IP, then forwarded, then socket IP
    return cfConnectingIP || realIP || (forwarded && forwarded.split(',')[0]?.trim()) || request.socket.remoteAddress || '';
  }
  
  private async isIPBlocked(ip: string): Promise<boolean> {
    const blocked = await this.redis.get(`security:blocked:${ip}`);
    return blocked !== null;
  }
  
  async blockIP(ip: string, reason: string, duration: number = this.config.blockDuration): Promise<void> {
    const key = `security:blocked:${ip}`;
    const data = {
      reason,
      blockedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + duration * 1000).toISOString(),
    };
    
    await this.redis.setex(key, duration, JSON.stringify(data));
    await this.redis.sadd('security:blocked:list', ip);
    
    this.logger.info({ ip, reason, duration }, 'IP address blocked');
  }
  
  async getBlockedIPs(): Promise<Array<{ ip: string; reason: string; blockedAt: string; expiresAt: string }>> {
    const blockedIPs = await this.redis.smembers('security:blocked:list');
    const result = [];
    
    for (const ip of blockedIPs) {
      const data = await this.redis.get(`security:blocked:${ip}`);
      if (data) {
        result.push({ ip, ...JSON.parse(data) });
      } else {
        // Clean up expired entries
        await this.redis.srem('security:blocked:list', ip);
      }
    }
    
    return result;
  }
  
  private async checkRateLimits(ip: string): Promise<boolean> {
    const now = Date.now();
    const minuteKey = `security:rate:minute:${ip}:${Math.floor(now / 60000)}`;
    const hourKey = `security:rate:hour:${ip}:${Math.floor(now / 3600000)}`;
    
    // Check minute rate limit
    const minuteCount = await this.redis.incr(minuteKey);
    if (minuteCount === 1) {
      await this.redis.expire(minuteKey, 60);
    }
    
    if (minuteCount > this.config.maxRequestsPerMinute) {
      return true;
    }
    
    // Check hour rate limit
    const hourCount = await this.redis.incr(hourKey);
    if (hourCount === 1) {
      await this.redis.expire(hourKey, 3600);
    }
    
    return hourCount > this.config.maxRequestsPerHour;
  }
  
  private isSuspiciousUserAgent(userAgent: string): boolean {
    if (!userAgent) return true;
    
    // Check against blocked patterns
    for (const pattern of this.config.blockedUserAgents) {
      if (pattern.test(userAgent)) {
        return true;
      }
    }
    
    // Check if it matches allowed patterns (for strict mode)
    // Disabled by default to allow mobile apps and other clients
    // return !this.config.allowedUserAgents.some(pattern => pattern.test(userAgent));
    
    return false;
  }
  
  private async checkMaliciousPayload(request: FastifyRequest): Promise<boolean> {
    const payload = JSON.stringify({
      url: request.url,
      query: request.query,
      body: request.body,
      headers: request.headers,
    });
    
    for (const pattern of this.config.suspiciousPatterns) {
      if (pattern.test(payload)) {
        return true;
      }
    }
    
    return false;
  }
  
  private async recordRequest(ip: string): Promise<void> {
    const key = `security:requests:${ip}`;
    await this.redis.incr(key);
    await this.redis.expire(key, 3600); // Expire after 1 hour
  }
  
  private async recordSuspiciousActivity(ip: string, type: string, details: string): Promise<void> {
    const key = `security:suspicious:${ip}`;
    const activity = {
      type,
      details,
      timestamp: new Date().toISOString(),
    };
    
    await this.redis.lpush(key, JSON.stringify(activity));
    await this.redis.ltrim(key, 0, 99); // Keep last 100 activities
    await this.redis.expire(key, 86400); // Expire after 24 hours
  }
  
  // Geographic blocking (requires MaxMind GeoLite2 database)
  private async checkGeographicRestrictions(ip: string): Promise<boolean> {
    // Implementation would require MaxMind GeoLite2 database
    // For now, return false (not blocked)
    return false;
  }
  
  // DDoS detection
  async detectDDoS(ip: string): Promise<boolean> {
    const window = 60; // 1 minute window
    const threshold = 1000; // requests per minute
    
    const key = `security:ddos:${ip}:${Math.floor(Date.now() / (window * 1000))}`;
    const count = await this.redis.incr(key);
    
    if (count === 1) {
      await this.redis.expire(key, window);
    }
    
    if (count > threshold) {
      await this.blockIP(ip, 'DDoS attack detected', 3600); // Block for 1 hour
      return true;
    }
    
    return false;
  }
  
  // Honeypot endpoints
  async setupHoneypots(): Promise<void> {
    // These would be registered as routes that shouldn't normally be accessed
    const honeypotPaths = [
      '/admin.php',
      '/wp-admin/',
      '/phpmyadmin/',
      '/.env',
      '/config.json',
      '/robots.txt',
    ];
    
    // Implementation would register these routes and block IPs that access them
  }
  
  // Security headers
  getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
    };
  }
}