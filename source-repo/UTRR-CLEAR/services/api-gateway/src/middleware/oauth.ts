import { FastifyRequest, FastifyReply } from 'fastify';
import Redis from 'ioredis';
import { Logger } from 'pino';
import axios from 'axios';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

interface OAuthProvider {
  id: string;
  name: string;
  clientId: string;
  clientSecret: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scope: string[];
  redirectUri: string;
  jwksUrl?: string;
  issuer?: string;
}

interface OAuthToken {
  accessToken: string;
  refreshToken?: string;
  idToken?: string;
  tokenType: string;
  expiresAt: number;
  scope: string[];
}

interface UserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
  roles?: string[];
  groups?: string[];
  organizationId?: string;
  metadata?: Record<string, any>;
}

interface OAuthSession {
  sessionId: string;
  providerId: string;
  userId: string;
  userInfo: UserInfo;
  tokens: OAuthToken;
  createdAt: number;
  lastUsed: number;
  expiresAt: number;
}

export class OAuthManager {
  private redis: Redis;
  private logger: Logger;
  private providers: Map<string, OAuthProvider>;
  private jwksCache: Map<string, any>;
  
  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.providers = new Map();
    this.jwksCache = new Map();
    
    this.initializeProviders();
  }
  
  private initializeProviders(): void {
    // Google OAuth 2.0 / OpenID Connect
    this.addProvider({
      id: 'google',
      name: 'Google',
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',
      scope: ['openid', 'email', 'profile'],
      redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:8003/auth/google/callback',
      jwksUrl: 'https://www.googleapis.com/oauth2/v3/certs',
      issuer: 'https://accounts.google.com',
    });
    
    // Microsoft Azure AD / Office 365
    this.addProvider({
      id: 'microsoft',
      name: 'Microsoft',
      clientId: process.env.MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
      authorizationUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
      tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      userInfoUrl: 'https://graph.microsoft.com/v1.0/me',
      scope: ['openid', 'email', 'profile', 'User.Read'],
      redirectUri: process.env.MICROSOFT_REDIRECT_URI || 'http://localhost:8003/auth/microsoft/callback',
      jwksUrl: 'https://login.microsoftonline.com/common/discovery/v2.0/keys',
      issuer: 'https://login.microsoftonline.com',
    });
    
    // Okta
    this.addProvider({
      id: 'okta',
      name: 'Okta',
      clientId: process.env.OKTA_CLIENT_ID || '',
      clientSecret: process.env.OKTA_CLIENT_SECRET || '',
      authorizationUrl: `${process.env.OKTA_DOMAIN}/oauth2/v1/authorize`,
      tokenUrl: `${process.env.OKTA_DOMAIN}/oauth2/v1/token`,
      userInfoUrl: `${process.env.OKTA_DOMAIN}/oauth2/v1/userinfo`,
      scope: ['openid', 'email', 'profile'],
      redirectUri: process.env.OKTA_REDIRECT_URI || 'http://localhost:8003/auth/okta/callback',
      jwksUrl: `${process.env.OKTA_DOMAIN}/oauth2/v1/keys`,
      issuer: process.env.OKTA_DOMAIN,
    });
    
    // Auth0
    this.addProvider({
      id: 'auth0',
      name: 'Auth0',
      clientId: process.env.AUTH0_CLIENT_ID || '',
      clientSecret: process.env.AUTH0_CLIENT_SECRET || '',
      authorizationUrl: `${process.env.AUTH0_DOMAIN}/authorize`,
      tokenUrl: `${process.env.AUTH0_DOMAIN}/oauth/token`,
      userInfoUrl: `${process.env.AUTH0_DOMAIN}/userinfo`,
      scope: ['openid', 'email', 'profile'],
      redirectUri: process.env.AUTH0_REDIRECT_URI || 'http://localhost:8003/auth/auth0/callback',
      jwksUrl: `${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`,
      issuer: process.env.AUTH0_DOMAIN,
    });
  }
  
  addProvider(provider: OAuthProvider): void {
    this.providers.set(provider.id, provider);
    this.logger.info({ providerId: provider.id, name: provider.name }, 'OAuth provider added');
  }
  
  getAuthorizationUrl(providerId: string, state?: string): string {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`OAuth provider not found: ${providerId}`);
    }
    
    const authState = state || this.generateState();
    const nonce = this.generateNonce();
    
    const params = new URLSearchParams({
      client_id: provider.clientId,
      response_type: 'code',
      scope: provider.scope.join(' '),
      redirect_uri: provider.redirectUri,
      state: authState,
      nonce,
    });
    
    // Store state and nonce for validation
    this.storeAuthState(authState, { providerId, nonce });
    
    return `${provider.authorizationUrl}?${params.toString()}`;
  }
  
  async handleCallback(
    providerId: string,
    code: string,
    state: string
  ): Promise<{ sessionId: string; userInfo: UserInfo; tokens: OAuthToken }> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`OAuth provider not found: ${providerId}`);
    }
    
    // Validate state
    const storedState = await this.getStoredAuthState(state);
    if (!storedState || storedState.providerId !== providerId) {
      throw new Error('Invalid state parameter');
    }
    
    // Exchange code for tokens
    const tokens = await this.exchangeCodeForTokens(provider, code);
    
    // Validate ID token if present
    if (tokens.idToken) {
      await this.validateIdToken(provider, tokens.idToken, storedState.nonce);
    }
    
    // Get user information
    const userInfo = await this.getUserInfo(provider, tokens.accessToken);
    
    // Create session
    const sessionId = await this.createSession(providerId, userInfo, tokens);
    
    // Clean up state
    await this.removeAuthState(state);
    
    this.logger.info({ 
      providerId, 
      userId: userInfo.id, 
      sessionId 
    }, 'OAuth authentication successful');
    
    return { sessionId, userInfo, tokens };
  }
  
  private async exchangeCodeForTokens(provider: OAuthProvider, code: string): Promise<OAuthToken> {
    const tokenData = {
      client_id: provider.clientId,
      client_secret: provider.clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: provider.redirectUri,
    };
    
    try {
      const response = await axios.post(provider.tokenUrl, tokenData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        timeout: 10000,
      });
      
      const data = response.data;
      
      return {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        idToken: data.id_token,
        tokenType: data.token_type || 'Bearer',
        expiresAt: Date.now() + (data.expires_in * 1000),
        scope: data.scope ? data.scope.split(' ') : provider.scope,
      };
    } catch (error) {
      this.logger.error({ error, providerId: provider.id }, 'Failed to exchange code for tokens');
      throw new Error('Failed to obtain access token');
    }
  }
  
  private async getUserInfo(provider: OAuthProvider, accessToken: string): Promise<UserInfo> {
    try {
      const response = await axios.get(provider.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        },
        timeout: 10000,
      });
      
      const data = response.data;
      
      // Normalize user info across providers
      return this.normalizeUserInfo(provider.id, data);
    } catch (error) {
      this.logger.error({ error, providerId: provider.id }, 'Failed to get user info');
      throw new Error('Failed to obtain user information');
    }
  }
  
  private normalizeUserInfo(providerId: string, rawUserInfo: any): UserInfo {
    switch (providerId) {
      case 'google':
        return {
          id: rawUserInfo.id,
          email: rawUserInfo.email,
          name: rawUserInfo.name,
          picture: rawUserInfo.picture,
          metadata: {
            verified_email: rawUserInfo.verified_email,
            locale: rawUserInfo.locale,
          },
        };
        
      case 'microsoft':
        return {
          id: rawUserInfo.id,
          email: rawUserInfo.mail || rawUserInfo.userPrincipalName,
          name: rawUserInfo.displayName,
          picture: rawUserInfo.photo,
          metadata: {
            jobTitle: rawUserInfo.jobTitle,
            department: rawUserInfo.department,
            officeLocation: rawUserInfo.officeLocation,
          },
        };
        
      case 'okta':
        return {
          id: rawUserInfo.sub,
          email: rawUserInfo.email,
          name: rawUserInfo.name,
          picture: rawUserInfo.picture,
          groups: rawUserInfo.groups,
          metadata: {
            preferred_username: rawUserInfo.preferred_username,
            locale: rawUserInfo.locale,
            zoneinfo: rawUserInfo.zoneinfo,
          },
        };
        
      case 'auth0':
        return {
          id: rawUserInfo.sub,
          email: rawUserInfo.email,
          name: rawUserInfo.name,
          picture: rawUserInfo.picture,
          metadata: {
            email_verified: rawUserInfo.email_verified,
            updated_at: rawUserInfo.updated_at,
          },
        };
        
      default:
        return {
          id: rawUserInfo.sub || rawUserInfo.id,
          email: rawUserInfo.email,
          name: rawUserInfo.name || rawUserInfo.displayName,
          picture: rawUserInfo.picture || rawUserInfo.avatar,
        };
    }
  }
  
  private async validateIdToken(provider: OAuthProvider, idToken: string, expectedNonce: string): Promise<void> {
    if (!provider.jwksUrl || !provider.issuer) {
      return; // Skip validation if JWKS URL or issuer not configured
    }
    
    try {
      // Decode token header to get key ID
      const decoded = jwt.decode(idToken, { complete: true });
      if (!decoded || typeof decoded === 'string') {
        throw new Error('Invalid ID token format');
      }
      
      const keyId = decoded.header.kid;
      if (!keyId) {
        throw new Error('No key ID in token header');
      }
      
      // Get public key for verification
      const publicKey = await this.getPublicKey(provider.jwksUrl, keyId);
      
      // Verify token
      const payload = jwt.verify(idToken, publicKey, {
        issuer: provider.issuer,
        audience: provider.clientId,
        algorithms: ['RS256'],
      }) as any;
      
      // Verify nonce
      if (payload.nonce !== expectedNonce) {
        throw new Error('Invalid nonce in ID token');
      }
      
      this.logger.debug({ providerId: provider.id }, 'ID token validated successfully');
    } catch (error) {
      this.logger.error({ error, providerId: provider.id }, 'ID token validation failed');
      throw new Error('Invalid ID token');
    }
  }
  
  private async getPublicKey(jwksUrl: string, keyId: string): Promise<string> {
    // Check cache first
    const cacheKey = `${jwksUrl}:${keyId}`;
    let jwks = this.jwksCache.get(cacheKey);
    
    if (!jwks) {
      // Fetch JWKS
      const response = await axios.get(jwksUrl, { timeout: 10000 });
      const keys = response.data.keys;
      
      const key = keys.find((k: any) => k.kid === keyId);
      if (!key) {
        throw new Error(`Key not found: ${keyId}`);
      }
      
      // Convert JWK to PEM format (simplified for RS256)
      const publicKey = this.jwkToPem(key);
      
      // Cache for 1 hour
      this.jwksCache.set(cacheKey, publicKey);
      setTimeout(() => this.jwksCache.delete(cacheKey), 3600000);
      
      jwks = publicKey;
    }
    
    return jwks;
  }
  
  private jwkToPem(jwk: any): string {
    // Simplified JWK to PEM conversion for RS256
    // In production, use a proper library like node-jose
    const modulus = Buffer.from(jwk.n, 'base64url');
    const exponent = Buffer.from(jwk.e, 'base64url');
    
    // This is a simplified implementation
    // Use a proper library like node-jose in production
    return `-----BEGIN PUBLIC KEY-----\n${Buffer.concat([modulus, exponent]).toString('base64')}\n-----END PUBLIC KEY-----`;
  }
  
  private async createSession(providerId: string, userInfo: UserInfo, tokens: OAuthToken): Promise<string> {
    const sessionId = uuidv4();
    const session: OAuthSession = {
      sessionId,
      providerId,
      userId: userInfo.id,
      userInfo,
      tokens,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      expiresAt: Date.now() + 28800000, // 8 hours
    };
    
    await this.redis.setex(
      `oauth:session:${sessionId}`,
      28800, // 8 hours TTL
      JSON.stringify(session)
    );
    
    return sessionId;
  }
  
  async getSession(sessionId: string): Promise<OAuthSession | null> {
    try {
      const sessionData = await this.redis.get(`oauth:session:${sessionId}`);
      if (!sessionData) {
        return null;
      }
      
      const session: OAuthSession = JSON.parse(sessionData);
      
      // Check if session is expired
      if (Date.now() > session.expiresAt) {
        await this.removeSession(sessionId);
        return null;
      }
      
      // Update last used timestamp
      session.lastUsed = Date.now();
      await this.redis.setex(
        `oauth:session:${sessionId}`,
        Math.floor((session.expiresAt - Date.now()) / 1000),
        JSON.stringify(session)
      );
      
      return session;
    } catch (error) {
      this.logger.error({ error, sessionId }, 'Error getting OAuth session');
      return null;
    }
  }
  
  async removeSession(sessionId: string): Promise<void> {
    await this.redis.del(`oauth:session:${sessionId}`);
  }
  
  async refreshTokens(sessionId: string): Promise<OAuthToken | null> {
    const session = await this.getSession(sessionId);
    if (!session || !session.tokens.refreshToken) {
      return null;
    }
    
    const provider = this.providers.get(session.providerId);
    if (!provider) {
      return null;
    }
    
    try {
      const tokenData = {
        client_id: provider.clientId,
        client_secret: provider.clientSecret,
        refresh_token: session.tokens.refreshToken,
        grant_type: 'refresh_token',
      };
      
      const response = await axios.post(provider.tokenUrl, tokenData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        timeout: 10000,
      });
      
      const data = response.data;
      
      const newTokens: OAuthToken = {
        accessToken: data.access_token,
        refreshToken: data.refresh_token || session.tokens.refreshToken,
        idToken: data.id_token,
        tokenType: data.token_type || 'Bearer',
        expiresAt: Date.now() + (data.expires_in * 1000),
        scope: data.scope ? data.scope.split(' ') : session.tokens.scope,
      };
      
      // Update session with new tokens
      session.tokens = newTokens;
      await this.redis.setex(
        `oauth:session:${sessionId}`,
        Math.floor((session.expiresAt - Date.now()) / 1000),
        JSON.stringify(session)
      );
      
      return newTokens;
    } catch (error) {
      this.logger.error({ error, sessionId, providerId: session.providerId }, 'Failed to refresh tokens');
      return null;
    }
  }
  
  private generateState(): string {
    return crypto.randomBytes(32).toString('hex');
  }
  
  private generateNonce(): string {
    return crypto.randomBytes(16).toString('hex');
  }
  
  private async storeAuthState(state: string, data: { providerId: string; nonce: string }): Promise<void> {
    await this.redis.setex(`oauth:state:${state}`, 600, JSON.stringify(data)); // 10 minutes TTL
  }
  
  private async getStoredAuthState(state: string): Promise<{ providerId: string; nonce: string } | null> {
    const data = await this.redis.get(`oauth:state:${state}`);
    return data ? JSON.parse(data) : null;
  }
  
  private async removeAuthState(state: string): Promise<void> {
    await this.redis.del(`oauth:state:${state}`);
  }
  
  // Middleware for Fastify
  async authenticateOAuth(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const sessionId = this.extractSessionId(request);
    
    if (!sessionId) {
      reply.code(401).send({
        error: 'Unauthorized',
        message: 'No OAuth session found',
      });
      return;
    }
    
    const session = await this.getSession(sessionId);
    if (!session) {
      reply.code(401).send({
        error: 'Unauthorized',
        message: 'Invalid or expired OAuth session',
      });
      return;
    }
    
    // Check if access token is expired and try to refresh
    if (Date.now() >= session.tokens.expiresAt) {
      const newTokens = await this.refreshTokens(sessionId);
      if (!newTokens) {
        reply.code(401).send({
          error: 'Unauthorized',
          message: 'Failed to refresh access token',
        });
        return;
      }
      session.tokens = newTokens;
    }
    
    // Add user info to request
    (request as any).oauthSession = session;
    (request as any).user = session.userInfo;
  }
  
  private extractSessionId(request: FastifyRequest): string | null {
    // Try to get session ID from various sources
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    const sessionCookie = request.headers.cookie?.match(/session=([^;]+)/);
    if (sessionCookie) {
      return sessionCookie[1];
    }
    
    const sessionHeader = request.headers['x-session-id'] as string;
    if (sessionHeader) {
      return sessionHeader;
    }
    
    return null;
  }
  
  // Management methods
  getProviders(): Array<{ id: string; name: string; scope: string[] }> {
    return Array.from(this.providers.values()).map(provider => ({
      id: provider.id,
      name: provider.name,
      scope: provider.scope,
    }));
  }
  
  async getActiveSessions(): Promise<Array<{ sessionId: string; providerId: string; userId: string; lastUsed: string }>> {
    const keys = await this.redis.keys('oauth:session:*');
    const sessions = [];
    
    for (const key of keys) {
      const sessionData = await this.redis.get(key);
      if (sessionData) {
        const session: OAuthSession = JSON.parse(sessionData);
        sessions.push({
          sessionId: session.sessionId,
          providerId: session.providerId,
          userId: session.userId,
          lastUsed: new Date(session.lastUsed).toISOString(),
        });
      }
    }
    
    return sessions.sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime());
  }
  
  async revokeSession(sessionId: string): Promise<void> {
    await this.removeSession(sessionId);
    this.logger.info({ sessionId }, 'OAuth session revoked');
  }
  
  async revokeAllUserSessions(userId: string): Promise<void> {
    const keys = await this.redis.keys('oauth:session:*');
    let revokedCount = 0;
    
    for (const key of keys) {
      const sessionData = await this.redis.get(key);
      if (sessionData) {
        const session: OAuthSession = JSON.parse(sessionData);
        if (session.userId === userId) {
          await this.redis.del(key);
          revokedCount++;
        }
      }
    }
    
    this.logger.info({ userId, revokedCount }, 'All user OAuth sessions revoked');
  }
}