import Redis from 'ioredis';
import { Logger } from 'pino';

interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
}

enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open',
}

interface CircuitStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: number;
  lastSuccessTime?: number;
  nextAttemptTime?: number;
}

export class CircuitBreaker {
  private redis: Redis;
  private logger: Logger;
  private configs: Map<string, CircuitBreakerConfig>;
  
  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.configs = new Map();
    
    // Default configurations for different services
    this.setConfig('main-app', {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 300000, // 5 minutes
      halfOpenMaxCalls: 3,
    });
    
    this.setConfig('spatial-processor', {
      failureThreshold: 3,
      recoveryTimeout: 30000, // 30 seconds
      monitoringPeriod: 180000, // 3 minutes
      halfOpenMaxCalls: 2,
    });
    
    this.setConfig('websocket-server', {
      failureThreshold: 5,
      recoveryTimeout: 30000, // 30 seconds
      monitoringPeriod: 180000, // 3 minutes
      halfOpenMaxCalls: 3,
    });
  }
  
  setConfig(serviceName: string, config: CircuitBreakerConfig): void {
    this.configs.set(serviceName, config);
    this.logger.debug({ serviceName, config }, 'Circuit breaker config set');
  }
  
  async execute<T>(serviceName: string, operation: () => Promise<T>): Promise<T> {
    const config = this.configs.get(serviceName);
    if (!config) {
      throw new Error(`No circuit breaker configuration found for service: ${serviceName}`);
    }
    
    const stats = await this.getStats(serviceName);
    
    // Check if circuit is open
    if (stats.state === CircuitState.OPEN) {
      if (Date.now() < (stats.nextAttemptTime || 0)) {
        throw new Error(`Circuit breaker is OPEN for ${serviceName}. Next attempt at ${new Date(stats.nextAttemptTime!)}`);
      } else {
        // Transition to half-open
        await this.setState(serviceName, CircuitState.HALF_OPEN);
        stats.state = CircuitState.HALF_OPEN;
        this.logger.info({ serviceName }, 'Circuit breaker transitioning to HALF_OPEN');
      }
    }
    
    // Check if we should limit calls in half-open state
    if (stats.state === CircuitState.HALF_OPEN) {
      const halfOpenCalls = await this.getHalfOpenCallCount(serviceName);
      if (halfOpenCalls >= config.halfOpenMaxCalls) {
        throw new Error(`Circuit breaker is HALF_OPEN for ${serviceName} and max calls exceeded`);
      }
      await this.incrementHalfOpenCallCount(serviceName);
    }
    
    try {
      const startTime = Date.now();
      const result = await operation();
      const duration = Date.now() - startTime;
      
      await this.recordSuccess(serviceName, duration);
      
      // If we're in half-open state and have enough successful calls, close the circuit
      if (stats.state === CircuitState.HALF_OPEN) {
        const successCount = await this.getRecentSuccessCount(serviceName);
        if (successCount >= config.halfOpenMaxCalls) {
          await this.setState(serviceName, CircuitState.CLOSED);
          await this.resetHalfOpenCallCount(serviceName);
          this.logger.info({ serviceName }, 'Circuit breaker transitioning to CLOSED');
        }
      }
      
      return result;
    } catch (error) {
      await this.recordFailure(serviceName, error);
      
      // Check if we should open the circuit
      const failureCount = await this.getRecentFailureCount(serviceName, config.monitoringPeriod);
      if (failureCount >= config.failureThreshold) {
        await this.setState(serviceName, CircuitState.OPEN);
        await this.setNextAttemptTime(serviceName, Date.now() + config.recoveryTimeout);
        this.logger.warn({ serviceName, failureCount }, 'Circuit breaker transitioning to OPEN');
      }
      
      throw error;
    }
  }
  
  private async getStats(serviceName: string): Promise<CircuitStats> {
    const key = `circuit:${serviceName}:stats`;
    const data = await this.redis.get(key);
    
    if (data) {
      return JSON.parse(data);
    }
    
    // Return default stats for new circuit
    const defaultStats: CircuitStats = {
      state: CircuitState.CLOSED,
      failureCount: 0,
      successCount: 0,
    };
    
    await this.redis.setex(key, 3600, JSON.stringify(defaultStats)); // 1 hour TTL
    return defaultStats;
  }
  
  private async setState(serviceName: string, state: CircuitState): Promise<void> {
    const key = `circuit:${serviceName}:stats`;
    const stats = await this.getStats(serviceName);
    stats.state = state;
    
    await this.redis.setex(key, 3600, JSON.stringify(stats));
  }
  
  private async setNextAttemptTime(serviceName: string, time: number): Promise<void> {
    const key = `circuit:${serviceName}:stats`;
    const stats = await this.getStats(serviceName);
    stats.nextAttemptTime = time;
    
    await this.redis.setex(key, 3600, JSON.stringify(stats));
  }
  
  private async recordSuccess(serviceName: string, duration: number): Promise<void> {
    const now = Date.now();
    
    // Record success event
    const successKey = `circuit:${serviceName}:success`;
    await this.redis.zadd(successKey, now, `${now}:${duration}`);
    await this.redis.expire(successKey, 3600); // 1 hour TTL
    
    // Update stats
    const statsKey = `circuit:${serviceName}:stats`;
    const stats = await this.getStats(serviceName);
    stats.successCount += 1;
    stats.lastSuccessTime = now;
    
    await this.redis.setex(statsKey, 3600, JSON.stringify(stats));
    
    // Clean old entries
    await this.redis.zremrangebyscore(successKey, 0, now - 3600000); // Remove entries older than 1 hour
  }
  
  private async recordFailure(serviceName: string, error: any): Promise<void> {
    const now = Date.now();
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Record failure event
    const failureKey = `circuit:${serviceName}:failure`;
    await this.redis.zadd(failureKey, now, `${now}:${errorMessage}`);
    await this.redis.expire(failureKey, 3600); // 1 hour TTL
    
    // Update stats
    const statsKey = `circuit:${serviceName}:stats`;
    const stats = await this.getStats(serviceName);
    stats.failureCount += 1;
    stats.lastFailureTime = now;
    
    await this.redis.setex(statsKey, 3600, JSON.stringify(stats));
    
    // Clean old entries
    await this.redis.zremrangebyscore(failureKey, 0, now - 3600000); // Remove entries older than 1 hour
    
    this.logger.warn({ serviceName, error: errorMessage }, 'Circuit breaker recorded failure');
  }
  
  private async getRecentFailureCount(serviceName: string, periodMs: number): Promise<number> {
    const failureKey = `circuit:${serviceName}:failure`;
    const now = Date.now();
    const since = now - periodMs;
    
    return await this.redis.zcount(failureKey, since, now);
  }
  
  private async getRecentSuccessCount(serviceName: string): Promise<number> {
    const successKey = `circuit:${serviceName}:success`;
    const now = Date.now();
    const since = now - 300000; // Last 5 minutes
    
    return await this.redis.zcount(successKey, since, now);
  }
  
  private async getHalfOpenCallCount(serviceName: string): Promise<number> {
    const key = `circuit:${serviceName}:half_open_calls`;
    const count = await this.redis.get(key);
    return count ? parseInt(count) : 0;
  }
  
  private async incrementHalfOpenCallCount(serviceName: string): Promise<void> {
    const key = `circuit:${serviceName}:half_open_calls`;
    await this.redis.incr(key);
    await this.redis.expire(key, 300); // 5 minutes TTL
  }
  
  private async resetHalfOpenCallCount(serviceName: string): Promise<void> {
    const key = `circuit:${serviceName}:half_open_calls`;
    await this.redis.del(key);
  }
  
  // Management methods
  async getCircuitStatus(serviceName: string): Promise<{
    state: CircuitState;
    failureCount: number;
    successCount: number;
    lastFailureTime?: string;
    lastSuccessTime?: string;
    nextAttemptTime?: string;
    recentFailures: number;
    recentSuccesses: number;
  }> {
    const stats = await this.getStats(serviceName);
    const config = this.configs.get(serviceName);
    
    if (!config) {
      throw new Error(`No configuration found for service: ${serviceName}`);
    }
    
    const recentFailures = await this.getRecentFailureCount(serviceName, config.monitoringPeriod);
    const recentSuccesses = await this.getRecentSuccessCount(serviceName);
    
    return {
      state: stats.state,
      failureCount: stats.failureCount,
      successCount: stats.successCount,
      lastFailureTime: stats.lastFailureTime ? new Date(stats.lastFailureTime).toISOString() : undefined,
      lastSuccessTime: stats.lastSuccessTime ? new Date(stats.lastSuccessTime).toISOString() : undefined,
      nextAttemptTime: stats.nextAttemptTime ? new Date(stats.nextAttemptTime).toISOString() : undefined,
      recentFailures,
      recentSuccesses,
    };
  }
  
  async getAllCircuitStatuses(): Promise<Record<string, any>> {
    const statuses: Record<string, any> = {};
    
    for (const [serviceName] of this.configs) {
      try {
        statuses[serviceName] = await this.getCircuitStatus(serviceName);
      } catch (error) {
        statuses[serviceName] = { error: error instanceof Error ? error.message : String(error) };
      }
    }
    
    return statuses;
  }
  
  async forceOpen(serviceName: string, durationMs: number = 300000): Promise<void> {
    await this.setState(serviceName, CircuitState.OPEN);
    await this.setNextAttemptTime(serviceName, Date.now() + durationMs);
    this.logger.info({ serviceName, durationMs }, 'Circuit breaker manually opened');
  }
  
  async forceClosed(serviceName: string): Promise<void> {
    await this.setState(serviceName, CircuitState.CLOSED);
    await this.resetHalfOpenCallCount(serviceName);
    this.logger.info({ serviceName }, 'Circuit breaker manually closed');
  }
  
  async reset(serviceName: string): Promise<void> {
    const keys = [
      `circuit:${serviceName}:stats`,
      `circuit:${serviceName}:success`,
      `circuit:${serviceName}:failure`,
      `circuit:${serviceName}:half_open_calls`,
    ];
    
    await this.redis.del(...keys);
    this.logger.info({ serviceName }, 'Circuit breaker reset');
  }
  
  // Health check integration
  async isServiceHealthy(serviceName: string): Promise<boolean> {
    const stats = await this.getStats(serviceName);
    return stats.state !== CircuitState.OPEN;
  }
  
  // Metrics for monitoring
  async getMetrics(serviceName: string): Promise<{
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    failureRate: number;
    circuitOpenCount: number;
  }> {
    const successKey = `circuit:${serviceName}:success`;
    const failureKey = `circuit:${serviceName}:failure`;
    const now = Date.now();
    const since = now - 3600000; // Last hour
    
    const successEntries = await this.redis.zrangebyscore(successKey, since, now);
    const failureEntries = await this.redis.zrangebyscore(failureKey, since, now);
    
    const totalRequests = successEntries.length + failureEntries.length;
    const successRate = totalRequests > 0 ? successEntries.length / totalRequests : 0;
    const failureRate = totalRequests > 0 ? failureEntries.length / totalRequests : 0;
    
    // Calculate average response time from success entries
    const responseTimes = successEntries
      .map(entry => {
        const [, duration] = entry.split(':');
        return parseInt(duration);
      })
      .filter(time => !isNaN(time));
    
    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;
    
    // Count circuit open events (simplified)
    const stats = await this.getStats(serviceName);
    const circuitOpenCount = stats.state === CircuitState.OPEN ? 1 : 0;
    
    return {
      totalRequests,
      successRate,
      averageResponseTime,
      failureRate,
      circuitOpenCount,
    };
  }
}