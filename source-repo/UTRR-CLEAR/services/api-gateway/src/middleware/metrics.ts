import Redis from 'ioredis';
import { Logger } from 'pino';
import { FastifyRequest, FastifyReply } from 'fastify';

interface RequestMetrics {
  timestamp: number;
  method: string;
  path: string;
  statusCode: number;
  responseTime: number;
  requestSize: number;
  responseSize: number;
  userAgent: string;
  ip: string;
  apiKeyId?: string;
  userId?: string;
  error?: string;
}

interface AggregatedMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  topEndpoints: Array<{ path: string; count: number }>;
  topErrors: Array<{ error: string; count: number }>;
  statusCodeDistribution: Record<string, number>;
}

export class MetricsCollector {
  private redis: Redis;
  private logger: Logger;
  private requestStartTimes: Map<string, number>;
  
  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.requestStartTimes = new Map();
  }
  
  async collectRequestMetrics(request: FastifyRequest): Promise<void> {
    // Store request start time for response time calculation
    this.requestStartTimes.set(request.id, Date.now());
    
    try {
      // Increment request counter
      await this.incrementCounter('total_requests');
      await this.incrementCounter(`requests_by_method:${request.method}`);
      
      // Track by path (normalized)
      const normalizedPath = this.normalizePath(request.url);
      await this.incrementCounter(`requests_by_path:${normalizedPath}`);
      
      // Track by user agent category
      const userAgentCategory = this.categorizeUserAgent(request.headers['user-agent'] || '');
      await this.incrementCounter(`requests_by_user_agent:${userAgentCategory}`);
      
      // Track API key usage if present
      const apiKey = request.headers['x-api-key'] as string;
      if (apiKey) {
        await this.incrementCounter('api_key_requests');
        // Hash the API key for privacy
        const hashedKey = this.hashApiKey(apiKey);
        await this.incrementCounter(`requests_by_api_key:${hashedKey}`);
      }
      
      // Track request size
      const contentLength = parseInt(request.headers['content-length'] || '0');
      await this.recordHistogram('request_size_bytes', contentLength);
      
    } catch (error) {
      this.logger.error({ error, requestId: request.id }, 'Error collecting request metrics');
    }
  }
  
  async collectResponseMetrics(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const startTime = this.requestStartTimes.get(request.id);
    if (!startTime) {
      return;
    }
    
    try {
      const responseTime = Date.now() - startTime;
      const statusCode = reply.statusCode;
      const normalizedPath = this.normalizePath(request.url);
      
      // Record response time
      await this.recordHistogram('response_time_ms', responseTime);
      await this.recordHistogram(`response_time_by_path:${normalizedPath}`, responseTime);
      
      // Record status code
      await this.incrementCounter(`status_code:${statusCode}`);
      await this.incrementCounter(`status_code_by_path:${normalizedPath}:${statusCode}`);
      
      // Record success/error
      if (statusCode >= 200 && statusCode < 400) {
        await this.incrementCounter('successful_requests');
      } else {
        await this.incrementCounter('failed_requests');
        await this.incrementCounter(`errors_by_status:${statusCode}`);
        
        // Record error details if available
        if (statusCode >= 500) {
          await this.incrementCounter('server_errors');
        } else if (statusCode >= 400) {
          await this.incrementCounter('client_errors');
        }
      }
      
      // Record response size if available
      const responseSize = this.getResponseSize(reply);
      if (responseSize > 0) {
        await this.recordHistogram('response_size_bytes', responseSize);
      }
      
      // Store detailed metrics for recent requests
      const metrics: RequestMetrics = {
        timestamp: Date.now(),
        method: request.method,
        path: normalizedPath,
        statusCode,
        responseTime,
        requestSize: parseInt(request.headers['content-length'] || '0'),
        responseSize,
        userAgent: request.headers['user-agent'] || '',
        ip: this.getClientIP(request),
        apiKeyId: (request as any).apiKey?.keyId,
        userId: (request.user as any)?.id,
        error: statusCode >= 400 ? this.getErrorMessage(reply) : undefined,
      };
      
      await this.storeDetailedMetrics(metrics);
      
    } catch (error) {
      this.logger.error({ error, requestId: request.id }, 'Error collecting response metrics');
    } finally {
      // Clean up request start time
      this.requestStartTimes.delete(request.id);
    }
  }
  
  private async incrementCounter(key: string, value: number = 1): Promise<void> {
    // Increment both current and time-windowed counters
    await this.redis.incrby(`metrics:counter:${key}`, value);
    
    // Time-windowed counters (for rate calculations)
    const now = Date.now();
    const minute = Math.floor(now / 60000);
    const hour = Math.floor(now / 3600000);
    
    await this.redis.incrby(`metrics:counter:${key}:minute:${minute}`, value);
    await this.redis.expire(`metrics:counter:${key}:minute:${minute}`, 120); // 2 minutes TTL
    
    await this.redis.incrby(`metrics:counter:${key}:hour:${hour}`, value);
    await this.redis.expire(`metrics:counter:${key}:hour:${hour}`, 7200); // 2 hours TTL
  }
  
  private async recordHistogram(key: string, value: number): Promise<void> {
    // Store in sorted set for percentile calculations
    const now = Date.now();
    await this.redis.zadd(`metrics:histogram:${key}`, value, `${now}:${value}`);
    
    // Keep only recent values (last hour)
    await this.redis.zremrangebyscore(`metrics:histogram:${key}`, 0, now - 3600000);
    await this.redis.expire(`metrics:histogram:${key}`, 3600); // 1 hour TTL
  }
  
  private async storeDetailedMetrics(metrics: RequestMetrics): Promise<void> {
    // Store in a list for detailed analysis
    await this.redis.lpush('metrics:detailed:requests', JSON.stringify(metrics));
    await this.redis.ltrim('metrics:detailed:requests', 0, 9999); // Keep last 10k requests
    await this.redis.expire('metrics:detailed:requests', 86400); // 24 hours TTL
  }
  
  async getAggregatedMetrics(timeRangeMs: number = 3600000): Promise<AggregatedMetrics> {
    const now = Date.now();
    const since = now - timeRangeMs;
    
    // Get detailed requests for calculations
    const detailedRequestsData = await this.redis.lrange('metrics:detailed:requests', 0, -1);
    const requests: RequestMetrics[] = detailedRequestsData
      .map(data => JSON.parse(data))
      .filter(req => req.timestamp >= since);
    
    if (requests.length === 0) {
      return this.getEmptyMetrics();
    }
    
    const totalRequests = requests.length;
    const successfulRequests = requests.filter(r => r.statusCode >= 200 && r.statusCode < 400).length;
    const failedRequests = totalRequests - successfulRequests;
    
    // Calculate response time statistics
    const responseTimes = requests.map(r => r.responseTime).sort((a, b) => a - b);
    const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const p95ResponseTime = this.getPercentile(responseTimes, 95);
    const p99ResponseTime = this.getPercentile(responseTimes, 99);
    
    // Calculate requests per second
    const requestsPerSecond = totalRequests / (timeRangeMs / 1000);
    
    // Calculate error rate
    const errorRate = totalRequests > 0 ? failedRequests / totalRequests : 0;
    
    // Get top endpoints
    const endpointCounts: Record<string, number> = {};
    requests.forEach(req => {
      endpointCounts[req.path] = (endpointCounts[req.path] || 0) + 1;
    });
    const topEndpoints = Object.entries(endpointCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([path, count]) => ({ path, count }));
    
    // Get top errors
    const errorCounts: Record<string, number> = {};
    requests.filter(r => r.error).forEach(req => {
      const error = req.error!;
      errorCounts[error] = (errorCounts[error] || 0) + 1;
    });
    const topErrors = Object.entries(errorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([error, count]) => ({ error, count }));
    
    // Get status code distribution
    const statusCodeDistribution: Record<string, number> = {};
    requests.forEach(req => {
      const code = req.statusCode.toString();
      statusCodeDistribution[code] = (statusCodeDistribution[code] || 0) + 1;
    });
    
    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      requestsPerSecond,
      errorRate,
      topEndpoints,
      topErrors,
      statusCodeDistribution,
    };
  }
  
  async getPrometheusMetrics(): Promise<string> {
    const metrics = await this.getAggregatedMetrics();
    const now = Date.now();
    
    let output = '';
    
    // Basic counters
    output += `# HELP api_gateway_requests_total Total number of requests\n`;
    output += `# TYPE api_gateway_requests_total counter\n`;
    output += `api_gateway_requests_total ${metrics.totalRequests} ${now}\n\n`;
    
    output += `# HELP api_gateway_requests_successful_total Total number of successful requests\n`;
    output += `# TYPE api_gateway_requests_successful_total counter\n`;
    output += `api_gateway_requests_successful_total ${metrics.successfulRequests} ${now}\n\n`;
    
    output += `# HELP api_gateway_requests_failed_total Total number of failed requests\n`;
    output += `# TYPE api_gateway_requests_failed_total counter\n`;
    output += `api_gateway_requests_failed_total ${metrics.failedRequests} ${now}\n\n`;
    
    // Response time metrics
    output += `# HELP api_gateway_response_time_seconds Response time in seconds\n`;
    output += `# TYPE api_gateway_response_time_seconds histogram\n`;
    output += `api_gateway_response_time_seconds_sum ${metrics.averageResponseTime * metrics.totalRequests / 1000} ${now}\n`;
    output += `api_gateway_response_time_seconds_count ${metrics.totalRequests} ${now}\n`;
    output += `api_gateway_response_time_seconds{quantile="0.95"} ${metrics.p95ResponseTime / 1000} ${now}\n`;
    output += `api_gateway_response_time_seconds{quantile="0.99"} ${metrics.p99ResponseTime / 1000} ${now}\n\n`;
    
    // Rate metrics
    output += `# HELP api_gateway_requests_per_second Current request rate\n`;
    output += `# TYPE api_gateway_requests_per_second gauge\n`;
    output += `api_gateway_requests_per_second ${metrics.requestsPerSecond} ${now}\n\n`;
    
    output += `# HELP api_gateway_error_rate Current error rate\n`;
    output += `# TYPE api_gateway_error_rate gauge\n`;
    output += `api_gateway_error_rate ${metrics.errorRate} ${now}\n\n`;
    
    // Status code distribution
    output += `# HELP api_gateway_requests_by_status_code Requests by HTTP status code\n`;
    output += `# TYPE api_gateway_requests_by_status_code counter\n`;
    Object.entries(metrics.statusCodeDistribution).forEach(([code, count]) => {
      output += `api_gateway_requests_by_status_code{status_code="${code}"} ${count} ${now}\n`;
    });
    output += '\n';
    
    // Top endpoints
    output += `# HELP api_gateway_requests_by_endpoint Requests by endpoint\n`;
    output += `# TYPE api_gateway_requests_by_endpoint counter\n`;
    metrics.topEndpoints.forEach(({ path, count }) => {
      output += `api_gateway_requests_by_endpoint{endpoint="${path}"} ${count} ${now}\n`;
    });
    
    return output;
  }
  
  private normalizePath(url: string): string {
    // Remove query parameters
    const path = url.split('?')[0] || url;
    
    // Replace dynamic segments with placeholders
    return path
      .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:uuid')
      .replace(/\/\d+/g, '/:id')
      .replace(/\/[a-f0-9]{24}/g, '/:objectid');
  }
  
  private categorizeUserAgent(userAgent: string): string {
    const ua = userAgent.toLowerCase();
    
    if (ua.includes('bot') || ua.includes('crawler') || ua.includes('spider')) {
      return 'bot';
    } else if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'mobile';
    } else if (ua.includes('curl') || ua.includes('wget') || ua.includes('postman')) {
      return 'tool';
    } else if (ua.includes('chrome')) {
      return 'chrome';
    } else if (ua.includes('firefox')) {
      return 'firefox';
    } else if (ua.includes('safari')) {
      return 'safari';
    } else if (ua.includes('edge')) {
      return 'edge';
    } else {
      return 'other';
    }
  }
  
  private hashApiKey(apiKey: string): string {
    // Simple hash for grouping without exposing the actual key
    return apiKey.substring(0, 8) + '...';
  }
  
  private getClientIP(request: FastifyRequest): string {
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIP = request.headers['x-real-ip'] as string;
    const cfConnectingIP = request.headers['cf-connecting-ip'] as string;
    
    return cfConnectingIP || realIP || (forwarded && forwarded.split(',')[0]?.trim()) || request.socket.remoteAddress || '';
  }
  
  private getResponseSize(reply: FastifyReply): number {
    const contentLength = reply.getHeader('content-length');
    if (typeof contentLength === 'string') {
      return parseInt(contentLength);
    } else if (typeof contentLength === 'number') {
      return contentLength;
    }
    return 0;
  }
  
  private getErrorMessage(reply: FastifyReply): string {
    // Try to extract error message from response
    const payload = (reply as any).payload;
    if (payload && typeof payload === 'object') {
      return payload.message || payload.error || 'Unknown error';
    }
    return `HTTP ${reply.statusCode}`;
  }
  
  private getPercentile(sortedArray: number[], percentile: number): number {
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, index)] || 0;
  }
  
  private getEmptyMetrics(): AggregatedMetrics {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      requestsPerSecond: 0,
      errorRate: 0,
      topEndpoints: [],
      topErrors: [],
      statusCodeDistribution: {},
    };
  }
  
  // Cleanup methods
  async cleanupOldMetrics(): Promise<void> {
    const patterns = [
      'metrics:counter:*:minute:*',
      'metrics:counter:*:hour:*',
      'metrics:histogram:*',
    ];
    
    for (const pattern of patterns) {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        // Check TTL and remove expired keys
        for (const key of keys) {
          const ttl = await this.redis.ttl(key);
          if (ttl === -1) {
            // Key exists but has no TTL, remove it
            await this.redis.del(key);
          }
        }
      }
    }
    
    this.logger.debug('Old metrics cleaned up');
  }
  
  // Real-time metrics for dashboard
  async getRealTimeMetrics(): Promise<{
    currentRPS: number;
    activeConnections: number;
    errorRate: number;
    averageResponseTime: number;
    topActiveEndpoints: Array<{ path: string; count: number }>;
  }> {
    // Get metrics for the last 5 minutes
    const metrics = await this.getAggregatedMetrics(300000); // 5 minutes
    
    // Calculate current RPS (requests per second in the last minute)
    const now = Date.now();
    const lastMinute = Math.floor(now / 60000);
    const currentMinuteKey = `metrics:counter:total_requests:minute:${lastMinute}`;
    const currentMinuteRequests = await this.redis.get(currentMinuteKey);
    const currentRPS = currentMinuteRequests ? parseInt(currentMinuteRequests) / 60 : 0;
    
    return {
      currentRPS,
      activeConnections: this.requestStartTimes.size,
      errorRate: metrics.errorRate,
      averageResponseTime: metrics.averageResponseTime,
      topActiveEndpoints: metrics.topEndpoints.slice(0, 5),
    };
  }
}