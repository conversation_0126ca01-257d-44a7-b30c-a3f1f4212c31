import { FastifyRequest, FastifyReply } from 'fastify';
import { Logger } from 'pino';
import { z } from 'zod';

interface TransformRule {
  id: string;
  name: string;
  pattern: RegExp;
  requestTransform?: (data: any) => any;
  responseTransform?: (data: any) => any;
  headers?: Record<string, string>;
  enabled: boolean;
}

export class RequestTransformer {
  private logger: Logger;
  private transformRules: TransformRule[];
  
  constructor(logger: Logger) {
    this.logger = logger;
    this.transformRules = this.initializeDefaultRules();
  }
  
  private initializeDefaultRules(): TransformRule[] {
    return [
      // API versioning transformation
      {
        id: 'api-v1-transform',
        name: 'API v1 Response Format',
        pattern: /^\/api\/v1\//,
        responseTransform: (data: any) => {
          if (typeof data === 'object' && data !== null) {
            return {
              version: '1.0',
              timestamp: new Date().toISOString(),
              data,
              meta: {
                apiVersion: 'v1',
                deprecationWarning: data.length > 100 ? 'Large response detected. Consider pagination.' : undefined,
              },
            };
          }
          return data;
        },
        enabled: true,
      },
      
      // Spatial data transformation
      {
        id: 'spatial-coordinates-transform',
        name: 'Spatial Coordinates Normalization',
        pattern: /^\/api\/(spatial|conflicts|utilities)/,
        requestTransform: (data: any) => {
          if (data && typeof data === 'object') {
            return this.normalizeCoordinates(data);
          }
          return data;
        },
        responseTransform: (data: any) => {
          if (data && typeof data === 'object') {
            return this.formatSpatialResponse(data);
          }
          return data;
        },
        enabled: true,
      },
      
      // Project data enrichment
      {
        id: 'project-data-enrichment',
        name: 'Project Data Enrichment',
        pattern: /^\/api\/(projects|dashboard)/,
        responseTransform: (data: any) => {
          if (Array.isArray(data)) {
            return data.map(item => this.enrichProjectData(item));
          } else if (data && typeof data === 'object') {
            return this.enrichProjectData(data);
          }
          return data;
        },
        enabled: true,
      },
      
      // Error response standardization
      {
        id: 'error-standardization',
        name: 'Error Response Standardization',
        pattern: /.*/,
        responseTransform: (data: any, statusCode?: number) => {
          if (statusCode && statusCode >= 400) {
            return this.standardizeErrorResponse(data, statusCode);
          }
          return data;
        },
        enabled: true,
      },
      
      // Security headers injection
      {
        id: 'security-headers',
        name: 'Security Headers Injection',
        pattern: /.*/,
        headers: {
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
        enabled: true,
      },
      
      // Legacy API compatibility
      {
        id: 'legacy-api-compat',
        name: 'Legacy API Compatibility',
        pattern: /^\/api\/legacy\//,
        requestTransform: (data: any) => {
          // Transform legacy request format to new format
          if (data && data.project_id) {
            data.projectId = data.project_id;
            delete data.project_id;
          }
          if (data && data.user_id) {
            data.userId = data.user_id;
            delete data.user_id;
          }
          return data;
        },
        responseTransform: (data: any) => {
          // Transform new response format to legacy format
          if (data && typeof data === 'object') {
            return this.toLegacyFormat(data);
          }
          return data;
        },
        enabled: true,
      },
    ];
  }
  
  async transformRequest(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const path = request.url;
    
    for (const rule of this.transformRules) {
      if (rule.enabled && rule.pattern.test(path)) {
        try {
          // Apply request transformation
          if (rule.requestTransform && request.body) {
            const transformedBody = rule.requestTransform(request.body);
            (request as any).body = transformedBody;
            
            this.logger.debug({ 
              ruleId: rule.id, 
              path,
              requestId: request.id 
            }, 'Request transformed');
          }
          
          // Apply headers
          if (rule.headers) {
            Object.entries(rule.headers).forEach(([key, value]) => {
              reply.header(key, value);
            });
          }
        } catch (error) {
          this.logger.error({ 
            error, 
            ruleId: rule.id, 
            path,
            requestId: request.id 
          }, 'Request transformation error');
        }
      }
    }
  }
  
  async transformResponse(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const path = request.url;
    const statusCode = reply.statusCode;
    
    // Get response payload if available
    let responsePayload = (reply as any).payload;
    
    for (const rule of this.transformRules) {
      if (rule.enabled && rule.pattern.test(path)) {
        try {
          if (rule.responseTransform && responsePayload) {
            responsePayload = rule.responseTransform(responsePayload, statusCode);
            
            this.logger.debug({ 
              ruleId: rule.id, 
              path,
              statusCode,
              requestId: request.id 
            }, 'Response transformed');
          }
        } catch (error) {
          this.logger.error({ 
            error, 
            ruleId: rule.id, 
            path,
            requestId: request.id 
          }, 'Response transformation error');
        }
      }
    }
    
    // Update the response payload
    if (responsePayload) {
      (reply as any).payload = responsePayload;
    }
  }
  
  private normalizeCoordinates(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => this.normalizeCoordinates(item));
    }
    
    if (data && typeof data === 'object') {
      const normalized = { ...data };
      
      // Normalize common coordinate field names
      if (normalized.longitude && normalized.latitude) {
        normalized.coordinates = [normalized.longitude, normalized.latitude];
      } else if (normalized.lng && normalized.lat) {
        normalized.coordinates = [normalized.lng, normalized.lat];
      } else if (normalized.x && normalized.y) {
        normalized.coordinates = [normalized.x, normalized.y];
      }
      
      // Ensure coordinates are in the correct format [-180, 180], [-90, 90]
      if (normalized.coordinates && Array.isArray(normalized.coordinates)) {
        const [lng, lat] = normalized.coordinates;
        if (lng > 180 || lng < -180 || lat > 90 || lat < -90) {
          this.logger.warn({ 
            coordinates: normalized.coordinates 
          }, 'Invalid coordinates detected');
        }
      }
      
      // Recursively normalize nested objects
      Object.keys(normalized).forEach(key => {
        if (typeof normalized[key] === 'object' && normalized[key] !== null) {
          normalized[key] = this.normalizeCoordinates(normalized[key]);
        }
      });
      
      return normalized;
    }
    
    return data;
  }
  
  private formatSpatialResponse(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => this.formatSpatialResponse(item));
    }
    
    if (data && typeof data === 'object') {
      const formatted = { ...data };
      
      // Add spatial metadata
      if (formatted.geometry || formatted.coordinates) {
        formatted.spatialMetadata = {
          hasGeometry: true,
          coordinateSystem: 'WGS84',
          precision: 'meter',
        };
        
        // Calculate bounding box if geometry exists
        if (formatted.geometry && formatted.geometry.coordinates) {
          formatted.spatialMetadata.boundingBox = this.calculateBoundingBox(formatted.geometry);
        }
      }
      
      // Format distance values
      if (formatted.distance !== undefined) {
        formatted.distanceFormatted = this.formatDistance(formatted.distance);
      }
      
      // Format area values
      if (formatted.area !== undefined) {
        formatted.areaFormatted = this.formatArea(formatted.area);
      }
      
      return formatted;
    }
    
    return data;
  }
  
  private enrichProjectData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const enriched = { ...data };
    
    // Add computed fields
    if (enriched.createdAt) {
      const created = new Date(enriched.createdAt);
      const now = new Date();
      enriched.daysActive = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
    }
    
    // Add status indicators
    if (enriched.status) {
      enriched.statusMetadata = {
        isActive: ['active', 'in_progress'].includes(enriched.status),
        requiresAttention: ['pending', 'conflict'].includes(enriched.status),
        isComplete: ['completed', 'closed'].includes(enriched.status),
      };
    }
    
    // Add utility count if utilities array exists
    if (Array.isArray(enriched.utilities)) {
      enriched.utilityCount = enriched.utilities.length;
      enriched.utilityTypes = [...new Set(enriched.utilities.map((u: any) => u.type))];
    }
    
    // Add conflict summary if conflicts exist
    if (Array.isArray(enriched.conflicts)) {
      enriched.conflictSummary = {
        total: enriched.conflicts.length,
        critical: enriched.conflicts.filter((c: any) => c.priority === 'critical').length,
        high: enriched.conflicts.filter((c: any) => c.priority === 'high').length,
        resolved: enriched.conflicts.filter((c: any) => c.status === 'resolved').length,
      };
    }
    
    return enriched;
  }
  
  private standardizeErrorResponse(data: any, statusCode: number): any {
    const standardError = {
      error: {
        code: statusCode,
        message: data?.message || data?.error || 'An error occurred',
        type: this.getErrorType(statusCode),
        timestamp: new Date().toISOString(),
      },
    };
    
    // Add details if available
    if (data?.details || data?.validation) {
      standardError.error.details = data.details || data.validation;
    }
    
    // Add help information for common errors
    if (statusCode === 401) {
      standardError.error.help = 'Ensure you have provided a valid API key or authentication token';
    } else if (statusCode === 403) {
      standardError.error.help = 'You do not have permission to access this resource';
    } else if (statusCode === 404) {
      standardError.error.help = 'The requested resource was not found';
    } else if (statusCode === 429) {
      standardError.error.help = 'You have exceeded the rate limit. Please wait before making more requests';
    }
    
    return standardError;
  }
  
  private toLegacyFormat(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => this.toLegacyFormat(item));
    }
    
    if (data && typeof data === 'object') {
      const legacy = { ...data };
      
      // Convert camelCase to snake_case for legacy compatibility
      Object.keys(legacy).forEach(key => {
        const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        if (snakeKey !== key) {
          legacy[snakeKey] = legacy[key];
          delete legacy[key];
        }
      });
      
      return legacy;
    }
    
    return data;
  }
  
  private calculateBoundingBox(geometry: any): [number, number, number, number] {
    // Simple bounding box calculation for basic geometries
    // Returns [minX, minY, maxX, maxY]
    
    let coordinates: number[][] = [];
    
    if (geometry.type === 'Point') {
      coordinates = [geometry.coordinates];
    } else if (geometry.type === 'LineString') {
      coordinates = geometry.coordinates;
    } else if (geometry.type === 'Polygon') {
      coordinates = geometry.coordinates[0]; // Outer ring
    }
    
    if (coordinates.length === 0) {
      return [0, 0, 0, 0];
    }
    
    const xs = coordinates.map(coord => coord[0]);
    const ys = coordinates.map(coord => coord[1]);
    
    return [
      Math.min(...xs),
      Math.min(...ys),
      Math.max(...xs),
      Math.max(...ys),
    ];
  }
  
  private formatDistance(distance: number): string {
    if (distance < 1) {
      return `${Math.round(distance * 100)} cm`;
    } else if (distance < 1000) {
      return `${Math.round(distance * 10) / 10} m`;
    } else {
      return `${Math.round(distance / 100) / 10} km`;
    }
  }
  
  private formatArea(area: number): string {
    if (area < 1) {
      return `${Math.round(area * 10000)} cm²`;
    } else if (area < 10000) {
      return `${Math.round(area * 100) / 100} m²`;
    } else {
      return `${Math.round(area / 10000 * 100) / 100} ha`;
    }
  }
  
  private getErrorType(statusCode: number): string {
    if (statusCode >= 400 && statusCode < 500) {
      return 'client_error';
    } else if (statusCode >= 500) {
      return 'server_error';
    }
    return 'unknown';
  }
  
  // Rule management methods
  addTransformRule(rule: TransformRule): void {
    this.transformRules.push(rule);
    this.logger.info({ ruleId: rule.id }, 'Transform rule added');
  }
  
  removeTransformRule(ruleId: string): void {
    this.transformRules = this.transformRules.filter(rule => rule.id !== ruleId);
    this.logger.info({ ruleId }, 'Transform rule removed');
  }
  
  enableRule(ruleId: string): void {
    const rule = this.transformRules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = true;
      this.logger.info({ ruleId }, 'Transform rule enabled');
    }
  }
  
  disableRule(ruleId: string): void {
    const rule = this.transformRules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = false;
      this.logger.info({ ruleId }, 'Transform rule disabled');
    }
  }
  
  listRules(): TransformRule[] {
    return this.transformRules.map(rule => ({ ...rule }));
  }
}