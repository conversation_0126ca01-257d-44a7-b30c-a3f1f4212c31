import Redis from 'ioredis';
import { Logger } from 'pino';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

interface ApiKeyData {
  keyId: string;
  hashedKey: string;
  name: string;
  permissions: string[];
  userId?: string;
  organizationId?: string;
  rateLimit: number;
  createdAt: Date;
  expiresAt?: Date;
  lastUsed?: Date;
  usageCount: number;
  isActive: boolean;
  metadata?: Record<string, any>;
}

interface ApiKeyCreateOptions {
  name: string;
  permissions: string[];
  userId?: string;
  organizationId?: string;
  rateLimit?: number;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

interface ApiKeyUsage {
  timestamp: Date;
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  ip: string;
  userAgent: string;
}

export class ApiKeyManager {
  private redis: Redis;
  private logger: Logger;
  private secretKey: string;
  
  constructor(redis: Redis, secretKey: string, logger: Logger) {
    this.redis = redis;
    this.secretKey = secretKey;
    this.logger = logger;
  }
  
  async generateApiKey(options: ApiKeyCreateOptions): Promise<{
    keyId: string;
    apiKey: string;
    name: string;
    permissions: string[];
    createdAt: string;
    expiresAt?: string;
  }> {
    const keyId = uuidv4();
    const rawKey = this.generateSecureKey();
    const apiKey = `clear_${keyId.replace(/-/g, '')}_${rawKey}`;
    const hashedKey = this.hashApiKey(apiKey);
    
    const keyData: ApiKeyData = {
      keyId,
      hashedKey,
      name: options.name,
      permissions: options.permissions,
      userId: options.userId,
      organizationId: options.organizationId,
      rateLimit: options.rateLimit || 1000,
      createdAt: new Date(),
      expiresAt: options.expiresAt,
      usageCount: 0,
      isActive: true,
      metadata: options.metadata,
    };
    
    // Store key data
    await this.redis.setex(
      `api-key:${keyId}`,
      options.expiresAt ? Math.floor((options.expiresAt.getTime() - Date.now()) / 1000) : 31536000, // 1 year default
      JSON.stringify(keyData)
    );
    
    // Index by hash for quick lookup
    await this.redis.setex(
      `api-key-hash:${hashedKey}`,
      options.expiresAt ? Math.floor((options.expiresAt.getTime() - Date.now()) / 1000) : 31536000,
      keyId
    );
    
    // Add to user's key list if userId provided
    if (options.userId) {
      await this.redis.sadd(`user-keys:${options.userId}`, keyId);
    }
    
    // Add to organization's key list if organizationId provided
    if (options.organizationId) {
      await this.redis.sadd(`org-keys:${options.organizationId}`, keyId);
    }
    
    this.logger.info({ keyId, name: options.name, userId: options.userId }, 'API key generated');
    
    return {
      keyId,
      apiKey,
      name: options.name,
      permissions: options.permissions,
      createdAt: keyData.createdAt.toISOString(),
      expiresAt: options.expiresAt?.toISOString(),
    };
  }
  
  async validateApiKey(apiKey: string): Promise<ApiKeyData | null> {
    try {
      const hashedKey = this.hashApiKey(apiKey);
      const keyId = await this.redis.get(`api-key-hash:${hashedKey}`);
      
      if (!keyId) {
        this.logger.debug({ hashedKey: hashedKey.substring(0, 10) + '...' }, 'API key not found');
        return null;
      }
      
      const keyDataStr = await this.redis.get(`api-key:${keyId}`);
      if (!keyDataStr) {
        this.logger.warn({ keyId }, 'API key data not found');
        return null;
      }
      
      const keyData: ApiKeyData = JSON.parse(keyDataStr);
      
      // Check if key is active
      if (!keyData.isActive) {
        this.logger.debug({ keyId }, 'API key is inactive');
        return null;
      }
      
      // Check if key is expired
      if (keyData.expiresAt && new Date() > new Date(keyData.expiresAt)) {
        this.logger.debug({ keyId }, 'API key is expired');
        await this.deactivateApiKey(keyId);
        return null;
      }
      
      // Update last used timestamp and usage count
      await this.updateUsage(keyId);
      
      return keyData;
    } catch (error) {
      this.logger.error({ error }, 'Error validating API key');
      return null;
    }
  }
  
  async checkPermission(keyData: ApiKeyData, permission: string): Promise<boolean> {
    return keyData.permissions.includes(permission) || keyData.permissions.includes('*');
  }
  
  async checkRateLimit(keyData: ApiKeyData, ip: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const window = Math.floor(Date.now() / windowMs);
    const key = `rate-limit:${keyData.keyId}:${window}`;
    
    const current = await this.redis.incr(key);
    if (current === 1) {
      await this.redis.expire(key, Math.ceil(windowMs / 1000));
    }
    
    const remaining = Math.max(0, keyData.rateLimit - current);
    const resetTime = (window + 1) * windowMs;
    
    if (current > keyData.rateLimit) {
      this.logger.warn({ keyId: keyData.keyId, current, limit: keyData.rateLimit }, 'API key rate limit exceeded');
      
      // Record rate limit violation
      await this.recordUsage(keyData.keyId, {
        timestamp: new Date(),
        endpoint: 'rate-limit-exceeded',
        method: 'N/A',
        statusCode: 429,
        responseTime: 0,
        ip,
        userAgent: 'N/A',
      });
      
      return { allowed: false, remaining: 0, resetTime };
    }
    
    return { allowed: true, remaining, resetTime };
  }
  
  async listApiKeys(userId?: string, organizationId?: string): Promise<Omit<ApiKeyData, 'hashedKey'>[]> {
    let keyIds: string[] = [];
    
    if (userId) {
      keyIds = await this.redis.smembers(`user-keys:${userId}`);
    } else if (organizationId) {
      keyIds = await this.redis.smembers(`org-keys:${organizationId}`);
    } else {
      // Get all keys (admin only)
      const keys = await this.redis.keys('api-key:*');
      keyIds = keys.map(key => key.replace('api-key:', ''));
    }
    
    const keys: Omit<ApiKeyData, 'hashedKey'>[] = [];
    
    for (const keyId of keyIds) {
      const keyDataStr = await this.redis.get(`api-key:${keyId}`);
      if (keyDataStr) {
        const keyData: ApiKeyData = JSON.parse(keyDataStr);
        const { hashedKey, ...safeKeyData } = keyData;
        keys.push(safeKeyData);
      }
    }
    
    return keys.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
  
  async revokeApiKey(keyId: string): Promise<void> {
    const keyDataStr = await this.redis.get(`api-key:${keyId}`);
    if (!keyDataStr) {
      throw new Error('API key not found');
    }
    
    const keyData: ApiKeyData = JSON.parse(keyDataStr);
    
    // Remove from Redis
    await this.redis.del(`api-key:${keyId}`);
    await this.redis.del(`api-key-hash:${keyData.hashedKey}`);
    
    // Remove from user/org lists
    if (keyData.userId) {
      await this.redis.srem(`user-keys:${keyData.userId}`, keyId);
    }
    if (keyData.organizationId) {
      await this.redis.srem(`org-keys:${keyData.organizationId}`, keyId);
    }
    
    // Archive the key data for audit purposes
    await this.redis.setex(
      `api-key-revoked:${keyId}`,
      2592000, // 30 days
      JSON.stringify({
        ...keyData,
        revokedAt: new Date().toISOString(),
      })
    );
    
    this.logger.info({ keyId, name: keyData.name }, 'API key revoked');
  }
  
  async deactivateApiKey(keyId: string): Promise<void> {
    const keyDataStr = await this.redis.get(`api-key:${keyId}`);
    if (!keyDataStr) {
      return;
    }
    
    const keyData: ApiKeyData = JSON.parse(keyDataStr);
    keyData.isActive = false;
    
    await this.redis.set(`api-key:${keyId}`, JSON.stringify(keyData));
    
    this.logger.info({ keyId }, 'API key deactivated');
  }
  
  async updateUsage(keyId: string): Promise<void> {
    const keyDataStr = await this.redis.get(`api-key:${keyId}`);
    if (!keyDataStr) {
      return;
    }
    
    const keyData: ApiKeyData = JSON.parse(keyDataStr);
    keyData.lastUsed = new Date();
    keyData.usageCount += 1;
    
    await this.redis.set(`api-key:${keyId}`, JSON.stringify(keyData));
  }
  
  async recordUsage(keyId: string, usage: ApiKeyUsage): Promise<void> {
    const usageKey = `api-key-usage:${keyId}`;
    await this.redis.lpush(usageKey, JSON.stringify(usage));
    await this.redis.ltrim(usageKey, 0, 999); // Keep last 1000 usage records
    await this.redis.expire(usageKey, 2592000); // 30 days
  }
  
  async getUsageStats(keyId: string, days: number = 7): Promise<{
    totalRequests: number;
    requestsByDay: Array<{ date: string; count: number }>;
    requestsByEndpoint: Array<{ endpoint: string; count: number }>;
    errorRate: number;
    averageResponseTime: number;
  }> {
    const usageKey = `api-key-usage:${keyId}`;
    const usageData = await this.redis.lrange(usageKey, 0, -1);
    
    const usages: ApiKeyUsage[] = usageData
      .map(data => JSON.parse(data))
      .filter(usage => {
        const usageDate = new Date(usage.timestamp);
        const cutoff = new Date();
        cutoff.setDate(cutoff.getDate() - days);
        return usageDate >= cutoff;
      });
    
    const totalRequests = usages.length;
    const errors = usages.filter(u => u.statusCode >= 400).length;
    const errorRate = totalRequests > 0 ? errors / totalRequests : 0;
    const averageResponseTime = totalRequests > 0 
      ? usages.reduce((sum, u) => sum + u.responseTime, 0) / totalRequests 
      : 0;
    
    // Group by day
    const requestsByDay: Map<string, number> = new Map();
    usages.forEach(usage => {
      const date = new Date(usage.timestamp).toISOString().split('T')[0];
      requestsByDay.set(date, (requestsByDay.get(date) || 0) + 1);
    });
    
    // Group by endpoint
    const requestsByEndpoint: Map<string, number> = new Map();
    usages.forEach(usage => {
      requestsByEndpoint.set(usage.endpoint, (requestsByEndpoint.get(usage.endpoint) || 0) + 1);
    });
    
    return {
      totalRequests,
      requestsByDay: Array.from(requestsByDay.entries()).map(([date, count]) => ({ date, count })),
      requestsByEndpoint: Array.from(requestsByEndpoint.entries())
        .map(([endpoint, count]) => ({ endpoint, count }))
        .sort((a, b) => b.count - a.count),
      errorRate,
      averageResponseTime,
    };
  }
  
  private generateSecureKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }
  
  private hashApiKey(apiKey: string): string {
    return crypto.createHmac('sha256', this.secretKey).update(apiKey).digest('hex');
  }
  
  // Bulk operations
  async revokeAllUserKeys(userId: string): Promise<void> {
    const keyIds = await this.redis.smembers(`user-keys:${userId}`);
    
    for (const keyId of keyIds) {
      await this.revokeApiKey(keyId);
    }
    
    this.logger.info({ userId, count: keyIds.length }, 'All user API keys revoked');
  }
  
  async revokeAllOrgKeys(organizationId: string): Promise<void> {
    const keyIds = await this.redis.smembers(`org-keys:${organizationId}`);
    
    for (const keyId of keyIds) {
      await this.revokeApiKey(keyId);
    }
    
    this.logger.info({ organizationId, count: keyIds.length }, 'All organization API keys revoked');
  }
  
  // Cleanup expired keys
  async cleanupExpiredKeys(): Promise<void> {
    const keys = await this.redis.keys('api-key:*');
    let cleanedUp = 0;
    
    for (const key of keys) {
      const keyDataStr = await this.redis.get(key);
      if (keyDataStr) {
        const keyData: ApiKeyData = JSON.parse(keyDataStr);
        if (keyData.expiresAt && new Date() > new Date(keyData.expiresAt)) {
          await this.deactivateApiKey(keyData.keyId);
          cleanedUp++;
        }
      }
    }
    
    this.logger.info({ cleanedUp }, 'Expired API keys cleaned up');
  }
}