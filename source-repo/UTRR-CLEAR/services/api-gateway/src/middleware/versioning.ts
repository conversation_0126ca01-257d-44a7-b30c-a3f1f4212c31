import { FastifyRequest, FastifyReply } from 'fastify';
import { Logger } from 'pino';

interface VersionConfig {
  version: string;
  status: 'active' | 'deprecated' | 'sunset';
  deprecationDate?: Date;
  sunsetDate?: Date;
  supportedUntil?: Date;
  migrationGuide?: string;
  changelog?: string;
}

interface VersionMapping {
  from: string;
  to: string;
  transformer?: (data: any) => any;
  compatibilityLevel: 'full' | 'partial' | 'breaking';
}

export class ApiVersionManager {
  private logger: Logger;
  private versions: Map<string, VersionConfig>;
  private mappings: Map<string, VersionMapping>;
  private currentVersion: string;
  
  constructor(logger: Logger) {
    this.logger = logger;
    this.versions = new Map();
    this.mappings = new Map();
    this.currentVersion = 'v1';
    
    this.initializeVersions();
    this.initializeMappings();
  }
  
  private initializeVersions(): void {
    // Version 1.0 - Current stable
    this.addVersion({
      version: 'v1',
      status: 'active',
      supportedUntil: new Date('2025-12-31'),
      changelog: 'Initial stable release with core functionality',
    });
    
    // Version 1.1 - In development
    this.addVersion({
      version: 'v1.1',
      status: 'active',
      changelog: 'Enhanced spatial analysis and improved performance',
    });
    
    // Version 2.0 - Future version
    this.addVersion({
      version: 'v2',
      status: 'active',
      changelog: 'Major API redesign with improved consistency and new features',
    });
    
    // Legacy versions
    this.addVersion({
      version: 'legacy',
      status: 'deprecated',
      deprecationDate: new Date('2024-01-01'),
      sunsetDate: new Date('2024-12-31'),
      migrationGuide: 'https://docs.clear.example.com/migration/legacy-to-v1',
      changelog: 'Original API version - deprecated in favor of v1',
    });
  }
  
  private initializeMappings(): void {
    // Legacy to v1 mapping
    this.addMapping({
      from: 'legacy',
      to: 'v1',
      compatibilityLevel: 'partial',
      transformer: this.legacyToV1Transformer,
    });
    
    // v1 to v1.1 mapping (backward compatible)
    this.addMapping({
      from: 'v1',
      to: 'v1.1',
      compatibilityLevel: 'full',
    });
  }
  
  addVersion(config: VersionConfig): void {
    this.versions.set(config.version, config);
    this.logger.info({ version: config.version, status: config.status }, 'API version registered');
  }
  
  addMapping(mapping: VersionMapping): void {
    const key = `${mapping.from}->${mapping.to}`;
    this.mappings.set(key, mapping);
    this.logger.info({ from: mapping.from, to: mapping.to }, 'Version mapping registered');
  }
  
  async handleVersioning(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const requestedVersion = this.extractVersion(request);
    const config = this.versions.get(requestedVersion);
    
    if (!config) {
      reply.code(400).send({
        error: {
          code: 400,
          type: 'invalid_version',
          message: `Unsupported API version: ${requestedVersion}`,
          supportedVersions: Array.from(this.versions.keys()),
        },
      });
      return;
    }
    
    // Check if version is sunset
    if (config.status === 'sunset' || (config.sunsetDate && new Date() > config.sunsetDate)) {
      reply.code(410).send({
        error: {
          code: 410,
          type: 'version_sunset',
          message: `API version ${requestedVersion} has been sunset`,
          migrationGuide: config.migrationGuide,
          recommendedVersion: this.currentVersion,
        },
      });
      return;
    }
    
    // Add deprecation warnings
    if (config.status === 'deprecated') {
      reply.header('Warning', `299 - "API version ${requestedVersion} is deprecated"`);
      reply.header('Sunset', config.sunsetDate?.toISOString() || '');
      reply.header('Link', `<${config.migrationGuide}>; rel="successor-version"`);
    }
    
    // Add version headers
    reply.header('API-Version', requestedVersion);
    reply.header('API-Supported-Versions', Array.from(this.versions.keys()).join(', '));
    
    // Store version info in request for later use
    (request as any).apiVersion = requestedVersion;
    (request as any).versionConfig = config;
  }
  
  private extractVersion(request: FastifyRequest): string {
    // Try different methods to extract version
    
    // 1. From URL path (e.g., /api/v1/projects)
    const pathMatch = request.url.match(/^\/api\/(v\d+(?:\.\d+)?)\//);
    if (pathMatch) {
      return pathMatch[1];
    }
    
    // 2. From Accept header (e.g., application/vnd.clear.v1+json)
    const acceptHeader = request.headers.accept;
    if (acceptHeader) {
      const versionMatch = acceptHeader.match(/application\/vnd\.clear\.(v\d+(?:\.\d+)?)\+json/);
      if (versionMatch) {
        return versionMatch[1];
      }
    }
    
    // 3. From custom header
    const versionHeader = request.headers['api-version'] as string;
    if (versionHeader) {
      return versionHeader;
    }
    
    // 4. From query parameter
    const versionQuery = (request.query as any)?.version;
    if (versionQuery) {
      return versionQuery;
    }
    
    // 5. Default to current version
    return this.currentVersion;
  }
  
  async transformResponse(
    request: FastifyRequest,
    reply: FastifyReply,
    data: any
  ): Promise<any> {
    const requestedVersion = (request as any).apiVersion || this.currentVersion;
    const currentVersion = this.currentVersion;
    
    // If requesting the current version, return as-is
    if (requestedVersion === currentVersion) {
      return data;
    }
    
    // Check if we need to transform from current to requested version
    const mappingKey = `${currentVersion}->${requestedVersion}`;
    const reverseMapping = `${requestedVersion}->${currentVersion}`;
    
    let mapping = this.mappings.get(mappingKey);
    let isReverse = false;
    
    if (!mapping) {
      mapping = this.mappings.get(reverseMapping);
      isReverse = true;
    }
    
    if (!mapping) {
      this.logger.warn({ 
        requestedVersion, 
        currentVersion 
      }, 'No version mapping found, returning data as-is');
      return data;
    }
    
    // Apply transformation if available
    if (mapping.transformer) {
      try {
        const transformed = isReverse 
          ? this.reverseTransform(data, mapping.transformer)
          : mapping.transformer(data);
        
        this.logger.debug({ 
          from: isReverse ? currentVersion : requestedVersion,
          to: isReverse ? requestedVersion : currentVersion 
        }, 'Response transformed for version compatibility');
        
        return transformed;
      } catch (error) {
        this.logger.error({ error, requestedVersion, currentVersion }, 'Version transformation failed');
        return data; // Return original data on transformation error
      }
    }
    
    return data;
  }
  
  private legacyToV1Transformer = (data: any): any => {
    if (Array.isArray(data)) {
      return data.map(item => this.legacyToV1Transformer(item));
    }
    
    if (data && typeof data === 'object') {
      const transformed = { ...data };
      
      // Convert snake_case to camelCase
      Object.keys(transformed).forEach(key => {
        if (key.includes('_')) {
          const camelKey = key.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
          transformed[camelKey] = transformed[key];
          delete transformed[key];
        }
      });
      
      // Legacy field mappings
      if (transformed.project_id) {
        transformed.projectId = transformed.project_id;
        delete transformed.project_id;
      }
      
      if (transformed.user_id) {
        transformed.userId = transformed.user_id;
        delete transformed.user_id;
      }
      
      if (transformed.created_date) {
        transformed.createdAt = transformed.created_date;
        delete transformed.created_date;
      }
      
      if (transformed.modified_date) {
        transformed.updatedAt = transformed.modified_date;
        delete transformed.modified_date;
      }
      
      // Convert legacy status values
      if (transformed.status === 'in_progress') {
        transformed.status = 'active';
      }
      
      return transformed;
    }
    
    return data;
  };
  
  private reverseTransform(data: any, transformer: (data: any) => any): any {
    // Simple reverse transformation - convert camelCase back to snake_case
    if (Array.isArray(data)) {
      return data.map(item => this.reverseTransform(item, transformer));
    }
    
    if (data && typeof data === 'object') {
      const transformed = { ...data };
      
      // Convert camelCase to snake_case
      Object.keys(transformed).forEach(key => {
        if (/[A-Z]/.test(key)) {
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          transformed[snakeKey] = transformed[key];
          delete transformed[key];
        }
      });
      
      return transformed;
    }
    
    return data;
  }
  
  getVersionInfo(version?: string): VersionConfig | null {
    const versionKey = version || this.currentVersion;
    return this.versions.get(versionKey) || null;
  }
  
  getAllVersions(): Array<VersionConfig & { version: string }> {
    return Array.from(this.versions.entries()).map(([version, config]) => ({
      version,
      ...config,
    }));
  }
  
  getCompatibilityInfo(fromVersion: string, toVersion: string): {
    compatible: boolean;
    level?: 'full' | 'partial' | 'breaking';
    migrationRequired: boolean;
    migrationGuide?: string;
  } {
    const directMapping = this.mappings.get(`${fromVersion}->${toVersion}`);
    const reverseMapping = this.mappings.get(`${toVersion}->${fromVersion}`);
    
    if (directMapping) {
      return {
        compatible: true,
        level: directMapping.compatibilityLevel,
        migrationRequired: directMapping.compatibilityLevel === 'breaking',
      };
    }
    
    if (reverseMapping) {
      return {
        compatible: true,
        level: reverseMapping.compatibilityLevel,
        migrationRequired: reverseMapping.compatibilityLevel === 'breaking',
      };
    }
    
    // Check if both versions exist
    const fromConfig = this.versions.get(fromVersion);
    const toConfig = this.versions.get(toVersion);
    
    if (!fromConfig || !toConfig) {
      return {
        compatible: false,
        migrationRequired: true,
      };
    }
    
    // If no direct mapping exists, assume breaking change
    return {
      compatible: false,
      migrationRequired: true,
      migrationGuide: fromConfig.migrationGuide,
    };
  }
  
  // Deprecation management
  deprecateVersion(version: string, sunsetDate: Date, migrationGuide?: string): void {
    const config = this.versions.get(version);
    if (config) {
      config.status = 'deprecated';
      config.deprecationDate = new Date();
      config.sunsetDate = sunsetDate;
      if (migrationGuide) {
        config.migrationGuide = migrationGuide;
      }
      
      this.versions.set(version, config);
      this.logger.info({ version, sunsetDate }, 'API version deprecated');
    }
  }
  
  sunsetVersion(version: string): void {
    const config = this.versions.get(version);
    if (config) {
      config.status = 'sunset';
      this.versions.set(version, config);
      this.logger.info({ version }, 'API version sunset');
    }
  }
  
  // Health check for version management
  getVersionHealth(): {
    currentVersion: string;
    totalVersions: number;
    activeVersions: number;
    deprecatedVersions: number;
    sunsetVersions: number;
    upcomingSunsets: Array<{ version: string; sunsetDate: Date }>;
  } {
    const versions = Array.from(this.versions.entries());
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    return {
      currentVersion: this.currentVersion,
      totalVersions: versions.length,
      activeVersions: versions.filter(([, config]) => config.status === 'active').length,
      deprecatedVersions: versions.filter(([, config]) => config.status === 'deprecated').length,
      sunsetVersions: versions.filter(([, config]) => config.status === 'sunset').length,
      upcomingSunsets: versions
        .filter(([, config]) => 
          config.sunsetDate && 
          config.sunsetDate > now && 
          config.sunsetDate <= thirtyDaysFromNow
        )
        .map(([version, config]) => ({ 
          version, 
          sunsetDate: config.sunsetDate! 
        })),
    };
  }
}