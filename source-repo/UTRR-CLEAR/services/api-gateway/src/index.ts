import Fastify from 'fastify';
import fastifyCors from '@fastify/cors';
import fastifyHelmet from '@fastify/helmet';
import fastifyRateLimit from '@fastify/rate-limit';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUi from '@fastify/swagger-ui';
import fastifyJWT from '@fastify/jwt';
import fastifyAuth from '@fastify/auth';
import Redis from 'ioredis';
import { z } from 'zod';
import pino from 'pino';
import axios from 'axios';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import 'dotenv/config';

// Import custom modules
import { SecurityMiddleware } from './middleware/security';
import { ApiKeyManager } from './middleware/api-keys';
import { RequestTransformer } from './middleware/transformers';
import { CircuitBreaker } from './middleware/circuit-breaker';
import { MetricsCollector } from './middleware/metrics';

// Environment schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('8003'),
  REDIS_URL: z.string(),
  JWT_SECRET: z.string(),
  API_KEY_SECRET: z.string(),
  MAIN_APP_URL: z.string().default('http://app:3000'),
  SPATIAL_SERVICE_URL: z.string().default('http://spatial-processor:8001'),
  WEBSOCKET_SERVICE_URL: z.string().default('http://websocket-server:8002'),
  LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error']).default('info'),
  RATE_LIMIT_MAX: z.string().default('1000'),
  RATE_LIMIT_WINDOW: z.string().default('900'), // 15 minutes
});

const env = envSchema.parse(process.env);

// Logger configuration
const logger = pino({
  level: env.LOG_LEVEL,
  transport: env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'HH:MM:ss Z',
      ignore: 'pid,hostname',
    },
  } : undefined,
});

// Redis client for rate limiting and caching
const redis = new Redis(env.REDIS_URL, {
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
});

// Fastify server
const fastify = Fastify({
  logger,
  trustProxy: true,
  requestIdLogLabel: 'requestId',
  requestIdHeader: 'x-request-id',
  genReqId: () => uuidv4(),
});

// Initialize middleware components
const securityMiddleware = new SecurityMiddleware(redis, logger);
const apiKeyManager = new ApiKeyManager(redis, env.API_KEY_SECRET, logger);
const requestTransformer = new RequestTransformer(logger);
const circuitBreaker = new CircuitBreaker(redis, logger);
const metricsCollector = new MetricsCollector(redis, logger);

// Register core plugins
fastify.register(fastifyHelmet, {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
});

fastify.register(fastifyCors, {
  origin: (origin, callback) => {
    // Allow requests without origin (e.g., mobile apps, Postman)
    if (!origin) return callback(null, true);
    
    // Allow localhost for development
    if (env.NODE_ENV === 'development' && origin.includes('localhost')) {
      return callback(null, true);
    }
    
    // Add your allowed domains here
    const allowedOrigins = [
      'https://clear.example.com',
      'https://app.clear.example.com',
    ];
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-Request-ID'],
});

// JWT authentication
fastify.register(fastifyJWT, {
  secret: env.JWT_SECRET,
  sign: {
    expiresIn: '8h',
  },
  verify: {
    maxAge: '8h',
  },
});

fastify.register(fastifyAuth);

// Rate limiting with Redis
fastify.register(fastifyRateLimit, {
  max: parseInt(env.RATE_LIMIT_MAX),
  timeWindow: parseInt(env.RATE_LIMIT_WINDOW) * 1000,
  redis: redis,
  nameSpace: 'api-gateway:rate-limit:',
  addHeaders: {
    'x-ratelimit-limit': true,
    'x-ratelimit-remaining': true,
    'x-ratelimit-reset': true,
  },
  keyGenerator: (request) => {
    // Use API key if available, otherwise IP
    const apiKey = request.headers['x-api-key'] as string;
    if (apiKey) {
      return `api-key:${crypto.createHash('sha256').update(apiKey).digest('hex')}`;
    }
    return `ip:${request.ip}`;
  },
  errorResponseBuilder: (request, context) => {
    return {
      error: 'Rate limit exceeded',
      message: `Too many requests. Limit: ${context.max} per ${context.timeWindow}ms`,
      retryAfter: context.ttl,
      requestId: request.id,
    };
  },
});

// OpenAPI/Swagger documentation
fastify.register(fastifySwagger, {
  openapi: {
    info: {
      title: 'CLEAR API Gateway',
      description: 'Enterprise API Gateway for CLEAR utility infrastructure management platform',
      version: '1.0.0',
      contact: {
        name: 'CLEAR Support',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    externalDocs: {
      url: 'https://docs.clear.example.com',
      description: 'Find more info here',
    },
    servers: [
      {
        url: 'https://api.clear.example.com',
        description: 'Production server',
      },
      {
        url: 'http://localhost:8003',
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        Bearer: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        ApiKey: {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
        },
      },
    },
    security: [
      { Bearer: [] },
      { ApiKey: [] },
    ],
  },
  hideUntagged: true,
});

fastify.register(fastifySwaggerUi, {
  routePrefix: '/docs',
  uiConfig: {
    docExpansion: 'list',
    deepLinking: false,
    displayRequestDuration: true,
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
});

// Authentication decorators
fastify.decorate('authenticate', async function(request: any, reply: any) {
  try {
    await request.jwtVerify();
  } catch (err) {
    reply.code(401).send({ error: 'Unauthorized', message: 'Invalid or expired token' });
  }
});

fastify.decorate('authenticateApiKey', async function(request: any, reply: any) {
  const apiKey = request.headers['x-api-key'] as string;
  
  if (!apiKey) {
    reply.code(401).send({ error: 'Unauthorized', message: 'API key required' });
    return;
  }
  
  try {
    const keyData = await apiKeyManager.validateApiKey(apiKey);
    if (!keyData) {
      reply.code(401).send({ error: 'Unauthorized', message: 'Invalid API key' });
      return;
    }
    
    // Add key data to request
    request.apiKey = keyData;
  } catch (error) {
    reply.code(401).send({ error: 'Unauthorized', message: 'API key validation failed' });
  }
});

// Middleware registration
fastify.addHook('onRequest', async (request, reply) => {
  // Security middleware
  await securityMiddleware.checkSecurity(request, reply);
  
  // Request transformation
  await requestTransformer.transformRequest(request, reply);
  
  // Metrics collection
  await metricsCollector.collectRequestMetrics(request);
});

fastify.addHook('onResponse', async (request, reply) => {
  // Response transformation
  await requestTransformer.transformResponse(request, reply);
  
  // Metrics collection
  await metricsCollector.collectResponseMetrics(request, reply);
});

// Health check endpoint
fastify.get('/health', {
  schema: {
    description: 'Health check endpoint',
    tags: ['System'],
    response: {
      200: {
        type: 'object',
        properties: {
          status: { type: 'string' },
          timestamp: { type: 'string' },
          services: { type: 'object' },
          version: { type: 'string' },
        },
      },
    },
  },
}, async (request, reply) => {
  try {
    // Check Redis connection
    await redis.ping();
    
    // Check upstream services
    const services = {
      redis: 'connected',
      mainApp: 'unknown',
      spatialProcessor: 'unknown',
      websocketServer: 'unknown',
    };
    
    // Test upstream services with circuit breaker
    try {
      await circuitBreaker.execute('main-app', () => 
        axios.get(`${env.MAIN_APP_URL}/api/health`, { timeout: 5000 })
      );
      services.mainApp = 'connected';
    } catch (error) {
      services.mainApp = 'disconnected';
    }
    
    try {
      await circuitBreaker.execute('spatial-processor', () =>
        axios.get(`${env.SPATIAL_SERVICE_URL}/health`, { timeout: 5000 })
      );
      services.spatialProcessor = 'connected';
    } catch (error) {
      services.spatialProcessor = 'disconnected';
    }
    
    try {
      await circuitBreaker.execute('websocket-server', () =>
        axios.get(`${env.WEBSOCKET_SERVICE_URL}/health`, { timeout: 5000 })
      );
      services.websocketServer = 'connected';
    } catch (error) {
      services.websocketServer = 'disconnected';
    }
    
    const allHealthy = Object.values(services).every(status => status === 'connected');
    
    return {
      status: allHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      services,
      version: '1.0.0',
    };
  } catch (error) {
    reply.code(503);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
});

// Metrics endpoint
fastify.get('/metrics', {
  schema: {
    description: 'Prometheus metrics endpoint',
    tags: ['System'],
  },
}, async (request, reply) => {
  const metrics = await metricsCollector.getPrometheusMetrics();
  reply.header('Content-Type', 'text/plain');
  return metrics;
});

// API Key management endpoints
fastify.register(async function(fastify) {
  // Generate new API key
  fastify.post('/admin/api-keys', {
    preHandler: fastify.authenticate,
    schema: {
      description: 'Generate a new API key',
      tags: ['Admin'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['name', 'permissions'],
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100 },
          permissions: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
          },
          expiresAt: { type: 'string', format: 'date-time' },
          rateLimit: { type: 'number', minimum: 1, maximum: 10000 },
        },
      },
      response: {
        201: {
          type: 'object',
          properties: {
            keyId: { type: 'string' },
            apiKey: { type: 'string' },
            name: { type: 'string' },
            permissions: { type: 'array', items: { type: 'string' } },
            createdAt: { type: 'string' },
            expiresAt: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { name, permissions, expiresAt, rateLimit } = request.body as any;
    
    const keyData = await apiKeyManager.generateApiKey({
      name,
      permissions,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      rateLimit: rateLimit || 1000,
      userId: (request.user as any)?.id,
    });
    
    reply.code(201);
    return keyData;
  });
  
  // List API keys
  fastify.get('/admin/api-keys', {
    preHandler: fastify.authenticate,
    schema: {
      description: 'List API keys',
      tags: ['Admin'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            keys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  keyId: { type: 'string' },
                  name: { type: 'string' },
                  permissions: { type: 'array', items: { type: 'string' } },
                  createdAt: { type: 'string' },
                  expiresAt: { type: 'string' },
                  lastUsed: { type: 'string' },
                  usageCount: { type: 'number' },
                  isActive: { type: 'boolean' },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const keys = await apiKeyManager.listApiKeys((request.user as any)?.id);
    return { keys };
  });
  
  // Revoke API key
  fastify.delete('/admin/api-keys/:keyId', {
    preHandler: fastify.authenticate,
    schema: {
      description: 'Revoke an API key',
      tags: ['Admin'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          keyId: { type: 'string' },
        },
        required: ['keyId'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { keyId } = request.params as any;
    
    await apiKeyManager.revokeApiKey(keyId);
    
    return {
      success: true,
      message: 'API key revoked successfully',
    };
  });
});

// Proxy routes to main application
fastify.register(async function(fastify) {
  // Public API routes (with API key authentication)
  fastify.all('/api/v1/*', {
    preHandler: fastify.authenticateApiKey,
  }, async (request, reply) => {
    const path = (request.params as any)['*'];
    const targetUrl = `${env.MAIN_APP_URL}/api/${path}`;
    
    return circuitBreaker.execute('main-app', async () => {
      const response = await axios({
        method: request.method.toLowerCase() as any,
        url: targetUrl,
        data: request.body,
        headers: {
          ...request.headers,
          'x-forwarded-for': request.ip,
          'x-request-id': request.id,
          'x-api-key-id': (request as any).apiKey?.keyId,
        },
        timeout: 30000,
      });
      
      reply.code(response.status);
      Object.entries(response.headers).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          reply.header(key, value);
        }
      });
      
      return response.data;
    });
  });
  
  // Internal API routes (with JWT authentication)
  fastify.all('/api/internal/*', {
    preHandler: fastify.authenticate,
  }, async (request, reply) => {
    const path = (request.params as any)['*'];
    const targetUrl = `${env.MAIN_APP_URL}/api/${path}`;
    
    return circuitBreaker.execute('main-app', async () => {
      const response = await axios({
        method: request.method.toLowerCase() as any,
        url: targetUrl,
        data: request.body,
        headers: {
          ...request.headers,
          'x-forwarded-for': request.ip,
          'x-request-id': request.id,
          'x-user-id': (request.user as any)?.id,
        },
        timeout: 30000,
      });
      
      reply.code(response.status);
      Object.entries(response.headers).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          reply.header(key, value);
        }
      });
      
      return response.data;
    });
  });
  
  // Spatial processing routes
  fastify.all('/api/spatial/*', {
    preHandler: [fastify.authenticateApiKey],
  }, async (request, reply) => {
    const path = (request.params as any)['*'];
    const targetUrl = `${env.SPATIAL_SERVICE_URL}/spatial/${path}`;
    
    return circuitBreaker.execute('spatial-processor', async () => {
      const response = await axios({
        method: request.method.toLowerCase() as any,
        url: targetUrl,
        data: request.body,
        headers: {
          ...request.headers,
          'x-forwarded-for': request.ip,
          'x-request-id': request.id,
          'x-api-key-id': (request as any).apiKey?.keyId,
        },
        timeout: 120000, // 2 minutes for spatial operations
      });
      
      reply.code(response.status);
      Object.entries(response.headers).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          reply.header(key, value);
        }
      });
      
      return response.data;
    });
  });
});

// Security monitoring endpoints
fastify.get('/admin/security/blocked-ips', {
  preHandler: fastify.authenticate,
  schema: {
    description: 'List blocked IP addresses',
    tags: ['Security'],
    security: [{ Bearer: [] }],
  },
}, async (request, reply) => {
  const blockedIPs = await securityMiddleware.getBlockedIPs();
  return { blockedIPs };
});

fastify.post('/admin/security/block-ip', {
  preHandler: fastify.authenticate,
  schema: {
    description: 'Block an IP address',
    tags: ['Security'],
    security: [{ Bearer: [] }],
    body: {
      type: 'object',
      required: ['ip'],
      properties: {
        ip: { type: 'string', format: 'ipv4' },
        reason: { type: 'string' },
        duration: { type: 'number', minimum: 1 },
      },
    },
  },
}, async (request, reply) => {
  const { ip, reason, duration } = request.body as any;
  
  await securityMiddleware.blockIP(ip, reason, duration);
  
  return {
    success: true,
    message: `IP ${ip} blocked successfully`,
  };
});

// Error handling
fastify.setErrorHandler(async (error, request, reply) => {
  logger.error({ error, requestId: request.id }, 'Request error');
  
  if (error.validation) {
    reply.code(400).send({
      error: 'Validation Error',
      message: 'Invalid request data',
      details: error.validation,
      requestId: request.id,
    });
    return;
  }
  
  if (error.statusCode) {
    reply.code(error.statusCode).send({
      error: error.name || 'Error',
      message: error.message,
      requestId: request.id,
    });
    return;
  }
  
  reply.code(500).send({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    requestId: request.id,
  });
});

// Graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Shutting down API Gateway gracefully...');
  
  try {
    await fastify.close();
    redis.disconnect();
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error({ error }, 'Error during shutdown');
    process.exit(1);
  }
};

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: parseInt(env.PORT), host: '0.0.0.0' });
    logger.info(`🛡️ API Gateway running on port ${env.PORT}`);
    logger.info(`📖 API Documentation available at http://localhost:${env.PORT}/docs`);
  } catch (error) {
    logger.error({ error }, 'Failed to start server');
    process.exit(1);
  }
};

start();