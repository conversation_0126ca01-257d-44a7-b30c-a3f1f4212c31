{"name": "clear-api-gateway", "version": "1.0.0", "description": "Enterprise API Gateway for CLEAR utility infrastructure management", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"fastify": "^4.24.3", "fastify-cors": "^6.1.0", "fastify-helmet": "^11.1.1", "fastify-rate-limit": "^9.1.0", "fastify-swagger": "^8.14.0", "fastify-swagger-ui": "^2.1.0", "fastify-plugin": "^4.5.1", "fastify-jwt": "^7.2.4", "fastify-auth": "^1.2.0", "fastify-bearer-auth": "^9.2.0", "ioredis": "^5.3.2", "zod": "^3.22.4", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "dotenv": "^16.3.1", "axios": "^1.6.2", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "crypto": "^1.0.1", "node-cache": "^5.1.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-slow-down": "^2.0.1", "node-geoip": "^1.5.1", "maxmind": "^4.3.6"}, "devDependencies": {"@types/node": "^20.8.10", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "typescript": "^5.2.2", "tsx": "^4.1.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0"}, "engines": {"node": ">=20.0.0"}}