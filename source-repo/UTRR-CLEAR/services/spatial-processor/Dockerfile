# Production Dockerfile for Spatial Processor Service

FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:20-alpine AS builder
WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

FROM node:20-alpine AS production
RUN apk add --no-cache dumb-init curl

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 spatialproc

WORKDIR /app

COPY --from=builder --chown=spatialproc:nodejs /app/dist ./dist
COPY --from=deps --chown=spatialproc:nodejs /app/node_modules ./node_modules
COPY --chown=spatialproc:nodejs package*.json ./

USER spatialproc

EXPOSE 8001

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:8001/health || exit 1

CMD ["dumb-init", "node", "dist/index.js"]