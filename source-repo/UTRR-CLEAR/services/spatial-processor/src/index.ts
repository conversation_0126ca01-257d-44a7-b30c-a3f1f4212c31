import Fastify from 'fastify';
import fastifyCors from '@fastify/cors';
import fastifyHelmet from '@fastify/helmet';
import fastifyRateLimit from '@fastify/rate-limit';
import { Pool } from 'pg';
import Redis from 'ioredis';
import { z } from 'zod';
import pino from 'pino';
import 'dotenv/config';

// Environment schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('8001'),
  DATABASE_URL: z.string(),
  REDIS_URL: z.string().optional(),
  LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error']).default('info'),
});

const env = envSchema.parse(process.env);

// Logger configuration
const logger = pino({
  level: env.LOG_LEVEL,
  transport: env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'HH:MM:ss Z',
      ignore: 'pid,hostname',
    },
  } : undefined,
});

// Database pool
const dbPool = new Pool({
  connectionString: env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
});

// Redis client
const redis = env.REDIS_URL ? new Redis(env.REDIS_URL, {
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
}) : null;

// Fastify server
const fastify = Fastify({
  logger,
  trustProxy: true,
});

// Register plugins
fastify.register(fastifyHelmet, {
  contentSecurityPolicy: false,
});

fastify.register(fastifyCors, {
  origin: true,
  credentials: true,
});

fastify.register(fastifyRateLimit, {
  max: 100,
  timeWindow: '1 minute',
  redis: redis || undefined,
});

// Schema definitions
const ConflictDetectionSchema = z.object({
  projectId: z.string().uuid(),
  threshold: z.number().min(0.1).max(100).default(3.0),
  detectCrossings: z.boolean().default(true),
  detectProximity: z.boolean().default(true),
  includeProposed: z.boolean().default(false),
  sensitivityLevel: z.enum(['low', 'medium', 'high']).default('medium'),
});

const SpatialQuerySchema = z.object({
  projectId: z.string().uuid(),
  operation: z.enum(['buffer', 'intersection', 'union', 'difference', 'contains', 'within']),
  geometries: z.array(z.any()),
  parameters: z.record(z.any()).optional(),
});

const GeometryValidationSchema = z.object({
  geometry: z.any(),
  type: z.enum(['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon']).optional(),
});

// Routes

// Health check
fastify.get('/health', async (request, reply) => {
  try {
    // Check database connection
    const dbResult = await dbPool.query('SELECT 1');
    
    // Check Redis connection if available
    let redisStatus = 'not_configured';
    if (redis) {
      try {
        await redis.ping();
        redisStatus = 'connected';
      } catch (error) {
        redisStatus = 'disconnected';
      }
    }

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbResult.rows.length > 0 ? 'connected' : 'disconnected',
        redis: redisStatus,
      },
      version: '1.0.0',
    };
  } catch (error) {
    reply.code(503);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
});

// Enhanced conflict detection endpoint with materialized view support
fastify.post<{
  Body: z.infer<typeof ConflictDetectionSchema>
}>('/spatial/conflicts', {
  schema: {
    body: ConflictDetectionSchema,
  },
}, async (request, reply) => {
  const { projectId, threshold, detectCrossings, detectProximity, includeProposed, sensitivityLevel } = request.body;
  
  const startTime = Date.now();
  logger.info({ projectId, threshold, detectCrossings, detectProximity, includeProposed, sensitivityLevel }, 'Starting enhanced conflict detection');

  try {
    // Enhanced cache key with optimization strategy
    const cacheKey = `conflicts:v2:${projectId}:${threshold}:${sensitivityLevel}:${detectCrossings}:${detectProximity}:${includeProposed}`;
    
    if (redis) {
      const cached = await redis.get(cacheKey);
      if (cached) {
        logger.info({ projectId, cacheKey }, 'Returning cached conflict detection results');
        return {
          conflicts: JSON.parse(cached),
          cached: true,
          processingTime: Date.now() - startTime,
          method: 'cache',
        };
      }
    }

    // Get optimization plan for this project
    let optimizationPlan;
    try {
      const planResult = await dbPool.query(`
        SELECT * FROM optimize_spatial_query_plan($1::uuid, 'conflict_detection')
      `, [projectId]);
      optimizationPlan = planResult.rows[0];
    } catch (error) {
      logger.warn({ error, projectId }, 'Could not get optimization plan, using fallback');
      optimizationPlan = null;
    }

    let conflicts = [];
    let detectionMethod = 'standard';

    // Try materialized view first for large datasets
    if (optimizationPlan?.use_materialized_view || 
        (sensitivityLevel === 'medium' && threshold >= 3.0)) {
      try {
        const hotspotResult = await dbPool.query(`
          SELECT 
            h.grid_center,
            h.conflict_count,
            h.severity_level,
            h.utility_types_1,
            h.utility_types_2,
            h.min_distance,
            h.avg_distance,
            ST_AsGeoJSON(h.hotspot_area) as hotspot_geometry
          FROM mv_spatial_conflict_hotspots h
          WHERE h.hotspot_area && (
            SELECT ST_Union(u.geometry)
            FROM "Utility" u
            JOIN "ProjectUtility" pu ON u.id = pu."utilityId"
            WHERE pu."projectId" = $1::uuid
              AND u.geometry IS NOT NULL
          )
          AND h.severity_level IN ('high', 'critical')
          ORDER BY h.conflict_count DESC
        `, [projectId]);

        if (hotspotResult.rows.length > 0) {
          conflicts = hotspotResult.rows.map((row, index) => ({
            id: `hotspot-${index}`,
            utility1Id: null,
            utility2Id: null,
            utility1Name: 'Multiple utilities',
            utility2Name: 'Multiple utilities',
            utility1Type: row.utility_types_1?.[0] || 'Unknown',
            utility2Type: row.utility_types_2?.[0] || 'Unknown',
            conflictType: 'hotspot',
            distance: row.min_distance,
            distance3D: row.min_distance,
            intersectionPoint: row.grid_center,
            hotspotGeometry: JSON.parse(row.hotspot_geometry),
            confidenceScore: Math.min(0.95, row.conflict_count / 20),
            priority: row.severity_level === 'critical' ? 'critical' : 'high',
            description: `Conflict hotspot with ${row.conflict_count} detected conflicts`,
            detectedAt: new Date().toISOString(),
            conflictCount: row.conflict_count,
            avgDistance: row.avg_distance,
            utilityTypes1: row.utility_types_1,
            utilityTypes2: row.utility_types_2,
          }));
          
          detectionMethod = 'materialized_view_hotspots';
          logger.info({ projectId, hotspotCount: conflicts.length }, 'Used materialized view hotspots for conflict detection');
        }
      } catch (error) {
        logger.warn({ error, projectId }, 'Materialized view hotspot detection failed, falling back');
      }
    }

    // If no conflicts from materialized view or for detailed analysis, use optimized function
    if (conflicts.length === 0) {
      // Try spatial clustering for large projects
      if (optimizationPlan?.optimization_strategy === 'spatial_clustering' || 
          optimizationPlan?.optimization_strategy === 'cluster_and_batch') {
        try {
          const clusterResult = await dbPool.query(`
            SELECT * FROM find_optimal_spatial_clusters(
              $1::uuid,
              $2::decimal,
              3::integer
            )
          `, [projectId, optimizationPlan?.recommended_cluster_radius || 500]);

          if (clusterResult.rows.length > 0) {
            // Process conflicts by cluster for better performance
            const clusterConflicts = [];
            
            for (const cluster of clusterResult.rows) {
              const clusterConflictResult = await dbPool.query(`
                SELECT * FROM detect_utility_conflicts_optimized(
                  $1::uuid,
                  $2::decimal,
                  $3::boolean,
                  $4::boolean,
                  $5::boolean
                )
                WHERE ST_Intersects(
                  intersection_point,
                  $6::geometry
                )
              `, [
                projectId,
                threshold,
                detectCrossings,
                detectProximity,
                includeProposed,
                cluster.cluster_boundary
              ]);

              clusterConflicts.push(...clusterConflictResult.rows.map(row => ({
                ...row,
                clusterId: cluster.cluster_id,
                clusterUtilityCount: cluster.utility_count,
              })));
            }

            if (clusterConflicts.length > 0) {
              conflicts = clusterConflicts.map(row => ({
                id: `${row.utility1_id}-${row.utility2_id}`,
                utility1Id: row.utility1_id,
                utility2Id: row.utility2_id,
                utility1Name: row.utility1_name,
                utility2Name: row.utility2_name,
                utility1Type: row.utility1_type,
                utility2Type: row.utility2_type,
                conflictType: row.conflict_type,
                distance: parseFloat(row.distance_meters),
                distance3D: parseFloat(row.distance_3d_meters),
                intersectionPoint: row.intersection_point,
                confidenceScore: row.confidence_score,
                priority: row.priority,
                description: row.description,
                detectedAt: new Date().toISOString(),
                clusterId: row.clusterId,
                clusterUtilityCount: row.clusterUtilityCount,
              }));
              
              detectionMethod = 'spatial_clustering';
              logger.info({ projectId, clusterCount: clusterResult.rows.length, conflictCount: conflicts.length }, 'Used spatial clustering for conflict detection');
            }
          }
        } catch (error) {
          logger.warn({ error, projectId }, 'Spatial clustering detection failed, falling back');
        }
      }

      // Fallback to standard optimized detection
      if (conflicts.length === 0) {
        const result = await dbPool.query(`
          SELECT * FROM detect_utility_conflicts_optimized(
            $1::uuid,
            $2::decimal,
            $3::boolean,
            $4::boolean,
            $5::boolean
          )
        `, [
          projectId,
          threshold,
          detectCrossings,
          detectProximity,
          includeProposed
        ]);

        conflicts = result.rows.map(row => ({
          id: `${row.utility1_id}-${row.utility2_id}`,
          utility1Id: row.utility1_id,
          utility2Id: row.utility2_id,
          utility1Name: row.utility1_name,
          utility2Name: row.utility2_name,
          utility1Type: row.utility1_type,
          utility2Type: row.utility2_type,
          conflictType: row.conflict_type,
          distance: parseFloat(row.distance_meters),
          distance3D: parseFloat(row.distance_3d_meters),
          intersectionPoint: row.intersection_point,
          confidenceScore: row.confidence_score,
          priority: row.priority,
          description: row.description,
          detectedAt: new Date().toISOString(),
        }));
        
        detectionMethod = 'standard_optimized';
      }
    }

    // Adaptive cache TTL based on project size and conflict count
    let cacheTTL = 300; // Default 5 minutes
    if (optimizationPlan) {
      if (optimizationPlan.optimization_strategy === 'cluster_and_batch') {
        cacheTTL = 1800; // 30 minutes for large projects
      } else if (optimizationPlan.optimization_strategy === 'spatial_clustering') {
        cacheTTL = 900; // 15 minutes for medium projects
      }
    }

    // Cache results with adaptive TTL
    if (redis) {
      await redis.setex(cacheKey, cacheTTL, JSON.stringify(conflicts));
    }

    const processingTime = Date.now() - startTime;
    logger.info({ 
      projectId, 
      conflictCount: conflicts.length, 
      processingTime, 
      detectionMethod,
      cacheTTL 
    }, 'Enhanced conflict detection completed');

    return {
      conflicts,
      cached: false,
      processingTime,
      method: detectionMethod,
      optimizationPlan: optimizationPlan ? {
        strategy: optimizationPlan.optimization_strategy,
        estimatedGain: optimizationPlan.estimated_performance_gain,
        clusterRadius: optimizationPlan.recommended_cluster_radius,
        batchSize: optimizationPlan.recommended_batch_size,
      } : null,
      statistics: {
        total: conflicts.length,
        critical: conflicts.filter(c => c.priority === 'critical').length,
        high: conflicts.filter(c => c.priority === 'high').length,
        medium: conflicts.filter(c => c.priority === 'medium').length,
        low: conflicts.filter(c => c.priority === 'low').length,
      },
    };

  } catch (error) {
    logger.error({ error, projectId }, 'Error in enhanced conflict detection');
    reply.code(500);
    return {
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      processingTime: Date.now() - startTime,
    };
  }
});

// Generic spatial operations
fastify.post<{
  Body: z.infer<typeof SpatialQuerySchema>
}>('/spatial/query', {
  schema: {
    body: SpatialQuerySchema,
  },
}, async (request, reply) => {
  const { projectId, operation, geometries, parameters = {} } = request.body;
  
  const startTime = Date.now();
  logger.info({ projectId, operation, geometryCount: geometries.length }, 'Starting spatial query');

  try {
    let query: string;
    let queryParams: any[];

    switch (operation) {
      case 'buffer':
        const distance = parameters.distance || 1.0;
        query = `
          SELECT ST_AsGeoJSON(ST_Buffer(ST_GeomFromGeoJSON($1), $2)) as result
        `;
        queryParams = [JSON.stringify(geometries[0]), distance];
        break;

      case 'intersection':
        query = `
          SELECT ST_AsGeoJSON(ST_Intersection(
            ST_GeomFromGeoJSON($1),
            ST_GeomFromGeoJSON($2)
          )) as result
        `;
        queryParams = [JSON.stringify(geometries[0]), JSON.stringify(geometries[1])];
        break;

      case 'union':
        query = `
          SELECT ST_AsGeoJSON(ST_Union(ARRAY[${geometries.map((_, i) => `ST_GeomFromGeoJSON($${i + 1})`).join(', ')}])) as result
        `;
        queryParams = geometries.map(g => JSON.stringify(g));
        break;

      case 'contains':
        query = `
          SELECT ST_Contains(
            ST_GeomFromGeoJSON($1),
            ST_GeomFromGeoJSON($2)
          ) as result
        `;
        queryParams = [JSON.stringify(geometries[0]), JSON.stringify(geometries[1])];
        break;

      case 'within':
        query = `
          SELECT ST_Within(
            ST_GeomFromGeoJSON($1),
            ST_GeomFromGeoJSON($2)
          ) as result
        `;
        queryParams = [JSON.stringify(geometries[0]), JSON.stringify(geometries[1])];
        break;

      default:
        reply.code(400);
        return { error: 'Unsupported spatial operation' };
    }

    const result = await dbPool.query(query, queryParams);
    const processingTime = Date.now() - startTime;

    logger.info({ projectId, operation, processingTime }, 'Spatial query completed');

    return {
      result: result.rows[0]?.result ? JSON.parse(result.rows[0].result) : result.rows[0]?.result,
      operation,
      processingTime,
    };

  } catch (error) {
    logger.error({ error, projectId, operation }, 'Error in spatial query');
    reply.code(500);
    return {
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      processingTime: Date.now() - startTime,
    };
  }
});

// Geometry validation
fastify.post<{
  Body: z.infer<typeof GeometryValidationSchema>
}>('/spatial/validate', {
  schema: {
    body: GeometryValidationSchema,
  },
}, async (request, reply) => {
  const { geometry, type } = request.body;
  
  try {
    const query = `
      SELECT 
        ST_IsValid(ST_GeomFromGeoJSON($1)) as is_valid,
        ST_IsSimple(ST_GeomFromGeoJSON($1)) as is_simple,
        ST_GeometryType(ST_GeomFromGeoJSON($1)) as geometry_type,
        ST_Area(ST_GeomFromGeoJSON($1)) as area,
        ST_Length(ST_GeomFromGeoJSON($1)) as length,
        ST_AsText(ST_Centroid(ST_GeomFromGeoJSON($1))) as centroid
    `;

    const result = await dbPool.query(query, [JSON.stringify(geometry)]);
    const row = result.rows[0];

    const validation = {
      isValid: row.is_valid,
      isSimple: row.is_simple,
      geometryType: row.geometry_type,
      area: parseFloat(row.area) || 0,
      length: parseFloat(row.length) || 0,
      centroid: row.centroid,
      errors: [],
    };

    // Additional type validation if specified
    if (type && !row.geometry_type.includes(type)) {
      validation.errors.push(`Expected ${type} but got ${row.geometry_type}`);
    }

    if (!validation.isValid) {
      validation.errors.push('Geometry is not valid');
    }

    return validation;

  } catch (error) {
    logger.error({ error, geometry }, 'Error in geometry validation');
    reply.code(500);
    return {
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
});

// Cache management
fastify.delete('/cache/:projectId', async (request, reply) => {
  const { projectId } = request.params as { projectId: string };
  
  if (!redis) {
    reply.code(404);
    return { error: 'Redis not configured' };
  }

  try {
    const pattern = `conflicts:${projectId}:*`;
    const keys = await redis.keys(pattern);
    
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    logger.info({ projectId, keysDeleted: keys.length }, 'Cache cleared for project');
    
    return {
      message: 'Cache cleared successfully',
      keysDeleted: keys.length,
    };

  } catch (error) {
    logger.error({ error, projectId }, 'Error clearing cache');
    reply.code(500);
    return {
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
});

// Graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Shutting down spatial processor gracefully...');
  
  try {
    await fastify.close();
    await dbPool.end();
    if (redis) {
      redis.disconnect();
    }
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error({ error }, 'Error during shutdown');
    process.exit(1);
  }
};

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: parseInt(env.PORT), host: '0.0.0.0' });
    logger.info(`🚀 Spatial processor running on port ${env.PORT}`);
  } catch (error) {
    logger.error({ error }, 'Failed to start server');
    process.exit(1);
  }
};

start();