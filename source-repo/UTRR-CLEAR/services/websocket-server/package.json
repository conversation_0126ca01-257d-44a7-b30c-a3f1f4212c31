{"name": "clear-websocket-server", "version": "1.0.0", "description": "Real-time WebSocket server for CLEAR utility infrastructure management", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"ws": "^8.14.2", "fastify": "^4.24.3", "fastify-cors": "^6.1.0", "fastify-helmet": "^11.1.1", "fastify-rate-limit": "^9.1.0", "fastify-websocket": "^8.3.0", "ioredis": "^5.3.2", "zod": "^3.22.4", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.8.10", "@types/ws": "^8.5.8", "@types/jsonwebtoken": "^9.0.5", "typescript": "^5.2.2", "tsx": "^4.1.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0"}, "engines": {"node": ">=20.0.0"}}