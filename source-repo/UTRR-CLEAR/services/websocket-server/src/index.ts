import Fastify from 'fastify';
import fastifyCors from '@fastify/cors';
import fastifyHelmet from '@fastify/helmet';
import fastifyRateLimit from '@fastify/rate-limit';
import fastifyWebsocket from '@fastify/websocket';
import Redis from 'ioredis';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import axios from 'axios';
import pino from 'pino';
import 'dotenv/config';

// Environment schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('8002'),
  REDIS_URL: z.string(),
  AUTH_SERVICE_URL: z.string(),
  JWT_SECRET: z.string().optional(),
  LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error']).default('info'),
});

const env = envSchema.parse(process.env);

// Logger configuration
const logger = pino({
  level: env.LOG_LEVEL,
  transport: env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'HH:MM:ss Z',
      ignore: 'pid,hostname',
    },
  } : undefined,
});

// Redis clients
const redisPublisher = new Redis(env.REDIS_URL, {
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
});

const redisSubscriber = new Redis(env.REDIS_URL, {
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
});

// WebSocket connection tracking
const connections = new Map<string, {
  ws: any;
  userId?: string;
  sessionId?: string;
  subscriptions: Set<string>;
  lastSeen: number;
}>();

// Message schemas
const WebSocketMessageSchema = z.object({
  type: z.enum(['subscribe', 'unsubscribe', 'ping', 'message']),
  payload: z.any().optional(),
  channels: z.array(z.string()).optional(),
  channel: z.string().optional(),
});

const BroadcastMessageSchema = z.object({
  channel: z.string(),
  event: z.string(),
  data: z.any(),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
});

// Fastify server
const fastify = Fastify({
  logger,
  trustProxy: true,
});

// Register plugins
fastify.register(fastifyHelmet, {
  contentSecurityPolicy: false,
});

fastify.register(fastifyCors, {
  origin: true,
  credentials: true,
});

fastify.register(fastifyRateLimit, {
  max: 200,
  timeWindow: '1 minute',
  redis: redisPublisher,
});

fastify.register(fastifyWebsocket, {
  options: {
    maxPayload: 1048576, // 1MB
    verifyClient: async (info) => {
      try {
        // Extract token from query string or headers
        const url = new URL(info.req.url!, `http://${info.req.headers.host}`);
        const token = url.searchParams.get('token') || info.req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          logger.warn('WebSocket connection rejected: No auth token');
          return false;
        }

        // Verify token with auth service
        const response = await axios.post(`${env.AUTH_SERVICE_URL}/verify`, 
          { token },
          { timeout: 5000 }
        );

        if (response.status === 200 && response.data.valid) {
          // Store user info for connection
          (info.req as any).user = response.data.user;
          return true;
        }

        logger.warn({ token: token.substring(0, 10) + '...' }, 'WebSocket connection rejected: Invalid token');
        return false;

      } catch (error) {
        logger.error({ error }, 'Error verifying WebSocket connection');
        return false;
      }
    },
  },
});

// WebSocket route
fastify.register(async function (fastify) {
  fastify.get('/ws', { websocket: true }, async (connection, request) => {
    const connectionId = generateConnectionId();
    const user = (request as any).user;
    
    logger.info({ connectionId, userId: user?.id }, 'New WebSocket connection established');

    // Register connection
    connections.set(connectionId, {
      ws: connection.socket,
      userId: user?.id,
      sessionId: user?.sessionId,
      subscriptions: new Set(),
      lastSeen: Date.now(),
    });

    // Send welcome message
    connection.socket.send(JSON.stringify({
      type: 'connected',
      connectionId,
      timestamp: new Date().toISOString(),
    }));

    // Handle incoming messages
    connection.socket.on('message', async (rawMessage: Buffer) => {
      try {
        const message = JSON.parse(rawMessage.toString());
        const parsed = WebSocketMessageSchema.parse(message);

        await handleWebSocketMessage(connectionId, parsed);

      } catch (error) {
        logger.error({ error, connectionId }, 'Error processing WebSocket message');
        connection.socket.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format',
          timestamp: new Date().toISOString(),
        }));
      }
    });

    // Handle connection close
    connection.socket.on('close', () => {
      logger.info({ connectionId, userId: user?.id }, 'WebSocket connection closed');
      
      const conn = connections.get(connectionId);
      if (conn) {
        // Unsubscribe from all channels
        conn.subscriptions.forEach(channel => {
          redisPublisher.srem(`channel:${channel}:connections`, connectionId);
        });
      }
      
      connections.delete(connectionId);
    });

    // Handle connection errors
    connection.socket.on('error', (error: Error) => {
      logger.error({ error, connectionId }, 'WebSocket connection error');
      connections.delete(connectionId);
    });

    // Update last seen timestamp periodically
    const heartbeat = setInterval(() => {
      const conn = connections.get(connectionId);
      if (conn) {
        conn.lastSeen = Date.now();
      } else {
        clearInterval(heartbeat);
      }
    }, 30000);
  });
});

// Handle WebSocket messages
async function handleWebSocketMessage(connectionId: string, message: z.infer<typeof WebSocketMessageSchema>) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  switch (message.type) {
    case 'subscribe':
      if (message.channels) {
        for (const channel of message.channels) {
          await subscribeToChannel(connectionId, channel);
        }
      }
      break;

    case 'unsubscribe':
      if (message.channels) {
        for (const channel of message.channels) {
          await unsubscribeFromChannel(connectionId, channel);
        }
      }
      break;

    case 'ping':
      connection.ws.send(JSON.stringify({
        type: 'pong',
        timestamp: new Date().toISOString(),
      }));
      break;

    case 'message':
      if (message.channel && message.payload) {
        await broadcastToChannel(message.channel, {
          event: 'user_message',
          data: message.payload,
          userId: connection.userId,
          sessionId: connection.sessionId,
        });
      }
      break;
  }
}

// Subscribe to channel
async function subscribeToChannel(connectionId: string, channel: string) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  connection.subscriptions.add(channel);
  await redisPublisher.sadd(`channel:${channel}:connections`, connectionId);
  
  logger.debug({ connectionId, channel }, 'Subscribed to channel');
  
  connection.ws.send(JSON.stringify({
    type: 'subscribed',
    channel,
    timestamp: new Date().toISOString(),
  }));
}

// Unsubscribe from channel
async function unsubscribeFromChannel(connectionId: string, channel: string) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  connection.subscriptions.delete(channel);
  await redisPublisher.srem(`channel:${channel}:connections`, connectionId);
  
  logger.debug({ connectionId, channel }, 'Unsubscribed from channel');
  
  connection.ws.send(JSON.stringify({
    type: 'unsubscribed',
    channel,
    timestamp: new Date().toISOString(),
  }));
}

// Broadcast message to channel
async function broadcastToChannel(channel: string, message: Omit<z.infer<typeof BroadcastMessageSchema>, 'channel'>) {
  const broadcastMessage = {
    channel,
    ...message,
  };

  // Publish to Redis for other server instances
  await redisPublisher.publish('websocket:broadcast', JSON.stringify(broadcastMessage));
  
  // Send to local connections
  await sendToChannelConnections(channel, broadcastMessage);
}

// Send message to all connections in a channel
async function sendToChannelConnections(channel: string, message: z.infer<typeof BroadcastMessageSchema>) {
  const connectionIds = await redisPublisher.smembers(`channel:${channel}:connections`);
  
  for (const connectionId of connectionIds) {
    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === 1) { // WebSocket.OPEN
      try {
        connection.ws.send(JSON.stringify({
          type: 'message',
          channel: message.channel,
          event: message.event,
          data: message.data,
          timestamp: new Date().toISOString(),
        }));
      } catch (error) {
        logger.error({ error, connectionId }, 'Error sending message to connection');
        // Clean up dead connection
        connections.delete(connectionId);
        await redisPublisher.srem(`channel:${channel}:connections`, connectionId);
      }
    }
  }
}

// Listen for Redis broadcasts from other services
redisSubscriber.subscribe('websocket:broadcast');
redisSubscriber.on('message', async (channel, message) => {
  if (channel === 'websocket:broadcast') {
    try {
      const broadcastMessage = BroadcastMessageSchema.parse(JSON.parse(message));
      await sendToChannelConnections(broadcastMessage.channel, broadcastMessage);
    } catch (error) {
      logger.error({ error, message }, 'Error processing broadcast message');
    }
  }
});

// REST API endpoints

// Health check
fastify.get('/health', async (request, reply) => {
  try {
    await redisPublisher.ping();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connections: connections.size,
      services: {
        redis: 'connected',
      },
      version: '1.0.0',
    };
  } catch (error) {
    reply.code(503);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
});

// Get connection statistics
fastify.get('/stats', async (request, reply) => {
  const stats = {
    totalConnections: connections.size,
    connectionsByUser: new Map<string, number>(),
    channelSubscriptions: new Map<string, number>(),
  };

  for (const [connectionId, connection] of connections) {
    if (connection.userId) {
      const userCount = stats.connectionsByUser.get(connection.userId) || 0;
      stats.connectionsByUser.set(connection.userId, userCount + 1);
    }

    for (const channel of connection.subscriptions) {
      const channelCount = stats.channelSubscriptions.get(channel) || 0;
      stats.channelSubscriptions.set(channel, channelCount + 1);
    }
  }

  return {
    ...stats,
    connectionsByUser: Object.fromEntries(stats.connectionsByUser),
    channelSubscriptions: Object.fromEntries(stats.channelSubscriptions),
    timestamp: new Date().toISOString(),
  };
});

// Broadcast message to channel via REST API
fastify.post<{
  Body: z.infer<typeof BroadcastMessageSchema>
}>('/broadcast', {
  schema: {
    body: BroadcastMessageSchema,
  },
}, async (request, reply) => {
  const { channel, event, data, userId, sessionId } = request.body;
  
  try {
    await broadcastToChannel(channel, { event, data, userId, sessionId });
    
    return {
      success: true,
      message: 'Message broadcasted successfully',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error({ error, channel, event }, 'Error broadcasting message');
    reply.code(500);
    return {
      success: false,
      error: 'Failed to broadcast message',
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
});

// Utility functions
function generateConnectionId(): string {
  return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Clean up dead connections periodically
setInterval(() => {
  const now = Date.now();
  const timeout = 5 * 60 * 1000; // 5 minutes

  for (const [connectionId, connection] of connections) {
    if (now - connection.lastSeen > timeout) {
      logger.info({ connectionId }, 'Cleaning up inactive connection');
      connections.delete(connectionId);
      
      // Clean up Redis subscriptions
      connection.subscriptions.forEach(channel => {
        redisPublisher.srem(`channel:${channel}:connections`, connectionId);
      });
    }
  }
}, 60000); // Check every minute

// Graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Shutting down WebSocket server gracefully...');
  
  try {
    // Close all WebSocket connections
    for (const [connectionId, connection] of connections) {
      connection.ws.close(1001, 'Server shutting down');
    }
    connections.clear();

    await fastify.close();
    redisPublisher.disconnect();
    redisSubscriber.disconnect();
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error({ error }, 'Error during shutdown');
    process.exit(1);
  }
};

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: parseInt(env.PORT), host: '0.0.0.0' });
    logger.info(`🔌 WebSocket server running on port ${env.PORT}`);
  } catch (error) {
    logger.error({ error }, 'Failed to start server');
    process.exit(1);
  }
};

start();