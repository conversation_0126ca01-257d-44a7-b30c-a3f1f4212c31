# OWASP ZAP Rules Configuration
# Format: Rule ID	PASS/FAIL/WARN/IGNORE	Threshold	URL Pattern

# Authentication and Session Management
10011	WARN	MEDIUM	.*	# Cookie Without Secure Flag
10012	WARN	MEDIUM	.*	# Password Autocomplete
10015	WARN	LOW	.*	# Incomplete or No Cache-control and Pragma HTTP Header Set
10016	WARN	LOW	.*	# Web Browser XSS Protection Not Enabled
10017	WARN	LOW	.*	# Cross-Domain JavaScript Source File Inclusion
10019	WARN	LOW	.*	# Content-Type Header Missing
10020	WARN	LOW	.*	# X-Frame-Options Header Not Set
10021	WARN	LOW	.*	# X-Content-Type-Options Header Missing
10023	WARN	LOW	.*	# Information Disclosure - Debug Error Messages
10024	WARN	LOW	.*	# Information Disclosure - Sensitive Information in URL
10025	WARN	LOW	.*	# Information Disclosure - Sensitive Information in HTTP Referrer Header
10027	WARN	LOW	.*	# Information Disclosure - Suspicious Comments
10028	IGNORE	LOW	.*	# Open Redirect (acceptable for OAuth flows)
10029	WARN	MEDIUM	.*	# Cookie Poisoning
10030	WARN	MEDIUM	.*	# User Controllable Charset
10031	WARN	LOW	.*	# User Controllable HTML Element Attribute
10032	WARN	LOW	.*	# Viewstate Scanner
10033	WARN	LOW	.*	# Directory Browsing
10034	WARN	LOW	.*	# Heartbleed OpenSSL Vulnerability
10035	WARN	LOW	.*	# Strict-Transport-Security Header Not Set
10036	WARN	LOW	.*	# Server Leaks Version Information
10037	WARN	LOW	.*	# Server Leaks Information
10038	WARN	LOW	.*	# Content Security Policy Header Not Set
10039	WARN	LOW	.*	# X-Backend-Server Header Information Leak
10040	WARN	LOW	.*	# Secure Pages Include Mixed Content
10041	WARN	LOW	.*	# HTTP to HTTPS Insecure Transition in Form Post
10042	WARN	MEDIUM	.*	# HTTPS to HTTP Insecure Transition in Form Post
10043	WARN	MEDIUM	.*	# User Controllable JavaScript Event
10044	WARN	LOW	.*	# Big Redirect Detected
10045	WARN	LOW	.*	# Source Code Disclosure - /WEB-INF folder
10047	WARN	HIGH	.*	# HTTPS Content Available via HTTP
10048	WARN	LOW	.*	# Remote Code Execution - Shell Shock
10049	IGNORE	LOW	/api/health	# Allow health check endpoint
10050	WARN	LOW	.*	# Retrieved from Cache
10051	WARN	LOW	.*	# Relative Path Confusion
10052	WARN	LOW	.*	# X-ChromeLogger-Data Header Information Leak
10053	WARN	LOW	.*	# Apache Range Header DoS (CVE-2011-3192)
10054	IGNORE	LOW	.*	# Cookie Without SameSite Attribute (handled by framework)
10055	WARN	LOW	.*	# CSP Scanner
10056	WARN	LOW	.*	# X-Debug-Token Information Leak
10057	WARN	LOW	.*	# Username Hash Found
10058	WARN	LOW	.*	# GET for POST
10061	WARN	LOW	.*	# X-AspNet-Version Response Header Scanner
10062	WARN	LOW	.*	# PII Scanner
10070	WARN	LOW	.*	# Use of SAML
10094	WARN	LOW	.*	# Base64 Disclosure
10095	WARN	LOW	.*	# Backup File Disclosure
10096	WARN	LOW	.*	# Timestamp Disclosure
10097	WARN	LOW	.*	# Hash Disclosure
10098	WARN	LOW	.*	# Cross-Domain Misconfiguration
10099	WARN	LOW	.*	# Source Code Disclosure

# SQL Injection
40003	FAIL	HIGH	.*	# CRLF Injection
40008	FAIL	HIGH	.*	# Parameter Tampering
40009	FAIL	HIGH	.*	# Server Side Include
40012	FAIL	HIGH	.*	# Cross Site Scripting (Reflected)
40013	FAIL	HIGH	.*	# Session Fixation
40014	FAIL	HIGH	.*	# Cross Site Scripting (Persistent)
40016	FAIL	HIGH	.*	# Cross Site Scripting (Persistent) - Prime
40017	FAIL	HIGH	.*	# Cross Site Scripting (Persistent) - Spider
40018	FAIL	HIGH	.*	# SQL Injection
40019	FAIL	HIGH	.*	# SQL Injection - MySQL
40020	FAIL	HIGH	.*	# SQL Injection - Hypersonic
40021	FAIL	HIGH	.*	# SQL Injection - Oracle
40022	FAIL	HIGH	.*	# SQL Injection - PostgreSQL
40023	FAIL	HIGH	.*	# Possible Username Enumeration
40024	FAIL	HIGH	.*	# SQL Injection - SQLite
40025	FAIL	HIGH	.*	# Proxy Disclosure
40026	FAIL	HIGH	.*	# Cross Site Scripting (DOM Based)
40027	FAIL	HIGH	.*	# SQL Injection - MsSQL
40028	FAIL	HIGH	.*	# ELMAH Information Leak
40029	FAIL	HIGH	.*	# Trace.axd Information Leak
40030	WARN	LOW	.*	# Backslash Powered Scanner
40031	FAIL	HIGH	.*	# Cross Site Scripting (Reflected) - Prime
40032	FAIL	HIGH	.*	# .htaccess Information Leak
40033	WARN	MEDIUM	.*	# Crypto Mining Activity
40034	FAIL	HIGH	.*	# .env Information Leak
40035	FAIL	HIGH	.*	# Hidden File Found

# Custom Rules for Next.js/React
10096	IGNORE	LOW	/_next/.*	# Next.js static assets are timestamped by design
10098	IGNORE	LOW	/api/.*	# API routes have proper CORS configuration
10049	IGNORE	LOW	/api/trpc/.*	# tRPC endpoints don't need caching
90001	FAIL	HIGH	.*	# Insecure JSF ViewState
90011	FAIL	HIGH	.*	# Charset Mismatch
90022	FAIL	HIGH	.*	# Application Error Disclosure
90023	FAIL	HIGH	.*	# XML External Entity Attack
90024	FAIL	HIGH	.*	# Generic Padding Oracle
90025	FAIL	HIGH	.*	# Expression Language Injection
90026	FAIL	HIGH	.*	# SOAP Action Spoofing
90027	FAIL	HIGH	.*	# Cookie Slack Detector
90028	FAIL	HIGH	.*	# Insecure HTTP Method
90029	FAIL	HIGH	.*	# SOAP XML Injection
90030	WARN	LOW	.*	# WSDL File Detection
90033	WARN	LOW	.*	# Loosely Scoped Cookie

# False Positive Suppressions
10015	IGNORE	LOW	/_next/static/.*	# Static assets don't need cache headers
10035	IGNORE	LOW	/public/.*	# Public assets served over CDN
10023	IGNORE	LOW	/api/health	# Health endpoint can show version info
10037	IGNORE	LOW	/api/version	# Version endpoint is intentional