{"permissions": {"allow": ["Bash(find:*)", "<PERSON>sh(vercel deploy:*)", "Bash(vercel logs:*)", "<PERSON><PERSON>(vercel inspect:*)", "Bash(npm run typecheck:*)", "Bash(npm run build:*)", "Bash(grep:*)", "<PERSON><PERSON>(vercel:*)", "Bash(git add:*)", "Bash(git pull:*)", "Bash(git config:*)", "Bash(git push:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "<PERSON><PERSON>(sed:*)", "Bash(timeout 30 npm run lint -- --max-warnings 0)", "WebFetch(domain:vercel.com)", "WebFetch(domain:clear-a1q9zrrdb-craft1563s-projects.vercel.app)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(git reset:*)", "Bash(npx tsx:*)", "Bash(npx prisma db pull:*)", "Bash(npx supabase db remote commit:*)", "mcp__supabase__execute_sql", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(cat:*)", "mcp__supabase__list_projects", "Bash(git commit:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__supabase__get_project", "mcp__supabase__list_tables", "Bash(lsof:*)", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_click", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_close", "Bash(npm run:*)", "Bash(npx prisma generate:*)", "Bash(ss:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx supabase:*)", "<PERSON><PERSON>(pkill:*)"], "deny": []}, "enabledMcpjsonServers": ["sequential-thinking", "sparc2", "memory", "prisma-mcp-server", "playwright", "context7", "supabase"]}