name: Security Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security tests daily at 2 AM
    - cron: '0 2 * * *'

jobs:
  static-security-analysis:
    name: Static Security Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        # Full git history needed for some security scanners
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './utility-sync-t3/package-lock.json'

    - name: Install dependencies
      working-directory: ./utility-sync-t3
      run: npm ci

    - name: Run npm audit
      working-directory: ./utility-sync-t3
      run: |
        npm audit --audit-level=high --production
        npm audit --audit-level=critical --production

    - name: Run Semgrep SAST
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/owasp-top-ten
          p/javascript
          p/typescript
          p/react
        generateSarif: "1"
      env:
        SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

    - name: Upload Semgrep results to GitHub
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: semgrep.sarif

    - name: CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        languages: javascript
        queries: +security-and-quality

    - name: Run ESLint Security Rules
      working-directory: ./utility-sync-t3
      run: |
        npx eslint . \
          --ext .js,.jsx,.ts,.tsx \
          --config .eslintrc.security.js \
          --format sarif \
          --output-file eslint-security-results.sarif
      continue-on-error: true

    - name: Upload ESLint Security Results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: ./utility-sync-t3/eslint-security-results.sarif

  dependency-security-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Snyk Security Scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high --all-projects

    - name: Upload Snyk results to GitHub
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: snyk.sarif

  api-security-tests:
    name: API Security Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgis/postgis:14-3.2
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: clear_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './utility-sync-t3/package-lock.json'

    - name: Install dependencies
      working-directory: ./utility-sync-t3
      run: npm ci

    - name: Setup test database
      working-directory: ./utility-sync-t3
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/clear_test
      run: |
        npx prisma migrate deploy
        npx prisma db seed

    - name: Start application for testing
      working-directory: ./utility-sync-t3
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/clear_test
        NEXTAUTH_SECRET: test-secret-for-ci
        NEXTAUTH_URL: http://localhost:3000
      run: |
        npm run build
        npm start &
        sleep 30 # Wait for app to start

    - name: Run API Security Tests
      working-directory: ./utility-sync-t3
      run: |
        npx playwright test tests/e2e/security-tests.spec.ts \
          --reporter=junit \
          --output-dir=test-results/security

    - name: Run OWASP ZAP API Scan
      uses: zaproxy/action-api-scan@v0.4.0
      with:
        target: 'http://localhost:3000/api'
        format: sarif
        output: zap-api-results.sarif

    - name: Upload ZAP results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: zap-api-results.sarif

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-results
        path: ./utility-sync-t3/test-results/

  multi-tenant-security-validation:
    name: Multi-Tenant Security Validation
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgis/postgis:14-3.2
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: clear_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './utility-sync-t3/package-lock.json'

    - name: Install dependencies
      working-directory: ./utility-sync-t3
      run: npm ci

    - name: Run Multi-Tenant Security Tests
      working-directory: ./utility-sync-t3
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/clear_test
      run: |
        # Custom script to validate multi-tenant isolation
        npm run test:security:multi-tenant

    - name: Validate Organization Data Isolation
      working-directory: ./utility-sync-t3
      run: |
        # Run tests to ensure no cross-organization data leakage
        npm run test:isolation

  secrets-scanning:
    name: Secrets Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: TruffleHog OSS Secrets Scan
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified

    - name: GitLeaks Secrets Scan
      uses: gitleaks/gitleaks-action@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  container-security-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Build Docker image
      working-directory: ./utility-sync-t3
      run: |
        docker build -t clear-app:${{ github.sha }} .

    - name: Run Trivy container scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'clear-app:${{ github.sha }}'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  security-headers-test:
    name: Security Headers Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './utility-sync-t3/package-lock.json'

    - name: Install dependencies
      working-directory: ./utility-sync-t3
      run: npm ci

    - name: Start application
      working-directory: ./utility-sync-t3
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/clear_test
        NEXTAUTH_SECRET: test-secret-for-ci
        NEXTAUTH_URL: http://localhost:3000
      run: |
        npm run build
        npm start &
        sleep 30

    - name: Test Security Headers
      run: |
        # Test for required security headers
        curl -I http://localhost:3000 | grep -i "x-frame-options"
        curl -I http://localhost:3000 | grep -i "x-content-type-options"
        curl -I http://localhost:3000 | grep -i "strict-transport-security"
        curl -I http://localhost:3000 | grep -i "content-security-policy"

    - name: Mozilla Observatory Scan
      run: |
        npx observatory-cli http://localhost:3000 --format=json > observatory-results.json

    - name: Upload Observatory results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: observatory-results
        path: observatory-results.json

  security-report:
    name: Security Report
    runs-on: ubuntu-latest
    needs: [
      static-security-analysis,
      dependency-security-scan,
      api-security-tests,
      multi-tenant-security-validation,
      secrets-scanning,
      container-security-scan,
      security-headers-test
    ]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Generate Security Report
      run: |
        echo "# Security Test Results" > security-report.md
        echo "Generated: $(date)" >> security-report.md
        echo "" >> security-report.md
        
        echo "## Test Status" >> security-report.md
        echo "- Static Security Analysis: ${{ needs.static-security-analysis.result }}" >> security-report.md
        echo "- Dependency Security Scan: ${{ needs.dependency-security-scan.result }}" >> security-report.md
        echo "- API Security Tests: ${{ needs.api-security-tests.result }}" >> security-report.md
        echo "- Multi-Tenant Validation: ${{ needs.multi-tenant-security-validation.result }}" >> security-report.md
        echo "- Secrets Scanning: ${{ needs.secrets-scanning.result }}" >> security-report.md
        echo "- Container Security: ${{ needs.container-security-scan.result }}" >> security-report.md
        echo "- Security Headers: ${{ needs.security-headers-test.result }}" >> security-report.md

    - name: Upload Security Report
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.md

    - name: Post Security Report to PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('security-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## Security Test Results\n\n${report}`
          });

    - name: Fail on security issues
      if: |
        needs.static-security-analysis.result == 'failure' ||
        needs.dependency-security-scan.result == 'failure' ||
        needs.api-security-tests.result == 'failure' ||
        needs.multi-tenant-security-validation.result == 'failure' ||
        needs.secrets-scanning.result == 'failure'
      run: |
        echo "Security tests failed. Please review the security report."
        exit 1