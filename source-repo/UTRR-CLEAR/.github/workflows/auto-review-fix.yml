name: Auto Review & Fix

on:
  push:
    branches: [master, main]
    paths:
      - 'utility-sync-t3/**'
      - '.github/workflows/auto-review-fix.yml'
  workflow_dispatch:

jobs:
  auto-fix:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: utility-sync-t3/package-lock.json
      
      - name: Install dependencies
        working-directory: ./utility-sync-t3
        run: npm ci
      
      - name: Run type check
        id: typecheck
        working-directory: ./utility-sync-t3
        run: |
          echo "Running typecheck..."
          npm run typecheck 2>&1 | tee typecheck.log || echo "TYPECHECK_FAILED=true" >> $GITHUB_OUTPUT
          
      - name: Auto-fix linting issues
        working-directory: ./utility-sync-t3
        run: |
          echo "Auto-fixing lint issues..."
          npm run lint || true
          
      - name: Auto-format code
        working-directory: ./utility-sync-t3
        run: |
          echo "Auto-formatting code..."
          npm run format || true
      
      - name: Run tests
        id: tests
        working-directory: ./utility-sync-t3
        run: |
          echo "Running tests..."
          npm test 2>&1 | tee test.log || echo "TESTS_FAILED=true" >> $GITHUB_OUTPUT
      
      - name: Check for changes
        id: changes
        run: |
          if [[ -n $(git status -s) ]]; then
            echo "CHANGES_MADE=true" >> $GITHUB_OUTPUT
          fi
      
      - name: Commit auto-fixes
        if: steps.changes.outputs.CHANGES_MADE == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add -A
          git commit -m "🤖 Auto-fix: lint and format issues"
          git push

  claude-review:
    runs-on: ubuntu-latest
    needs: auto-fix
    if: always()
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
          
      - name: Get changed files
        id: changed-files
        run: |
          echo "Changed files:"
          git diff --name-only HEAD^ HEAD | grep -E '\.(ts|tsx|js|jsx)$' | tee changed_files.txt || true
          
      - name: Review with Claude
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          # Create review script
          cat > review.js << 'EOF'
          const fs = require('fs');
          const { Anthropic } = require('@anthropic-ai/sdk');
          
          const anthropic = new Anthropic({
            apiKey: process.env.ANTHROPIC_API_KEY,
          });
          
          async function reviewCode() {
            const changedFiles = fs.readFileSync('changed_files.txt', 'utf-8')
              .split('\n')
              .filter(f => f.trim());
              
            if (changedFiles.length === 0) {
              console.log('No code files to review');
              return;
            }
            
            const issues = [];
            
            for (const file of changedFiles) {
              if (!fs.existsSync(file)) continue;
              
              const content = fs.readFileSync(file, 'utf-8');
              const prompt = `Review this ${file.split('.').pop()} file for:
              - Security vulnerabilities
              - Performance issues
              - Type safety problems
              - React best practices violations
              - Potential bugs
              - Database query optimization (if applicable)
              
              File: ${file}
              Content:
              ${content}
              
              Respond with a JSON array of issues found, or empty array if none.
              Format: [{"severity": "high|medium|low", "line": number|null, "issue": "description", "suggestion": "fix"}]`;
              
              try {
                const response = await anthropic.messages.create({
                  model: 'claude-3-5-sonnet-20241022',
                  max_tokens: 1024,
                  messages: [{ role: 'user', content: prompt }],
                });
                
                const match = response.content[0].text.match(/\[[\s\S]*\]/);
                if (match) {
                  const fileIssues = JSON.parse(match[0]);
                  issues.push(...fileIssues.map(i => ({ ...i, file })));
                }
              } catch (e) {
                console.error(`Error reviewing ${file}:`, e.message);
              }
            }
            
            // Create summary
            if (issues.length > 0) {
              console.log('\n🔍 Claude Review Results:\n');
              console.log(`Found ${issues.length} issues:\n`);
              
              const highSeverity = issues.filter(i => i.severity === 'high');
              const mediumSeverity = issues.filter(i => i.severity === 'medium');
              const lowSeverity = issues.filter(i => i.severity === 'low');
              
              if (highSeverity.length > 0) {
                console.log('🔴 HIGH SEVERITY:');
                highSeverity.forEach(i => {
                  console.log(`  - ${i.file}${i.line ? `:${i.line}` : ''}: ${i.issue}`);
                  console.log(`    Fix: ${i.suggestion}\n`);
                });
              }
              
              if (mediumSeverity.length > 0) {
                console.log('🟡 MEDIUM SEVERITY:');
                mediumSeverity.forEach(i => {
                  console.log(`  - ${i.file}${i.line ? `:${i.line}` : ''}: ${i.issue}`);
                  console.log(`    Fix: ${i.suggestion}\n`);
                });
              }
              
              if (lowSeverity.length > 0) {
                console.log('🟢 LOW SEVERITY:');
                lowSeverity.forEach(i => {
                  console.log(`  - ${i.file}${i.line ? `:${i.line}` : ''}: ${i.issue}`);
                  console.log(`    Fix: ${i.suggestion}\n`);
                });
              }
              
              // Fail if high severity issues
              if (highSeverity.length > 0) {
                process.exit(1);
              }
            } else {
              console.log('✅ No issues found!');
            }
          }
          
          reviewCode().catch(console.error);
          EOF
          
          # Install Anthropic SDK
          npm install @anthropic-ai/sdk
          
          # Run review
          node review.js || echo "Review found issues"
          
      - name: Create issue for critical problems
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const title = '🚨 Critical issues found in automated review';
            const body = 'The automated Claude review found critical issues in the latest push. Please check the workflow logs for details.';
            
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title,
              body,
              labels: ['bug', 'critical', 'automated-review']
            });

  security-checks:
    runs-on: ubuntu-latest
    needs: auto-fix
    steps:
      - uses: actions/checkout@v4
      
      - name: Run npm audit
        working-directory: ./utility-sync-t3
        run: |
          npm audit --production || true
          
      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./utility-sync-t3
          base: ${{ github.event.before }}
          head: ${{ github.event.after }}
          
      - name: SAST Scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: './utility-sync-t3'
          format: 'table'