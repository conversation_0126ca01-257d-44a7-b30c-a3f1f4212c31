name: Automated Code Review

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches: [main, develop]

jobs:
  lint-and-type:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: utility-sync-t3/package-lock.json
      
      - name: Install dependencies
        working-directory: ./utility-sync-t3
        run: npm ci
      
      - name: Type Check
        working-directory: ./utility-sync-t3
        run: npm run typecheck
        
      - name: Lint
        working-directory: ./utility-sync-t3
        run: npm run lint
        
      - name: Format Check
        working-directory: ./utility-sync-t3
        run: npm run format:check || true

  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run CodeQL
        uses: github/codeql-action/analyze@v3
        
      - name: npm audit
        working-directory: ./utility-sync-t3
        run: npm audit --audit-level=moderate || true

  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: utility-sync-t3/package-lock.json
          
      - name: Install dependencies
        working-directory: ./utility-sync-t3
        run: npm ci
        
      - name: Run tests
        working-directory: ./utility-sync-t3
        run: npm test
        
      - name: Upload coverage
        uses: actions/upload-artifact@v4
        with:
          name: coverage
          path: utility-sync-t3/coverage/

  # Optional: Add PR comment with results
  comment:
    runs-on: ubuntu-latest
    needs: [lint-and-type, security, test]
    if: github.event_name == 'pull_request'
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '✅ Automated checks passed!'
            })