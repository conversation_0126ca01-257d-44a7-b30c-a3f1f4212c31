-- Initialize PostGIS extensions and configure database for CLEAR application
-- This script runs automatically when the PostgreSQL container starts

-- Create PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS postgis_tiger_geocoder;

-- Create extensions for performance and monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS pg_buffercache;

-- Set timezone
SET timezone = 'UTC';

-- Configure PostGIS settings for optimal performance
-- Increase work_mem for complex spatial operations
ALTER SYSTEM SET work_mem = '256MB';
ALTER SYSTEM SET maintenance_work_mem = '1GB';
ALTER SYSTEM SET shared_buffers = '512MB';
ALTER SYSTEM SET effective_cache_size = '2GB';

-- Optimize for spatial operations
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET seq_page_cost = 1.0;

-- Enable query planning for spatial operations
ALTER SYSTEM SET enable_seqscan = on;
ALTER SYSTEM SET enable_indexscan = on;
ALTER SYSTEM SET enable_bitmapscan = on;

-- Configure checkpoints for write-heavy workloads
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET checkpoint_segments = 32;

-- Enable logging for performance monitoring
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_duration = on;
ALTER SYSTEM SET log_min_duration_statement = 1000; -- Log queries taking more than 1 second
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_lock_waits = on;

-- Reload configuration
SELECT pg_reload_conf();

-- Create indexes for common PostGIS operations
-- Note: Application-specific indexes will be created by Prisma migrations

-- Grant necessary permissions for the application user
-- These will be created when the application connects

-- Create a function to optimize spatial indexes
CREATE OR REPLACE FUNCTION optimize_spatial_indexes()
RETURNS void AS $$
BEGIN
    -- Analyze all tables with spatial data
    ANALYZE;
    
    -- Update spatial statistics
    PERFORM UpdateGeometrySRID('public', 'utility_line_data', 'geometry', 4326);
    
    -- Log optimization
    RAISE NOTICE 'Spatial indexes optimized at %', now();
END;
$$ LANGUAGE plpgsql;

-- Create a function to monitor spatial query performance
CREATE OR REPLACE FUNCTION spatial_query_stats()
RETURNS TABLE(
    query_text text,
    calls bigint,
    total_time double precision,
    mean_time double precision,
    min_time double precision,
    max_time double precision
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.query,
        s.calls,
        s.total_exec_time,
        s.mean_exec_time,
        s.min_exec_time,
        s.max_exec_time
    FROM pg_stat_statements s
    WHERE s.query ILIKE '%ST_%' -- Spatial operations
       OR s.query ILIKE '%PostGIS%'
       OR s.query ILIKE '%geometry%'
    ORDER BY s.total_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- Create monitoring views
CREATE OR REPLACE VIEW spatial_table_stats AS
SELECT 
    schemaname,
    tablename,
    attname as geometry_column,
    n_distinct,
    correlation,
    most_common_vals
FROM pg_stats 
WHERE attname LIKE '%geom%' 
   OR attname LIKE '%geometry%'
ORDER BY schemaname, tablename;

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'PostGIS database initialization completed successfully at %', now();
END $$;