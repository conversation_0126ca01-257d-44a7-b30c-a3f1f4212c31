/**
 * Cryptography Security Tests
 * Tests the security improvements made to fix SEC-20 and SEC-18
 */
import { describe, test, expect } from 'vitest';
import * as argon2 from 'argon2';
import { 
  secureRandom, 
  secureRandomInt, 
  generateSecurePassword,
  encryptPII,
  decryptPII,
  hashForSearch,
  generateSessionId,
  generateA<PERSON><PERSON><PERSON>
} from '../lib/crypto-utils';
import { 
  encryptPIIData, 
  decryptPIIData, 
  createSearchHash 
} from '../lib/pii-encryption';

describe('Cryptography Security Improvements', () => {
  
  describe('SEC-20: Argon2 Password Hashing', () => {
    test('should use argon2 for password hashing', async () => {
      const password = 'TestPassword123!';
      const hash = await argon2.hash(password);
      
      // Verify the hash was created
      expect(hash).toBeDefined();
      expect(hash.length).toBeGreaterThan(0);
      expect(hash).toContain('$argon2'); // Verify it's an argon2 hash
      
      // Verify it can be compared
      const isValid = await argon2.verify(hash, password);
      expect(isValid).toBe(true);
      
      // Verify wrong password fails
      const isInvalid = await argon2.verify(hash, 'WrongPassword');
      expect(isInvalid).toBe(false);
    });

    test('should use secure argon2 parameters', async () => {
      const password = 'TestPassword123!';
      const hash = await argon2.hash(password);
      
      // Argon2 uses better defaults than bcrypt
      expect(hash).toContain('argon2id'); // Should use argon2id variant
    });
  });

  describe('SEC-18: Replace Math.random() with crypto.randomBytes()', () => {
    test('secureRandom should generate different values', () => {
      const values = Array.from({ length: 100 }, () => secureRandom());
      const uniqueValues = new Set(values);
      
      // Should generate unique values (with high probability)
      expect(uniqueValues.size).toBeGreaterThan(95);
      
      // Should be within [0, 1) range
      values.forEach(value => {
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThan(1);
      });
    });

    test('secureRandomInt should generate values within range', () => {
      const min = 10;
      const max = 50;
      const values = Array.from({ length: 100 }, () => secureRandomInt(min, max));
      
      values.forEach(value => {
        expect(value).toBeGreaterThanOrEqual(min);
        expect(value).toBeLessThan(max);
        expect(Number.isInteger(value)).toBe(true);
      });
      
      // Should generate different values
      const uniqueValues = new Set(values);
      expect(uniqueValues.size).toBeGreaterThan(10);
    });

    test('generateSecurePassword should create complex passwords', () => {
      const password = generateSecurePassword(12);
      
      expect(password).toBeDefined();
      expect(password.length).toBe(12);
      
      // Should contain at least one of each character type
      expect(/[a-z]/.test(password)).toBe(true); // lowercase
      expect(/[A-Z]/.test(password)).toBe(true); // uppercase
      expect(/[0-9]/.test(password)).toBe(true); // numbers
      expect(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)).toBe(true); // symbols
      
      // Should generate different passwords
      const password2 = generateSecurePassword(12);
      expect(password).not.toBe(password2);
    });

    test('generateSessionId should create unique IDs', () => {
      const sessionIds = Array.from({ length: 10 }, () => generateSessionId());
      const uniqueIds = new Set(sessionIds);
      
      expect(uniqueIds.size).toBe(10);
      
      sessionIds.forEach(id => {
        expect(id).toBeDefined();
        expect(id.length).toBe(64); // 32 bytes * 2 (hex)
        expect(/^[a-f0-9]+$/.test(id)).toBe(true); // hex format
      });
    });

    test('generateApiKey should create secure API keys', () => {
      const apiKey1 = generateApiKey();
      const apiKey2 = generateApiKey();
      
      // Should have correct structure
      expect(apiKey1.prefix).toBeDefined();
      expect(apiKey1.key).toBeDefined();
      expect(apiKey1.hash).toBeDefined();
      
      expect(apiKey1.prefix.length).toBe(8); // 4 bytes * 2 (hex)
      expect(apiKey1.key).toMatch(/^[a-f0-9]{8}_[a-f0-9]{56}$/); // prefix_keypart format
      expect(apiKey1.hash.length).toBe(64); // 32 bytes * 2 (hex)
      
      // Should be unique
      expect(apiKey1.key).not.toBe(apiKey2.key);
      expect(apiKey1.hash).not.toBe(apiKey2.hash);
    });
  });

  describe('SEC-20: Field-level PII Encryption', () => {
    const testMasterKey = 'test-master-key-for-encryption-testing-123456789';
    
    // Mock environment variable
    beforeAll(() => {
      process.env.PII_ENCRYPTION_KEY = testMasterKey;
    });

    test('should encrypt and decrypt PII data correctly', () => {
      const originalData = '<EMAIL>';
      
      const encrypted = encryptPII(originalData, testMasterKey);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(originalData);
      expect(encrypted.length).toBeGreaterThan(originalData.length);
      
      const decrypted = decryptPII(encrypted, testMasterKey);
      expect(decrypted).toBe(originalData);
    });

    test('should handle empty or invalid data gracefully', () => {
      expect(() => encryptPII('', testMasterKey)).toThrow();
      expect(() => encryptPII('test', '')).toThrow();
      expect(() => decryptPII('', testMasterKey)).toThrow();
      expect(() => decryptPII('invalid-data', testMasterKey)).toThrow();
    });

    test('should create consistent search hashes', () => {
      const email = '<EMAIL>';
      const hash1 = hashForSearch(email);
      const hash2 = hashForSearch(email);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toBeDefined();
      expect(hash1.length).toBeGreaterThan(0);
      
      // Different emails should produce different hashes
      const hash3 = hashForSearch('<EMAIL>');
      expect(hash1).not.toBe(hash3);
    });

    test('should encrypt PII fields in user data', () => {
      const userData = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        role: 'user'
      };
      
      const encryptedData = encryptPIIData(userData, 'users');
      
      // PII fields should be encrypted
      expect(encryptedData.email).not.toBe(userData.email);
      expect(encryptedData.first_name).not.toBe(userData.first_name);
      expect(encryptedData.last_name).not.toBe(userData.last_name);
      
      // Non-PII fields should remain unchanged
      expect(encryptedData.id).toBe(userData.id);
      expect(encryptedData.username).toBe(userData.username);
      expect(encryptedData.role).toBe(userData.role);
      
      // Should have search hashes for indexed fields
      expect(encryptedData.email_hash).toBeDefined();
    });

    test('should decrypt PII fields in user data', () => {
      const userData = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        role: 'user'
      };
      
      const encryptedData = encryptPIIData(userData, 'users');
      const decryptedData = decryptPIIData(encryptedData, 'users');
      
      // PII fields should be decrypted back to original
      expect(decryptedData.email).toBe(userData.email);
      expect(decryptedData.first_name).toBe(userData.first_name);
      expect(decryptedData.last_name).toBe(userData.last_name);
      
      // Non-PII fields should remain unchanged
      expect(decryptedData.id).toBe(userData.id);
      expect(decryptedData.username).toBe(userData.username);
      expect(decryptedData.role).toBe(userData.role);
    });

    test('should handle non-PII tables without modification', () => {
      const projectData = {
        id: 'proj-1',
        name: 'Test Project',
        description: 'A test project'
      };
      
      const encryptedData = encryptPIIData(projectData, 'projects');
      expect(encryptedData).toEqual(projectData);
      
      const decryptedData = decryptPIIData(encryptedData, 'projects');
      expect(decryptedData).toEqual(projectData);
    });

    test('should create search hashes consistently', () => {
      const email = '<EMAIL>';
      const hash1 = createSearchHash(email, 'email');
      const hash2 = createSearchHash(email, 'email');
      
      expect(hash1).toBe(hash2);
      
      // Different field types should produce different hashes
      const hash3 = createSearchHash(email, 'name');
      expect(hash1).not.toBe(hash3);
    });
  });

  describe('General Security Properties', () => {
    test('should not expose sensitive data in error messages', () => {
      const testMasterKey = 'test-key';
      
      try {
        decryptPII('invalid-encrypted-data', testMasterKey);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        expect(errorMessage).not.toContain(testMasterKey);
        expect(errorMessage).not.toContain('invalid-encrypted-data');
      }
    });

    test('should use different salts for different operations', () => {
      const data = '<EMAIL>';
      const searchHash = hashForSearch(data, 'email_search');
      const nameSearchHash = hashForSearch(data, 'name_search');
      
      expect(searchHash).not.toBe(nameSearchHash);
    });
  });
});