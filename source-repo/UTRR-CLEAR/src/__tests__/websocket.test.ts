import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { WebSocket } from 'ws';
import type { Server as WebSocketServer } from 'ws';

// Mock WebSocket implementation for testing
class MockWebSocket {
  private listeners: Map<string, Function[]> = new Map();
  public readyState: number = WebSocket.CONNECTING;
  public url: string;
  
  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      this.emit('open');
    }, 10);
  }
  
  addEventListener(event: string, handler: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);
  }
  
  removeEventListener(event: string, handler: Function) {
    const handlers = this.listeners.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
  
  emit(event: string, data?: any) {
    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }
  
  send(data: string | ArrayBuffer) {
    if (this.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // Echo back for testing
    setTimeout(() => {
      this.emit('message', { data });
    }, 5);
  }
  
  close(code?: number, reason?: string) {
    this.readyState = WebSocket.CLOSING;
    setTimeout(() => {
      this.readyState = WebSocket.CLOSED;
      this.emit('close', { code, reason });
    }, 5);
  }
}

// WebSocket client wrapper for testing
class WebSocketClient {
  private ws: MockWebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;
  private messageQueue: string[] = [];
  private listeners: Map<string, Function[]> = new Map();
  
  constructor(private url: string) {}
  
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.ws = new MockWebSocket(this.url);
      
      this.ws.addEventListener('open', () => {
        this.reconnectAttempts = 0;
        this.flushMessageQueue();
        resolve();
      });
      
      this.ws.addEventListener('close', (event: any) => {
        this.emit('disconnected', event);
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnect();
        }
      });
      
      this.ws.addEventListener('error', (error: any) => {
        this.emit('error', error);
        reject(error);
      });
      
      this.ws.addEventListener('message', (event: any) => {
        this.emit('message', event.data);
      });
    });
  }
  
  private reconnect() {
    this.reconnectAttempts++;
    setTimeout(() => {
      this.connect().catch(() => {
        // Ignore reconnection errors
      });
    }, this.reconnectDelay * this.reconnectAttempts);
  }
  
  send(message: string) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(message);
    } else {
      this.messageQueue.push(message);
    }
  }
  
  private flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.send(message);
      }
    }
  }
  
  on(event: string, handler: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);
  }
  
  off(event: string, handler: Function) {
    const handlers = this.listeners.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
  
  private emit(event: string, data?: any) {
    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
  
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

describe('WebSocket Connection Tests', () => {
  let client: WebSocketClient;
  
  beforeAll(() => {
    // Mock WebSocket in global scope
    (global as any).WebSocket = MockWebSocket;
  });
  
  afterAll(() => {
    delete (global as any).WebSocket;
  });
  
  it('should connect to WebSocket server', async () => {
    client = new WebSocketClient('ws://localhost:3001');
    await client.connect();
    expect(client.isConnected).toBe(true);
  });
  
  it('should send and receive messages', async () => {
    client = new WebSocketClient('ws://localhost:3001');
    await client.connect();
    
    const receivedMessages: string[] = [];
    client.on('message', (data: string) => {
      receivedMessages.push(data);
    });
    
    client.send('Hello, WebSocket!');
    
    // Wait for echo
    await new Promise(resolve => setTimeout(resolve, 20));
    
    expect(receivedMessages).toContain('Hello, WebSocket!');
  });
  
  it('should queue messages when disconnected', () => {
    client = new WebSocketClient('ws://localhost:3001');
    
    // Send message before connection
    client.send('Queued message 1');
    client.send('Queued message 2');
    
    const receivedMessages: string[] = [];
    client.on('message', (data: string) => {
      receivedMessages.push(data);
    });
    
    // Connect and check if queued messages are sent
    return client.connect().then(async () => {
      await new Promise(resolve => setTimeout(resolve, 50));
      expect(receivedMessages).toContain('Queued message 1');
      expect(receivedMessages).toContain('Queued message 2');
    });
  });
  
  it('should handle reconnection', async () => {
    client = new WebSocketClient('ws://localhost:3001');
    await client.connect();
    
    let disconnectCount = 0;
    client.on('disconnected', () => {
      disconnectCount++;
    });
    
    // Force disconnect
    client.disconnect();
    
    await new Promise(resolve => setTimeout(resolve, 50));
    expect(disconnectCount).toBe(1);
  });
  
  it('should handle JSON messages', async () => {
    client = new WebSocketClient('ws://localhost:3001');
    await client.connect();
    
    const receivedData: any[] = [];
    client.on('message', (data: string) => {
      try {
        receivedData.push(JSON.parse(data));
      } catch {
        // Ignore non-JSON messages
      }
    });
    
    const testData = { type: 'test', payload: { value: 42 } };
    client.send(JSON.stringify(testData));
    
    await new Promise(resolve => setTimeout(resolve, 20));
    
    expect(receivedData).toHaveLength(1);
    expect(receivedData[0]).toEqual(testData);
  });
});

describe('WebSocket Real-time Features', () => {
  it('should handle real-time notifications', async () => {
    const notificationClient = new WebSocketClient('ws://localhost:3001/notifications');
    await notificationClient.connect();
    
    const notifications: any[] = [];
    notificationClient.on('message', (data: string) => {
      const parsed = JSON.parse(data);
      if (parsed.type === 'notification') {
        notifications.push(parsed);
      }
    });
    
    // Simulate notification
    notificationClient.send(JSON.stringify({
      type: 'notification',
      payload: {
        title: 'New Project Update',
        message: 'Project X has been updated',
        timestamp: new Date().toISOString(),
      },
    }));
    
    await new Promise(resolve => setTimeout(resolve, 20));
    
    expect(notifications).toHaveLength(1);
    expect(notifications[0].payload.title).toBe('New Project Update');
  });
  
  it('should handle collaborative editing', async () => {
    const editor1 = new WebSocketClient('ws://localhost:3001/collab');
    const editor2 = new WebSocketClient('ws://localhost:3001/collab');
    
    await Promise.all([editor1.connect(), editor2.connect()]);
    
    const changes1: any[] = [];
    const changes2: any[] = [];
    
    editor1.on('message', (data: string) => {
      const parsed = JSON.parse(data);
      if (parsed.type === 'change') {
        changes1.push(parsed);
      }
    });
    
    editor2.on('message', (data: string) => {
      const parsed = JSON.parse(data);
      if (parsed.type === 'change') {
        changes2.push(parsed);
      }
    });
    
    // Editor 1 makes a change
    editor1.send(JSON.stringify({
      type: 'change',
      payload: {
        documentId: 'doc123',
        change: { insert: 'Hello ', position: 0 },
        userId: 'user1',
      },
    }));
    
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // Both editors should receive the change
    expect(changes1.length).toBeGreaterThan(0);
    expect(changes2.length).toBeGreaterThan(0);
  });
});

describe('WebSocket Error Handling', () => {
  it('should handle connection errors gracefully', async () => {
    const errorClient = new WebSocketClient('ws://invalid-host:9999');
    
    let errorOccurred = false;
    errorClient.on('error', () => {
      errorOccurred = true;
    });
    
    try {
      // Mock connection failure
      (global as any).WebSocket = class extends MockWebSocket {
        constructor(url: string) {
          super(url);
          setTimeout(() => {
            this.emit('error', new Error('Connection failed'));
          }, 5);
        }
      };
      
      await errorClient.connect();
    } catch (error) {
      expect(error).toBeDefined();
    }
    
    // Restore mock
    (global as any).WebSocket = MockWebSocket;
  });
  
  it('should handle invalid message formats', async () => {
    const client = new WebSocketClient('ws://localhost:3001');
    await client.connect();
    
    let errorCount = 0;
    client.on('message', (data: string) => {
      try {
        JSON.parse(data);
      } catch {
        errorCount++;
      }
    });
    
    // Send invalid JSON
    client.send('Invalid JSON {{{');
    
    await new Promise(resolve => setTimeout(resolve, 20));
    
    expect(errorCount).toBe(1);
  });
});