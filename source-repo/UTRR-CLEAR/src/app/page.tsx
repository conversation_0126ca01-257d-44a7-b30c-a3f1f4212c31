import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';

export default async function HomePage() {
  // Pre-fetch organization data for potential use
  void api.organizations.getPublic.prefetch();

  return (
    <HydrateClient>
      <div>
        {/* Redirect to auth signin - no public homepage */}
        {redirect('/auth/signin')}
      </div>
    </HydrateClient>
  );
}