import { NextRequest } from 'next/server';
import { auth<PERSON><PERSON><PERSON> } from './auth-wrapper';

// Next.js 15 requires proper typing for route handlers
export async function GET(
  request: NextRequest, 
  { params }: { params: Promise<{ nextauth: string[] }> }
) {
  const resolvedParams = await params;
  return authHandler(request, { params: resolvedParams });
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ nextauth: string[] }> }
) {
  const resolvedParams = await params;
  return authHandler(request, { params: resolvedParams });
}
