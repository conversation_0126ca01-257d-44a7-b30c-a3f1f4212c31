import { NextRequest, NextResponse } from 'next/server';
import { applyRateLimits } from '~/lib/rate-limit';

// Rate limit specific auth endpoints
const authRateLimits: Record<string, keyof typeof import('~/lib/rate-limit').rateLimitConfigs> = {
  '/api/auth/signin': 'login',
  '/api/auth/callback/credentials': 'login',
  '/api/auth/signout': 'auth',
  '/api/auth/session': 'api',
};

export async function authHandler(
  request: NextRequest,
  context: { params: { nextauth: string[] } }
) {
  const pathname = request.nextUrl.pathname;
  
  // Determine rate limit config based on pathname
  let rateLimitConfig: keyof typeof import('~/lib/rate-limit').rateLimitConfigs = 'auth';
  for (const [path, config] of Object.entries(authRateLimits)) {
    if (pathname.startsWith(path)) {
      rateLimitConfig = config;
      break;
    }
  }
  
  // Get user ID if authenticated (since we use Supabase now, this is always null)
  let userId: string | null = null;
  // Note: With Supabase migration, we rely on IP-based rate limiting
  
  // Apply rate limiting
  const rateLimitResult = await applyRateLimits(request, rateLimitConfig, userId);
  
  if (!rateLimitResult.success) {
    return new NextResponse('Too Many Requests', {
      status: 429,
      headers: {
        'X-RateLimit-Limit': rateLimitResult.limit.toString(),
        'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
        'X-RateLimit-Reset': new Date(rateLimitResult.reset).toISOString(),
        'Retry-After': rateLimitResult.retryAfter?.toString() ?? '60',
      },
    });
  }
  
  // Extract IP for auth logging
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
              request.headers.get('x-real-ip') || 
              'unknown';
  
  // Clone request and add IP to body for credentials
  if (request.method === 'POST' && pathname.includes('/callback/credentials')) {
    const body = await request.text();
    const newRequest = new Request(request.url, {
      method: request.method,
      headers: request.headers,
      body: body.replace('"}', `","ip":"${ip}"}"`),
    });
    request = newRequest as NextRequest;
  }
  
  // Since we've migrated to Supabase, return a placeholder response
  const response = new NextResponse(
    JSON.stringify({ message: 'Authentication handled by Supabase' }), 
    { status: 200, headers: { 'Content-Type': 'application/json' } }
  );
  
  // Clone response and add headers
  const newHeaders = new Headers(response.headers);
  newHeaders.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
  newHeaders.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
  newHeaders.set('X-RateLimit-Reset', new Date(rateLimitResult.reset).toISOString());
  
  return new NextResponse(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders,
  });
}