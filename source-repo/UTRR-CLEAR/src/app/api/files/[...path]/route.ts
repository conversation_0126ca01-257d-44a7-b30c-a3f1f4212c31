import { NextRequest, NextResponse } from 'next/server';
import { getServerAuthSession } from '~/server/auth';
import { db } from '~/server/db';
import fs from 'fs/promises';
import path from 'path';
import { stat } from 'fs/promises';
import { safeLog } from '~/lib/error-handler';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // Check authentication
    const session = await getServerAuthSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Await params and reconstruct file path
    const resolvedParams = await params;
    const requestedPath = decodeURIComponent(resolvedParams.path.join('/'));
    
    // Enhanced security check: prevent directory traversal
    // 1. Check for common path traversal patterns
    const pathTraversalPatterns = [
      '..',
      '..\\',
      '../',
      '..%2F',
      '..%5C',
      '%2E%2E',
      '~',
      './',
      '.\\',
      '\\\\',
      '//',
    ];
    
    const normalizedPath = requestedPath.replace(/\\/g, '/');
    for (const pattern of pathTraversalPatterns) {
      if (normalizedPath.includes(pattern)) {
        return NextResponse.json({ error: 'Invalid file path' }, { status: 400 });
      }
    }
    
    // 2. Ensure the path doesn't start with / or contain absolute paths
    if (normalizedPath.startsWith('/') || /^[a-zA-Z]:/.test(normalizedPath)) {
      return NextResponse.json({ error: 'Invalid file path' }, { status: 400 });
    }
    
    const filePath = normalizedPath;

    // Check if file exists in database
    const document = await db.project_documents.findFirst({
      where: {
        file_path: filePath,
      },
      include: {
        projects: true,
      },
    });

    if (document) {
      // Check permissions for project documents
      if (!session.user.isAdmin) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }

    // Construct full file path with security validation
    const baseDir = path.resolve(process.cwd(), 'public');
    const fullPath = path.resolve(baseDir, filePath);
    
    // 3. Ensure the resolved path is within the allowed directory
    if (!fullPath.startsWith(baseDir)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    try {
      // Check if file exists
      const fileStats = await stat(fullPath);
      
      if (!fileStats.isFile()) {
        return NextResponse.json({ error: 'File not found' }, { status: 404 });
      }

      // Read file
      const fileBuffer = await fs.readFile(fullPath);

      // Determine content type
      const ext = path.extname(filePath).toLowerCase();
      const contentTypeMap: Record<string, string> = {
        '.pdf': 'application/pdf',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.dwg': 'application/acad',
        '.dxf': 'application/dxf',
        '.txt': 'text/plain',
        '.csv': 'text/csv',
      };

      const contentType = contentTypeMap[ext] || 'application/octet-stream';

      // Return file with appropriate headers
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileStats.size.toString(),
          'Cache-Control': 'private, max-age=3600', // Cache for 1 hour
          'Content-Disposition': `inline; filename="${path.basename(filePath)}"`,
        },
      });

    } catch (fileError) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

  } catch (error) {
    safeLog.error('File serving error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  // Same logic as GET but only return headers
  const getResponse = await GET(request, { params });
  
  if (getResponse.status === 200) {
    return new NextResponse(null, {
      status: 200,
      headers: getResponse.headers,
    });
  }
  
  return new NextResponse(null, { status: getResponse.status });
}