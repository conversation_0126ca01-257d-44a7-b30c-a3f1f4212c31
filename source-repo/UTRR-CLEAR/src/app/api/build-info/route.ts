import { NextResponse } from 'next/server';
import { BUILD_HASH, GENERATED_AT } from '~/lib/build-hash';
import { safeLog } from '~/lib/error-handler';

export async function GET() {
  try {
    return NextResponse.json({
      buildHash: BUILD_HASH,
      deployedAt: GENERATED_AT,
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    safeLog.error('Error in build-info endpoint:', { error: String(error) });
    return NextResponse.json(
      { error: 'Failed to get build info' },
      { status: 500 }
    );
  }
}
