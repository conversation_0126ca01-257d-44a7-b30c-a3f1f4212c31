import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  // Check if database is available during build
  if (!process.env.DATABASE_URL) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 503 }
    );
  }

  // Dynamic imports to avoid build-time issues
  const { auth } = await import('~/server/auth');
  const { queryMonitor, getPerformanceReport, exportMetricsToCSV } = await import('~/lib/query-monitor');
  
  // Check authentication
  const session = await auth();
  if (!session || session.user.role !== 'ADMIN') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { searchParams } = new URL(req.url);
  const format = searchParams.get('format') || 'json';
  const hours = parseInt(searchParams.get('hours') || '24', 10);
  
  if (format === 'csv') {
    const csv = exportMetricsToCSV();
    return new NextResponse(csv, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="query-metrics-${new Date().toISOString()}.csv"`,
      },
    });
  }
  
  if (format === 'report') {
    const report = getPerformanceReport(hours);
    return new NextResponse(report, {
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
  
  // Default JSON format
  const start = new Date();
  start.setHours(start.getHours() - hours);
  const end = new Date();
  
  const stats = queryMonitor.getStats({ start, end });
  
  return NextResponse.json({
    timeRange: {
      start: start.toISOString(),
      end: end.toISOString(),
      hours,
    },
    stats,
  });
}

export async function DELETE(req: NextRequest) {
  // Check if database is available during build
  if (!process.env.DATABASE_URL) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 503 }
    );
  }

  // Dynamic imports to avoid build-time issues
  const { auth } = await import('~/server/auth');
  const { queryMonitor } = await import('~/lib/query-monitor');
  
  // Check authentication
  const session = await auth();
  if (!session || session.user.role !== 'ADMIN') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Reset query metrics
  queryMonitor.reset();
  
  return NextResponse.json({ message: 'Query metrics reset successfully' });
}