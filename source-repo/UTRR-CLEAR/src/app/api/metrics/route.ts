import { NextResponse } from 'next/server';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';
import { safeLog } from '~/lib/error-handler';

// Initialize Prometheus metrics
collectDefaultMetrics({ prefix: 'utilitysync_' });

// Custom metrics
const httpRequestsTotal = new Counter({
  name: 'utilitysync_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'path', 'status_code'],
});

const httpRequestDuration = new Histogram({
  name: 'utilitysync_http_request_duration_ms',
  help: 'Duration of HTTP requests in ms',
  labelNames: ['method', 'path', 'status_code'],
  buckets: [0.1, 5, 15, 50, 100, 500, 1000, 5000],
});

const activeUsers = new Gauge({
  name: 'utilitysync_active_users',
  help: 'Number of active users',
});

const totalProjects = new Gauge({
  name: 'utilitysync_total_projects',
  help: 'Total number of projects',
});

const activeProjects = new Gauge({
  name: 'utilitysync_active_projects',
  help: 'Number of active projects',
  labelNames: ['status'],
});

const totalConflicts = new Gauge({
  name: 'utilitysync_total_conflicts',
  help: 'Total number of conflicts',
  labelNames: ['severity', 'status'],
});

const databaseConnections = new Gauge({
  name: 'utilitysync_database_connections',
  help: 'Number of active database connections',
});

// Update metrics function
async function updateMetrics() {
  try {
    // Only update metrics if DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      return;
    }

    const { db } = await import('~/server/db');
    
    // Update user metrics
    const activeSessions = await db.sessions.count({
      where: {
        expires_at: {
          gt: new Date(),
        },
      },
    });
    activeUsers.set(activeSessions);

    // Update project metrics
    const projectStats = await db.projects.groupBy({
      by: ['rag_status'],
      _count: true,
    });

    let totalProjectCount = 0;
    projectStats.forEach((stat: any) => {
      activeProjects.labels(stat.rag_status || 'unknown').set(stat._count);
      totalProjectCount += stat._count;
    });
    totalProjects.set(totalProjectCount);

    // Update conflict metrics
    const conflictStats = await db.conflicts.groupBy({
      by: ['priority', 'status'],
      _count: true,
    });

    conflictStats.forEach((stat: any) => {
      totalConflicts
        .labels(stat.priority || 'unknown', stat.status || 'unknown')
        .set(stat._count);
    });

    // Database connection metrics (simplified)
    const poolStats = await db.$queryRaw<[{ count: bigint }]>`
      SELECT count(*) FROM pg_stat_activity WHERE datname = current_database()
    `;
    databaseConnections.set(Number(poolStats[0].count));
  } catch (error) {
    safeLog.error('Error updating metrics:', { error: String(error) });
  }
}

// Export metrics endpoint
export async function GET() {
  try {
    // Update metrics before returning
    await updateMetrics();
    
    // Get metrics
    const metrics = await register.metrics();
    
    return new NextResponse(metrics, {
      headers: {
        'Content-Type': register.contentType,
      },
    });
  } catch (error) {
    safeLog.error('Error generating metrics:', { error: String(error) });
    return new NextResponse('Error generating metrics', { status: 500 });
  }
}

// Export metrics for use in middleware
// Note: Only route handlers (GET, POST, etc.) should be exported from route.ts files
// Metrics are available through the register imported from prom-client