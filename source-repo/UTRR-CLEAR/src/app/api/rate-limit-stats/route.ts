import { NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';
import { safeLog } from '~/lib/error-handler';

// Get rate limiting statistics
export async function GET() {
  try {
    const redis = Redis.fromEnv();
    
    // Get all rate limit keys
    const keys = await redis.keys('@upstash/ratelimit/*');
    
    const stats = {
      totalKeys: keys.length,
      byType: {
        api: 0,
        bot: 0,
        auth: 0,
        upload: 0,
        expensive: 0,
      },
      blockedIPs: [] as string[],
      recentActivity: [] as Array<{
        key: string;
        ttl: number;
        hits: unknown;
      }>,
    };
    
    // Analyze keys
    for (const key of keys) {
      if (key.includes('/api/')) stats.byType.api++;
      else if (key.includes('/bot/')) stats.byType.bot++;
      else if (key.includes('/auth/')) stats.byType.auth++;
      else if (key.includes('/upload/')) stats.byType.upload++;
      else if (key.includes('/expensive/')) stats.byType.expensive++;
      
      // Get TTL to see if recently active
      const ttl = await redis.ttl(key);
      if (ttl > 0 && ttl < 3600) { // Active in last hour
        const value = await redis.get(key);
        stats.recentActivity.push({
          key: key.replace('@upstash/ratelimit/', ''),
          ttl,
          hits: value,
        });
      }
    }
    
    // Sort by most recent activity
    stats.recentActivity.sort((a: any, b: any) => a.ttl - b.ttl);
    stats.recentActivity = stats.recentActivity.slice(0, 20); // Top 20
    
    return NextResponse.json({
      success: true,
      stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    safeLog.error('Rate limit stats error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Failed to fetch rate limit statistics' },
      { status: 500 }
    );
  }
}