import { NextResponse } from 'next/server';
import { createServerClient } from '~/lib/supabase/server';
import { getServerAuthSession } from '~/server/auth';

export async function GET() {
  try {
    // Get the session
    const session = await getServerAuthSession();
    const supabase = await createServerClient();
    
    // Get user info from auth.users
    let userInfo = null;
    if (session?.user?.id) {
      const { data: authUser } = await supabase
        .schema('auth')
        .from('users')
        .select('id, email, raw_user_meta_data')
        .eq('id', session.user.id)
        .single();
      
      userInfo = authUser;
    }
    
    // Test 1: Get projects with RLS (normal query)
    const { data: projectsWithRLS, error: rlsError, count: rlsCount } = await supabase
      .from('projects')
      .select('*', { count: 'exact' });
    
    // Test 2: Get projects without RLS (using service role if available)
    let projectsWithoutRLS = null;
    let noRlsError = null;
    let noRlsCount = null;
    
    // Try to bypass RLS by using service role key (only works if SUPABASE_SERVICE_ROLE_KEY is set)
    try {
      if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
        // Create a service role client manually
        const { createServerClient: createSupabaseServerClient } = await import('@supabase/ssr');
        const { cookies } = await import('next/headers');
        const cookieStore = await cookies();
        
        const serviceSupabase = createSupabaseServerClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!.trim(),
          process.env.SUPABASE_SERVICE_ROLE_KEY.trim(),
          {
            cookies: {
              getAll() {
                return cookieStore.getAll();
              },
              setAll() {
                // Service role doesn't need to set cookies
              },
            },
          }
        );
        
        const result = await serviceSupabase
          .from('projects')
          .select('*', { count: 'exact' });
        
        projectsWithoutRLS = result.data;
        noRlsError = result.error;
        noRlsCount = result.count;
      } else {
        noRlsError = { message: 'Service role key not available' };
      }
    } catch (e) {
      // Service role not available, which is expected in some environments
      noRlsError = { message: `Service role error: ${(e as Error).message}` };
    }
    
    // Test 3: Check if user is admin
    let isAdminCheck = null;
    try {
      const { data: adminResult } = await supabase
        .rpc('is_admin');
      isAdminCheck = adminResult;
    } catch (e) {
      isAdminCheck = { error: (e as Error).message };
    }
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      session: {
        userId: session?.user?.id || null,
        email: session?.user?.email || null,
      },
      authUser: userInfo,
      isAdmin: isAdminCheck,
      tests: {
        withRLS: {
          count: rlsCount,
          dataLength: projectsWithRLS?.length || 0,
          error: rlsError?.message || null,
          sampleProject: projectsWithRLS?.[0] || null,
        },
        withoutRLS: {
          count: noRlsCount,
          dataLength: projectsWithoutRLS?.length || 0,
          error: noRlsError?.message || null,
          sampleProject: projectsWithoutRLS?.[0] || null,
        },
      },
      debug: {
        hasSession: !!session,
        hasAuthUser: !!userInfo,
        userRole: userInfo?.raw_user_meta_data?.role || 'user',
      }
    });
  } catch (error) {
    console.error('Test projects error:', error);
    return NextResponse.json({
      success: false,
      error: (error as Error).message,
    }, { status: 500 });
  }
}