import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import { z } from 'zod';
import { safeLog } from '~/lib/error-handler';

// GitHub API configuration
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
const GITHUB_OWNER = process.env.GITHUB_OWNER || 'Craft1563';
const GITHUB_REPO = process.env.GITHUB_REPO || 'CLEAR-nextjs';

// Validation schema
const createIssueSchema = z.object({
  title: z.string().min(1).max(256),
  description: z.string(),
  type: z.enum(['feature', 'bug', 'enhancement', 'question']),
  priority: z.enum(['low', 'medium', 'high', 'critical']),
  category: z.string(),
  browserInfo: z.string().optional(),
  requestId: z.number().optional(),
  submittedBy: z.string().optional(),
  assignToCopilot: z.boolean().optional(),
});

// Map request types to GitHub labels
const typeToLabel: Record<string, string> = {
  feature: 'enhancement',
  bug: 'bug',
  enhancement: 'enhancement',
  question: 'question',
};

// Map priority to GitHub labels
const priorityToLabel: Record<string, string> = {
  low: 'priority: low',
  medium: 'priority: medium',
  high: 'priority: high',
  critical: 'priority: critical',
};

// Map categories to GitHub labels
const categoryToLabel: Record<string, string> = {
  'User Interface': 'ui',
  'Performance': 'performance',
  'Database': 'database',
  'Mapping/GIS': 'gis',
  'Project Management': 'project-management',
  'Reporting': 'reporting',
  'Integration': 'integration',
  'Authentication': 'auth',
  'Mobile/Responsive': 'mobile',
  'Documentation': 'documentation',
  'Other': 'other',
};

export async function POST(request: NextRequest) {
  try {
    // Skip authentication check for server-side calls
    // In production, you might want to use a secret token instead

    // Check if GitHub integration is configured
    if (!GITHUB_TOKEN) {
      safeLog.warn('GitHub token not configured');
      return NextResponse.json(
        { error: 'GitHub integration not configured' },
        { status: 501 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createIssueSchema.parse(body);

    // Prepare labels
    const labels = [
      typeToLabel[validatedData.type] || 'enhancement',
      priorityToLabel[validatedData.priority] || 'priority: medium',
      categoryToLabel[validatedData.category] || 'other',
      'from-app', // Mark issues created from the app
    ].filter(Boolean);

    // Format the issue body
    const issueBody = formatIssueBody({
      ...validatedData,
      submittedBy: validatedData.submittedBy || 'Unknown',
      submittedAt: new Date().toISOString(),
    });

    // Create GitHub issue
    const response = await fetch(
      `https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPO}/issues`,
      {
        method: 'POST',
        headers: {
          'Authorization': `token ${GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: validatedData.assignToCopilot 
            ? `[COPILOT] [${validatedData.type.toUpperCase()}] ${validatedData.title}`
            : `[${validatedData.type.toUpperCase()}] ${validatedData.title}`,
          body: issueBody,
          labels,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      // Log error without sensitive data
      safeLog.error('GitHub API error', { status: response.status, statusText: response.statusText });
      throw new Error(`GitHub API error: ${response.statusText}`);
    }

    const issue = await response.json();

    // Return the created issue
    return NextResponse.json({
      success: true,
      issueNumber: issue.number,
      issueUrl: issue.html_url,
      issueId: issue.id,
    });

  } catch (error) {
    safeLog.error('Error creating GitHub issue:', { error: String(error) });
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create GitHub issue' },
      { status: 500 }
    );
  }
}

function formatIssueBody(data: {
  description: string;
  type: string;
  priority: string;
  category: string;
  browserInfo?: string;
  submittedBy: string;
  submittedAt: string;
  requestId?: number;
}): string {
  return `## Description
${data.description}

## Details
- **Type**: ${data.type}
- **Priority**: ${data.priority}
- **Category**: ${data.category}
- **Submitted by**: ${data.submittedBy}
- **Submitted at**: ${data.submittedAt}
${data.requestId ? `- **App Request ID**: #${data.requestId}` : ''}

${data.browserInfo ? `## Browser Diagnostics
<details>
<summary>Click to expand diagnostics</summary>

\`\`\`
${data.browserInfo}
\`\`\`

</details>` : ''}

---
*This issue was automatically created from the CLEAR application feature request system.*
`;
}