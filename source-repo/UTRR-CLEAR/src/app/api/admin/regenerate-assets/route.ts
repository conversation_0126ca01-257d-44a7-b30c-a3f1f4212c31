import { NextRequest, NextResponse } from 'next/server';
import { getServerAuthSession } from '~/server/auth';
import { exec } from 'child_process';
import { promisify } from 'util';
import { safeLog } from '~/lib/error-handler';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerAuthSession();
    if (!session?.user?.isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Run the asset generation script
    const { stdout, stderr } = await execAsync('npm run generate:org-assets');
    
    if (stderr) {
      safeLog.error('Asset generation stderr:', { stderr });
    }
    
    safeLog.info('Asset generation stdout:', { stdout });
    
    return NextResponse.json({ 
      success: true, 
      message: 'Organization assets regenerated successfully',
      output: stdout
    });
    
  } catch (error) {
    safeLog.error('Failed to regenerate assets:', { error: String(error) });
    return NextResponse.json(
      { error: 'Failed to regenerate assets', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}