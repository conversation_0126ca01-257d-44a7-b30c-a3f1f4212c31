import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '~/lib/supabase/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  // Create a Supabase admin client for user management inside the function
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !serviceRoleKey) {
    return NextResponse.json({ 
      error: 'Server configuration error - Supabase not configured' 
    }, { status: 500 });
  }
  
  const supabaseAdmin = createClient(
    supabaseUrl,
    serviceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );
  try {
    // Check if current user is admin
    const supabase = await createServerClient();
    const { data: { user: currentUser } } = await supabase.auth.getUser();
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Verify admin role
    const adminRoles = ['admin', 'executive', 'department_manager'];
    const userRole = currentUser.user_metadata?.role;
    
    if (!adminRoles.includes(userRole)) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }
    
    // Get request data
    const body = await request.json();
    const { email, password, firstName, lastName, role, department, jobTitle } = body;
    
    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }
    
    // Get admin's organization ID
    const organizationId = currentUser.user_metadata?.organization_id;
    
    // Create user in auth.users with metadata
    const { data: newAuthUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email since admin is creating
      user_metadata: {
        full_name: `${firstName || ''} ${lastName || ''}`.trim(),
        first_name: firstName,
        last_name: lastName,
        role: role || 'utility_coordinator',
        department,
        job_title: jobTitle,
        organization_id: organizationId,
        created_by: currentUser.id,
        created_at: new Date().toISOString(),
      },
    });
    
    if (createError) {
      console.error('Error creating auth user:', createError);
      return NextResponse.json({ 
        error: createError.message || 'Failed to create user' 
      }, { status: 400 });
    }
    
    if (!newAuthUser || !newAuthUser.user) {
      return NextResponse.json({ 
        error: 'User creation failed - no user returned' 
      }, { status: 500 });
    }
    
    // Create user profile with same ID as auth user
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: newAuthUser.user.id, // Use auth.users.id as primary key
        email: newAuthUser.user.email,
        display_name: `${firstName || ''} ${lastName || ''}`.trim() || email.split('@')[0],
        first_name: firstName,
        last_name: lastName,
      });
    
    if (profileError) {
      console.error('Error creating user profile:', profileError);
      // Try to clean up auth user if profile creation fails
      await supabaseAdmin.auth.admin.deleteUser(newAuthUser.user.id);
      
      return NextResponse.json({ 
        error: 'Failed to create user profile' 
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      user: {
        id: newAuthUser.user.id,
        email: newAuthUser.user.email,
        role: role || 'utility_coordinator',
        created_at: newAuthUser.user.created_at,
      },
    });
    
  } catch (error) {
    console.error('Error in create user API:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}