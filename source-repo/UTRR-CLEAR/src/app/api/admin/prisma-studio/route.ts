import { NextRequest, NextResponse } from 'next/server';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { safeLog } from '~/lib/error-handler';

const execAsync = promisify(exec);

// Store the Prisma Studio process
let prismaStudioProcess: any = null;

export async function POST(request: NextRequest) {
  try {
    // Dynamic import to avoid build-time issues
    const { auth } = await import('~/server/auth');
    
    // Check authentication
    const session = await auth();
    
    if (!session?.user?.isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if database is available
    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 503 }
      );
    }

    const { action } = await request.json();

    if (action === 'start') {
      // Check if Prisma Studio is already running
      if (prismaStudioProcess) {
        return NextResponse.json({ 
          message: 'Prisma Studio is already running',
          status: 'running' 
        });
      }

      try {
        // Check if port 5555 is already in use
        try {
          await execAsync('lsof -ti:5555');
          // If we reach here, port is in use
          return NextResponse.json({ 
            message: 'Port 5555 is already in use. Prisma Studio may already be running.',
            status: 'error' 
          }, { status: 409 });
        } catch {
          // Port is free, continue
        }

        // Start Prisma Studio
        prismaStudioProcess = spawn('npx', ['prisma', 'studio', '--port', '5555'], {
          cwd: process.cwd(),
          detached: true,
          stdio: ['ignore', 'pipe', 'pipe']
        });

        // Handle process events
        prismaStudioProcess.on('error', (error: Error) => {
          safeLog.error('Prisma Studio error:', { error: error.message });
          prismaStudioProcess = null;
        });

        prismaStudioProcess.on('exit', (code: number) => {
          safeLog.info(`Prisma Studio process exited with code ${code}`);
          prismaStudioProcess = null;
        });

        // Give it a moment to start
        await new Promise(resolve => setTimeout(resolve, 1000));

        return NextResponse.json({ 
          message: 'Prisma Studio started successfully',
          status: 'starting',
          url: 'http://localhost:5555'
        });

      } catch (error) {
        safeLog.error('Failed to start Prisma Studio:', { error: String(error) });
        return NextResponse.json(
          { error: 'Failed to start Prisma Studio' },
          { status: 500 }
        );
      }
    }

    if (action === 'stop') {
      if (prismaStudioProcess) {
        try {
          // Kill the process group to ensure all child processes are terminated
          process.kill(-prismaStudioProcess.pid, 'SIGTERM');
          prismaStudioProcess = null;
          
          // Also try to kill any remaining processes on port 5555
          try {
            await execAsync('pkill -f "prisma studio"');
          } catch {
            // Ignore errors, process might not exist
          }

          return NextResponse.json({ 
            message: 'Prisma Studio stopped successfully',
            status: 'stopped' 
          });
        } catch (error) {
          safeLog.error('Failed to stop Prisma Studio:', { error: String(error) });
          return NextResponse.json(
            { error: 'Failed to stop Prisma Studio' },
            { status: 500 }
          );
        }
      } else {
        return NextResponse.json({ 
          message: 'Prisma Studio is not running',
          status: 'stopped' 
        });
      }
    }

    if (action === 'status') {
      try {
        // Check if port 5555 is in use
        await execAsync('lsof -ti:5555');
        return NextResponse.json({ 
          status: 'running',
          url: 'http://localhost:5555'
        });
      } catch {
        return NextResponse.json({ 
          status: 'stopped'
        });
      }
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    safeLog.error('Prisma Studio API error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Dynamic import to avoid build-time issues
    const { auth } = await import('~/server/auth');
    
    // Check authentication
    const session = await auth();
    
    if (!session?.user?.isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check status
    try {
      await execAsync('lsof -ti:5555');
      return NextResponse.json({ 
        status: 'running',
        url: 'http://localhost:5555'
      });
    } catch {
      return NextResponse.json({ 
        status: 'stopped'
      });
    }
  } catch (error) {
    safeLog.error('Prisma Studio status check error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}