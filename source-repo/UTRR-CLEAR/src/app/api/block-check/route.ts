import { NextRequest, NextResponse } from 'next/server';
import { createRateLimiter, isAIRequest, getRateLimitIdentifier } from '~/lib/rate-limit';
import { safeLog } from '~/lib/error-handler';

// Endpoint to check if an IP/user agent should be blocked
export async function POST(request: NextRequest) {
  try {
    const { ip, userAgent, headers } = await request.json();
    
    // Create a mock request to test patterns
    const mockRequest = new Request('https://example.com', {
      headers: new Headers({
        'user-agent': userAgent,
        ...headers,
      }),
    });
    
    const isBlocked = isAIRequest(mockRequest);
    const rateLimiter = createRateLimiter(isBlocked ? 'bot' : 'api');
    const identifier = ip ?? getRateLimitIdentifier(mockRequest);
    
    const { success, limit, reset, remaining } = await rateLimiter.limit(identifier);
    
    return NextResponse.json({
      blocked: !success || isBlocked,
      isAI: isBlocked,
      rateLimit: {
        limit,
        remaining,
        reset: new Date(reset).toISOString(),
      },
    });
  } catch (error) {
    safeLog.error('Block check error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Failed to check block status' },
      { status: 500 }
    );
  }
}