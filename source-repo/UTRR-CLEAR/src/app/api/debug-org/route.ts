import { NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import { db } from '~/server/db';

export async function GET() {
  try {
    const session = await auth() as SupabaseSession | null;
    const org = await db.organizations.findFirst();
    
    return NextResponse.json({
      session: {
        user: session?.user,
        expires_at: session?.expires_at // Use Supabase session property
      },
      organization: {
        id: org?.id,
        name: org?.name,
        setup_completed: org?.setup_completed
      }
    });
  } catch (error) {
    return NextResponse.json({ error: String(error) }, { status: 500 });
  }
}