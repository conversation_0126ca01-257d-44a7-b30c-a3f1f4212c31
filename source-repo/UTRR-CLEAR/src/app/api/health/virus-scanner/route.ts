import { NextResponse } from 'next/server';
import { virusScannerService } from '~/server/services/virus-scanner.service';

export async function GET() {
  try {
    const status = await virusScannerService.getStatus();
    
    return NextResponse.json({
      service: 'virus-scanner',
      status: status.available ? 'healthy' : 'unhealthy',
      details: status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        service: 'virus-scanner',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}