import { NextResponse } from 'next/server';
import { safeLog } from '~/lib/error-handler';

export async function GET() {
  try {
    // Only import db if DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({
        status: 'partial',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV,
        database: 'not_configured',
      });
    }

    const { db } = await import('~/server/db');
    // Check database connectivity
    await db.$queryRaw`SELECT 1`;

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '0.1.0',
      environment: process.env.NODE_ENV,
      database: 'connected',
    });
  } catch (error) {
    safeLog.error('Health check failed:', { error: String(error) });

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV,
        database: 'disconnected',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 }
    );
  }
}
