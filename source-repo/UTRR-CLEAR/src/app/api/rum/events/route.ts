import { NextRequest, NextResponse } from 'next/server';
import { db } from '~/server/db';
import { safeLog } from '~/lib/error-handler';

/**
 * RUM Events API Endpoint
 * 
 * Receives Real User Monitoring data from the client and stores it
 * for analysis and performance monitoring.
 */

interface RUMPayload {
  sessionId: string;
  userId?: string;
  events: RUMEvent[];
  sessionInfo: UserSession;
  deviceInfo: DeviceInfo;
}

interface RUMEvent {
  type: 'performance' | 'error' | 'user-action' | 'page-view' | 'custom';
  timestamp: number;
  sessionId: string;
  userId?: string;
  page: string;
  data: Record<string, any>;
}

interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  lastActivity: number;
  pageViews: string[];
  totalDuration: number;
  device: DeviceInfo;
}

interface DeviceInfo {
  type: 'desktop' | 'mobile' | 'tablet';
  os: string;
  browser: string;
  screenSize: string;
  connection?: string;
}

export async function POST(request: NextRequest) {
  try {
    const payload: RUMPayload = await request.json();
    
    // Validate payload
    if (!payload.sessionId || !payload.events || !Array.isArray(payload.events)) {
      return NextResponse.json(
        { error: 'Invalid payload structure' },
        { status: 400 }
      );
    }

    // Get client IP and user agent
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Process events in batch
    const processedEvents = await Promise.all(
      payload.events.map(event => processRUMEvent(event, clientIP, userAgent, payload))
    );

    // Store session information
    await updateUserSession(payload.sessionInfo, clientIP, userAgent);

    // Log performance issues for immediate attention
    await checkForPerformanceAlerts(payload.events);

    // Aggregate metrics for dashboard
    await updateRUMMetrics(payload.events, payload.deviceInfo);

    safeLog.info('[RUM] Processed events batch', {
      sessionId: payload.sessionId,
      eventCount: payload.events.length,
      userId: payload.userId
    });

    return NextResponse.json({ 
      success: true, 
      processed: processedEvents.length 
    });

  } catch (error) {
    safeLog.error('[RUM] Error processing events:', { error: String(error) });
    
    return NextResponse.json(
      { error: 'Failed to process RUM events' },
      { status: 500 }
    );
  }
}

/**
 * Process individual RUM event
 */
async function processRUMEvent(
  event: RUMEvent, 
  clientIP: string, 
  userAgent: string,
  payload: RUMPayload
): Promise<void> {
  try {
    // Store raw event data
    await (db as any).rum_events.create({
      data: {
        session_id: event.sessionId,
        user_id: event.userId,
        event_type: event.type,
        page: event.page,
        timestamp: new Date(event.timestamp),
        event_data: event.data,
        client_ip: clientIP,
        user_agent: userAgent,
        device_type: payload.deviceInfo.type,
        browser: payload.deviceInfo.browser,
        os: payload.deviceInfo.os,
        screen_size: payload.deviceInfo.screenSize,
        connection_type: payload.deviceInfo.connection
      }
    });

    // Process specific event types
    switch (event.type) {
      case 'performance':
        await processPerformanceEvent(event);
        break;
      case 'error':
        await processErrorEvent(event);
        break;
      case 'user-action':
        await processUserActionEvent(event);
        break;
      case 'page-view':
        await processPageViewEvent(event);
        break;
    }

  } catch (error) {
    safeLog.error('[RUM] Error processing individual event:', { 
      error: String(error),
      eventType: event.type,
      sessionId: event.sessionId
    });
  }
}

/**
 * Process performance events (Core Web Vitals, etc.)
 */
async function processPerformanceEvent(event: RUMEvent): Promise<void> {
  const metrics = event.data.metrics;
  if (!metrics) return;

  // Store performance metrics
  await (db as any).rum_performance_metrics.upsert({
    where: {
      session_id_page: {
        session_id: event.sessionId,
        page: event.page
      }
    },
    update: {
      fcp: metrics.fcp,
      lcp: metrics.lcp,
      fid: metrics.fid,
      cls: metrics.cls,
      ttfb: metrics.ttfb,
      dom_content_loaded: metrics.domContentLoaded,
      window_load: metrics.windowLoad,
      updated_at: new Date()
    },
    create: {
      session_id: event.sessionId,
      page: event.page,
      fcp: metrics.fcp,
      lcp: metrics.lcp,
      fid: metrics.fid,
      cls: metrics.cls,
      ttfb: metrics.ttfb,
      dom_content_loaded: metrics.domContentLoaded,
      window_load: metrics.windowLoad,
      created_at: new Date(),
      updated_at: new Date()
    }
  });
}

/**
 * Process error events
 */
async function processErrorEvent(event: RUMEvent): Promise<void> {
  const error = event.data.error;
  if (!error) return;

  // Store error information
  await (db as any).rum_errors.create({
    data: {
      session_id: event.sessionId,
      user_id: event.userId,
      page: event.page,
      error_message: error.message,
      error_stack: error.stack,
      filename: error.filename,
      line_number: error.lineno,
      column_number: error.colno,
      user_agent: error.userAgent,
      timestamp: new Date(error.timestamp),
      created_at: new Date()
    }
  });

  // Check for critical errors that need immediate attention
  if (error.message.includes('ChunkLoadError') || 
      error.message.includes('Script error') ||
      error.message.includes('Network Error')) {
    
    safeLog.error('[RUM] Critical error detected:', {
      message: error.message,
      page: event.page,
      sessionId: event.sessionId
    });
  }
}

/**
 * Process user action events
 */
async function processUserActionEvent(event: RUMEvent): Promise<void> {
  // Store user interaction data
  await (db as any).rum_user_actions.create({
    data: {
      session_id: event.sessionId,
      user_id: event.userId,
      page: event.page,
      action_type: event.data.action,
      action_data: event.data,
      timestamp: new Date(event.timestamp),
      created_at: new Date()
    }
  });
}

/**
 * Process page view events
 */
async function processPageViewEvent(event: RUMEvent): Promise<void> {
  // Store page view data
  await (db as any).rum_page_views.create({
    data: {
      session_id: event.sessionId,
      user_id: event.userId,
      page: event.data.page,
      page_title: event.data.title,
      referrer: event.data.referrer,
      timestamp: new Date(event.data.timestamp),
      created_at: new Date()
    }
  });
}

/**
 * Update user session information
 */
async function updateUserSession(
  sessionInfo: UserSession,
  clientIP: string,
  userAgent: string
): Promise<void> {
  await (db as any).rum_sessions.upsert({
    where: { session_id: sessionInfo.sessionId },
    update: {
      last_activity: new Date(sessionInfo.lastActivity),
      total_duration: sessionInfo.totalDuration,
      updated_at: new Date()
    },
    create: {
      session_id: sessionInfo.sessionId,
      user_id: sessionInfo.userId,
      start_time: new Date(sessionInfo.startTime),
      last_activity: new Date(sessionInfo.lastActivity),
      total_duration: sessionInfo.totalDuration,
      device_type: sessionInfo.device.type,
      browser: sessionInfo.device.browser,
      os: sessionInfo.device.os,
      screen_size: sessionInfo.device.screenSize,
      connection_type: sessionInfo.device.connection,
      client_ip: clientIP,
      user_agent: userAgent,
      created_at: new Date(),
      updated_at: new Date()
    }
  });
}

/**
 * Check for performance alerts
 */
async function checkForPerformanceAlerts(events: RUMEvent[]): Promise<void> {
  for (const event of events) {
    if (event.type === 'performance' && event.data.metrics) {
      const metrics = event.data.metrics;
      
      // Check for poor Core Web Vitals
      if (metrics.lcp > 4000) { // LCP > 4s is poor
        safeLog.warn('[RUM] Poor LCP detected:', {
          lcp: metrics.lcp,
          page: event.page,
          sessionId: event.sessionId
        });
      }
      
      if (metrics.fid > 300) { // FID > 300ms is poor
        safeLog.warn('[RUM] Poor FID detected:', {
          fid: metrics.fid,
          page: event.page,
          sessionId: event.sessionId
        });
      }
      
      if (metrics.cls > 0.25) { // CLS > 0.25 is poor
        safeLog.warn('[RUM] Poor CLS detected:', {
          cls: metrics.cls,
          page: event.page,
          sessionId: event.sessionId
        });
      }
    }
  }
}

/**
 * Update aggregated RUM metrics
 */
async function updateRUMMetrics(events: RUMEvent[], deviceInfo: DeviceInfo): Promise<void> {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    // Count events by type
    const eventCounts = events.reduce((acc: any, event: any) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Update daily metrics
    await (db as any).rum_daily_metrics.upsert({
      where: {
        date_device_type: {
          date: today,
          device_type: deviceInfo.type
        }
      },
      update: {
        page_views: { increment: eventCounts['page-view'] || 0 },
        user_actions: { increment: eventCounts['user-action'] || 0 },
        errors: { increment: eventCounts['error'] || 0 },
        updated_at: new Date()
      },
      create: {
        date: today,
        device_type: deviceInfo.type,
        page_views: eventCounts['page-view'] || 0,
        user_actions: eventCounts['user-action'] || 0,
        errors: eventCounts['error'] || 0,
        unique_sessions: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    });

  } catch (error) {
    safeLog.error('[RUM] Error updating aggregated metrics:', { error: String(error) });
  }
}