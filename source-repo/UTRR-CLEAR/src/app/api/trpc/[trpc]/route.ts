import { fetchRe<PERSON>Handler } from '@trpc/server/adapters/fetch';
import { safeLog } from '~/lib/error-handler';
import type { NextRequest } from 'next/server';

/**
 * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when
 * handling a HTTP request (e.g. when you make requests from Client Components).
 */
const createContext = async (req: NextRequest) => {
  const { createTRPCContext } = await import('~/server/api/trpc');
  return createTRPCContext({
    headers: req.headers,
  });
};

const handler = async (req: NextRequest) => {
  // Check if database is available during build
  if (!process.env.DATABASE_URL) {
    return new Response('Database not configured', { status: 503 });
  }

  const { env } = await import('~/env');
  const { appRouter } = await import('~/server/api/root');
  
  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext(req),
    onError:
      env.NODE_ENV === 'development'
        ? ({ path, error }) => {
            safeLog.error(`❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`);
          }
        : undefined,
  });
};

export { handler as GET, handler as POST };
