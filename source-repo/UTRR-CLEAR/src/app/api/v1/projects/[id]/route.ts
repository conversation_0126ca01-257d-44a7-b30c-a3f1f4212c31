import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { safeLog } from '~/lib/error-handler';

// GET /api/v1/projects/:id - Get project by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Check if database is available during build
    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 503 }
      );
    }

    const { db } = await import('~/server/db');
    
    const project = await db.projects.findUnique({
      where: { id: id },
      include: {
        // Relations will be added when stakeholder and utility schemas are defined
        project_logs: {
          take: 10,
          orderBy: { timestamp: 'desc' },
          include: {
            users: {
              select: {
                id: true,
                username: true,
                email: true,
              },
            },
          },
        },
        conflicts: true,
        project_documents: true,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: project });
  } catch (error) {
    safeLog.error('Error fetching project:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/v1/projects/:id - Update project
const updateProjectSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  client: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  contract_amount: z.number().min(0).optional(),
  location: z.string().optional(),
  coordinator_name: z.string().optional(),
  rag_status: z.string().optional(),
  current_phase: z.string().optional(),
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Check if database is available during build
    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 503 }
      );
    }

    const { db } = await import('~/server/db');
    
    const userId = request.headers.get('X-API-User-ID');
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateProjectSchema.parse(body);

    // Get current project state
    const currentProject = await db.projects.findUnique({
      where: { id: id },
    });

    if (!currentProject) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Update project
    const updatedProject = await db.projects.update({
      where: { id: id },
      data: validatedData,
    });

    // Log the update
    const userIdNum = parseInt(userId, 10);
    if (isNaN(userIdNum)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    await db.project_logs.create({
      data: {
        project_id: id,
        user_id: userIdNum,
        action_type: 'updated',
        entity_type: 'project',
        details: {
          before: currentProject,
          after: updatedProject,
          changes: validatedData,
        },
      },
    });

    return NextResponse.json({
      data: updatedProject,
      message: 'Project updated successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    safeLog.error('Error updating project:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/v1/projects/:id - Delete project (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Check if database is available during build
    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 503 }
      );
    }

    const { db } = await import('~/server/db');
    
    const userId = request.headers.get('X-API-User-ID');
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 401 }
      );
    }

    const project = await db.projects.findUnique({
      where: { id: id },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Soft delete by updating status
    await db.projects.update({
      where: { id: id },
      data: { rag_status: 'cancelled' },
    });

    // Log the deletion
    const userIdNum = parseInt(userId, 10);
    if (isNaN(userIdNum)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    await db.project_logs.create({
      data: {
        project_id: id,
        user_id: userIdNum,
        action_type: 'deleted',
        entity_type: 'project',
        details: { project },
      },
    });

    return NextResponse.json({
      message: 'Project deleted successfully',
    });
  } catch (error) {
    safeLog.error('Error deleting project:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}