import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Debug endpoint to check middleware configuration
 * Accessible at /api/middleware-debug
 */
export async function GET(request: NextRequest) {
  const debugInfo = {
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      hasAuthSecret: !!process.env.AUTH_SECRET,
      hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET,
      hasRedisUrl: !!process.env.UPSTASH_REDIS_REST_URL,
      hasRedisToken: !!process.env.UPSTASH_REDIS_REST_TOKEN,
      hasDatabaseUrl: !!process.env.DATABASE_URL,
      apiGatewayEnabled: process.env.ENABLE_API_GATEWAY !== 'false',
      geoRestrictionsEnabled: process.env.ENABLE_GEO_RESTRICTIONS === 'true',
    },
    request: {
      method: request.method,
      url: request.url,
      pathname: request.nextUrl.pathname,
      headers: {
        'user-agent': request.headers.get('user-agent'),
        'x-forwarded-for': request.headers.get('x-forwarded-for'),
        'x-real-ip': request.headers.get('x-real-ip'),
        'cf-connecting-ip': request.headers.get('cf-connecting-ip'),
      },
      cookies: {
        hasSessionToken: request.cookies.has('next-auth.session-token'),
        hasSecureSessionToken: request.cookies.has('__Secure-next-auth.session-token'),
        hasCsrfToken: request.cookies.has('csrf-token'),
      }
    },
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(debugInfo, { 
    status: 200,
    headers: {
      'Cache-Control': 'no-store, no-cache, must-revalidate',
    }
  });
}