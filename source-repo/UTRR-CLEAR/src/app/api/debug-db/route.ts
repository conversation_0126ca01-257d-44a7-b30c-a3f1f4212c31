import { NextResponse } from 'next/server';
import { createServerClient } from '~/lib/supabase/server';

export async function GET() {
  try {
    const supabase = await createServerClient();
    
    // Get auth user info
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    // Check team_messages structure
    const { data: messages, error: messagesError } = await supabase
      .from('team_messages')
      .select('*')
      .limit(1);
      
    // Check user_profiles structure  
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('email', user.email)
      .single();
      
    // Check auth.users schema
    const { data: authSchema, error: authSchemaError } = await supabase
      .rpc('get_auth_users_schema', {});
    
    return NextResponse.json({
      authUser: {
        id: user.id,
        email: user.email,
        metadata: user.user_metadata,
      },
      userProfile: profiles,
      sampleMessage: messages?.[0] || null,
      errors: {
        messages: messagesError?.message,
        profiles: profilesError?.message,
        authSchema: authSchemaError?.message,
      }
    });
  } catch (error) {
    return NextResponse.json({ error: String(error) }, { status: 500 });
  }
}