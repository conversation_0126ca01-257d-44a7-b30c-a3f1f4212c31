import { NextRequest, NextResponse } from 'next/server';
import { getServerAuthSession } from '~/server/auth';
import { db } from '~/server/db';
import { safeLog } from '~/lib/error-handler';
import type { RealtimeMessage, RealtimeEvent } from '~/lib/real-time-manager';

interface PollRequest {
  lastMessageId?: string;
  subscriptions: RealtimeEvent[];
  organizationId?: string;
}

interface PollResponse {
  messages: RealtimeMessage[];
  timestamp: number;
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerAuthSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json() as PollRequest;
    const { lastMessageId, subscriptions, organizationId } = body;

    // Get user's organization if not provided
    const userOrgId = organizationId || session.user.organizationId;
    if (!userOrgId) {
      return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
    }

    // Query for real-time events based on subscriptions
    const messages: RealtimeMessage[] = [];

    // Project-related events
    if (subscriptions.includes('project:updated') || subscriptions.includes('project:created')) {
      try {
        const recentProjects = await db.projects.findMany({
          where: {
            organization_id: userOrgId,
            ...(lastMessageId && {
              updated_at: {
                gt: new Date(parseInt(lastMessageId) || Date.now() - 300000) // 5 minutes fallback
              }
            })
          },
          orderBy: { updated_at: 'desc' },
          take: 10,
          select: {
            id: true,
            name: true,
            // phase: true, // Property doesn't exist
            updated_at: true,
            created_at: true
          }
        });

        recentProjects.forEach((project) => {
          const isNewProject = !lastMessageId || project.created_at > new Date(parseInt(lastMessageId));
          messages.push({
            id: project.updated_at.getTime().toString(),
            event: isNewProject ? 'project:created' : 'project:updated',
            data: {
              id: project.id,
              name: project.name,
              // phase: project.phase // Property doesn't exist
            },
            timestamp: project.updated_at.getTime(),
            organizationId: userOrgId
          });
        });
      } catch (error) {
        safeLog.error('[Realtime Poll] Error fetching projects:', { error: String(error) });
      }
    }

    // Conflict-related events
    if (subscriptions.includes('conflict:detected') || subscriptions.includes('conflict:resolved')) {
      try {
        const recentConflicts = await db.conflicts.findMany({
          where: {
            // projects: {
            //   organization_id: userOrgId
            // },
            ...(lastMessageId && {
              updated_at: {
                gt: new Date(parseInt(lastMessageId) || Date.now() - 300000)
              }
            })
          },
          orderBy: { updated_at: 'desc' },
          take: 10,
          select: {
            id: true,
            // severity: true, // Property doesn't exist
            status: true,
            conflict_type: true,
            updated_at: true,
            // projects: { // Relation doesn't exist
            //   select: {
            //     id: true,
            //     name: true
            //   }
            // }
          }
        });

        recentConflicts.forEach((conflict) => {
          messages.push({
            id: conflict.updated_at.getTime().toString(),
            event: conflict.status === 'resolved' ? 'conflict:resolved' : 'conflict:detected',
            data: {
              id: conflict.id,
              // severity: conflict.severity, // Property doesn't exist
              type: conflict.conflict_type,
              // projectId: conflict.projects.id, // Relation doesn't exist
              // projectName: conflict.projects.name // Relation doesn't exist
            },
            timestamp: conflict.updated_at.getTime(),
            organizationId: userOrgId
          });
        });
      } catch (error) {
        safeLog.error('[Realtime Poll] Error fetching conflicts:', { error: String(error) });
      }
    }

    // Task-related events
    if (subscriptions.includes('task:updated') || subscriptions.includes('task:created') || subscriptions.includes('task:completed')) {
      try {
        const recentTasks = await db.tasks.findMany({
          where: {
            // projects: {
            //   organization_id: userOrgId
            // },
            ...(lastMessageId && {
              updated_at: {
                gt: new Date(parseInt(lastMessageId) || Date.now() - 300000)
              }
            })
          },
          orderBy: { updated_at: 'desc' },
          take: 10,
          select: {
            id: true,
            title: true, // Use title instead of name
            status: true,
            priority: true,
            updated_at: true,
            created_at: true,
            // projects: { // Relation doesn't exist
            //   select: {
            //     id: true,
            //     name: true
            //   }
            // }
          }
        });

        recentTasks.forEach((task) => {
          let event: RealtimeEvent = 'task:updated';
          if (!lastMessageId || task.created_at > new Date(parseInt(lastMessageId))) {
            event = 'task:created';
          } else if (task.status === 'completed') {
            event = 'task:completed';
          }

          messages.push({
            id: task.updated_at.getTime().toString(),
            event,
            data: {
              id: task.id,
              name: task.title, // Use title instead of name
              status: task.status,
              priority: task.priority,
              // projectId: task.projects.id, // Relation doesn't exist
              // projectName: task.projects.name // Relation doesn't exist
            },
            timestamp: task.updated_at.getTime(),
            organizationId: userOrgId
          });
        });
      } catch (error) {
        safeLog.error('[Realtime Poll] Error fetching tasks:', { error: String(error) });
      }
    }

    // Notification events - Now using real notifications table
    if (subscriptions.includes('notification:new')) {
      try {
        const recentNotifications = await db.notifications.findMany({
          where: {
            user_id: parseInt(session.user.id),
            ...(lastMessageId && {
              created_at: {
                gt: new Date(parseInt(lastMessageId) || Date.now() - 300000)
              }
            }),
            // Only fetch unread or recently created notifications
            OR: [
              { read: false },
              { 
                created_at: {
                  gt: new Date(Date.now() - 300000) // Last 5 minutes
                }
              }
            ]
          },
          orderBy: { created_at: 'desc' },
          take: 10,
          select: {
            id: true,
            type: true,
            title: true,
            message: true,
            priority: true,
            read: true,
            action_url: true,
            action_label: true,
            created_at: true
          }
        });

        recentNotifications.forEach((notification) => {
          messages.push({
            id: notification.created_at.getTime().toString(),
            event: 'notification:new',
            data: {
              id: notification.id,
              type: notification.type,
              title: notification.title,
              content: notification.message,
              priority: notification.priority,
              read: notification.read,
              actionUrl: notification.action_url,
              actionLabel: notification.action_label
            },
            timestamp: notification.created_at.getTime(),
            userId: session.user.id,
            organizationId: userOrgId
          });
        });
      } catch (error) {
        safeLog.error('[Realtime Poll] Error fetching notifications:', { error: String(error) });
      }
    }

    // Sort messages by timestamp (newest first) and limit to prevent large responses
    const sortedMessages = messages
      .sort((a: any, b: any) => b.timestamp - a.timestamp)
      .slice(0, 20);

    const response: PollResponse = {
      messages: sortedMessages,
      timestamp: Date.now()
    };

    return NextResponse.json(response);

  } catch (error) {
    safeLog.error('[Realtime Poll] Unexpected error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}