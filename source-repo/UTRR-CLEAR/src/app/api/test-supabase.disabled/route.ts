// Simple API route to test Supabase connection
import { NextResponse } from 'next/server';
import { createServerClient } from '~/lib/supabase/server';

export async function GET() {
  try {
    const supabase = await createServerClient();
    
    // Test the connection by calling our test function
    const { data, error } = await (supabase as any).rpc('test_supabase_connection');
    
    if (error) {
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        details: error 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Supabase connection working',
      data 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error 
    }, { status: 500 });
  }
}