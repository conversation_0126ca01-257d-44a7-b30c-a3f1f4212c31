// Simple API route to test Supabase status without auth complications
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check environment variables
    const hasSupabaseUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL;
    const hasSupabaseKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const hasDatabaseUrl = !!process.env.DATABASE_URL;

    return NextResponse.json({
      status: 'success',
      message: 'Supabase integration status',
      environment: {
        supabaseUrl: hasSupabaseUrl,
        supabaseKey: hasSupabaseKey,
        databaseUrl: hasDatabaseUrl,
      },
      ready: hasSupabaseUrl && hasSupabaseKey && hasDatabaseUrl,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to check Supabase status',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}