import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { safeLog } from '~/lib/error-handler';

/**
 * HPKP (HTTP Public Key Pinning) violation report endpoint
 * Receives reports when certificate pinning violations occur
 */

// Schema for HPKP violation reports
const hpkpReportSchema = z.object({
  'date-time': z.string(),
  hostname: z.string(),
  port: z.number(),
  'effective-expiration-date': z.string(),
  'include-subdomains': z.boolean(),
  'noted-hostname': z.string(),
  'served-certificate-chain': z.array(z.string()),
  'validated-certificate-chain': z.array(z.string()),
  'known-pins': z.array(z.string()),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the report
    const report = hpkpReportSchema.parse(body);
    
    // Log the violation for monitoring
    safeLog.warn('HPKP Violation Report:', {
      hostname: report.hostname,
      port: report.port,
      notedHostname: report['noted-hostname'],
      dateTime: report['date-time'],
      effectiveExpiration: report['effective-expiration-date'],
      includeSubdomains: report['include-subdomains'],
      servedChainLength: report['served-certificate-chain'].length,
      validatedChainLength: report['validated-certificate-chain'].length,
      knownPinsCount: report['known-pins'].length,
    });
    
    // In production, you might want to:
    // 1. Store in database for analysis
    // 2. Send alerts for critical domains
    // 3. Check for patterns indicating attacks
    
    // Check if this is a critical domain
    const criticalDomains = ['api.clear.com', 'api.monday.com'];
    if (criticalDomains.includes(report.hostname)) {
      // Send immediate alert
      safeLog.error('CRITICAL: HPKP violation on critical domain', {
        hostname: report.hostname,
        timestamp: new Date().toISOString(),
      });
      
      // TODO: Implement alerting mechanism (email, Slack, etc.)
    }
    
    // Return 204 No Content as per specification
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    safeLog.error('Failed to process HPKP report:', { error: String(error) });
    
    // Still return 204 to prevent report flooding
    return new NextResponse(null, { status: 204 });
  }
}

// Only accept POST requests
export async function GET() {
  return new NextResponse('Method Not Allowed', { status: 405 });
}