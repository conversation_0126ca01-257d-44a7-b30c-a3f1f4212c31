import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const reason = searchParams.get('reason') || 'permission';
  const resource = searchParams.get('resource') || 'this resource';
  const requiredRole = searchParams.get('role');
  const requiredPermission = searchParams.get('permission');

  return NextResponse.json(
    {
      error: 'Forbidden',
      message: 'Access denied',
      statusCode: 403,
      details: {
        reason,
        resource,
        requiredRole,
        requiredPermission,
        timestamp: new Date().toISOString(),
      },
    },
    { status: 403 }
  );
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { reason, resource, requiredRole, requiredPermission, userId, action } = body;

    // Log access denial for security monitoring
    console.warn('Access denied:', {
      userId,
      action,
      reason,
      resource,
      requiredRole,
      requiredPermission,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('User-Agent'),
      ip: request.headers.get('X-Forwarded-For') || request.headers.get('X-Real-IP'),
    });

    return NextResponse.json(
      {
        error: 'Forbidden',
        message: 'Access denied',
        statusCode: 403,
        details: {
          reason,
          resource,
          requiredRole,
          requiredPermission,
          timestamp: new Date().toISOString(),
        },
      },
      { status: 403 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Bad Request',
        message: 'Invalid request body',
        statusCode: 400,
      },
      { status: 400 }
    );
  }
}