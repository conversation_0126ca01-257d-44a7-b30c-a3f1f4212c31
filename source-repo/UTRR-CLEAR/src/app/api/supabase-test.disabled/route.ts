// Test Supabase connection without authentication dependencies
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Test environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const databaseUrl = process.env.DATABASE_URL;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase configuration',
        environment: {
          supabaseUrl: !!supabaseUrl,
          supabaseKey: !!supabaseKey,
          databaseUrl: !!databaseUrl,
        }
      }, { status: 500 });
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      }
    });

    // Test basic function call
    const { data, error } = await supabase.rpc('test_supabase_connection');
    
    return NextResponse.json({
      success: true,
      message: 'Supabase integration test',
      environment: {
        supabaseUrl: !!supabaseUrl,
        supabaseKey: !!supabaseKey,
        databaseUrl: !!databaseUrl,
      },
      connectionTest: {
        attempted: true,
        success: !error,
        error: error?.message || null,
        data: data || null,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}