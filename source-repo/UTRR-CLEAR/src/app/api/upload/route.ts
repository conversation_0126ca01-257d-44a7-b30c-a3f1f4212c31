import { NextRequest, NextResponse } from 'next/server';
import { getServerAuthSession } from '~/server/auth';
import { fileStorageService } from '~/server/services/file-storage.service';
import { db } from '~/server/db';
import { projectLogService } from '~/server/services/project-log.service';
import { safeLog } from '~/lib/error-handler';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerAuthSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Handle CORS for POST requests
    const origin = request.headers.get('origin');
    const allowedOrigins = [
      process.env.NEXTAUTH_URL || 'http://localhost:3000',
      'https://clear-utility.vercel.app', // Add your production domain
    ].filter(Boolean);
    
    const isAllowed = origin && allowedOrigins.some(allowed => 
      origin === allowed || origin.startsWith(allowed)
    );

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const category = formData.get('category') as string;
    const description = formData.get('description') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate project access if projectId is provided
    if (projectId) {
      const project = await db.projects.findUnique({
        where: { id: projectId },
        select: { 
          id: true,
          organization_id: true 
        }
      });

      if (!project) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 });
      }

      // Multi-tenant isolation: ensure project belongs to user's organization
      if (!session.user.isAdmin && 
          session.user.organizationId && 
          project.organization_id !== session.user.organizationId) {
        return NextResponse.json({ error: 'Forbidden: Project belongs to another organization' }, { status: 403 });
      }
    }

    // Upload file using storage service
    const uploadResult = await fileStorageService.uploadFile(file, {
      projectId,
      userId: session.user.id,
      category: category || 'other',
      preserveOriginalName: true,
    });

    if (!uploadResult.success) {
      return NextResponse.json({ error: uploadResult.error }, { status: 400 });
    }

    // Create database record if this is a project document
    let document = null;
    if (projectId) {
      document = await db.project_documents.create({
        data: {
          project_id: projectId,
          name: file.name,
          document_type: file.type,
          description: description || null,
          category: category || 'other',
          uploaded_by_id: parseInt(session.user.id),
          uploaded_at: new Date(),
          file_path: uploadResult.filePath,
          file_size: uploadResult.fileSize,
          tags: [],
        },
        include: {
          users: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          projects: true,
        },
      });

      // Log the activity
      await projectLogService.logDocumentActivity(
        projectId,
        session.user.id,
        'upload',
        document.id.toString(),
        {
          fileName: file.name,
          fileType: file.type,
          category: category || 'other',
        }
      );
    }

    const response = NextResponse.json({
      success: true,
      file: {
        name: file.name,
        size: uploadResult.fileSize,
        type: file.type,
        path: uploadResult.filePath,
        url: uploadResult.publicUrl || await fileStorageService.getFileUrl(uploadResult.filePath),
      },
      document,
    });
    
    // Add CORS headers if origin is allowed
    if (isAllowed && origin) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
    
    return response;

  } catch (error) {
    safeLog.error('Upload error:', { error: String(error) });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin');
  
  // Define allowed origins
  const allowedOrigins = [
    process.env.NEXTAUTH_URL || 'http://localhost:3000',
    'https://clear-utility.vercel.app', // Add your production domain
    // Add other trusted domains as needed
  ].filter(Boolean);
  
  // Check if the origin is allowed
  const isAllowed = origin && allowedOrigins.some(allowed => 
    origin === allowed || origin.startsWith(allowed)
  );
  
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': isAllowed ? origin : 'null',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // Cache preflight for 24 hours
    },
  });
}