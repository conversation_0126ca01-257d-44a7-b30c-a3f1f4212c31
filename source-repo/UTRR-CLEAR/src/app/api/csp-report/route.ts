import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { safeLog } from '~/lib/error-handler';

// CSP violation report schema
const cspReportSchema = z.object({
  'csp-report': z.object({
    'document-uri': z.string(),
    'referrer': z.string().optional(),
    'violated-directive': z.string(),
    'effective-directive': z.string(),
    'original-policy': z.string(),
    'disposition': z.string(),
    'blocked-uri': z.string().optional(),
    'line-number': z.number().optional(),
    'column-number': z.number().optional(),
    'source-file': z.string().optional(),
    'status-code': z.number().optional(),
    'script-sample': z.string().optional(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Parse CSP violation report
    const body = await request.json();
    const report = cspReportSchema.parse(body);

    // Log CSP violations in development
    if (process.env.NODE_ENV === 'development') {
      safeLog.warn('CSP Violation:', {
        uri: report['csp-report']['document-uri'],
        directive: report['csp-report']['violated-directive'],
        blockedUri: report['csp-report']['blocked-uri'],
        sourceFile: report['csp-report']['source-file'],
        lineNumber: report['csp-report']['line-number'],
      });
    }

    // In production, you might want to:
    // 1. Send to a logging service (Sentry, LogRocket, etc.)
    // 2. Store in a database for analysis
    // 3. Alert on critical violations

    // Example: Send to Sentry if configured
    if (process.env.SENTRY_DSN && process.env.NODE_ENV === 'production') {
      // Sentry integration would go here
      // Sentry.captureMessage('CSP Violation', {
      //   level: 'warning',
      //   extra: report['csp-report'],
      // });
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    safeLog.error('Invalid CSP report:', { error: String(error) });
    return new NextResponse('Invalid CSP report', { status: 400 });
  }
}

// CSP reports should not require authentication
export const runtime = 'nodejs';