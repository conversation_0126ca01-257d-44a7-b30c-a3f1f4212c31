'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { supabase } from '~/lib/supabase/client';
import type { User } from '@supabase/supabase-js';
import Header from '~/components/layout/header';
import { CommentProvider } from '~/components/comments/comment-provider';
import { CommentDrawer } from '~/components/comments/comment-drawer';
import { ContextMenuProvider } from '~/components/context-menu/context-menu-provider';
import { Breadcrumb } from '~/components/ui/breadcrumb';
import { DashboardSkeleton } from '~/components/ui/loading-states';
import { CommandPaletteProvider } from '~/components/ui/command-palette';
import { KeyboardShortcutsProvider } from '~/hooks/use-keyboard-shortcuts';
import { OrganizationSetupGuard } from '~/components/organization/organization-setup-guard';
import { OrganizationThemeProvider } from '~/components/providers/organization-theme-provider';
import { useInactivityLogout } from '~/hooks/use-inactivity-logout';
import { SidebarNavigation } from '~/components/layout/sidebar-navigation';
import { Button } from '~/components/ui/button';
import { Menu, X } from 'lucide-react';
import { cn } from '~/lib/utils';

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Add inactivity logout for authenticated users  
  useInactivityLogout();

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setLoading(false);
      
      if (!session) {
        router.push('/auth/signin');
      }
    };

    getSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
      if (event === 'SIGNED_OUT' || !session) {
        router.push('/auth/signin');
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex flex-col h-screen bg-background">
        <div className="h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60" />
        <div className="flex-1 p-6">
          <DashboardSkeleton />
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (!user) {
    return null;
  }

  // For authenticated status, always render even if session is still loading
  // The middleware ensures we're authenticated at this point

  return (
    <OrganizationThemeProvider>
      <KeyboardShortcutsProvider>
        <CommandPaletteProvider>
          <CommentProvider>
            <ContextMenuProvider>
              <OrganizationSetupGuard>
                <div className="flex flex-col h-screen bg-background">
                  <Header />
                  <div className="flex flex-1 overflow-hidden">
                    {/* Sidebar Navigation */}
                    <div className={cn(
                      "hidden lg:block transition-all duration-300",
                      sidebarOpen ? "w-64" : "w-0"
                    )}>
                      {sidebarOpen && <SidebarNavigation />}
                    </div>
                    
                    {/* Main Content Area */}
                    <div className="flex flex-col flex-1 overflow-hidden">
                      {/* Breadcrumb with Sidebar Toggle */}
                      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                        <div className="container mx-auto px-4 py-4 flex items-start gap-3">
                          {/* Sidebar Toggle Button */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSidebarOpen(!sidebarOpen)}
                            className="hidden lg:flex mt-1"
                          >
                            {sidebarOpen ? (
                              <X className="h-4 w-4" />
                            ) : (
                              <Menu className="h-4 w-4" />
                            )}
                          </Button>
                          <div className="flex-1">
                            <Breadcrumb />
                          </div>
                        </div>
                      </div>
                      <main className="flex-1 overflow-y-auto">{children}</main>
                    </div>
                  </div>
                  <CommentDrawer />
                </div>
              </OrganizationSetupGuard>
            </ContextMenuProvider>
          </CommentProvider>
        </CommandPaletteProvider>
      </KeyboardShortcutsProvider>
    </OrganizationThemeProvider>
  );
}