import { Suspense } from 'react';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import { NotesContent } from '~/components/notes/notes-content';
import { PageContainer } from '~/components/layout/page-container';
import { Skeleton } from '~/components/ui/skeleton';

export const metadata = {
  title: 'My Notes | CLEAR',
  description: 'Manage your personal notes and documents',
};

function NotesLoading() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i: any) => (
          <Skeleton key={i} className="h-48" />
        ))}
      </div>
    </div>
  );
}

export default async function NotesPage() {
  const session = await getServerAuthSession();
  
  // Prefetch notes data for faster loading

  return (
    <HydrateClient>
      <PageContainer title="Notes">
        <Suspense fallback={<NotesLoading />}>
          <NotesContent />
        </Suspense>
      </PageContainer>
    </HydrateClient>
  );
}