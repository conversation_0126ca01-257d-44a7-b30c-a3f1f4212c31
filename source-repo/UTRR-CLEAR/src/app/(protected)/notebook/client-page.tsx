'use client';

import React, { useState, useMemo } from 'react';
import { api } from '~/trpc/react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { NoteEditorDialog } from '~/components/ui/note-editor-dialog';
import { MarkdownEditor } from '~/components/ui/markdown-editor';
import { sanitizeHtml } from '~/lib/html-sanitizer';
import {
  Plus,
  Search,
  Pin,
  Edit,
  Trash,
  FileText,
  Calendar,
  Tag,
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from '~/hooks/use-toast';
import { useContextMenuRef } from '~/hooks/use-context-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';

interface NotebookClientPageProps {
  initialNotesData?: {
    notes: any[];
    total: number;
    hasMore: boolean;
  };
  initialTags?: string[];
}

export default function NotebookClientPage({ initialNotesData, initialTags }: NotebookClientPageProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<any>(null);
  const [viewingNote, setViewingNote] = useState<any>(null);
  const [deleteNoteId, setDeleteNoteId] = useState<string | null>(null);

  const { data: notesData, refetch } = api.notes.getAll.useQuery({
    search: searchQuery,
    tags: selectedTags,
    limit: 50,
    offset: 0,
  }, {
    initialData: initialNotesData,
  });

  const { data: tags } = api.notes.getTags.useQuery(undefined, {
    initialData: initialTags,
  });

  const createNote = api.notes.create.useMutation({
    onSuccess: () => {
      toast({
        title: 'Note created',
        description: 'Your note has been saved successfully.',
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const updateNote = api.notes.update.useMutation({
    onSuccess: () => {
      toast({
        title: 'Note updated',
        description: 'Your note has been updated successfully.',
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const deleteNote = api.notes.delete.useMutation({
    onSuccess: () => {
      toast({
        title: 'Note deleted',
        description: 'Your note has been deleted successfully.',
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const togglePin = api.notes.togglePin.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleCreateNote = () => {
    setEditingNote(null);
    setIsEditorOpen(true);
  };

  const handleEditNote = (note: any) => {
    setEditingNote(note);
    setIsEditorOpen(true);
  };

  const handleSaveNote = (noteData: any) => {
    if (editingNote) {
      updateNote.mutate({
        id: editingNote.id,
        ...noteData,
      });
    } else {
      createNote.mutate(noteData);
    }
  };

  const handleDeleteNote = (id: string) => {
    deleteNote.mutate({ id });
    setDeleteNoteId(null);
  };

  const handleTogglePin = (id: string) => {
    togglePin.mutate({ id });
  };

  const handleTagClick = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">My Notebook</h1>
        <p className="text-muted-foreground">
          Your personal collection of notes and ideas. These notes are private and only visible to you.
        </p>
      </div>

      <div className="mb-6 space-y-4">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notes..."
              value={searchQuery}
              onChange={(e: any) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button onClick={handleCreateNote}>
            <Plus className="h-4 w-4 mr-2" />
            New Note
          </Button>
        </div>

        {tags && tags.length > 0 && (
          <div className="flex gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">Filter by tags:</span>
            {tags.map((tag: any) => (
              <Badge
                key={tag as string}
                variant={selectedTags.includes(tag as string) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => handleTagClick(tag as string)}
              >
                <Tag className="h-3 w-3 mr-1" />
                {tag as string}
              </Badge>
            ))}
          </div>
        )}
      </div>

      {notesData?.notes && notesData.notes.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No notes yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first note to get started
            </p>
            <Button onClick={handleCreateNote}>
              <Plus className="h-4 w-4 mr-2" />
              Create Note
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {notesData?.notes.map((note: any) => {
          // Note card with context menu
          const NoteCard = () => {
            const noteRef = useContextMenuRef<HTMLDivElement>({
              entityType: 'note',
              entityId: note.id,
              entityName: note.title,
              data: {
                text: `${note.title} - Note`,
                onView: () => setViewingNote(note),
                onEdit: () => {
                  setEditingNote(note);
                  setIsEditorOpen(true);
                },
                onDelete: () => setDeleteNoteId(note.id),
                onShare: () => {
                  navigator.clipboard.writeText(`Note: ${note.title}\n\nCreated: ${format(new Date(note.created_at), 'MMM d, yyyy')}\nTags: ${note.tags.join(', ')}`);
                },
              },
              securityLevel: 'low',
            });

            return (
              <Card
                ref={noteRef}
                key={note.id}
                className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => setViewingNote(note)}
              >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg line-clamp-1 flex-1">
                  {note.title}
                </CardTitle>
                {note.is_pinned && (
                  <Pin className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
              <CardDescription className="flex items-center gap-2 text-xs">
                <Calendar className="h-3 w-3" />
                {format(new Date(note.updated_at), 'MMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm dark:prose-invert line-clamp-3 mb-3">
                <div 
                  dangerouslySetInnerHTML={{ 
                    __html: sanitizeHtml(note.content)
                  }} 
                />
              </div>
              {note.tags.length > 0 && (
                <div className="flex gap-1 flex-wrap">
                  {note.tags.slice(0, 3).map((tag: any) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {note.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{note.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
              <div className="flex gap-2 mt-3" onClick={(e: any) => e.stopPropagation()}>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleTogglePin(note.id)}
                >
                  <Pin className={`h-4 w-4 ${note.is_pinned ? 'fill-current' : ''}`} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleEditNote(note)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setDeleteNoteId(note.id)}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
            );
          };

          return <NoteCard />;
        })}
      </div>

      <NoteEditorDialog
        isOpen={isEditorOpen}
        onClose={() => {
          setIsEditorOpen(false);
          setEditingNote(null);
        }}
        onSave={handleSaveNote}
        initialData={editingNote}
        mode="personal"
      />

      {viewingNote && (
        <Dialog open={!!viewingNote} onOpenChange={() => setViewingNote(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{viewingNote.title}</DialogTitle>
              <DialogDescription>
                Last updated: {format(new Date(viewingNote.updated_at), 'MMMM d, yyyy h:mm a')}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <MarkdownEditor
                content={viewingNote.content}
                onChange={() => {}}
                readOnly
              />
              {viewingNote.tags.length > 0 && (
                <div className="flex gap-2 flex-wrap mt-4">
                  {viewingNote.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setViewingNote(null)}>
                Close
              </Button>
              <Button onClick={() => {
                handleEditNote(viewingNote);
                setViewingNote(null);
              }}>
                Edit Note
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <AlertDialog open={!!deleteNoteId} onOpenChange={() => setDeleteNoteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Note</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this note? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteNoteId && handleDeleteNote(deleteNoteId)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}