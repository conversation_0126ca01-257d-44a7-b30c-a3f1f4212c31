import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import NotebookClientPage from './client-page';

export default async function NotebookPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch notebook data for faster loading
  void api.notes.getAll.prefetch({
    search: '',
    tags: [],
    limit: 50,
    offset: 0,
  });
  void api.notes.getTags.prefetch();

  // Fetch initial data server-side
  const [notesData, tags] = await Promise.all([
    api.notes.getAll({
      search: '',
      tags: [],
      limit: 50,
      offset: 0,
    }),
    api.notes.getTags()
  ]);

  return (
    <HydrateClient>
      <NotebookClientPage 
        initialNotesData={{
          notes: notesData.notes,
          total: notesData.total,
          hasMore: notesData.hasMore
        }}
        initialTags={tags as string[]}
      />
    </HydrateClient>
  );
}