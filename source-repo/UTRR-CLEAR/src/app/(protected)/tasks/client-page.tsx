'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Checkbox } from '~/components/ui/checkbox';
import { Calendar, Clock, Filter, AlertCircle, CheckCircle2, Plus } from 'lucide-react';
import { api } from '~/trpc/react';
import { AddTaskDialog } from '~/components/dashboard/add-task-dialog';
import { EditTaskDialog } from '~/components/dashboard/edit-task-dialog';
import { DeleteConfirmationDialog } from '~/components/ui/delete-confirmation-dialog';
import { EmptyState } from '~/components/ui/empty-state';
import { TaskListSkeleton } from '~/components/ui/loading-skeleton';
import { formatDate, isOverdue as checkOverdue } from '~/lib/date-utils';
import { showSuccessToast, showErrorToast } from '~/lib/toast-messages';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import { CommentBadge } from '~/components/comments/comment-badge';

interface Task {
  id: string;
  title: string;
  description?: string | null;
  projectId?: string | null;
  projectName: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dueDate?: string | null;
  assignedTo: {
    id: string;
    name: string;
    avatar?: string;
  };
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const priorityColors = {
  low: 'bg-muted',
  medium: 'bg-blue-500',
  high: 'bg-amber-500',
  critical: 'bg-red-500',
};

const priorityBadgeVariants = {
  low: 'secondary',
  medium: 'default',
  high: 'default',
  critical: 'destructive',
} as const;

export default function TasksClientPage() {
  const [showCompleted, setShowCompleted] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [deletingTask, setDeletingTask] = useState<Task | null>(null);

  // Fetch tasks
  const { data: tasks = [], isLoading, refetch } = api.tasks.getMyTasks.useQuery({
    includeCompleted: showCompleted,
    limit: 100,
  });

  const toggleTaskMutation = api.tasks.toggleComplete.useMutation({
    onSuccess: () => {
      refetch();
      showSuccessToast.statusChanged('Task', 'updated');
    },
    onError: (error: any) => {
      showErrorToast.updateFailed('task', error.message);
    },
  });

  const deleteTaskMutation = api.tasks.delete.useMutation({
    onSuccess: () => {
      refetch();
      showSuccessToast.deleted('Task');
      setDeletingTask(null);
    },
    onError: (error: any) => {
      showErrorToast.deleteFailed('task', error.message);
    },
  });

  const pendingTasks = tasks.filter((task: any) => !task.completed);
  const completedTasks = tasks.filter((task: any) => task.completed);

  const toggleTaskComplete = (taskId: string) => {
    toggleTaskMutation.mutate({ taskId });
  };

  const isOverdue = (dueDate: string | null) => {
    return checkOverdue(dueDate);
  };

  const TaskItem = ({ task }: { task: Task }) => (
    <Card className={`p-4 ${task.completed ? 'opacity-60' : ''}`}>
      <div className="flex items-start gap-3">
        <Checkbox
          checked={task.completed}
          onCheckedChange={() => toggleTaskComplete(task.id)}
          disabled={toggleTaskMutation.isPending}
          className="mt-1"
        />

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <h4 className={`font-medium ${task.completed ? 'line-through text-muted-foreground' : ''}`}>
              {task.title}
            </h4>
            <Badge variant={priorityBadgeVariants[task.priority]} className="text-xs">
              {task.priority}
            </Badge>
            {task.dueDate && isOverdue(task.dueDate) && !task.completed && (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
          </div>

          {task.description && (
            <p className="text-sm text-muted-foreground mb-3">{task.description}</p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              {task.dueDate && (
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(task.dueDate, 'medium')}
                </span>
              )}
              <span className="font-medium">{task.projectName}</span>
            </div>

            <div className="flex items-center gap-2">
              <CommentBadge
                entityType="task"
                entityId={task.id}
                entityName={task.title}
                variant="icon-only"
                showZero={false}
              />
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {task.assignedTo.name
                    .split(' ')
                    .map((n: any) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingTask(task)}
              >
                Edit
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDeletingTask(task)}
                className="text-destructive hover:text-destructive"
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold">My Tasks</h1>
            <p className="text-muted-foreground mt-1">
              Manage your assigned tasks across all projects
            </p>
          </div>
          <AddTaskDialog onSuccess={() => refetch()} />
        </div>

        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <Checkbox 
              checked={showCompleted} 
              onCheckedChange={(checked) => setShowCompleted(checked === true)}
              disabled={isLoading}
            />
            Show completed tasks
          </label>
          <div className="flex gap-2 text-sm text-muted-foreground">
            <span>{pendingTasks.length} pending</span>
            <span>•</span>
            <span>{completedTasks.length} completed</span>
          </div>
        </div>
      </div>

      {isLoading && <TaskListSkeleton />}

      {!isLoading && (
        <div className="space-y-6">
          {/* Pending Tasks */}
          {pendingTasks.length > 0 && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Pending Tasks
              </h2>
              <div className="space-y-3">
                {pendingTasks.map((task: any) => (
                  <TaskItem key={task.id} task={task} />
                ))}
              </div>
            </div>
          )}

          {/* Completed Tasks */}
          {showCompleted && completedTasks.length > 0 && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                Completed Tasks
              </h2>
              <div className="space-y-3">
                {completedTasks.map((task: any) => (
                  <TaskItem key={task.id} task={task} />
                ))}
              </div>
            </div>
          )}

          {/* Empty State */}
          {tasks.length === 0 && (
            <EmptyState
              icon={<Clock className="h-12 w-12" />}
              title="No tasks assigned"
              description="Tasks from your active projects will appear here"
              action={
                <Button onClick={() => refetch()}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Task
                </Button>
              }
            />
          )}
        </div>
      )}

      {/* Edit Task Dialog */}
      {editingTask && (
        <EditTaskDialog
          task={editingTask}
          open={!!editingTask}
          onOpenChange={(open) => !open && setEditingTask(null)}
          onSuccess={() => {
            setEditingTask(null);
            refetch();
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={!!deletingTask}
        onOpenChange={(open) => !open && setDeletingTask(null)}
        onConfirm={() => {
          if (deletingTask) {
            deleteTaskMutation.mutate({ id: deletingTask.id });
          }
        }}
        itemName={deletingTask?.title}
        isDeleting={deleteTaskMutation.isPending}
      />
    </div>
  );
}