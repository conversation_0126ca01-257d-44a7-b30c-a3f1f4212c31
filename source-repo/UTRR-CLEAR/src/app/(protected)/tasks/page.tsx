import { getServerAuthSession } from '~/server/auth';
import { api, HydrateClient } from '~/trpc/server';
import TasksClientPage from './client-page';

export default async function TasksPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    return null; // Layout will redirect
  }

  // Prefetch tasks data for faster loading
  void api.tasks.getMyTasks.prefetch({ includeCompleted: false, limit: 50 });
  void api.projects.getActiveProjects.prefetch();

  return (
    <HydrateClient>
      <TasksClientPage />
    </HydrateClient>
  );
}