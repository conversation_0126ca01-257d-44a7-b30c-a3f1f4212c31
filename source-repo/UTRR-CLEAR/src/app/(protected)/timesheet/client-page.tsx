"use client";

import { TimesheetEntryWidget } from "~/components/timesheet/timesheet-entry-widget";
import { PageContainer } from "~/components/layout/page-container";
import { useRealtimeProjects } from '~/hooks/use-realtime-events';

export default function TimesheetClientPage() {
  // Add real-time updates for timesheet entries
  useRealtimeProjects();

  return (
    <PageContainer title="Timesheet">
      <div className="max-w-[1400px] mx-auto">
        <TimesheetEntryWidget />
      </div>
    </PageContainer>
  );
}