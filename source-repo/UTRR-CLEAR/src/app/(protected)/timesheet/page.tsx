import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import TimesheetClientPage from './client-page';

export default async function TimesheetPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch timesheet data for faster loading
  void api.timesheet.getWeeklyEntries.prefetch({ 
    weekStart: new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toISOString()
  });
  void api.projects.getActiveProjects.prefetch();

  return (
    <HydrateClient>
      <TimesheetClientPage />
    </HydrateClient>
  );
}