'use client';

import { useState } from 'react';
import { useRealtimeProjects } from '~/hooks/use-realtime-events';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Download,
  FileText,
  Plus,
  Search,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { CommentBadge } from '~/components/comments/comment-badge';
import { EmptyState } from '~/components/ui/empty-state';
import { LoadingSkeleton } from '~/components/ui/loading-skeleton';

interface SubInvoicesClientPageProps {
  initialInvoices?: any[];
  initialStats?: any;
}

export default function SubInvoicesClientPage({ initialInvoices, initialStats }: SubInvoicesClientPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [clientFilter, setClientFilter] = useState<string>('all');

  // Add real-time updates for invoice changes
  useRealtimeProjects();

  // Use real tRPC queries
  const { data: invoicesData, isLoading: invoicesLoading } = api.subInvoices.getAll.useQuery({
    status: statusFilter === 'all' ? undefined : statusFilter,
  });
  
  const { data: stats, isLoading: statsLoading } = api.subInvoices.getStats.useQuery();

  // Use real data if available, otherwise empty array
  const invoices = invoicesData?.invoices || [];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Paid', label: 'Paid' },
    { value: 'Approved', label: 'Approved' },
    { value: 'Rejected', label: 'Rejected' },
  ];

  // Get unique clients from invoices
  const uniqueClients = Array.from(new Set(invoices.map((inv: any) => inv.projects?.client).filter(Boolean)));
  const clientOptions = [
    { value: 'all', label: 'All Clients' },
    ...uniqueClients.map(client => ({ value: client, label: client }))
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'default';
      case 'approved':
        return 'secondary';
      case 'pending':
        return 'outline';
      case 'rejected':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const kpiCards = [
    {
      title: 'Total Outstanding',
      value: formatCurrency(stats?.pendingAmount || 0),
      change: (stats?.totalInvoices ?? 0) > 0 ? `${stats?.statusCounts?.Pending || 0} invoices` : 'No invoices',
      trend: 'up',
      icon: DollarSign,
      description: 'Pending invoices',
    },
    {
      title: 'Total Paid',
      value: formatCurrency(stats?.paidAmount || 0),
      change: (stats?.totalInvoices ?? 0) > 0 ? `${stats?.statusCounts?.Paid || 0} invoices` : 'No invoices',
      trend: 'up',
      icon: CheckCircle,
      description: 'Completed payments',
    },
    {
      title: 'Total Invoices',
      value: stats?.totalInvoices || 0,
      change: 'All time',
      trend: 'up',
      icon: FileText,
      description: 'Total created',
    },
    {
      title: 'Total Fees',
      value: formatCurrency(stats?.totalFees || 0),
      change: 'Sub-consultant fees',
      trend: 'up',
      icon: TrendingUp,
      description: 'Total fees collected',
    },
  ];

  const filteredInvoices = invoices.filter((invoice: any) => {
    const matchesSearch =
      invoice.sub_company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.projects?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.full_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    const matchesClient = clientFilter === 'all' || invoice.projects?.client === clientFilter;

    return matchesSearch && matchesStatus && matchesClient;
  });

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">Sub-Consultant Invoices</h1>
              <RealtimeIndicator showDetails />
            </div>
            <p className="text-muted-foreground">
              Track and manage sub-consultant invoices across all projects
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Invoice
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        {statsLoading ? (
          <LoadingSkeleton variant="card" count={4} />
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {kpiCards.map((kpi: any) => {
              const Icon = kpi.icon;
              const TrendIcon = kpi.trend === 'up' ? TrendingUp : TrendingDown;
              return (
                <Card key={kpi.title}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                    <Icon className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{kpi.value}</div>
                    <div className="flex items-center space-x-2 text-xs">
                      <span className="text-muted-foreground">{kpi.change}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">{kpi.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Invoice Directory</CardTitle>
            <CardDescription>
              Manage all sub-consultant invoices and billing activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search invoices..."
                    value={searchTerm}
                    onChange={(e: any) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option: any) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {uniqueClients.length > 0 && (
                <Select value={clientFilter} onValueChange={setClientFilter}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {clientOptions.map((option: any) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Invoices List */}
            {invoicesLoading ? (
              <LoadingSkeleton variant="list" count={3} />
            ) : filteredInvoices.length === 0 ? (
              <EmptyState
                icon={<FileText className="h-12 w-12" />}
                title="No invoices found"
                description={searchTerm || statusFilter !== 'all' ? "Try adjusting your filters" : "Create your first sub-consultant invoice"}
                variant="default"
              />
            ) : (
              <div className="space-y-4">
                {filteredInvoices.map((invoice: any) => (
                  <Card key={invoice.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-base flex items-center space-x-2">
                            <span>{invoice.invoice_number}</span>
                            <CommentBadge
                              entityType="sub_invoice"
                              entityId={invoice.id.toString()}
                              entityName={`${invoice.invoice_number} - ${invoice.sub_company}`}
                              variant="icon-only"
                              showZero={false}
                            />
                            <Badge variant={getStatusColor(invoice.status)}>
                              {invoice.status}
                            </Badge>
                          </CardTitle>
                          <CardDescription className="mt-1">
                            {invoice.sub_company} • {invoice.projects?.name || 'No project assigned'}
                          </CardDescription>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-semibold">
                            {formatCurrency(Number(invoice.invoiced_amount))}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Fee: {formatCurrency(Number(invoice.sub_consultant_fee))}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Consultant
                          </Label>
                          <p className="text-sm">
                            {invoice.full_name} • {invoice.contact_company}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Client
                          </Label>
                          <p className="text-sm">{invoice.projects?.client || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Received Date
                          </Label>
                          <p className="text-sm">
                            {invoice.received_date
                              ? new Date(invoice.received_date).toLocaleDateString()
                              : 'Not received'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Signed Date
                          </Label>
                          <p className="text-sm">
                            {invoice.signed_date
                              ? new Date(invoice.signed_date).toLocaleDateString()
                              : 'Not signed'}
                          </p>
                        </div>
                      </div>
                      {invoice.notes && (
                        <div className="mt-4">
                          <Label className="text-sm font-medium text-muted-foreground">
                            Notes
                          </Label>
                          <p className="text-sm mt-1">{invoice.notes}</p>
                        </div>
                      )}
                      <div className="flex justify-end space-x-2 mt-4">
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}