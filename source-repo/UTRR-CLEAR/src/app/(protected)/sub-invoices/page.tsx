import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import SubInvoicesClientPage from './client-page';

export default async function SubInvoicesPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch invoice and project data for faster loading
  void api.projects.getAll.prefetch({ limit: 50 });

  // Fetch initial data server-side if available
  // Since we're using mock data, we'll pass undefined for now
  const invoices = undefined;
  const invoiceStats = undefined;

  return (
    <HydrateClient>
      <SubInvoicesClientPage 
        initialInvoices={invoices}
        initialStats={invoiceStats}
      />
    </HydrateClient>
  );
}
