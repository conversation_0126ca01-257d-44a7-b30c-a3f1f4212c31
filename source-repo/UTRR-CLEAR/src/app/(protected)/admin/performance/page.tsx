import type { Metadata } from 'next';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { BarChart3, TrendingUp, Clock, AlertTriangle, ExternalLink } from 'lucide-react';
import { Button } from '~/components/ui/button';
import Link from 'next/link.js';

export const metadata: Metadata = {
  title: 'Performance Dashboard | CLEAR Admin',
  description: 'Monitor application performance metrics and insights',
};

export default async function PerformanceDashboardPage() {
  const session = await getServerAuthSession();
  
  // Prefetch performance data for faster loading
  void api.admin.getStats.prefetch();
  return (
    <HydrateClient>
      <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Performance Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Monitor real-time performance metrics and insights
        </p>
      </div>

      <Alert className="mb-6">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Vercel Speed Insights and Analytics have been integrated into the application. 
          Visit your Vercel dashboard to view detailed performance metrics.
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Speed Insights</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground">
              Real User Monitoring (RUM) data including Core Web Vitals
            </p>
            <Button asChild className="mt-4 w-full" variant="outline">
              <a 
                href="https://vercel.com/craft1563s-projects/clear/speed-insights" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                View in Vercel
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Analytics</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground">
              Page views, unique visitors, and custom events tracking
            </p>
            <Button asChild className="mt-4 w-full" variant="outline">
              <a 
                href="https://vercel.com/craft1563s-projects/clear/analytics" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                View Analytics
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Tips</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground">
              Optimize your pages based on real user data
            </p>
            <Button asChild className="mt-4 w-full" variant="outline">
              <Link href="/admin/performance/tips">
                View Optimization Tips
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>What&apos;s Being Tracked</CardTitle>
          <CardDescription>
            Automatic performance metrics collected on every page
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Core Web Vitals</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• <strong>LCP (Largest Contentful Paint)</strong> - Loading performance</li>
              <li>• <strong>FID (First Input Delay)</strong> - Interactivity</li>
              <li>• <strong>CLS (Cumulative Layout Shift)</strong> - Visual stability</li>
              <li>• <strong>TTFB (Time to First Byte)</strong> - Server response time</li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Custom Metrics</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• Page load times for each route</li>
              <li>• Resource counts (JS, CSS, images, API calls)</li>
              <li>• Slow page loads (&gt;3 seconds)</li>
              <li>• Slow API calls (&gt;2 seconds)</li>
              <li>• User interactions and navigation patterns</li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-2">How to Use This Data</h3>
            <ol className="space-y-2 text-sm text-muted-foreground list-decimal list-inside">
              <li>Check Speed Insights daily to monitor Core Web Vitals</li>
              <li>Look for pages with consistently slow load times</li>
              <li>Identify patterns in user behavior from Analytics</li>
              <li>Focus optimization efforts on high-traffic, slow pages</li>
              <li>Monitor improvements after deploying optimizations</li>
            </ol>
          </div>
        </CardContent>
      </Card>
      </div>
    </HydrateClient>
  );
}