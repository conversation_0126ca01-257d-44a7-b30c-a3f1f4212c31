'use client';

import { useAuth } from '~/hooks/use-auth';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Award,
  Copy,
  Download,
  Edit,
  FileText,
  Plus,
  Save,
  Search,
  Settings,
  Trash2,
  Upload,
} from 'lucide-react';
import { api } from '~/trpc/react';

interface Template {
  id: string;
  name: string;
  category: string;
  description: string;
  version: string;
  status: string;
  usage_count: number;
  created_at: string;
  updated_at: string;
  created_by: {
    first_name: string;
    last_name: string;
  };
}

interface Badge {
  id: string;
  name: string;
  color: string;
  icon: string;
  criteria: string;
  auto_assign: boolean;
  active: boolean;
}

interface TemplateStats {
  totalTemplates: number;
  activeTemplates: number;
  totalUsage: number;
  mostUsed: string;
  recentlyUpdated: number;
}

interface TemplateManagementClientPageProps {
  initialTemplates?: Template[];
  initialBadges?: Badge[];
  initialTemplateStats?: TemplateStats;
}

export default function TemplateManagementClientPage({ 
  initialTemplates, 
  initialBadges,
  initialTemplateStats 
}: TemplateManagementClientPageProps) {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Template management data fetching - implement when admin router endpoints are available
  const templates = undefined; // api.admin.getTemplates.useQuery when implemented
  const badges = undefined; // api.admin.getBadges.useQuery when implemented
  const templateStats = undefined; // api.admin.getTemplateStats.useQuery when implemented

  const mockTemplates = templates || initialTemplates || [
    {
      id: '1',
      name: 'Standard Highway Project',
      category: 'fee_calculation',
      description: 'Standard fee calculation template for highway projects',
      version: '2.1',
      status: 'active',
      usage_count: 145,
      created_at: '2024-01-10T10:30:00Z',
      updated_at: '2024-01-15T14:22:00Z',
      created_by: { first_name: 'John', last_name: 'Smith' },
    },
    {
      id: '2',
      name: 'Bridge Construction Template',
      category: 'fee_calculation',
      description: 'Specialized template for bridge construction projects',
      version: '1.8',
      status: 'active',
      usage_count: 78,
      created_at: '2024-01-08T09:15:00Z',
      updated_at: '2024-01-12T16:45:00Z',
      created_by: { first_name: 'Sarah', last_name: 'Johnson' },
    },
    {
      id: '3',
      name: 'Urban Development Package',
      category: 'project_template',
      description: 'Complete template package for urban development projects',
      version: '3.0',
      status: 'active',
      usage_count: 234,
      created_at: '2024-01-05T11:20:00Z',
      updated_at: '2024-01-14T13:30:00Z',
      created_by: { first_name: 'Mike', last_name: 'Davis' },
    },
    {
      id: '4',
      name: 'Quality Assurance Badge',
      category: 'badge',
      description: 'Badge template for quality assurance milestones',
      version: '1.2',
      status: 'active',
      usage_count: 89,
      created_at: '2024-01-03T08:45:00Z',
      updated_at: '2024-01-11T10:15:00Z',
      created_by: { first_name: 'Lisa', last_name: 'Wilson' },
    },
  ];

  const mockBadges = badges || initialBadges || [
    {
      id: '1',
      name: 'Project Completion',
      color: '#22c55e',
      icon: 'checkmark',
      criteria: 'Project reaches 100% completion',
      auto_assign: true,
      active: true,
    },
    {
      id: '2',
      name: 'Quality Excellence',
      color: '#3b82f6',
      icon: 'star',
      criteria: 'Project achieves SUE Quality Level A',
      auto_assign: true,
      active: true,
    },
    {
      id: '3',
      name: 'On-Time Delivery',
      color: '#f59e0b',
      icon: 'clock',
      criteria: 'Project completed on or before deadline',
      auto_assign: false,
      active: true,
    },
    {
      id: '4',
      name: 'Budget Champion',
      color: '#8b5cf6',
      icon: 'dollar',
      criteria: 'Project completed under budget',
      auto_assign: false,
      active: true,
    },
  ];

  const mockStats = templateStats || initialTemplateStats || {
    totalTemplates: 24,
    activeTemplates: 18,
    totalUsage: 1547,
    mostUsed: 'Urban Development Package',
    recentlyUpdated: 6,
  };

  const templateCategories = [
    { value: 'all', label: 'All Categories' },
    { value: 'fee_calculation', label: 'Fee Calculation' },
    { value: 'project_template', label: 'Project Templates' },
    { value: 'contract', label: 'Contract Templates' },
    { value: 'report', label: 'Report Templates' },
    { value: 'badge', label: 'Badge Templates' },
    { value: 'workflow', label: 'Workflow Templates' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'draft':
        return 'secondary';
      case 'archived':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const kpiCards = [
    {
      title: 'Total Templates',
      value: mockStats.totalTemplates,
      icon: FileText,
      description: 'All template types',
    },
    {
      title: 'Active Templates',
      value: mockStats.activeTemplates,
      icon: Settings,
      description: 'Currently in use',
    },
    {
      title: 'Total Usage',
      value: mockStats.totalUsage.toLocaleString(),
      icon: Copy,
      description: 'Times templates used',
    },
    {
      title: 'Recently Updated',
      value: mockStats.recentlyUpdated,
      icon: Edit,
      description: 'Updated this month',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Template Management</h1>
            <p className="text-muted-foreground">
              Manage project templates, fee calculations, and badge systems
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import Template
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Template
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="templates" className="space-y-4">
          <TabsList>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="badges">Badges</TabsTrigger>
            <TabsTrigger value="fee-calculation">Fee Calculation</TabsTrigger>
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Template Library</CardTitle>
                <CardDescription>
                  Manage project templates and standardized configurations
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filters */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search templates..."
                        value={searchTerm}
                        onChange={(e: any) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {templateCategories.map((category: any) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Templates Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {mockTemplates
                    .filter(
                      (template) =>
                        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        template.description.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .filter(
                      (template) =>
                        selectedCategory === 'all' || template.category === selectedCategory
                    )
                    .map((template: any) => (
                      <Card
                        key={template.id}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedTemplate === template.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() =>
                          setSelectedTemplate(template.id === selectedTemplate ? null : template.id)
                        }
                      >
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-base">{template.name}</CardTitle>
                              <CardDescription className="text-sm">
                                v{template.version} • {template.usage_count} uses
                              </CardDescription>
                            </div>
                            <Badge variant={getStatusColor(template.status)}>
                              {template.status}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            {template.description}
                          </p>

                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>
                              Created by {template.created_by.first_name}{' '}
                              {template.created_by.last_name}
                            </span>
                            <span>{new Date(template.updated_at).toLocaleDateString()}</span>
                          </div>

                          {selectedTemplate === template.id && (
                            <div className="flex items-center space-x-2 mt-4 pt-4 border-t">
                              <Button size="sm" variant="outline">
                                <Edit className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                              <Button size="sm" variant="outline">
                                <Copy className="h-3 w-3 mr-1" />
                                Clone
                              </Button>
                              <Button size="sm" variant="outline">
                                <Download className="h-3 w-3 mr-1" />
                                Export
                              </Button>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="badges" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Badge System</CardTitle>
                <CardDescription>
                  Configure achievement badges and milestone rewards
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {mockBadges.map((badge: any) => (
                    <Card key={badge.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div
                              className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold"
                              style={{ backgroundColor: badge.color }}
                            >
                              <Award className="h-5 w-5" />
                            </div>
                            <div>
                              <CardTitle className="text-base">{badge.name}</CardTitle>
                              <CardDescription>{badge.criteria}</CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={badge.auto_assign ? 'default' : 'secondary'}>
                              {badge.auto_assign ? 'Auto' : 'Manual'}
                            </Badge>
                            <Badge variant={badge.active ? 'default' : 'outline'}>
                              {badge.active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            <Copy className="h-3 w-3 mr-1" />
                            Clone
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="flex justify-center mt-6">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create New Badge
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="fee-calculation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Fee Calculation Templates</CardTitle>
                <CardDescription>
                  Manage fee calculation formulas and pricing models
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockTemplates
                    .filter((template: any) => template.category === 'fee_calculation')
                    .map((template: any) => (
                      <Card key={template.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-base">{template.name}</CardTitle>
                              <CardDescription>
                                {template.description} • Used {template.usage_count} times
                              </CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">v{template.version}</Badge>
                              <Badge variant={getStatusColor(template.status)}>
                                {template.status}
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div>
                              <Label className="text-sm font-medium text-muted-foreground">
                                Base Rate
                              </Label>
                              <p className="text-sm">$125/hour</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-muted-foreground">
                                Complexity Multiplier
                              </Label>
                              <p className="text-sm">1.0x - 1.6x</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-muted-foreground">
                                Overhead
                              </Label>
                              <p className="text-sm">25%</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-muted-foreground">
                                Min Project Fee
                              </Label>
                              <p className="text-sm">$5,000</p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 mt-4">
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3 mr-1" />
                              Configure
                            </Button>
                            <Button size="sm" variant="outline">
                              <Copy className="h-3 w-3 mr-1" />
                              Duplicate
                            </Button>
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              Export
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>

                <div className="flex justify-center mt-6">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Fee Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="workflows" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Workflow Templates</CardTitle>
                <CardDescription>Define standard workflows and process templates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      name: 'Standard Project Workflow',
                      description: 'Default workflow for utility coordination projects',
                      steps: 8,
                      duration: '12-16 weeks',
                      usage: 234,
                    },
                    {
                      name: 'Emergency Response Workflow',
                      description: 'Expedited workflow for emergency utility work',
                      steps: 5,
                      duration: '2-4 weeks',
                      usage: 45,
                    },
                    {
                      name: 'Bridge Construction Workflow',
                      description: 'Specialized workflow for bridge projects',
                      steps: 12,
                      duration: '20-24 weeks',
                      usage: 78,
                    },
                  ].map((workflow, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-base">{workflow.name}</CardTitle>
                            <CardDescription>{workflow.description}</CardDescription>
                          </div>
                          <Badge variant="outline">{workflow.usage} uses</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Steps
                            </Label>
                            <p className="text-sm">{workflow.steps} workflow steps</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Duration
                            </Label>
                            <p className="text-sm">{workflow.duration}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Actions
                            </Label>
                            <div className="flex items-center space-x-2">
                              <Button size="sm" variant="outline">
                                <Edit className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                              <Button size="sm" variant="outline">
                                <Copy className="h-3 w-3 mr-1" />
                                Clone
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="flex justify-center mt-6">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Workflow Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}