import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import TemplateManagementClientPage from './client-page';

export default async function TemplateManagementPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch template management data for faster loading
  void api.projectTemplates.getAll.prefetch();

  return (
    <HydrateClient>
      <TemplateManagementClientPage 
        initialTemplates={undefined}
        initialBadges={undefined}
        initialTemplateStats={undefined}
      />
    </HydrateClient>
  );
}