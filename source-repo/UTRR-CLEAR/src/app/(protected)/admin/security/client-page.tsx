'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Shield, Lock, Key, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';
import { PINNED_CERTIFICATES } from '~/lib/security/certificate-pinning';
import { SRI_HASHES } from '~/components/security/external-scripts';
import { CSP_EXTERNAL_SOURCES } from '~/config/security/sri-hashes';

export default function SecurityDashboard() {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [testing, setTesting] = useState(false);

  const runSecurityTests = async () => {
    setTesting(true);
    const results: Record<string, boolean> = {};
    
    // Test CSP header
    try {
      const response = await fetch('/api/health');
      results.csp = !!response.headers.get('Content-Security-Policy');
      results.hsts = !!response.headers.get('Strict-Transport-Security');
      results.xFrameOptions = !!response.headers.get('X-Frame-Options');
      results.xContentType = !!response.headers.get('X-Content-Type-Options');
      results.noServerHeader = !response.headers.get('Server') && !response.headers.get('X-Powered-By');
    } catch (error) {
      console.error('Security test failed:', error);
    }
    
    setTestResults(results);
    setTesting(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Security Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Monitor and manage application security features
          </p>
        </div>
        <Button onClick={runSecurityTests} disabled={testing}>
          <RefreshCw className={`mr-2 h-4 w-4 ${testing ? 'animate-spin' : ''}`} />
          Run Security Tests
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Headers</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              CSP, HSTS, X-Frame-Options enabled
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Certificate Pinning</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(PINNED_CERTIFICATES).length}</div>
            <p className="text-xs text-muted-foreground">
              Domains with pinned certificates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SRI Hashes</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(SRI_HASHES).length}</div>
            <p className="text-xs text-muted-foreground">
              External resources with SRI
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CAPTCHA</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Enabled</div>
            <p className="text-xs text-muted-foreground">
              On login & password reset forms
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="headers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="headers">Security Headers</TabsTrigger>
          <TabsTrigger value="certificates">Certificate Pinning</TabsTrigger>
          <TabsTrigger value="sri">SRI Configuration</TabsTrigger>
          <TabsTrigger value="csp">CSP Policy</TabsTrigger>
        </TabsList>

        <TabsContent value="headers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Headers Status</CardTitle>
              <CardDescription>
                Current security headers configuration and test results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries({
                'Content-Security-Policy': testResults.csp,
                'Strict-Transport-Security': testResults.hsts,
                'X-Frame-Options': testResults.xFrameOptions,
                'X-Content-Type-Options': testResults.xContentType,
                'Server Headers Removed': testResults.noServerHeader,
              }).map(([header, status]) => (
                <div key={header} className="flex items-center justify-between">
                  <span className="font-medium">{header}</span>
                  {status === undefined ? (
                    <Badge variant="secondary">Not Tested</Badge>
                  ) : status ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Missing
                    </Badge>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="certificates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pinned Certificates</CardTitle>
              <CardDescription>
                Domains with certificate pinning enabled
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Object.entries(PINNED_CERTIFICATES).length === 0 ? (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    No certificate pins configured yet. Use the generate-certificate-pins script to add pins.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {Object.entries(PINNED_CERTIFICATES).map(([domain, pins]) => (
                    <div key={domain} className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-2">{domain}</h4>
                      <div className="space-y-1">
                        {pins.map((pin, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {pin.substring(0, 20)}...
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {index === 0 ? 'Primary' : `Backup ${index}`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sri" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subresource Integrity (SRI)</CardTitle>
              <CardDescription>
                External resources with integrity hashes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Object.entries(SRI_HASHES).length === 0 ? (
                <Alert>
                  <AlertDescription>
                    No external resources with SRI hashes configured. Most resources are loaded dynamically or handled by Next.js.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-2">
                  {Object.entries(SRI_HASHES).map(([resource, hash]) => (
                    <div key={resource} className="flex items-center justify-between">
                      <span className="text-sm font-mono">{resource}</span>
                      <Badge variant="outline">{String(hash).substring(0, 20)}...</Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="csp" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Security Policy</CardTitle>
              <CardDescription>
                Current CSP configuration for external sources
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(CSP_EXTERNAL_SOURCES).map(([directive, sources]) => (
                <div key={directive} className="space-y-2">
                  <h4 className="font-semibold">{directive}</h4>
                  <div className="flex flex-wrap gap-2">
                    {Array.isArray(sources) ? sources.map((source, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {source}
                      </Badge>
                    )) : (
                      <span className="text-sm text-muted-foreground">{sources}</span>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}