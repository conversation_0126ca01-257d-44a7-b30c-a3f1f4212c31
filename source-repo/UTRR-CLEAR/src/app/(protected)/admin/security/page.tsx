import type { Metadata } from 'next';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import SecurityDashboard from './client-page';

export const metadata: Metadata = {
  title: 'Security Dashboard - Admin',
  description: 'Monitor and manage security features',
};

export default async function SecurityPage() {
  const session = await getServerAuthSession();
  
  // Prefetch security data for faster loading
  void api.admin.getStats.prefetch();
  void api.users.getAll.prefetch({ limit: 100 });
  void api.rbac.getRoles.prefetch();
  
  return (
    <HydrateClient>
      <SecurityDashboard />
    </HydrateClient>
  );
}