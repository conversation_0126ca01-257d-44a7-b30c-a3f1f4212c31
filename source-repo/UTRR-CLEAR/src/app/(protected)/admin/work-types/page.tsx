import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import WorkTypesClientPage from './client-page';

export default async function WorkTypesPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Pre-fetch admin data
  void api.organizations.getCurrent.prefetch();
  void api.users.getAll.prefetch({});

  return (
    <HydrateClient>
      <WorkTypesClientPage 
        initialWorkTypes={undefined}
        initialWorkTypeStats={undefined}
      />
    </HydrateClient>
  );
}