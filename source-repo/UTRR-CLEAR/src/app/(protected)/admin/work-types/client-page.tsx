'use client';

import { useAuth } from '~/hooks/use-auth';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Switch } from '~/components/ui/switch';
import {
  Building,
  Calculator,
  Clock,
  DollarSign,
  Edit,
  MapPin,
  Plus,
  Route,
  Save,
  Search,
  Settings,
  Trash2,
  Wrench,
} from 'lucide-react';
import { api } from '~/trpc/react';

interface WorkType {
  id: string;
  name: string;
  category: string;
  description: string;
  base_rate: number;
  complexity_multiplier: number;
  estimated_hours: number;
  active: boolean;
  usage_count: number;
  requirements: string[];
  deliverables: string[];
}

interface WorkTypeStats {
  totalWorkTypes: number;
  activeWorkTypes: number;
  totalUsage: number;
  averageRate: number;
  mostUsed: string;
  recentlyUpdated: number;
}

interface WorkTypesClientPageProps {
  initialWorkTypes?: WorkType[];
  initialWorkTypeStats?: WorkTypeStats;
}

export default function WorkTypesClientPage({ 
  initialWorkTypes, 
  initialWorkTypeStats 
}: WorkTypesClientPageProps) {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedWorkType, setSelectedWorkType] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Work types data fetching - implement when admin router methods are available
  const workTypes = undefined; // api.admin.getWorkTypes.useQuery when implemented
  const workTypeStats = undefined; // api.admin.getWorkTypeStats.useQuery when implemented

  const mockWorkTypes = workTypes || initialWorkTypes || [
    {
      id: '1',
      name: 'Highway Widening',
      category: 'highway',
      description: 'Highway expansion and widening projects',
      base_rate: 125.0,
      complexity_multiplier: 1.2,
      estimated_hours: 480,
      active: true,
      usage_count: 34,
      requirements: [
        'SUE Quality Level B minimum',
        'Environmental impact assessment',
        'Traffic management plan',
        'Utility coordination meetings',
      ],
      deliverables: [
        'Utility conflict matrix',
        'Relocation drawings',
        'Cost estimates',
        'Project timeline',
      ],
    },
    {
      id: '2',
      name: 'Bridge Construction',
      category: 'bridge',
      description: 'New bridge construction and replacement',
      base_rate: 150.0,
      complexity_multiplier: 1.6,
      estimated_hours: 720,
      active: true,
      usage_count: 18,
      requirements: [
        'SUE Quality Level A required',
        'Structural engineering review',
        'Environmental permits',
        'Navigation clearance approval',
      ],
      deliverables: [
        'Detailed utility relocations',
        'Construction sequencing',
        'Traffic control plans',
        'Environmental monitoring',
      ],
    },
    {
      id: '3',
      name: 'Urban Development',
      category: 'urban',
      description: 'Mixed-use urban development projects',
      base_rate: 110.0,
      complexity_multiplier: 1.3,
      estimated_hours: 360,
      active: true,
      usage_count: 26,
      requirements: [
        'Municipal permits',
        'Zoning compliance review',
        'Underground utility mapping',
        'Parking analysis',
      ],
      deliverables: [
        'Site development plan',
        'Utility service agreements',
        'Traffic impact study',
        'Landscape architecture plans',
      ],
    },
    {
      id: '4',
      name: 'Airport Infrastructure',
      category: 'airport',
      description: 'Airport runway and terminal infrastructure',
      base_rate: 175.0,
      complexity_multiplier: 1.8,
      estimated_hours: 960,
      active: true,
      usage_count: 7,
      requirements: [
        'FAA compliance review',
        'Security clearance requirements',
        'Specialized utility systems',
        'Emergency response planning',
      ],
      deliverables: [
        'FAA compliance documentation',
        'Specialized utility designs',
        'Emergency response procedures',
        'Security system integration',
      ],
    },
    {
      id: '5',
      name: 'Residential Subdivision',
      category: 'residential',
      description: 'New residential subdivision development',
      base_rate: 95.0,
      complexity_multiplier: 0.9,
      estimated_hours: 240,
      active: false,
      usage_count: 45,
      requirements: [
        'Subdivision approval',
        'Storm water management',
        'Street lighting design',
        'Utility service sizing',
      ],
      deliverables: [
        'Subdivision utility plans',
        'Street improvement plans',
        'Storm water calculations',
        'Service connection details',
      ],
    },
  ];

  const mockStats = workTypeStats || initialWorkTypeStats || {
    totalWorkTypes: 23,
    activeWorkTypes: 18,
    totalUsage: 567,
    averageRate: 128.5,
    mostUsed: 'Highway Widening',
    recentlyUpdated: 5,
  };

  const workTypeCategories = [
    { value: 'all', label: 'All Categories', icon: Settings },
    { value: 'highway', label: 'Highway/Interstate', icon: Route },
    { value: 'bridge', label: 'Bridge Construction', icon: Building },
    { value: 'urban', label: 'Urban Development', icon: MapPin },
    { value: 'airport', label: 'Airport Infrastructure', icon: Building },
    { value: 'residential', label: 'Residential', icon: Building },
    { value: 'industrial', label: 'Industrial', icon: Wrench },
  ];

  const getCategoryIcon = (category: string) => {
    const categoryConfig = workTypeCategories.find((c: any) => c.value === category);
    return categoryConfig?.icon || Settings;
  };

  const kpiCards = [
    {
      title: 'Total Work Types',
      value: mockStats.totalWorkTypes,
      icon: Settings,
      description: 'All defined work types',
    },
    {
      title: 'Active Work Types',
      value: mockStats.activeWorkTypes,
      icon: Wrench,
      description: 'Currently available',
    },
    {
      title: 'Average Rate',
      value: `$${mockStats.averageRate}`,
      icon: DollarSign,
      description: 'Per hour billing rate',
    },
    {
      title: 'Total Usage',
      value: mockStats.totalUsage,
      icon: Calculator,
      description: 'Times used in projects',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Work Type Management</h1>
            <p className="text-muted-foreground">
              Define and manage project work types, rates, and requirements
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Calculator className="mr-2 h-4 w-4" />
              Bulk Update Rates
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Work Type
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="work-types" className="space-y-4">
          <TabsList>
            <TabsTrigger value="work-types">Work Types</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="rates">Rate Management</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="work-types" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Work Type Library</CardTitle>
                <CardDescription>
                  Manage work types, their rates, and project requirements
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filters */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search work types..."
                        value={searchTerm}
                        onChange={(e: any) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {workTypeCategories.map((category: any) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Work Types List */}
                <div className="space-y-4">
                  {mockWorkTypes
                    .filter(
                      (workType) =>
                        workType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        workType.description.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .filter(
                      (workType) => categoryFilter === 'all' || workType.category === categoryFilter
                    )
                    .map((workType: WorkType) => {
                      const CategoryIcon = getCategoryIcon(workType.category);
                      return (
                        <Card
                          key={workType.id}
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedWorkType === workType.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() =>
                            setSelectedWorkType(
                              workType.id === selectedWorkType ? null : workType.id
                            )
                          }
                        >
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <CategoryIcon className="h-5 w-5 text-blue-600" />
                                <div>
                                  <CardTitle className="text-base">{workType.name}</CardTitle>
                                  <CardDescription>{workType.description}</CardDescription>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Badge variant={workType.active ? 'default' : 'secondary'}>
                                  {workType.active ? 'Active' : 'Inactive'}
                                </Badge>
                                <Badge variant="outline">{workType.usage_count} uses</Badge>
                              </div>
                            </div>
                          </CardHeader>

                          {selectedWorkType === workType.id && (
                            <CardContent className="pt-0">
                              <div className="grid gap-6 md:grid-cols-2">
                                {/* Pricing Information */}
                                <div>
                                  <h4 className="font-medium mb-3">Pricing & Effort</h4>
                                  <div className="space-y-3">
                                    <div className="flex items-center justify-between text-sm">
                                      <span>Base Rate:</span>
                                      <span className="font-medium">
                                        ${workType.base_rate}/hour
                                      </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                      <span>Complexity Multiplier:</span>
                                      <span className="font-medium">
                                        {workType.complexity_multiplier}x
                                      </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                      <span>Estimated Hours:</span>
                                      <span className="font-medium">
                                        {workType.estimated_hours} hrs
                                      </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm border-t pt-2">
                                      <span className="font-medium">Estimated Cost:</span>
                                      <span className="font-medium text-lg">
                                        $
                                        {Math.round(
                                          workType.base_rate *
                                            workType.complexity_multiplier *
                                            workType.estimated_hours
                                        ).toLocaleString()}
                                      </span>
                                    </div>
                                  </div>
                                </div>

                                {/* Requirements & Deliverables */}
                                <div>
                                  <h4 className="font-medium mb-3">Requirements & Deliverables</h4>
                                  <div className="space-y-3">
                                    <div>
                                      <Label className="text-sm font-medium text-muted-foreground">
                                        Requirements:
                                      </Label>
                                      <ul className="text-sm space-y-1 mt-1">
                                        {workType.requirements.slice(0, 3).map((req: string, index: number) => (
                                          <li key={index} className="flex items-start space-x-2">
                                            <span className="text-muted-foreground">•</span>
                                            <span>{req}</span>
                                          </li>
                                        ))}
                                        {workType.requirements.length > 3 && (
                                          <li className="text-xs text-muted-foreground">
                                            +{workType.requirements.length - 3} more...
                                          </li>
                                        )}
                                      </ul>
                                    </div>

                                    <div>
                                      <Label className="text-sm font-medium text-muted-foreground">
                                        Deliverables:
                                      </Label>
                                      <ul className="text-sm space-y-1 mt-1">
                                        {workType.deliverables
                                          .slice(0, 3)
                                          .map((deliverable: string, index: number) => (
                                            <li key={index} className="flex items-start space-x-2">
                                              <span className="text-muted-foreground">•</span>
                                              <span>{deliverable}</span>
                                            </li>
                                          ))}
                                        {workType.deliverables.length > 3 && (
                                          <li className="text-xs text-muted-foreground">
                                            +{workType.deliverables.length - 3} more...
                                          </li>
                                        )}
                                      </ul>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2 mt-6 pt-4 border-t">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setIsEditing(true)}
                                >
                                  <Edit className="h-3 w-3 mr-1" />
                                  Edit
                                </Button>
                                <Button size="sm" variant="outline">
                                  <Calculator className="h-3 w-3 mr-1" />
                                  Calculate
                                </Button>
                                <Button size="sm" variant="outline">
                                  <Settings className="h-3 w-3 mr-1" />
                                  Configure
                                </Button>
                              </div>
                            </CardContent>
                          )}
                        </Card>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Work Type Categories</CardTitle>
                <CardDescription>Organize work types into logical categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {workTypeCategories.slice(1).map((category: any) => {
                    const Icon = category.icon;
                    const categoryCount = mockWorkTypes.filter(
                      (wt: WorkType) => wt.category === category.value
                    ).length;
                    const categoryUsage = mockWorkTypes
                      .filter((wt: WorkType) => wt.category === category.value)
                      .reduce((sum: number, wt: WorkType) => sum + wt.usage_count, 0);

                    return (
                      <Card key={category.value}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center space-x-3">
                            <Icon className="h-5 w-5 text-blue-600" />
                            <div>
                              <CardTitle className="text-base">{category.label}</CardTitle>
                              <CardDescription>
                                {categoryCount} work types • {categoryUsage} total uses
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline" className="flex-1">
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button size="sm" variant="outline" className="flex-1">
                              <Settings className="h-3 w-3 mr-1" />
                              Configure
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>

                <div className="flex justify-center mt-6">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Category
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Rate Management</CardTitle>
                <CardDescription>Manage billing rates and pricing models</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Global Rate Settings */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Global Rate Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-3">
                        <div>
                          <Label htmlFor="base-rate">Base Hourly Rate</Label>
                          <Input
                            id="base-rate"
                            type="number"
                            defaultValue="125.00"
                            placeholder="0.00"
                          />
                        </div>
                        <div>
                          <Label htmlFor="overhead-rate">Overhead Percentage</Label>
                          <Input
                            id="overhead-rate"
                            type="number"
                            defaultValue="25"
                            placeholder="0"
                          />
                        </div>
                        <div>
                          <Label htmlFor="profit-margin">Profit Margin</Label>
                          <Input
                            id="profit-margin"
                            type="number"
                            defaultValue="15"
                            placeholder="0"
                          />
                        </div>
                      </div>

                      <Button>
                        <Save className="mr-2 h-4 w-4" />
                        Save Global Settings
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Rate History */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Recent Rate Changes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          {
                            work_type: 'Highway Widening',
                            old_rate: 120.0,
                            new_rate: 125.0,
                            change_date: '2024-01-15',
                            changed_by: 'John Smith',
                          },
                          {
                            work_type: 'Bridge Construction',
                            old_rate: 145.0,
                            new_rate: 150.0,
                            change_date: '2024-01-10',
                            changed_by: 'Sarah Johnson',
                          },
                          {
                            work_type: 'Urban Development',
                            old_rate: 105.0,
                            new_rate: 110.0,
                            change_date: '2024-01-08',
                            changed_by: 'Mike Davis',
                          },
                        ].map((change, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 border rounded-lg"
                          >
                            <div>
                              <p className="font-medium text-sm">{change.work_type}</p>
                              <p className="text-xs text-muted-foreground">
                                ${change.old_rate} → ${change.new_rate} •{change.changed_by} •{' '}
                                {change.change_date}
                              </p>
                            </div>
                            <Badge variant="outline">
                              +
                              {(
                                ((change.new_rate - change.old_rate) / change.old_rate) *
                                100
                              ).toFixed(1)}
                              %
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Work Type Templates</CardTitle>
                <CardDescription>Pre-configured templates for common work types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {[
                    {
                      name: 'Standard Highway Template',
                      description: 'Typical highway widening and improvement projects',
                      base_rate: 125.0,
                      complexity: 1.2,
                      hours: 480,
                    },
                    {
                      name: 'Complex Bridge Template',
                      description: 'Major bridge construction and replacement projects',
                      base_rate: 150.0,
                      complexity: 1.6,
                      hours: 720,
                    },
                    {
                      name: 'Urban Redevelopment Template',
                      description: 'Mixed-use urban development projects',
                      base_rate: 110.0,
                      complexity: 1.3,
                      hours: 360,
                    },
                    {
                      name: 'Emergency Response Template',
                      description: 'Expedited emergency utility work',
                      base_rate: 175.0,
                      complexity: 2.0,
                      hours: 120,
                    },
                  ].map((template, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">{template.name}</CardTitle>
                        <CardDescription>{template.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Base Rate:</span>
                            <span className="font-medium">${template.base_rate}/hr</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Complexity:</span>
                            <span className="font-medium">{template.complexity}x</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Est. Hours:</span>
                            <span className="font-medium">{template.hours} hrs</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 mt-4">
                          <Button size="sm" variant="outline" className="flex-1">
                            Use Template
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="flex justify-center mt-6">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}