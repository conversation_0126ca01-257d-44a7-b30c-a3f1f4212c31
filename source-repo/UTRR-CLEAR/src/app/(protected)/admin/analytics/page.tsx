import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import AnalyticsClientPage from './client-page';

export default async function AnalyticsPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch analytics data for faster loading
  void api.admin.getStats.prefetch();
  void api.projects.getAll.prefetch({ limit: 100 });
  void api.users.getAll.prefetch({ limit: 100 });

  // Fetch initial data server-side
  const statsData = await api.admin.getStats();

  return (
    <HydrateClient>
      <AnalyticsClientPage 
        initialStatsData={statsData}
      />
    </HydrateClient>
  );
}
