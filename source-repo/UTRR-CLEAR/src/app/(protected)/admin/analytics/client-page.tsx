'use client';

import { useAuth } from '~/hooks/use-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import {
  Activity,
  AlertTriangle,
  BarChart3,
  CheckCircle,
  Clock,
  Download,
  FileText,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react';
import { api } from '~/trpc/react';

interface AnalyticsClientPageProps {
  initialStatsData?: any;
}

export default function AnalyticsClientPage({ 
  initialStatsData 
}: AnalyticsClientPageProps) {
  const { user } = useAuth();

  const { data: statsData, isLoading } = api.admin.getStats.useQuery(undefined, {
    initialData: initialStatsData,
  });

  // TODO: Implement getRecentActivity endpoint
  const recentActivity: any[] = [];

  const kpiCards = [
    {
      title: 'Total Projects',
      value: statsData?.projects || 0,
      change: '+12%',
      trend: 'up' as const,
      icon: FileText,
      description: 'From last month',
    },
    {
      title: 'Active Users',
      value: statsData?.users || 0,
      change: '+8%',
      trend: 'up' as const,
      icon: Users,
      description: 'From last month',
    },
    {
      title: 'Open Conflicts',
      value: statsData?.conflicts || 0,
      change: '-15%',
      trend: 'down' as const,
      icon: AlertTriangle,
      description: 'From last month',
    },
    {
      title: 'Completion Rate',
      value: '94%',
      change: '+3%',
      trend: 'up' as const,
      icon: CheckCircle,
      description: 'Project completion rate',
    },
  ];

  const chartData = {
    projectActivity: [
      { name: 'Jan', value: 12 },
      { name: 'Feb', value: 19 },
      { name: 'Mar', value: 15 },
      { name: 'Apr', value: 28 },
      { name: 'May', value: 23 },
      { name: 'Jun', value: 34 },
    ],
    conflictResolution: [
      { name: 'Week 1', resolved: 8, pending: 12 },
      { name: 'Week 2', resolved: 12, pending: 8 },
      { name: 'Week 3', resolved: 15, pending: 5 },
      { name: 'Week 4', resolved: 18, pending: 3 },
    ],
  };

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
            <p className="text-muted-foreground">System insights and performance metrics</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button>
              <Activity className="mr-2 h-4 w-4" />
              Live View
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <div className="flex items-center space-x-2 text-xs">
                    <div
                      className={`flex items-center ${
                        kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {kpi.trend === 'up' ? (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      )}
                      {kpi.change}
                    </div>
                    <span className="text-muted-foreground">{kpi.description}</span>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Charts Section */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Project Activity</CardTitle>
              <CardDescription>Monthly project creation and completion trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                  <p className="text-sm text-gray-600">
                    Interactive charts will be implemented with Chart.js or D3
                  </p>
                  <div className="mt-4 space-y-2">
                    {chartData.projectActivity.map((item, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span>{item.name}</span>
                        <Badge variant="outline">{item.value} projects</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Conflict Resolution</CardTitle>
              <CardDescription>Weekly conflict detection and resolution statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Activity className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                  <p className="text-sm text-gray-600">Real-time conflict resolution tracking</p>
                  <div className="mt-4 space-y-2">
                    {chartData.conflictResolution.map((item, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span>{item.name}</span>
                        <div className="flex space-x-2">
                          <Badge variant="default">{item.resolved} resolved</Badge>
                          <Badge variant="secondary">{item.pending} pending</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <div className="grid gap-6 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Project Status Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active</span>
                  <Badge variant="outline">{statsData?.activeProjects || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total</span>
                  <Badge variant="outline">{statsData?.projects || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Completion Rate</span>
                  <Badge variant="default">
                    {statsData?.projects ? Math.round(((statsData.activeProjects || 0) / statsData.projects) * 100) : 0}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Conflict Priority Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Open Conflicts</span>
                  <Badge variant="destructive">{statsData?.conflicts || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">High Priority</span>
                  <Badge variant="destructive">
                    {Math.floor((statsData?.conflicts || 0) * 0.3)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Medium Priority</span>
                  <Badge variant="default">
                    {Math.floor((statsData?.conflicts || 0) * 0.5)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Low Priority</span>
                  <Badge variant="secondary">
                    {Math.floor((statsData?.conflicts || 0) * 0.2)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Uptime</span>
                  <Badge variant="default">99.9%</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Response Time</span>
                  <Badge variant="outline">120ms</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Error Rate</span>
                  <Badge variant="secondary">0.1%</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active Sessions</span>
                  <Badge variant="outline">{statsData?.users || 0}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Recent System Activity</CardTitle>
            <CardDescription>Summary of recent activities and system events</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="text-sm text-muted-foreground mt-2">Loading activity data...</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentActivity?.slice(0, 5).map((activity: any) => (
                  <div
                    key={activity.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <p className="font-medium text-sm">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {activity.user} • {activity.metadata?.projectName || 'System'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {new Date(activity.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No recent activities</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}