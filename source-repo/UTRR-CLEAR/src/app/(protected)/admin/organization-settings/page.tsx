import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import OrganizationSettingsClientPage from './client-page';

export default async function OrganizationSettingsPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch organization settings data for faster loading
  void api.organizations.getCurrent.prefetch();

  // Fetch organization data server-side
  const organization = await api.organizations.getCurrent();

  return (
    <HydrateClient>
      <OrganizationSettingsClientPage organization={organization} />
    </HydrateClient>
  );
}