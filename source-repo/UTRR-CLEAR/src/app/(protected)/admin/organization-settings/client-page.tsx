'use client';

import { useState } from 'react';
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Switch } from '~/components/ui/switch';
import { Separator } from '~/components/ui/separator';
import {
  Building,
  Globe,
  Key,
  Mail,
  MapPin,
  Phone,
  Save,
  Settings,
  Shield,
  Upload,
  Users,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { FileUpload } from '~/components/ui/file-upload';
import Image from 'next/image';
import { toast } from 'sonner';
import { Image as ImageIcon } from 'lucide-react';
import { ThemeEditor } from '~/components/admin/theme-editor';
import { ArchitectureVisualizer } from '~/components/admin/architecture-visualizer';
import { EmailIntegrationSettings } from '~/components/admin/email-integration-settings';
import { LogoDisplaySettings } from '~/components/admin/logo-display-settings';
import { safeLog } from '~/lib/error-handler';
interface OrganizationSettingsClientPageProps {
  organization: {
    id: string;
    name: string;
    theme_config?: unknown;
    [key: string]: unknown;
  } | null;
}

export default function OrganizationSettingsClientPage({ organization }: OrganizationSettingsClientPageProps) {
  const [isLoading, setIsLoading] = useState(false);
  const utils = api.useUtils();
  
  // Update organization mutation
  const updateOrganization = api.organizations.update.useMutation({
    onSuccess: () => {
      toast.success('Organization settings updated successfully');
      utils.organizations.getCurrent.invalidate();
    },
    onError: (error: unknown) => {
      toast.error('Failed to update organization settings');
      safeLog.error('Update error:', { error: error instanceof Error ? error.message : String(error) });
    },
  });

  // Organization Info State - Initialize from props
  const [orgInfo, setOrgInfo] = useState({
    name: String(organization?.name || ''),
    logo_url: String(organization?.logo_url || ''),
    signin_footer_url: String(organization?.signin_footer_url || ''),
    address: String(organization?.address || ''),
    city: String(organization?.city || ''),
    state: String(organization?.state || ''),
    zip_code: String(organization?.zip_code || ''),
    contact_phone: String(organization?.contact_phone || ''),
    contact_email: String(organization?.contact_email || ''),
    website: String(organization?.website || ''),
  });

  // System Settings State - Initialize from props
  const [systemSettings, setSystemSettings] = useState({
    timezone: String(organization?.timezone || 'America/New_York'),
    date_format: String(organization?.date_format || 'MM/DD/YYYY'),
    currency: String(organization?.currency || 'USD'),
  });

  // API Integration Settings
  const [apiSettings, setApiSettings] = useState({
    monday_api_key: '••••••••••••••••',
    monday_board_id: '123456789',
    google_maps_api_key: '••••••••••••••••',
    smtp_server: 'smtp.gmail.com',
    smtp_port: 587,
    smtp_username: '<EMAIL>',
    smtp_password: '••••••••••••••••',
  });

  // Billing Settings State
  const [billingSettings, setBillingSettings] = useState({
    billing_address: '123 Main Street',
    billing_city: 'Indianapolis',
    billing_state: 'IN',
    billing_zip: '46201',
    payment_method: 'credit_card',
    billing_contact: 'John Smith',
    billing_email: '<EMAIL>',
    auto_renew: true,
  });

  const timezones = [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'UTC', label: 'Coordinated Universal Time (UTC)' },
  ];

  const dateFormats = [
    { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY (US Format)' },
    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY (European Format)' },
    { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD (ISO Format)' },
  ];

  const currencies = [
    { value: 'USD', label: 'US Dollar ($)' },
    { value: 'CAD', label: 'Canadian Dollar (C$)' },
    { value: 'EUR', label: 'Euro (€)' },
    { value: 'GBP', label: 'British Pound (£)' },
  ];

  const handleSave = async (section: string) => {
    if (!organization) return;
    
    setIsLoading(true);
    try {
      if (section === 'organization' || section === 'all') {
        await updateOrganization.mutateAsync({
          ...orgInfo,
        });
      }
      
      if (section === 'system' || section === 'all') {
        // TODO: System settings (timezone, date_format, currency) need to be added to organization schema
        // await updateOrganization.mutateAsync({
        //   ...systemSettings,
        // });
        toast.success('System settings saved (schema update needed)');
      }
    } catch (error) {
      safeLog.error('Save error:', { error: String(error) });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Organization Settings</h1>
            <p className="text-muted-foreground">
              Configure organization profile, system settings, and integrations
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import Settings
            </Button>
            <Button onClick={() => handleSave('all')}>
              <Save className="mr-2 h-4 w-4" />
              Save All
            </Button>
          </div>
        </div>

        <Tabs defaultValue="organization" className="space-y-4">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="organization">Organization</TabsTrigger>
            <TabsTrigger value="logos">Logos</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
            <TabsTrigger value="architecture">Architecture</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
          </TabsList>

          <TabsContent value="organization" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5" />
                  Organization Information
                </CardTitle>
                <CardDescription>
                  Basic organization details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Logo and Sign-in Image Uploads */}
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <Label className="flex items-center gap-2 mb-2">
                      <ImageIcon className="h-4 w-4" />
                      Organization Logo
                    </Label>
                    <div className="space-y-2">
                      {orgInfo.logo_url && (
                        <div className="relative w-40 h-12 mb-2">
                          {orgInfo.logo_url.toLowerCase().endsWith('.svg') ? (
                            <>
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                src={orgInfo.logo_url}
                                alt="Organization Logo"
                                className="w-full h-full object-contain"
                                onError={(e) => {
                                  safeLog.error('SVG failed to load:', { url: orgInfo.logo_url });
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            </>
                          ) : (
                            <Image
                              src={orgInfo.logo_url}
                              alt="Organization Logo"
                              fill
                              className="object-contain"
                            />
                          )}
                        </div>
                      )}
                      <FileUpload
                        onFileSelect={(file) => {
                          safeLog.info('Logo file selected:', { fileName: file.name });
                        }}
                        onUploadComplete={(result) => {
                          safeLog.info('Logo upload result:', { result: JSON.stringify(result) });
                          if (result?.file?.url) {
                            setOrgInfo((prev) => ({ ...prev, logo_url: result.file.url }));
                          } else if (result?.url) {
                            setOrgInfo((prev) => ({ ...prev, logo_url: result.url }));
                          }
                        }}
                        accept={{
                          'image/svg+xml': ['.svg']
                        }}
                        maxSize={5 * 1024 * 1024}
                        uploadEndpoint="/api/upload"
                      />
                      <p className="text-sm text-muted-foreground">
                        SVG format required for theme compatibility. Recommended: 160×48px, transparent background
                      </p>
                    </div>
                  </div>
                </div>

                {/* Sign-in Footer Image Upload */}
                <div className="mt-6">
                  <Label className="flex items-center gap-2 mb-2">
                    <ImageIcon className="h-4 w-4" />
                    Sign-in Footer Image
                  </Label>
                  <div className="space-y-2">
                    {orgInfo.signin_footer_url && (
                      <div className="relative w-full h-32 mb-2">
                        <Image
                          src={orgInfo.signin_footer_url}
                          alt="Sign-in Footer Image"
                          fill
                          className="object-contain rounded-lg border"
                        />
                      </div>
                    )}
                    <FileUpload
                      onFileSelect={(file) => {
                        safeLog.info('Footer image file selected:', { fileName: file.name });
                      }}
                      onUploadComplete={(result) => {
                        safeLog.info('Footer image upload result:', { result: JSON.stringify(result) });
                        if (result?.file?.url) {
                          setOrgInfo((prev) => ({ ...prev, signin_footer_url: result.file.url }));
                        } else if (result?.url) {
                          setOrgInfo((prev) => ({ ...prev, signin_footer_url: result.url }));
                        }
                      }}
                      accept={{
                        'image/png': ['.png'],
                        'image/jpeg': ['.jpg', '.jpeg'],
                        'image/webp': ['.webp']
                      }}
                      maxSize={5 * 1024 * 1024}
                      uploadEndpoint="/api/upload"
                    />
                    <p className="text-sm text-muted-foreground">
                      Optional footer image shown at the bottom of the sign-in form when no decorative image is set.
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Leave empty to hide the footer image. Recommended: 300×200px
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="org-name">Organization Name</Label>
                  <Input
                    id="org-name"
                    value={orgInfo.name}
                    onChange={(e) => setOrgInfo((prev) => ({ ...prev, name: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={orgInfo.address}
                    onChange={(e) => setOrgInfo((prev) => ({ ...prev, address: e.target.value }))}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={orgInfo.city}
                      onChange={(e) => setOrgInfo((prev) => ({ ...prev, city: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={orgInfo.state}
                      onChange={(e) => setOrgInfo((prev) => ({ ...prev, state: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="zip">ZIP Code</Label>
                    <Input
                      id="zip"
                      value={orgInfo.zip_code}
                      onChange={(e) => setOrgInfo((prev) => ({ ...prev, zip_code: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={orgInfo.contact_phone}
                      onChange={(e) => setOrgInfo((prev) => ({ ...prev, contact_phone: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={orgInfo.contact_email}
                      onChange={(e) => setOrgInfo((prev) => ({ ...prev, contact_email: e.target.value }))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={orgInfo.website}
                    onChange={(e) => setOrgInfo((prev) => ({ ...prev, website: e.target.value }))}
                  />
                </div>

                <Button onClick={() => handleSave('organization')} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Organization Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logos" className="space-y-4">
            <LogoDisplaySettings />
          </TabsContent>

          <TabsContent value="theme" className="space-y-4">
            <ThemeEditor />
          </TabsContent>

          <TabsContent value="architecture" className="space-y-4">
            <ArchitectureVisualizer />
          </TabsContent>

          <TabsContent value="system" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="mr-2 h-5 w-5" />
                  System Configuration
                </CardTitle>
                <CardDescription>Global system settings and preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="timezone">Default Timezone</Label>
                    <Select
                      value={systemSettings.timezone}
                      onValueChange={(value) =>
                        setSystemSettings((prev) => ({ ...prev, timezone: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timezones.map((tz) => (
                          <SelectItem key={tz.value} value={tz.value}>
                            {tz.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="date-format">Date Format</Label>
                    <Select
                      value={systemSettings.date_format}
                      onValueChange={(value) =>
                        setSystemSettings((prev) => ({ ...prev, date_format: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {dateFormats.map((format) => (
                          <SelectItem key={format.value} value={format.value}>
                            {format.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="currency">Default Currency</Label>
                    <Select
                      value={systemSettings.currency}
                      onValueChange={(value) =>
                        setSystemSettings((prev) => ({ ...prev, currency: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.map((currency) => (
                          <SelectItem key={currency.value} value={currency.value}>
                            {currency.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button onClick={() => handleSave('system')} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save System Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integrations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="mr-2 h-5 w-5" />
                  API Integrations
                </CardTitle>
                <CardDescription>Configure third-party API keys and integrations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-medium mb-4">Monday.com Integration</h4>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="monday-api-key">API Key</Label>
                      <Input
                        id="monday-api-key"
                        type="password"
                        value={apiSettings.monday_api_key}
                        onChange={(e: any) =>
                          setApiSettings((prev) => ({ ...prev, monday_api_key: e.target.value }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="monday-board-id">Board ID</Label>
                      <Input
                        id="monday-board-id"
                        value={apiSettings.monday_board_id}
                        onChange={(e: any) =>
                          setApiSettings((prev) => ({ ...prev, monday_board_id: e.target.value }))
                        }
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">Google Maps Integration</h4>
                  <div>
                    <Label htmlFor="google-maps-api-key">Google Maps API Key</Label>
                    <Input
                      id="google-maps-api-key"
                      type="password"
                      value={apiSettings.google_maps_api_key}
                      onChange={(e: any) =>
                        setApiSettings((prev) => ({ ...prev, google_maps_api_key: e.target.value }))
                      }
                    />
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">Email Configuration (SMTP)</h4>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="smtp-server">SMTP Server</Label>
                      <Input
                        id="smtp-server"
                        value={apiSettings.smtp_server}
                        onChange={(e: any) =>
                          setApiSettings((prev) => ({ ...prev, smtp_server: e.target.value }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="smtp-port">Port</Label>
                      <Input
                        id="smtp-port"
                        type="number"
                        value={apiSettings.smtp_port}
                        onChange={(e: any) =>
                          setApiSettings((prev) => ({
                            ...prev,
                            smtp_port: parseInt(e.target.value),
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="grid gap-4 md:grid-cols-2 mt-4">
                    <div>
                      <Label htmlFor="smtp-username">Username</Label>
                      <Input
                        id="smtp-username"
                        value={apiSettings.smtp_username}
                        onChange={(e: any) =>
                          setApiSettings((prev) => ({ ...prev, smtp_username: e.target.value }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="smtp-password">Password</Label>
                      <Input
                        id="smtp-password"
                        type="password"
                        value={apiSettings.smtp_password}
                        onChange={(e: any) =>
                          setApiSettings((prev) => ({ ...prev, smtp_password: e.target.value }))
                        }
                      />
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                <EmailIntegrationSettings />

                <Button onClick={() => handleSave('integrations')} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Integration Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  Security Settings
                </CardTitle>
                <CardDescription>Configure security policies and access controls</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="password-policy">Password Policy</Label>
                      <Select defaultValue="medium">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low (8 characters)</SelectItem>
                          <SelectItem value="medium">Medium (8+ with symbols)</SelectItem>
                          <SelectItem value="high">High (12+ complex)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="session-policy">Session Policy</Label>
                      <Select defaultValue="standard">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="strict">Strict (1 hour)</SelectItem>
                          <SelectItem value="standard">Standard (8 hours)</SelectItem>
                          <SelectItem value="extended">Extended (24 hours)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="ip-whitelist">IP Address Whitelist</Label>
                      <Textarea
                        id="ip-whitelist"
                        placeholder="Enter IP addresses or ranges, one per line"
                        className="min-h-[100px]"
                      />
                    </div>
                    <div>
                      <Label htmlFor="allowed-domains">Allowed Email Domains</Label>
                      <Textarea
                        id="allowed-domains"
                        placeholder="Enter allowed domains, one per line"
                        className="min-h-[100px]"
                      />
                    </div>
                  </div>
                </div>

                <Button onClick={() => handleSave('security')} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Security Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="billing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Billing Information
                </CardTitle>
                <CardDescription>Manage billing details and subscription settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-medium mb-4">Billing Address</h4>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="billing-address">Address</Label>
                      <Input
                        id="billing-address"
                        value={billingSettings.billing_address}
                        onChange={(e: any) =>
                          setBillingSettings((prev) => ({
                            ...prev,
                            billing_address: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div>
                        <Label htmlFor="billing-city">City</Label>
                        <Input
                          id="billing-city"
                          value={billingSettings.billing_city}
                          onChange={(e: any) =>
                            setBillingSettings((prev) => ({
                              ...prev,
                              billing_city: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="billing-state">State</Label>
                        <Input
                          id="billing-state"
                          value={billingSettings.billing_state}
                          onChange={(e: any) =>
                            setBillingSettings((prev) => ({
                              ...prev,
                              billing_state: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="billing-zip">ZIP Code</Label>
                        <Input
                          id="billing-zip"
                          value={billingSettings.billing_zip}
                          onChange={(e: any) =>
                            setBillingSettings((prev) => ({ ...prev, billing_zip: e.target.value }))
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">Billing Contact</h4>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="billing-contact">Contact Name</Label>
                      <Input
                        id="billing-contact"
                        value={billingSettings.billing_contact}
                        onChange={(e: any) =>
                          setBillingSettings((prev) => ({
                            ...prev,
                            billing_contact: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="billing-email">Email Address</Label>
                      <Input
                        id="billing-email"
                        type="email"
                        value={billingSettings.billing_email}
                        onChange={(e: any) =>
                          setBillingSettings((prev) => ({ ...prev, billing_email: e.target.value }))
                        }
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">Subscription Settings</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-renew">Auto-Renewal</Label>
                        <p className="text-sm text-muted-foreground">
                          Automatically renew subscription
                        </p>
                      </div>
                      <Switch
                        id="auto-renew"
                        checked={billingSettings.auto_renew}
                        onCheckedChange={(checked) =>
                          setBillingSettings((prev) => ({ ...prev, auto_renew: checked }))
                        }
                      />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base">Current Plan</CardTitle>
                          <CardDescription>Professional Plan</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Users:</span>
                              <span className="font-medium">50</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Storage:</span>
                              <span className="font-medium">1TB</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Price:</span>
                              <span className="font-medium">$299/month</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base">Next Billing</CardTitle>
                          <CardDescription>February 15, 2024</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Amount:</span>
                              <span className="font-medium">$299.00</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Method:</span>
                              <span className="font-medium">•••• 4242</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Status:</span>
                              <Badge variant="default">Active</Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </div>

                <Button onClick={() => handleSave('billing')} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Billing Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}