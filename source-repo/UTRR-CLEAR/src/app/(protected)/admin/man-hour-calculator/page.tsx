import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import ManHourCalculatorClientPage from './client-page';

export default async function ManHourCalculatorPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch calculator data for faster loading
  void api.projects.getAll.prefetch({ limit: 100 });
  void api.organizations.getCurrent.prefetch();

  return (
    <HydrateClient>
      <ManHourCalculatorClientPage />
    </HydrateClient>
  );
}
