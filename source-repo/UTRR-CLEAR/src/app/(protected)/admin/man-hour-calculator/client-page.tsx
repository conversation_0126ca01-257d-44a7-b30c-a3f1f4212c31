'use client';

import { useAuth } from '~/hooks/use-auth';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Calculator,
  Clock,
  DollarSign,
  Download,
  FileSpreadsheet,
  History,
  MapPin,
  Plus,
  Save,
  Settings,
  Users,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { safeLog } from '~/lib/error-handler';

interface CalculationResult {
  totalHours: number;
  laborCost: number;
  overhead: number;
  totalCost: number;
  breakdown: {
    design: number;
    coordination: number;
    fieldWork: number;
    documentation: number;
    management: number;
  };
}

export default function ManHourCalculatorClientPage() {
  const { user } = useAuth();
  const [projectType, setProjectType] = useState<string>('');
  const [projectSize, setProjectSize] = useState<string>('');
  const [complexity, setComplexity] = useState<string>('');
  const [utilityCount, setUtilityCount] = useState<string>('');
  const [relocationCount, setRelocationCount] = useState<string>('');
  const [calculation, setCalculation] = useState<CalculationResult | null>(null);
  const [customRates, setCustomRates] = useState({
    engineer: 125,
    coordinator: 95,
    technician: 75,
    admin: 65,
  });

  // TODO: Implement API endpoints for project templates, calculation history, and default rates
  const projectTemplates = undefined;
  const calculationHistory = undefined;
  const defaultRates = undefined;

  const projectTypes = [
    { value: 'highway', label: 'Highway/Interstate', multiplier: 1.0 },
    { value: 'bridge', label: 'Bridge Construction', multiplier: 1.3 },
    { value: 'urban', label: 'Urban Development', multiplier: 1.2 },
    { value: 'industrial', label: 'Industrial Site', multiplier: 1.1 },
    { value: 'residential', label: 'Residential Development', multiplier: 0.8 },
    { value: 'airport', label: 'Airport Infrastructure', multiplier: 1.4 },
    { value: 'railroad', label: 'Railroad Crossing', multiplier: 1.2 },
  ];

  const projectSizes = [
    { value: 'small', label: 'Small (< 1 mile)', baseHours: 120 },
    { value: 'medium', label: 'Medium (1-5 miles)', baseHours: 320 },
    { value: 'large', label: 'Large (5-10 miles)', baseHours: 680 },
    { value: 'xlarge', label: 'Extra Large (> 10 miles)', baseHours: 1200 },
  ];

  const complexityLevels = [
    { value: 'low', label: 'Low Complexity', multiplier: 0.8 },
    { value: 'medium', label: 'Medium Complexity', multiplier: 1.0 },
    { value: 'high', label: 'High Complexity', multiplier: 1.3 },
    { value: 'critical', label: 'Critical Infrastructure', multiplier: 1.6 },
  ];

  const mockHistory = calculationHistory || [
    {
      id: '1',
      project_name: 'I-65 Widening Project',
      project_type: 'highway',
      total_hours: 456,
      total_cost: 47800,
      created_at: '2024-01-15T10:30:00Z',
      user: { first_name: 'John', last_name: 'Smith' },
    },
    {
      id: '2',
      project_name: 'Downtown Bridge Replacement',
      project_type: 'bridge',
      total_hours: 892,
      total_cost: 98400,
      created_at: '2024-01-14T14:22:00Z',
      user: { first_name: 'Sarah', last_name: 'Johnson' },
    },
    {
      id: '3',
      project_name: 'Airport Terminal Expansion',
      project_type: 'airport',
      total_hours: 1234,
      total_cost: 145600,
      created_at: '2024-01-13T09:15:00Z',
      user: { first_name: 'Mike', last_name: 'Davis' },
    },
  ];

  const calculateManHours = () => {
    if (!projectType || !projectSize || !complexity) return;

    const typeConfig = projectTypes.find((t: any) => t.value === projectType);
    const sizeConfig = projectSizes.find((s: any) => s.value === projectSize);
    const complexityConfig = complexityLevels.find((c: any) => c.value === complexity);

    if (!typeConfig || !sizeConfig || !complexityConfig) return;

    const baseHours = sizeConfig.baseHours;
    const typeMultiplier = typeConfig.multiplier;
    const complexityMultiplier = complexityConfig.multiplier;
    const utilityMultiplier = utilityCount ? 1 + parseInt(utilityCount) * 0.1 : 1;
    const relocationMultiplier = relocationCount ? 1 + parseInt(relocationCount) * 0.15 : 1;

    const totalHours =
      baseHours * typeMultiplier * complexityMultiplier * utilityMultiplier * relocationMultiplier;

    // Breakdown by activity
    const breakdown = {
      design: Math.round(totalHours * 0.25),
      coordination: Math.round(totalHours * 0.35),
      fieldWork: Math.round(totalHours * 0.2),
      documentation: Math.round(totalHours * 0.15),
      management: Math.round(totalHours * 0.05),
    };

    // Cost calculation
    const laborCost =
      breakdown.design * customRates.engineer +
      breakdown.coordination * customRates.coordinator +
      breakdown.fieldWork * customRates.technician +
      breakdown.documentation * customRates.admin +
      breakdown.management * customRates.engineer;

    const overhead = laborCost * 0.25; // 25% overhead
    const totalCost = laborCost + overhead;

    const result: CalculationResult = {
      totalHours: Math.round(totalHours),
      laborCost: Math.round(laborCost),
      overhead: Math.round(overhead),
      totalCost: Math.round(totalCost),
      breakdown,
    };

    setCalculation(result);
  };

  const exportToExcel = () => {
    // This would generate an Excel file with the calculation results
    safeLog.info('Exporting to Excel...', calculation ? { calculation } : undefined);
  };

  const saveCalculation = () => {
    // This would save the calculation to the database
    safeLog.info('Saving calculation...', calculation ? { calculation } : undefined);
  };

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Man-Hour Calculator</h1>
            <p className="text-muted-foreground">
              Calculate project effort and costs for utility coordination projects
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Configure Rates
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Templates
            </Button>
          </div>
        </div>

        <Tabs defaultValue="calculator" className="space-y-4">
          <TabsList>
            <TabsTrigger value="calculator">Calculator</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="rates">Rate Management</TabsTrigger>
          </TabsList>

          <TabsContent value="calculator" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Input Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Project Parameters</CardTitle>
                  <CardDescription>
                    Enter project details to calculate man-hours and costs
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="project-type">Project Type</Label>
                    <Select value={projectType} onValueChange={setProjectType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select project type" />
                      </SelectTrigger>
                      <SelectContent>
                        {projectTypes.map((type: any) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="project-size">Project Size</Label>
                    <Select value={projectSize} onValueChange={setProjectSize}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select project size" />
                      </SelectTrigger>
                      <SelectContent>
                        {projectSizes.map((size: any) => (
                          <SelectItem key={size.value} value={size.value}>
                            {size.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="complexity">Complexity Level</Label>
                    <Select value={complexity} onValueChange={setComplexity}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select complexity" />
                      </SelectTrigger>
                      <SelectContent>
                        {complexityLevels.map((level: any) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="utility-count">Number of Utilities</Label>
                      <Input
                        id="utility-count"
                        type="number"
                        value={utilityCount}
                        onChange={(e: any) => setUtilityCount(e.target.value)}
                        placeholder="e.g., 8"
                      />
                    </div>
                    <div>
                      <Label htmlFor="relocation-count">Relocations Required</Label>
                      <Input
                        id="relocation-count"
                        type="number"
                        value={relocationCount}
                        onChange={(e: any) => setRelocationCount(e.target.value)}
                        placeholder="e.g., 3"
                      />
                    </div>
                  </div>

                  <Button onClick={calculateManHours} className="w-full">
                    <Calculator className="mr-2 h-4 w-4" />
                    Calculate Man-Hours
                  </Button>
                </CardContent>
              </Card>

              {/* Results */}
              <Card>
                <CardHeader>
                  <CardTitle>Calculation Results</CardTitle>
                  <CardDescription>Estimated effort and cost breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  {calculation ? (
                    <div className="space-y-6">
                      {/* Summary Cards */}
                      <div className="grid gap-4 md:grid-cols-2">
                        <Card>
                          <CardHeader className="pb-3">
                            <div className="flex items-center space-x-2">
                              <Clock className="h-4 w-4 text-blue-600" />
                              <CardTitle className="text-base">Total Hours</CardTitle>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">{calculation.totalHours}</div>
                            <p className="text-sm text-muted-foreground">Person-hours</p>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-3">
                            <div className="flex items-center space-x-2">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <CardTitle className="text-base">Total Cost</CardTitle>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              ${calculation.totalCost.toLocaleString()}
                            </div>
                            <p className="text-sm text-muted-foreground">Including overhead</p>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Breakdown */}
                      <div>
                        <h4 className="font-medium mb-3">Effort Breakdown</h4>
                        <div className="space-y-3">
                          {Object.entries(calculation.breakdown).map(([activity, hours]) => (
                            <div key={activity} className="flex items-center justify-between">
                              <span className="text-sm capitalize">
                                {activity.replace(/([A-Z])/g, ' $1').trim()}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium">{hours} hrs</span>
                                <Badge variant="outline">
                                  {Math.round((hours / calculation.totalHours) * 100)}%
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Cost Breakdown */}
                      <div>
                        <h4 className="font-medium mb-3">Cost Breakdown</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between text-sm">
                            <span>Labor Cost</span>
                            <span className="font-medium">
                              ${calculation.laborCost.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>Overhead (25%)</span>
                            <span className="font-medium">
                              ${calculation.overhead.toLocaleString()}
                            </span>
                          </div>
                          <div className="border-t pt-2 flex items-center justify-between font-medium">
                            <span>Total Cost</span>
                            <span>${calculation.totalCost.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button onClick={saveCalculation} className="flex-1">
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button onClick={exportToExcel} variant="outline" className="flex-1">
                          <FileSpreadsheet className="mr-2 h-4 w-4" />
                          Export
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Calculator className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-sm text-muted-foreground">
                        Enter project parameters and click calculate to see results
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Calculation Templates</CardTitle>
                <CardDescription>Pre-configured templates for common project types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {projectTypes.map((type: any) => (
                    <Card key={type.value}>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">{type.label}</CardTitle>
                        <CardDescription>
                          Optimized for {type.label.toLowerCase()} projects
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Multiplier:</span>
                            <span className="font-medium">{type.multiplier}x</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Complexity:</span>
                            <span className="font-medium">Medium</span>
                          </div>
                        </div>
                        <Button size="sm" className="w-full mt-3">
                          Use Template
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Calculation History</CardTitle>
                <CardDescription>Previous calculations and their results</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockHistory.map((calc: any) => (
                    <Card key={calc.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-base">{calc.project_name}</CardTitle>
                            <CardDescription className="flex items-center space-x-4">
                              <Badge variant="outline" className="capitalize">
                                {calc.project_type}
                              </Badge>
                              <span>
                                {calc.user.first_name} {calc.user.last_name}
                              </span>
                              <span>{new Date(calc.created_at).toLocaleDateString()}</span>
                            </CardDescription>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-semibold">{calc.total_hours} hrs</div>
                            <div className="text-sm text-muted-foreground">
                              ${calc.total_cost.toLocaleString()}
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Hourly Rates Management</CardTitle>
                <CardDescription>Configure billing rates for different role types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-medium">Current Rates</h4>
                    {Object.entries(customRates).map(([role, rate]) => (
                      <div key={role} className="flex items-center space-x-4">
                        <Label className="w-24 capitalize">{role}:</Label>
                        <div className="flex items-center space-x-2">
                          <span>$</span>
                          <Input
                            type="number"
                            value={rate}
                            onChange={(e: any) =>
                              setCustomRates((prev) => ({
                                ...prev,
                                [role]: parseInt(e.target.value) || 0,
                              }))
                            }
                            className="w-20"
                          />
                          <span>/hr</span>
                        </div>
                      </div>
                    ))}

                    <Button className="w-full">
                      <Save className="mr-2 h-4 w-4" />
                      Save Rates
                    </Button>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Rate Templates</h4>
                    <div className="space-y-3">
                      {[
                        { name: 'Standard Rates', description: 'Default industry rates' },
                        { name: 'Premium Rates', description: 'Higher-tier billing rates' },
                        { name: 'Government Rates', description: 'GSA schedule rates' },
                      ].map((template: any) => (
                        <Card key={template.name}>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-sm">{template.name}</CardTitle>
                            <CardDescription className="text-xs">
                              {template.description}
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <Button size="sm" variant="outline" className="w-full">
                              Apply Template
                            </Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}