'use client';

import { useState } from 'react';
import { Shell } from '~/components/layout/shell';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { Plus, Copy, Trash2, Edit2, Settings, MoreHorizontal, FileJson, Check } from 'lucide-react';
import { api } from '~/trpc/react';
import { Badge } from '~/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import { toast } from 'sonner';
import Link from 'next/link.js';
import { useRouter } from 'next/navigation';

interface TemplatesClientPageProps {
  initialTemplates?: any[];
}

export default function TemplatesClientPage({ initialTemplates }: TemplatesClientPageProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('templates');
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [newTemplateDescription, setNewTemplateDescription] = useState('');

  const { data: templates, isLoading, refetch } = api.projectTemplates.getAll.useQuery(undefined, {
    initialData: initialTemplates,
  });
  const createTemplate = api.projectTemplates.create.useMutation();
  const deleteTemplate = api.projectTemplates.delete.useMutation();
  const duplicateTemplate = api.projectTemplates.duplicate.useMutation();
  const importWorkflowTemplate = api.projectTemplates.importWorkflowTemplate.useMutation();

  const handleCreateTemplate = async () => {
    if (!newTemplateName.trim()) {
      toast.error('Template name is required');
      return;
    }

    try {
      await createTemplate.mutateAsync({
        name: newTemplateName,
        description: newTemplateDescription,
      });
      
      toast.success('Template created successfully');
      setIsCreateOpen(false);
      setNewTemplateName('');
      setNewTemplateDescription('');
      refetch();
    } catch (error) {
      toast.error('Failed to create template');
    }
  };

  const handleDeleteTemplate = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      await deleteTemplate.mutateAsync({ id });
      toast.success('Template deleted successfully');
      refetch();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete template');
    }
  };

  const handleDuplicateTemplate = async (id: string, name: string) => {
    const newName = prompt('Enter name for the duplicated template:', `${name} (Copy)`);
    if (!newName) return;

    try {
      await duplicateTemplate.mutateAsync({ id, name: newName });
      toast.success('Template duplicated successfully');
      refetch();
    } catch (error) {
      toast.error('Failed to duplicate template');
    }
  };

  const handleImportWorkflowTemplate = async () => {
    if (!confirm('This will import the complete utility coordination workflow template with 29 columns and sample tasks. Continue?')) {
      return;
    }

    try {
      await importWorkflowTemplate.mutateAsync();
      toast.success('Workflow template imported successfully');
      refetch();
    } catch (error) {
      toast.error('Failed to import workflow template');
    }
  };

  return (
    <Shell>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Template Center</h1>
          <p className="text-muted-foreground mt-2">
            Create and manage project templates for your organization
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="mt-6">
            <div className="flex justify-between items-center mb-6">
              <div className="flex gap-2">
                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Template
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create New Template</DialogTitle>
                      <DialogDescription>
                        Create a new project template with customizable columns and workflows
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 mt-4">
                      <div>
                        <Label htmlFor="name">Template Name</Label>
                        <Input
                          id="name"
                          value={newTemplateName}
                          onChange={(e: any) => setNewTemplateName(e.target.value)}
                          placeholder="Enter template name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={newTemplateDescription}
                          onChange={(e: any) => setNewTemplateDescription(e.target.value)}
                          placeholder="Enter template description"
                          rows={3}
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsCreateOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreateTemplate} disabled={createTemplate.isPending}>
                          {createTemplate.isPending ? 'Creating...' : 'Create Template'}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button
                  variant="outline"
                  onClick={handleImportWorkflowTemplate}
                  disabled={importWorkflowTemplate.isPending}
                >
                  <FileJson className="h-4 w-4 mr-2" />
                  Import Workflow Template
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[1, 2, 3].map((i: any) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-6 bg-muted rounded w-3/4 mb-2" />
                      <div className="h-4 bg-muted rounded w-full" />
                    </CardHeader>
                    <CardContent>
                      <div className="h-4 bg-muted rounded w-1/2" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : templates && templates.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templates.map((template: any) => (
                  <Card key={template.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center gap-2">
                            {template.name}
                            {template.is_default && (
                              <Badge variant="secondary" className="text-xs">Default</Badge>
                            )}
                          </CardTitle>
                          <CardDescription className="mt-1">
                            {template.description || 'No description'}
                          </CardDescription>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/templates/${template.id}`}>
                                <Edit2 className="h-4 w-4 mr-2" />
                                Edit Template
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDuplicateTemplate(template.id, template.name)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDeleteTemplate(template.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-4">
                          <span>{template._count.template_columns} columns</span>
                          <span>{template._count.template_tasks} tasks</span>
                        </div>
                        <span>{template._count.projects} projects</span>
                      </div>
                      <div className="mt-4">
                        <Button
                          className="w-full"
                          variant="outline"
                          onClick={() => router.push(`/admin/templates/${template.id}`)}
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Configure Template
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="py-12">
                  <div className="text-center space-y-4">
                    <h3 className="text-lg font-semibold">No templates yet</h3>
                    <p className="text-muted-foreground max-w-md mx-auto">
                      Create your first template to standardize your project workflows
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button onClick={() => setIsCreateOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Template
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleImportWorkflowTemplate}
                      >
                        <FileJson className="h-4 w-4 mr-2" />
                        Import Workflow Template
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Template Settings</CardTitle>
                <CardDescription>
                  Configure global template settings and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Default Template</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select which template to use by default when creating new projects
                    </p>
                    <select className="w-full md:w-1/2 px-3 py-2 border rounded-md">
                      <option value="">No default template</option>
                      {templates?.map((template: any) => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Template Permissions</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Control who can create and modify templates
                    </p>
                    <div className="space-y-2">
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" defaultChecked />
                        <span className="text-sm">Only administrators can create templates</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" defaultChecked />
                        <span className="text-sm">Only administrators can modify templates</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Allow project managers to create templates</span>
                      </label>
                    </div>
                  </div>

                  <div className="pt-4">
                    <Button>Save Settings</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Shell>
  );
}