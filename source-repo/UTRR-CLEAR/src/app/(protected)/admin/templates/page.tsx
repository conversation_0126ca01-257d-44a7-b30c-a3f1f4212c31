import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import TemplatesClientPage from './client-page';

export default async function TemplatesPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch template management data for faster loading
  void api.projectTemplates.getAll.prefetch();

  // Fetch initial templates data server-side
  const initialTemplates = await api.projectTemplates.getAll();

  return (
    <HydrateClient>
      <TemplatesClientPage initialTemplates={initialTemplates} />
    </HydrateClient>
  );
}