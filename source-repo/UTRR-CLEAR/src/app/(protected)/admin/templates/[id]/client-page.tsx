'use client';

import { useState } from 'react';
import { Shell } from '~/components/layout/shell';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { 
  Plus, 
  GripVertical, 
  Trash2, 
  Edit2, 
  Settings, 
  Save,
  ArrowLeft,
  MoreVertical,
  ChevronRight,
  Type,
  Hash,
  Calendar,
  Users,
  CheckSquare,
  Link as LinkIcon,
  MapPin,
  Paperclip,
  Calculator,
  BarChart,
  Star,
  DollarSign,
  Mail,
  Phone,
  Globe,
  Clock,
  GitBranch,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Palette,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { Badge } from '~/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import { toast } from 'sonner';
import Link from 'next/link.js';
import { useRouter } from 'next/navigation';
import { cn } from '~/lib/utils';
import { Switch } from '~/components/ui/switch';
import { Separator } from '~/components/ui/separator';
import { COLUMN_TYPES, COLUMN_TYPE_LABELS } from '~/lib/constants/column-types';
import {
  DndContext,
  type DragEndEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const columnTypeIcons: Record<string, any> = {
  [COLUMN_TYPES.TEXT]: Type,
  [COLUMN_TYPES.NUMBER]: Hash,
  [COLUMN_TYPES.DATE]: Calendar,
  [COLUMN_TYPES.DATETIME]: Clock,
  [COLUMN_TYPES.STATUS]: BarChart,
  [COLUMN_TYPES.PEOPLE]: Users,
  [COLUMN_TYPES.CHECKBOX]: CheckSquare,
  [COLUMN_TYPES.FORMULA]: Calculator,
  [COLUMN_TYPES.DROPDOWN]: ChevronRight,
  [COLUMN_TYPES.MULTISELECT]: CheckSquare,
  [COLUMN_TYPES.LINK]: LinkIcon,
  [COLUMN_TYPES.EMAIL]: Mail,
  [COLUMN_TYPES.PHONE]: Phone,
  [COLUMN_TYPES.LOCATION]: MapPin,
  [COLUMN_TYPES.FILE]: Paperclip,
  [COLUMN_TYPES.TIMELINE]: GitBranch,
  [COLUMN_TYPES.DEPENDENCY]: GitBranch,
  [COLUMN_TYPES.PROGRESS]: BarChart,
  [COLUMN_TYPES.RATING]: Star,
  [COLUMN_TYPES.CURRENCY]: DollarSign,
  [COLUMN_TYPES.BUDGET]: DollarSign,
  [COLUMN_TYPES.URL]: Globe,
  [COLUMN_TYPES.TIME_TRACKING]: Clock,
  [COLUMN_TYPES.COLOR]: Palette,
  [COLUMN_TYPES.LONG_TEXT]: Type,
  [COLUMN_TYPES.TAGS]: Hash,
  [COLUMN_TYPES.LAST_UPDATED]: Clock,
  [COLUMN_TYPES.CREATION_LOG]: Clock,
};

// Use imported COLUMN_TYPE_LABELS instead of defining locally

function SortableColumnItem({ column, onEdit, onDelete }: any) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: column.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const Icon = columnTypeIcons[column.column_type] || Type;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "flex items-center gap-3 p-4 bg-card rounded-lg border",
        isDragging && "opacity-50"
      )}
    >
      <div {...attributes} {...listeners} className="cursor-move">
        <GripVertical className="h-5 w-5 text-muted-foreground" />
      </div>
      
      <Icon className="h-5 w-5 text-muted-foreground" />
      
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <span className="font-medium">{column.display_name}</span>
          {column.is_system && (
            <Badge variant="secondary" className="text-xs">
              <Lock className="h-3 w-3 mr-1" />
              System
            </Badge>
          )}
          {column.is_required && (
            <Badge variant="outline" className="text-xs">Required</Badge>
          )}
          {!column.is_visible && (
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
        <div className="text-sm text-muted-foreground mt-1">
          {COLUMN_TYPE_LABELS[column.column_type]} • {column.name}
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        Width: {column.width || 150}px
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => onEdit(column)}>
            <Edit2 className="h-4 w-4 mr-2" />
            Edit Column
          </DropdownMenuItem>
          {!column.is_system && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => onDelete(column.id)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Column
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

export default function TemplateBuilder({ templateId }: { templateId: string }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('columns');
  const [isColumnDialogOpen, setIsColumnDialogOpen] = useState(false);
  const [editingColumn, setEditingColumn] = useState<any>(null);
  
  // Form states
  const [columnName, setColumnName] = useState('');
  const [columnDisplayName, setColumnDisplayName] = useState('');
  const [columnType, setColumnType] = useState<string>(COLUMN_TYPES.TEXT);
  const [columnWidth, setColumnWidth] = useState(150);
  const [isRequired, setIsRequired] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const { data: template, isLoading, refetch } = api.projectTemplates.getById.useQuery(
    { id: templateId },
    { enabled: !!templateId }
  );

  const updateTemplate = api.projectTemplates.update.useMutation();
  const createColumn = api.projectTemplates.columns.create.useMutation();
  const updateColumn = api.projectTemplates.columns.update.useMutation();
  const deleteColumn = api.projectTemplates.columns.delete.useMutation();
  const reorderColumns = api.projectTemplates.columns.reorder.useMutation();

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = template?.template_columns.findIndex((col: any) => col.id === active.id) ?? -1;
      const newIndex = template?.template_columns.findIndex((col: any) => col.id === over?.id) ?? -1;

      if (oldIndex !== -1 && newIndex !== -1) {
        const newOrder = arrayMove(template!.template_columns, oldIndex, newIndex);
        const columnIds = newOrder.map((col: any) => (col as any).id);
        
        try {
          await reorderColumns.mutateAsync({
            templateId,
            columnIds,
          });
          refetch();
          toast.success('Column order updated');
        } catch (error) {
          toast.error('Failed to reorder columns');
        }
      }
    }
  };

  const handleCreateColumn = async () => {
    if (!columnDisplayName.trim()) {
      toast.error('Column name is required');
      return;
    }

    const name = columnName.trim() || columnDisplayName.toLowerCase().replace(/\s+/g, '_');

    try {
      if (editingColumn) {
        await updateColumn.mutateAsync({
          column: {
            id: editingColumn.id,
            name,
            display_name: columnDisplayName,
            column_type: columnType,
            width: columnWidth,
            is_required: isRequired,
            is_visible: isVisible,
          },
        });
        toast.success('Column updated successfully');
      } else {
        await createColumn.mutateAsync({
          templateId,
          column: {
            name,
            display_name: columnDisplayName,
            column_type: columnType,
            width: columnWidth,
            is_required: isRequired,
            is_visible: isVisible,
          },
        });
        toast.success('Column created successfully');
      }
      
      setIsColumnDialogOpen(false);
      resetColumnForm();
      refetch();
    } catch (error) {
      toast.error(editingColumn ? 'Failed to update column' : 'Failed to create column');
    }
  };

  const handleDeleteColumn = async (id: string) => {
    if (!confirm('Are you sure you want to delete this column?')) {
      return;
    }

    try {
      await deleteColumn.mutateAsync({ id });
      toast.success('Column deleted successfully');
      refetch();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete column');
    }
  };

  const handleEditColumn = (column: any) => {
    setEditingColumn(column);
    setColumnName(column.name);
    setColumnDisplayName(column.display_name);
    setColumnType(column.column_type);
    setColumnWidth(column.width || 150);
    setIsRequired(column.is_required);
    setIsVisible(column.is_visible);
    setIsColumnDialogOpen(true);
  };

  const resetColumnForm = () => {
    setEditingColumn(null);
    setColumnName('');
    setColumnDisplayName('');
    setColumnType(COLUMN_TYPES.TEXT);
    setColumnWidth(150);
    setIsRequired(false);
    setIsVisible(true);
  };

  if (isLoading) {
    return (
      <Shell>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/4" />
            <div className="h-64 bg-muted rounded" />
          </div>
        </div>
      </Shell>
    );
  }

  if (!template) {
    return (
      <Shell>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <Card>
            <CardContent className="py-12">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Template not found</h3>
                <Button asChild>
                  <Link href="/admin/templates">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Templates
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </Shell>
    );
  }

  return (
    <Shell>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="icon" asChild>
              <Link href="/admin/templates">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div className="flex-1">
              <h1 className="text-3xl font-bold">{template.name}</h1>
              {template.description && (
                <p className="text-muted-foreground mt-1">{template.description}</p>
              )}
            </div>
            <Button>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="columns">Columns</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="workflow">Workflow</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="columns" className="mt-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Template Columns</CardTitle>
                    <CardDescription>
                      Define the columns that will be available in projects using this template
                    </CardDescription>
                  </div>
                  <Dialog open={isColumnDialogOpen} onOpenChange={(open) => {
                    setIsColumnDialogOpen(open);
                    if (!open) resetColumnForm();
                  }}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Column
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>
                          {editingColumn ? 'Edit Column' : 'Add New Column'}
                        </DialogTitle>
                        <DialogDescription>
                          Configure the column properties and behavior
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="display_name">Display Name</Label>
                            <Input
                              id="display_name"
                              value={columnDisplayName}
                              onChange={(e: any) => setColumnDisplayName(e.target.value)}
                              placeholder="e.g., Due Date"
                            />
                          </div>
                          <div>
                            <Label htmlFor="name">Internal Name</Label>
                            <Input
                              id="name"
                              value={columnName}
                              onChange={(e: any) => setColumnName(e.target.value)}
                              placeholder="e.g., due_date"
                              disabled={editingColumn?.is_system}
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Leave empty to auto-generate
                            </p>
                          </div>
                        </div>

                        <div>
                          <Label>Column Type</Label>
                          <div className="grid grid-cols-3 gap-2 mt-2">
                            {Object.entries(COLUMN_TYPE_LABELS).map(([type, label]) => {
                              const Icon = columnTypeIcons[type];
                              return (
                                <button
                                  key={type}
                                  type="button"
                                  onClick={() => setColumnType(type)}
                                  className={cn(
                                    "flex items-center gap-2 p-3 rounded-lg border text-sm font-medium transition-colors",
                                    columnType === type
                                      ? "border-primary bg-primary/5 text-primary"
                                      : "border-border hover:bg-muted"
                                  )}
                                  disabled={editingColumn?.is_system}
                                >
                                  <Icon className="h-4 w-4" />
                                  {label}
                                </button>
                              );
                            })}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="width">Column Width</Label>
                            <Input
                              id="width"
                              type="number"
                              value={columnWidth}
                              onChange={(e: any) => setColumnWidth(Number(e.target.value))}
                              min={50}
                              max={500}
                            />
                          </div>
                          <div className="space-y-3 pt-6">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="required">Required Field</Label>
                              <Switch
                                id="required"
                                checked={isRequired}
                                onCheckedChange={setIsRequired}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="visible">Visible by Default</Label>
                              <Switch
                                id="visible"
                                checked={isVisible}
                                onCheckedChange={setIsVisible}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Column type specific configuration would go here */}

                        <div className="flex justify-end gap-2 pt-4">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setIsColumnDialogOpen(false);
                              resetColumnForm();
                            }}
                          >
                            Cancel
                          </Button>
                          <Button onClick={handleCreateColumn}>
                            {editingColumn ? 'Update Column' : 'Create Column'}
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={template.template_columns.map((col: any) => col.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-2">
                      {template.template_columns.map((column: any) => (
                        <SortableColumnItem
                          key={column.id}
                          column={column}
                          onEdit={handleEditColumn}
                          onDelete={handleDeleteColumn}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Template Tasks</CardTitle>
                <CardDescription>
                  Define default tasks that will be created for new projects
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  Task management coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="workflow" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Workflow Phases</CardTitle>
                <CardDescription>
                  Configure the workflow phases for this template
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  Workflow configuration coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Template Settings</CardTitle>
                <CardDescription>
                  Configure template properties and behavior
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <Label htmlFor="template-name">Template Name</Label>
                    <Input
                      id="template-name"
                      value={template.name}
                      className="max-w-md"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="template-description">Description</Label>
                    <Textarea
                      id="template-description"
                      value={template.description || ''}
                      rows={3}
                      className="max-w-md"
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between max-w-md">
                      <div>
                        <Label htmlFor="active">Active Template</Label>
                        <p className="text-sm text-muted-foreground">
                          Inactive templates cannot be used to create new projects
                        </p>
                      </div>
                      <Switch
                        id="active"
                        checked={template.is_active}
                      />
                    </div>

                    <div className="flex items-center justify-between max-w-md">
                      <div>
                        <Label htmlFor="default">Default Template</Label>
                        <p className="text-sm text-muted-foreground">
                          This template will be selected by default when creating new projects
                        </p>
                      </div>
                      <Switch
                        id="default"
                        checked={template.is_default}
                      />
                    </div>
                  </div>

                  <div className="pt-4">
                    <Button>Save Settings</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Shell>
  );
}