import { api, HydrateClient } from '~/trpc/server';
import TemplateBuilder from './client-page';

type Props = {
  params: Promise<{ id: string }>;
};

export default async function TemplateBuilderPage({ params }: Props) {
  const { id } = await params;

  // Pre-fetch template data
  if (id && id !== 'new') {
    try {
      void api.projectTemplates.getById.prefetch({ id });
    } catch {
      // Template might not exist, let client handle
    }
  }
  
  void api.projectTemplates.getAll.prefetch();
  void api.organizations.getCurrent.prefetch();

  return (
    <HydrateClient>
      <TemplateBuilder templateId={id} />
    </HydrateClient>
  );
}