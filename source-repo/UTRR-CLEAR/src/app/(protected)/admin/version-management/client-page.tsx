'use client';

import { useAuth } from '~/hooks/use-auth';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Archive,
  ArrowUpCircle,
  CheckCircle,
  Clock,
  Download,
  GitBranch,
  GitCommit,
  GitMerge,
  History,
  Plus,
  RefreshCw,
  RotateCcw,
  Tag,
  Upload,
  Zap,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { safeLog } from '~/lib/error-handler';

interface Version {
  id: string;
  tag: string;
  name: string;
  description: string;
  branch: string;
  commit_hash: string;
  author: string;
  created_at: string;
  status: string;
  changelog: string[];
}

interface Deployment {
  id: string;
  version: string;
  environment: string;
  status: string;
  deployed_at: string;
  deployed_by: string;
  duration: string;
  rollback_available: boolean;
}

interface GitStatus {
  current_branch: string;
  latest_commit: string;
  commits_ahead: number;
  commits_behind: number;
  uncommitted_changes: number;
  last_sync: string;
}

interface VersionManagementClientPageProps {
  initialVersions?: Version[];
  initialDeployments?: Deployment[];
  initialGitStatus?: GitStatus;
}

export default function VersionManagementClientPage({ 
  initialVersions, 
  initialDeployments,
  initialGitStatus 
}: VersionManagementClientPageProps) {
  const { user } = useAuth();
  const [selectedBranch, setSelectedBranch] = useState('main');
  const [deploymentStatus, setDeploymentStatus] = useState<string>('idle');

  // Version management data fetching - implement when admin router endpoints are available
  const versions = undefined; // api.admin.getVersions.useQuery when implemented
  const deployments = undefined; // api.admin.getDeployments.useQuery when implemented
  const gitStatus = undefined; // api.admin.getGitStatus.useQuery when implemented

  const mockVersions = versions || initialVersions || [
    {
      id: 'v2.1.3',
      tag: 'v2.1.3',
      name: 'Admin Dashboard Enhancement',
      description: 'Added comprehensive admin dashboard with analytics and user management',
      branch: 'main',
      commit_hash: 'a7b3c9d2',
      author: 'John Smith',
      created_at: '2024-01-15T10:30:00Z',
      status: 'released',
      changelog: [
        'Added analytics dashboard with KPI tracking',
        'Enhanced user management interface',
        'Improved contract administration tools',
        'Fixed database performance issues',
      ],
    },
    {
      id: 'v2.1.2',
      tag: 'v2.1.2',
      name: 'Mapping System Update',
      description: 'Enhanced 3D mapping capabilities and conflict detection',
      branch: 'main',
      commit_hash: 'f5e8a1b4',
      author: 'Sarah Johnson',
      created_at: '2024-01-10T14:22:00Z',
      status: 'released',
      changelog: [
        'Improved 3D visualization performance',
        'Added advanced conflict detection algorithms',
        'Enhanced spatial data processing',
        'Updated OpenLayers integration',
      ],
    },
    {
      id: 'v2.1.1',
      tag: 'v2.1.1',
      name: 'Project Management Improvements',
      description: 'Enhanced project workflow and stakeholder management',
      branch: 'main',
      commit_hash: 'c3d7e2f9',
      author: 'Mike Davis',
      created_at: '2024-01-05T09:15:00Z',
      status: 'released',
      changelog: [
        'Added project timeline visualization',
        'Enhanced stakeholder communication tools',
        'Improved document management system',
        'Added activity tracking and logging',
      ],
    },
    {
      id: 'v2.2.0-beta',
      tag: 'v2.2.0-beta',
      name: 'AI Integration Beta',
      description: 'Beta release with AI-powered recommendations and automation',
      branch: 'development',
      commit_hash: 'e9f2a5b8',
      author: 'Lisa Wilson',
      created_at: '2024-01-14T16:45:00Z',
      status: 'beta',
      changelog: [
        'Added AI-powered project recommendations',
        'Implemented automated conflict detection',
        'Enhanced data import/export capabilities',
        'Added machine learning analytics',
      ],
    },
  ];

  const mockDeployments = deployments || initialDeployments || [
    {
      id: '1',
      version: 'v2.1.3',
      environment: 'production',
      status: 'success',
      deployed_at: '2024-01-15T12:00:00Z',
      deployed_by: 'John Smith',
      duration: '00:03:45',
      rollback_available: true,
    },
    {
      id: '2',
      version: 'v2.1.3',
      environment: 'staging',
      status: 'success',
      deployed_at: '2024-01-15T10:45:00Z',
      deployed_by: 'System',
      duration: '00:02:30',
      rollback_available: true,
    },
    {
      id: '3',
      version: 'v2.2.0-beta',
      environment: 'development',
      status: 'success',
      deployed_at: '2024-01-14T17:00:00Z',
      deployed_by: 'Lisa Wilson',
      duration: '00:04:12',
      rollback_available: true,
    },
    {
      id: '4',
      version: 'v2.1.2',
      environment: 'production',
      status: 'rolled_back',
      deployed_at: '2024-01-10T15:30:00Z',
      deployed_by: 'Sarah Johnson',
      duration: '00:05:20',
      rollback_available: false,
    },
  ];

  const mockGitStatus = gitStatus || initialGitStatus || {
    current_branch: 'main',
    latest_commit: 'a7b3c9d2',
    commits_ahead: 0,
    commits_behind: 0,
    uncommitted_changes: 3,
    last_sync: '2024-01-15T10:30:00Z',
  };

  const environments = [
    {
      name: 'Development',
      status: 'healthy',
      version: 'v2.2.0-beta',
      url: 'https://dev.utilsync.com',
    },
    { name: 'Staging', status: 'healthy', version: 'v2.1.3', url: 'https://staging.utilsync.com' },
    { name: 'Production', status: 'healthy', version: 'v2.1.3', url: 'https://app.utilsync.com' },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <Clock className="h-4 w-4 text-red-600" />;
      case 'in_progress':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'rolled_back':
        return <RotateCcw className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
      case 'released':
        return 'default';
      case 'failed':
        return 'destructive';
      case 'in_progress':
        return 'secondary';
      case 'beta':
        return 'secondary';
      case 'rolled_back':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getEnvironmentStatus = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'default';
      case 'warning':
        return 'secondary';
      case 'error':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const simulateDeployment = async (version: string, environment: string) => {
    setDeploymentStatus('deploying');
    // Simulate deployment process
    await new Promise((resolve) => setTimeout(resolve, 3000));
    setDeploymentStatus('success');
    safeLog.info(`Deployed ${version} to ${environment}`);
  };

  const kpiCards = [
    {
      title: 'Current Version',
      value: 'v2.1.3',
      icon: Tag,
      description: 'Production release',
    },
    {
      title: 'Deployments Today',
      value: '3',
      icon: ArrowUpCircle,
      description: 'Across all environments',
    },
    {
      title: 'Success Rate',
      value: '99.2%',
      icon: CheckCircle,
      description: 'Last 30 days',
    },
    {
      title: 'Rollbacks',
      value: '1',
      icon: RotateCcw,
      description: 'This month',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Version Management</h1>
            <p className="text-muted-foreground">
              Manage application versions, deployments, and releases
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <GitBranch className="mr-2 h-4 w-4" />
              Sync Repository
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Release
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="releases" className="space-y-4">
          <TabsList>
            <TabsTrigger value="releases">Releases</TabsTrigger>
            <TabsTrigger value="deployments">Deployments</TabsTrigger>
            <TabsTrigger value="environments">Environments</TabsTrigger>
            <TabsTrigger value="git">Git Status</TabsTrigger>
          </TabsList>

          <TabsContent value="releases" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Release History</CardTitle>
                <CardDescription>View and manage application releases and versions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockVersions.map((version: Version) => (
                    <Card key={version.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg flex items-center space-x-2">
                              <Tag className="h-4 w-4" />
                              <span>{version.tag}</span>
                            </CardTitle>
                            <CardDescription className="mt-1">
                              {version.name} • by {version.author} •{' '}
                              {new Date(version.created_at).toLocaleDateString()}
                            </CardDescription>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={getStatusColor(version.status)}>{version.status}</Badge>
                            <Badge variant="outline">{version.branch}</Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-4">{version.description}</p>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Changelog:</Label>
                          <ul className="text-sm space-y-1">
                            {version.changelog.map((change: string, index: number) => (
                              <li key={index} className="flex items-start space-x-2">
                                <span className="text-muted-foreground">•</span>
                                <span>{change}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="flex items-center justify-between mt-4 pt-4 border-t">
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span className="flex items-center space-x-1">
                              <GitCommit className="h-3 w-3" />
                              <span>{version.commit_hash}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <GitBranch className="h-3 w-3" />
                              <span>{version.branch}</span>
                            </span>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                            <Button size="sm" variant="outline">
                              <ArrowUpCircle className="h-3 w-3 mr-1" />
                              Deploy
                            </Button>
                            <Button size="sm" variant="outline">
                              <History className="h-3 w-3 mr-1" />
                              Compare
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="deployments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Deployment History</CardTitle>
                <CardDescription>Track deployments across all environments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockDeployments.map((deployment: Deployment) => (
                    <Card key={deployment.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-base flex items-center space-x-2">
                              {getStatusIcon(deployment.status)}
                              <span>
                                {deployment.version} → {deployment.environment}
                              </span>
                            </CardTitle>
                            <CardDescription>
                              Deployed by {deployment.deployed_by} • Duration: {deployment.duration}{' '}
                              •{new Date(deployment.deployed_at).toLocaleString()}
                            </CardDescription>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={getStatusColor(deployment.status)}>
                              {deployment.status.replace('_', ' ')}
                            </Badge>
                            {deployment.rollback_available && (
                              <Button size="sm" variant="outline">
                                <RotateCcw className="h-3 w-3 mr-1" />
                                Rollback
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="environments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Environment Status</CardTitle>
                <CardDescription>Monitor and manage deployment environments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
                  {environments.map((env: any) => (
                    <Card key={env.name}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{env.name}</CardTitle>
                          <Badge variant={getEnvironmentStatus(env.status)}>{env.status}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Current Version
                            </Label>
                            <p className="text-sm font-medium">{env.version}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">URL</Label>
                            <p className="text-sm text-blue-600">{env.url}</p>
                          </div>
                          <div className="space-y-2">
                            <Button
                              size="sm"
                              className="w-full"
                              onClick={() => simulateDeployment('v2.1.3', env.name.toLowerCase())}
                              disabled={deploymentStatus === 'deploying'}
                            >
                              {deploymentStatus === 'deploying' ? (
                                <>
                                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                  Deploying...
                                </>
                              ) : (
                                <>
                                  <ArrowUpCircle className="h-3 w-3 mr-1" />
                                  Deploy Latest
                                </>
                              )}
                            </Button>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline" className="flex-1">
                                <Zap className="h-3 w-3 mr-1" />
                                Health
                              </Button>
                              <Button size="sm" variant="outline" className="flex-1">
                                <History className="h-3 w-3 mr-1" />
                                Logs
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="git" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Git Repository Status</CardTitle>
                <CardDescription>
                  Monitor repository synchronization and branch status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Repository Status</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Current Branch</span>
                          <Badge variant="outline">{mockGitStatus.current_branch}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Latest Commit</span>
                          <Badge variant="outline">{mockGitStatus.latest_commit}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Commits Ahead</span>
                          <Badge variant="secondary">{mockGitStatus.commits_ahead}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Commits Behind</span>
                          <Badge variant="secondary">{mockGitStatus.commits_behind}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Uncommitted Changes</span>
                          <Badge
                            variant={
                              mockGitStatus.uncommitted_changes > 0 ? 'destructive' : 'default'
                            }
                          >
                            {mockGitStatus.uncommitted_changes}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Last Sync</span>
                          <span className="text-sm text-muted-foreground">
                            {new Date(mockGitStatus.last_sync).toLocaleString()}
                          </span>
                        </div>
                      </div>

                      <Button className="w-full">
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Sync Repository
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Branch Management</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="branch-select">Switch Branch</Label>
                        <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="main">main</SelectItem>
                            <SelectItem value="development">development</SelectItem>
                            <SelectItem value="feature/ai-integration">
                              feature/ai-integration
                            </SelectItem>
                            <SelectItem value="hotfix/security-patch">
                              hotfix/security-patch
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Button variant="outline" className="w-full">
                          <GitBranch className="mr-2 h-4 w-4" />
                          Create Branch
                        </Button>
                        <Button variant="outline" className="w-full">
                          <GitMerge className="mr-2 h-4 w-4" />
                          Merge Branch
                        </Button>
                        <Button variant="outline" className="w-full">
                          <Archive className="mr-2 h-4 w-4" />
                          Archive Branch
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}