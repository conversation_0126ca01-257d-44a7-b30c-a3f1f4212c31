import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import { UserManagement } from '~/components/admin/user-management';

export default async function UserManagementPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch user management data for faster loading
  void api.users.getAll.prefetch({ limit: 100 });
  void api.rbac.getRoles.prefetch();
  void api.organizations.getCurrent.prefetch();

  return (
    <HydrateClient>
      <UserManagement />
    </HydrateClient>
  );
}