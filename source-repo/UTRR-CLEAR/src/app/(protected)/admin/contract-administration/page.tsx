import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import ContractAdministrationClientPage from './client-page';

export default async function ContractAdministrationPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch contract administration data for faster loading
  void api.projects.getAll.prefetch({ limit: 100 });
  void api.organizations.getCurrent.prefetch();
  void api.users.getAll.prefetch({ limit: 100 });

  // Fetch initial data server-side (API endpoints not implemented yet)
  const initialContracts = undefined; // Will be replaced when API is implemented
  const initialStats = undefined; // Will be replaced when API is implemented

  return (
    <HydrateClient>
      <ContractAdministrationClientPage initialContracts={initialContracts} initialStats={initialStats} />
    </HydrateClient>
  );
}
