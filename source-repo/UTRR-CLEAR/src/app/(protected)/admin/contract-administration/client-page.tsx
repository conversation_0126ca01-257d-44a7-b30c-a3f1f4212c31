'use client';

import { useAuth } from '~/hooks/use-auth';
import { redirect } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Download,
  FileText,
  Inbox,
  Mail,
  Plus,
  Search,
  Settings,
  Upload,
  Users,
} from 'lucide-react';
import { api } from '~/trpc/react';

// Type definition matching the API response
interface Contract {
  id: string;
  title: string;
  type: string;
  status: string;
  client: string;
  value: number;
  start_date: string;
  end_date: string;
  project_count: number;
  last_modified: string;
  billed_to_date?: number;
  profit_to_date?: number;
  billed_percentage?: number;
}

interface ContractAdministrationClientPageProps {
  initialContracts?: {
    contracts: Contract[];
  };
  initialStats?: {
    totalContracts: number;
    activeContracts: number;
    totalValue: number;
    expiringThisMonth: number;
    pendingApproval: number;
    avgContractValue: number;
  };
}

export default function ContractAdministrationClientPage({ 
  initialContracts, 
  initialStats 
}: ContractAdministrationClientPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedContract, setSelectedContract] = useState<string | null>(null);

  // Since these API endpoints don't exist yet, we'll use the mock data
  const contractsLoading = false;
  const contracts = initialContracts;
  const stats = initialStats;

  const contractTypes = [
    { value: 'master_service', label: 'Master Service Agreement' },
    { value: 'project_specific', label: 'Project Specific Contract' },
    { value: 'utility_agreement', label: 'Utility Agreement' },
    { value: 'vendor_contract', label: 'Vendor Contract' },
    { value: 'consulting', label: 'Consulting Agreement' },
  ];

  const contractStatuses = [
    { value: 'draft', label: 'Draft', color: 'gray' },
    { value: 'under_review', label: 'Under Review', color: 'yellow' },
    { value: 'approved', label: 'Approved', color: 'green' },
    { value: 'executed', label: 'Executed', color: 'blue' },
    { value: 'expired', label: 'Expired', color: 'red' },
    { value: 'terminated', label: 'Terminated', color: 'red' },
  ];

  const getStatusColor = (status: string) => {
    const statusConfig = contractStatuses.find((s: any) => s.value === status);
    return statusConfig?.color || 'gray';
  };

  // Helper function to get title from contract
  const getContractTitle = (contract: Contract): string => {
    return contract.title;
  };

  const mockContracts: Contract[] = (contracts?.contracts as Contract[]) || [
    {
      id: '1',
      title: 'Duke Energy Master Service Agreement',
      type: 'master_service',
      status: 'executed',
      client: 'Duke Energy',
      value: 2500000,
      start_date: '2024-01-01',
      end_date: '2026-12-31',
      project_count: 15,
      last_modified: '2024-01-15',
    },
    {
      id: '2',
      title: 'I-65 Widening Utility Coordination',
      type: 'project_specific',
      status: 'under_review',
      client: 'INDOT',
      value: 850000,
      start_date: '2024-03-01',
      end_date: '2025-06-30',
      project_count: 1,
      last_modified: '2024-01-20',
    },
    {
      id: '3',
      title: 'AT&T Underground Relocation Agreement',
      type: 'utility_agreement',
      status: 'approved',
      client: 'AT&T',
      value: 450000,
      start_date: '2024-02-15',
      end_date: '2024-08-15',
      project_count: 3,
      last_modified: '2024-01-18',
    },
  ];

  const mockStats = stats || {
    totalContracts: 24,
    activeContracts: 18,
    totalValue: 12750000,
    expiringThisMonth: 3,
    pendingApproval: 5,
    avgContractValue: 531250,
  };

  const kpiCards = [
    {
      title: 'Total Contracts',
      value: mockStats.totalContracts,
      icon: FileText,
      description: 'All time contracts',
      color: 'blue',
    },
    {
      title: 'Active Contracts',
      value: mockStats.activeContracts,
      icon: CheckCircle,
      description: 'Currently active',
      color: 'green',
    },
    {
      title: 'Total Value',
      value: `$${(mockStats.totalValue / 1000000).toFixed(1)}M`,
      icon: DollarSign,
      description: 'Combined contract value',
      color: 'purple',
    },
    {
      title: 'Pending Approval',
      value: mockStats.pendingApproval,
      icon: Clock,
      description: 'Awaiting approval',
      color: 'yellow',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Contract Administration</h1>
            <p className="text-muted-foreground">
              Manage contracts, agreements, and vendor relationships
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import Contracts
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Contract
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="contracts" className="space-y-4">
          <TabsList>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="approvals">Approvals</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="contracts" className="space-y-4">
            {/* Search and Filters */}
            <Card>
              <CardHeader>
                <CardTitle>Contract Management</CardTitle>
                <CardDescription>
                  Search, filter, and manage all contracts and agreements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search contracts..."
                        value={searchTerm}
                        onChange={(e: any) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      {contractStatuses.map((status: any) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Contracts Table */}
                <div className="space-y-4">
                  {contractsLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="text-sm text-muted-foreground mt-2">Loading contracts...</p>
                    </div>
                  ) : (
                    mockContracts
                      .filter(
                        (contract) =>
                          contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          contract.client.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                      .filter(
                        (contract) => statusFilter === 'all' || contract.status === statusFilter
                      )
                      .map((contract: any) => (
                        <Card
                          key={contract.id}
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedContract === contract.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() =>
                            setSelectedContract(
                              contract.id === selectedContract ? null : contract.id
                            )
                          }
                        >
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <div>
                                <CardTitle className="text-lg">{contract.title}</CardTitle>
                                <CardDescription className="flex items-center space-x-4 mt-1">
                                  <span>{contract.client}</span>
                                  <Badge variant="outline">
                                    {contractTypes.find((t: any) => t.value === contract.type)?.label}
                                  </Badge>
                                  <Badge
                                    variant={
                                      getStatusColor(contract.status) === 'green'
                                        ? 'default'
                                        : 'secondary'
                                    }
                                    className={
                                      getStatusColor(contract.status) === 'yellow'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : getStatusColor(contract.status) === 'red'
                                          ? 'bg-red-100 text-red-800'
                                          : getStatusColor(contract.status) === 'blue'
                                            ? 'bg-blue-100 text-blue-800'
                                            : ''
                                    }
                                  >
                                    {
                                      contractStatuses.find((s: any) => s.value === contract.status)
                                        ?.label
                                    }
                                  </Badge>
                                </CardDescription>
                              </div>
                              <div className="text-right">
                                <div className="text-lg font-semibold">
                                  ${(contract.value / 1000000).toFixed(1)}M
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {contract.project_count} project
                                  {contract.project_count !== 1 ? 's' : ''}
                                </div>
                              </div>
                            </div>
                          </CardHeader>

                          {selectedContract === contract.id && (
                            <CardContent className="pt-0">
                              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                <div>
                                  <Label className="text-sm font-medium text-muted-foreground">
                                    Start Date
                                  </Label>
                                  <p className="text-sm">
                                    {new Date(contract.start_date).toLocaleDateString()}
                                  </p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium text-muted-foreground">
                                    End Date
                                  </Label>
                                  <p className="text-sm">
                                    {new Date(contract.end_date).toLocaleDateString()}
                                  </p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium text-muted-foreground">
                                    Last Modified
                                  </Label>
                                  <p className="text-sm">
                                    {new Date(contract.last_modified).toLocaleDateString()}
                                  </p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium text-muted-foreground">
                                    Actions
                                  </Label>
                                  <div className="flex items-center space-x-2">
                                    <Button size="sm" variant="outline">
                                      <FileText className="h-3 w-3 mr-1" />
                                      View
                                    </Button>
                                    <Button size="sm" variant="outline">
                                      <Download className="h-3 w-3 mr-1" />
                                      Download
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          )}
                        </Card>
                      ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Contract Templates</CardTitle>
                <CardDescription>Manage contract templates and standard agreements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {contractTypes.map((template: any) => (
                    <Card key={template.value}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-base">{template.label}</CardTitle>
                            <CardDescription>
                              Standard template for {template.label.toLowerCase()}
                            </CardDescription>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Settings className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="approvals" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Pending Approvals</CardTitle>
                <CardDescription>Contracts awaiting review and approval</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockContracts
                    .filter((contract: any) => contract.status === 'under_review')
                    .map((contract: any) => (
                      <Card key={contract.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-base">{contract.title}</CardTitle>
                              <CardDescription>{contract.client}</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button size="sm" variant="outline">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Review
                              </Button>
                              <Button size="sm">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Approve
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Contract Reports</CardTitle>
                <CardDescription>
                  Generate reports and analytics for contract performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Contract Summary Report</CardTitle>
                      <CardDescription>
                        Overview of all active contracts and their status
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Financial Analysis</CardTitle>
                      <CardDescription>
                        Contract values, payments, and financial metrics
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Expiration Report</CardTitle>
                      <CardDescription>Contracts expiring in the next 6 months</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Performance Metrics</CardTitle>
                      <CardDescription>Contract performance and completion rates</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}