import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import { BusinessMetricsDashboard } from "~/components/admin/business-metrics-dashboard";

export const metadata = {
  title: "Business Metrics | CLEAR Admin",
  description: "Business metrics and performance dashboard",
};

export default async function BusinessMetricsPage() {
  const session = await getServerAuthSession();
  
  // Prefetch business metrics data for faster loading
  void api.admin.getStats.prefetch();
  void api.projects.getAll.prefetch({ limit: 100 });
  
  return (
    <HydrateClient>
      <BusinessMetricsDashboard />
    </HydrateClient>
  );
}