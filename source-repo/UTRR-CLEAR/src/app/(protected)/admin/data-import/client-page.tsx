'use client';

import { useAuth } from '~/hooks/use-auth';
import { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import type { TransformedImportRecord } from '~/types/admin';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Database,
  Download,
  FileText,
  FileSpreadsheet,
  Loader2,
  MapPin,
  RefreshCw,
  Upload,
  Users,
  XCircle,
} from 'lucide-react';
import { api } from '~/trpc/react';

interface ImportRecord {
  id: string;
  type: string;
  filename: string;
  status: 'completed' | 'failed' | 'running';
  records_imported?: number;
  records_failed?: number;
  created_at: string;
  user: { first_name: string; last_name: string };
  error_message?: string;
}

interface DataImportClientPageProps {
  initialHistory?: any;
  initialStats?: any;
}

export default function DataImportClientPage({ initialHistory, initialStats }: DataImportClientPageProps) {
  const { user } = useAuth();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [importType, setImportType] = useState<string>('projects');
  const [importStatus, setImportStatus] = useState<string>('idle');
  const [importProgress, setImportProgress] = useState(0);
  const [importResults, setImportResults] = useState<any>(null);

  // TODO: Implement import history tracking in Supabase
  const importHistory: ImportRecord[] = initialHistory || [];
  
  const { data: systemStats } = api.admin.getStats.useQuery();

  const importTypes = [
    {
      value: 'projects',
      label: 'Projects',
      icon: FileText,
      description: 'Import project data and metadata',
    },
    {
      value: 'utilities',
      label: 'Utilities',
      icon: MapPin,
      description: 'Import utility company and infrastructure data',
    },
    {
      value: 'stakeholders',
      label: 'Stakeholders',
      icon: Users,
      description: 'Import stakeholder and contact information',
    },
    {
      value: 'spatial',
      label: 'Spatial Data',
      icon: Database,
      description: 'Import GIS and spatial datasets',
    },
    {
      value: 'monday',
      label: 'Monday.com',
      icon: RefreshCw,
      description: 'Import data from Monday.com boards',
    },
    {
      value: 'excel',
      label: 'Excel/CSV',
      icon: FileSpreadsheet,
      description: 'Import from Excel or CSV files',
    },
  ];

  // TODO: Replace with real API call to fetch import history
  const importHistoryData: TransformedImportRecord[] = [];

  // TODO: Replace with real API call to fetch import statistics
  const importStatsData = {
    totalImports: 0,
    successfulImports: 0,
    failedImports: 0,
    totalRecordsSuccessful: 0,
    lastImport: null,
  };

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
  }, []);

  const simulateImport = async () => {
    setImportStatus('running');
    setImportProgress(0);

    // Simulate import progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      setImportProgress(i);
    }

    // Simulate results
    const mockResults = {
      success: true,
      records_imported: 87,
      records_failed: 3,
      errors: [
        "Row 25: Missing required field 'client_name'",
        'Row 43: Invalid date format',
        'Row 67: Duplicate project ID',
      ],
    };

    setImportResults(mockResults);
    setImportStatus('completed');
    // TODO: Refresh history when API is implemented
    console.log('Import completed, would refresh history here');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'failed':
        return 'destructive';
      case 'running':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const kpiCards = [
    {
      title: 'Total Imports',
      value: importStatsData.totalImports,
      icon: Database,
      description: 'All time imports',
    },
    {
      title: 'Success Rate',
      value: `${Math.round((importStatsData.successfulImports / importStatsData.totalImports) * 100)}%`,
      icon: CheckCircle,
      description: 'Import success rate',
    },
    {
      title: 'Records Imported',
      value: importStatsData.totalRecordsSuccessful.toLocaleString(),
      icon: FileText,
      description: 'Total records imported',
    },
    {
      title: 'Failed Imports',
      value: importStatsData.failedImports,
      icon: XCircle,
      description: 'Require attention',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Data Import</h1>
            <p className="text-muted-foreground">
              Import data from various sources and manage import history
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Template
            </Button>
            <Button onClick={() => console.log('Refresh not implemented yet')}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="import" className="space-y-4">
          <TabsList>
            <TabsTrigger value="import">New Import</TabsTrigger>
            <TabsTrigger value="history">Import History</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="mapping">Field Mapping</TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Import Data</CardTitle>
                <CardDescription>Select data type and upload files for import</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Import Type Selection */}
                <div>
                  <Label className="text-base font-medium">Import Type</Label>
                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3 mt-3">
                    {importTypes.map((type: any) => {
                      const Icon = type.icon;
                      return (
                        <Card
                          key={type.value}
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            importType === type.value ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() => setImportType(type.value)}
                        >
                          <CardHeader className="pb-3">
                            <div className="flex items-center space-x-3">
                              <Icon className="h-5 w-5 text-blue-600" />
                              <div>
                                <CardTitle className="text-sm">{type.label}</CardTitle>
                                <CardDescription className="text-xs">
                                  {type.description}
                                </CardDescription>
                              </div>
                            </div>
                          </CardHeader>
                        </Card>
                      );
                    })}
                  </div>
                </div>

                {/* File Upload */}
                <div>
                  <Label htmlFor="file-upload" className="text-base font-medium">
                    Select Files
                  </Label>
                  <div className="mt-3">
                    <Input
                      id="file-upload"
                      type="file"
                      multiple
                      accept=".xlsx,.xls,.csv,.json,.xml"
                      onChange={handleFileSelect}
                      className="cursor-pointer"
                    />
                    {selectedFiles.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {selectedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded"
                          >
                            <span className="text-sm">{file.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Import Options */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="skip-duplicates">Skip Duplicate Records</Label>
                    <Select defaultValue="true">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Yes, skip duplicates</SelectItem>
                        <SelectItem value="false">No, import all records</SelectItem>
                        <SelectItem value="update">Update existing records</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="validation-level">Validation Level</Label>
                    <Select defaultValue="strict">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="strict">Strict validation</SelectItem>
                        <SelectItem value="moderate">Moderate validation</SelectItem>
                        <SelectItem value="permissive">Permissive validation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Import Progress */}
                {importStatus !== 'idle' && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        {getStatusIcon(importStatus)}
                        <span>Import Progress</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Progress value={importProgress} className="w-full" />
                        <div className="text-sm text-muted-foreground">
                          {importStatus === 'running'
                            ? `${importProgress}% complete`
                            : importStatus === 'completed'
                              ? 'Import completed successfully'
                              : 'Import failed'}
                        </div>

                        {importResults && (
                          <div className="space-y-3">
                            <div className="flex items-center justify-between text-sm">
                              <span>Records Imported:</span>
                              <Badge variant="default">{importResults.records_imported}</Badge>
                            </div>
                            <div className="flex items-center justify-between text-sm">
                              <span>Records Failed:</span>
                              <Badge
                                variant={
                                  importResults.records_failed > 0 ? 'destructive' : 'secondary'
                                }
                              >
                                {importResults.records_failed}
                              </Badge>
                            </div>

                            {importResults.errors && importResults.errors.length > 0 && (
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Errors:
                                </Label>
                                <div className="mt-2 space-y-1">
                                  {importResults.errors.map((error: string, index: number) => (
                                    <div
                                      key={index}
                                      className="text-xs text-red-600 bg-red-50 p-2 rounded"
                                    >
                                      {error}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Import Button */}
                <div className="flex justify-end">
                  <Button
                    onClick={simulateImport}
                    disabled={selectedFiles.length === 0 || importStatus === 'running'}
                    className="w-full md:w-auto"
                  >
                    {importStatus === 'running' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Start Import
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Import History</CardTitle>
                <CardDescription>View past imports and their results</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {importHistoryData.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No import history available yet.</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Import history will appear here after your first data import.
                      </p>
                    </div>
                  ) : (
                    importHistoryData.map((import_record: any) => (
                    <Card key={import_record.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-base flex items-center space-x-2">
                              {getStatusIcon(import_record.status)}
                              <span>{import_record.filename}</span>
                            </CardTitle>
                            <CardDescription className="flex items-center space-x-4 mt-1">
                              <Badge variant="outline">
                                {importTypes.find((t: any) => t.value === import_record.type)?.label}
                              </Badge>
                              <span>
                                {(import_record as any).users ? `${(import_record as any).users.first_name} ${(import_record as any).users.last_name}` : (import_record as any).uploadedBy}
                              </span>
                              <span>{new Date((import_record as any).created_at || (import_record as any).uploadedAt).toLocaleDateString()}</span>
                            </CardDescription>
                          </div>
                          <Badge variant={getStatusColor(import_record.status)}>
                            {import_record.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Records Imported
                            </Label>
                            <p className="text-lg font-semibold text-green-600">
                              {(import_record as any).records_imported || (import_record as any).recordsSuccessful}
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Records Failed
                            </Label>
                            <p
                              className={`text-lg font-semibold ${((import_record as any).records_failed || (import_record as any).recordsFailed) > 0 ? 'text-red-600' : 'text-gray-600'}`}
                            >
                              {(import_record as any).records_failed || (import_record as any).recordsFailed}
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-muted-foreground">
                              Success Rate
                            </Label>
                            <p className="text-lg font-semibold text-blue-600">
                              {Math.round(
                                (((import_record as any).records_imported || (import_record as any).recordsSuccessful) /
                                  (((import_record as any).records_imported || (import_record as any).recordsSuccessful) + ((import_record as any).records_failed || (import_record as any).recordsFailed))) *
                                  100
                              )}
                              %
                            </p>
                          </div>
                        </div>

                        {(import_record as any).error_message && (
                          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                            <div className="flex items-center space-x-2">
                              <AlertCircle className="h-4 w-4 text-red-600" />
                              <span className="text-sm font-medium text-red-800">Error:</span>
                            </div>
                            <p className="text-sm text-red-700 mt-1">
                              {(import_record as any).error_message}
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Import Templates</CardTitle>
                <CardDescription>Download templates for different data types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {importTypes.map((type: any) => {
                    const Icon = type.icon;
                    return (
                      <Card key={type.value}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Icon className="h-5 w-5 text-blue-600" />
                              <div>
                                <CardTitle className="text-base">{type.label} Template</CardTitle>
                                <CardDescription className="text-sm">
                                  {type.description}
                                </CardDescription>
                              </div>
                            </div>
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        </CardHeader>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="mapping" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Field Mapping</CardTitle>
                <CardDescription>
                  Configure how imported fields map to database columns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <Database className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Field Mapping Configuration
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Advanced field mapping interface will be implemented to allow custom mapping
                      of import fields to database columns.
                    </p>
                    <Button variant="outline">Configure Mappings</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}