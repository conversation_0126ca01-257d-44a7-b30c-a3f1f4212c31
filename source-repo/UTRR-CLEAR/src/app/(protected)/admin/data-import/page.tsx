import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import DataImportClientPage from './client-page';

export default async function DataImportPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Pre-fetch admin data
  void api.organizations.getCurrent.prefetch();
  void api.projects.getAll.prefetch({});

  return (
    <HydrateClient>
      <DataImportClientPage />
    </HydrateClient>
  );
}
