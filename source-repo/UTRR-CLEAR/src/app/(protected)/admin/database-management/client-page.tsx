'use client';

import { useAuth } from '~/hooks/use-auth';
import { redirect } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import {
  AlertTriangle,
  Archive,
  Database,
  Download,
  FileText,
  HardDrive,
  History,
  Monitor,
  Play,
  RefreshCw,
  Save,
  Search,
  Settings,
  Shield,
  Table2,
  Upload,
  Zap,
  ExternalLink,
  Globe,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { DatabaseOverview } from '~/components/database/database-overview';
import { QueryLogger } from '~/components/database/query-logger';
import { toast } from '~/hooks/use-toast';
import { safeLog } from '~/lib/error-handler';

interface TableInfo {
  name: string;
  records: number;
  size: string;
  last_modified: string;
}

interface BackupInfo {
  id: string;
  filename: string;
  size: string;
  type: string;
  status: string;
  created_at: string;
  duration: string;
}

interface DatabaseManagementClientPageProps {
  initialStats?: any;
  initialTables?: any;
  initialBackups?: any;
}

export default function DatabaseManagementClientPage({ 
  initialStats, 
  initialTables, 
  initialBackups 
}: DatabaseManagementClientPageProps) {
  const { user } = useAuth();
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [queryText, setQueryText] = useState('');
  const [queryResults, setQueryResults] = useState<any>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [supabaseStudioStatus, setSupabaseStudioStatus] = useState<'stopped' | 'starting' | 'running'>('stopped');

  const { data: systemStats } = api.admin.getStats.useQuery();
  // TODO: Implement getSystemHealth and getRecentActivity endpoints
  const systemHealth = { status: 'healthy', connections: 0, maxConnections: 100 };
  const recentActivity: any[] = [];
  
  // TODO: Implement database table listing and backup management in Supabase
  const tables: TableInfo[] = initialTables || [];
  const backups: BackupInfo[] = initialBackups || [];
  
  // Supabase Studio is always available at the hosted URL
  const openSupabaseStudio = () => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    if (supabaseUrl) {
      // Extract project ref from URL like https://xeaimnfnaiuuaazovoxx.supabase.co
      const projectRef = supabaseUrl.replace('https://', '').replace('.supabase.co', '');
      window.open(`https://supabase.com/dashboard/project/${projectRef}/editor`, '_blank');
    }
  };

  const executeQueryMutation = {
    mutate: () => {
      console.log('Query execution not implemented yet');
      setQueryResults({
        message: 'Query execution not implemented yet - use Supabase Studio for SQL queries',
        executionTime: '0ms',
      });
      setIsExecuting(false);
    },
    isLoading: false
  };

  const createBackupMutation = {
    mutate: () => {
      console.log('Database backup not implemented yet - use Supabase dashboard for backups');
    },
    isLoading: false
  };

  const runMaintenanceMutation = {
    mutate: () => {
      console.log('Database maintenance not implemented yet - use Supabase dashboard');
    },
    isLoading: false
  };

  const databaseStats = {
    totalTables: 15, // TODO: Get from Supabase
    totalRecords: (systemStats?.users || 0) + (systemStats?.projects || 0) + (systemStats?.utilities || 0),
    databaseSize: '2.4 GB', // TODO: Get from Supabase
    lastBackup: new Date().toISOString(),
    connections: 5, // TODO: Get from Supabase
    performance: 95, // TODO: Calculate from Supabase metrics
    uptime: '99.9%',
    lastMaintenance: new Date().toISOString(),
  };

  const tablesList: TableInfo[] = tables || [];

  // TODO: Replace with real API call to fetch backup history
  const backupHistory: BackupInfo[] = backups || [];

  const sampleQueries = [
    {
      name: 'Active Projects Summary',
      query:
        'SELECT rag_status, COUNT(*) as count FROM projects WHERE rag_status IS NOT NULL GROUP BY rag_status;',
    },
    {
      name: 'Recent Project Activity',
      query:
        'SELECT p.name, pl.action, pl.timestamp FROM projects p JOIN project_logs pl ON p.id = pl.project_id ORDER BY pl.timestamp DESC LIMIT 10;',
    },
    {
      name: 'Conflict Statistics',
      query: 'SELECT priority, status, COUNT(*) as count FROM conflicts GROUP BY priority, status;',
    },
    {
      name: 'User Activity Summary',
      query:
        'SELECT u.first_name, u.last_name, COUNT(pl.id) as activities FROM users u LEFT JOIN project_logs pl ON u.id = pl.user_id GROUP BY u.id, u.first_name, u.last_name ORDER BY activities DESC;',
    },
  ];

  const executeQuery = async () => {
    if (!queryText.trim()) return;

    setIsExecuting(true);
    executeQueryMutation.mutate();
  };

  const startSupabaseStudio = async () => {
    setSupabaseStudioStatus('starting');
    
    try {
      openSupabaseStudio();
      setSupabaseStudioStatus('running');
    } catch (error) {
      setSupabaseStudioStatus('stopped');
      console.error('Error opening Supabase Studio:', error);
      alert('Failed to open Supabase Studio');
    }
  };

  const stopSupabaseStudio = async () => {
    try {
      console.log('Supabase Studio runs in browser, cannot stop programmatically');
      setSupabaseStudioStatus('stopped');
    } catch (error) {
      console.error('Error stopping Supabase Studio:', error);
    }
  };

  const kpiCards = [
    {
      title: 'Total Tables',
      value: databaseStats.totalTables,
      icon: Database,
      description: 'Database tables',
      color: 'blue',
    },
    {
      title: 'Total Records',
      value: databaseStats.totalRecords.toLocaleString(),
      icon: FileText,
      description: 'All records',
      color: 'green',
    },
    {
      title: 'Database Size',
      value: databaseStats.databaseSize,
      icon: HardDrive,
      description: 'Storage used',
      color: 'purple',
    },
    {
      title: 'Performance',
      value: `${databaseStats.performance}%`,
      icon: Zap,
      description: 'Query performance',
      color: 'yellow',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Database Management</h1>
            <p className="text-muted-foreground">
              Monitor, maintain, and optimize database performance
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => console.log('Refresh stats not implemented yet')}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Stats
            </Button>
            <Button onClick={() => createBackupMutation.mutate()} disabled={createBackupMutation.isLoading}>
              <Archive className="mr-2 h-4 w-4" />
              {createBackupMutation.isLoading ? 'Creating...' : 'Create Backup'}
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">
              <Monitor className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="tables">
              <Table2 className="h-4 w-4 mr-2" />
              Tables
            </TabsTrigger>
            <TabsTrigger value="browser">
              <Globe className="h-4 w-4 mr-2" />
              Browser
            </TabsTrigger>
            <TabsTrigger value="query">
              <Search className="h-4 w-4 mr-2" />
              Query Tool
            </TabsTrigger>
            <TabsTrigger value="history">
              <History className="h-4 w-4 mr-2" />
              Change History
            </TabsTrigger>
            <TabsTrigger value="backups">
              <Archive className="h-4 w-4 mr-2" />
              Backups
            </TabsTrigger>
            <TabsTrigger value="maintenance">
              <Settings className="h-4 w-4 mr-2" />
              Maintenance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Database Health</CardTitle>
                  <CardDescription>Current database status and performance metrics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Performance Score</span>
                    <Badge variant="default">{databaseStats.performance}%</Badge>
                  </div>
                  <Progress value={databaseStats.performance} className="w-full" />

                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Uptime</span>
                      <span className="font-medium">{databaseStats.uptime}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Active Connections</span>
                      <span className="font-medium">{databaseStats.connections}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Last Backup</span>
                      <span className="font-medium">
                        {new Date(databaseStats.lastBackup).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Last Maintenance</span>
                      <span className="font-medium">
                        {new Date(databaseStats.lastMaintenance).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Storage Analysis</CardTitle>
                  <CardDescription>Database storage utilization and trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Total Size</span>
                      <Badge variant="outline">{databaseStats.databaseSize}</Badge>
                    </div>

                    <div className="space-y-3">
                      {tablesList.slice(0, 4).map((table: any) => (
                        <div key={table.name} className="flex items-center justify-between text-sm">
                          <span className="capitalize">{table.name.replace('_', ' ')}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-muted-foreground">
                              {table.records.toLocaleString()}
                            </span>
                            <span className="font-medium">{table.size}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    <Button variant="outline" className="w-full">
                      <Monitor className="mr-2 h-4 w-4" />
                      View Detailed Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Recent database operations and system events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(recentActivity || []).map((activity: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium text-sm">{activity.action}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={activity.status === 'success' ? 'default' : 'secondary'}>
                        {activity.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tables" className="space-y-4">
            <DatabaseOverview />
          </TabsContent>

          <TabsContent value="browser" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Database Browser</CardTitle>
                <CardDescription>
                  Browse and interact with your database using Prisma Studio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-green-500" />
                    <div>
                      <p className="font-medium">Supabase Studio</p>
                      <p className="text-sm text-muted-foreground">
                        Browser-based database manager
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button onClick={startSupabaseStudio}>
                      <Play className="h-4 w-4 mr-2" />
                      Open Supabase Studio
                    </Button>
                  </div>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Supabase Studio Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3 md:grid-cols-2">
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                        <div>
                          <p className="font-medium text-sm">SQL Editor</p>
                          <p className="text-xs text-muted-foreground">Write and execute SQL queries</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                          <div>
                            <p className="font-medium text-sm">Edit Records</p>
                            <p className="text-xs text-muted-foreground">Add, update, and delete records</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                          <div>
                            <p className="font-medium text-sm">Filter & Search</p>
                            <p className="text-xs text-muted-foreground">Advanced filtering and search</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-orange-500 rounded-full mt-2" />
                          <div>
                            <p className="font-medium text-sm">Relationship Navigation</p>
                            <p className="text-xs text-muted-foreground">Navigate between related records</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Quick Table Access</CardTitle>
                    <CardDescription>
                      Quick links to browse your most important tables
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-2 md:grid-cols-3">
                      {tablesList.slice(0, 6).map((table: any) => (
                        <Button
                          key={table.name}
                          variant="outline"
                          size="sm"
                          className="justify-start h-auto p-3"
                          onClick={() => {
                            openSupabaseStudio();
                          }}
                        >
                          <div className="text-left">
                            <div className="font-medium text-xs capitalize">
                              {table.name.replace('_', ' ')}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {table.records.toLocaleString()} records
                            </div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Database className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-blue-900 text-sm">About Prisma Studio</p>
                      <p className="text-blue-700 text-xs mt-1">
                        Prisma Studio is a visual database browser that runs on localhost:5555. 
                        It provides a user-friendly interface to view and edit your database records, 
                        making it perfect for development and debugging.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <QueryLogger />
          </TabsContent>

          <TabsContent value="query" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>SQL Query Tool</CardTitle>
                <CardDescription>Execute SQL queries and view results</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Sample Queries */}
                <div>
                  <Label className="text-sm font-medium">Sample Queries</Label>
                  <div className="grid gap-2 md:grid-cols-2 mt-2">
                    {sampleQueries.map((sample, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setQueryText(sample.query)}
                        className="justify-start h-auto p-3"
                      >
                        <div className="text-left">
                          <div className="font-medium text-xs">{sample.name}</div>
                          <div className="text-xs text-muted-foreground truncate">
                            {sample.query.substring(0, 50)}...
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Query Input */}
                <div>
                  <Label htmlFor="query-input" className="text-sm font-medium">
                    SQL Query
                  </Label>
                  <Textarea
                    id="query-input"
                    value={queryText}
                    onChange={(e: any) => setQueryText(e.target.value)}
                    placeholder="Enter your SQL query here..."
                    className="mt-2 min-h-[120px] font-mono text-sm"
                  />
                </div>

                {/* Execute Button */}
                <div className="flex justify-between items-center">
                  <div className="text-xs text-muted-foreground">
                    <Shield className="inline h-3 w-3 mr-1" />
                    Read-only queries only for security
                  </div>
                  <Button onClick={executeQuery} disabled={!queryText.trim() || isExecuting}>
                    {isExecuting ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Executing...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Execute Query
                      </>
                    )}
                  </Button>
                </div>

                {/* Query Results */}
                {queryResults && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Query Results</CardTitle>
                      <CardDescription>
                        {queryResults.error ? (
                          <span className="text-destructive">Error: {queryResults.error}</span>
                        ) : (
                          `${queryResults.rowCount || 0} rows returned in ${queryResults.executionTime}`
                        )}
                      </CardDescription>
                    </CardHeader>
                    {!queryResults.error && queryResults.rows && (
                      <CardContent>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                {queryResults.columns?.map((column: string) => (
                                  <th key={column} className="text-left p-2 font-medium">
                                    {column}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {queryResults.rows.map((row: any[], index: number) => (
                                <tr key={index} className="border-b">
                                  {row.map((cell, cellIndex) => (
                                    <td key={cellIndex} className="p-2">
                                      {cell === null ? 'NULL' : String(cell)}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </CardContent>
                    )}
                  </Card>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="backups" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Database Backups</CardTitle>
                <CardDescription>Manage database backups and restore points</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {backupHistory.length === 0 ? (
                    <div className="text-center py-8">
                      <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No backup history available.</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Backup history will appear here after scheduled backups are configured.
                      </p>
                    </div>
                  ) : (
                    backupHistory.map((backup: any) => (
                    <Card key={backup.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-base">{backup.filename}</CardTitle>
                            <CardDescription className="flex items-center space-x-4">
                              <Badge variant="outline" className="capitalize">
                                {backup.type}
                              </Badge>
                              <span>{backup.size}</span>
                              <span>Duration: {backup.duration}</span>
                              <span>{new Date(backup.created_at).toLocaleString()}</span>
                            </CardDescription>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="default">completed</Badge>
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))
                  )}

                  <div className="flex justify-center pt-4">
                    <Button onClick={() => createBackupMutation.mutate()} disabled={createBackupMutation.isLoading}>
                      <Archive className="mr-2 h-4 w-4" />
                      {createBackupMutation.isLoading ? 'Creating Backup...' : 'Create New Backup'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Database Maintenance</CardTitle>
                <CardDescription>Performance optimization and maintenance tools</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Optimize Indexes</CardTitle>
                      <CardDescription>
                        Rebuild and optimize database indexes for better performance
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button 
                        className="w-full"
                        onClick={() => runMaintenanceMutation.mutate()}
                        disabled={runMaintenanceMutation.isLoading}
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        {runMaintenanceMutation.isLoading ? 'Running...' : 'Run Index Optimization'}
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Update Statistics</CardTitle>
                      <CardDescription>
                        Update table statistics for query optimization
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button 
                        className="w-full"
                        onClick={() => runMaintenanceMutation.mutate()}
                        disabled={runMaintenanceMutation.isLoading}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        {runMaintenanceMutation.isLoading ? 'Updating...' : 'Update Statistics'}
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Vacuum Database</CardTitle>
                      <CardDescription>
                        Reclaim storage space and optimize database files
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button 
                        className="w-full"
                        onClick={() => runMaintenanceMutation.mutate()}
                        disabled={runMaintenanceMutation.isLoading}
                      >
                        <Archive className="h-4 w-4 mr-2" />
                        {runMaintenanceMutation.isLoading ? 'Vacuuming...' : 'Run Vacuum'}
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Analyze Performance</CardTitle>
                      <CardDescription>Generate performance analysis report</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button 
                        className="w-full"
                        onClick={() => runMaintenanceMutation.mutate()}
                        disabled={runMaintenanceMutation.isLoading}
                      >
                        <Monitor className="h-4 w-4 mr-2" />
                        {runMaintenanceMutation.isLoading ? 'Analyzing...' : 'Analyze Performance'}
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="text-base flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2 text-yellow-600" />
                      Maintenance Schedule
                    </CardTitle>
                    <CardDescription>Automated maintenance tasks and schedules</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span>Daily backup</span>
                        <Badge variant="default">02:00 AM</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Weekly index optimization</span>
                        <Badge variant="secondary">Sunday 03:00 AM</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Monthly statistics update</span>
                        <Badge variant="outline">1st of month 04:00 AM</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
