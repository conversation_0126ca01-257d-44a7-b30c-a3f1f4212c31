import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import DatabaseManagementClientPage from './client-page';

export default async function DatabaseManagementPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch database management data for faster loading
  void api.organizations.getCurrent.prefetch();

  // TODO: Implement database stats fetching from Supabase
  const databaseStats = null;
  const tables = null;
  const backups = null;

  return (
    <HydrateClient>
      <DatabaseManagementClientPage 
        initialStats={databaseStats}
        initialTables={tables}
        initialBackups={backups}
      />
    </HydrateClient>
  );
}