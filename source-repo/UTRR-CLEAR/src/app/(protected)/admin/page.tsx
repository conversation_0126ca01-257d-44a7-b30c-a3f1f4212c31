import { redirect } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Activity, BarChart3, Clock, Database, FileText, Settings, Users, Layers } from 'lucide-react';
import Link from 'next/link.js';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import { SupabaseDashboard } from '~/components/admin/supabase-dashboard';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';

export default async function AdminDashboardPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  // Check admin access
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch critical admin data for faster loading
  void api.organizations.checkSetup.prefetch();
  void api.admin.getStats.prefetch();
  void api.users.getAll.prefetch({ limit: 100 });
  void api.organizations.getCurrent.prefetch();

  // Fetch data server-side for immediate redirect logic
  const [setupStatus, stats] = await Promise.all([
    api.organizations.checkSetup(),
    api.admin.getStats()
  ]);

  // If organization is not set up, redirect to setup page
  if (!setupStatus.isSetup) {
    redirect('/admin/organization-setup');
  }

  const adminStats = [
    {
      title: 'Total Users',
      value: stats.users.toString(),
      description: 'Registered users',
      icon: Users,
      href: '/admin/user-management',
    },
    {
      title: 'Active Projects',
      value: stats.projects.toString(),
      description: 'Currently active',
      icon: FileText,
      href: '/projects',
    },
    {
      title: 'Total Utilities',
      value: stats.utilities.toString(),
      description: 'Tracked utilities',
      icon: Layers,
      href: '/projects',
    },
    {
      title: 'Conflicts Detected',
      value: stats.conflicts.toString(),
      description: 'Total conflicts found',
      icon: Activity,
      href: '/projects',
    },
  ];

  const adminActions = [
    {
      title: 'Organization Settings',
      description: 'Manage organization info, logos, and branding',
      icon: Settings,
      href: '/admin/organization-settings',
    },
    {
      title: 'User Management',
      description: 'Manage user accounts, roles, and permissions',
      icon: Users,
      href: '/admin/user-management',
    },
    {
      title: 'Template Center',
      description: 'Create and manage project templates and workflows',
      icon: Layers,
      href: '/admin/templates',
    },
    {
      title: 'Analytics Dashboard',
      description: 'View system analytics and usage statistics',
      icon: BarChart3,
      href: '/admin/analytics',
    },
    {
      title: 'Database Management',
      description: 'Database administration and maintenance tools',
      icon: Database,
      href: '/admin/database-management',
    },
    {
      title: 'Supabase Dashboard',
      description: 'Supabase backend management and monitoring',
      icon: Database,
      href: '#supabase',
    },
  ];

  return (
    <HydrateClient>
      <div className="container py-6">
        <div className="space-y-6">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">Admin Dashboard</h1>
              <RealtimeIndicator showDetails />
            </div>
            <p className="text-muted-foreground">System administration and management tools</p>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {adminStats.map((stat: any) => {
              const Icon = stat.icon;
              return (
                <Link key={stat.title} href={stat.href}>
                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                      <Icon className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stat.value}</div>
                      <p className="text-xs text-muted-foreground">{stat.description}</p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {adminActions.map((action: any) => {
              const Icon = action.icon;
              return (
                <Card key={action.title}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Icon className="mr-2 h-5 w-5" />
                      {action.title}
                    </CardTitle>
                    <CardDescription>{action.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Link href={action.href}>
                      <Button>Access {action.title}</Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              {false ? (
                <div className="space-y-2">
                  {[].map((activity: any) => (
                    <div key={activity.id} className="flex items-center justify-between border-b pb-2 last:border-0">
                      <div className="flex-1">
                        <p className="text-sm">
                          <span className="font-medium">{activity.users?.username || 'Unknown user'}</span>
                          {' '}{activity.action}
                          {activity.projects && (
                            <span className="text-muted-foreground"> in {activity.projects.name}</span>
                          )}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No recent activities found.</p>
              )}
            </CardContent>
          </Card>

          {/* Supabase Dashboard Section */}
          <div id="supabase">
            <SupabaseDashboard />
          </div>
        </div>
      </div>
    </HydrateClient>
  );
}