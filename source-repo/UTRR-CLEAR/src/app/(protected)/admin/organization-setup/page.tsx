import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import OrganizationSetupClientPage from './client-page';
import { Shell } from '~/components/layout/shell';
import { Loader2 } from 'lucide-react';

export default async function OrganizationSetupPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch organization setup data for faster loading
  void api.organizations.checkSetup.prefetch();
  void api.organizations.getCurrent.prefetch();

  // Check if organization is already set up
  const setupStatus = await api.organizations.checkSetup();

  // If already set up, redirect to admin
  if (setupStatus?.isSetup) {
    redirect('/admin');
  }

  return (
    <HydrateClient>
      <OrganizationSetupClientPage isSetup={setupStatus?.isSetup} />
    </HydrateClient>
  );
}