import { auth } from "~/server/auth";
import { redirect } from "next/navigation";
import { api, HydrateClient } from '~/trpc/server';
import TestResultsViewer from "./client-page";

export default async function TestResultsPage() {
  const session = await auth();
  
  if (!session) {
    redirect("/auth/signin");
  }

  // Check if user has admin role
  if (session.user.role !== "admin") {
    redirect("/dashboard");
  }

  // Pre-fetch admin data
  void api.organizations.getCurrent.prefetch();

  return (
    <HydrateClient>
      <TestResultsViewer />
    </HydrateClient>
  );
}