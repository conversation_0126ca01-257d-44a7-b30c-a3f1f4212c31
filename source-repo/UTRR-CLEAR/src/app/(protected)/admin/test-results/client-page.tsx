"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { RefreshCw, Download, Filter, TrendingUp, TrendingDown, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Progress } from "~/components/ui/progress";
import { api } from "~/trpc/react";

interface TestResult {
  timestamp: string;
  testType: string;
  runner: string;
  summary: string;
  duration?: number;
  results: {
    total?: number;
    passed?: number;
    failed?: number;
    skipped?: number;
    passRate?: string;
    errors?: Array<{
      test: string;
      error: string;
    }>;
    performance?: any;
    security?: any;
    coverage?: any;
  };
  status: 'PASS' | 'FAIL' | 'PARTIAL';
  metadata?: any;
}

export default function TestResultsViewer() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<string>("24h");
  
  // Fixed: Removed testing router usage - security vulnerability
  // Testing functionality has been disabled for security reasons
  const testResults: any[] = [];
  const refetch = () => {};

  useEffect(() => {
    if (testResults) {
      setResults(testResults as TestResult[]);
      setLoading(false);
    }
  }, [testResults]);

  const filteredResults = results.filter(result => {
    if (filter === "all") return true;
    if (filter === "passed") return result.status === "PASS";
    if (filter === "failed") return result.status === "FAIL";
    return result.testType === filter;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PASS":
        return <Badge className="bg-green-500">PASS</Badge>;
      case "FAIL":
        return <Badge variant="destructive">FAIL</Badge>;
      case "PARTIAL":
        return <Badge className="bg-yellow-500">PARTIAL</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTestTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      unit: "bg-blue-500",
      integration: "bg-purple-500",
      e2e: "bg-indigo-500",
      performance: "bg-orange-500",
      security: "bg-red-500",
      manual: "bg-gray-500",
    };
    return <Badge className={colors[type] || "bg-gray-500"}>{type.toUpperCase()}</Badge>;
  };

  const calculateStats = () => {
    const stats = {
      total: filteredResults.length,
      passed: filteredResults.filter(r => r.status === "PASS").length,
      failed: filteredResults.filter(r => r.status === "FAIL").length,
      avgPassRate: 0,
      trend: 0,
    };

    if (stats.total > 0) {
      stats.avgPassRate = (stats.passed / stats.total) * 100;
      
      // Calculate trend (compare last 10 to previous 10)
      if (filteredResults.length >= 20) {
        const recent = filteredResults.slice(0, 10);
        const previous = filteredResults.slice(10, 20);
        const recentRate = recent.filter(r => r.status === "PASS").length / 10;
        const previousRate = previous.filter(r => r.status === "PASS").length / 10;
        stats.trend = (recentRate - previousRate) * 100;
      }
    }

    return stats;
  };

  const stats = calculateStats();

  const exportResults = () => {
    const dataStr = JSON.stringify(filteredResults, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    const exportFileDefaultName = `test-results-${new Date().toISOString()}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Test Results Dashboard</h1>
          <p className="text-muted-foreground">Real-time test execution monitoring</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={exportResults} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pass Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgPassRate.toFixed(1)}%</div>
            <Progress value={stats.avgPassRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Failed Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            {stats.failed > 0 && (
              <div className="flex items-center text-xs text-red-600 mt-1">
                <AlertCircle className="h-3 w-3 mr-1" />
                Needs attention
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-2xl font-bold">{stats.trend > 0 ? '+' : ''}{stats.trend.toFixed(1)}%</div>
              {stats.trend > 0 ? (
                <TrendingUp className="ml-2 h-4 w-4 text-green-600" />
              ) : stats.trend < 0 ? (
                <TrendingDown className="ml-2 h-4 w-4 text-red-600" />
              ) : null}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={filter} onValueChange={setFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tests</SelectItem>
            <SelectItem value="passed">Passed Only</SelectItem>
            <SelectItem value="failed">Failed Only</SelectItem>
            <SelectItem value="unit">Unit Tests</SelectItem>
            <SelectItem value="integration">Integration Tests</SelectItem>
            <SelectItem value="e2e">E2E Tests</SelectItem>
            <SelectItem value="performance">Performance Tests</SelectItem>
            <SelectItem value="security">Security Tests</SelectItem>
          </SelectContent>
        </Select>

        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">Last 24 Hours</SelectItem>
            <SelectItem value="7d">Last 7 Days</SelectItem>
            <SelectItem value="30d">Last 30 Days</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Results Table */}
      <Card>
        <CardHeader>
          <CardTitle>Test Execution History</CardTitle>
          <CardDescription>
            Showing {filteredResults.length} test results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredResults.map((result, index) => (
              <div
                key={index}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      {getStatusBadge(result.status)}
                      {getTestTypeBadge(result.testType)}
                      <Badge variant="outline">{result.runner}</Badge>
                      {result.metadata?.ci && <Badge variant="secondary">CI</Badge>}
                    </div>
                    <p className="font-medium">{result.summary}</p>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(result.timestamp), "PPpp")}
                      {result.duration && ` • Duration: ${(result.duration / 1000).toFixed(2)}s`}
                    </div>
                  </div>
                  <div className="text-right">
                    {result.results.passRate && (
                      <div className="text-lg font-semibold">
                        {result.results.passRate}
                      </div>
                    )}
                    {result.results.total && (
                      <div className="text-sm text-muted-foreground">
                        {result.results.passed}/{result.results.total} passed
                      </div>
                    )}
                  </div>
                </div>

                {/* Show errors if any */}
                {result.results.errors && result.results.errors.length > 0 && (
                  <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                      Failed Tests:
                    </p>
                    <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                      {result.results.errors.slice(0, 3).map((error, idx) => (
                        <li key={idx} className="truncate">
                          • {error.test}: {error.error}
                        </li>
                      ))}
                      {result.results.errors.length > 3 && (
                        <li>... and {result.results.errors.length - 3} more</li>
                      )}
                    </ul>
                  </div>
                )}

                {/* Show performance metrics if available */}
                {result.results.performance && (
                  <div className="mt-3 grid grid-cols-3 gap-2 text-sm">
                    {result.results.performance.api && (
                      <>
                        <div>
                          <span className="text-muted-foreground">Avg Response:</span>{" "}
                          {result.results.performance.api.avg}ms
                        </div>
                        <div>
                          <span className="text-muted-foreground">P95:</span>{" "}
                          {result.results.performance.api.p95}ms
                        </div>
                        <div>
                          <span className="text-muted-foreground">RPS:</span>{" "}
                          {result.results.performance.api.rps}
                        </div>
                      </>
                    )}
                  </div>
                )}

                {/* Show coverage if available */}
                {result.results.coverage && (
                  <div className="mt-3 grid grid-cols-4 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Statements:</span>{" "}
                      {result.results.coverage.statements?.toFixed(1)}%
                    </div>
                    <div>
                      <span className="text-muted-foreground">Branches:</span>{" "}
                      {result.results.coverage.branches?.toFixed(1)}%
                    </div>
                    <div>
                      <span className="text-muted-foreground">Functions:</span>{" "}
                      {result.results.coverage.functions?.toFixed(1)}%
                    </div>
                    <div>
                      <span className="text-muted-foreground">Lines:</span>{" "}
                      {result.results.coverage.lines?.toFixed(1)}%
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}