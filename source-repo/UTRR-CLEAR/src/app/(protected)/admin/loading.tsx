import { Card, CardContent, CardHeader } from '~/components/ui/card';

export default function AdminLoading() {
  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div>
          <div className="h-9 w-64 bg-muted animate-pulse rounded" />
          <div className="h-5 w-96 bg-muted animate-pulse rounded mt-2" />
        </div>
        
        {/* Stats skeleton */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i: any) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 w-24 bg-muted rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted rounded" />
                <div className="h-3 w-32 bg-muted rounded mt-2" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Actions skeleton */}
        <div className="grid gap-6 md:grid-cols-2">
          {[1, 2, 3, 4, 5].map((i: any) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-48 bg-muted rounded" />
                <div className="h-4 w-72 bg-muted rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-10 w-32 bg-muted rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}