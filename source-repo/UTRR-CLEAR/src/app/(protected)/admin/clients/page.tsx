import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import ClientManagementClientPage from './client-page';

export default async function ClientManagementPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user?.isAdmin) {
    redirect('/dashboard');
  }

  // Prefetch basic data for faster loading
  void api.organizations.getCurrent.prefetch();

  // Fetch initial data server-side (API endpoints not implemented yet)
  const initialClients: any[] = []; // Will be replaced when API is implemented
  const initialStats = {
    totalClients: 0,
    activeClients: 0,
    totalRevenue: 0,
    avgProjectValue: 0,
    retentionRate: 0,
  }; // Will be replaced when API is implemented

  return (
    <HydrateClient>
      <ClientManagementClientPage initialClients={initialClients} initialStats={initialStats} />
    </HydrateClient>
  );
}