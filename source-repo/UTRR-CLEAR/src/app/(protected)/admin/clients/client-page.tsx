'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Building,
  Calendar,
  DollarSign,
  Edit,
  FileText,
  Mail,
  MapPin,
  Phone,
  Plus,
  Search,
  TrendingUp,
  Users,
  Loader2,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { CommentBadge } from '~/components/comments/comment-badge';

interface ClientManagementClientPageProps {
  initialClients?: any[];
  initialStats?: {
    totalClients: number;
    activeClients: number;
    totalRevenue: number;
    avgProjectValue: number;
    retentionRate: number;
  };
}

export default function ClientManagementClientPage({ 
  initialClients, 
  initialStats 
}: ClientManagementClientPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [selectedClient, setSelectedClient] = useState<string | null>(null);

  // Query from clients table (will show empty until data is added)
  const clientsLoading = false;
  const statsLoading = false;
  const clients = initialClients || [];
  const clientStats = initialStats || {
    totalClients: 0,
    activeClients: 0,
    totalRevenue: 0,
    avgProjectValue: 0,
    retentionRate: 0,
  };

  const clientTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'government', label: 'Government' },
    { value: 'municipal', label: 'Municipal' },
    { value: 'authority', label: 'Authority' },
    { value: 'utility', label: 'Utility Company' },
    { value: 'private', label: 'Private' },
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'government':
        return 'default';
      case 'municipal':
        return 'secondary';
      case 'authority':
        return 'outline';
      case 'utility':
        return 'secondary';
      case 'private':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'inactive':
        return 'secondary';
      case 'pending':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (clientsLoading || statsLoading) {
    return (
      <div className="container py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  const kpiCards = [
    {
      title: 'Total Clients',
      value: clientStats?.totalClients || 0,
      icon: Building,
      description: `${clientStats?.activeClients || 0} active clients`,
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(clientStats?.totalRevenue || 0),
      icon: DollarSign,
      description: 'All-time revenue',
    },
    {
      title: 'Avg Project Value',
      value: formatCurrency(clientStats?.avgProjectValue || 0),
      icon: TrendingUp,
      description: 'Per project average',
    },
    {
      title: 'Retention Rate',
      value: `${clientStats?.retentionRate || 0}%`,
      icon: Users,
      description: 'Client retention',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Client Management</h1>
            <p className="text-muted-foreground">
              Manage client relationships and track business metrics
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              Export Clients
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Client
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="clients" className="space-y-4">
          <TabsList>
            <TabsTrigger value="clients">Clients</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
          </TabsList>

          <TabsContent value="clients" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Client Directory</CardTitle>
                <CardDescription>Manage client information and relationships</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filters */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search clients..."
                        value={searchTerm}
                        onChange={(e: any) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {clientTypes.map((option: any) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Clients List */}
                <div className="space-y-4">
                  {clients && clients.length > 0 ? (
                    clients.map((client: any) => (
                      <Card
                        key={client.id}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedClient === client.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() =>
                          setSelectedClient(client.id === selectedClient ? null : client.id)
                        }
                      >
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Building className="h-5 w-5 text-blue-600" />
                              <div>
                                <CardTitle className="text-base flex items-center gap-2">
                                  {client.name}
                                  <CommentBadge
                                    entityType="client"
                                    entityId={client.id}
                                    entityName={client.name}
                                    variant="icon-only"
                                    showZero={false}
                                  />
                                </CardTitle>
                                <CardDescription className="flex items-center space-x-4 mt-1">
                                  <span>{client.primary_contact}</span>
                                  <span>•</span>
                                  <span>{client.active_projects} active projects</span>
                                  <span>•</span>
                                  <span>{formatCurrency(client.total_revenue)} revenue</span>
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={getTypeColor(client.type)}>{client.type}</Badge>
                              <Badge variant={getStatusColor(client.status)}>{client.status}</Badge>
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Building className="w-12 h-12 text-muted-foreground opacity-60 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-1">No Clients Found</h3>
                      <p className="text-muted-foreground mb-4">
                        {searchTerm || typeFilter !== 'all'
                          ? 'No clients match your current filters'
                          : 'Start by adding your first client'}
                      </p>
                      <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Client
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Client Analytics</CardTitle>
                <CardDescription>Performance metrics and insights</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Client analytics dashboard coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Contract Management</CardTitle>
                <CardDescription>Track contracts and agreements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Contract management coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}