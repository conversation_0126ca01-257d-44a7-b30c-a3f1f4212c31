import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import StakeholdersClientPage from './client-page';

export default async function StakeholdersPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch stakeholder data for faster loading
  void api.utilities.getAll.prefetch({ limit: 100 });
  void api.organizations.getCurrent.prefetch();
  void api.projects.getActiveProjects.prefetch();

  // Pass organizationId to client component for realtime subscriptions
  const organizationId = session.user.organizationId || '';

  return (
    <HydrateClient>
      <StakeholdersClientPage organizationId={organizationId} />
    </HydrateClient>
  );
}