'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Plus, Search, Users } from 'lucide-react';
import { useSupabaseRealtimeProjects } from '~/hooks/use-supabase-realtime';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';

interface StakeholdersClientPageProps {
  organizationId: string;
}

export default function StakeholdersClientPage({ organizationId }: StakeholdersClientPageProps) {
  // Add real-time updates for stakeholder changes with proper organizationId
  const realtimeState = useSupabaseRealtimeProjects(organizationId);

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">Stakeholders</h1>
              <RealtimeIndicator showDetails />
            </div>
            <p className="text-muted-foreground">
              Manage project stakeholders and contact information
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Stakeholder
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search stakeholders..." className="pl-8" />
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Users className="mr-2 h-5 w-5" />
                Total Stakeholders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">No stakeholders found</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Active Projects</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">With stakeholder assignments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Recent Updates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">In the last 30 days</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Stakeholder Management</CardTitle>
            <CardDescription>
              Stakeholder management functionality will be implemented in Phase 4
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              This will include contact management, role assignments, communication tracking, and
              integration with project workflows.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}