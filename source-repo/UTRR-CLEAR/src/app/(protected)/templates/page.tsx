import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import { TemplateManager } from "~/components/templates/template-manager";

export default async function TemplatesPage() {
  const session = await getServerAuthSession();
  
  // Prefetch template data for faster loading
  void api.projectTemplates.getAll.prefetch();
  
  return (
    <HydrateClient>
      <TemplateManager />
    </HydrateClient>
  );
}