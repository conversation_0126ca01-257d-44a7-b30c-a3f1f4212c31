'use client';

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { ScrollArea } from "~/components/ui/scroll-area";
import { 
  DndContext, 
  closestCenter, 
  KeyboardSensor, 
  PointerSensor, 
  useSensor, 
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Search, Settings, Download, Filter, GripVertical } from "lucide-react";
import { Skeleton } from "~/components/ui/skeleton";
import { Shell } from "~/components/layout/shell";
import { api } from "~/trpc/react";
import type { RouterOutputs } from "~/trpc/react";

interface Project {
  id: string;
  name: string;
  client: string;
  work_type: string;
  coordination_type: string;
  project_funding: string;
  egis_project_manager: string;
  egis_project_manager_email: string;
  client_job_number: string;
  current_phase: string;
  "Project Health (RAG)": string;
  start_date: string;
  end_date: string;
  project_id_only: string;
  phase_id_only: string;
  created_at: string;
  updated_at: string;
  [key: string]: any;
}

interface SortableColumnHeaderProps {
  column: string;
  displayName: string;
}

const SortableColumnHeader: React.FC<SortableColumnHeaderProps> = ({ column, displayName }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: column });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <TableHead 
      ref={setNodeRef} 
      style={style} 
      className="relative bg-gray-50 border-r last:border-r-0"
    >
      <div className="flex items-center space-x-2 min-w-0">
        <button
          className="flex items-center space-x-1 cursor-move hover:bg-gray-100 p-1 rounded"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-3 w-3 text-gray-400" />
        </button>
        <span className="font-medium text-sm truncate" title={displayName}>
          {displayName}
        </span>
      </div>
    </TableHead>
  );
};

const formatColumnName = (columnName: string): string => {
  // Handle special cases
  if (columnName === "Project Health (RAG)") return "Project Health";
  if (columnName === "egis_project_manager") return "EGIS PM";
  if (columnName === "egis_project_manager_email") return "PM Email";
  if (columnName === "client_job_number") return "Job Number";
  if (columnName === "work_type") return "Work Type";
  if (columnName === "coordination_type") return "Coordination";
  if (columnName === "project_funding") return "Funding";
  if (columnName === "current_phase") return "Phase";
  if (columnName === "start_date") return "Start Date";
  if (columnName === "end_date") return "End Date";
  if (columnName === "project_id_only") return "Project ID";
  if (columnName === "phase_id_only") return "Phase ID";
  if (columnName === "created_at") return "Created";
  if (columnName === "updated_at") return "Updated";
  
  // Convert snake_case to Title Case
  return columnName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const formatCellValue = (value: any, columnName: string): string => {
  if (value === null || value === undefined || value === '') {
    return '-';
  }
  
  // Format dates
  if (columnName.includes('date') || columnName.includes('_at')) {
    try {
      return new Date(value).toLocaleDateString();
    } catch {
      return String(value);
    }
  }
  
  return String(value);
};

const getRAGColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'on track': return 'bg-green-100 text-green-800';
    case 'at risk': return 'bg-yellow-100 text-yellow-800';
    case 'off track': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

interface ProjectPortfolioClientPageProps {
  initialProjects?: RouterOutputs['projects']['getAll'];
}

export default function ProjectPortfolioClientPage({ initialProjects }: ProjectPortfolioClientPageProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [columns, setColumns] = useState<string[]>([]);

  // Debounce search term to reduce API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);
  
  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Use tRPC to fetch projects data with initial data and debounced search
  const { data: projectsResponse, isLoading, error } = api.projects.getAll.useQuery(
    {
      search: debouncedSearchTerm,
      limit: 100, // Set reasonable limit
    },
    {
      initialData: initialProjects,
      refetchOnMount: false,
      refetchOnReconnect: false,
      enabled: true, // Always fetch data
      staleTime: 5 * 60 * 1000, // 5 minutes - reduce unnecessary refetches
      gcTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Transform tRPC data to match original interface
  const projects: Project[] = React.useMemo(() => {
    if (!projectsResponse?.projects) return [];
    
    return projectsResponse.projects.map(project => ({
      id: String(project.id),
      name: project.name || '',
      client: (project as any).client || '',
      work_type: (project as any).work_type || '',
      coordination_type: (project as any).coordination_type || '',
      project_funding: (project as any).project_funding || '',
      egis_project_manager: (project as any).egis_project_manager || '',
      egis_project_manager_email: (project as any).egis_project_manager_email || '',
      client_job_number: (project as any).client_job_number || '',
      current_phase: (project as any).current_phase || '',
      "Project Health (RAG)": (project as any).rag_status || (project as any)["Project Health (RAG)"] || 'Unknown',
      start_date: project.start_date ? new Date(project.start_date).toISOString() : '',
      end_date: project.end_date ? new Date(project.end_date).toISOString() : '',
      project_id_only: (project as any).project_id_only || '',
      phase_id_only: (project as any).phase_id_only || '',
      created_at: project.created_at ? new Date(project.created_at).toISOString() : '',
      updated_at: project.updated_at ? new Date(project.updated_at).toISOString() : '',
      // Add any additional fields from the project
      ...Object.fromEntries(
        Object.entries(project).filter(([key]) => 
          !['id', 'name', 'client', 'work_type', 'coordination_type', 'project_funding',
            'egis_project_manager', 'egis_project_manager_email', 'client_job_number',
            'current_phase', 'rag_status', 'start_date', 'end_date', 'project_id_only',
            'phase_id_only', 'created_at', 'updated_at'].includes(key)
        )
      )
    }));
  }, [projectsResponse]);

  // Initialize columns when projects data is loaded
  useEffect(() => {
    if (projects && projects.length > 0 && projects[0]) {
      const allColumns = Object.keys(projects[0]);
      // Define a preferred order for columns
      const preferredOrder = [
        'id', 'name', 'client', 'work_type', 'coordination_type', 
        'project_funding', 'egis_project_manager', 'Project Health (RAG)',
        'current_phase', 'start_date', 'end_date', 'project_id_only', 
        'phase_id_only', 'client_job_number', 'egis_project_manager_email',
        'created_at', 'updated_at'
      ];
      
      // Sort columns by preferred order, with remaining columns at the end
      const sortedColumns = [
        ...preferredOrder.filter(col => allColumns.includes(col)),
        ...allColumns.filter(col => !preferredOrder.includes(col))
      ];
      
      setColumns(sortedColumns);
    }
  }, [projects]);

  // Filter projects based on search term (use immediate search for better UX)
  const filteredProjects = React.useMemo(() => {
    if (!projects) return [];
    if (!searchTerm.trim()) return projects;
    
    return projects.filter(project =>
      Object.values(project).some((value: any) =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [projects, searchTerm]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setColumns((columns) => {
        const oldIndex = columns.indexOf(active.id as string);
        const newIndex = columns.indexOf(over.id as string);

        return arrayMove(columns, oldIndex, newIndex);
      });
    }
  };

  if (isLoading && !initialProjects) {
    return (
      <Shell>
        <div className="container mx-auto py-6 space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
            <Skeleton className="h-10 w-32" />
          </div>
          
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </Shell>
    );
  }

  if (error) {
    return (
      <Shell>
        <div className="container mx-auto py-6">
          <Card>
            <CardContent className="py-6">
              <div className="text-center text-red-600">
                Error loading projects: {error instanceof Error ? error.message : 'Unknown error'}
              </div>
            </CardContent>
          </Card>
        </div>
      </Shell>
    );
  }

  return (
    <Shell>
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Project Portfolio</h1>
            <p className="text-muted-foreground">
              Complete overview of all projects with full details
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search projects..."
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">
                {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Project Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Projects</CardTitle>
            <CardDescription>
              Drag column headers to reorder them. Scroll horizontally to see all columns.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg">
              <ScrollArea className="w-full whitespace-nowrap">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <SortableContext 
                          items={columns} 
                          strategy={horizontalListSortingStrategy}
                        >
                          {columns.map((column: any) => (
                            <SortableColumnHeader
                              key={column}
                              column={column}
                              displayName={formatColumnName(column)}
                            />
                          ))}
                        </SortableContext>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredProjects.map((project, rowIndex) => (
                        <TableRow 
                          key={project.id} 
                          className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}
                        >
                          {columns.map((column: any) => (
                            <TableCell 
                              key={`${project.id}-${column}`} 
                              className="border-r last:border-r-0 max-w-xs"
                            >
                              {column === "Project Health (RAG)" ? (
                                <Badge 
                                  variant="secondary" 
                                  className={getRAGColor(project[column])}
                                >
                                  {formatCellValue(project[column], column)}
                                </Badge>
                              ) : (
                                <div 
                                  className="truncate" 
                                  title={formatCellValue(project[column], column)}
                                >
                                  {formatCellValue(project[column], column)}
                                </div>
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </DndContext>
              </ScrollArea>
            </div>
            
            {filteredProjects.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No projects found matching your search criteria.
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Shell>
  );
}