import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import ProjectPortfolioClientPage from './client-page';

export default async function ProjectPortfolioPage() {
  const session = await getServerAuthSession();
  
  // Prefetch critical portfolio data for faster loading
  void api.projects.getAll.prefetch({ search: '', limit: 50 });
  void api.projects.getActiveProjects.prefetch();
  void api.organizations.getCurrent.prefetch();
  void api.utilities.getAll.prefetch({ limit: 100 });

  // Fetch initial projects data server-side
  const initialProjects = await api.projects.getAll({
    search: '',
  });

  return (
    <HydrateClient>
      <ProjectPortfolioClientPage initialProjects={initialProjects} />
    </HydrateClient>
  );
}
