import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import NewProjectClientPage from './client-page';

export default async function NewProjectPage() {
  const session = await getServerAuthSession();
  
  // Prefetch data needed for creating new projects
  void api.projectTemplates.getAll.prefetch();
  void api.organizations.getCurrent.prefetch();
  void api.utilities.getAll.prefetch({ limit: 100 });
  
  return (
    <HydrateClient>
      <NewProjectClientPage />
    </HydrateClient>
  );
}