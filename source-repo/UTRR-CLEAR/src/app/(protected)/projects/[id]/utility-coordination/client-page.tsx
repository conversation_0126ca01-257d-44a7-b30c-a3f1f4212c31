'use client';

import { useParams, redirect } from 'next/navigation';
import { useAuth } from '~/hooks/use-auth';
import { useState } from 'react';
import { useRealtimeConflicts, useRealtimeUtilities } from '~/hooks/use-realtime-events';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import {
  AlertTriangle,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Mail,
  MapPin,
  MessageSquare,
  Phone,
  Plus,
  Search,
  Users,
  Zap,
  Map,
  Layers3,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { Shell } from '~/components/layout/shell';
import Link from 'next/link.js';

// Import our GIS components
import { MapWrapper } from '~/components/gis/map-wrapper';

export default function ProjectUtilityCoordinationClientPage() {
  const { user } = useAuth();
  const params = useParams();
  const projectId = params?.id as string;
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [mapView, setMapView] = useState<'2d' | '3d'>('2d');

  if (!user) {
    redirect('/auth/signin');
  }

  // Get project details
  const { data: project } = api.projects.getById.useQuery(
    { id: Number(projectId) },
    { enabled: !!projectId }
  );

  // Add real-time conflict and utility updates - CRITICAL for safety!
  useRealtimeConflicts(projectId);
  useRealtimeUtilities(projectId);

  // Mock data for utility coordination activities specific to this project
  const coordinationActivities = [
    {
      id: 1,
      projectId,
      utilityCompany: 'Pacific Gas & Electric',
      utilityType: 'Gas',
      status: 'in-progress',
      priority: 'high',
      description: 'Relocate gas main for street widening',
      lastContact: '2024-01-20',
      nextAction: 'Site meeting scheduled',
      contactName: 'John Smith',
      contactPhone: '(*************',
      contactEmail: '<EMAIL>',
    },
    {
      id: 2,
      projectId,
      utilityCompany: 'AT&T',
      utilityType: 'Telecom',
      status: 'pending',
      priority: 'medium',
      description: 'Underground fiber optic relocation',
      lastContact: '2024-01-18',
      nextAction: 'Awaiting design approval',
      contactName: 'Sarah Johnson',
      contactPhone: '(*************',
      contactEmail: '<EMAIL>',
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Pending', variant: 'secondary' as const },
      'in-progress': { label: 'In Progress', variant: 'default' as const },
      completed: { label: 'Completed', variant: 'success' as const },
      blocked: { label: 'Blocked', variant: 'destructive' as const },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config?.variant}>{config?.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Low', className: 'bg-gray-100 text-gray-800' },
      medium: { label: 'Medium', className: 'bg-yellow-100 text-yellow-800' },
      high: { label: 'High', className: 'bg-red-100 text-red-800' },
    };
    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return <Badge className={config?.className}>{config?.label}</Badge>;
  };

  const filteredActivities = coordinationActivities.filter((activity: any) => {
    const matchesSearch =
      searchTerm === '' ||
      activity.utilityCompany.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || activity.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || activity.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <Shell>
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Link href={`/projects/${projectId}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Project
              </Button>
            </Link>
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-3xl font-bold">Utility Coordination</h1>
                <RealtimeIndicator showDetails />
              </div>
              {project && (
                <p className="text-muted-foreground">
                  {project.name} - {project.id}
                </p>
              )}
            </div>
          </div>
        </div>

        <Tabs defaultValue="coordination" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="coordination">Coordination</TabsTrigger>
            <TabsTrigger value="mapping">GIS Mapping</TabsTrigger>
            <TabsTrigger value="conflicts">Conflicts</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="coordination" className="space-y-4">
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <Label htmlFor="search" className="sr-only">
                  Search utilities
                </Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by utility company or description..."
                    value={searchTerm}
                    onChange={(e: any) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="blocked">Blocked</SelectItem>
                </SelectContent>
              </Select>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Utility
              </Button>
            </div>

            <div className="grid gap-4">
              {filteredActivities.map((activity: any) => (
                <Card key={activity.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-xl">{activity.utilityCompany}</CardTitle>
                        <CardDescription className="mt-1">
                          {activity.utilityType} • {activity.description}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        {getStatusBadge(activity.status)}
                        {getPriorityBadge(activity.priority)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Contact:</span>
                          <span>{activity.contactName}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span>{activity.contactPhone}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span>{activity.contactEmail}</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Last Contact:</span>
                          <span>{activity.lastContact}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <MessageSquare className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Next Action:</span>
                          <span>{activity.nextAction}</span>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 flex gap-2">
                      <Button size="sm" variant="outline">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Add Note
                      </Button>
                      <Button size="sm" variant="outline">
                        <FileText className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="mapping" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>GIS Utility Mapping</CardTitle>
                    <CardDescription>
                      Draw utilities in 2D and visualize conflicts in 3D
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant={mapView === '2d' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setMapView('2d')}
                    >
                      <Map className="mr-2 h-4 w-4" />
                      2D Map
                    </Button>
                    <Button
                      variant={mapView === '3d' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setMapView('3d')}
                    >
                      <Layers3 className="mr-2 h-4 w-4" />
                      3D View
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <MapWrapper projectId={projectId} view={mapView} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="conflicts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Utility Conflicts</CardTitle>
                <CardDescription>
                  View and manage utility conflicts detected from GIS mapping
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground mb-4">
                  <p>Use the GIS Mapping tab to:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Draw utility lines with type and depth information</li>
                    <li>Automatically detect soft conflicts (proximity within 2ft)</li>
                    <li>Automatically detect hard conflicts (crossing at same depth)</li>
                    <li>View conflicts in 3D visualization</li>
                  </ul>
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    const tabsList = document.querySelector('[role="tablist"]');
                    const mappingTab = tabsList?.querySelector('[value="mapping"]') as HTMLButtonElement;
                    mappingTab?.click();
                  }}
                >
                  <MapPin className="mr-2 h-4 w-4" />
                  Go to GIS Mapping
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Coordination Schedule</CardTitle>
                <CardDescription>Timeline and milestones for utility coordination</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Schedule information coming soon.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Coordination Documents</CardTitle>
                <CardDescription>Agreements, permits, and related documents</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">No documents uploaded yet.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Shell>
  );
}