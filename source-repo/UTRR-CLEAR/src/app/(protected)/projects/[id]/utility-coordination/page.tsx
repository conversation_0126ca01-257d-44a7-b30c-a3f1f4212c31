import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import ProjectUtilityCoordinationClientPage from './client-page';

type Props = {
  params: Promise<{ id: string }>;
};

export default async function ProjectUtilityCoordinationPage({ params }: Props) {
  const { id } = await params;
  const session = await getServerAuthSession();

  // Prefetch utility coordination data for faster loading
  void api.projects.getById.prefetch({ id: Number(id) });

  return (
    <HydrateClient>
      <ProjectUtilityCoordinationClientPage />
    </HydrateClient>
  );
}