import { notFound } from 'next/navigation';
import { getServerAuthSession } from '~/server/auth';
import { api, HydrateClient } from '~/trpc/server';
import EditProjectClientPage from './client-page';

interface EditProjectPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditProjectPage({ params }: EditProjectPageProps) {
  const session = await getServerAuthSession();
  
  // Await the params to get the id
  const { id } = await params;
  
  // Prefetch project edit data for faster loading
  void api.projects.getById.prefetch({ id: Number(id) });
  void api.projectTemplates.getAll.prefetch();
  
  // Fetch initial project data server-side
  let initialProject;
  try {
    initialProject = await api.projects.getById({ id: Number(id) });
  } catch (error) {
    // If project not found, show 404
    notFound();
  }
  
  return (
    <HydrateClient>
      <EditProjectClientPage projectId={id} initialProject={initialProject} />
    </HydrateClient>
  );
}