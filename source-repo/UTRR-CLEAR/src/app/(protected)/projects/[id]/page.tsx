import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import ProjectDetailClientPage from './client-page';

type Props = {
  params: Promise<{ id: string }>;
};

export default async function ProjectDetailPage({ params }: Props) {
  const { id } = await params;
  const session = await getServerAuthSession();

  // Prefetch project detail data for faster loading
  void api.projects.getById.prefetch({ id: Number(id) });

  return (
    <HydrateClient>
      <ProjectDetailClientPage projectId={id} />
    </HydrateClient>
  );
}
