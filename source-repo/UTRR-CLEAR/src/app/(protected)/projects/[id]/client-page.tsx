'use client';

import { useState } from 'react';
import Link from 'next/link.js';
import { ArrowLeft, FileText } from 'lucide-react';
import { formatDate } from '~/lib/utils';
import { Badge } from '~/components/ui/badge';
import { But<PERSON> } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { api } from '~/trpc/react';
import { useProjectRealtime } from '~/hooks/use-realtime-events';
import { ProjectRealtimeUpdates } from '~/components/project/project-realtime-updates';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { MapViewer } from '~/components/mapping/map-viewer';
import { DocumentUpload } from '~/components/file-upload/document-upload';
import { Shell } from '~/components/layout/shell';

// Import our enhanced project management components
import { ProjectDetailsCard as EnhancedProjectDetailsCard } from '~/components/project/project-details-card';
import { ProjectTimeline } from '~/components/project/project-timeline';
import { CommunicationLog } from '~/components/project/communication-log';
import { DocumentsManagement as EnhancedDocumentsManagement } from '~/components/project/documents-management';
import { ProjectNotes } from '~/components/projects/project-notes';
import { WorkflowTemplateView } from '~/components/project/workflow-template-view';
import ProjectGanttContainer from '~/components/project/project-gantt-container';
import TaskDataGrid from '~/components/project/task-data-grid';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { SubtaskList } from '~/components/project/subtask-list';

// Enhanced utility coordination component with mapping
const UtilitiesManagement = ({ projectId }: { projectId: string }) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-semibold">Utility Coordination</h3>
      <Link href={`/projects/${projectId}/utility-coordination`}>
        <Button size="sm">
          View Full Coordination
          <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
        </Button>
      </Link>
    </div>
    <MapViewer projectId={projectId} height="500px" />
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Active Utilities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">12</div>
          <p className="text-xs text-muted-foreground">Companies involved</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Conflicts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-amber-600">3</div>
          <p className="text-xs text-muted-foreground">Require resolution</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Agreements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">8</div>
          <p className="text-xs text-muted-foreground">Signed & active</p>
        </CardContent>
      </Card>
    </div>
  </div>
);

// Enhanced utility agreements component
const UtilityAgreementManagement = ({ projectId }: { projectId: string }) => {
  // TODO: Replace with real data from tRPC query
  const agreementData = [
    { company: 'Duke Energy', type: 'Power Relocation', status: 'Signed', date: '2024-01-15' },
    { company: 'Citizens Gas', type: 'Gas Line Protection', status: 'Pending', date: '2024-01-20' },
    { company: 'AT&T', type: 'Fiber Relocation', status: 'Draft', date: '2024-01-25' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Utility Agreements</h3>
          <p className="text-sm text-muted-foreground">
            Manage agreements and contracts with utility companies
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" disabled className="opacity-50 cursor-not-allowed">
            <span className="mr-2">+</span>
            New Agreement
          </Button>
          <Badge variant="outline" className="text-xs">
            Coming Soon
          </Badge>
        </div>
      </div>

      <div className="text-center py-8 border border-dashed rounded-lg">
        <div className="space-y-3">
          <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
            <FileText className="h-6 w-6 text-muted-foreground" />
          </div>
          <div>
            <h4 className="font-medium">Agreement Templates Coming Soon</h4>
            <p className="text-sm text-muted-foreground">
              Please propose a template for an admin to review if you would like to create agreements.
              Templates will be available after they are created in the Template Editor.
            </p>
          </div>
          <Button variant="outline" size="sm" disabled>
            Suggest Template
          </Button>
        </div>
      </div>

      {/* Show sample data for now */}
      <div className="space-y-4">
        <h4 className="font-medium text-muted-foreground">Sample Agreement Data:</h4>
        {agreementData.map((agreement, i) => (
          <Card key={i} className="opacity-60">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="font-medium">{agreement.company}</h5>
                  <p className="text-sm text-muted-foreground">{agreement.type}</p>
                </div>
                <div className="text-right">
                  <Badge
                    variant={
                      agreement.status === 'Signed'
                        ? 'default'
                        : agreement.status === 'Pending'
                          ? 'secondary'
                          : 'outline'
                    }
                  >
                    {agreement.status}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">{agreement.date}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

// Enhanced conflict matrix component with per-project numbering
const ConflictMatrix = ({ projectId }: { projectId: string }) => {
  // TODO: Replace with real tRPC query for project conflicts
  // const { data: conflicts } = api.conflicts.getByProject.useQuery({ projectId });
  
  // Sample conflicts with per-project numbering (CF-001, CF-002, etc.)
  const sampleConflicts = [
    {
      id: `CF-001`,
      projectId,
      utilities: ['Duke Energy', 'Citizens Gas'],
      severity: 'High',
      description: 'Power line crosses gas main at Station 15+00',
      status: 'Open',
      coordinates: { lat: 39.7684, lng: -86.1581 },
      depth: '8 feet',
      detectedDate: '2024-02-15',
    },
    {
      id: `CF-002`,
      projectId,
      utilities: ['AT&T', 'Comcast'],
      severity: 'Medium', 
      description: 'Fiber optic lines in close proximity - clearance verification required',
      status: 'Monitoring',
      coordinates: { lat: 39.7691, lng: -86.1575 },
      depth: '4 feet',
      detectedDate: '2024-02-18',
    },
    {
      id: `CF-003`,
      projectId,
      utilities: ['Water Authority', 'Duke Energy'],
      severity: 'Low',
      description: 'Standard clearance verification completed successfully',
      status: 'Resolved',
      coordinates: { lat: 39.7679, lng: -86.1588 },
      depth: '6 feet',
      detectedDate: '2024-02-10',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Utility Conflicts</h3>
          <p className="text-sm text-muted-foreground">
            Each project maintains its own conflict numbering starting from CF-001
          </p>
        </div>
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <span className="mr-2">+</span>
            Report Conflict
          </Button>
          <Button size="sm" variant="outline">
            Export Report
          </Button>
        </div>
      </div>

      {/* Stats summary */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-red-600">1</div>
            <p className="text-xs text-muted-foreground">High Severity</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-orange-600">1</div>
            <p className="text-xs text-muted-foreground">Medium Severity</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-green-600">1</div>
            <p className="text-xs text-muted-foreground">Resolved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Total Conflicts</p>
          </CardContent>
        </Card>
      </div>

      {/* Conflicts list */}
      <div className="grid gap-4">
        {sampleConflicts.map((conflict: any) => (
          <Card key={conflict.id} className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between mb-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono">{conflict.id}</Badge>
                    <Badge
                      variant={
                        conflict.severity === 'High'
                          ? 'destructive'
                          : conflict.severity === 'Medium'
                            ? 'default'
                            : 'secondary'
                      }
                    >
                      {conflict.severity}
                    </Badge>
                    <Badge variant={conflict.status === 'Resolved' ? 'default' : 'secondary'}>
                      {conflict.status}
                    </Badge>
                  </div>
                  <h4 className="font-medium">{conflict.utilities.join(' vs ')}</h4>
                  <p className="text-sm text-muted-foreground">{conflict.description}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Detected:</span>
                  <p className="font-medium">{conflict.detectedDate}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Depth:</span>
                  <p className="font-medium">{conflict.depth}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Location:</span>
                  <p className="font-medium">{conflict.coordinates.lat.toFixed(4)}, {conflict.coordinates.lng.toFixed(4)}</p>
                </div>
              </div>
              
              <div className="flex gap-2 mt-4">
                <Button size="sm" variant="outline">
                  View on Map
                </Button>
                <Button size="sm" variant="outline">
                  Update Status
                </Button>
                <Button size="sm" variant="outline">
                  Add Note
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default function ProjectDetailClientPage({ projectId }: { projectId: string }) {
  const [activeTab, setActiveTab] = useState('Details');

  const tabs = [
    'Details',
    'Real-time',
    'Timeline',
    'Gantt Chart',
    'Task Management',
    'Utility Stakeholders',
    'Communications',
    'GIS Map',
    'Utility Agreements',
    'Conflicts',
    'Documents',
    'Notes',
    'Workflow Template',
  ];

  // Check if this is demo mode
  const isDemoMode = projectId === 'demo';

  const {
    data: project,
    isLoading,
    error,
  } = api.projects.getById.useQuery({ id: Number(projectId) }, { enabled: !!projectId && !isDemoMode });

  // Real-time updates are handled by ProjectRealtimeUpdates component to avoid duplicate subscriptions

  // Create demo project data for demo mode
  const demoProject = isDemoMode ? {
    id: 'demo',
    name: 'Demo Project - UI Preview',
    client: 'Demo Client',
    rag_status: 'Green',
    current_phase: 'Planning',
    start_date: new Date().toISOString(),
    end_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
    description: 'This is a demo project showing the full UI structure and available features.',
    project_priority: 'High',
    egis_project_manager: 'Demo Manager',
    client_pm: 'Client Manager',
    contract_amount: 500000,
    billed_to_date: 125000,
    work_type: 'Utility Coordination',
    location: 'Demo City, State',
    _count: {
      utilities: 12,
      conflicts: 3,
      tasks: 8,
    },
  } : null;

  if (isLoading && !isDemoMode) {
    return (
      <Shell>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-muted rounded w-full mb-2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </div>
      </Shell>
    );
  }

  if (error && !isDemoMode) {
    return (
      <Shell>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-1">Error Loading Project</h3>
                  <p className="text-muted-foreground mb-3">
                    Failed to load project data. Please try again later.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Shell>
    );
  }

  // Use demo project if in demo mode, otherwise use real project
  const displayProject = isDemoMode ? demoProject : project;

  if (!displayProject) {
    return (
      <Shell>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-1">Project Not Found</h3>
                  <p className="text-muted-foreground mb-3">
                    The project {projectId} could not be found.
                  </p>
                  <p className="text-sm text-muted-foreground mt-4">
                    Tip: Visit <Link href="/projects/demo" className="text-primary underline">/projects/demo</Link> to see the UI structure without data.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Shell>
    );
  }

  // Get status badge color
  let statusColor = 'bg-muted';
  if ((displayProject as any).rag_status?.toLowerCase().includes('green')) {
    statusColor = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
  } else if ((displayProject as any).rag_status?.toLowerCase().includes('yellow') || (displayProject as any).rag_status?.toLowerCase().includes('amber')) {
    statusColor = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
  } else if ((displayProject as any).rag_status?.toLowerCase().includes('red')) {
    statusColor = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
  }

  return (
    <Shell>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline">{displayProject.id}</Badge>
                    <Badge className={`px-2 ${statusColor}`} variant="secondary">
                      {(displayProject as any).rag_status}
                    </Badge>
                    {isDemoMode && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        Demo Mode
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-3">
                    <CardTitle className="text-2xl">{displayProject.name}</CardTitle>
                    <RealtimeIndicator showDetails />
                  </div>
                  <div className="flex items-center mt-2 text-sm text-muted-foreground">
                    <p>{(displayProject as any).client}</p>
                    {(displayProject as any).current_phase && (
                      <>
                        <span className="mx-2">•</span>
                        <p>{(displayProject as any).current_phase}</p>
                      </>
                    )}
                    {displayProject.start_date && (
                      <>
                        <span className="mx-2">•</span>
                        <p>
                          {formatDate(new Date(displayProject.start_date))} -
                          {displayProject.end_date && formatDate(new Date(displayProject.end_date))}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="grid w-full grid-cols-12">
                  {tabs.map((tab: any) => (
                    <TabsTrigger key={tab} value={tab} className="text-xs">
                      {tab}
                    </TabsTrigger>
                  ))}
                </TabsList>

                <TabsContent value="Details">
                  <EnhancedProjectDetailsCard projectId={projectId} project={displayProject} />
                </TabsContent>

                <TabsContent value="Real-time">
                  <ProjectRealtimeUpdates 
                    projectId={projectId} 
                    organizationId={(displayProject as any).organization_id}
                  />
                </TabsContent>

                <TabsContent value="Timeline">
                  <ProjectTimeline projectId={projectId} />
                </TabsContent>

                <TabsContent value="Gantt Chart">
                  <ProjectGanttContainer projectId={projectId} />
                </TabsContent>

                <TabsContent value="Task Management">
                  <TaskDataGrid projectId={projectId} />
                </TabsContent>

                <TabsContent value="Utility Stakeholders">
                  <div className="space-y-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <h2 className="text-2xl font-bold tracking-tight">Utility Stakeholders</h2>
                        <p className="text-muted-foreground">
                          Manage utility companies involved in this project across all phases
                        </p>
                      </div>
                    </div>
                    
                    {/* Grid of utility companies */}
                    <div className="grid gap-4">
                      {[
                        { name: 'Duke Energy', type: 'Electric', status: 'Active', phases: ['Design', 'Coordination', 'Construction'], contacted: true },
                        { name: 'Citizens Gas', type: 'Gas', status: 'Pending Contact', phases: ['Coordination'], contacted: false },
                        { name: 'AT&T', type: 'Telecommunications', status: 'Active', phases: ['Design', 'Coordination'], contacted: true },
                        { name: 'Comcast', type: 'Cable/Internet', status: 'Not Yet Coordinated', phases: [], contacted: false },
                        { name: 'Verizon', type: 'Telecommunications', status: 'Active', phases: ['Coordination', 'Construction'], contacted: true },
                        { name: 'City Water Department', type: 'Water', status: 'Active', phases: ['Design', 'Coordination', 'Construction'], contacted: true },
                      ].map((utility, i) => (
                        <Card key={i} className="hover:shadow-md transition-shadow">
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center gap-3">
                                <div className={`w-3 h-3 rounded-full ${
                                  utility.status === 'Active' ? 'bg-green-500' :
                                  utility.status === 'Pending Contact' ? 'bg-yellow-500' :
                                  'bg-gray-400'
                                }`} />
                                <div>
                                  <h3 className="font-medium">{utility.name}</h3>
                                  <p className="text-sm text-muted-foreground">{utility.type}</p>
                                </div>
                              </div>
                              <Badge variant={
                                utility.status === 'Active' ? 'default' :
                                utility.status === 'Pending Contact' ? 'secondary' :
                                'outline'
                              }>
                                {utility.status}
                              </Badge>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-muted-foreground">Project Phases:</span>
                                <span className="font-medium">
                                  {utility.phases.length > 0 ? utility.phases.join(', ') : 'None yet'}
                                </span>
                              </div>
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-muted-foreground">Contacted:</span>
                                <span className={utility.contacted ? 'text-green-600' : 'text-red-600'}>
                                  {utility.contacted ? 'Yes' : 'No'}
                                </span>
                              </div>
                            </div>
                            
                            <div className="flex gap-2 mt-4">
                              <Button size="sm" variant="outline">
                                View Details
                              </Button>
                              <Button size="sm" variant="outline">
                                Contact Log
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                    
                    <Card className="border-dashed">
                      <CardContent className="pt-6 text-center">
                        <p className="text-muted-foreground mb-4">
                          Add utility companies to track their involvement in project phases
                        </p>
                        <Button variant="outline">
                          <span className="mr-2">+</span>
                          Add Utility Company
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="Communications">
                  <CommunicationLog projectId={projectId} />
                </TabsContent>

                <TabsContent value="GIS Map">
                  <div className="space-y-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <h2 className="text-2xl font-bold tracking-tight">GIS Map</h2>
                        <p className="text-muted-foreground">
                          Live 2D and 3D mapping with utility visualization and conflict detection
                        </p>
                      </div>
                    </div>
                    <MapViewer projectId={projectId} height="600px" />
                  </div>
                </TabsContent>

                <TabsContent value="Utility Agreements">
                  <UtilityAgreementManagement projectId={projectId} />
                </TabsContent>

                <TabsContent value="Conflicts">
                  <ConflictMatrix projectId={projectId} />
                </TabsContent>

                <TabsContent value="Documents">
                  <EnhancedDocumentsManagement projectId={projectId} />
                </TabsContent>

                <TabsContent value="Notes">
                  <ProjectNotes projectId={projectId} projectName={displayProject.name} />
                </TabsContent>

                <TabsContent value="Workflow Template">
                  <WorkflowTemplateView projectId={projectId} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </Shell>
  );
}
