'use client';

import { useState, createElement, useEffect } from 'react';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Checkbox } from '~/components/ui/checkbox';
import { api } from '~/trpc/react';
import { useRealtimeProjects } from '~/hooks/use-realtime-events';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import {
  AlertTriangle,
  ArrowUpRight,
  Building2,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  Plus,
  Search,
  Target,
  Users,
  Download,
  Archive,
  Trash2,
  ChevronUp,
  ChevronDown,
  Printer,
  Grid3X3,
  List,
} from 'lucide-react';
import Link from 'next/link.js';
import { toast } from '~/hooks/use-toast';
import { DeleteConfirmationDialog } from '~/components/ui/delete-confirmation-dialog';
import { downloadCSV, downloadExcel, exportTableData } from '~/lib/export-utils';
import { PaginationControls } from '~/components/ui/pagination-controls';
import { EmptyState } from '~/components/ui/empty-state';
import { LoadingSkeleton, ProjectCardSkeleton } from '~/components/ui/loading-skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip';
import { Breadcrumb } from '~/components/ui/breadcrumb';
import { showSuccessToast, showErrorToast } from '~/lib/toast-messages';
import { ProjectPortfolioGrid } from '~/components/projects/project-portfolio-grid';
import { ToggleGroup, ToggleGroupItem } from '~/components/ui/toggle-group';
import { convertProjectDecimals } from '~/lib/decimal-converter';

export default function ProjectsPageWithBulk() {
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [clientFilter, setClientFilter] = useState('');
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [showBulkDelete, setShowBulkDelete] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'cards' | 'grid'>('grid');
  const [pageSize, setPageSize] = useState(20);
  const [sortBy, setSortBy] = useState<'name' | 'client' | 'updated_at' | 'rag_status'>('updated_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const { data: projectsData, isLoading, refetch } = api.projects.getAll.useQuery({
    limit: viewMode === 'grid' ? 1000 : pageSize, // Fetch all projects for grid view
    offset: viewMode === 'grid' ? 0 : (currentPage - 1) * pageSize,
    search: search || undefined,
    status: statusFilter || undefined,
    // client: clientFilter || undefined, // Property not in API
    sortBy: sortBy as "status" | "name" | "created_at" | "updated_at" | undefined,
    sortOrder,
  });

  // Convert decimal fields to strings for compatibility
  const processedProjects = projectsData?.projects?.map((project: any) => convertProjectDecimals(project)) || [];

  const { data: projectStats } = api.projects.getStats.useQuery();

  // Add real-time project updates
  useRealtimeProjects();

  // TODO: Implement bulk delete in API
  const bulkDeleteMutation = {
    mutate: () => console.log('Bulk delete not implemented'),
    isPending: false
  };

  // TODO: Implement bulk export in API
  const bulkExportQuery = {
    data: null,
    isLoading: false
  };

  // Handle bulk export success
  useEffect(() => {
    if (bulkExportQuery.data) {
      // Use the export utility to download the data
      const columns = [
        { key: 'id', label: 'Project ID' },
        { key: 'name', label: 'Project Name' },
        { key: 'client', label: 'Client' },
        { key: 'location', label: 'Location' },
        { key: 'status', label: 'Status' },
        { key: 'current_phase', label: 'Phase' },
        { key: 'priority', label: 'Priority' },
        { key: 'start_date', label: 'Start Date' },
        { key: 'end_date', label: 'End Date' },
        { key: 'project_manager', label: 'Project Manager' },
        { key: 'contract_amount', label: 'Contract Amount', format: (v: any) => v ? `$${v.toLocaleString()}` : '' },
        { key: 'utility_count', label: 'Utilities' },
        { key: 'conflict_count', label: 'Conflicts' },
        { key: 'task_count', label: 'Tasks' },
      ];
      
      exportTableData(bulkExportQuery.data, columns, 'projects-export', 'csv');
      
      showSuccessToast.exported('CSV');
    }
  }, [bulkExportQuery.data]);

  // Handle bulk export error
  useEffect(() => {
    if ((bulkExportQuery as any).error) {
      showErrorToast.generic((bulkExportQuery as any).error.message || 'Failed to export projects. Please try again.');
    }
  }, [(bulkExportQuery as any).error]);

  // TODO: Implement bulk archive in API
  const bulkArchiveMutation = {
    mutate: () => console.log('Bulk archive not implemented'),
    isPending: false
  };

  const handleSelectAll = () => {
    if (selectedProjects.length === projectsData?.projects.length) {
      setSelectedProjects([]);
    } else {
      setSelectedProjects(projectsData?.projects.map(p => String(p.id)) || []);
    }
  };

  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prev =>
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  };

  const handleBulkDelete = () => {
    bulkDeleteMutation.mutate();
  };

  const handleBulkExport = () => {
    console.log('Bulk export not implemented');
  };

  const handleBulkArchive = () => {
    bulkArchiveMutation.mutate();
  };

  const getStatusBadge = (ragStatus: string | null) => {
    const colorMap: Record<string, string> = {
      Green: 'bg-green-100 text-green-800 border-green-200',
      Red: 'bg-red-100 text-red-800 border-red-200',
      Amber: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      Complete: 'bg-blue-100 text-blue-800 border-blue-200',
    };
    return colorMap[ragStatus || ''] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getStatusIcon = (ragStatus: string | null) => {
    const iconMap: Record<string, React.ElementType> = {
      Green: CheckCircle,
      Red: AlertTriangle,
      Amber: Clock,
      Complete: CheckCircle,
    };
    const Icon = ragStatus && iconMap[ragStatus] ? iconMap[ragStatus] : Clock;
    return createElement(Icon, { className: "h-3 w-3" });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div className="space-y-2">
              <LoadingSkeleton variant="text" count={1} className="h-8 w-32" />
              <LoadingSkeleton variant="text" count={1} className="h-4 w-64" />
            </div>
            <LoadingSkeleton variant="custom" className="h-10 w-32 mt-4 sm:mt-0" />
          </div>

          {/* Stats Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <LoadingSkeleton variant="card" count={4} className="h-24" />
          </div>

          {/* Projects List Skeleton */}
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i: any) => (
              <ProjectCardSkeleton key={i} />
            ))}
          </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Breadcrumb */}
        <div className="mb-4">
          <Breadcrumb />
        </div>
        
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
              <RealtimeIndicator showDetails />
            </div>
            <p className="text-gray-600 mt-1">Manage and track utility coordination projects</p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            {/* View Mode Toggle */}
            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'cards' | 'grid')}>
              <ToggleGroupItem value="cards" aria-label="Card view">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="grid" aria-label="Grid view">
                <Grid3X3 className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.print()}
              className="print-button"
            >
              <Printer className="mr-2 h-4 w-4" />
              Print
            </Button>
            <Button asChild>
              <Link href="/projects/new">
                <Plus className="mr-2 h-4 w-4" />
                New Project
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Projects</p>
                  <p className="text-2xl font-bold text-blue-600">{projectStats?.totalProjects || 0}</p>
                </div>
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-green-600">{projectStats?.activeProjects || 0}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Red Status</p>
                  <p className="text-2xl font-bold text-red-600">{0}</p> {/* Red status not available in current stats */}
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-blue-600">{projectStats?.completedProjects || 0}</p>
                </div>
                <Target className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters & Bulk Actions */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search projects..."
                      value={search}
                      onChange={(e: any) => setSearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e: any) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Status</option>
                    <option value="Green">Green</option>
                    <option value="Amber">Amber</option>
                    <option value="Red">Red</option>
                    <option value="Complete">Complete</option>
                  </select>
                  <Input
                    placeholder="Filter by client..."
                    value={clientFilter}
                    onChange={(e: any) => setClientFilter(e.target.value)}
                    className="w-48"
                  />
                  <div className="flex items-center gap-2">
                    <select
                      value={sortBy}
                      onChange={(e: any) => setSortBy(e.target.value as typeof sortBy)}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="name">Name</option>
                      <option value="client">Client</option>
                      <option value="updated_at">Last Updated</option>
                      <option value="rag_status">Status</option>
                    </select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="px-3"
                    >
                      {sortOrder === 'asc' ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Bulk Actions Bar */}
              {selectedProjects.length > 0 && (
                <div className="flex items-center justify-between bg-blue-50 p-3 rounded-md">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-blue-700">
                      {selectedProjects.length} project{selectedProjects.length > 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkExport}
                      disabled={(bulkExportQuery as any).isFetching}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkArchive}
                      disabled={bulkArchiveMutation.isPending}
                    >
                      <Archive className="mr-2 h-4 w-4" />
                      Archive
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => setShowBulkDelete(true)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Conditionally render based on view mode */}
        {viewMode === 'grid' ? (
          /* Excel-like Grid View */
          <ProjectPortfolioGrid 
            projects={processedProjects} 
            onRefetch={() => void refetch()}
          />
        ) : (
          /* Card View */
          <div className="space-y-4">
            {/* Select All */}
            {projectsData?.projects && projectsData.projects.length > 0 && (
              <div className="flex items-center gap-2 px-4 py-2">
                <Checkbox
                  checked={selectedProjects.length === projectsData.projects.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm text-gray-600">Select all</span>
              </div>
            )}

            {projectsData?.projects.map((project: any) => (
            <Card
              key={project.id}
              className={`group hover:shadow-md transition-all duration-200 ${
                selectedProjects.includes(project.id) ? 'border-blue-500 bg-blue-50/50' : 'hover:border-blue-200'
              }`}
            >
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <Checkbox
                    checked={selectedProjects.includes(project.id)}
                    onCheckedChange={() => handleSelectProject(project.id)}
                    className="mt-1"
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-3">
                      <Link
                        href={`/projects/${project.id}`}
                        className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors"
                      >
                        {project.name}
                      </Link>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span
                              className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border ${getStatusBadge(project.rag_status)}`}
                            >
                              {getStatusIcon(project.rag_status)}
                              {project.rag_status || 'Active'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {project.rag_status === 'Green' && 'Project is on track with no major issues'}
                              {project.rag_status === 'Amber' && 'Project has some concerns that need attention'}
                              {project.rag_status === 'Red' && 'Project has critical issues requiring immediate action'}
                              {project.rag_status === 'Complete' && 'Project has been completed'}
                              {!project.rag_status && 'Project is currently active'}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      {project.project_priority && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">
                                {project.project_priority}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Project priority level</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <MapPin className="h-4 w-4" />
                        <div>
                          <p className="font-medium">{project.client}</p>
                          <p className="text-xs text-gray-500">Client</p>
                        </div>
                      </div>

                      {project.current_phase && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Clock className="h-4 w-4" />
                          <div>
                            <p className="font-medium">{project.current_phase}</p>
                            <p className="text-xs text-gray-500">Current Phase</p>
                          </div>
                        </div>
                      )}

                      {project.egis_project_manager && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <div>
                            <p className="font-medium">{project.egis_project_manager}</p>
                            <p className="text-xs text-gray-500">Project Manager</p>
                          </div>
                        </div>
                      )}

                      {project.updated_at && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <div>
                            <p className="font-medium">
                              {new Date(project.updated_at).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-gray-500">Last Updated</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {project.description && (
                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                        {project.description}
                      </p>
                    )}

                    {/* Project Stats */}
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <span className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {project.utilities?.length || 0} utilities
                      </span>
                      {(project.conflicts?.length || 0) > 0 && (
                        <span className="flex items-center gap-1 text-red-600">
                          <AlertTriangle className="h-4 w-4" />
                          {project.conflicts?.length || 0} conflicts
                        </span>
                      )}
                      {(project.tasks?.length || 0) > 0 && (
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {project.tasks?.length || 0} tasks
                        </span>
                      )}
                      {project.contract_amount && (
                        <span className="text-green-600 font-medium">
                          ${Number(project.contract_amount).toLocaleString()}
                        </span>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    asChild
                    className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Link href={`/projects/${project.id}`}>
                      <ArrowUpRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
            ))}

            {projectsData?.projects.length === 0 && (
              <Card>
                <CardContent className="py-12">
                  <EmptyState
                    icon={<Building2 className="h-12 w-12" />}
                    title="No projects found"
                    description={
                      search || statusFilter || clientFilter
                        ? 'Try adjusting your search or filters.'
                        : 'Get started by creating your first project.'
                    }
                    action={
                      !search && !statusFilter && !clientFilter && (
                        <Button asChild>
                          <Link href="/projects/new">
                            <Plus className="mr-2 h-4 w-4" />
                            Create Project
                          </Link>
                        </Button>
                      )
                    }
                  />
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Pagination - only show in card view */}
        {viewMode === 'cards' && projectsData && projectsData.total > 0 && (
          <div className="mt-8">
            <PaginationControls
              currentPage={currentPage}
              totalPages={Math.ceil(projectsData.total / pageSize)}
              pageSize={pageSize}
              totalItems={projectsData.total}
              onPageChange={(page) => {
                setCurrentPage(page);
                setSelectedProjects([]); // Clear selection when changing pages
              }}
              onPageSizeChange={(newPageSize) => {
                setPageSize(newPageSize);
                setCurrentPage(1); // Reset to first page when changing page size
                setSelectedProjects([]);
              }}
            />
          </div>
        )}

        {/* Bulk Delete Confirmation */}
        <DeleteConfirmationDialog
          open={showBulkDelete}
          onOpenChange={setShowBulkDelete}
          onConfirm={handleBulkDelete}
          title="Delete Selected Projects?"
          description={`Are you sure you want to delete ${selectedProjects.length} project${selectedProjects.length > 1 ? 's' : ''}? This action cannot be undone.`}
          confirmText={`Delete ${selectedProjects.length} Project${selectedProjects.length > 1 ? 's' : ''}`}
          isDeleting={bulkDeleteMutation.isPending}
        />
    </div>
  );
}