'use client';

import { useState, useEffect } from 'react';
import { Shell } from '~/components/layout/shell';
import ProjectsWithBulk from './projects-with-bulk';
import { LoadingOverlay } from '~/components/ui/loading-overlay';

interface ProjectsClientPageProps {
  // No initial data needed as ProjectsWithBulk handles its own data fetching
}

export default function ProjectsClientPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show loading state until client-side hydration is complete
  if (!mounted) {
    return (
      <Shell>
        <div className="container mx-auto px-4 py-6">
          <div className="relative min-h-[400px]">
            <LoadingOverlay message="Loading projects..." />
          </div>
        </div>
      </Shell>
    );
  }

  return (
    <Shell>
      <ProjectsWithBulk />
    </Shell>
  );
}