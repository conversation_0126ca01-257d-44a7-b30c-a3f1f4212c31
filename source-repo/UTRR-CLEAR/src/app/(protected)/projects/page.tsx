import { getServerAuthSession } from '~/server/auth';
import { api, HydrateClient } from '~/trpc/server';
import ProjectsClientPage from './client-page';

export default async function ProjectsPage() {
  const session = await getServerAuthSession();
  
  // Prefetch critical projects data for faster loading
  void api.projects.getAll.prefetch({ limit: 50 });
  void api.organizations.getCurrent.prefetch();
  
  return (
    <HydrateClient>
      <ProjectsClientPage />
    </HydrateClient>
  );
}