import { redirect } from 'next/navigation';
import { createServerClient } from '~/lib/supabase/server';
import HeaderWrapper from '~/components/layout/header-wrapper';
import { Breadcrumb } from '~/components/ui/breadcrumb';
import { ClientLayoutWrapper } from '~/components/layout/client-layout-wrapper';

// Force dynamic rendering for all protected routes
export const dynamic = 'force-dynamic';

export default async function ProtectedLayout({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  // Server-side auth check
  const supabase = await createServerClient();
  const { data: { session } } = await supabase.auth.getSession();

  // Redirect to signin if not authenticated
  if (!session) {
    redirect('/auth/signin');
  }

  return (
    <ClientLayoutWrapper>
      <div className="flex flex-col h-screen bg-background">
        <HeaderWrapper />
        <div className="flex flex-1 overflow-hidden">
          {/* Main Content Area */}
          <div className="flex flex-col flex-1 overflow-hidden">
            {/* Breadcrumb */}
            <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="container mx-auto px-4 py-4">
                <Breadcrumb />
              </div>
            </div>
            <main className="flex-1 overflow-y-auto">{children}</main>
          </div>
        </div>
      </div>
    </ClientLayoutWrapper>
  );
}