import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import MyProjectsClientPage from './client-page';

export default async function MyProjectsPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch critical data for faster loading
  void api.projects.getAll.prefetch({ limit: 50 });

  // Fetch initial projects data server-side
  const initialProjects = await api.projects.getAll({ limit: 10 });

  return (
    <HydrateClient>
      <MyProjectsClientPage initialProjects={initialProjects} />
    </HydrateClient>
  );
}