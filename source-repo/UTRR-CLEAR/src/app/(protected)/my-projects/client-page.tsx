'use client';

import { Shell } from '~/components/layout/shell';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import Link from 'next/link.js';
import { Folder<PERSON><PERSON>ban, ArrowUpRight } from 'lucide-react';
import { cn } from '~/lib/utils';
import { type RouterOutputs } from '~/trpc/react';

interface MyProjectsClientPageProps {
  initialProjects: RouterOutputs['projects']['getAll'];
}

export default function MyProjectsClientPage({ initialProjects }: MyProjectsClientPageProps) {
  const projects = initialProjects?.projects || [];

  return (
    <Shell>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold">My Projects</h1>
          <p className="text-muted-foreground">View and manage your assigned projects</p>
        </div>
        <div className="bg-card border border-border rounded-lg shadow-sm">
          {!projects || projects.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>My Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="flex flex-col items-center gap-3">
                    <FolderKanban className="w-12 h-12 text-muted-foreground opacity-60" />
                    <div>
                      <h3 className="text-lg font-semibold mb-1">No Active Projects</h3>
                      <p className="text-muted-foreground mb-3">You don&apos;t have any assigned projects</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>My Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {projects.map((project: any) => {
                    // Get status badge color
                    let statusColor = 'bg-muted';
                    const status = project.rag_status?.toLowerCase() || '';
                    if (status.includes('active')) {
                      statusColor = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
                    } else if (status.includes('pending')) {
                      statusColor = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
                    } else if (status.includes('complete')) {
                      statusColor = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
                    }

                    return (
                      <li key={project.id}>
                        <Link
                          href={`/projects/${project.id}`}
                          className="block group border rounded-lg p-3 hover:border-primary/40 hover:bg-muted/30 transition-colors cursor-pointer"
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="flex items-center gap-2 mb-1">
                                <Badge className="text-xs font-medium" variant="outline">
                                  {project.id}
                                </Badge>
                                <Badge
                                  className={cn('text-xs font-medium px-2', statusColor)}
                                  variant="secondary"
                                >
                                  {project.rag_status || 'Unknown'}
                                </Badge>
                              </div>
                              <h3 className="font-medium">{project.name}</h3>
                              <div className="text-sm text-muted-foreground mt-1">
                                <span className="inline-block">
                                  Phase: {project.current_phase || 'Not started'}
                                </span>
                                <span className="inline-block mx-2 text-primary/40">•</span>
                                <span className="inline-block">
                                  Updated: {project.updated_at?.toLocaleDateString() || 'Unknown'}
                                </span>
                              </div>
                            </div>
                            <ArrowUpRight className="h-5 w-5 text-primary/60 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </Shell>
  );
}