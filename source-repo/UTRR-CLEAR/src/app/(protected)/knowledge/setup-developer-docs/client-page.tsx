'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Code, 
  FileText, 
  Shield,
  ArrowLeft
} from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';
import Link from 'next/link.js';

const DEVELOPER_CATEGORY = {
  id: 'developer-documentation',
  name: 'Developer Documentation',
  description: 'Technical documentation, architecture diagrams, and developer resources'
};

const WHISPERS_ARTICLE = {
  title: 'myWhispers Security Architecture',
  description: 'Visual overview of the myWhispers private messaging system security architecture with interactive diagram',
  content: `# myWhispers Security Architecture

## Overview
This document provides a visual overview of the myWhispers private messaging system's security architecture, demonstrating how the system maintains enterprise-grade privacy while delivering a modern user experience.

## Security Architecture Diagram

\`\`\`mermaid
graph TB
    subgraph "Client Browser"
        A[User Interface<br/>shadcn-chat Components] --> B[WhispersChat Component]
        B --> C[localStorage<br/>5min TTL]
        C --> D[Cross-Tab Sync<br/>StorageEvent]
        D --> E[Auto-Cleanup<br/>Expired Messages]
    end
    
    subgraph "Server"
        F[tRPC API] --> G[User Activity Tracking<br/>Online Status Only]
        G --> H[Redis Cache<br/>Activity Data Only]
    end
    
    subgraph "Security Features"
        I["🛡️ Zero Server Storage"]
        J["⏰ 5min Auto-Expire"]
        K["🔒 Client-Side Only"]
        L["🚫 No Message Logging"]
    end
    
    B -.->|"Activity Updates Only<br/>(No Message Content)"| F
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#fff3e0
    style I fill:#e8f5e8
    style J fill:#fff8e1
    style K fill:#fce4ec
    style L fill:#f1f8e9
\`\`\`

## Key Security Components

### 🔒 **Client-Side Storage**
- **localStorage Only**: Messages never leave the user's browser
- **5-Minute TTL**: Automatic expiration prevents data accumulation
- **Cross-Tab Sync**: Real-time updates across browser tabs without server communication

### 🛡️ **Zero-Knowledge Server**
- **Activity Tracking Only**: Server tracks user online status, never message content
- **No Message Storage**: No database tables or logs contain private messages
- **Separation of Concerns**: User presence completely isolated from message data

### ⏰ **Ephemeral Messaging**
- **Auto-Expiration**: Messages automatically deleted after 5 minutes
- **Real-Time Countdown**: Users see time remaining for each message
- **Automatic Cleanup**: Background process removes expired messages every 30 seconds

### 🚫 **Privacy by Design**
- **No Server Logs**: Message content never appears in server logs
- **No Backups**: Messages cannot be recovered once expired
- **No Admin Access**: Even administrators cannot view message content

## Technical Implementation

### Message Storage
\`\`\`typescript
// Client-side only storage with TTL
const MESSAGE_TTL = 5 * 60 * 1000; // 5 minutes
const STORAGE_KEY = 'whispers_messages';

// Store message locally
localStorage.setItem(STORAGE_KEY, JSON.stringify(messages));
\`\`\`

### Cross-Tab Synchronization
\`\`\`typescript
// Sync across browser tabs without server
window.dispatchEvent(new StorageEvent('storage', {
  key: STORAGE_KEY,
  newValue: JSON.stringify(messages),
  storageArea: localStorage
}));
\`\`\`

### Activity Tracking (Server-Side)
\`\`\`typescript
// Only track user presence, never message content
const updateActivity = api.whispers.updateActivity.useMutation({
  // Updates Redis cache with user online status only
});
\`\`\`

## Security Benefits

1. **Zero-Knowledge Architecture**: Server never sees message content
2. **Forward Secrecy**: Messages automatically deleted after TTL
3. **No Data Breach Risk**: No server-side message storage to compromise
4. **Regulatory Compliance**: Meets strict privacy requirements
5. **User Control**: Messages exist only on user's device

## Use Cases

- **Sensitive Communications**: Internal discussions requiring privacy
- **Temporary Coordination**: Quick messages that don't need persistence
- **Compliance-Sensitive Environments**: Organizations with strict data retention policies
- **Real-Time Collaboration**: Immediate communication without permanent records

## Comparison with Traditional Chat

| Feature | myWhispers | Traditional Chat |
|---------|------------|------------------|
| Server Storage | ❌ None | ✅ Permanent |
| Message Persistence | ⏰ 5 minutes | ♾️ Forever |
| Admin Access | ❌ Impossible | ✅ Full access |
| Data Breach Risk | 🔒 Minimal | ⚠️ High |
| Compliance | ✅ Privacy-first | ⚠️ Depends |

## Conclusion

The myWhispers security architecture demonstrates that it's possible to provide modern, user-friendly chat functionality while maintaining the highest levels of privacy and security. By keeping message content exclusively on the client-side and implementing automatic expiration, the system eliminates many common security risks associated with traditional messaging platforms.`,
  tags: ['security', 'privacy', 'architecture', 'messaging', 'ephemeral', 'zero-knowledge', 'client-side', 'shadcn-chat', 'visual-diagram']
};

export default function SetupDeveloperDocsClientPage() {
  const [categoryCreated, setCategoryCreated] = useState(false);
  const [articleCreated, setArticleCreated] = useState(false);

  const { data: categories } = api.knowledge.getCategories.useQuery();

  const createCategoryMutation = api.knowledge.createCategory.useMutation({
    onSuccess: () => {
      setCategoryCreated(true);
      toast({
        title: 'Success',
        description: 'Developer Documentation category created successfully',
      });
    },
    onError: (error: any) => {
      if (error.message.includes('already exists')) {
        setCategoryCreated(true);
        toast({
          title: 'Info',
          description: 'Developer Documentation category already exists',
        });
      } else {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      }
    },
  });

  const createArticleMutation = api.knowledge.create.useMutation({
    onSuccess: () => {
      setArticleCreated(true);
      toast({
        title: 'Success',
        description: 'myWhispers Security Architecture article created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleCreateCategory = () => {
    createCategoryMutation.mutate(DEVELOPER_CATEGORY);
  };

  const handleCreateArticle = () => {
    createArticleMutation.mutate({
      title: WHISPERS_ARTICLE.title,
      description: WHISPERS_ARTICLE.description,
      content: WHISPERS_ARTICLE.content,
      category_id: DEVELOPER_CATEGORY.id,
      tags: WHISPERS_ARTICLE.tags,
    });
  };

  const developerCategoryExists = categories?.some((cat: any) => cat.id === DEVELOPER_CATEGORY.id);
  const setupComplete = (categoryCreated || developerCategoryExists) && articleCreated;

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/knowledge">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Knowledge Base
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Setup Developer Documentation</h1>
            <p className="text-muted-foreground">
              Add developer resources and visual diagrams to the knowledge base
            </p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Step 1: Create Category */}
          <Card className={categoryCreated || developerCategoryExists ? 'border-green-200 bg-green-50' : ''}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  <CardTitle>Step 1: Developer Category</CardTitle>
                </div>
                {(categoryCreated || developerCategoryExists) && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Complete
                  </Badge>
                )}
              </div>
              <CardDescription>
                Create the "Developer Documentation" category for technical content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Category Details:</p>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>Name:</strong> {DEVELOPER_CATEGORY.name}</li>
                  <li>• <strong>ID:</strong> {DEVELOPER_CATEGORY.id}</li>
                  <li>• <strong>Description:</strong> {DEVELOPER_CATEGORY.description}</li>
                </ul>
              </div>
              
              {developerCategoryExists ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Developer Documentation category already exists in the knowledge base.
                  </AlertDescription>
                </Alert>
              ) : (
                <Button 
                  onClick={handleCreateCategory}
                  disabled={createCategoryMutation.isPending || categoryCreated}
                  className="w-full"
                >
                  {createCategoryMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Category...
                    </>
                  ) : (
                    <>
                      <Code className="h-4 w-4 mr-2" />
                      Create Developer Category
                    </>
                  )}
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Step 2: Create Article */}
          <Card className={articleCreated ? 'border-green-200 bg-green-50' : ''}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  <CardTitle>Step 2: Security Architecture</CardTitle>
                </div>
                {articleCreated && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Complete
                  </Badge>
                )}
              </div>
              <CardDescription>
                Add the myWhispers Security Architecture article with visual diagram
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Article Details:</p>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>Title:</strong> {WHISPERS_ARTICLE.title}</li>
                  <li>• <strong>Category:</strong> Developer Documentation</li>
                  <li>• <strong>Content:</strong> Security architecture with Mermaid diagram</li>
                  <li>• <strong>Tags:</strong> {WHISPERS_ARTICLE.tags.slice(0, 3).join(', ')}...</li>
                </ul>
              </div>
              
              <Button 
                onClick={handleCreateArticle}
                disabled={createArticleMutation.isPending || articleCreated || (!categoryCreated && !developerCategoryExists)}
                className="w-full"
              >
                {createArticleMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Article...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Create Security Architecture Article
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Completion Status */}
        {setupComplete && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <CardTitle className="text-green-800">Setup Complete!</CardTitle>
              </div>
              <CardDescription>
                Developer documentation has been successfully added to the knowledge base
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm">What was created:</p>
                <ul className="text-sm space-y-1">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    Developer Documentation category
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    myWhispers Security Architecture article with visual diagram
                  </li>
                </ul>
              </div>
              
              <div className="flex gap-2">
                <Link href="/knowledge">
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    View Knowledge Base
                  </Button>
                </Link>
                <Link href="/knowledge?category=developer-documentation">
                  <Button variant="outline">
                    <Code className="h-4 w-4 mr-2" />
                    View Developer Docs
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>About This Setup</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm">This setup will add:</p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• A new "Developer Documentation" category for technical content</li>
                <li>• The myWhispers Security Architecture article with an interactive Mermaid diagram</li>
                <li>• Proper tagging for easy discovery (security, privacy, architecture, visual-diagram)</li>
                <li>• Technical implementation details and code examples</li>
              </ul>
            </div>
            
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                The security architecture diagram shows how myWhispers maintains enterprise-grade privacy 
                with client-side only storage, 5-minute auto-expiration, and zero-knowledge server design.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}