import { redirect } from 'next/navigation';
import { getServerAuthSession } from '~/server/auth';
import { api, HydrateClient } from '~/trpc/server';
import SetupDeveloperDocsClientPage from './client-page';

export default async function SetupDeveloperDocsPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  if (!session.user.role || session.user.role !== 'admin') {
    redirect('/dashboard');
  }

  // Pre-fetch knowledge base data
  void api.knowledge.getCategories.prefetch();
  
  return (
    <HydrateClient>
      <SetupDeveloperDocsClientPage />
    </HydrateClient>
  );
}