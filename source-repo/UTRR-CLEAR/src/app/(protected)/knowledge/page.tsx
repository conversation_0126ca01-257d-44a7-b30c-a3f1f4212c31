import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import KnowledgeClientPage from './client-page';

export default async function KnowledgePage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch knowledge data for faster loading
  void api.knowledge.getAll.prefetch({ search: '', category: undefined });
  void api.knowledge.getStats.prefetch();
  void api.knowledge.getCategories.prefetch();

  // Fetch initial data server-side
  const [initialArticles, initialStats, initialCategories] = await Promise.all([
    api.knowledge.getAll({ search: '', category: undefined }),
    api.knowledge.getStats(),
    api.knowledge.getCategories(),
  ]);

  return (
    <HydrateClient>
      <KnowledgeClientPage 
        initialArticles={initialArticles}
        initialStats={initialStats}
        initialCategories={initialCategories}
        isAdmin={session.user.isAdmin || false}
      />
    </HydrateClient>
  );
}