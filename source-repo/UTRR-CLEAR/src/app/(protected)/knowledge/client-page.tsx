'use client';

import { useAuth } from '~/hooks/use-auth';
import { redirect } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Shell } from '~/components/layout/shell';
import type { ChangeEvent } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  BookOpen,
  Download,
  Eye,
  FileText,
  Folder,
  Plus,
  Search,
  Star,
  Loader2,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';

interface KnowledgeClientPageProps {
  initialArticles: any;
  initialStats: any;
  initialCategories: any;
  isAdmin: boolean;
}

export default function KnowledgeClientPage({ 
  initialArticles, 
  initialStats, 
  initialCategories,
  isAdmin 
}: KnowledgeClientPageProps) {
  const { user } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedArticle, setSelectedArticle] = useState<number | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Form state for new article
  const [newArticle, setNewArticle] = useState({
    title: '',
    description: '',
    content: '',
    category_id: '',
    tags: '',
  });

  if (!user) {
    redirect('/auth/signin');
  }

  const { data: articlesData, isLoading: articlesLoading, refetch: refetchArticles } = api.knowledge.getAll.useQuery({
    search: searchTerm,
    category: categoryFilter === 'all' ? undefined : categoryFilter,
  }, {
    initialData: initialArticles,
  });

  const { data: knowledgeStats, isLoading: statsLoading } = api.knowledge.getStats.useQuery(undefined, {
    initialData: initialStats,
  });
  
  const { data: categories } = api.knowledge.getCategories.useQuery(undefined, {
    initialData: initialCategories,
  });

  const createArticleMutation = api.knowledge.create.useMutation({
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Article created successfully',
      });
      // Reset form
      setNewArticle({
        title: '',
        description: '',
        content: '',
        category_id: '',
        tags: '',
      });
      // Refetch articles
      void refetchArticles();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const articles = articlesData?.articles || [];

  const kpiCards = [
    {
      title: 'Total Articles',
      value: knowledgeStats?.totalArticles || 0,
      icon: BookOpen,
      description: `${knowledgeStats?.recentArticles || 0} added this month`,
    },
    {
      title: 'Total Views',
      value: knowledgeStats?.totalViews || 0,
      icon: Eye,
      description: 'Community engagement',
    },
    {
      title: 'Avg Rating',
      value: knowledgeStats?.avgRating || 0,
      icon: Star,
      description: 'User satisfaction',
    },
    {
      title: 'Downloads',
      value: knowledgeStats?.downloads || 0,
      icon: Download,
      description: 'Resource utilization',
    },
  ];

  const handleSubmitArticle = () => {
    if (!newArticle.title || !newArticle.content || !newArticle.category_id) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    createArticleMutation.mutate({
      title: newArticle.title,
      description: newArticle.description,
      content: newArticle.content,
      category_id: newArticle.category_id,
      tags: newArticle.tags ? newArticle.tags.split(',').map(t => t.trim()) : [],
    });
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (!mounted || articlesLoading || statsLoading) {
    return (
      <Shell>
        <div className="container py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </Shell>
    );
  }

  return (
    <Shell>
      <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Knowledge Base</h1>
            <p className="text-muted-foreground">
              Documentation, guides, and resources for utility coordination
            </p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export Articles
          </Button>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="browse" className="space-y-4">
          <TabsList>
            <TabsTrigger value="browse">Browse Articles</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="popular">Popular</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
            {isAdmin && (
              <TabsTrigger value="create">Create Article</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="browse" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Knowledge Base Articles</CardTitle>
                <CardDescription>
                  Search and browse documentation and guides
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filters */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search articles..."
                        value={searchTerm}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories?.map((category: any) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name} ({category._count.knowledge_articles})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Articles List */}
                <div className="space-y-4">
                  {articles.length > 0 ? (
                    articles.map((article: any) => (
                      <Card
                        key={article.id}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedArticle === article.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() =>
                          setSelectedArticle(article.id === selectedArticle ? null : article.id)
                        }
                      >
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <FileText className="h-5 w-5 text-blue-600" />
                              <div>
                                <CardTitle className="text-base">{article.title}</CardTitle>
                                <CardDescription className="mt-1">
                                  {article.description || 'No description available'}
                                </CardDescription>
                              </div>
                            </div>
                            <Badge variant="outline">
                              {article.knowledge_categories?.name || 'Uncategorized'}
                            </Badge>
                          </div>
                        </CardHeader>

                        {selectedArticle === article.id && (
                          <CardContent>
                            <div className="space-y-4">
                              <div className="prose max-w-none">
                                <p className="text-sm">{article.content}</p>
                              </div>

                              <div className="flex items-center justify-between text-xs text-muted-foreground pt-4 border-t">
                                <span>Created {formatDate(article.created_at)}</span>
                                <span>Last updated {formatDate(article.last_updated)}</span>
                              </div>

                              {article.tags && Array.isArray(article.tags) && article.tags.length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                  {article.tags.map((tag: any, index: number) => (
                                    <Badge key={index} variant="secondary" className="text-xs">
                                      {String(tag)}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          </CardContent>
                        )}
                      </Card>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No articles found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {categories?.map((category: any) => (
                <Card 
                  key={category.id}
                  className="cursor-pointer hover:shadow-md transition-all"
                  onClick={() => setCategoryFilter(category.id)}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Folder className="h-5 w-5 text-blue-600" />
                        <div>
                          <CardTitle className="text-base">{category.name}</CardTitle>
                          <CardDescription>{category.description || 'No description'}</CardDescription>
                        </div>
                      </div>
                      <Badge>{category._count.knowledge_articles} articles</Badge>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="popular" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Popular Articles</CardTitle>
                <CardDescription>Most viewed and highly rated content</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Popular articles feature coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recent" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Articles</CardTitle>
                <CardDescription>Latest additions to the knowledge base</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {articles.slice(0, 5).map((article: any) => (
                    <div key={article.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">{article.title}</p>
                        <p className="text-xs text-muted-foreground">
                          {article.knowledge_categories?.name} • {formatDate(article.created_at)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {isAdmin && (
            <TabsContent value="create" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Create New Article</CardTitle>
                  <CardDescription>Add content to the knowledge base</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={newArticle.category_id}
                      onValueChange={(value) =>
                        setNewArticle({ ...newArticle, category_id: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories?.map((category: any) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      placeholder="Article title"
                      value={newArticle.title}
                      onChange={(e: any) => setNewArticle({ ...newArticle, title: e.target.value })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      placeholder="Brief description (optional)"
                      value={newArticle.description}
                      onChange={(e: any) =>
                        setNewArticle({ ...newArticle, description: e.target.value })
                      }
                    />
                  </div>

                  <div>
                    <Label htmlFor="content">Content</Label>
                    <Textarea
                      id="content"
                      placeholder="Article content..."
                      rows={10}
                      value={newArticle.content}
                      onChange={(e: any) =>
                        setNewArticle({ ...newArticle, content: e.target.value })
                      }
                    />
                  </div>

                  <div>
                    <Label htmlFor="tags">Tags (comma-separated)</Label>
                    <Input
                      id="tags"
                      placeholder="e.g., utility, mapping, best-practices"
                      value={newArticle.tags}
                      onChange={(e: any) => setNewArticle({ ...newArticle, tags: e.target.value })}
                    />
                  </div>

                  <Button
                    className="w-full"
                    onClick={handleSubmitArticle}
                    disabled={createArticleMutation.isPending}
                  >
                    {createArticleMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Article
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
      </div>
    </Shell>
  );
}