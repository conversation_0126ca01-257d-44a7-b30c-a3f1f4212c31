'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Map, Layers3 } from 'lucide-react';
import { MapWrapper } from '~/components/gis/map-wrapper';

interface GISTestClientPageProps {
  testProjectId?: string;
}

export default function GISTestClientPage({ testProjectId = 'test-project-001' }: GISTestClientPageProps) {
  const [view, setView] = useState<'2d' | '3d'>('2d');

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>GIS Mapping Test</CardTitle>
              <CardDescription>
                Test the 2D utility drawing and 3D conflict visualization
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant={view === '2d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setView('2d')}
              >
                <Map className="mr-2 h-4 w-4" />
                2D Map
              </Button>
              <Button
                variant={view === '3d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setView('3d')}
              >
                <Layers3 className="mr-2 h-4 w-4" />
                3D View
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <MapWrapper projectId={testProjectId} view={view} />
        </CardContent>
      </Card>
    </div>
  );
}