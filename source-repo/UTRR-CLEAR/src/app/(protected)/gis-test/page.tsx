import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import GISTestClientPage from './client-page';

export default async function GISTestPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch GIS test data for faster loading
  void api.projects.getActiveProjects.prefetch();
  void api.utilities.getAll.prefetch({ limit: 50 });

  // Could fetch project data here if needed
  const testProjectId = 'test-project-001';

  return (
    <HydrateClient>
      <GISTestClientPage testProjectId={testProjectId} />
    </HydrateClient>
  );
}