'use client';

import * as React from 'react';
import { useAuth } from '~/hooks/use-auth';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Shell } from '~/components/layout/shell';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Button } from '~/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Loader2, AlertCircle, Key, Lock } from 'lucide-react';
import { toast } from '~/hooks/use-toast';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '~/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { api } from '~/trpc/react';

// Schema for password change form
const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

type PasswordChangeFormValues = z.infer<typeof passwordChangeSchema>;

// Password Change Form Component
function PasswordChangeForm() {
  const { user: currentUser } = useAuth();
  const [showSuccessMessage, setShowSuccessMessage] = React.useState(false);
  
  const form = useForm({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });
  
  const changePasswordMutation = api.users.changePassword.useMutation({
    onSuccess: () => {
      form.reset();
      setShowSuccessMessage(true);
      
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 5000);
      
      toast({
        title: 'Password updated',
        description: 'Your password has been successfully changed.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update password',
        variant: 'destructive',
      });
    },
  });
  
  const onSubmit = (values: PasswordChangeFormValues) => {
    if (!currentUser?.id) return;
    changePasswordMutation.mutate({
      userId: currentUser.id,
      currentPassword: values.currentPassword,
      newPassword: values.newPassword,
    });
  };
  
  return (
    <div className="space-y-6">
      {showSuccessMessage && (
        <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <AlertCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle className="text-green-800 dark:text-green-200">Success</AlertTitle>
          <AlertDescription className="text-green-700 dark:text-green-300">
            Your password has been successfully updated!
          </AlertDescription>
        </Alert>
      )}
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Change Password
          </CardTitle>
          <CardDescription>
            Update your password to keep your account secure
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button 
                type="submit" 
                disabled={changePasswordMutation.isPending}
                className="w-full"
              >
                {changePasswordMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Changing Password...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-4 w-4" />
                    Change Password
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ProfileClientPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  // Fetch user profile data
  const { data: profile, isLoading: profileLoading, refetch } = api.users.getMyProfile.useQuery();
  
  const currentSession = user;
  
  if (loading || profileLoading) {
    return (
      <Shell>
        <div className="container max-w-4xl mx-auto py-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-gray-100"></div>
        </div>
      </Shell>
    );
  }

  if (!user) {
    router.push('/auth/signin');
    return null;
  }

  return (
    <Shell>
      <div className="container max-w-4xl mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Profile Settings</h1>
            <p className="text-muted-foreground">
              Manage your account settings and preferences.
            </p>
          </div>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader className="text-center">
                <CardTitle>My Profile</CardTitle>
                <CardDescription>View your profile information</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                <div className="relative mb-4">
                  <Avatar className="h-32 w-32 border-4 border-muted">
                    {user?.user_metadata?.avatar_url ? (
                      <AvatarImage 
                        src={user?.user_metadata?.avatar_url} 
                        alt={user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User'} 
                      />
                    ) : null}
                    <AvatarFallback className="text-4xl bg-primary text-primary-foreground">
                      {user?.user_metadata?.full_name || user?.email?.split('@')[0]?.[0]?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <div className="text-center mb-4">
                  <h3 className="text-lg font-medium">{profile?.firstName} {profile?.lastName}</h3>
                  <p className="text-sm text-muted-foreground">{profile?.role}</p>
                  <p className="text-sm text-muted-foreground mt-1">{profile?.email}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>Your personal information and settings (admin controls weekly hours target)</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label>First Name</Label>
                      <Input value={profile?.firstName || ''} disabled />
                    </div>
                    <div>
                      <Label>Last Name</Label>
                      <Input value={profile?.lastName || ''} disabled />
                    </div>
                  </div>
                  <div>
                    <Label>Email</Label>
                    <Input value={profile?.email || ''} disabled />
                  </div>
                  <div>
                    <Label>Role</Label>
                    <Input value={profile?.role || ''} disabled />
                  </div>
                  <div>
                    <Label>Unit Preference</Label>
                    <Input value={profile?.unit_preference === 'metric' ? 'Metric (m, cm, km)' : 'Imperial (ft, in, mi)'} disabled />
                  </div>
                  <div>
                    <Label>Weekly Hours Target</Label>
                    <Input 
                      value={`${(profile?.custom_settings as any)?.weeklyHoursTarget || 40} hours/week`} 
                      disabled 
                      className="text-muted-foreground"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mt-4">
                    To update your preferences, use the <a href="/settings" className="underline">Settings</a> page.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <PasswordChangeForm />
          </TabsContent>
        </Tabs>
      </div>
    </Shell>
  );
}