import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import ProfileClientPage from './client-page';

export default async function ProfilePage() {
  const session = await getServerAuthSession();
  
  // Prefetch user profile data for faster loading
  if (session?.user) {
    void api.timesheet.getWeeklyEntries.prefetch({ 
      weekStart: new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toISOString()
    });
  }
  
  return (
    <HydrateClient>
      <ProfileClientPage />
    </HydrateClient>
  );
}