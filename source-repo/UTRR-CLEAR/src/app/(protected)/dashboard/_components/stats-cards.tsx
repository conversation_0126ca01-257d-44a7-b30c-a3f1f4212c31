'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '~/components/ui/card';
import { api } from '~/trpc/react';
import {
  AlertTriangle,
  Building2,
  CheckCircle,
  Clock,
  Target,
  TrendingUp,
  Zap,
} from 'lucide-react';

export function StatsCards() {
  const { data: stats, isLoading: statsLoading } = api.dashboard.getOverviewStats.useQuery();

  if (statsLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i: any) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
              <div className="h-4 w-4 bg-gray-300 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-300 rounded mb-1"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Projects',
      value: stats?.projects.total || 0,
      icon: Building2,
      description: 'All projects in system',
      className: 'text-blue-600',
    },
    {
      title: 'Red Status',
      value: stats?.projects.red || 0,
      icon: AlertTriangle,
      description: 'Critical attention needed',
      className: 'text-red-600',
    },
    {
      title: 'Active Utilities',
      value: stats?.utilities.active || 0,
      icon: Zap,
      description: 'Currently being coordinated',
      className: 'text-orange-600',
    },
    {
      title: 'Open Conflicts',
      value: stats?.conflicts.open || 0,
      icon: Target,
      description: 'Unresolved conflicts',
      className: 'text-purple-600',
    },
    {
      title: 'Pending Tasks',
      value: stats?.tasks.pending || 0,
      icon: Clock,
      description: 'Awaiting completion',
      className: 'text-yellow-600',
    },
    {
      title: 'High Priority',
      value: stats?.conflicts.highPriority || 0,
      icon: TrendingUp,
      description: 'Critical conflicts',
      className: 'text-red-500',
    },
    {
      title: 'Green Projects',
      value: stats?.projects.green || 0,
      icon: CheckCircle,
      description: 'On track',
      className: 'text-green-600',
    },
    {
      title: 'Overdue Tasks',
      value: stats?.tasks.overdue || 0,
      icon: AlertTriangle,
      description: 'Past due date',
      className: 'text-red-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat: any) => (
        <Card key={stat.title} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.className}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${stat.className}`}>{stat.value}</div>
            <p className="text-xs text-muted-foreground mt-1">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
