'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { api } from '~/trpc/react';
import {
  AlertTriangle,
  ArrowUpRight,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  Users,
} from 'lucide-react';
import Link from 'next/link.js';

export function RecentProjects() {
  const { data: recentProjects, isLoading } = api.dashboard.getRecentProjects.useQuery({
    limit: 5,
  });

  const getStatusBadge = (ragStatus: string | null) => {
    const colorMap: Record<string, string> = {
      Green: 'bg-green-100 text-green-800 border-green-200',
      Red: 'bg-red-100 text-red-800 border-red-200',
      Amber: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      Complete: 'bg-blue-100 text-blue-800 border-blue-200',
    };
    return colorMap[ragStatus || ''] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getStatusIcon = (ragStatus: string | null) => {
    const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
      Green: CheckCircle,
      Red: AlertTriangle,
      Amber: Clock,
      Complete: CheckCircle,
    };
    const Icon = ragStatus && iconMap[ragStatus] ? iconMap[ragStatus] : Clock;
    return <Icon className="h-3 w-3" />;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Projects</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i: any) => (
              <div key={i} className="animate-pulse">
                <div className="h-20 bg-gray-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Projects</CardTitle>
        <Button variant="outline" size="sm" asChild>
          <Link href="/projects">
            View All
            <ArrowUpRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentProjects?.map((project: any) => (
            <div
              key={project.id}
              className="group border rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:border-blue-200"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                      {project.name}
                    </h4>
                    <span
                      className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border ${getStatusBadge(project.rag_status)}`}
                    >
                      {getStatusIcon(project.rag_status)}
                      {project.rag_status || 'Active'}
                    </span>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {project.client}
                    </span>
                    {project.current_phase && <span>• {project.current_phase}</span>}
                    {project.updated_at && (
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(project.updated_at).toLocaleDateString()}
                      </span>
                    )}
                  </div>

                  {project.description && (
                    <p className="text-sm text-gray-500 line-clamp-2 mb-3">{project.description}</p>
                  )}

                  {/* Project Stats */}
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {project._count.utilities} utilities
                    </span>
                    {project._count.conflicts > 0 && (
                      <span className="flex items-center gap-1 text-red-600">
                        <AlertTriangle className="h-3 w-3" />
                        {project._count.conflicts} conflicts
                      </span>
                    )}
                    {project._count.tasks > 0 && (
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {project._count.tasks} tasks
                      </span>
                    )}
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  asChild
                  className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Link href={`/projects/${project.id}`}>View</Link>
                </Button>
              </div>
            </div>
          ))}

          {(!recentProjects || recentProjects.length === 0) && (
            <div className="text-center py-12 text-gray-500">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <MapPin className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="font-medium text-gray-900 mb-1">No projects yet</h3>
              <p className="text-sm">
                Create your first project to get started with utility coordination.
              </p>
              <Button asChild className="mt-4">
                <Link href="/projects/new">Create Project</Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
