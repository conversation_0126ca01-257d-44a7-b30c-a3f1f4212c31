import { Suspense } from 'react';
import { Shell } from "~/components/layout/shell";
import { 
  TimesheetSummary, 
  TasksList,
  MeetingsList,
  MyProjectsList
} from "~/components/dashboard/dashboard-widgets-dynamic";
import { RefreshButton } from "~/components/dashboard/refresh-button";
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';

export default async function OptimizedDashboardPage() {
  const session = await getServerAuthSession();
  
  // Prefetch critical data on the server
  void api.dashboard.getStats.prefetch();
  
  return (
    <Shell>
      <HydrateClient>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold">Team Dashboard</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Last updated:</span>
              <span className="text-sm font-medium">
                {new Date().toLocaleString('en-US', { 
                  hour: 'numeric', 
                  minute: '2-digit',
                  hour12: true,
                  month: 'short',
                  day: 'numeric'
                })}
              </span>
              <RefreshButton />
            </div>
          </div>

          {/* Critical widgets load immediately */}

          <div className="mb-6">
            <TimesheetSummary />
          </div>

          {/* Non-critical widgets load lazily */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="bg-card border border-border rounded-lg shadow-sm">
              <Suspense>
                <TasksList />
              </Suspense>
            </div>
            <div className="bg-card border border-border rounded-lg shadow-sm">
              <Suspense>
                <MeetingsList />
              </Suspense>
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-lg shadow-sm">
            <Suspense>
              <MyProjectsList />
            </Suspense>
          </div>
        </div>
      </HydrateClient>
    </Shell>
  );
}