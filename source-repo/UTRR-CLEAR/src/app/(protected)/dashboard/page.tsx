import { Suspense } from 'react';
import { Shell } from "~/components/layout/shell";
import { TimesheetSummary } from "~/components/dashboard/timesheet-summary";
import { TeamChat } from "~/components/dashboard/team-chat";
import { TasksList } from "~/components/dashboard/tasks-list";
import { MeetingsList } from "~/components/dashboard/meetings-list";
import { MyProjectsList } from "~/components/dashboard/my-projects-list";
import { RefreshButton } from "~/components/dashboard/refresh-button";
import { RealtimeIndicator } from "~/components/realtime/realtime-indicator";
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { Skeleton } from '~/components/ui/skeleton';

export default async function DashboardPage() {
  const session = await getServerAuthSession();
  
  // Prefetch critical data on the server for faster loading
  void api.chat.getMessages.prefetch({ limit: 50 });
  void api.timesheet.getWeeklyEntries.prefetch({ 
    weekStart: new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toISOString()
  });
  void api.tasks.getMyTasks.prefetch({ includeCompleted: false, limit: 50 });
  void api.meetings.getUpcoming.prefetch({ limit: 5, daysAhead: 7 });
  void api.projects.getActiveProjects.prefetch();
  
  return (
    <Shell>
      <HydrateClient>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-semibold">Team Dashboard</h2>
              <RealtimeIndicator showDetails />
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Last updated:</span>
              <span className="text-sm font-medium">
                {new Date().toLocaleString('en-US', { 
                  hour: 'numeric', 
                  minute: '2-digit',
                  hour12: true,
                  month: 'short',
                  day: 'numeric'
                })}
              </span>
              <RefreshButton />
            </div>
          </div>

          {/* myTeam Section (Team Chat with shadcn-chat components) */}
          <div className="mb-6">
            <Suspense fallback={
              <Card className="h-[600px]">
                <CardHeader><Skeleton className="h-6 w-32" /></CardHeader>
                <CardContent className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex space-x-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-4 w-full max-w-xs" />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            }>
              <TeamChat />
            </Suspense>
          </div>

          {/* myTime Section (formerly Weekly Timesheet) */}
          <div className="mb-6">
            <Suspense fallback={
              <Card>
                <CardHeader><Skeleton className="h-6 w-40" /></CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="text-center">
                        <Skeleton className="h-4 w-16 mx-auto mb-2" />
                        <Skeleton className="h-8 w-12 mx-auto" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            }>
              <TimesheetSummary />
            </Suspense>
          </div>

          {/* myTasks and myMeetings Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <Suspense fallback={
              <Card>
                <CardHeader><Skeleton className="h-6 w-32" /></CardHeader>
                <CardContent className="space-y-3">
                  {[...Array(4)].map((_, j) => (
                    <div key={j} className="flex items-center space-x-3">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 flex-1" />
                    </div>
                  ))}
                </CardContent>
              </Card>
            }>
              <TasksList />
            </Suspense>
            
            <Suspense fallback={
              <Card>
                <CardHeader><Skeleton className="h-6 w-32" /></CardHeader>
                <CardContent className="space-y-3">
                  {[...Array(4)].map((_, j) => (
                    <div key={j} className="flex items-center space-x-3">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 flex-1" />
                    </div>
                  ))}
                </CardContent>
              </Card>
            }>
              <MeetingsList />
            </Suspense>
          </div>
          
          {/* myProjects Section */}
          <Suspense fallback={
            <Card>
              <CardHeader><Skeleton className="h-6 w-36" /></CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="p-4 border rounded-lg">
                      <Skeleton className="h-5 w-32 mb-2" />
                      <Skeleton className="h-4 w-24 mb-2" />
                      <Skeleton className="h-3 w-full" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          }>
            <MyProjectsList />
          </Suspense>
        </div>
      </HydrateClient>
    </Shell>
  );
}