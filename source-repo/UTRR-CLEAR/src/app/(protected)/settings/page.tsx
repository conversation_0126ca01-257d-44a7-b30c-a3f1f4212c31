import { redirect } from 'next/navigation';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import { api, HydrateClient } from '~/trpc/server';
import SettingsClientPage from './client-page';

export default async function SettingsPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;
  
  if (!session?.user) {
    redirect('/auth/signin');
  }
  
  // Prefetch user settings data for faster loading
  
  return (
    <HydrateClient>
      <SettingsClientPage session={session as SupabaseSession} />
    </HydrateClient>
  );
}