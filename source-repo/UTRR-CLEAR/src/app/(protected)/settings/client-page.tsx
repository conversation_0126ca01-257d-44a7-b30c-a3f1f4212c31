'use client';

import * as React from 'react';
import { Shell } from '~/components/layout/shell';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Button } from '~/components/ui/button';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Input } from '~/components/ui/input';
import { Loader2, Save } from 'lucide-react';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';

interface SettingsClientPageProps {
  session: SupabaseSession | null;
}

export default function SettingsClientPage({ session }: SettingsClientPageProps) {
  // Fetch user profile and preferences
  const { data: profile, isLoading: profileLoading } = api.users.getMyProfile.useQuery();
  
  // Local state for settings
  const [unitPreference, setUnitPreference] = React.useState<'imperial' | 'metric'>('imperial');
  const [weeklyHoursTarget, setWeeklyHoursTarget] = React.useState(40);
  const [emailNotifications, setEmailNotifications] = React.useState(true);
  const [pushNotifications, setPushNotifications] = React.useState(true);
  const [autoSave, setAutoSave] = React.useState(true);
  
  // Update preferences mutation
  const updatePreferences = api.users.updateMyPreferences.useMutation({
    onSuccess: () => {
      toast({
        title: 'Settings saved',
        description: 'Your preferences have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save settings',
        variant: 'destructive',
      });
    },
  });
  
  // Initialize state with profile data
  React.useEffect(() => {
    if (profile) {
      setUnitPreference(profile.unit_preference as 'imperial' | 'metric');
      setWeeklyHoursTarget((profile.custom_settings as any)?.weeklyHoursTarget || 40);
      setEmailNotifications((profile.ui_preferences as any)?.emailNotifications ?? true);
      setPushNotifications((profile.ui_preferences as any)?.pushNotifications ?? true);
      setAutoSave((profile.ui_preferences as any)?.autoSave ?? true);
    }
  }, [profile]);
  
  const handleSaveGeneralSettings = () => {
    updatePreferences.mutate({
      unitPreference,
      weeklyHoursTarget,
      uiPreferences: {
        autoSave,
      },
    });
  };
  
  const handleSaveNotificationSettings = () => {
    updatePreferences.mutate({
      uiPreferences: {
        emailNotifications,
        pushNotifications,
      },
    });
  };
  
  if (profileLoading) {
    return (
      <Shell>
        <div className="container max-w-5xl mx-auto py-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-gray-100"></div>
        </div>
      </Shell>
    );
  }

  return (
    <Shell>
      <div className="container max-w-5xl mx-auto py-6">
        <div className="flex flex-col space-y-8">
          <div>
            <h1 className="text-2xl font-bold">Settings</h1>
            <p className="text-muted-foreground">
              Manage your account settings and preferences
            </p>
          </div>
          
          <Tabs defaultValue="general">
            <div className="flex flex-col md:flex-row gap-6">
              <div className="md:w-1/4">
                <TabsList className="flex flex-col h-auto w-full">
                  <TabsTrigger 
                    value="general" 
                    className="justify-start w-full"
                  >
                    General
                  </TabsTrigger>
                  <TabsTrigger 
                    value="notifications" 
                    className="justify-start w-full"
                  >
                    Notifications
                  </TabsTrigger>
                  <TabsTrigger 
                    value="appearance" 
                    className="justify-start w-full"
                  >
                    Appearance
                  </TabsTrigger>
                  <TabsTrigger 
                    value="advanced" 
                    className="justify-start w-full"
                  >
                    Advanced
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <div className="md:w-3/4">
                <TabsContent value="general" className="m-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>General Settings</CardTitle>
                      <CardDescription>
                        Manage your general preferences and account settings
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-6">
                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <Label htmlFor="unit-preference">Unit Preference</Label>
                            <Select value={unitPreference} onValueChange={(value: 'imperial' | 'metric') => setUnitPreference(value)}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="imperial">Imperial (ft, in, mi)</SelectItem>
                                <SelectItem value="metric">Metric (m, cm, km)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="weekly-hours">Weekly Hours Target</Label>
                            <Input
                              id="weekly-hours"
                              type="number"
                              min="1"
                              max="80"
                              value={weeklyHoursTarget}
                              onChange={(e) => setWeeklyHoursTarget(parseInt(e.target.value) || 40)}
                            />
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="auto-save" className="font-medium">
                              Auto-save
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              Automatically save changes as you work
                            </p>
                          </div>
                          <Switch
                            id="auto-save"
                            checked={autoSave}
                            onCheckedChange={setAutoSave}
                          />
                        </div>
                        
                        <div className="border-t pt-6">
                          <Button 
                            onClick={handleSaveGeneralSettings}
                            disabled={updatePreferences.isPending}
                          >
                            {updatePreferences.isPending ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Settings
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="notifications" className="m-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>Notification Preferences</CardTitle>
                      <CardDescription>
                        Control how and when you receive notifications
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="email-notifications" className="font-medium">
                              Email Notifications
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              Receive notifications via email
                            </p>
                          </div>
                          <Switch
                            id="email-notifications"
                            checked={emailNotifications}
                            onCheckedChange={setEmailNotifications}
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="push-notifications" className="font-medium">
                              Push Notifications
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              Receive push notifications in your browser
                            </p>
                          </div>
                          <Switch
                            id="push-notifications"
                            checked={pushNotifications}
                            onCheckedChange={setPushNotifications}
                          />
                        </div>
                        
                        <div className="border-t pt-6">
                          <Button 
                            onClick={handleSaveNotificationSettings}
                            disabled={updatePreferences.isPending}
                          >
                            {updatePreferences.isPending ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Preferences
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="appearance" className="m-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>Appearance</CardTitle>
                      <CardDescription>
                        Customize the look and feel of the application
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">Theme</h3>
                            <p className="text-sm text-muted-foreground">
                              Select between light, dark, or system theme
                            </p>
                          </div>
                        </div>
                        
                        <div className="border-t pt-6">
                          <p className="text-sm text-muted-foreground mb-4">
                            Appearance customization will be available in future updates.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="advanced" className="m-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>Advanced Settings</CardTitle>
                      <CardDescription>
                        Configure advanced application preferences
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">Data Management</h3>
                            <p className="text-sm text-muted-foreground">
                              Export or delete your account data
                            </p>
                          </div>
                        </div>
                        
                        <div className="border-t pt-6">
                          <p className="text-sm text-muted-foreground mb-4">
                            Advanced settings will be available in future updates.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </div>
          </Tabs>
        </div>
      </div>
    </Shell>
  );
}