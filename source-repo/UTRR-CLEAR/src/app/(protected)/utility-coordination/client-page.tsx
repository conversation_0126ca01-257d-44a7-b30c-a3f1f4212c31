'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Mail,
  MapPin,
  MessageSquare,
  Phone,
  Plus,
  Search,
  Users,
  Zap,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';

export default function UtilityCoordinationClientPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  // Utility coordination data fetching - implement when utilities router is available
  const coordinationActivities = null; // api.utilities.getCoordinationActivities.useQuery when implemented
  const utilityCompanies = null; // api.utilities.getCompanies.useQuery when implemented
  const coordinationStats = null; // api.utilities.getCoordinationStats.useQuery when implemented

  const mockActivities = coordinationActivities || [
    {
      id: '1',
      title: 'Duke Energy Transmission Line Relocation',
      project: 'I-65 Widening Project',
      utility_company: 'Duke Energy',
      type: 'relocation',
      status: 'in_progress',
      priority: 'high',
      coordinator: 'Mike Davis',
      start_date: '2024-01-15T00:00:00Z',
      target_date: '2024-03-15T00:00:00Z',
      progress: 65,
      estimated_cost: 250000,
      description: 'Relocation of 69kV transmission line to accommodate highway widening',
      stakeholders: 5,
      meetings_count: 3,
      documents_count: 12,
    },
    {
      id: '2',
      title: 'Citizens Gas Main Protection',
      project: 'Downtown Bridge Replacement',
      utility_company: 'Citizens Energy Group',
      type: 'protection',
      status: 'planning',
      priority: 'medium',
      coordinator: 'Sarah Johnson',
      start_date: '2024-02-01T00:00:00Z',
      target_date: '2024-04-30T00:00:00Z',
      progress: 25,
      estimated_cost: 85000,
      description: 'Protection and monitoring of 24-inch gas main during bridge construction',
      stakeholders: 3,
      meetings_count: 1,
      documents_count: 6,
    },
    {
      id: '3',
      title: 'AT&T Fiber Optic Coordination',
      project: 'Airport Terminal Expansion',
      utility_company: 'AT&T',
      type: 'coordination',
      status: 'completed',
      priority: 'low',
      coordinator: 'Tom Brown',
      start_date: '2023-11-01T00:00:00Z',
      target_date: '2024-01-31T00:00:00Z',
      progress: 100,
      estimated_cost: 45000,
      description: 'Coordination for fiber optic infrastructure during terminal expansion',
      stakeholders: 4,
      meetings_count: 6,
      documents_count: 18,
    },
    {
      id: '4',
      title: 'Water Main Relocation Assessment',
      project: 'Carmel Utility Underground',
      utility_company: 'Citizens Energy Group',
      type: 'assessment',
      status: 'on_hold',
      priority: 'medium',
      coordinator: 'Lisa Wilson',
      start_date: '2024-01-20T00:00:00Z',
      target_date: '2024-05-15T00:00:00Z',
      progress: 40,
      estimated_cost: 120000,
      description: 'Assessment and planning for water main relocation in downtown area',
      stakeholders: 6,
      meetings_count: 2,
      documents_count: 8,
    },
  ];

  const mockCompanies = utilityCompanies || [
    {
      id: '1',
      name: 'Duke Energy',
      type: 'Electric',
      contact_name: 'Mike Thompson',
      contact_email: '<EMAIL>',
      contact_phone: '(*************',
      active_projects: 8,
      total_coordination: 24,
      response_time: '2.3 days',
      satisfaction_score: 4.2,
    },
    {
      id: '2',
      name: 'Citizens Energy Group',
      type: 'Gas & Water',
      contact_name: 'Lisa Davis',
      contact_email: '<EMAIL>',
      contact_phone: '(*************',
      active_projects: 12,
      total_coordination: 36,
      response_time: '1.8 days',
      satisfaction_score: 4.5,
    },
    {
      id: '3',
      name: 'AT&T',
      type: 'Telecommunications',
      contact_name: 'James Wilson',
      contact_email: '<EMAIL>',
      contact_phone: '(*************',
      active_projects: 6,
      total_coordination: 18,
      response_time: '3.1 days',
      satisfaction_score: 3.8,
    },
    {
      id: '4',
      name: 'Comcast',
      type: 'Telecommunications',
      contact_name: 'Jennifer Brown',
      contact_email: '<EMAIL>',
      contact_phone: '(*************',
      active_projects: 4,
      total_coordination: 12,
      response_time: '2.9 days',
      satisfaction_score: 3.9,
    },
  ];

  const mockStats = coordinationStats || {
    totalActivities: 45,
    inProgress: 18,
    completed: 23,
    onHold: 4,
    averageResponseTime: 2.5,
    avgSatisfactionScore: 4.1,
    totalUtilities: 15,
    activeUtilities: 12,
  };

  const activityTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'relocation', label: 'Relocation' },
    { value: 'protection', label: 'Protection' },
    { value: 'coordination', label: 'Coordination' },
    { value: 'assessment', label: 'Assessment' },
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'planning', label: 'Planning' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'on_hold', label: 'On Hold' },
  ];

  const priorityOptions = [
    { value: 'all', label: 'All Priorities' },
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in_progress':
        return 'default';
      case 'planning':
        return 'secondary';
      case 'on_hold':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getUtilityTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'electric':
        return Zap;
      case 'gas & water':
        return MapPin;
      case 'telecommunications':
        return Phone;
      default:
        return Users;
    }
  };

  const kpiCards = [
    {
      title: 'Active Coordination',
      value: mockStats.inProgress,
      icon: Clock,
      description: `${mockStats.totalActivities} total activities`,
    },
    {
      title: 'Completion Rate',
      value: `${Math.round((mockStats.completed / mockStats.totalActivities) * 100)}%`,
      icon: CheckCircle,
      description: 'Projects completed on time',
    },
    {
      title: 'Response Time',
      value: `${mockStats.averageResponseTime} days`,
      icon: MessageSquare,
      description: 'Average utility response',
    },
    {
      title: 'Satisfaction Score',
      value: mockStats.avgSatisfactionScore.toFixed(1),
      icon: Users,
      description: 'Out of 5.0 rating',
    },
  ];

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Utility Coordination</h1>
            <p className="text-muted-foreground">
              Manage utility coordination activities and stakeholder relationships
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline"
              onClick={() => {
                toast({
                  title: "Coming Soon",
                  description: "Meeting scheduling functionality is under development.",
                });
              }}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Schedule Meeting
            </Button>
            <Button
              onClick={() => {
                toast({
                  title: "Coming Soon",
                  description: "Create new activity functionality is under development.",
                });
              }}
            >
              <Plus className="mr-2 h-4 w-4" />
              New Activity
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="activities" className="space-y-4">
          <TabsList>
            <TabsTrigger value="activities">Activities</TabsTrigger>
            <TabsTrigger value="utilities">Utility Companies</TabsTrigger>
            <TabsTrigger value="meetings">Meetings</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="activities" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Coordination Activities</CardTitle>
                <CardDescription>
                  Track and manage all utility coordination activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filters */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search activities..."
                        value={searchTerm}
                        onChange={(e: any) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option: any) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map((option: any) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Activities List */}
                <div className="space-y-4">
                  {mockActivities
                    .filter(
                      (activity) =>
                        activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        activity.utility_company.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .filter(
                      (activity) => statusFilter === 'all' || activity.status === statusFilter
                    )
                    .filter(
                      (activity) => priorityFilter === 'all' || activity.priority === priorityFilter
                    )
                    .map((activity: any) => (
                      <Card key={activity.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-base">{activity.title}</CardTitle>
                              <CardDescription className="flex items-center space-x-4 mt-1">
                                <span>{activity.utility_company}</span>
                                <span>•</span>
                                <span>{activity.project}</span>
                                <span>•</span>
                                <span>Coordinator: {activity.coordinator}</span>
                              </CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={getPriorityColor(activity.priority)}>
                                {activity.priority}
                              </Badge>
                              <Badge variant={getStatusColor(activity.status)}>
                                {activity.status.replace('_', ' ')}
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            {activity.description}
                          </p>

                          <div className="space-y-4">
                            <div>
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium">Progress</span>
                                <span className="text-sm font-medium">{activity.progress}%</span>
                              </div>
                              <Progress value={activity.progress} />
                            </div>

                            <div className="grid gap-4 md:grid-cols-4">
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Target Date
                                </Label>
                                <p className="text-sm">
                                  {new Date(activity.target_date).toLocaleDateString()}
                                </p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Estimated Cost
                                </Label>
                                <p className="text-sm font-medium">
                                  ${activity.estimated_cost.toLocaleString()}
                                </p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Stakeholders
                                </Label>
                                <p className="text-sm">{activity.stakeholders} involved</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Documents
                                </Label>
                                <p className="text-sm">{activity.documents_count} files</p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2 pt-2 border-t">
                              <Button size="sm" variant="outline">
                                <MessageSquare className="h-3 w-3 mr-1" />
                                Notes
                              </Button>
                              <Button size="sm" variant="outline">
                                <Calendar className="h-3 w-3 mr-1" />
                                Schedule
                              </Button>
                              <Button size="sm" variant="outline">
                                <FileText className="h-3 w-3 mr-1" />
                                Documents
                              </Button>
                              <Button size="sm" variant="outline">
                                <Users className="h-3 w-3 mr-1" />
                                Stakeholders
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="utilities" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Utility Companies</CardTitle>
                <CardDescription>
                  Manage relationships with utility companies and contacts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {mockCompanies.map((company: any) => {
                    const TypeIcon = getUtilityTypeIcon(company.type);
                    return (
                      <Card key={company.id}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <TypeIcon className="h-5 w-5 text-blue-600" />
                              <div>
                                <CardTitle className="text-base">{company.name}</CardTitle>
                                <CardDescription>{company.type}</CardDescription>
                              </div>
                            </div>
                            <Badge variant="outline">{company.active_projects} active</Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div>
                              <Label className="text-sm font-medium text-muted-foreground">
                                Primary Contact
                              </Label>
                              <p className="text-sm font-medium">{company.contact_name}</p>
                              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Mail className="h-3 w-3" />
                                <span>{company.contact_email}</span>
                              </div>
                              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Phone className="h-3 w-3" />
                                <span>{company.contact_phone}</span>
                              </div>
                            </div>

                            <div className="grid gap-3 md:grid-cols-2">
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Response Time
                                </Label>
                                <p className="text-sm">{company.response_time}</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                  Satisfaction
                                </Label>
                                <p className="text-sm">{company.satisfaction_score}/5.0</p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2 pt-2 border-t">
                              <Button size="sm" variant="outline" className="flex-1">
                                <Mail className="h-3 w-3 mr-1" />
                                Contact
                              </Button>
                              <Button size="sm" variant="outline" className="flex-1">
                                <Calendar className="h-3 w-3 mr-1" />
                                Schedule
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="meetings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Coordination Meetings</CardTitle>
                <CardDescription>Schedule and manage stakeholder meetings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      title: 'Duke Energy Coordination Meeting',
                      date: '2024-01-20T10:00:00Z',
                      attendees: 8,
                      status: 'scheduled',
                      project: 'I-65 Widening Project',
                    },
                    {
                      title: 'Monthly Utility Stakeholder Review',
                      date: '2024-01-25T14:00:00Z',
                      attendees: 15,
                      status: 'scheduled',
                      project: 'Multiple Projects',
                    },
                    {
                      title: 'AT&T Fiber Coordination Follow-up',
                      date: '2024-01-18T09:00:00Z',
                      attendees: 6,
                      status: 'completed',
                      project: 'Airport Terminal Expansion',
                    },
                  ].map((meeting, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{meeting.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              {meeting.project} • {meeting.attendees} attendees
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {new Date(meeting.date).toLocaleString()}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant={meeting.status === 'completed' ? 'default' : 'secondary'}
                            >
                              {meeting.status}
                            </Badge>
                            <Button size="sm" variant="outline">
                              <Calendar className="h-3 w-3 mr-1" />
                              Details
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="flex justify-center mt-6">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Schedule Meeting
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Coordination Reports</CardTitle>
                <CardDescription>
                  Generate reports and analytics for coordination activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Monthly Coordination Report</CardTitle>
                      <CardDescription>
                        Summary of all coordination activities and progress
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <FileText className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Utility Performance Analysis</CardTitle>
                      <CardDescription>
                        Response times and satisfaction metrics by utility
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <Users className="h-4 w-4 mr-2" />
                        View Analytics
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Conflict Resolution Report</CardTitle>
                      <CardDescription>
                        Analysis of utility conflicts and resolution strategies
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Cost Analysis Report</CardTitle>
                      <CardDescription>
                        Financial impact and cost breakdown of coordinations
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        <FileText className="h-4 w-4 mr-2" />
                        View Analysis
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}