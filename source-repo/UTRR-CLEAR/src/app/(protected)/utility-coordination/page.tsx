import { redirect } from 'next/navigation';
import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import UtilityCoordinationClientPage from './client-page';

export default async function UtilityCoordinationPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch utility coordination data for faster loading
  void api.utilities.getAll.prefetch({ limit: 100 });
  void api.projects.getActiveProjects.prefetch();

  return (
    <HydrateClient>
      <UtilityCoordinationClientPage />
    </HydrateClient>
  );
}