import { api, HydrateClient } from '~/trpc/server';
import { getServerAuthSession } from '~/server/auth';
import GISProfessionalContent from '~/components/gis/gis-professional-page';

export default async function GISProfessionalPage() {
  const session = await getServerAuthSession();
  
  if (!session?.user) {
    return null; // Layout will redirect
  }

  // Prefetch GIS data for faster loading
  void api.projects.getActiveProjects.prefetch();
  void api.utilities.getAll.prefetch({ limit: 100 });
  
  return (
    <HydrateClient>
      <GISProfessionalContent />
    </HydrateClient>
  );
}