'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import { Map, Layers3, Zap, FileCode, Gauge, Settings } from 'lucide-react';
import { MapWrapper } from '~/components/gis/map-wrapper';

export default function GISProfessionalClientPage() {
  const [view, setView] = useState<'2d' | '3d'>('2d');
  const testProjectId = 'test-project-001';

  const features = [
    { icon: Zap, label: 'CAD Drawing', desc: 'Perpendicular, parallel, arc tools' },
    { icon: FileCode, label: 'DXF Import/Export', desc: 'AutoCAD compatibility' },
    { icon: Gauge, label: 'WebGL Rendering', desc: 'High-performance visualization' },
    { icon: Settings, label: 'Advanced Snapping', desc: 'Vertex, edge, angle snapping' },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">Professional GIS Mapping</CardTitle>
              <CardDescription>
                Advanced CAD-style utility mapping with professional features
              </CardDescription>
            </div>
            <Badge variant="secondary" className="text-sm">
              OpenLayers v10.5.0
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {features.map((feature) => {
              const Icon = feature.icon;
              return (
                <div key={feature.label} className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50">
                  <Icon className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <p className="font-medium text-sm">{feature.label}</p>
                    <p className="text-xs text-muted-foreground">{feature.desc}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex gap-2">
            <Button
              variant={view === '2d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('2d')}
            >
              <Map className="mr-2 h-4 w-4" />
              2D Map
            </Button>
            <Button
              variant={view === '3d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('3d')}
            >
              <Layers3 className="mr-2 h-4 w-4" />
              3D View
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="h-[800px]">
            <MapWrapper projectId={testProjectId} view={view} />
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">CAD Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Perpendicular Drawing</span>
              <Badge variant="outline" className="text-xs">Ctrl+P</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Arc Drawing</span>
              <Badge variant="outline" className="text-xs">Ctrl+A</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Parallel Lines</span>
              <Badge variant="outline" className="text-xs">Ctrl+L</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Measurement Tools</span>
              <Badge variant="outline" className="text-xs">Ctrl+M</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Import/Export</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">DXF (AutoCAD)</span>
              <Badge className="text-xs">Supported</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">GeoJSON</span>
              <Badge className="text-xs">Supported</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">KML/KMZ</span>
              <Badge className="text-xs">Supported</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Shapefile</span>
              <Badge className="text-xs">Supported</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">WebGL Points</span>
              <Badge variant="secondary" className="text-xs">1M+ features</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Vector Tiles</span>
              <Badge variant="secondary" className="text-xs">Optimized</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">Clustering</span>
              <Badge variant="secondary" className="text-xs">Dynamic</Badge>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-sm">3D Shaders</span>
              <Badge variant="secondary" className="text-xs">GPU Accel</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}