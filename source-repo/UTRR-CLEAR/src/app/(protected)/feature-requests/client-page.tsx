'use client';

import { useState, useEffect } from 'react';
import type { ChangeEvent } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  ArrowUp,
  CheckCircle,
  Clock,
  FileText,
  Lightbulb,
  MessageSquare,
  Plus,
  Search,
  ThumbsUp,
  TrendingUp,
  Users,
  X,
  Loader2,
  Code2,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';
import { FeatureRequestDialog } from '~/components/feature-request/feature-request-dialog';
import { exportTableData, downloadJSON } from '~/lib/export-utils';
import { ExportDropdown } from '~/components/ui/export-dropdown';
import { format } from 'date-fns';
import { safeLog } from '~/lib/error-handler';
import { useRealtimeNotifications } from '~/hooks/use-realtime-events';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';

interface FeatureRequestClientPageProps {
  session: SupabaseSession;
  initialRequests?: any;
  initialStats?: any;
}

export default function FeatureRequestClientPage({ 
  session,
  initialRequests,
  initialStats 
}: FeatureRequestClientPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<number | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Add real-time updates for feature requests
  useRealtimeNotifications();

  // Demo console logs for testing diagnostics collection
  useEffect(() => {
    safeLog.info('📋 Feature Request Page Loaded');
    safeLog.info('User Session:', { email: session?.user?.email || 'Not logged in' });
    safeLog.warn('Demo Warning: This is a test warning for diagnostics collection');
    
    // Simulate an error (caught, so it won't break the app)
    try {
      // This will create an error in the console
      const testObj: any = null;
      testObj.nonExistentMethod();
    } catch (error) {
      safeLog.error('Demo Error: Caught error for diagnostics testing', { error });
    }
  }, [session]);
  
  // Form state for new request
  const [newRequest, setNewRequest] = useState({
    title: '',
    description: '',
    type: 'feature' as 'feature' | 'enhancement' | 'bug',
    priority: 'medium' as 'high' | 'medium' | 'low',
    businessCase: '',
    tags: '',
  });

  // Use real API data with initial data
  const { data: featureRequests, isLoading: requestsLoading, refetch } = api.features.getAll.useQuery({
    search: searchTerm,
    status: statusFilter === 'all' ? undefined : statusFilter,
    priority: priorityFilter === 'all' ? undefined : priorityFilter,
  }, {
    initialData: initialRequests,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: featureStats, isLoading: statsLoading } = api.features.getStats.useQuery(undefined, {
    initialData: initialStats,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
  
  const createRequestMutation = api.features.create.useMutation({
    onSuccess: (data: any) => {
      const hasGithubIssue = data.github_issue_number && data.github_issue_url;
      
      toast({
        title: 'Success',
        description: 'Feature request submitted successfully' + 
          (hasGithubIssue ? ` - GitHub Issue #${data.github_issue_number} created` : ''),
      });
      // Reset form
      setNewRequest({
        title: '',
        description: '',
        type: 'feature',
        priority: 'medium',
        businessCase: '',
        tags: '',
      });
      void refetch();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create feature request',
        variant: 'destructive',
      });
    },
  });

  const voteMutation = api.features.vote.useMutation({
    onSuccess: () => {
      // Refetch data
      void refetch();
    },
  });

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'open', label: 'Open' },
    { value: 'under_review', label: 'Under Review' },
    { value: 'planned', label: 'Planned' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'rejected', label: 'Rejected' },
  ];

  const priorityOptions = [
    { value: 'all', label: 'All Priorities' },
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in_progress':
        return 'default';
      case 'planned':
        return 'secondary';
      case 'under_review':
        return 'secondary';
      case 'open':
        return 'outline';
      case 'rejected':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'feature':
        return Lightbulb;
      case 'enhancement':
        return TrendingUp;
      case 'bug':
        return X;
      default:
        return FileText;
    }
  };

  const kpiCards = [
    {
      title: 'Open Requests',
      value: featureStats?.openRequests || 0,
      icon: Clock,
      description: `${featureStats?.totalRequests || 0} total requests`,
    },
    {
      title: 'In Progress',
      value: featureStats?.inProgress || 0,
      icon: TrendingUp,
      description: 'Being developed',
    },
    {
      title: 'Completed',
      value: featureStats?.completed || 0,
      icon: CheckCircle,
      description: 'Successfully delivered',
    },
    {
      title: 'Avg Votes',
      value: featureStats?.avgVotes || 0,
      icon: ThumbsUp,
      description: 'Community engagement',
    },
  ];

  const handleSubmitRequest = () => {
    if (!newRequest.title || !newRequest.description) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    createRequestMutation.mutate({
      title: newRequest.title,
      description: `${newRequest.description}\n\nBusiness Case: ${newRequest.businessCase}\n\nTags: ${newRequest.tags}`,
      type: newRequest.type,
      priority: newRequest.priority,
    });
  };

  if (requestsLoading || statsLoading) {
    return (
      <div className="container py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Feature Requests</h1>
            <p className="text-muted-foreground">
              Submit, track, and manage feature requests and enhancements
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <ExportDropdown
              onExport={(format) => {
                if (!featureRequests?.requests || featureRequests.requests.length === 0) {
                  toast({
                    title: "No Data",
                    description: "No feature requests to export.",
                    variant: "destructive",
                  });
                  return;
                }

                if (format === 'json') {
                  downloadJSON(featureRequests.requests, 'feature-requests');
                  toast({
                    title: "Export Successful",
                    description: "Feature requests have been exported to JSON.",
                  });
                  return;
                }

                const columns = [
                  { key: 'id', label: 'ID' },
                  { key: 'title', label: 'Title' },
                  { key: 'description', label: 'Description' },
                  { key: 'type', label: 'Type' },
                  { key: 'priority', label: 'Priority' },
                  { key: 'status', label: 'Status', format: (value: unknown) => String(value).replace('_', ' ').toUpperCase() },
                  { key: 'votes', label: 'Votes' },
                  { key: 'comments_count', label: 'Comments' },
                  { key: 'created_at', label: 'Created', format: (value: unknown) => (format as any)(new Date(String(value)), 'yyyy-MM-dd') },
                  { key: 'updated_at', label: 'Updated', format: (value: unknown) => (format as any)(new Date(String(value)), 'yyyy-MM-dd') },
                ];

                exportTableData(
                  featureRequests.requests,
                  columns,
                  'feature-requests',
                  format as 'csv' | 'excel' | 'pdf'
                );

                toast({
                  title: "Export Successful",
                  description: `Feature requests have been exported to ${format.toUpperCase()}.`,
                });
              }}
              buttonText="Export Requests"
              disabled={requestsLoading}
            />
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Submit Request
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi: any) => {
            const Icon = kpi.icon;
            return (
              <Card key={kpi.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{kpi.value}</div>
                  <p className="text-xs text-muted-foreground">{kpi.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs defaultValue="requests" className="space-y-4">
          <TabsList>
            <TabsTrigger value="requests">Requests</TabsTrigger>
            <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="submit">Submit Request</TabsTrigger>
          </TabsList>

          <TabsContent value="requests" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Feature Requests</CardTitle>
                <CardDescription>
                  Browse and manage feature requests from the community
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filters */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search requests..."
                        value={searchTerm}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option: any) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map((option: any) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Requests List */}
                <div className="space-y-4">
                  {featureRequests?.requests && featureRequests.requests.length > 0 ? (
                    featureRequests.requests.map((request: any) => {
                      const TypeIcon = getTypeIcon(request.type);
                      return (
                        <Card
                          key={request.id}
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedRequest === request.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() =>
                            setSelectedRequest(request.id === selectedRequest ? null : request.id)
                          }
                        >
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <TypeIcon className="h-5 w-5 text-blue-600" />
                                <div>
                                  <CardTitle className="text-base">{request.title}</CardTitle>
                                  <CardDescription className="flex items-center space-x-4 mt-1">
                                    <span>#{request.id}</span>
                                    <span>•</span>
                                    <span>By User {request.created_by}</span>
                                    <span>•</span>
                                    <span>
                                      {new Date(request.created_at).toLocaleDateString()}
                                    </span>
                                  </CardDescription>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center space-x-1">
                                  <ArrowUp className="h-4 w-4 text-green-600" />
                                  <span className="text-sm font-medium">{request.votes}</span>
                                </div>
                                <Badge variant={getPriorityColor(request.priority)}>
                                  {request.priority}
                                </Badge>
                                <Badge variant={getStatusColor(request.status)}>
                                  {request.status.replace('_', ' ')}
                                </Badge>
                                {request.github_issue_number && (
                                  <a 
                                    href={request.github_issue_url || '#'} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    onClick={(e: any) => e.stopPropagation()}
                                  >
                                    <Badge variant="outline" className="flex items-center gap-1">
                                      <Code2 className="h-3 w-3" />
                                      #{request.github_issue_number}
                                    </Badge>
                                  </a>
                                )}
                              </div>
                            </div>
                          </CardHeader>

                          {selectedRequest === request.id && (
                            <CardContent className="pt-0">
                              <div className="space-y-4">
                                <p className="text-sm text-muted-foreground">
                                  {request.description}
                                </p>

                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                  <div>
                                    <Label className="text-sm font-medium text-muted-foreground">
                                      Type
                                    </Label>
                                    <p className="text-sm capitalize">{request.type}</p>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-muted-foreground">
                                      Created
                                    </Label>
                                    <p className="text-sm">
                                      {new Date(request.created_at).toLocaleDateString()}
                                    </p>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-muted-foreground">
                                      Updated
                                    </Label>
                                    <p className="text-sm">
                                      {new Date(request.updated_at).toLocaleDateString()}
                                    </p>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-muted-foreground">
                                      Comments
                                    </Label>
                                    <p className="text-sm">{request.comments_count}</p>
                                  </div>
                                </div>

                                {request.status === 'completed' && request.resolved_at && (
                                  <div className="p-3 bg-green-50 border border-green-200 rounded">
                                    <p className="text-sm text-green-800">
                                      ✅ Completed on{' '}
                                      {new Date(request.resolved_at).toLocaleDateString()}
                                    </p>
                                  </div>
                                )}

                                <div className="flex items-center space-x-2 pt-2 border-t">
                                  <Button 
                                    size="sm" 
                                    variant="outline"
                                    onClick={(e: any) => {
                                      e.stopPropagation();
                                      voteMutation.mutate({ requestId: request.id });
                                    }}
                                    disabled={voteMutation.isPending}
                                  >
                                    <ThumbsUp className="h-3 w-3 mr-1" />
                                    Vote ({request.votes})
                                  </Button>
                                  <Button size="sm" variant="outline">
                                    <MessageSquare className="h-3 w-3 mr-1" />
                                    Comments ({request.comments_count})
                                  </Button>
                                  {session?.user?.isAdmin && (
                                    <Button size="sm" variant="outline">
                                      <Users className="h-3 w-3 mr-1" />
                                      Manage
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          )}
                        </Card>
                      );
                    })
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No feature requests found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="roadmap" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Development Roadmap</CardTitle>
                <CardDescription>Planned features and release timeline</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Roadmap visualization coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Request Distribution</CardTitle>
                  <CardDescription>Feature requests by type and status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">Analytics coming soon</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Voted Requests</CardTitle>
                  <CardDescription>Most popular feature requests by votes</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {featureRequests?.requests
                      ?.sort((a: any, b: any) => b.votes - a.votes)
                      .slice(0, 5)
                      .map((request: any) => (
                        <div key={request.id} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm line-clamp-1">{request.title}</p>
                            <p className="text-xs text-muted-foreground">
                              {request.status.replace('_', ' ')}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <ArrowUp className="h-4 w-4 text-green-600" />
                            <span className="font-medium text-sm">{request.votes}</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="submit" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Submit Feature Request</CardTitle>
                <CardDescription>Share your ideas to improve the platform</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="request-type">Request Type</Label>
                    <Select
                      value={newRequest.type}
                      onValueChange={(value: 'feature' | 'enhancement' | 'bug') =>
                        setNewRequest({ ...newRequest, type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="feature">New Feature</SelectItem>
                        <SelectItem value="enhancement">Enhancement</SelectItem>
                        <SelectItem value="bug">Bug Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select
                      value={newRequest.priority}
                      onValueChange={(value: 'high' | 'medium' | 'low') =>
                        setNewRequest({ ...newRequest, priority: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    placeholder="Brief, descriptive title for your request"
                    value={newRequest.title}
                    onChange={(e: any) => setNewRequest({ ...newRequest, title: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Detailed description of the feature or enhancement you'd like to see..."
                    rows={6}
                    value={newRequest.description}
                    onChange={(e: any) =>
                      setNewRequest({ ...newRequest, description: e.target.value })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="business-case">Business Case</Label>
                  <Textarea
                    id="business-case"
                    placeholder="How would this feature benefit users and the business?"
                    rows={4}
                    value={newRequest.businessCase}
                    onChange={(e: any) =>
                      setNewRequest({ ...newRequest, businessCase: e.target.value })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input
                    id="tags"
                    placeholder="e.g., mapping, mobile, integration"
                    value={newRequest.tags}
                    onChange={(e: any) => setNewRequest({ ...newRequest, tags: e.target.value })}
                  />
                </div>

                <Button
                  className="w-full"
                  onClick={handleSubmitRequest}
                  disabled={createRequestMutation.isPending}
                >
                  {createRequestMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Submit Feature Request
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Feature Request Dialog */}
      <FeatureRequestDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSubmit={(request) => {
          // Build full description with additional fields
          let fullDescription = request.description;
          
          if (request.type === 'bug' && (request.currentBehavior || request.expectedBehavior || request.stepsToReproduce)) {
            fullDescription += '\n\n### Bug Details';
            if (request.currentBehavior) {
              fullDescription += `\n**Current Behavior:** ${request.currentBehavior}`;
            }
            if (request.expectedBehavior) {
              fullDescription += `\n**Expected Behavior:** ${request.expectedBehavior}`;
            }
            if (request.stepsToReproduce) {
              fullDescription += `\n**Steps to Reproduce:**\n${request.stepsToReproduce}`;
            }
          }
          
          if (request.affectsWorkflow) {
            fullDescription += '\n\n⚠️ This affects daily workflow';
          }

          createRequestMutation.mutate({
            title: request.title,
            description: fullDescription,
            type: request.type as 'feature' | 'enhancement' | 'bug',
            priority: request.priority as 'high' | 'medium' | 'low',
            createGithubIssue: request.createGithubIssue,
            assignToCopilot: request.assignToCopilot,
            category: request.category,
            browserInfo: request.browserInfo,
          });
          setDialogOpen(false);
        }}
      />
    </div>
  );
}