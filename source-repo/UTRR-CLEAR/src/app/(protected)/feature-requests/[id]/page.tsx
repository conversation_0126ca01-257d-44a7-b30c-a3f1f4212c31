import { redirect } from 'next/navigation';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import { api, HydrateClient } from '~/trpc/server';
import FeatureRequestDetailClientPage from './client-page';

interface FeatureRequestDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function FeatureRequestDetailPage({ params }: FeatureRequestDetailPageProps) {
  const session = await getServerAuthSession() as SupabaseSession | null;

  if (!session?.user) {
    redirect('/auth/signin');
  }

  const resolvedParams = await params;
  const id = parseInt(resolvedParams.id);
  
  if (isNaN(id)) {
    redirect('/feature-requests');
  }

  // Prefetch feature request detail data for faster loading
  void api.features.getById.prefetch({ id });

  // Fetch initial data on the server
  let initialData;
  try {
    initialData = await api.features.getById({ id });
  } catch (error) {
    // If the feature request doesn't exist or there's an error, let the client handle it
    initialData = null;
  }

  return (
    <HydrateClient>
      <FeatureRequestDetailClientPage 
        id={id}
        session={session as SupabaseSession}
        initialData={initialData}
      />
    </HydrateClient>
  );
}