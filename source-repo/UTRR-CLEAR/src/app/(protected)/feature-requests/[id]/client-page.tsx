'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link.js';
import { format } from 'date-fns';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '~/components/ui/card';
import { Textarea } from '~/components/ui/textarea';
import { Separator } from '~/components/ui/separator';
import { ArrowLeft, ArrowUpDown, Send, Paperclip, AlertTriangle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import { Shell } from '~/components/layout/shell';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';

interface FeatureRequestDetailClientPageProps {
  id: number;
  session: SupabaseSession;
  initialData?: any;
}

export default function FeatureRequestDetailClientPage({
  id,
  session,
  initialData
}: FeatureRequestDetailClientPageProps) {
  const router = useRouter();
  
  const commentInputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [comment, setComment] = useState('');
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  
  const { data: featureRequest, isLoading, error, refetch } = api.features.getById.useQuery(
    { id },
    { 
      enabled: !!id,
      initialData,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    }
  );
  
  const addCommentMutation = api.features.addComment.useMutation({
    onSuccess: () => {
      setComment('');
      refetch();
      toast({
        title: 'Comment added',
        description: 'Your comment has been added successfully',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive',
      });
    },
  });
  
  const voteMutation = api.features.vote.useMutation({
    onSuccess: (data: any) => {
      refetch();
      toast({
        title: data.voted ? 'Vote added' : 'Vote removed',
        description: data.voted ? 'Your vote has been counted' : 'Your vote has been removed',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to vote for the feature request',
        variant: 'destructive',
      });
    },
  });
  
  // Upload attachment functionality will be implemented when the API endpoint is ready
  const uploadAttachmentMutation = {
    mutate: () => {
      toast({
        title: 'Feature not available',
        description: 'File upload functionality is coming soon',
        variant: 'destructive',
      });
    },
    isPending: false,
  };
  
  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (comment.trim()) {
      addCommentMutation.mutate({
        requestId: id,
        content: comment,
      });
    }
  };
  
  const handleVote = () => {
    if (!featureRequest) return;
    voteMutation.mutate({ requestId: id });
  };
  
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    uploadAttachmentMutation.mutate();
    e.target.value = '';
  };
  
  const renderStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      'open': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
      'in-progress': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
      'under-review': 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100',
      'completed': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
      'declined': 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
    };
    
    const labels: Record<string, string> = {
      'open': 'Open',
      'in-progress': 'In Progress',
      'under-review': 'Under Review',
      'completed': 'Completed',
      'declined': 'Declined',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[status] || ''}`}>
        {labels[status] || status}
      </span>
    );
  };
  
  const renderPriorityBadge = (priority: string) => {
    const variants: Record<string, string> = {
      'low': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100',
      'medium': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
      'high': 'bg-orange-100 text-orange-800 dark:bg-orange-700 dark:text-orange-100',
      'critical': 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[priority] || ''}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };
  
  const renderTypeBadge = (type: string) => {
    const variants: Record<string, string> = {
      'feature': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
      'bug': 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100',
      'improvement': 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[type] || ''}`}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </span>
    );
  };
  
  const getInitials = (name: string) => {
    const parts = name.split(' ');
    return parts.map(p => p[0]).join('').toUpperCase();
  };
  
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };
  
  if (isLoading) {
    return (
      <Shell>
        <div className="container mx-auto py-8 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-gray-100"></div>
        </div>
      </Shell>
    );
  }
  
  if (error || !featureRequest) {
    return (
      <Shell>
        <div className="container mx-auto py-8">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load feature request. Please try again.
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Link href="/feature-requests">
              <Button variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to feature requests
              </Button>
            </Link>
          </div>
        </div>
      </Shell>
    );
  }
  
  return (
    <Shell>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <Link href="/feature-requests">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            </Link>
            <h1 className="text-3xl font-bold tracking-tight">
              {featureRequest.title}
            </h1>
          </div>
          <Button variant="outline" disabled={true}>
            Edit Request
          </Button>
        </div>
        
        <div className="bg-muted rounded-lg p-6 mb-6">
          <div className="flex justify-between mb-4">
            <div className="space-x-3">
              {renderStatusBadge(featureRequest.status)}
              {renderTypeBadge(featureRequest.type)}
              {renderPriorityBadge(featureRequest.priority)}
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline"
                    size="sm"
                    onClick={handleVote}
                    disabled={voteMutation.isPending}
                  >
                    <ArrowUpDown className="mr-1 h-3 w-3" />
                    <span>{featureRequest.votes} {featureRequest.votes === 1 ? 'vote' : 'votes'}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Vote for this request</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <div className="text-sm text-muted-foreground mb-4">
            <span className="font-medium">#{featureRequest.id}</span> opened on {format(new Date(featureRequest.created_at), 'MMM d, yyyy')} by{' '}
            <span className="font-medium">{featureRequest.creator.name}</span>
            {featureRequest.assignee && (
              <span> • Assigned to <span className="font-medium">{featureRequest.assignee.name}</span></span>
            )}
          </div>
          
          <div className="prose dark:prose-invert max-w-none">
            <p>{featureRequest.description}</p>
          </div>
          
          {featureRequest.attachments && featureRequest.attachments.length > 0 && (
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Attachments</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {featureRequest.attachments.map((attachment: any) => (
                  <a 
                    key={attachment.id}
                    href={attachment.fileUrl} 
                    target="_blank" 
                    rel="noreferrer"
                    className="flex items-center space-x-3 p-3 bg-background rounded border hover:bg-gray-50 dark:hover:bg-gray-800 transition"
                  >
                    <Paperclip className="h-4 w-4" />
                    <div className="overflow-hidden">
                      <div className="truncate text-sm">{attachment.fileName}</div>
                      <div className="text-xs text-muted-foreground">{formatFileSize(attachment.fileSize)}</div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">
            Comments ({featureRequest.comments?.length || 0})
          </h2>
          
          <div className="space-y-4">
            {featureRequest.comments?.map((comment: any) => (
              <div key={comment.id} className="flex gap-4">
                <Avatar className="h-10 w-10">
                  {comment.user.image ? (
                    <AvatarImage src={comment.user.image} alt={comment.user.name || 'User'} />
                  ) : (
                    <AvatarFallback>{getInitials(comment.user.name || 'U')}</AvatarFallback>
                  )}
                </Avatar>
                <div className="flex-1">
                  <Card>
                    <CardHeader className="py-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm font-medium">
                          {comment.user.name}
                        </CardTitle>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(comment.created_at), 'MMM d, yyyy, h:mm a')}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent className="py-3">
                      <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <Separator className="my-6" />
        
        <div>
          <h3 className="text-lg font-medium mb-4">Add a comment</h3>
          <form onSubmit={handleCommentSubmit}>
            <Textarea
              ref={commentInputRef}
              value={comment}
              onChange={(e: any) => setComment(e.target.value)}
              placeholder="Write a comment..."
              className="min-h-[100px] mb-3"
            />
            <div className="flex justify-between">
              <Button 
                type="button" 
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={true}
              >
                <Paperclip className="mr-2 h-4 w-4" />
                Add attachment
              </Button>
              <Button 
                type="submit" 
                disabled={!comment.trim() || addCommentMutation.isPending}
              >
                <Send className="mr-2 h-4 w-4" />
                Submit
              </Button>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileUpload}
            />
          </form>
        </div>
        
      </div>
    </Shell>
  );
}