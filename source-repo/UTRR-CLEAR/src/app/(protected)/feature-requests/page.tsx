import { redirect } from 'next/navigation';
import { getServerAuthSession } from '~/server/auth';
import type { SupabaseSession } from '~/lib/auth/supabase-auth';
import { api, HydrateClient } from '~/trpc/server';
import FeatureRequestClientPage from './client-page';

export default async function FeatureRequestPage() {
  const session = await getServerAuthSession() as SupabaseSession | null;

  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Prefetch feature request data for faster loading
  void api.features.getAll.prefetch({
    search: '',
    status: undefined,
    priority: undefined,
  });
  void api.features.getStats.prefetch();

  // Fetch initial data on the server
  const [initialRequests, initialStats] = await Promise.all([
    api.features.getAll({
      search: '',
      status: undefined,
      priority: undefined,
    }),
    api.features.getStats(),
  ]);

  return (
    <HydrateClient>
      <FeatureRequestClientPage 
        session={session as SupabaseSession}
        initialRequests={initialRequests}
        initialStats={initialStats}
      />
    </HydrateClient>
  );
}