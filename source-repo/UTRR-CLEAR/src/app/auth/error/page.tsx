import { api, HydrateClient } from '~/trpc/server';
import ErrorClientPage from './client-page';

// Force dynamic rendering for auth routes
export const dynamic = 'force-dynamic';

export default async function AuthErrorPage() {
  // Pre-fetch organization data for auth context
  void api.organizations.getPublic.prefetch();
  
  return (
    <HydrateClient>
      <ErrorClientPage />
    </HydrateClient>
  );
}
