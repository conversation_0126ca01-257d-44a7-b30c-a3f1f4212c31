import { api, HydrateClient } from '~/trpc/server';
import SignInClientPage from './client-page';

// Force dynamic rendering for auth routes
export const dynamic = 'force-dynamic';

export default async function SignInPage() {
  try {
    // Pre-fetch organization data for faster initial render
    void api.organizations.getPublic.prefetch();
    const organization = await api.organizations.getPublic();
    
    return (
      <HydrateClient>
        <SignInClientPage organization={organization} />
      </HydrateClient>
    );
  } catch (error) {
    console.error('Failed to fetch organization data:', error);
    // Fallback if organization fetch fails
    return (
      <HydrateClient>
        <SignInClientPage organization={null} />
      </HydrateClient>
    );
  }
}