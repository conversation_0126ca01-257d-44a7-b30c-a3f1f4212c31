'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '~/lib/supabase/client';
import Image from 'next/image';
import { Button } from '~/components/ui/button';
import { Card, CardContent } from '~/components/ui/card';
import { Input } from '~/components/ui/input';
import { OrganizationLogoStatic, OrganizationFooterImageStatic } from '~/components/ui/organization-logo-static';
import { LogoSkeletonSigninLeft, LogoSkeletonSigninForm, FooterImageSkeleton } from '~/components/ui/image-skeleton';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '~/components/ui/form';
import { Loader2, Eye, EyeOff } from 'lucide-react';
import EgisLogo from '~/components/ui/egis-logo';
import { SignInUpdateNotification } from '~/components/ui/signin-update-notification';
import { api } from '~/trpc/react';
import { cn } from '~/lib/utils';
import { safeLog } from '~/lib/error-handler';
import { Captcha, HoneypotField } from '~/components/security/captcha';

const loginSchema = z.object({
  email: z.string().email('Valid email is required').min(1, 'Email is required'),
  password: z.string().min(1, 'Password is required'),
});

type LoginFormValues = z.infer<typeof loginSchema>;

interface SignInClientPageProps {
  organization?: any;
}

export default function SignInClientPage({ organization }: SignInClientPageProps) {
  // Extract logo display settings from theme config
  const logoConfig = React.useMemo(() => {
    if (organization?.theme_config) {
      try {
        const themeConfig = organization.theme_config as any;
        return themeConfig.logoDisplay?.signinLogo || {
          useDarkMode: true,
          scale: 1,
        };
      } catch {
        // Fallback to defaults
      }
    }
    return {
      useDarkMode: true,
      scale: 1,
    };
  }, [organization]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);
  const [error, setError] = React.useState('');
  const [inactivityMessage, setInactivityMessage] = React.useState('');
  const [captchaVerified, setCaptchaVerified] = React.useState(false);
  const [showCaptcha, setShowCaptcha] = React.useState(false);
  const [failedAttempts, setFailedAttempts] = React.useState(0);
  const router = useRouter();

  // Check if user was logged out due to inactivity or session expiration
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const reason = urlParams.get('reason');
    
    if (reason === 'inactivity') {
      setInactivityMessage('Your session expired due to inactivity. Please log in again.');
    } else if (reason === 'session-expired') {
      setInactivityMessage('Your session has expired. Please log in again.');
    }
  }, []);

  const form = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    // Check if CAPTCHA is required and not verified
    if (failedAttempts >= 2 && !captchaVerified) {
      setError('Please complete the security check');
      setShowCaptcha(true);
      return;
    }
    
    safeLog.info('Form submitted with data:', { email: data.email });
    setIsLoading(true);
    setError('');

    try {
      safeLog.info('Attempting Supabase authentication...');
      
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (authError) {
        safeLog.error('Supabase auth error:', { error: authError.message });
        setError('Invalid email or password');
        setFailedAttempts(prev => prev + 1);
        
        // Show CAPTCHA after 2 failed attempts
        if (failedAttempts >= 1) {
          setShowCaptcha(true);
          setCaptchaVerified(false);
        }
      } else if (authData.user) {
        safeLog.info('Authentication successful, redirecting to dashboard');
        router.push('/dashboard');
        router.refresh();
      } else {
        safeLog.warn('Unexpected auth result');
        setError('Login failed');
      }
    } catch (err) {
      safeLog.error('Authentication exception:', { error: String(err) });
      setError('An error occurred during sign in');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Removed hasSigninImage - we always want the 2-column layout

  return (
    <>
      <SignInUpdateNotification />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-egis-blue/20 via-egis-teal/10 to-egis-midnightBlue/30 backdrop-blur-sm px-4 relative overflow-hidden">
        <div className="grid w-full lg:grid-cols-2 max-w-6xl overflow-hidden rounded-xl border border-white/20 bg-white/10 backdrop-blur-md shadow-2xl relative z-10">
        {/* Left side with brand info - always show */}
        <div className="flex flex-col h-full p-8 bg-egis-midnightBlue text-white rounded-l-xl relative overflow-hidden">
          {/* Floating elements in background - smaller, more dynamic */}
          <div className="absolute top-[10%] left-[10%] w-64 h-64 rounded-full bg-sky-500/40 blur-xl animate-float-slow z-0"></div>
          <div className="absolute bottom-[30%] right-[15%] w-56 h-56 rounded-full bg-teal-400/40 blur-xl animate-float-medium z-0"></div>
          <div className="absolute top-[60%] left-[25%] w-48 h-48 rounded-full bg-green-400/40 blur-xl animate-float-fast z-0"></div>
          <div className="absolute top-[40%] right-[30%] w-52 h-52 rounded-full bg-blue-400/30 blur-xl animate-float-reverse z-0"></div>
          
          {/* Adding some smaller, sharper bubbles for a more dynamic look */}
          <div className="absolute top-[30%] left-[30%] w-16 h-16 rounded-full bg-white/20 blur-md animate-bounce-slow z-0"></div>
          <div className="absolute top-[70%] left-[5%] w-20 h-20 rounded-full bg-white/25 blur-md animate-float-fast z-0"></div>
          <div className="absolute bottom-[20%] right-[30%] w-12 h-12 rounded-full bg-white/30 blur-md animate-bounce-slow z-0"></div>
          
          <div className="flex flex-col space-y-8 relative z-10">
            {/* Logo area - uses generated assets with fallback */}
            <div className="w-60 h-20 mx-auto">
              <OrganizationLogoStatic
                className="w-full h-full object-contain"
                scale={logoConfig.scale}
                darkBackground={logoConfig.useDarkMode}
              />
            </div>
            
            {/* CLEAR branding - always visible, not dependent on org data */}
            <div className="space-y-6">
            <h2 className="text-2xl font-codec font-bold text-white">The Power of CLEAR</h2>
            <p className="text-gray-300">
              CLEAR isn&apos;t just a name. It&apos;s a revolutionary approach to utility coordination:
            </p>
          
            <div className="space-y-4">
              <div className="p-4 rounded-lg bg-white/10 border border-white/20 backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300 hover:bg-white/15 hover:scale-105 hover:border-white/30 relative z-10">
                <h3 className="text-lg font-codec font-bold text-egis-green">Connect</h3>
                <p className="text-sm text-gray-300 mt-1">
                  Bringing every stakeholder into a seamless collaborative environment. No more information silos. No more communication gaps.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-white/10 border border-white/20 backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300 hover:bg-white/15 hover:scale-105 hover:border-white/30 relative z-10">
                <h3 className="text-lg font-codec font-bold text-egis-green">Locate</h3>
                <p className="text-sm text-gray-300 mt-1">
                  Transforming invisible infrastructure into vivid, actionable data. See what others can&apos;t see. Prevent what others can&apos;t prevent.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-white/10 border border-white/20 backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300 hover:bg-white/15 hover:scale-105 hover:border-white/30 relative z-10">
                <h3 className="text-lg font-codec font-bold text-egis-green">Eliminate</h3>
                <p className="text-sm text-gray-300 mt-1">
                  Conflicts vanish before they exist. Predictive technology identifies and resolves issues at the earliest possible stage.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-white/10 border border-white/20 backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300 hover:bg-white/15 hover:scale-105 hover:border-white/30 relative z-10">
                <h3 className="text-lg font-codec font-bold text-egis-green">Accelerate</h3>
                <p className="text-sm text-gray-300 mt-1">
                  Projects move at unprecedented speed when coordination barriers disappear. What once took weeks now takes moments.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-white/10 border border-white/20 backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300 hover:bg-white/15 hover:scale-105 hover:border-white/30 relative z-10">
                <h3 className="text-lg font-codec font-bold text-egis-green">Resolve</h3>
                <p className="text-sm text-gray-300 mt-1">
                  Complex utility challenges become simple, structured workflows. The most difficult aspect of infrastructure development, made intuitive.
                </p>
            </div>
            </div>
          </div>
        </div>
        </div>
        
        {/* Right side with login form */}
        <div className="flex flex-col justify-center p-8 bg-white backdrop-blur-md relative py-12 rounded-r-xl">
          {inactivityMessage && (
            <div className="bg-amber-50 text-amber-800 p-3 rounded-md mb-4 text-sm border border-amber-200 relative z-10">
              {inactivityMessage}
            </div>
          )}
          {error && (
            <div className="bg-destructive/10 text-destructive p-3 rounded-md mb-4 text-sm border border-destructive/20 relative z-10">
              {error}
            </div>
          )}

          
          <div className="w-full">
            <Card className="bg-white/70 backdrop-blur-xl border border-white/40 shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl relative z-10">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <CardContent className="pt-6 pb-4 space-y-6">
                    {/* Title and description */}
                    <div className="space-y-2 mb-2">
                      <h2 className="text-2xl font-semibold text-egis-green font-codec">Login</h2>
                      <p className="text-sm text-muted-foreground">
                        Enter your email and password to login
                      </p>
                    </div>
                    
                    {/* Email input */}
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Enter your email"
                              className="bg-white/70 backdrop-blur-sm border-white/40 focus:border-egis-green/50 transition-all duration-300"
                              {...field}
                              disabled={isLoading}
                              autoComplete="email"
                            />
                          </FormControl>
                          <FormDescription className="text-xs">
                            Use your registered email address
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Password field */}
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input 
                                type={showPassword ? "text" : "password"} 
                                placeholder="••••••••" 
                                {...field} 
                                className="bg-white/70 backdrop-blur-sm border-white/40 focus:border-egis-green/50 transition-all duration-300 pr-10 [&::-ms-reveal]:hidden [&::-webkit-credentials-auto-fill-button]:hidden"
                                disabled={isLoading}
                                autoComplete="current-password"
                              />
                              {field.value && field.value.length > 0 && (
                                <button
                                  type="button"
                                  onClick={togglePasswordVisibility}
                                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                                  tabIndex={-1} 
                                  aria-label={showPassword ? "Hide password" : "Show password"}
                                >
                                  {showPassword ? (
                                    <EyeOff className="h-5 w-5" />
                                  ) : (
                                    <Eye className="h-5 w-5" />
                                  )}
                                </button>
                              )}
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* CAPTCHA - shown after failed attempts */}
                    {showCaptcha && (
                      <Captcha 
                        onVerify={(isValid) => {
                          setCaptchaVerified(isValid);
                          if (isValid) {
                            setError('');
                          }
                        }}
                        className="mt-4"
                      />
                    )}
                    
                    {/* Honeypot field for bot detection */}
                    <HoneypotField />
                    
                    {/* Login button and footer text */}
                    <div className="space-y-4 pt-2">
                      <Button 
                        type="submit" 
                        className="w-full bg-egis-green hover:bg-egis-green/95 transition-all duration-300 shadow-md hover:shadow-lg relative overflow-hidden before:absolute before:content-[''] before:inset-0 before:w-full before:h-full before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:animate-shimmer text-white" 
                        disabled={isLoading || (showCaptcha && !captchaVerified)}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Logging in...
                          </>
                        ) : (
                          "Login"
                        )}
                      </Button>
                      <div className="text-xs text-muted-foreground text-center">
                        Contact CLEAR admin if you need an account.
                      </div>
                    </div>
                  </CardContent>
                </form>
              </Form>
            </Card>
            
            {/* Footer - uses generated assets with fallback */}
            <div className="text-center mt-6 max-w-sm mx-auto relative z-20">
              <div className="bg-white/80 backdrop-blur-md border border-white/40 shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl p-4">
                <OrganizationFooterImageStatic 
                  className="w-full h-auto object-contain"
                  width={300}
                  height={200}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
