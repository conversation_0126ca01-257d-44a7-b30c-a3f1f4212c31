'use client';

import * as React from 'react';
import { useAuth } from '~/hooks/use-auth';
import { useRouter, useSearchParams } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Input } from '~/components/ui/input';
import { Button } from '~/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '~/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Loader2, Shield, AlertTriangle, CheckCircle } from 'lucide-react';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import { Captcha, <PERSON>potField } from '~/components/security/captcha';

// Schema for mandatory password reset (no current password required)
const mandatoryPasswordResetSchema = z.object({
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

type MandatoryPasswordResetFormValues = z.infer<typeof mandatoryPasswordResetSchema>;

interface MandatoryPasswordResetClientPageProps {
  isRequired: boolean;
  userId?: string;
}

export default function MandatoryPasswordResetClientPage({ isRequired, userId }: MandatoryPasswordResetClientPageProps) {
  const { user, loading, signOut } = useAuth();
  const router = useRouter();
  const [isResetting, setIsResetting] = React.useState(false);
  const [captchaVerified, setCaptchaVerified] = React.useState(false);

  const form = useForm({
    resolver: zodResolver(mandatoryPasswordResetSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  // Redirect if user doesn't need password reset
  React.useEffect(() => {
    if (user && !isRequired) {
      // Check if this user actually needs to reset password
      // If not required and they somehow got here, redirect to dashboard
      router.push('/dashboard');
    }
  }, [user, isRequired, router]);

  const resetPasswordMutation = api.users.mandatoryPasswordReset.useMutation({
    onSuccess: async () => {
      setIsResetting(true);
      
      toast({
        title: 'New Password Set Successfully',
        description: 'Your password has been updated. You will be signed out and need to sign in again.',
      });

      // Wait a moment for the toast to show
      setTimeout(async () => {
        // Sign out the user to clear the resetPasswordOnLogin flag from session
        await signOut();
        
        // Manually redirect to signin page
        router.push('/auth/signin?message=password-reset-complete');
      }, 2000);
    },
    onError: (error: any) => {
      toast({
        title: 'Password Update Failed',
        description: error.message || 'Failed to set new password. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (values: MandatoryPasswordResetFormValues) => {
    if (!captchaVerified) {
      toast({
        title: 'Security Check Required',
        description: 'Please complete the security check before resetting your password.',
        variant: 'destructive',
      });
      return;
    }
    
    const userIdToUse = userId || user?.id;
    
    if (!userIdToUse) {
      toast({
        title: 'Error',
        description: 'Session not found. Please sign in again.',
        variant: 'destructive',
      });
      return;
    }

    resetPasswordMutation.mutate({
      userId: userIdToUse,
      newPassword: values.newPassword,
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user && !userId) {
    router.push('/auth/signin');
    return null;
  }

  if (isResetting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">New Password Set</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  You will be signed out and redirected to the login page.
                </p>
              </div>
              <div className="flex justify-center">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
              <Shield className="h-8 w-8 text-orange-600" />
            </div>
          </div>
          <CardTitle className="text-2xl">Set New Password</CardTitle>
          <CardDescription className="text-center">
            You must create a new password before you can continue.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertTitle className="text-orange-800 dark:text-orange-200">Mandatory Password Reset</AlertTitle>
            <AlertDescription className="text-orange-700 dark:text-orange-300">
              An administrator has required you to create a new password. You cannot access the system until you set a new password.
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" autoComplete="new-password" placeholder="Enter your new password" />
                    </FormControl>
                    <FormMessage />
                    <p className="text-xs text-muted-foreground mt-1">
                      Must be 8+ characters with uppercase, lowercase, number, and special character
                    </p>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" autoComplete="new-password" placeholder="Confirm your new password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* CAPTCHA for security */}
              <Captcha 
                onVerify={(isValid) => {
                  setCaptchaVerified(isValid);
                }}
                className="my-4"
              />
              
              {/* Honeypot field */}
              <HoneypotField />

              <Button 
                type="submit" 
                disabled={resetPasswordMutation.isPending || !captchaVerified}
                className="w-full"
                size="lg"
              >
                {resetPasswordMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting New Password...
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-4 w-4" />
                    Set New Password & Continue
                  </>
                )}
              </Button>
            </form>
          </Form>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              After setting your new password, you&apos;ll be signed out and need to sign in again.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}