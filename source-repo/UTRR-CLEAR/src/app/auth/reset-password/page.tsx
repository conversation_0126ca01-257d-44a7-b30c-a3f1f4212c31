import { Suspense } from 'react';
import MandatoryPasswordResetClientPage from './client-page';
import { getServerAuthSession } from '~/server/auth';
import { api, HydrateClient } from '~/trpc/server';

// Force dynamic rendering for auth routes
export const dynamic = 'force-dynamic';

export default async function MandatoryPasswordResetPage({
  searchParams,
}: {
  searchParams: Promise<{ required?: string }>;
}) {
  const session = await getServerAuthSession();
  const params = await searchParams;
  const isRequired = params.required === 'true';
  
  // Pre-fetch organization data for auth context
  void api.organizations.getPublic.prefetch();
  
  return (
    <HydrateClient>
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      }>
        <MandatoryPasswordResetClientPage 
          isRequired={isRequired}
          userId={session?.user?.id}
        />
      </Suspense>
    </HydrateClient>
  );
}