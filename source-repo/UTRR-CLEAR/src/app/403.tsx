'use client';

import { AccessDenied } from '~/components/ui/access-denied';

export default function ForbiddenPage() {
  return (
    <AccessDenied
      variant="page"
      reason="permission"
      title="Access Forbidden"
      message="You don't have permission to access this page or resource."
      showContactSupport={true}
      showBackButton={true}
      showHomeButton={true}
    >
      <div className="space-y-2 text-sm text-muted-foreground">
        <p>This could happen if:</p>
        <ul className="list-disc list-inside space-y-1 ml-2">
          <li>Your account doesn't have the required permissions</li>
          <li>You're trying to access a feature restricted to certain roles</li>
          <li>Your session has expired or been invalidated</li>
          <li>This page requires administrator access</li>
        </ul>
      </div>
    </AccessDenied>
  );
}