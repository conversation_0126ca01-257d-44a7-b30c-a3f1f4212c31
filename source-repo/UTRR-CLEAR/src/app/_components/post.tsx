'use client';

import { api } from '~/trpc/react';

export function ProjectsList() {
  const { data: projectsData, isLoading } = api.projects.getAll.useQuery({
    limit: 6,
    offset: 0,
  });

  if (isLoading) {
    return <div className="text-white">Loading projects...</div>;
  }

  const projects = projectsData?.projects || [];

  return (
    <div className="w-full max-w-4xl">
      <h2 className="text-2xl font-bold text-white mb-4">Recent Projects</h2>
      {projects && projects.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project: any) => (
            <div
              key={project.id}
              className="rounded-lg bg-white/10 p-4 hover:bg-white/20 transition-colors"
            >
              <h3 className="font-semibold text-white truncate">{project.name}</h3>
              <p className="text-sm text-white/70 mt-1">Client: {project.client}</p>
              <p className="text-sm text-white/70">
                Status: {project.rag_status || project.this_month_status || 'Active'}
              </p>
              {project.work_type && (
                <p className="text-sm text-white/70">Type: {project.work_type}</p>
              )}
            </div>
          ))}
        </div>
      ) : (
        <p className="text-white/70">No projects found.</p>
      )}
    </div>
  );
}
