import '~/styles/globals.css';

import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON> } from 'next/font/google';

import { TRPCReactProvider } from '~/trpc/react';
import { AuthProvider } from '~/hooks/use-auth';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '~/components/ui/toaster';

export const metadata: Metadata = {
  title: 'CLEAR - Egis Utility Coordination',
  description: 'Modern utility coordination and project management by Egis Group',
  icons: [{ rel: 'icon', url: '/favicon.ico' }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

const geist = Geist({
  subsets: ['latin'],
  variable: '--font-geist-sans',
});

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geist.variable}`} suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange={false}
          storageKey="clear-theme"
        >
          <AuthProvider>
            <TRPCReactProvider>
              {children}
              <Toaster />
            </TRPCReactProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
