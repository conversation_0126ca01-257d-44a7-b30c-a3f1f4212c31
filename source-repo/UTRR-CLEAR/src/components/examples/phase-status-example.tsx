'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import PhaseStatusIndicator, { 
  PhaseStatusBadge, 
  type PhaseStep, 
  type PhaseStatus 
} from '~/components/ui/phase-status-indicator';

// Example phase data for utility coordination project
const samplePhases: PhaseStep[] = [
  {
    id: 'initiation',
    name: 'Initiation',
    description: 'Project kickoff',
    status: 'completed',
    completedDate: '2024-01-15',
  },
  {
    id: 'research',
    name: 'Research',
    description: 'Utility identification',
    status: 'completed',
    completedDate: '2024-02-01',
  },
  {
    id: 'coordination',
    name: 'Coordination',
    description: 'Stakeholder meetings',
    status: 'in-progress',
    dueDate: '2024-02-28',
  },
  {
    id: 'field-verification',
    name: 'Field Verification',
    description: 'Site surveys',
    status: 'at-risk',
    dueDate: '2024-03-15',
  },
  {
    id: 'conflict-analysis',
    name: 'Conflict Analysis',
    description: 'Issue identification',
    status: 'pending',
    dueDate: '2024-04-01',
  },
  {
    id: 'work-planning',
    name: 'Work Planning',
    description: 'Resolution planning',
    status: 'pending',
    dueDate: '2024-04-15',
  },
  {
    id: 'agreements',
    name: 'Agreements',
    description: 'Contract execution',
    status: 'pending',
    dueDate: '2024-05-01',
  },
  {
    id: 'construction',
    name: 'Construction',
    description: 'Work execution',
    status: 'pending',
    dueDate: '2024-06-01',
  },
];

// Example with different statuses for testing
const testPhases: PhaseStep[] = [
  { id: '1', name: 'Planning', status: 'completed' },
  { id: '2', name: 'Design', status: 'in-progress' },
  { id: '3', name: 'Review', status: 'overdue', dueDate: '2024-01-20' },
  { id: '4', name: 'Approval', status: 'blocked' },
  { id: '5', name: 'Construction', status: 'pending' },
];

export function PhaseStatusExample() {
  const [currentPhase, setCurrentPhase] = useState('coordination');
  const [selectedPhases, setSelectedPhases] = useState<PhaseStep[]>(samplePhases);
  
  const handleStatusChange = (phaseId: string, newStatus: PhaseStatus) => {
    setSelectedPhases(prev => 
      prev.map(phase => 
        phase.id === phaseId 
          ? { ...phase, status: newStatus }
          : phase
      )
    );
  };

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Phase Status Indicator Examples</h2>
        <p className="text-muted-foreground">
          Demonstrating the industry-standard phase status visualization with meaningful colors and icons.
        </p>
      </div>

      {/* Main example - Utility Coordination Workflow */}
      <Card>
        <CardHeader>
          <CardTitle>Utility Coordination Project Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <PhaseStatusIndicator
            phases={selectedPhases}
            currentPhaseId={currentPhase}
            showConnector={true}
            showLabels={true}
            size="md"
          />
          
          <div className="mt-6 space-y-2">
            <h4 className="font-medium">Change Current Phase:</h4>
            <div className="flex gap-2 flex-wrap">
              {selectedPhases.map(phase => (
                <Button
                  key={phase.id}
                  variant={currentPhase === phase.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPhase(phase.id)}
                >
                  {phase.name}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Size variations */}
      <Card>
        <CardHeader>
          <CardTitle>Size Variations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-2">Small</h4>
            <PhaseStatusIndicator
              phases={testPhases.slice(0, 3)}
              currentPhaseId="2"
              size="sm"
            />
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Medium (Default)</h4>
            <PhaseStatusIndicator
              phases={testPhases.slice(0, 3)}
              currentPhaseId="2"
              size="md"
            />
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Large</h4>
            <PhaseStatusIndicator
              phases={testPhases.slice(0, 3)}
              currentPhaseId="2"
              size="lg"
            />
          </div>
        </CardContent>
      </Card>

      {/* Status badges */}
      <Card>
        <CardHeader>
          <CardTitle>Status Badges</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <PhaseStatusBadge status="completed" />
              <p className="text-xs text-muted-foreground">Phase finished successfully</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="in-progress" />
              <p className="text-xs text-muted-foreground">Currently being worked on</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="at-risk" />
              <p className="text-xs text-muted-foreground">May miss deadline</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="overdue" />
              <p className="text-xs text-muted-foreground">Past deadline</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="blocked" />
              <p className="text-xs text-muted-foreground">Cannot proceed</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="pending" />
              <p className="text-xs text-muted-foreground">Not yet started</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="on-hold" />
              <p className="text-xs text-muted-foreground">Temporarily paused</p>
            </div>
            
            <div className="space-y-2">
              <PhaseStatusBadge status="cancelled" />
              <p className="text-xs text-muted-foreground">Will not be completed</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive status testing */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Status Testing</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testPhases.map(phase => (
              <div key={phase.id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-3">
                  <PhaseStatusBadge status={phase.status} size="sm" />
                  <span className="font-medium">{phase.name}</span>
                </div>
                
                <div className="flex gap-1">
                  {(['completed', 'in-progress', 'at-risk', 'overdue', 'blocked', 'pending'] as PhaseStatus[]).map(status => (
                    <Button
                      key={status}
                      variant={phase.status === status ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleStatusChange(phase.id, status)}
                      className="text-xs"
                    >
                      {status}
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Color legend */}
      <Card>
        <CardHeader>
          <CardTitle>Color Standards</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Success States</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span>Green: Completed phases</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Active States</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>Blue: In progress (animated)</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Warning States</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                  <span>Yellow: At risk (exclamation badge)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                  <span>Orange: On hold</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Critical States</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                  <span>Red: Overdue (exclamation badge)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-700 rounded-full"></div>
                  <span>Dark Red: Blocked</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PhaseStatusExample;