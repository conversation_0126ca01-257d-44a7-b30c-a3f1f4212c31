'use client';

import React, { memo, useCallback, useMemo, useRef, useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { throttle, debounce } from 'lodash';
import RBush from 'rbush';

// Lazy load heavy components
const OpenLayersMap = dynamic(() => import('../gis/utility-map-2d'), {
  ssr: false,
  loading: () => <div className="h-full w-full bg-muted animate-pulse" />,
});

// Conflict detection is handled internally in utility-map-2d
// const ConflictDetectionPanel = dynamic(() => import('./conflict-detection-panel'), {
//   ssr: false,
// });

// Import OpenLayers Feature type
interface UtilityFeature {
  id: string;
  geometry: any;
  properties: {
    utility_type: string;
    project_id: string;
  };
}

interface OptimizedUtilityMap2DProps {
  projectId: string;
  features: UtilityFeature[];
  onFeatureSelect?: (feature: UtilityFeature) => void;
  onConflictDetected?: (conflicts: any[]) => void;
}

/**
 * Optimized spatial index for conflict detection
 * Reduces O(n²) to O(n log n) complexity
 */
class SpatialIndex {
  private index = new RBush<{ 
    minX: number; 
    minY: number; 
    maxX: number; 
    maxY: number; 
    feature: UtilityFeature 
  }>();

  updateFeatures(features: UtilityFeature[]) {
    this.index.clear();
    
    const items = features.map(feature => {
      const coords = this.extractCoordinates(feature.geometry);
      const bounds = this.calculateBounds(coords);
      
      return {
        ...bounds,
        feature,
      };
    });
    
    this.index.load(items);
  }

  findPotentialConflicts(feature: UtilityFeature): UtilityFeature[] {
    const coords = this.extractCoordinates(feature.geometry);
    const bounds = this.calculateBounds(coords);
    
    // Add buffer for conflict detection
    const buffered = {
      minX: bounds.minX - 0.0001, // ~10 meters
      minY: bounds.minY - 0.0001,
      maxX: bounds.maxX + 0.0001,
      maxY: bounds.maxY + 0.0001,
    };
    
    return this.index
      .search(buffered)
      .map(item => item.feature)
      .filter(f => f.id !== feature.id);
  }

  private extractCoordinates(geometry: any): number[][] {
    if (geometry.type === 'LineString') {
      return geometry.coordinates;
    } else if (geometry.type === 'MultiLineString') {
      return geometry.coordinates.flat();
    }
    return [];
  }

  private calculateBounds(coords: number[][]): { minX: number; minY: number; maxX: number; maxY: number } {
    if (coords.length === 0) {
      return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    for (const coord of coords) {
      if (coord.length >= 2) {
        const x = coord[0];
        const y = coord[1];
        if (x !== undefined && y !== undefined) {
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x);
          maxY = Math.max(maxY, y);
        }
      }
    }
    
    return { minX, minY, maxX, maxY };
  }
}

/**
 * Optimized conflict detection using spatial indexing
 * Replaces expensive O(n²) nested loops
 */
const useOptimizedConflictDetection = (features: UtilityFeature[]) => {
  const [conflicts, setConflicts] = useState<any[]>([]);
  const spatialIndexRef = useRef(new SpatialIndex());
  
  // Debounced conflict detection to avoid excessive calculations
  const detectConflicts = useCallback(
    debounce((features: UtilityFeature[]) => {
      if (features.length === 0) {
        setConflicts([]);
        return;
      }

      // Update spatial index
      spatialIndexRef.current.updateFeatures(features);
      
      const newConflicts: any[] = [];
      const processedPairs = new Set<string>();
      
      for (const feature of features) {
        const candidates = spatialIndexRef.current.findPotentialConflicts(feature);
        
        for (const candidate of candidates) {
          const pairKey = [feature.id, candidate.id].sort().join('-');
          if (processedPairs.has(pairKey)) continue;
          
          processedPairs.add(pairKey);
          
          // Perform detailed geometric conflict check only on candidates
          const conflict = checkDetailedConflict(feature, candidate);
          if (conflict) {
            newConflicts.push(conflict);
          }
        }
      }
      
      setConflicts(newConflicts);
    }, 300), // 300ms debounce
    []
  );
  
  useEffect(() => {
    detectConflicts(features);
  }, [features, detectConflicts]);
  
  return conflicts;
};

/**
 * Detailed geometric conflict detection (only called on spatial candidates)
 */
function checkDetailedConflict(feature1: UtilityFeature, feature2: UtilityFeature) {
  // Implementation would use more sophisticated geometric algorithms
  // This is just a placeholder for the optimized version
  return {
    id: `${feature1.id}-${feature2.id}`,
    feature1,
    feature2,
    type: 'proximity',
    severity: 'medium',
  };
}

/**
 * Optimized Utility Map 2D Component
 * Split from the original 1183-line monster component
 */
const UtilityMap2DOptimized: React.FC<OptimizedUtilityMap2DProps> = memo(({
  projectId,
  features,
  onFeatureSelect,
  onConflictDetected,
}) => {
  // Memoize expensive calculations
  const featuresById = useMemo(() => {
    return new Map(features.map(f => [f.id, f]));
  }, [features]);
  
  // Optimized conflict detection
  const conflicts = useOptimizedConflictDetection(features);
  
  // Throttled feature selection to prevent excessive re-renders
  const handleFeatureSelect = useCallback(
    throttle((feature: UtilityFeature) => {
      onFeatureSelect?.(feature);
    }, 100),
    [onFeatureSelect]
  );
  
  // Batch conflict notifications
  useEffect(() => {
    if (conflicts.length > 0) {
      onConflictDetected?.(conflicts);
    }
  }, [conflicts, onConflictDetected]);
  
  return (
    <div className="relative h-full w-full">
      <OpenLayersMap
        projectId={projectId}
        onSave={(features: any) => {
          // Handle feature save if needed
        }}
        initialFeatures={features as any}
      />
      
      {/* Conflict detection is handled internally in the map component */}
    </div>
  );
});

UtilityMap2DOptimized.displayName = 'UtilityMap2DOptimized';

export default UtilityMap2DOptimized;