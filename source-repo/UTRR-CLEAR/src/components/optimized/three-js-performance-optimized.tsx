'use client';

import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { throttle } from 'lodash';
import * as THREE from 'three';

interface OptimizedThreeJSProps {
  utilities: Array<{
    id: string;
    geometry: any;
    utility_type: string;
    installation_depth?: number;
  }>;
  conflicts: Array<{
    id: string;
    utility1_id: string;
    utility2_id: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
}

/**
 * Optimized geometry manager that prevents unnecessary recreation
 */
class OptimizedGeometryManager {
  private geometryCache = new Map<string, THREE.BufferGeometry>();
  private materialCache = new Map<string, THREE.Material>();
  
  getUtilityGeometry(utilityType: string, quality: number): THREE.BufferGeometry {
    const key = `${utilityType}-${quality}`;
    
    if (!this.geometryCache.has(key)) {
      const geometry = this.createUtilityGeometry(utilityType, quality);
      this.geometryCache.set(key, geometry);
    }
    
    return this.geometryCache.get(key)!;
  }
  
  getUtilityMaterial(utilityType: string): THREE.Material {
    if (!this.materialCache.has(utilityType)) {
      const material = this.createUtilityMaterial(utilityType);
      this.materialCache.set(utilityType, material);
    }
    
    return this.materialCache.get(utilityType)!;
  }
  
  private createUtilityGeometry(utilityType: string, quality: number): THREE.BufferGeometry {
    switch (utilityType) {
      case 'water':
        return new THREE.CylinderGeometry(0.15, 0.15, 1, Math.max(8, quality));
      case 'gas':
        return new THREE.CylinderGeometry(0.1, 0.1, 1, Math.max(6, quality));
      case 'electric':
        return new THREE.BoxGeometry(0.1, 0.1, 1);
      case 'telecom':
        return new THREE.CylinderGeometry(0.05, 0.05, 1, Math.max(4, quality));
      default:
        return new THREE.CylinderGeometry(0.1, 0.1, 1, Math.max(6, quality));
    }
  }
  
  private createUtilityMaterial(utilityType: string): THREE.Material {
    const colors = {
      water: '#1E40AF',     // Blue
      gas: '#EAB308',       // Yellow
      electric: '#DC2626',  // Red
      telecom: '#16A34A',   // Green
      sewer: '#8B5CF6',     // Purple
    };
    
    return new THREE.MeshLambertMaterial({
      color: colors[utilityType as keyof typeof colors] || '#6B7280',
      transparent: true,
      opacity: 0.8,
    });
  }
  
  dispose() {
    this.geometryCache.forEach(geometry => geometry.dispose());
    this.materialCache.forEach(material => material.dispose());
    this.geometryCache.clear();
    this.materialCache.clear();
  }
}

/**
 * Optimized conflict animation system
 * Batches animations and reduces frame-by-frame calculations
 */
class ConflictAnimationSystem {
  private activeAnimations = new Map<string, {
    meshes: THREE.Mesh[];
    startTime: number;
    duration: number;
    type: 'pulse' | 'highlight';
  }>();
  
  addConflictAnimation(
    conflictId: string, 
    meshes: THREE.Mesh[], 
    type: 'pulse' | 'highlight' = 'pulse'
  ) {
    this.activeAnimations.set(conflictId, {
      meshes,
      startTime: performance.now(),
      duration: 2000, // 2 seconds
      type,
    });
  }
  
  removeConflictAnimation(conflictId: string) {
    const animation = this.activeAnimations.get(conflictId);
    if (animation) {
      // Reset mesh properties
      animation.meshes.forEach(mesh => {
        if (mesh.material instanceof THREE.Material) {
          mesh.material.opacity = 0.8;
        }
      });
      this.activeAnimations.delete(conflictId);
    }
  }
  
  // Update all animations in a single batch (called max 30fps)
  updateAnimations(currentTime: number) {
    this.activeAnimations.forEach((animation, conflictId) => {
      const elapsed = currentTime - animation.startTime;
      const progress = Math.min(elapsed / animation.duration, 1);
      
      if (progress >= 1) {
        this.removeConflictAnimation(conflictId);
        return;
      }
      
      const intensity = this.calculateAnimationIntensity(progress, animation.type);
      
      animation.meshes.forEach(mesh => {
        if (mesh.material instanceof THREE.Material) {
          mesh.material.opacity = 0.8 + (intensity * 0.2);
        }
      });
    });
  }
  
  private calculateAnimationIntensity(progress: number, type: 'pulse' | 'highlight'): number {
    switch (type) {
      case 'pulse':
        return Math.sin(progress * Math.PI * 6) * Math.pow(1 - progress, 2);
      case 'highlight':
        return Math.sin(progress * Math.PI * 2) * (1 - progress);
    }
  }
  
  dispose() {
    this.activeAnimations.clear();
  }
}

/**
 * Level of Detail (LOD) system for performance optimization
 */
const useLevelOfDetail = (camera: THREE.Camera) => {
  const [lodLevel, setLodLevel] = useState<'high' | 'medium' | 'low'>('medium');
  
  // Throttled LOD calculation
  const updateLOD = useCallback(
    throttle((cameraPosition: THREE.Vector3) => {
      const distance = cameraPosition.length();
      
      if (distance < 50) {
        setLodLevel('high');
      } else if (distance < 200) {
        setLodLevel('medium');
      } else {
        setLodLevel('low');
      }
    }, 200), // Update LOD max 5 times per second
    []
  );
  
  useFrame(() => {
    updateLOD(camera.position);
  });
  
  return lodLevel;
};

/**
 * Optimized utility mesh component
 */
const OptimizedUtilityMesh: React.FC<{
  utility: any;
  lodLevel: 'high' | 'medium' | 'low';
  geometryManager: OptimizedGeometryManager;
}> = React.memo(({ utility, lodLevel, geometryManager }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  // Memoize geometry and material based on LOD
  const { geometry, material } = useMemo(() => {
    const qualityMap = { high: 16, medium: 8, low: 4 };
    const quality = qualityMap[lodLevel];
    
    return {
      geometry: geometryManager.getUtilityGeometry(utility.utility_type, quality),
      material: geometryManager.getUtilityMaterial(utility.utility_type),
    };
  }, [utility.utility_type, lodLevel, geometryManager]);
  
  // Memoize position calculations
  const position = useMemo(() => {
    const coords = utility.geometry?.coordinates || [0, 0];
    return [coords[0], -(utility.installation_depth || 0), coords[1]] as [number, number, number];
  }, [utility.geometry, utility.installation_depth]);
  
  return (
    <mesh 
      ref={meshRef} 
      geometry={geometry} 
      material={material} 
      position={position}
      userData={{ utilityId: utility.id }}
    />
  );
});

OptimizedUtilityMesh.displayName = 'OptimizedUtilityMesh';

/**
 * Main optimized Three.js visualization component
 */
const OptimizedThreeJSVisualization: React.FC<OptimizedThreeJSProps> = ({
  utilities,
  conflicts,
}) => {
  const { camera } = useThree();
  const lodLevel = useLevelOfDetail(camera);
  
  // Singleton instances for performance
  const geometryManagerRef = useRef(new OptimizedGeometryManager());
  const animationSystemRef = useRef(new ConflictAnimationSystem());
  
  // Memoize utility meshes to prevent unnecessary re-renders
  const utilityMeshes = useMemo(() => {
    return utilities.map(utility => (
      <OptimizedUtilityMesh
        key={utility.id}
        utility={utility}
        lodLevel={lodLevel}
        geometryManager={geometryManagerRef.current}
      />
    ));
  }, [utilities, lodLevel]);
  
  // Update conflict animations (max 30fps)
  const lastUpdateRef = useRef(0);
  useFrame(() => {
    const now = performance.now();
    if (now - lastUpdateRef.current > 33) { // ~30 FPS
      animationSystemRef.current.updateAnimations(now);
      lastUpdateRef.current = now;
    }
  });
  
  // Handle conflict updates
  useEffect(() => {
    // Add new conflict animations
    conflicts.forEach(conflict => {
      const mesh1 = utilities.find((u: any) => u.id === conflict.utility1_id);
      const mesh2 = utilities.find((u: any) => u.id === conflict.utility2_id);
      
      if (mesh1 && mesh2) {
        animationSystemRef.current.addConflictAnimation(
          conflict.id,
          [], // Mesh references would be passed here
          conflict.severity === 'critical' ? 'highlight' : 'pulse'
        );
      }
    });
  }, [conflicts, utilities]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      geometryManagerRef.current.dispose();
      animationSystemRef.current.dispose();
    };
  }, []);
  
  return (
    <group>
      {utilityMeshes}
      
      {/* Optimized lighting - static setup */}
      <ambientLight intensity={0.4} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={0.6} 
        castShadow={lodLevel === 'high'} // Only enable shadows at high LOD
      />
    </group>
  );
};

export default React.memo(OptimizedThreeJSVisualization);