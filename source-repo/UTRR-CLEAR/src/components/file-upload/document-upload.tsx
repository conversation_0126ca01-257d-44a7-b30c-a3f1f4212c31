'use client';

import { useCallback, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import {
  AlertCircle,
  CheckCircle,
  FileArchive,
  FileImage,
  FileSpreadsheet,
  FileText,
  Upload,
  X,
} from 'lucide-react';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  url?: string;
}

interface DocumentUploadProps {
  projectId?: string;
  allowedTypes?: string[];
  maxFileSize?: number; // in MB
  onUploadComplete?: (files: UploadedFile[]) => void;
}

export function DocumentUpload({
  projectId,
  allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.dwg', '.jpg', '.png'],
  maxFileSize = 10,
  onUploadComplete,
}: DocumentUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const getFileIcon = (type: string) => {
    if (type.includes('image')) return <FileImage className="h-5 w-5" />;
    if (type.includes('spreadsheet') || type.includes('excel'))
      return <FileSpreadsheet className="h-5 w-5" />;
    if (type.includes('zip') || type.includes('archive'))
      return <FileArchive className="h-5 w-5" />;
    return <FileText className="h-5 w-5" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const simulateUpload = (file: UploadedFile) => {
    const interval = setInterval(() => {
      setFiles((prev) =>
        prev.map((f: any) => {
          if (f.id === file.id) {
            if (f.progress >= 100) {
              clearInterval(interval);
              return { ...f, status: 'success' as const };
            }
            return { ...f, progress: Math.min(f.progress + 10, 100) };
          }
          return f;
        })
      );
    }, 200);
  };

  const handleFiles = useCallback(
    (fileList: FileList) => {
      const newFiles: UploadedFile[] = Array.from(fileList).map((file: any) => ({
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        progress: 0,
        status: 'uploading' as const,
      }));

      setFiles((prev) => [...prev, ...newFiles]);

      // Simulate upload for each file
      newFiles.forEach((file: any) => {
        if (file.size > maxFileSize * 1024 * 1024) {
          setFiles((prev) =>
            prev.map((f: any) =>
              f.id === file.id ? { ...f, status: 'error' as const, progress: 0 } : f
            )
          );
        } else {
          simulateUpload(file);
        }
      });
    },
    [maxFileSize]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      if (e.dataTransfer.files) {
        handleFiles(e.dataTransfer.files);
      }
    },
    [handleFiles]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (fileId: string) => {
    setFiles((prev) => prev.filter((f: any) => f.id !== fileId));
  };

  const successFiles = files.filter((f: any) => f.status === 'success');
  const totalFiles = files.length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Upload className="mr-2 h-5 w-5" />
          Document Upload
        </CardTitle>
        <CardDescription>Upload project documents, drawings, and related files</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">Drop files here or click to browse</h3>
          <p className="text-sm text-gray-500 mb-4">
            Supports: {allowedTypes.join(', ')} (Max {maxFileSize}MB per file)
          </p>
          <input
            type="file"
            multiple
            accept={allowedTypes.join(',')}
            onChange={handleFileInput}
            className="hidden"
            id="file-upload"
          />
          <label htmlFor="file-upload">
            <Button asChild>
              <span>Select Files</span>
            </Button>
          </label>
        </div>

        {/* Upload Progress */}
        {totalFiles > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">
                Uploading Files ({successFiles.length}/{totalFiles})
              </h4>
              <Badge variant="outline">
                {Math.round((successFiles.length / totalFiles) * 100)}% Complete
              </Badge>
            </div>

            <div className="space-y-2">
              {files.map((file: any) => (
                <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0">{getFileIcon(file.type)}</div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{file.name}</p>
                    <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                    {file.status === 'uploading' && (
                      <Progress value={file.progress} className="mt-1" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {file.status === 'success' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {file.status === 'error' && <AlertCircle className="h-5 w-5 text-red-500" />}
                    <Button variant="ghost" size="sm" onClick={() => removeFile(file.id)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upload Statistics */}
        {successFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <div>
                <p className="font-medium text-green-800">Upload Complete!</p>
                <p className="text-sm text-green-600">
                  {successFiles.length} file(s) uploaded successfully
                  {projectId && ` to project ${projectId}`}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Supported formats: PDF, Word, Excel, AutoCAD, Images</p>
          <p>• Maximum file size: {maxFileSize}MB per file</p>
          <p>• Files are automatically organized by project and date</p>
          <p>• All uploads are virus scanned and encrypted</p>
        </div>
      </CardContent>
    </Card>
  );
}
