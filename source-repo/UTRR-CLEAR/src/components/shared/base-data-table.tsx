'use client';

import React, { memo, useMemo, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Badge } from '~/components/ui/badge';
import { Skeleton } from '~/components/ui/skeleton';
import { 
  ChevronLeft, 
  ChevronRight, 
  Search, 
  Filter,
  SortAsc,
  SortDesc,
} from 'lucide-react';

export interface Column<T> {
  key: keyof T;
  header: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: T[keyof T], row: T) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export interface BaseDataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  filterable?: boolean;
  sortable?: boolean;
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  onRowClick?: (row: T) => void;
  emptyMessage?: string;
  className?: string;
  rowClassName?: (row: T) => string;
}

// Type guard for Date objects
function isDate(value: unknown): value is Date {
  return value instanceof Date || Object.prototype.toString.call(value) === '[object Date]';
}

/**
 * Reusable data table component to reduce duplication across the app
 * Replaces multiple similar table implementations with a single configurable component
 */
function BaseDataTableComponent<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  searchable = false,
  searchPlaceholder = 'Search...',
  filterable = false,
  sortable = true,
  pagination,
  onRowClick,
  emptyMessage = 'No data available',
  className = '',
  rowClassName,
}: BaseDataTableProps<T>) {
  const [search, setSearch] = useState('');
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filters, setFilters] = useState<Record<string, string>>({});

  // Memoized filtered and sorted data
  const processedData = useMemo(() => {
    let filtered = [...data];

    // Apply search filter
    if (search && searchable) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(row =>
        columns.some((col: any) => {
          const value = row[col.key];
          return value?.toString().toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply column filters
    if (filterable) {
      Object.entries(filters).forEach(([columnKey, filterValue]) => {
        if (filterValue) {
          filtered = filtered.filter(row => {
            const value = row[columnKey];
            return value?.toString().toLowerCase().includes(filterValue.toLowerCase());
          });
        }
      });
    }

    // Apply sorting
    if (sortColumn && sortable) {
      filtered.sort((a: any, b: any) => {
        const aVal = a[sortColumn];
        const bVal = b[sortColumn];
        
        if (aVal === bVal) return 0;
        
        let comparison = 0;
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          comparison = aVal.localeCompare(bVal);
        } else if (typeof aVal === 'number' && typeof bVal === 'number') {
          comparison = aVal - bVal;
        } else if (isDate(aVal) && isDate(bVal)) {
          comparison = aVal.getTime() - bVal.getTime();
        } else {
          comparison = String(aVal).localeCompare(String(bVal));
        }
        
        return sortDirection === 'desc' ? -comparison : comparison;
      });
    }

    return filtered;
  }, [data, search, sortColumn, sortDirection, filters, columns, searchable, filterable, sortable]);

  const handleSort = (column: keyof T) => {
    if (!sortable) return;
    
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleFilterChange = (columnKey: string, value: string) => {
    setFilters(prev => ({ ...prev, [columnKey]: value }));
  };

  const renderCell = (column: Column<T>, row: T) => {
    const value = row[column.key];
    
    if (column.render) {
      return column.render(value, row);
    }
    
    // Default rendering based on value type
    if (typeof value === 'boolean') {
      return <Badge variant={value ? 'default' : 'secondary'}>{value ? 'Yes' : 'No'}</Badge>;
    }
    
    if (isDate(value)) {
      return value.toLocaleDateString();
    }
    
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    
    return value?.toString() || '-';
  };

  const LoadingSkeleton = () => (
    <TableBody>
      {Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={index}>
          {columns.map((column, colIndex) => (
            <TableCell key={colIndex}>
              <Skeleton className="h-4 w-full" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  );

  const EmptyState = () => (
    <TableBody>
      <TableRow>
        <TableCell colSpan={columns.length} className="text-center py-8 text-muted-foreground">
          {emptyMessage}
        </TableCell>
      </TableRow>
    </TableBody>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Filters */}
      {(searchable || filterable) && (
        <div className="flex flex-col sm:flex-row gap-4">
          {searchable && (
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={search}
                onChange={(e: any) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          )}
          
          {filterable && (
            <div className="flex gap-2">
              {columns
                .filter(col => col.filterable)
                .map(column => (
                  <Select
                    key={String(column.key)}
                    value={filters[String(column.key)] || ''}
                    onValueChange={(value) => handleFilterChange(String(column.key), value)}
                  >
                    <SelectTrigger className="w-[180px]">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder={`Filter ${column.header}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All {column.header}</SelectItem>
                      {/* Add dynamic filter options based on data */}
                    </SelectContent>
                  </Select>
                ))}
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column: any) => (
                <TableHead
                  key={String(column.key)}
                  className={`${column.width ? `w-${column.width}` : ''} ${
                    column.align === 'center' ? 'text-center' : 
                    column.align === 'right' ? 'text-right' : 'text-left'
                  }`}
                >
                  {column.sortable !== false && sortable ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 p-0 font-semibold"
                      onClick={() => handleSort(column.key)}
                    >
                      {column.header}
                      {sortColumn === column.key && (
                        sortDirection === 'asc' ? 
                          <SortAsc className="ml-2 h-4 w-4" /> : 
                          <SortDesc className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  ) : (
                    <span className="font-semibold">{column.header}</span>
                  )}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>

          {loading ? (
            <LoadingSkeleton />
          ) : processedData.length === 0 ? (
            <EmptyState />
          ) : (
            <TableBody>
              {processedData.map((row, index) => (
                <TableRow
                  key={index}
                  className={`${onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''} ${
                    rowClassName ? rowClassName(row) : ''
                  }`}
                  onClick={() => onRowClick?.(row)}
                >
                  {columns.map((column: any) => (
                    <TableCell
                      key={String(column.key)}
                      className={
                        column.align === 'center' ? 'text-center' : 
                        column.align === 'right' ? 'text-right' : 'text-left'
                      }
                    >
                      {renderCell(column, row)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          )}
        </Table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              Rows per page:
            </span>
            <Select
              value={pagination.pageSize.toString()}
              onValueChange={(value) => pagination.onPageSizeChange(parseInt(value))}
            >
              <SelectTrigger className="w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              Page {pagination.page} of {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={pagination.page >= Math.ceil(pagination.total / pagination.pageSize)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export const BaseDataTable = memo(BaseDataTableComponent) as typeof BaseDataTableComponent;