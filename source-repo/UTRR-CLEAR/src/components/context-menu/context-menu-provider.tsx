"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from "react";
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuSeparator, ContextMenuSub, ContextMenuSubContent, ContextMenuSubTrigger, ContextMenuTrigger } from "~/components/ui/context-menu";
import { useComments } from "~/components/comments/comment-provider";
import { Copy, MessageSquare, Edit, Trash, Share, Download, Eye, Lock, Unlock } from "lucide-react";
import { useToast } from "~/hooks/use-toast";

interface ContextMenuState {
  x: number;
  y: number;
  entityType: string;
  entityId: string;
  entityName?: string;
  data?: any;
  securityLevel?: "high" | "medium" | "low";
}

interface ContextMenuProviderProps {
  children: React.ReactNode;
}

const ContextMenuContext = createContext<{
  registerContextMenu: (
    element: HTMLElement,
    entityType: string,
    entityId: string,
    entityName?: string,
    data?: any,
    securityLevel?: "high" | "medium" | "low"
  ) => void;
  unregisterContextMenu: (element: HTMLElement) => void;
} | null>(null);

export const useContextMenu = () => {
  const context = useContext(ContextMenuContext);
  if (!context) {
    throw new Error("useContextMenu must be used within a ContextMenuProvider");
  }
  return context;
};

export function ContextMenuProvider({ children }: ContextMenuProviderProps) {
  const { openCommentDrawer } = useComments();
  const { toast } = useToast();
  const [menuState, setMenuState] = useState<ContextMenuState | null>(null);
  const [registeredElements] = useState(new WeakMap<HTMLElement, ContextMenuState>());

  const handleContextMenu = useCallback((e: MouseEvent) => {
    const target = e.target as HTMLElement;
    let element: HTMLElement | null = target;

    // Walk up the DOM tree to find a registered element
    while (element) {
      if (registeredElements.has(element)) {
        e.preventDefault();
        const state = registeredElements.get(element)!;
        setMenuState({
          ...state,
          x: e.clientX,
          y: e.clientY,
        });
        return;
      }
      element = element.parentElement;
    }
  }, [registeredElements]);

  const registerContextMenu = useCallback(
    (
      element: HTMLElement,
      entityType: string,
      entityId: string,
      entityName?: string,
      data?: any,
      securityLevel: "high" | "medium" | "low" = "low"
    ) => {
      registeredElements.set(element, {
        x: 0,
        y: 0,
        entityType,
        entityId,
        entityName,
        data,
        securityLevel,
      });

      // Apply security class based on level
      if (securityLevel === "high") {
        element.classList.add("select-none", "no-copy");
        element.style.userSelect = "none";
        element.style.webkitUserSelect = "none";
      } else if (securityLevel === "medium") {
        element.classList.add("select-text");
      }
    },
    [registeredElements]
  );

  const unregisterContextMenu = useCallback(
    (element: HTMLElement) => {
      registeredElements.delete(element);
    },
    [registeredElements]
  );

  useEffect(() => {
    document.addEventListener("contextmenu", handleContextMenu);
    return () => {
      document.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [handleContextMenu]);

  const handleCopy = () => {
    if (menuState?.data?.text) {
      navigator.clipboard.writeText(menuState.data.text);
      toast({
        title: "Copied",
        description: "Text copied to clipboard",
      });
    }
  };

  const handleEdit = () => {
    if (menuState?.data?.onEdit) {
      menuState.data.onEdit();
    }
  };

  const handleDelete = () => {
    if (menuState?.data?.onDelete) {
      menuState.data.onDelete();
    }
  };

  const handleShare = () => {
    if (menuState?.data?.onShare) {
      menuState.data.onShare();
    }
  };

  const handleDownload = () => {
    if (menuState?.data?.onDownload) {
      menuState.data.onDownload();
    }
  };

  const handleView = () => {
    if (menuState?.data?.onView) {
      menuState.data.onView();
    }
  };

  return (
    <ContextMenuContext.Provider value={{ registerContextMenu, unregisterContextMenu }}>
      {children}
      {menuState && (
        <div
          style={{
            position: "fixed",
            left: menuState.x,
            top: menuState.y,
            zIndex: 9999,
          }}
          onContextMenu={(e) => e.preventDefault()}
        >
          <ContextMenuContent
            onEscapeKeyDown={() => setMenuState(null)}
            onPointerDownOutside={() => setMenuState(null)}
          >
            <ContextMenuItem
              onClick={() => {
                openCommentDrawer(menuState.entityType, menuState.entityId, menuState.entityName);
                setMenuState(null);
              }}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Add Comment
            </ContextMenuItem>
            <ContextMenuSeparator />
            
            {menuState.securityLevel !== "high" && menuState.data?.text && (
              <ContextMenuItem onClick={handleCopy}>
                <Copy className="mr-2 h-4 w-4" />
                Copy
              </ContextMenuItem>
            )}
            
            {menuState.data?.onView && (
              <ContextMenuItem onClick={handleView}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </ContextMenuItem>
            )}
            
            {menuState.data?.onEdit && (
              <ContextMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </ContextMenuItem>
            )}
            
            {menuState.data?.onShare && (
              <ContextMenuItem onClick={handleShare}>
                <Share className="mr-2 h-4 w-4" />
                Share
              </ContextMenuItem>
            )}
            
            {menuState.data?.onDownload && (
              <ContextMenuItem onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </ContextMenuItem>
            )}
            
            {menuState.data?.onDelete && (
              <>
                <ContextMenuSeparator />
                <ContextMenuItem onClick={handleDelete} className="text-destructive">
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </ContextMenuItem>
              </>
            )}
            
            {menuState.securityLevel === "high" && (
              <>
                <ContextMenuSeparator />
                <ContextMenuItem disabled>
                  <Lock className="mr-2 h-4 w-4" />
                  Protected Content
                </ContextMenuItem>
              </>
            )}
          </ContextMenuContent>
        </div>
      )}
    </ContextMenuContext.Provider>
  );
}