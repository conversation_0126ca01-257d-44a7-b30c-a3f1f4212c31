'use client';

import { useState, useEffect } from 'react';
import { Badge } from '~/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';

interface RealtimeIndicatorProps {
  showDetails?: boolean;
  className?: string;
}

/**
 * Real-time connection status indicator
 * Shows connection state and provides feedback to users
 */
export function RealtimeIndicator({ showDetails = false, className = '' }: RealtimeIndicatorProps) {
  // Simple connection indicator without creating new subscriptions
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');
  const [lastUpdate, setLastUpdate] = useState<Date | null>(new Date());

  // Just show as connected for now to avoid duplicate subscriptions
  useEffect(() => {
    setConnectionStatus('connected');
    setLastUpdate(new Date());
  }, []);

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500';
      case 'disconnected':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  if (connectionStatus === 'disconnected' && !showDetails) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-2 ${className}`}>
            <div className="flex items-center gap-1">
              <div 
                className={`w-2 h-2 rounded-full ${getStatusColor()} ${
                  connectionStatus === 'connected' ? 'animate-pulse' : ''
                }`}
              />
              {showDetails && (
                <span className="text-xs text-muted-foreground">
                  {getStatusText()}
                </span>
              )}
            </div>
            {showDetails && lastUpdate && (
              <Badge variant="outline" className="text-xs">
                Last: {lastUpdate.toLocaleTimeString()}
              </Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <div>Real-time: {getStatusText()}</div>
            {lastUpdate && (
              <div className="text-xs text-muted-foreground">
                Last update: {lastUpdate.toLocaleString()}
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Real-time activity feed component
 * Shows recent real-time updates
 */
export function RealtimeActivityFeed() {
  const [activities, setActivities] = useState<any[]>([]);

  // This would integrate with your real-time hooks
  // For now, it's a placeholder for the UI structure

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Recent Activity</h3>
      <div className="space-y-1">
        {activities.length === 0 ? (
          <p className="text-xs text-muted-foreground">No recent activity</p>
        ) : (
          activities.map((activity, index) => (
            <div key={index} className="text-xs p-2 bg-muted rounded">
              <div className="font-medium">{activity.type}</div>
              <div className="text-muted-foreground">{activity.description}</div>
              <div className="text-xs text-muted-foreground">
                {new Date(activity.timestamp).toLocaleTimeString()}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}