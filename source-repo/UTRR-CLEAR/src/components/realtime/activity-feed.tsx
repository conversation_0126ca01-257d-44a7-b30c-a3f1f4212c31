'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Activity, AlertCircle, Clock, FileText, MapPin, RefreshCw, User } from 'lucide-react';
import { useRealtime } from '~/lib/real-time-manager';
import type { RealtimeMessage } from '~/lib/real-time-manager';

interface ActivityItem {
  id: string;
  type: 'project_update' | 'conflict_detected' | 'document_upload' | 'user_action';
  title: string;
  description: string;
  timestamp: Date;
  user: {
    name: string;
    avatar?: string;
  };
  project?: {
    id: string;
    name: string;
  };
  priority: 'low' | 'medium' | 'high';
}

// Convert real-time message to activity item
const convertMessageToActivity = (message: RealtimeMessage): ActivityItem => {
  const data = message.data as any;
  
  switch (message.event) {
    case 'project:created':
      return {
        id: message.id,
        type: 'project_update',
        title: 'New Project Created',
        description: `Project "${data.name}" has been created`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        project: { id: data.id, name: data.name },
        priority: 'medium',
      };
    
    case 'project:updated':
      return {
        id: message.id,
        type: 'project_update',
        title: 'Project Updated',
        description: `Project "${data.name}" has been updated`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        project: { id: data.id, name: data.name },
        priority: 'medium',
      };
    
    case 'conflict:detected':
      return {
        id: message.id,
        type: 'conflict_detected',
        title: 'Conflict Detected',
        description: `${data.severity} severity ${data.type} conflict detected`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System Alert' },
        project: data.projectId ? { id: data.projectId, name: data.projectName || 'Unknown Project' } : undefined,
        priority: data.severity === 'HIGH' ? 'high' : data.severity === 'MEDIUM' ? 'medium' : 'low',
      };
    
    case 'conflict:resolved':
      return {
        id: message.id,
        type: 'user_action',
        title: 'Conflict Resolved',
        description: `${data.type} conflict has been resolved`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        project: data.projectId ? { id: data.projectId, name: data.projectName || 'Unknown Project' } : undefined,
        priority: 'medium',
      };
    
    case 'task:created':
      return {
        id: message.id,
        type: 'user_action',
        title: 'Task Created',
        description: `New task "${data.name}" created`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        project: data.projectId ? { id: data.projectId, name: data.projectName || 'Unknown Project' } : undefined,
        priority: data.priority === 'high' ? 'high' : data.priority === 'medium' ? 'medium' : 'low',
      };
    
    case 'task:completed':
      return {
        id: message.id,
        type: 'user_action',
        title: 'Task Completed',
        description: `Task "${data.name}" has been completed`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        project: data.projectId ? { id: data.projectId, name: data.projectName || 'Unknown Project' } : undefined,
        priority: 'medium',
      };
    
    case 'notification:new':
      return {
        id: message.id,
        type: 'user_action',
        title: data.title || 'New Notification',
        description: data.content || 'You have a new notification',
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        priority: 'low',
      };
    
    default:
      return {
        id: message.id,
        type: 'user_action',
        title: 'System Event',
        description: `Event: ${message.event}`,
        timestamp: new Date(message.timestamp),
        user: { name: 'System' },
        priority: 'low',
      };
  }
};

export function ActivityFeed({ projectId }: { projectId?: string }) {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  
  // Initialize real-time connection
  const { 
    connectionStatus, 
    lastMessage, 
    subscribe, 
    forceRefresh,
    isConnected 
  } = useRealtime({
    pollingInterval: 30000, // 30 seconds
    debug: true
  });

  // Filter activities by project if specified
  const filteredActivities = projectId
    ? activities.filter((activity: any) => activity.project?.id === projectId)
    : activities;

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'conflict_detected':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'document_upload':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'project_update':
        return <MapPin className="h-5 w-5 text-green-500" />;
      case 'user_action':
        return <User className="h-5 w-5 text-purple-500" />;
      default:
        return <Activity className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: ActivityItem['priority']) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      case 'low':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Subscribe to real-time events
  useEffect(() => {
    // Subscribe to all activity-related events
    subscribe([
      'project:updated',
      'project:created',
      'conflict:detected', 
      'conflict:resolved',
      'task:updated',
      'task:created',
      'task:completed',
      'notification:new'
    ]);
  }, [subscribe]);

  // Handle new real-time messages
  useEffect(() => {
    if (lastMessage) {
      const newActivity = convertMessageToActivity(lastMessage);
      setActivities((prev) => {
        // Avoid duplicates and keep only the latest 20 activities
        const filtered = prev.filter(activity => activity.id !== newActivity.id);
        return [newActivity, ...filtered].slice(0, 20);
      });
      setLastUpdate(new Date());
    }
  }, [lastMessage]);

  const handleRefresh = () => {
    setLastUpdate(new Date());
    forceRefresh(); // Force a real-time poll
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Activity Feed
              {projectId && ' - Project Specific'}
            </CardTitle>
            <CardDescription>Real-time updates and project activities</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' : 
                connectionStatus === 'connecting' ? 'bg-yellow-500' : 
                connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-400'
              }`} />
              <span className="capitalize">{connectionStatus}</span>
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredActivities.length === 0 ? (
            <div className="text-center py-6">
              <Activity className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              <p className="text-muted-foreground">
                {projectId ? 'No activities for this project yet' : 'No recent activities'}
              </p>
            </div>
          ) : (
            filteredActivities.map((activity: any) => (
              <div
                key={activity.id}
                className="flex space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-shrink-0 mt-1">{getActivityIcon(activity.type)}</div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="font-medium text-sm">{activity.title}</p>
                    <div className="flex items-center space-x-2">
                      <Badge variant={getPriorityColor(activity.priority)}>
                        {activity.priority}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(activity.timestamp)}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{activity.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span className="flex items-center">
                      <User className="mr-1 h-3 w-3" />
                      {activity.user.name}
                    </span>
                    {activity.project && (
                      <span className="flex items-center">
                        <MapPin className="mr-1 h-3 w-3" />
                        {activity.project.name}
                      </span>
                    )}
                    <span className="flex items-center">
                      <Clock className="mr-1 h-3 w-3" />
                      {activity.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="mt-4 pt-3 border-t text-center">
          <p className="text-xs text-muted-foreground">
            Last updated: {lastUpdate.toLocaleTimeString()}
            {isConnected && ' • Real-time updates enabled'}
            {connectionStatus === 'error' && ' • Connection error - retrying...'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
