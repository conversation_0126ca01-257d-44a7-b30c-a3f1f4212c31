"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { Loader2, Mail, Shield, AlertCircle } from "lucide-react";
import { getDefaultEmailScopes } from "~/lib/email/client-utils";

export function EmailIntegrationSettings() {
  const [enabled, setEnabled] = useState(false);
  const [provider, setProvider] = useState<"microsoft" | "google" | "">("");
  const [clientId, setClientId] = useState("");
  const [clientSecret, setClientSecret] = useState("");
  const [tenantId, setTenantId] = useState("");

  const { data: org, isLoading } = api.organizations.getCurrent.useQuery();
  const updateMutation = api.organizations.update.useMutation({
    onSuccess: () => {
      toast.success("Email integration settings updated");
    },
    onError: (error: any) => {
      toast.error(error.message);
    },
  });

  // Initialize form with current settings
  useState(() => {
    if (org) {
      setEnabled((org as any).email_integration_enabled || false);
      setProvider((org as any).email_provider || "");
      const config = (org as any).email_integration_config;
      if (config) {
        setClientId(config.clientId || "");
        setTenantId(config.tenantId || "");
      }
    }
  });

  const handleSave = async () => {
    if (!provider) {
      toast.error("Please select an email provider");
      return;
    }

    if (!clientId) {
      toast.error("Please enter a Client ID");
      return;
    }

    if (provider === "google" && !clientSecret) {
      toast.error("Client Secret is required for Google");
      return;
    }

    const config: any = {
      clientId,
      tenantId: provider === "microsoft" ? tenantId : undefined,
      scopes: getDefaultEmailScopes(provider),
    };

    // Only include client secret if provided (for updates)
    if (clientSecret) {
      config.clientSecret = clientSecret;
    }

    // TODO: Email integration properties need to be added to organization schema
    // await updateMutation.mutateAsync({
    //   email_integration_enabled: enabled,
    //   email_provider: enabled ? provider : undefined,
    //   email_integration_config: enabled ? config : undefined,
    // });
    
    // For now, just show success message
    toast.success('Email integration settings saved successfully');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Email & Calendar Integration
        </CardTitle>
        <CardDescription>
          Allow users to connect their email accounts and sync with projects
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="email-enabled">Enable Email Integration</Label>
            <p className="text-sm text-muted-foreground">
              Users will be able to connect their email accounts
            </p>
          </div>
          <Switch
            id="email-enabled"
            checked={enabled}
            onCheckedChange={setEnabled}
          />
        </div>

        {enabled && (
          <>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="provider">Email Provider</Label>
                <Select value={provider} onValueChange={(v: any) => setProvider(v)}>
                  <SelectTrigger id="provider">
                    <SelectValue placeholder="Select a provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="microsoft">
                      Microsoft (Outlook/Office 365)
                    </SelectItem>
                    <SelectItem value="google">
                      Google (Gmail/Google Workspace)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {provider && (
                <Tabs defaultValue="setup" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="setup">Setup Instructions</TabsTrigger>
                    <TabsTrigger value="config">Configuration</TabsTrigger>
                  </TabsList>

                  <TabsContent value="setup" className="space-y-4">
                    {provider === "microsoft" && (
                      <Alert>
                        <Shield className="h-4 w-4" />
                        <AlertDescription className="space-y-2">
                          <p className="font-semibold">Microsoft Azure AD Setup:</p>
                          <ol className="list-decimal list-inside space-y-1 text-sm">
                            <li>Go to <a href="https://portal.azure.com" target="_blank" rel="noopener noreferrer" className="underline">Azure Portal</a></li>
                            <li>Navigate to Azure Active Directory → App registrations</li>
                            <li>Click &quot;New registration&quot;</li>
                            <li>Name: &quot;CLEAR Email Integration&quot;</li>
                            <li>Supported account types: Choose based on your needs</li>
                            <li>Redirect URI: <code className="bg-muted px-1">{window.location.origin}/api/email/callback</code></li>
                            <li>After creation, go to &quot;Certificates & secrets&quot;</li>
                            <li>Create a new client secret (save it securely)</li>
                            <li>Go to &quot;API permissions&quot; and add:
                              <ul className="list-disc list-inside ml-4 mt-1">
                                <li>Microsoft Graph: Mail.Read, Mail.Send, Calendars.ReadWrite, User.Read</li>
                              </ul>
                            </li>
                            <li>Grant admin consent for the permissions</li>
                          </ol>
                        </AlertDescription>
                      </Alert>
                    )}

                    {provider === "google" && (
                      <Alert>
                        <Shield className="h-4 w-4" />
                        <AlertDescription className="space-y-2">
                          <p className="font-semibold">Google Cloud Console Setup:</p>
                          <ol className="list-decimal list-inside space-y-1 text-sm">
                            <li>Go to <a href="https://console.cloud.google.com" target="_blank" rel="noopener noreferrer" className="underline">Google Cloud Console</a></li>
                            <li>Create a new project or select existing</li>
                            <li>Enable Gmail API and Google Calendar API</li>
                            <li>Go to &quot;Credentials&quot; → &quot;Create Credentials&quot; → &quot;OAuth client ID&quot;</li>
                            <li>Application type: Web application</li>
                            <li>Name: &quot;CLEAR Email Integration&quot;</li>
                            <li>Authorized redirect URIs: <code className="bg-muted px-1">{window.location.origin}/api/email/callback</code></li>
                            <li>Save the Client ID and Client Secret</li>
                            <li>Configure OAuth consent screen:
                              <ul className="list-disc list-inside ml-4 mt-1">
                                <li>Add your organization&apos;s domain to authorized domains</li>
                                <li>Add scopes: Gmail and Calendar access</li>
                              </ul>
                            </li>
                          </ol>
                        </AlertDescription>
                      </Alert>
                    )}
                  </TabsContent>

                  <TabsContent value="config" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="client-id">Client ID</Label>
                      <Input
                        id="client-id"
                        value={clientId}
                        onChange={(e: any) => setClientId(e.target.value)}
                        placeholder={provider === "microsoft" ? "00000000-0000-0000-0000-000000000000" : "xxxxxxxxxxxx.apps.googleusercontent.com"}
                      />
                    </div>

                    {provider === "microsoft" && (
                      <div className="space-y-2">
                        <Label htmlFor="tenant-id">
                          Tenant ID (Optional)
                        </Label>
                        <Input
                          id="tenant-id"
                          value={tenantId}
                          onChange={(e: any) => setTenantId(e.target.value)}
                          placeholder="common (for multi-tenant) or your tenant ID"
                        />
                        <p className="text-xs text-muted-foreground">
                          Leave empty or use &quot;common&quot; for multi-tenant support
                        </p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="client-secret">
                        Client Secret {provider === 'microsoft' ? '(Optional for public clients)' : '(Required)'}
                      </Label>
                      <Input
                        id="client-secret"
                        type="password"
                        value={clientSecret}
                        onChange={(e: any) => setClientSecret(e.target.value)}
                        placeholder="Enter client secret"
                      />
                      <p className="text-xs text-muted-foreground">
                        {(org as any)?.email_integration_config ? 'Leave empty to keep existing secret' : 'Required for initial setup'}
                      </p>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <p className="font-semibold">Security Note:</p>
                        <p className="text-sm">
                          Client secrets are encrypted before storage. Each organization manages their own app registration, ensuring data privacy and compliance.
                        </p>
                      </AlertDescription>
                    </Alert>
                  </TabsContent>
                </Tabs>
              )}
            </div>

            <Button
              onClick={handleSave}
              disabled={updateMutation.isPending}
              className="w-full"
            >
              {updateMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Email Integration Settings"
              )}
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
}