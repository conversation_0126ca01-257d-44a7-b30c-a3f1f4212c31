'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Button } from '~/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Switch } from '~/components/ui/switch';
import { Palette, Moon, Sun, RotateCcw, Save, Copy, Download } from 'lucide-react';
import { useTheme } from 'next-themes';
import { toast } from 'sonner';
import { api } from '~/trpc/react';

// Define the theme variables and their descriptions
const themeVariables = {
  general: [
    { name: '--background', label: 'Background', description: 'Main background color', group: 'Surfaces' },
    { name: '--foreground', label: 'Foreground', description: 'Main text color', group: 'Text' },
    { name: '--card', label: 'Card Background', description: 'Card and panel backgrounds', group: 'Surfaces' },
    { name: '--card-foreground', label: 'Card Text', description: 'Text on cards', group: 'Text' },
    { name: '--popover', label: 'Popover Background', description: 'Dropdown and popover backgrounds', group: 'Surfaces' },
    { name: '--popover-foreground', label: 'Popover Text', description: 'Text in popovers', group: 'Text' },
  ],
  interactive: [
    { name: '--primary', label: 'Primary', description: 'Primary buttons and actions', group: 'Actions' },
    { name: '--primary-foreground', label: 'Primary Text', description: 'Text on primary elements', group: 'Actions' },
    { name: '--secondary', label: 'Secondary', description: 'Secondary buttons and elements', group: 'Actions' },
    { name: '--secondary-foreground', label: 'Secondary Text', description: 'Text on secondary elements', group: 'Actions' },
    { name: '--accent', label: 'Accent', description: 'Accent color for highlights', group: 'Actions' },
    { name: '--accent-foreground', label: 'Accent Text', description: 'Text on accent elements', group: 'Actions' },
    { name: '--destructive', label: 'Destructive', description: 'Error and delete actions', group: 'Actions' },
    { name: '--destructive-foreground', label: 'Destructive Text', description: 'Text on destructive elements', group: 'Actions' },
  ],
  ui: [
    { name: '--muted', label: 'Muted Background', description: 'Subtle backgrounds', group: 'UI' },
    { name: '--muted-foreground', label: 'Muted Text', description: 'Less prominent text', group: 'UI' },
    { name: '--border', label: 'Border', description: 'Border color', group: 'UI' },
    { name: '--input', label: 'Input Border', description: 'Form input borders', group: 'UI' },
    { name: '--ring', label: 'Focus Ring', description: 'Focus indicator color', group: 'UI' },
  ],
  charts: [
    { name: '--chart-1', label: 'Chart Color 1', description: 'First chart color', group: 'Charts' },
    { name: '--chart-2', label: 'Chart Color 2', description: 'Second chart color', group: 'Charts' },
    { name: '--chart-3', label: 'Chart Color 3', description: 'Third chart color', group: 'Charts' },
    { name: '--chart-4', label: 'Chart Color 4', description: 'Fourth chart color', group: 'Charts' },
    { name: '--chart-5', label: 'Chart Color 5', description: 'Fifth chart color', group: 'Charts' },
  ],
  brand: [
    { name: 'egis-green', label: 'Brand Green', description: 'Primary brand green', group: 'Brand', value: '#8CC63F' },
    { name: 'egis-blue', label: 'Brand Blue', description: 'Primary brand blue', group: 'Brand', value: '#005AAB' },
    { name: 'egis-orange', label: 'Brand Orange', description: 'Brand orange accent', group: 'Brand', value: '#FF7F00' },
    { name: 'egis-teal', label: 'Brand Teal', description: 'Brand teal accent', group: 'Brand', value: '#14b8a6' },
    { name: 'egis-midnightBlue', label: 'Brand Midnight', description: 'Dark brand blue', group: 'Brand', value: '#1e293b' },
  ]
};

// Convert HSL to HEX
function hslToHex(hsl: string): string {
  const match = hsl.match(/(\d+)\s+(\d+)%\s+(\d+)%/);
  if (!match) return '#000000';
  
  const h = parseInt(match[1]!);
  const s = parseInt(match[2]!) / 100;
  const l = parseInt(match[3]!) / 100;
  
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;
  
  let r = 0, g = 0, b = 0;
  
  if (h >= 0 && h < 60) {
    r = c; g = x; b = 0;
  } else if (h >= 60 && h < 120) {
    r = x; g = c; b = 0;
  } else if (h >= 120 && h < 180) {
    r = 0; g = c; b = x;
  } else if (h >= 180 && h < 240) {
    r = 0; g = x; b = c;
  } else if (h >= 240 && h < 300) {
    r = x; g = 0; b = c;
  } else if (h >= 300 && h < 360) {
    r = c; g = 0; b = x;
  }
  
  const toHex = (n: number) => {
    const hex = Math.round((n + m) * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

// Convert HEX to HSL
function hexToHsl(hex: string): string {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0;
  const l = (max + min) / 2;
  
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = ((g - b) / d + (g < b ? 6 : 0)) / 6; break;
      case g: h = ((b - r) / d + 2) / 6; break;
      case b: h = ((r - g) / d + 4) / 6; break;
    }
  }
  
  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
}

export function ThemeEditor() {
  const { theme: currentTheme, setTheme } = useTheme();
  const [colors, setColors] = useState<Record<string, string>>({});
  const [previewMode, setPreviewMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch saved theme from database
  const { data: organization, refetch } = api.organizations.getCurrent.useQuery();
  const updateTheme = api.organizations.update.useMutation({
    onSuccess: () => {
      toast.success('Theme saved successfully');
      setHasChanges(false);
      refetch();
    },
    onError: () => {
      toast.error('Failed to save theme');
    },
  });

  // Load current theme colors
  useEffect(() => {
    const loadColors = () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      const loadedColors: Record<string, string> = {};
      
      // Load CSS variables
      [...themeVariables.general, ...themeVariables.interactive, ...themeVariables.ui, ...themeVariables.charts].forEach(variable => {
        const value = computedStyle.getPropertyValue(variable.name).trim();
        if (value) {
          loadedColors[variable.name] = hslToHex(value);
        }
      });
      
      // Load brand colors
      themeVariables.brand.forEach(color => {
        loadedColors[color.name] = color.value;
      });
      
      // Load from saved theme if available
      if ((organization as any)?.theme_config) {
        Object.assign(loadedColors, (organization as any).theme_config);
      }
      
      setColors(loadedColors);
    };
    
    loadColors();
  }, [currentTheme, organization]);

  // Apply color changes in real-time
  const updateColor = (variable: string, value: string) => {
    setColors(prev => ({ ...prev, [variable]: value }));
    setHasChanges(true);
    
    if (previewMode) {
      const root = document.documentElement;
      
      if (variable.startsWith('--')) {
        // CSS variable
        const hslValue = hexToHsl(value);
        root.style.setProperty(variable, hslValue);
      } else {
        // Brand color - update in Tailwind runtime if possible
        // For now, we'll store it for saving
      }
    }
  };

  // Reset to defaults
  const resetTheme = () => {
    const root = document.documentElement;
    // Remove all custom properties
    [...themeVariables.general, ...themeVariables.interactive, ...themeVariables.ui, ...themeVariables.charts].forEach(variable => {
      root.style.removeProperty(variable.name);
    });
    
    // Reload colors
    const computedStyle = getComputedStyle(root);
    const resetColors: Record<string, string> = {};
    
    [...themeVariables.general, ...themeVariables.interactive, ...themeVariables.ui, ...themeVariables.charts].forEach(variable => {
      const value = computedStyle.getPropertyValue(variable.name).trim();
      if (value) {
        resetColors[variable.name] = hslToHex(value);
      }
    });
    
    themeVariables.brand.forEach(color => {
      resetColors[color.name] = color.value;
    });
    
    setColors(resetColors);
    setHasChanges(false);
  };

  // Save theme
  const saveTheme = async () => {
    // TODO: theme_config needs to be added to organization update schema
    await updateTheme.mutateAsync({
      theme_colors: colors,
    });
  };

  // Export theme
  const exportTheme = () => {
    const themeData = {
      name: `${organization?.name || 'Custom'} Theme`,
      version: '1.0.0',
      colors: colors,
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `theme-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Copy CSS
  const copyCss = () => {
    const cssVars = Object.entries(colors)
      .filter(([key]) => key.startsWith('--'))
      .map(([key, value]) => `  ${key}: ${hexToHsl(value)};`)
      .join('\n');
    
    const css = `:root {\n${cssVars}\n}`;
    
    navigator.clipboard.writeText(css);
    toast.success('CSS copied to clipboard');
  };

  const ColorInput = ({ variable, label, description }: { variable: { name: string; label: string; description: string }; label: string; description: string }) => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor={variable.name} className="text-sm font-medium">
          {label}
        </Label>
        <span className="text-xs text-muted-foreground">{variable.name}</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="relative">
          <Input
            id={variable.name}
            type="color"
            value={colors[variable.name] || '#000000'}
            onChange={(e) => updateColor(variable.name, e.target.value)}
            className="w-16 h-10 cursor-pointer"
          />
        </div>
        <Input
          type="text"
          value={colors[variable.name] || '#000000'}
          onChange={(e) => updateColor(variable.name, e.target.value)}
          className="flex-1 font-mono text-sm"
          placeholder="#000000"
        />
      </div>
      <p className="text-xs text-muted-foreground">{description}</p>
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Theme Editor
          </CardTitle>
          <CardDescription>
            Customize your organization&apos;s theme colors. Changes can be previewed in real-time.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Switch
                    checked={previewMode}
                    onCheckedChange={setPreviewMode}
                  />
                  <Label>Live Preview</Label>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>Current theme:</span>
                  <span className="font-medium capitalize">{currentTheme}</span>
                  {currentTheme === 'dark' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={copyCss}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy CSS
                </Button>
                <Button variant="outline" size="sm" onClick={exportTheme}>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm" onClick={resetTheme}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
                <Button 
                  size="sm" 
                  onClick={saveTheme} 
                  disabled={!hasChanges || updateTheme.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Theme
                </Button>
              </div>
            </div>
            
            {hasChanges && (
              <div className="bg-amber-50 text-amber-800 p-3 rounded-md text-sm">
                You have unsaved changes. {previewMode ? 'Preview mode is active.' : 'Enable preview to see changes.'}
              </div>
            )}
          </div>

          <Tabs defaultValue="general" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="interactive">Interactive</TabsTrigger>
              <TabsTrigger value="ui">UI Elements</TabsTrigger>
              <TabsTrigger value="charts">Charts</TabsTrigger>
              <TabsTrigger value="brand">Brand Colors</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                {themeVariables.general.map((variable) => (
                  <ColorInput
                    key={variable.name}
                    variable={variable}
                    label={variable.label}
                    description={variable.description}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="interactive" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                {themeVariables.interactive.map((variable) => (
                  <ColorInput
                    key={variable.name}
                    variable={variable}
                    label={variable.label}
                    description={variable.description}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="ui" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                {themeVariables.ui.map((variable) => (
                  <ColorInput
                    key={variable.name}
                    variable={variable}
                    label={variable.label}
                    description={variable.description}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="charts" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                {themeVariables.charts.map((variable) => (
                  <ColorInput
                    key={variable.name}
                    variable={variable}
                    label={variable.label}
                    description={variable.description}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="brand" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                {themeVariables.brand.map((variable) => (
                  <ColorInput
                    key={variable.name}
                    variable={variable}
                    label={variable.label}
                    description={variable.description}
                  />
                ))}
              </div>
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Brand colors are used in specific components and may require a rebuild to fully apply.
                  These are defined in the Tailwind configuration.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}