'use client';

import React, { useEffect, useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { Button } from '~/components/ui/button';
import { RefreshCw, Download, Maximize2, FileCode, Database, Globe, GitBranch, Layers } from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from 'sonner';

// Mermaid will be initialized client-side only
import mermaid from 'mermaid';

// Mermaid component replacement
const MermaidDiagram: React.FC<{ chart: string }> = ({ chart }) => {
  const ref = React.useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (ref.current) {
      mermaid.initialize({ 
        startOnLoad: true,
        theme: 'default',
        securityLevel: 'loose',
      });
      
      ref.current.innerHTML = chart;
      mermaid.init(undefined, ref.current);
    }
  }, [chart]);
  
  return <div ref={ref} className="mermaid" />;
};

interface RouteInfo {
  path: string;
  method: string;
  handler: string;
  middleware?: string[];
}

interface ComponentInfo {
  name: string;
  path: string;
  type: 'page' | 'component' | 'layout';
  imports: string[];
  exports: string[];
}

interface DatabaseTable {
  name: string;
  schema: string;
  columns: { name: string; type: string; nullable: boolean }[];
  relations: { from: string; to: string; type: string }[];
}

export function ArchitectureVisualizer() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Fetch architecture data from API
  const { data: architectureData, refetch, isLoading } = (api.admin as any).getArchitectureMap?.useQuery(undefined, {
    refetchInterval: 30000, // Refresh every 30 seconds
  }) || { data: null, refetch: () => {}, isLoading: false };

  // Generate Mermaid diagrams based on the architecture data
  const diagrams = useMemo(() => {
    if (!architectureData) return {};

    return {
      overview: `
        graph TB
          subgraph "Frontend Layer"
            A[Next.js App Router]
            B[React Components]
            C[tRPC Client]
            D[Tailwind CSS]
          end
          
          subgraph "API Layer"
            E[tRPC Routers]
            F[NextAuth.js]
            G[Middleware]
          end
          
          subgraph "Data Layer"
            H[Prisma ORM]
            I[PostgreSQL]
            J[Redis Cache]
          end
          
          subgraph "External Services"
            K[Monday.com API]
            L[File Storage]
            M[Email Service]
          end
          
          A --> B
          B --> C
          C --> E
          E --> H
          H --> I
          F --> G
          G --> E
          E --> J
          E --> K
          E --> L
          E --> M
          
          classDef frontend fill:#e0f2fe,stroke:#0284c7,stroke-width:2px
          classDef api fill:#fef3c7,stroke:#f59e0b,stroke-width:2px
          classDef data fill:#d1fae5,stroke:#10b981,stroke-width:2px
          classDef external fill:#fce7f3,stroke:#ec4899,stroke-width:2px
          
          class A,B,C,D frontend
          class E,F,G api
          class H,I,J data
          class K,L,M external
      `,
      
      routes: generateRoutesDiagram(architectureData.routes),
      components: generateComponentsDiagram(architectureData.components),
      database: generateDatabaseDiagram(architectureData.tables),
      dataFlow: generateDataFlowDiagram(architectureData),
      authentication: `
        sequenceDiagram
          participant U as User
          participant C as Client
          participant M as Middleware
          participant A as NextAuth
          participant D as Database
          participant R as Redis
          
          U->>C: Access Protected Route
          C->>M: Request with Cookie
          M->>A: Validate Session
          A->>R: Check Session Cache
          alt Session in Cache
            R-->>A: Return Session
          else No Cache
            A->>D: Query Session
            D-->>A: Return Session
            A->>R: Cache Session
          end
          A-->>M: Session Valid/Invalid
          alt Valid Session
            M-->>C: Allow Access
            C-->>U: Show Protected Content
          else Invalid Session
            M-->>C: Redirect to Login
            C-->>U: Show Login Page
          end
      `,
    };
  }, [architectureData]);

  // Generate route diagram
  function generateRoutesDiagram(routes?: RouteInfo[]) {
    if (!routes || routes.length === 0) return 'graph LR\n  A[No Routes Found]';
    
    let diagram = 'graph LR\n';
    routes.forEach((route, idx) => {
      const nodeId = `R${idx}`;
      diagram += `  ${nodeId}["${route.method} ${route.path}\\n${route.handler}"]\n`;
      if (route.middleware && route.middleware.length > 0) {
        route.middleware.forEach((mw, mwIdx) => {
          const mwId = `M${idx}_${mwIdx}`;
          diagram += `  ${mwId}[${mw}] --> ${nodeId}\n`;
        });
      }
    });
    
    return diagram;
  }

  // Generate components diagram
  function generateComponentsDiagram(components?: ComponentInfo[]) {
    if (!components || components.length === 0) return 'graph TD\n  A[No Components Found]';
    
    let diagram = 'graph TD\n';
    const componentMap = new Map<string, string>();
    
    // Create nodes
    components.forEach((comp, idx) => {
      const nodeId = `C${idx}`;
      componentMap.set(comp.name, nodeId);
      const shape = comp.type === 'page' ? '[[' : comp.type === 'layout' ? '[(' : '[';
      const endShape = comp.type === 'page' ? ']]' : comp.type === 'layout' ? ')]' : ']';
      diagram += `  ${nodeId}${shape}"${comp.name}\\n${comp.path}"${endShape}\n`;
    });
    
    // Create relationships based on imports
    components.forEach((comp, idx) => {
      const fromId = `C${idx}`;
      comp.imports.forEach(imp => {
        const importedComp = components.find((c: any) => c.path === imp || c.name === imp);
        if (importedComp) {
          const toId = componentMap.get(importedComp.name);
          if (toId) {
            diagram += `  ${fromId} --> ${toId}\n`;
          }
        }
      });
    });
    
    return diagram;
  }

  // Generate database diagram
  function generateDatabaseDiagram(tables?: DatabaseTable[]) {
    if (!tables || tables.length === 0) return 'graph LR\n  A[No Tables Found]';
    
    let diagram = 'erDiagram\n';
    
    tables.forEach(table => {
      // Add table with columns
      diagram += `  ${table.name} {\n`;
      table.columns.forEach(col => {
        const nullable = col.nullable ? '?' : '';
        diagram += `    ${col.type} ${col.name}${nullable}\n`;
      });
      diagram += '  }\n';
      
      // Add relationships
      table.relations.forEach(rel => {
        diagram += `  ${rel.from} ||--${rel.type === 'one-to-many' ? 'o{' : '||'} ${rel.to} : has\n`;
      });
    });
    
    return diagram;
  }

  // Generate data flow diagram
  function generateDataFlowDiagram(data: any) {
    return `
      flowchart TD
        subgraph "User Interaction"
          UI[User Interface]
        end
        
        subgraph "State Management"
          RC[React Query Cache]
          LS[Local State]
          CTX[React Context]
        end
        
        subgraph "API Calls"
          TRPC[tRPC Client]
          REST[REST Endpoints]
        end
        
        subgraph "Server Processing"
          ROUTER[tRPC Routers]
          VALID[Validation Layer]
          BL[Business Logic]
        end
        
        subgraph "Data Access"
          PRISMA[Prisma ORM]
          SQL[SQL Queries]
          CACHE[Redis Cache]
        end
        
        subgraph "Database"
          PG[(PostgreSQL)]
        end
        
        UI --> RC
        UI --> LS
        UI --> CTX
        RC --> TRPC
        LS --> TRPC
        TRPC --> ROUTER
        UI -.-> REST
        REST --> ROUTER
        ROUTER --> VALID
        VALID --> BL
        BL --> PRISMA
        BL --> CACHE
        PRISMA --> SQL
        SQL --> PG
        CACHE -.-> PG
        
        classDef user fill:#dbeafe,stroke:#2563eb
        classDef state fill:#fef3c7,stroke:#f59e0b
        classDef api fill:#d1fae5,stroke:#10b981
        classDef server fill:#fce7f3,stroke:#ec4899
        classDef data fill:#e0e7ff,stroke:#6366f1
        
        class UI user
        class RC,LS,CTX state
        class TRPC,REST api
        class ROUTER,VALID,BL server
        class PRISMA,SQL,CACHE,PG data
    `;
  }

  // Export diagram as SVG
  const exportDiagram = () => {
    const svgElement = document.querySelector('.mermaid svg');
    if (svgElement) {
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const blob = new Blob([svgData], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `architecture-${activeTab}-${Date.now()}.svg`;
      a.click();
      URL.revokeObjectURL(url);
      toast.success('Diagram exported successfully');
    }
  };

  return (
    <div className={`space-y-6 ${isFullscreen ? 'fixed inset-0 z-50 bg-background p-6 overflow-auto' : ''}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Architecture Visualizer
              </CardTitle>
              <CardDescription>
                Real-time visualization of application architecture and data flow
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={exportDiagram}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={() => setIsFullscreen(!isFullscreen)}>
                <Maximize2 className="h-4 w-4 mr-2" />
                {isFullscreen ? 'Exit' : 'Fullscreen'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview" className="flex items-center gap-1">
                <Layers className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="routes" className="flex items-center gap-1">
                <Globe className="h-4 w-4" />
                Routes
              </TabsTrigger>
              <TabsTrigger value="components" className="flex items-center gap-1">
                <FileCode className="h-4 w-4" />
                Components
              </TabsTrigger>
              <TabsTrigger value="database" className="flex items-center gap-1">
                <Database className="h-4 w-4" />
                Database
              </TabsTrigger>
              <TabsTrigger value="dataFlow">Data Flow</TabsTrigger>
              <TabsTrigger value="authentication">Auth Flow</TabsTrigger>
            </TabsList>

            {Object.entries(diagrams).map(([key, diagram]) => (
              <TabsContent key={key} value={key} className="mt-6">
                <div className="border rounded-lg p-4 bg-card">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-96">
                      <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <MermaidDiagram chart={diagram} />
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>

          {architectureData?.lastUpdated && (
            <div className="mt-4 text-sm text-muted-foreground">
              Last updated: {new Date(architectureData.lastUpdated).toLocaleString()}
              {architectureData.autoDetected && ' (Auto-detected from codebase)'}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}