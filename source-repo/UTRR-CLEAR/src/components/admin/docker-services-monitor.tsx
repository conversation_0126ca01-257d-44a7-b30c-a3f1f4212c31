'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { useToast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import {
  Server,
  Database,
  Wifi,
  Clock,
  TrendingUp,
  BarChart3,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Zap,
  HardDrive,
  Activity,
} from 'lucide-react';

interface DockerServicesMonitorProps {
  projectId?: string;
}

export function DockerServicesMonitor({ projectId }: DockerServicesMonitorProps) {
  const { toast } = useToast();
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // Fetch system health
  const { 
    data: systemHealth, 
    isLoading: isLoadingHealth,
    refetch: refetchHealth 
  } = api.performance.getSystemHealth.useQuery(undefined, {
    refetchInterval: autoRefresh ? refreshInterval : false,
  });

  // Fetch cache performance
  const { 
    data: cachePerformance, 
    isLoading: isLoadingCache,
    refetch: refetchCache 
  } = api.performance.getCachePerformance.useQuery(undefined, {
    refetchInterval: autoRefresh ? refreshInterval : false,
  });

  // Fetch WebSocket metrics
  const { 
    data: websocketMetrics,
    isLoading: isLoadingWebSocket,
    refetch: refetchWebSocket 
  } = api.performance.getWebSocketMetrics.useQuery(undefined, {
    refetchInterval: autoRefresh ? refreshInterval : false,
  });

  // Fetch PostGIS performance for specific project if provided
  const { 
    data: postgisPerformance,
    isLoading: isLoadingPostGIS,
    refetch: refetchPostGIS 
  } = api.performance.getPostGISPerformance.useQuery(
    { projectId: projectId || '' },
    { 
      enabled: !!projectId,
      refetchInterval: autoRefresh ? refreshInterval * 2 : false, // Less frequent for heavy operations
    }
  );

  // Architecture status
  const { 
    data: architectureStatus,
    isLoading: isLoadingArchitecture 
  } = api.performance.getArchitectureStatus.useQuery();

  // Refresh all data
  const handleRefreshAll = async () => {
    try {
      await Promise.all([
        refetchHealth(),
        refetchCache(),
        refetchWebSocket(),
        projectId ? refetchPostGIS() : Promise.resolve(),
      ]);
      toast({
        title: 'Data Refreshed',
        description: 'All monitoring data has been updated.',
      });
    } catch (error) {
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh some monitoring data.',
        variant: 'destructive',
      });
    }
  };

  const getServiceStatusBadge = (healthy: boolean, name: string) => (
    <Badge variant={healthy ? 'default' : 'destructive'} className="ml-2">
      {healthy ? (
        <CheckCircle className="h-3 w-3 mr-1" />
      ) : (
        <XCircle className="h-3 w-3 mr-1" />
      )}
      {healthy ? 'Healthy' : 'Unhealthy'}
    </Badge>
  );

  const getResponseTimeColor = (responseTime: number) => {
    if (responseTime < 100) return 'text-green-600';
    if (responseTime < 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoadingHealth || isLoadingCache || isLoadingArchitecture) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Docker Services Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-20 bg-muted rounded"></div>
            <div className="h-20 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Docker Services Monitor
              </CardTitle>
              <CardDescription>
                Monitor health and performance of microservices architecture
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshAll}
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <Button
                variant={autoRefresh ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
                className="flex items-center gap-1"
              >
                <Activity className="h-4 w-4" />
                {autoRefresh ? 'Auto' : 'Manual'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="services" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="services">Services</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="cache">Cache</TabsTrigger>
              <TabsTrigger value="architecture">Architecture</TabsTrigger>
            </TabsList>

            <TabsContent value="services" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Database Status */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Database className="h-4 w-4" />
                        PostgreSQL Database
                      </div>
                      {systemHealth?.services?.database && 
                        getServiceStatusBadge(systemHealth.services.database.healthy, 'database')
                      }
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {systemHealth?.services?.database && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Response Time:</span>
                          <span className={getResponseTimeColor(systemHealth.services.database.responseTime)}>
                            {systemHealth.services.database.responseTime}ms
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Last Check:</span>
                          <span className="text-muted-foreground">
                            {new Date(systemHealth.services.database.lastCheck).toLocaleTimeString()}
                          </span>
                        </div>
                        {systemHealth.services.database.error && (
                          <Alert>
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="text-xs">
                              {systemHealth.services.database.error}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Redis Status */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <HardDrive className="h-4 w-4" />
                        Redis Cache
                      </div>
                      {systemHealth?.services?.redis && 
                        getServiceStatusBadge(systemHealth.services.redis.healthy, 'redis')
                      }
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {systemHealth?.services?.redis && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Response Time:</span>
                          <span className={getResponseTimeColor(systemHealth.services.redis.responseTime)}>
                            {systemHealth.services.redis.responseTime}ms
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Total Keys:</span>
                          <span>{systemHealth?.cache?.stats?.totalKeys || 0}</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Spatial Processor Status */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Spatial Processor
                      </div>
                      {systemHealth?.services?.spatialProcessor && 
                        getServiceStatusBadge(systemHealth.services.spatialProcessor.healthy, 'spatial-processor')
                      }
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {systemHealth?.services?.spatialProcessor && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Port:</span>
                          <span>8001</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Response Time:</span>
                          <span className={getResponseTimeColor(systemHealth.services.spatialProcessor.responseTime)}>
                            {systemHealth.services.spatialProcessor.responseTime}ms
                          </span>
                        </div>
                        {systemHealth.services.spatialProcessor.error && (
                          <Alert>
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="text-xs">
                              Service unavailable - using fallback PostGIS
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* WebSocket Server Status */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Wifi className="h-4 w-4" />
                        WebSocket Server
                      </div>
                      {systemHealth?.services?.websocketServer && 
                        getServiceStatusBadge(systemHealth.services.websocketServer.healthy, 'websocket')
                      }
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {systemHealth?.services?.websocketServer && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Port:</span>
                          <span>8002</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Connections:</span>
                          <span>{websocketMetrics?.connections || 0}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Channels:</span>
                          <span>{websocketMetrics?.channels || 0}</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              {projectId && postgisPerformance && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      PostGIS Performance (Project {projectId})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <div className="text-xs text-muted-foreground">Processing Time</div>
                        <div className="text-lg font-semibold">
                          {postgisPerformance.postgisTime}ms
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Conflicts Found</div>
                        <div className="text-lg font-semibold">
                          {postgisPerformance.conflictCount}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Spatial Index</div>
                        <Badge variant={postgisPerformance.spatialIndexUsed ? 'default' : 'secondary'}>
                          {postgisPerformance.spatialIndexUsed ? 'Used' : 'Not Used'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    System Performance Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Overall Health</span>
                        <span>{systemHealth?.overall?.healthy ? 'Healthy' : 'Degraded'}</span>
                      </div>
                      <Progress value={systemHealth?.overall?.healthy ? 100 : 60} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Cache Hit Rate</span>
                        <span>{((cachePerformance?.performance?.hitRate || 0) * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={(cachePerformance?.performance?.hitRate || 0) * 100} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cache" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{cachePerformance?.totalKeys || 0}</div>
                      <div className="text-xs text-muted-foreground">Total Keys</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{cachePerformance?.spatialKeys || 0}</div>
                      <div className="text-xs text-muted-foreground">Spatial Keys</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{cachePerformance?.conflictKeys || 0}</div>
                      <div className="text-xs text-muted-foreground">Conflict Keys</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {((cachePerformance?.performance?.hitRate || 0) * 100).toFixed(1)}%
                      </div>
                      <div className="text-xs text-muted-foreground">Hit Rate</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="architecture" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Microservices Architecture</CardTitle>
                  <CardDescription>
                    CLEAR platform running on Docker containers with Nginx reverse proxy
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {architectureStatus?.components && Object.entries(architectureStatus.components).map(([key, component]) => (
                      <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{component.name}</div>
                          <div className="text-sm text-muted-foreground">{component.role}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">Ports: {component.ports.join(', ')}</div>
                          <Badge variant={component.status === 'healthy' ? 'default' : 'secondary'}>
                            {component.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Data Flow</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {architectureStatus?.dataFlow?.map((flow, index) => (
                      <div key={index} className="text-sm p-2 bg-muted rounded">
                        {flow}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}