'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import { 
  Shield, 
  Database, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Copy,
  ExternalLink,
  Loader2,
  FileCode
} from 'lucide-react';

interface RLSScript {
  id: string;
  name: string;
  description: string;
  sql: string;
  category: 'setup' | 'verify' | 'cleanup';
  order: number;
}

export function RLSSetupRunner() {
  const [copiedScript, setCopiedScript] = useState<string | null>(null);
  const projectRef = process.env.NEXT_PUBLIC_SUPABASE_URL?.match(/https:\/\/([^.]+)/)?.[1] || '';

  const scripts: RLSScript[] = [
    {
      id: 'enable-rls',
      name: '01 - Enable RLS on All Tables',
      description: 'Enables Row Level Security on all public tables',
      category: 'setup',
      order: 1,
      sql: `-- Enable Row Level Security on ALL Tables
DO $$
DECLARE
    r RECORD;
    table_count INTEGER := 0;
    enabled_count INTEGER := 0;
BEGIN
    RAISE NOTICE '🔐 Starting RLS enablement process...';
    
    FOR r IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND tablename NOT LIKE 'pg_%'
        AND tablename NOT LIKE '_prisma_%'
        AND tablename NOT IN ('spatial_ref_sys', 'geography_columns', 'geometry_columns')
        ORDER BY tablename
    LOOP
        table_count := table_count + 1;
        
        -- Enable RLS on the table
        EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', r.tablename);
        enabled_count := enabled_count + 1;
        RAISE NOTICE '✅ Enabled RLS on table: %', r.tablename;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ RLS enablement complete!';
    RAISE NOTICE '   Total tables processed: %', table_count;
    RAISE NOTICE '   RLS enabled on: % tables', enabled_count;
END $$;`
    },
    {
      id: 'create-functions',
      name: '02 - Create Helper Functions',
      description: 'Creates helper functions for RLS policies',
      category: 'setup',
      order: 2,
      sql: `-- Create Helper Functions for RLS
CREATE OR REPLACE FUNCTION public.get_user_organization_id() 
RETURNS INTEGER AS $$
  SELECT COALESCE(
    (SELECT organization_id FROM public.users WHERE auth_user_id = auth.uid()),
    1 -- Default to org 1 if not found
  );
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_admin() 
RETURNS BOOLEAN AS $$
  SELECT COALESCE(
    (SELECT is_admin FROM public.users WHERE auth_user_id = auth.uid()),
    COALESCE((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role', '') = 'admin',
    false
  );
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_user_id() 
RETURNS INTEGER AS $$
  SELECT COALESCE(
    (SELECT id FROM public.users WHERE auth_user_id = auth.uid()),
    0
  );
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_user_organization_id() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_id() TO authenticated;`
    },
    {
      id: 'create-policies',
      name: '03 - Create Basic RLS Policies',
      description: 'Creates essential RLS policies for core tables',
      category: 'setup',
      order: 3,
      sql: `-- Organizations policies
DROP POLICY IF EXISTS "users_view_own_org" ON public.organizations;
CREATE POLICY "users_view_own_org" ON public.organizations
  FOR SELECT USING (id = public.get_user_organization_id() OR public.is_admin());

-- Users policies
DROP POLICY IF EXISTS "users_view_same_org" ON public.users;
CREATE POLICY "users_view_same_org" ON public.users
  FOR SELECT USING (organization_id = public.get_user_organization_id());

DROP POLICY IF EXISTS "users_update_self" ON public.users;
CREATE POLICY "users_update_self" ON public.users
  FOR UPDATE USING (auth_user_id = auth.uid());

-- Projects policies
DROP POLICY IF EXISTS "projects_view_org" ON public.projects;
CREATE POLICY "projects_view_org" ON public.projects
  FOR SELECT USING (organization_id = public.get_user_organization_id());

DROP POLICY IF EXISTS "projects_insert_org" ON public.projects;
CREATE POLICY "projects_insert_org" ON public.projects
  FOR INSERT WITH CHECK (organization_id = public.get_user_organization_id());

DROP POLICY IF EXISTS "projects_update_org" ON public.projects;
CREATE POLICY "projects_update_org" ON public.projects
  FOR UPDATE USING (
    organization_id = public.get_user_organization_id() 
    AND (public.is_admin() OR project_manager_id = public.get_user_id() OR created_by = public.get_user_id())
  );`
    },
    {
      id: 'grant-permissions',
      name: '04 - Grant Permissions',
      description: 'Grants necessary permissions to roles',
      category: 'setup',
      order: 4,
      sql: `-- Grant permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Limited permissions for anonymous users
GRANT SELECT ON public.organizations TO anon;
GRANT SELECT ON public.feature_requests TO anon;`
    },
    {
      id: 'verify-setup',
      name: 'Verify RLS Setup',
      description: 'Check the status of RLS and policies',
      category: 'verify',
      order: 5,
      sql: `-- Check RLS status on all tables
SELECT 
  tablename,
  CASE 
    WHEN c.relrowsecurity THEN '✅ ENABLED' 
    ELSE '❌ DISABLED' 
  END as rls_status,
  (
    SELECT COUNT(*) 
    FROM pg_policies p 
    WHERE p.schemaname = 'public' 
    AND p.tablename = t.tablename
  ) as policy_count
FROM pg_tables t
JOIN pg_class c ON c.relname = t.tablename
JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = t.schemaname
WHERE t.schemaname = 'public'
  AND t.tablename NOT LIKE 'pg_%'
  AND t.tablename NOT LIKE '_prisma_%'
  AND t.tablename NOT IN ('spatial_ref_sys', 'geography_columns', 'geometry_columns')
ORDER BY 
  CASE WHEN c.relrowsecurity THEN 0 ELSE 1 END,
  policy_count DESC,
  t.tablename;`
    }
  ];

  const copyToClipboard = async (sql: string, scriptId: string) => {
    await navigator.clipboard.writeText(sql);
    setCopiedScript(scriptId);
    setTimeout(() => setCopiedScript(null), 2000);
  };

  const openSQLEditor = () => {
    window.open(`https://supabase.com/dashboard/project/${projectRef}/sql/new`, '_blank');
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'setup':
        return <Database className="h-4 w-4" />;
      case 'verify':
        return <CheckCircle className="h-4 w-4" />;
      case 'cleanup':
        return <XCircle className="h-4 w-4" />;
      default:
        return <FileCode className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Shield className="h-8 w-8" />
          Row Level Security Setup
        </h1>
        <p className="text-muted-foreground mt-2">
          Set up Row Level Security to protect your data
        </p>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Important</AlertTitle>
        <AlertDescription>
          Run these scripts in order (01, 02, 03, 04) in the Supabase SQL Editor. 
          Each script builds on the previous one.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="scripts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="scripts">SQL Scripts</TabsTrigger>
          <TabsTrigger value="instructions">Instructions</TabsTrigger>
        </TabsList>

        <TabsContent value="scripts" className="space-y-4">
          {scripts.map((script) => (
            <Card key={script.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    {getCategoryIcon(script.category)}
                    <div>
                      <CardTitle className="text-lg">{script.name}</CardTitle>
                      <CardDescription>{script.description}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={script.category === 'verify' ? 'secondary' : 'default'}>
                      Step {script.order}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <ScrollArea className="h-[200px] w-full rounded-md border">
                  <pre className="p-4 text-sm">
                    <code>{script.sql}</code>
                  </pre>
                </ScrollArea>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(script.sql, script.id)}
                    className="flex-1"
                  >
                    {copiedScript === script.id ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy SQL
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={openSQLEditor}
                    className="flex-1"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Open SQL Editor
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="instructions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>How to Set Up RLS</CardTitle>
              <CardDescription>
                Follow these steps to enable Row Level Security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                    1
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">Open Supabase SQL Editor</h4>
                    <p className="text-sm text-muted-foreground">
                      Click the "Open SQL Editor" button to open your Supabase dashboard
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                    2
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">Copy Each Script</h4>
                    <p className="text-sm text-muted-foreground">
                      Copy each SQL script in order (01, 02, 03, 04) using the "Copy SQL" button
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                    3
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">Paste and Run</h4>
                    <p className="text-sm text-muted-foreground">
                      Paste the script into the SQL Editor and click "Run" or press Ctrl+Enter
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                    4
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">Verify Setup</h4>
                    <p className="text-sm text-muted-foreground">
                      Run the verification script to ensure RLS is properly enabled
                    </p>
                  </div>
                </div>
              </div>

              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  After enabling RLS, users will only be able to see data from their own organization.
                  Admin users (like <EMAIL>) will have broader access.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}