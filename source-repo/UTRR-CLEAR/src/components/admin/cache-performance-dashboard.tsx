'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  Activity, 
  Database, 
  Zap, 
  TrendingUp, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Clock,
  MemoryStick,
  HardDrive,
  Cpu
} from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';

const CHART_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

interface CachePerformanceProps {
  className?: string;
}

export function CachePerformanceDashboard({ className }: CachePerformanceProps) {
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d' | '30d'>('24h');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch cache statistics
  const { data: cacheStats, refetch: refetchStats } = api.cacheManagement.getCacheStats.useQuery();
  
  // Fetch usage patterns
  const { data: usagePatterns } = api.cacheManagement.analyzeUsagePatterns.useQuery({ days: 7 });
  
  // Fetch performance metrics
  const { data: performanceMetrics } = api.cacheManagement.getCachePerformanceMetrics.useQuery({ 
    timeRange 
  });

  // Mutations
  const warmupMutation = api.cacheManagement.executeScheduledWarmup.useMutation();
  const invalidateMutation = api.cacheManagement.invalidateCache.useMutation();
  const refreshViewsMutation = api.cacheManagement.refreshMaterializedViews.useMutation();

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetchStats();
      toast({
        title: 'Dashboard refreshed',
        description: 'Cache performance data has been updated.',
      });
    } catch (error) {
      toast({
        title: 'Refresh failed',
        description: 'Failed to refresh dashboard data.',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleWarmup = async () => {
    try {
      const result = await warmupMutation.mutateAsync();
      toast({
        title: 'Cache warmup completed',
        description: `Warmed up ${result.successfulWarmups} projects in ${result.totalTime}ms.`,
      });
      await refetchStats();
    } catch (error) {
      toast({
        title: 'Warmup failed',
        description: 'Failed to execute cache warmup.',
        variant: 'destructive',
      });
    }
  };

  const handleInvalidateAll = async () => {
    try {
      await invalidateMutation.mutateAsync({ cacheType: 'all' });
      toast({
        title: 'Cache invalidated',
        description: 'All caches have been cleared.',
      });
      await refetchStats();
    } catch (error) {
      toast({
        title: 'Invalidation failed',
        description: 'Failed to invalidate cache.',
        variant: 'destructive',
      });
    }
  };

  const handleRefreshViews = async () => {
    try {
      const result = await refreshViewsMutation.mutateAsync({ views: ['all'] });
      toast({
        title: 'Materialized views refreshed',
        description: `Refreshed ${result.successCount} views successfully.`,
      });
    } catch (error) {
      toast({
        title: 'View refresh failed',
        description: 'Failed to refresh materialized views.',
        variant: 'destructive',
      });
    }
  };

  // Prepare chart data
  const invalidationChartData = performanceMetrics?.invalidationEvents.map(event => ({
    name: event.cache_type,
    events: event.event_count,
    avgInterval: Math.round(event.avg_interval_seconds / 60), // Convert to minutes
  })) || [];

  const usageChartData = usagePatterns?.patterns.slice(0, 10).map((pattern, index) => ({
    name: `Project ${index + 1}`,
    accesses: pattern.accessCount,
    responseTime: Math.round(pattern.avgResponseTime),
    priority: pattern.priority,
  })) || [];

  const pieChartData = [
    { name: 'Spatial Keys', value: cacheStats?.spatial.spatialKeys || 0 },
    { name: 'Query Keys', value: cacheStats?.spatial.queryKeys || 0 },
    { name: 'Conflict Keys', value: cacheStats?.spatial.conflictKeys || 0 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'available':
      case 'connected':
        return 'bg-green-500';
      case 'warning':
      case 'disconnected':
        return 'bg-yellow-500';
      case 'error':
      case 'unavailable':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Cache Performance Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor and optimize cache performance for better system efficiency
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">1 Hour</SelectItem>
              <SelectItem value="6h">6 Hours</SelectItem>
              <SelectItem value="24h">24 Hours</SelectItem>
              <SelectItem value="7d">7 Days</SelectItem>
              <SelectItem value="30d">30 Days</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh} 
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex space-x-2">
        <Button 
          onClick={handleWarmup} 
          disabled={warmupMutation.isPending}
          size="sm"
        >
          <Zap className="h-4 w-4 mr-2" />
          {warmupMutation.isPending ? 'Warming...' : 'Cache Warmup'}
        </Button>
        <Button 
          variant="outline" 
          onClick={handleInvalidateAll} 
          disabled={invalidateMutation.isPending}
          size="sm"
        >
          <Database className="h-4 w-4 mr-2" />
          {invalidateMutation.isPending ? 'Clearing...' : 'Clear All Cache'}
        </Button>
        <Button 
          variant="outline" 
          onClick={handleRefreshViews} 
          disabled={refreshViewsMutation.isPending}
          size="sm"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {refreshViewsMutation.isPending ? 'Refreshing...' : 'Refresh Views'}
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceMetrics?.metrics.estimatedHitRate 
                ? `${Math.round(performanceMetrics.metrics.estimatedHitRate * 100)}%`
                : 'N/A'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              +2.1% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Keys</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheStats?.spatial.totalKeys?.toLocaleString() || '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              {cacheStats?.spatial.spatialKeys || 0} spatial keys
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Invalidations</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceMetrics?.metrics.totalInvalidations || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {timeRange} period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Redis Status</CardTitle>
            <div className={`h-2 w-2 rounded-full ${getStatusColor(cacheStats?.redis.available ? 'connected' : 'disconnected')}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheStats?.redis.available ? 'Connected' : 'Disconnected'}
            </div>
            <p className="text-xs text-muted-foreground">
              {cacheStats?.redis.available ? 'Operational' : 'Check connection'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="usage">Usage Patterns</TabsTrigger>
          <TabsTrigger value="materialized">Materialized Views</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Cache Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Cache Distribution</CardTitle>
                <CardDescription>
                  Breakdown of cache keys by type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Cache Invalidation Events */}
            <Card>
              <CardHeader>
                <CardTitle>Cache Invalidation Events</CardTitle>
                <CardDescription>
                  Events by cache type in the last {timeRange}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={invalidationChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="events" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Redis Memory Usage */}
          {cacheStats?.redis.available && (
            <Card>
              <CardHeader>
                <CardTitle>Redis Memory Usage</CardTitle>
                <CardDescription>
                  Current memory utilization and statistics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <MemoryStick className="h-4 w-4 mr-2 text-blue-500" />
                      <span className="text-sm font-medium">Used Memory</span>
                    </div>
                    <div className="text-2xl font-bold">
                      {formatBytes((cacheStats.redis.metrics as any).used_memory || 0)}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <HardDrive className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm font-medium">Peak Memory</span>
                    </div>
                    <div className="text-2xl font-bold">
                      {formatBytes((cacheStats.redis.metrics as any).used_memory_peak || 0)}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Cpu className="h-4 w-4 mr-2 text-orange-500" />
                      <span className="text-sm font-medium">Connected Clients</span>
                    </div>
                    <div className="text-2xl font-bold">
                      {(cacheStats.redis.metrics as any).connected_clients || 0}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>
                Key performance indicators for cache effectiveness
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Cache Efficiency</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Hit Rate</span>
                        <span className="text-sm font-medium">
                          {Math.round((performanceMetrics?.metrics.estimatedHitRate || 0) * 100)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Avg Invalidation Interval</span>
                        <span className="text-sm font-medium">
                          {Math.round(performanceMetrics?.metrics.avgInvalidationInterval || 0)}s
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Usage Statistics</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Total Projects</span>
                        <span className="text-sm font-medium">
                          {usagePatterns?.summary.totalProjects || 0}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Avg Response Time</span>
                        <span className="text-sm font-medium">
                          {Math.round(usagePatterns?.summary.avgResponseTime || 0)}ms
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Usage Patterns</CardTitle>
              <CardDescription>
                Access frequency and response times for top projects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={usageChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Area 
                      yAxisId="left" 
                      type="monotone" 
                      dataKey="accesses" 
                      stackId="1" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.6}
                    />
                    <Line 
                      yAxisId="right" 
                      type="monotone" 
                      dataKey="responseTime" 
                      stroke="#ff7300" 
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Priority Breakdown */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Badge variant="destructive" className="mr-2">High</Badge>
                  Priority Projects
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {usagePatterns?.summary.highPriority || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Require immediate cache optimization
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Badge variant="outline" className="mr-2">Medium</Badge>
                  Priority Projects
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {usagePatterns?.summary.mediumPriority || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Should be included in warmup
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Badge variant="secondary" className="mr-2">Low</Badge>
                  Priority Projects
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {usagePatterns?.summary.lowPriority || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  No immediate action needed
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="materialized" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Materialized Views Status</CardTitle>
              <CardDescription>
                Performance optimization through pre-computed views
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {cacheStats?.materializedViews?.map((view, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{view.matviewname}</h4>
                      <p className="text-sm text-muted-foreground">
                        Size: {view.size} | Schema: {view.schemaname}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={(view as any).ispopulated ? 'default' : 'destructive'}>
                        {(view as any).ispopulated ? 'Populated' : 'Empty'}
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        {view.last_refresh_project_summary && (
                          <span>Last: {new Date(view.last_refresh_project_summary).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-8 text-muted-foreground">
                    No materialized views data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}