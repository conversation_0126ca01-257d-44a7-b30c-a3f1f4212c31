"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { 
  Activity, 
  Users, 
  Eye, 
  AlertTriangle, 
  Clock, 
  Monitor, 
  Smartphone, 
  Tablet,
  TrendingUp,
  TrendingDown,
  RefreshCcw
} from 'lucide-react';

/**
 * Real User Monitoring Dashboard
 * 
 * Displays comprehensive RUM data including Core Web Vitals,
 * error tracking, user behavior analytics, and performance insights.
 */

interface RUMMetrics {
  totalSessions: number;
  totalPageViews: number;
  totalErrors: number;
  avgSessionDuration: number;
  bounceRate: number;
  topPages: Array<{ page: string; views: number }>;
  deviceBreakdown: Array<{ type: string; count: number; percentage: number }>;
  performanceMetrics: {
    avgFCP: number;
    avgLCP: number;
    avgFID: number;
    avgCLS: number;
    avgTTFB: number;
  };
  errorsByType: Array<{ type: string; count: number }>;
  userActions: Array<{ action: string; count: number }>;
}

export function RUMDashboard() {
  const [metrics, setMetrics] = useState<RUMMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');
  const [deviceFilter, setDeviceFilter] = useState('all');
  const [autoRefresh, setAutoRefresh] = useState(false);

  useEffect(() => {
    fetchRUMData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchRUMData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [timeRange, deviceFilter, autoRefresh]);

  const fetchRUMData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/rum/dashboard?timeRange=${timeRange}&device=${deviceFilter}`);
      const data = await response.json();
      setMetrics(data);
    } catch (error) {
      console.error('Failed to fetch RUM data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceGrade = (metric: number, thresholds: [number, number]): { grade: string; color: string } => {
    if (metric <= thresholds[0]) return { grade: 'Good', color: 'text-green-600' };
    if (metric <= thresholds[1]) return { grade: 'Needs Improvement', color: 'text-yellow-600' };
    return { grade: 'Poor', color: 'text-red-600' };
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  if (loading && !metrics) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center space-x-2">
            <RefreshCcw className="h-4 w-4 animate-spin" />
            <span>Loading RUM data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Real User Monitoring</h1>
          <p className="text-muted-foreground">
            Performance insights and user behavior analytics
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24 Hours</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={deviceFilter} onValueChange={setDeviceFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Devices</SelectItem>
              <SelectItem value="desktop">Desktop</SelectItem>
              <SelectItem value="mobile">Mobile</SelectItem>
              <SelectItem value="tablet">Tablet</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCcw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </Button>
          
          <Button variant="outline" size="sm" onClick={fetchRUMData}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {metrics && (
        <>
          {/* Key Metrics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalSessions.toLocaleString()}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Page Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalPageViews.toLocaleString()}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Errors</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{metrics.totalErrors}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Session</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatDuration(metrics.avgSessionDuration)}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.bounceRate.toFixed(1)}%</div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="performance" className="space-y-4">
            <TabsList>
              <TabsTrigger value="performance">Core Web Vitals</TabsTrigger>
              <TabsTrigger value="errors">Error Tracking</TabsTrigger>
              <TabsTrigger value="behavior">User Behavior</TabsTrigger>
              <TabsTrigger value="devices">Device Analytics</TabsTrigger>
            </TabsList>

            {/* Core Web Vitals Tab */}
            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">First Contentful Paint</CardTitle>
                    <CardDescription>FCP - Time to first content</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.performanceMetrics.avgFCP.toFixed(0)}ms</div>
                    <Badge 
                      variant="secondary" 
                      className={getPerformanceGrade(metrics.performanceMetrics.avgFCP, [1800, 3000]).color}
                    >
                      {getPerformanceGrade(metrics.performanceMetrics.avgFCP, [1800, 3000]).grade}
                    </Badge>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Largest Contentful Paint</CardTitle>
                    <CardDescription>LCP - Main content render time</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.performanceMetrics.avgLCP.toFixed(0)}ms</div>
                    <Badge 
                      variant="secondary" 
                      className={getPerformanceGrade(metrics.performanceMetrics.avgLCP, [2500, 4000]).color}
                    >
                      {getPerformanceGrade(metrics.performanceMetrics.avgLCP, [2500, 4000]).grade}
                    </Badge>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">First Input Delay</CardTitle>
                    <CardDescription>FID - Input responsiveness</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.performanceMetrics.avgFID.toFixed(0)}ms</div>
                    <Badge 
                      variant="secondary" 
                      className={getPerformanceGrade(metrics.performanceMetrics.avgFID, [100, 300]).color}
                    >
                      {getPerformanceGrade(metrics.performanceMetrics.avgFID, [100, 300]).grade}
                    </Badge>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Cumulative Layout Shift</CardTitle>
                    <CardDescription>CLS - Visual stability</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.performanceMetrics.avgCLS.toFixed(3)}</div>
                    <Badge 
                      variant="secondary" 
                      className={getPerformanceGrade(metrics.performanceMetrics.avgCLS, [0.1, 0.25]).color}
                    >
                      {getPerformanceGrade(metrics.performanceMetrics.avgCLS, [0.1, 0.25]).grade}
                    </Badge>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Time to First Byte</CardTitle>
                    <CardDescription>TTFB - Server response time</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.performanceMetrics.avgTTFB.toFixed(0)}ms</div>
                    <Badge 
                      variant="secondary" 
                      className={getPerformanceGrade(metrics.performanceMetrics.avgTTFB, [800, 1800]).color}
                    >
                      {getPerformanceGrade(metrics.performanceMetrics.avgTTFB, [800, 1800]).grade}
                    </Badge>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Error Tracking Tab */}
            <TabsContent value="errors" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Error Breakdown</CardTitle>
                    <CardDescription>Errors by type in the selected time range</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {metrics.errorsByType.map((error: any) => (
                        <div key={error.type} className="flex items-center justify-between">
                          <span className="text-sm">{error.type}</span>
                          <Badge variant="destructive">{error.count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Top Error Pages</CardTitle>
                    <CardDescription>Pages with the most errors</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {metrics.topPages.slice(0, 5).map((page: any) => (
                        <div key={page.page} className="flex items-center justify-between">
                          <span className="text-sm truncate">{page.page}</span>
                          <span className="text-sm text-muted-foreground">{page.views}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* User Behavior Tab */}
            <TabsContent value="behavior" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Top Pages</CardTitle>
                    <CardDescription>Most visited pages</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {metrics.topPages.map((page: any) => (
                        <div key={page.page} className="flex items-center justify-between">
                          <span className="text-sm truncate">{page.page}</span>
                          <Badge variant="secondary">{page.views}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>User Actions</CardTitle>
                    <CardDescription>Most common user interactions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {metrics.userActions.map((action: any) => (
                        <div key={action.action} className="flex items-center justify-between">
                          <span className="text-sm">{action.action}</span>
                          <Badge variant="outline">{action.count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Device Analytics Tab */}
            <TabsContent value="devices" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {metrics.deviceBreakdown.map((device: any) => {
                  const IconComponent = device.type === 'desktop' ? Monitor : 
                                       device.type === 'mobile' ? Smartphone : Tablet;
                  
                  return (
                    <Card key={device.type}>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium capitalize">
                          {device.type}
                        </CardTitle>
                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{device.count.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                          {device.percentage.toFixed(1)}% of sessions
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}