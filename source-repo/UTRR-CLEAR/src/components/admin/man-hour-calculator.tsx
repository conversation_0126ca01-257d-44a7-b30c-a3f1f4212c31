'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { useToast } from '~/hooks/use-toast';
import { Calculator, Download, Save, RotateCcw, Info } from 'lucide-react';
import { Alert, AlertDescription } from '~/components/ui/alert';

interface ProjectComplexity {
  baseHours: number;
  multiplier: number;
  description: string;
}

interface LaborClassification {
  id: string;
  name: string;
  rate: number;
  standardHours: number;
}

interface CalculationResult {
  classification: string;
  baseHours: number;
  adjustedHours: number;
  rate: number;
  cost: number;
}

export function ManHourCalculator() {
  const [activeTab, setActiveTab] = useState('input');
  const { toast } = useToast();

  // Form state
  const [projectType, setProjectType] = useState('');
  const [projectComplexity, setProjectComplexity] = useState('');
  const [numberOfUtilities, setNumberOfUtilities] = useState(0);
  const [projectDuration, setProjectDuration] = useState(0);
  const [specialRequirements, setSpecialRequirements] = useState<string[]>([]);
  const [conflictComplexity, setConflictComplexity] = useState('');
  const [coordinationLevel, setCoordinationLevel] = useState('');

  // Calculation state
  const [calculationResults, setCalculationResults] = useState<CalculationResult[]>([]);
  const [totalCost, setTotalCost] = useState(0);
  const [totalHours, setTotalHours] = useState(0);

  // Configuration data
  const projectTypes = [
    { value: 'bridge', label: 'Bridge Replacement', baseMultiplier: 1.2 },
    { value: 'road', label: 'Road Reconstruction', baseMultiplier: 1.0 },
    { value: 'intersection', label: 'Intersection Improvement', baseMultiplier: 0.8 },
    { value: 'trail', label: 'Trail Construction', baseMultiplier: 0.6 },
    { value: 'signal', label: 'Signal Modernization', baseMultiplier: 0.7 },
    { value: 'utility', label: 'Utility Relocation', baseMultiplier: 1.1 },
  ];

  const complexityLevels: Record<string, ProjectComplexity> = {
    simple: {
      baseHours: 40,
      multiplier: 0.8,
      description: 'Basic coordination, few conflicts',
    },
    moderate: {
      baseHours: 80,
      multiplier: 1.0,
      description: 'Standard coordination requirements',
    },
    complex: {
      baseHours: 120,
      multiplier: 1.3,
      description: 'Multiple utilities, significant conflicts',
    },
    very_complex: {
      baseHours: 200,
      multiplier: 1.6,
      description: 'Major infrastructure, critical coordination',
    },
  };

  const laborClassifications: LaborClassification[] = [
    { id: 'project_manager', name: 'Project Manager', rate: 125, standardHours: 20 },
    { id: 'senior_coordinator', name: 'Senior Utility Coordinator', rate: 95, standardHours: 80 },
    { id: 'coordinator', name: 'Utility Coordinator', rate: 75, standardHours: 60 },
    { id: 'field_coordinator', name: 'Field Coordinator', rate: 65, standardHours: 40 },
    { id: 'cad_technician', name: 'CAD Technician', rate: 55, standardHours: 30 },
    { id: 'administrative', name: 'Administrative Support', rate: 45, standardHours: 15 },
  ];

  const specialRequirementsList = [
    { id: 'environmental', label: 'Environmental Restrictions', multiplier: 1.15 },
    { id: 'historic', label: 'Historic District', multiplier: 1.25 },
    { id: 'emergency', label: 'Emergency Response', multiplier: 1.4 },
    { id: 'railroad', label: 'Railroad Coordination', multiplier: 1.3 },
    { id: 'airport', label: 'Airport Vicinity', multiplier: 1.2 },
    { id: 'waterway', label: 'Waterway Crossing', multiplier: 1.2 },
    { id: 'traffic', label: 'High Traffic Area', multiplier: 1.1 },
  ];

  const conflictComplexityLevels = [
    { value: 'minimal', label: 'Minimal Conflicts', multiplier: 0.9 },
    { value: 'moderate', label: 'Moderate Conflicts', multiplier: 1.0 },
    { value: 'significant', label: 'Significant Conflicts', multiplier: 1.2 },
    { value: 'severe', label: 'Severe Conflicts', multiplier: 1.5 },
  ];

  const coordinationLevels = [
    { value: 'standard', label: 'Standard Coordination', multiplier: 1.0 },
    { value: 'enhanced', label: 'Enhanced Coordination', multiplier: 1.2 },
    { value: 'intensive', label: 'Intensive Coordination', multiplier: 1.4 },
  ];

  // Handle special requirements change
  const toggleSpecialRequirement = (requirementId: string) => {
    setSpecialRequirements((prev) =>
      prev.includes(requirementId)
        ? prev.filter((id: any) => id !== requirementId)
        : [...prev, requirementId]
    );
  };

  // Calculate man hours
  const calculateManHours = () => {
    if (!projectType || !projectComplexity) {
      toast({
        title: 'Missing Information',
        description: 'Please select project type and complexity level.',
        variant: 'destructive',
      });
      return;
    }

    const selectedProjectType = projectTypes.find((p: any) => p.value === projectType);
    const complexity = complexityLevels[projectComplexity];

    if (!selectedProjectType || !complexity) return;

    let results: CalculationResult[] = [];
    let totalCostSum = 0;
    let totalHoursSum = 0;

    laborClassifications.forEach((classification: any) => {
      // Base hours calculation
      let baseHours = classification.standardHours;

      // Apply complexity multiplier
      baseHours *= complexity.multiplier;

      // Apply project type multiplier
      baseHours *= selectedProjectType.baseMultiplier;

      // Apply utility count factor (more utilities = more coordination)
      const utilityFactor = Math.max(0.5, Math.min(2.0, numberOfUtilities / 8));
      baseHours *= utilityFactor;

      // Apply project duration factor
      if (projectDuration > 0) {
        const durationFactor = Math.max(0.8, Math.min(1.5, projectDuration / 12));
        baseHours *= durationFactor;
      }

      // Apply special requirements multipliers
      let specialMultiplier = 1.0;
      specialRequirements.forEach((reqId: any) => {
        const requirement = specialRequirementsList.find((r: any) => r.id === reqId);
        if (requirement) {
          specialMultiplier *= requirement.multiplier;
        }
      });
      baseHours *= specialMultiplier;

      // Apply conflict complexity multiplier
      if (conflictComplexity) {
        const conflictLevel = conflictComplexityLevels.find((c: any) => c.value === conflictComplexity);
        if (conflictLevel) {
          baseHours *= conflictLevel.multiplier;
        }
      }

      // Apply coordination level multiplier
      if (coordinationLevel) {
        const coordLevel = coordinationLevels.find((c: any) => c.value === coordinationLevel);
        if (coordLevel) {
          baseHours *= coordLevel.multiplier;
        }
      }

      const adjustedHours = Math.round(baseHours * 10) / 10; // Round to 1 decimal
      const cost = adjustedHours * classification.rate;

      results.push({
        classification: classification.name,
        baseHours: classification.standardHours,
        adjustedHours,
        rate: classification.rate,
        cost,
      });

      totalCostSum += cost;
      totalHoursSum += adjustedHours;
    });

    setCalculationResults(results);
    setTotalCost(totalCostSum);
    setTotalHours(totalHoursSum);
    setActiveTab('results');

    toast({
      title: 'Calculation Complete',
      description: `Total estimated hours: ${totalHoursSum.toFixed(1)}, Total cost: $${totalCostSum.toLocaleString()}`,
    });
  };

  // Reset form
  const resetForm = () => {
    setProjectType('');
    setProjectComplexity('');
    setNumberOfUtilities(0);
    setProjectDuration(0);
    setSpecialRequirements([]);
    setConflictComplexity('');
    setCoordinationLevel('');
    setCalculationResults([]);
    setTotalCost(0);
    setTotalHours(0);
    setActiveTab('input');
  };

  // Export results
  const exportResults = () => {
    const data = {
      projectType,
      projectComplexity,
      numberOfUtilities,
      projectDuration,
      specialRequirements,
      conflictComplexity,
      coordinationLevel,
      results: calculationResults,
      totals: {
        hours: totalHours,
        cost: totalCost,
      },
      calculatedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `man-hour-calculation-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: 'Calculation results have been exported.',
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Calculator className="h-8 w-8" />
            Man-Hour Calculator
          </h1>
          <p className="text-muted-foreground">
            Calculate project coordination hours based on complexity and requirements
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetForm}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          {calculationResults.length > 0 && (
            <Button onClick={exportResults}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="input">Project Input</TabsTrigger>
          <TabsTrigger value="results">Calculation Results</TabsTrigger>
          <TabsTrigger value="summary">Summary Report</TabsTrigger>
        </TabsList>

        <TabsContent value="input" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Project Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Project Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="project-type">Project Type</Label>
                  <Select value={projectType} onValueChange={setProjectType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select project type" />
                    </SelectTrigger>
                    <SelectContent>
                      {projectTypes.map((type: any) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="project-complexity">Project Complexity</Label>
                  <Select value={projectComplexity} onValueChange={setProjectComplexity}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select complexity level" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(complexityLevels).map(([key, complexity]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {key.replace('_', ' ').toUpperCase()}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {complexity.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="utilities">Number of Utilities</Label>
                  <Input
                    id="utilities"
                    type="number"
                    min={0}
                    value={numberOfUtilities}
                    onChange={(e: any) => setNumberOfUtilities(parseInt(e.target.value) || 0)}
                    placeholder="Enter number of utility companies"
                  />
                </div>

                <div>
                  <Label htmlFor="duration">Project Duration (months)</Label>
                  <Input
                    id="duration"
                    type="number"
                    min={0}
                    value={projectDuration}
                    onChange={(e: any) => setProjectDuration(parseInt(e.target.value) || 0)}
                    placeholder="Enter project duration"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Advanced Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Advanced Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Conflict Complexity</Label>
                  <Select value={conflictComplexity} onValueChange={setConflictComplexity}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select conflict level" />
                    </SelectTrigger>
                    <SelectContent>
                      {conflictComplexityLevels.map((level: any) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Coordination Level</Label>
                  <Select value={coordinationLevel} onValueChange={setCoordinationLevel}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select coordination level" />
                    </SelectTrigger>
                    <SelectContent>
                      {coordinationLevels.map((level: any) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Special Requirements</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2">
                    {specialRequirementsList.map((requirement: any) => (
                      <div key={requirement.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={requirement.id}
                          checked={specialRequirements.includes(requirement.id)}
                          onChange={() => toggleSpecialRequirement(requirement.id)}
                          className="rounded"
                        />
                        <Label htmlFor={requirement.id} className="text-sm cursor-pointer flex-1">
                          {requirement.label}
                        </Label>
                        <Badge variant="outline" className="text-xs">
                          +{((requirement.multiplier - 1) * 100).toFixed(0)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-center">
            <Button onClick={calculateManHours} size="lg" className="w-full max-w-md">
              <Calculator className="h-4 w-4 mr-2" />
              Calculate Man Hours
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          {calculationResults.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-muted-foreground">Total Hours</p>
                      <p className="text-3xl font-bold">{totalHours.toFixed(1)}</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-muted-foreground">Total Cost</p>
                      <p className="text-3xl font-bold">${totalCost.toLocaleString()}</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-muted-foreground">Average Rate</p>
                      <p className="text-3xl font-bold">
                        ${(totalCost / totalHours).toFixed(0)}/hr
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Detailed Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2">Labor Classification</th>
                          <th className="text-right py-2">Base Hours</th>
                          <th className="text-right py-2">Adjusted Hours</th>
                          <th className="text-right py-2">Rate</th>
                          <th className="text-right py-2">Total Cost</th>
                        </tr>
                      </thead>
                      <tbody>
                        {calculationResults.map((result, index) => (
                          <tr key={index} className="border-b">
                            <td className="py-2 font-medium">{result.classification}</td>
                            <td className="text-right py-2">{result.baseHours}</td>
                            <td className="text-right py-2">{result.adjustedHours}</td>
                            <td className="text-right py-2">${result.rate}</td>
                            <td className="text-right py-2">${result.cost.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="border-t-2 font-bold">
                          <td className="py-2">TOTAL</td>
                          <td className="text-right py-2">
                            {laborClassifications.reduce((sum: any, c: any) => sum + c.standardHours, 0)}
                          </td>
                          <td className="text-right py-2">{totalHours.toFixed(1)}</td>
                          <td className="text-right py-2">-</td>
                          <td className="text-right py-2">${totalCost.toLocaleString()}</td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Calculator className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Calculations Yet</h3>                  <p className="text-muted-foreground">
                    Complete the project input form and click &quot;Calculate Man Hours&quot; to see results.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          {calculationResults.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Project Summary Report</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-2">Project Parameters</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Type:</span>{' '}
                        {projectTypes.find((p: any) => p.value === projectType)?.label}
                      </p>
                      <p>
                        <span className="font-medium">Complexity:</span>{' '}
                        {projectComplexity.replace('_', ' ').toUpperCase()}
                      </p>
                      <p>
                        <span className="font-medium">Utilities:</span> {numberOfUtilities}
                      </p>
                      <p>
                        <span className="font-medium">Duration:</span> {projectDuration} months
                      </p>
                      {conflictComplexity && (
                        <p>
                          <span className="font-medium">Conflicts:</span>{' '}
                          {
                            conflictComplexityLevels.find((c: any) => c.value === conflictComplexity)
                              ?.label
                          }
                        </p>
                      )}
                      {coordinationLevel && (
                        <p>
                          <span className="font-medium">Coordination:</span>{' '}
                          {coordinationLevels.find((c: any) => c.value === coordinationLevel)?.label}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Special Requirements</h4>
                    <div className="space-y-1">
                      {specialRequirements.length > 0 ? (
                        specialRequirements.map((reqId: any) => {
                          const req = specialRequirementsList.find((r: any) => r.id === reqId);
                          return req ? (
                            <Badge key={reqId} variant="outline" className="mr-1 mb-1">
                              {req.label}
                            </Badge>
                          ) : null;
                        })
                      ) : (
                        <p className="text-sm text-muted-foreground">None selected</p>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    This calculation is based on industry standards and historical project data.
                    Actual hours may vary based on specific project conditions and unforeseen
                    circumstances. Consider this estimate as a baseline for project planning and
                    budgeting purposes.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <h3 className="text-lg font-medium mb-2">No Summary Available</h3>
                  <p className="text-muted-foreground">
                    Complete a calculation to view the project summary report.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
