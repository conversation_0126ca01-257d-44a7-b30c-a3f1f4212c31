'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Switch } from '~/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { useToast } from '~/hooks/use-toast';
import { Save, Building, Palette, Settings } from 'lucide-react';

interface OrganizationData {
  name: string;
  slug: string;
  logoUrl?: string;
  primaryColor: string;
  unitSystem: 'imperial' | 'metric';
  forceUnitSystem: boolean;
}

export function OrganizationSettings() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [orgData, setOrgData] = useState<OrganizationData>({
    name: 'EGIS Engineering',
    slug: 'egis',
    primaryColor: '#4299E1',
    unitSystem: 'imperial',
    forceUnitSystem: false,
  });

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Implement organization settings save logic
      const response = await fetch('/api/admin/organization', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orgData),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      toast({
        title: 'Settings saved',
        description: 'Organization settings have been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save organization settings.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Organization Settings</h2>          <p className="text-muted-foreground">
            Configure your organization&apos;s preferences and branding
          </p>
        </div>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Changes'}
          <Save className="ml-2 h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList>
          <TabsTrigger value="general">
            <Building className="mr-2 h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="branding">
            <Palette className="mr-2 h-4 w-4" />
            Branding
          </TabsTrigger>
          <TabsTrigger value="units">
            <Settings className="mr-2 h-4 w-4" />
            Units & Preferences
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Information</CardTitle>
              <CardDescription>Basic organization details and identification</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="org-name">Organization Name</Label>
                <Input
                  id="org-name"
                  value={orgData.name}
                  onChange={(e: any) => setOrgData((prev) => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter organization name"
                />
              </div>
              <div>
                <Label htmlFor="org-slug">Organization Slug</Label>
                <Input
                  id="org-slug"
                  value={orgData.slug}
                  onChange={(e: any) => setOrgData((prev) => ({ ...prev, slug: e.target.value }))}
                  placeholder="enter-url-friendly-slug"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Used in URLs and system identification
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branding">
          <Card>
            <CardHeader>
              <CardTitle>Branding & Appearance</CardTitle>              <CardDescription>
                Customize the look and feel of your organization&apos;s interface
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="logo-url">Logo URL</Label>
                <Input
                  id="logo-url"
                  value={orgData.logoUrl || ''}
                  onChange={(e: any) => setOrgData((prev) => ({ ...prev, logoUrl: e.target.value }))}
                  placeholder="https://example.com/logo.png"
                />
              </div>
              <div>
                <Label htmlFor="primary-color">Primary Color</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="primary-color"
                    type="color"
                    value={orgData.primaryColor}
                    onChange={(e: any) =>
                      setOrgData((prev) => ({ ...prev, primaryColor: e.target.value }))
                    }
                    className="w-20 h-10"
                  />
                  <Input
                    value={orgData.primaryColor}
                    onChange={(e: any) =>
                      setOrgData((prev) => ({ ...prev, primaryColor: e.target.value }))
                    }
                    placeholder="#4299E1"
                    className="flex-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="units">
          <Card>
            <CardHeader>
              <CardTitle>Units & Measurements</CardTitle>
              <CardDescription>Configure default unit systems and user preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="unit-system">Default Unit System</Label>
                <Select
                  value={orgData.unitSystem}
                  onValueChange={(value: 'imperial' | 'metric') =>
                    setOrgData((prev) => ({ ...prev, unitSystem: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="imperial">Imperial (feet, inches, miles)</SelectItem>
                    <SelectItem value="metric">Metric (meters, kilometers)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="force-units">Force Unit System</Label>
                  <p className="text-sm text-muted-foreground">
                    Prevent users from changing their unit preferences
                  </p>
                </div>
                <Switch
                  id="force-units"
                  checked={orgData.forceUnitSystem}
                  onCheckedChange={(checked) =>
                    setOrgData((prev) => ({ ...prev, forceUnitSystem: checked }))
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
