'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import {
  Calculator,
  FileSpreadsheet,
  Settings,
  FileText,
  Users,
  Database,
  ChevronRight,
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const adminModules = [
  {
    title: 'Excel Fee Calculator',
    description: 'Calculate project fees using Excel templates',
    icon: Calculator,
    href: '/admin/fee-calculator',
    status: 'pending',
  },
  {
    title: 'Contract Administration',
    description: 'Document management and approval workflows',
    icon: FileText,
    href: '/admin/contract-admin',
    status: 'pending',
  },
  {
    title: 'Man-Hour Calculator',
    description: 'Project complexity and time calculations',
    icon: FileSpreadsheet,
    href: '/admin/man-hour-calculator',
    status: 'pending',
  },
  {
    title: 'Organization Settings',
    description: 'System configuration and preferences',
    icon: Setting<PERSON>,
    href: '/admin/organization-settings',
    status: 'available',
  },
  {
    title: 'Template Manager',
    description: 'Fee template and badge management',
    icon: Database,
    href: '/admin/template-manager',
    status: 'pending',
  },
  {
    title: 'User Management',
    description: 'User accounts and permissions',
    icon: Users,
    href: '/admin/user-management',
    status: 'available',
  },
];

export function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Administration</h1>
        <p className="text-muted-foreground">
          Manage system settings, users, and administrative tools
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {adminModules.map((module: any) => {
          const Icon = module.icon;
          return (
            <Card key={module.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{module.title}</CardTitle>
                  </div>
                  <Badge variant={module.status === 'available' ? 'default' : 'secondary'}>
                    {module.status}
                  </Badge>
                </div>
                <CardDescription>{module.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full" disabled={module.status === 'pending'}>
                  {module.status === 'available' ? 'Open' : 'Coming Soon'}
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="mt-8">{children}</div>
    </div>
  );
}
