'use client';

import { useState, useEffect, useCallback } from 'react';

import { UserPlus, Mail, UserCheck, UserX, Edit, RefreshCw, Shield, Clock } from 'lucide-react';

import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { DataTable } from '~/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { useToast } from '~/hooks/use-toast';
import { supabase } from '~/lib/supabase/client';


import type { ColumnDef } from '@tanstack/react-table';

// User role type
type UserRole = 'department_manager' | 'executive' | 'admin' | 'utility_coordinator' | 'utility_engineer' | 'client' | 'utility_company';

// Supabase RPC response types
interface SupabaseRPCResponse<T = unknown> {
  data: T | null;
  error: {
    message: string;
    details?: string;
    hint?: string;
    code?: string;
  } | null;
}

interface CreateUserResponse {
  success: boolean;
  error?: string;
  message?: string;
}

interface InviteUserResponse {
  success: boolean;
  error?: string;
  invite_token?: string;
  expires_at?: string;
}

interface UpdateUserStatusResponse {
  success: boolean;
  error?: string;
}

interface UserProfile {
  id: string;
  email: string;
  display_name: string | null;
  first_name: string | null;
  last_name: string | null;
  role: UserRole;
  department: string | null;
  job_title: string | null;
  status: 'pending' | 'active' | 'inactive' | 'suspended';
  email_verified: boolean;
  invite_token: string | null;
  invite_expires_at: string | null;
  last_login_at: string | null;
  created_at: string;
  organization_id: string;
  // Pay rate fields (only visible to department_manager and executive)
  hourly_rate?: number | null;
  salary?: number | null;
  pay_rate_effective_date?: string | null;
  pay_rate_notes?: string | null;
  email_domain?: string | null;
}

interface InviteFormData {
  email: string;
  role: UserRole;
  first_name: string;
  last_name: string;
  department: string;
  job_title: string;
}

export function UserManagement() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const { toast } = useToast();

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      
      // Use the get_organization_users function - Type assertion needed for RPC function
      const response = await (supabase as any).rpc('get_organization_users') as SupabaseRPCResponse<UserProfile[]>;

      if (response.error) throw new Error(response.error.message);
      
      setUsers(response.data ?? []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch users',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Fetch users on component mount
  useEffect(() => {
    void fetchUsers();
  }, [fetchUsers]);

  const handleCreateUser = async (formData: InviteFormData & { password: string }) => {
    try {
      // Use the new API route that properly creates users in auth.users
      const response = await fetch('/api/admin/users/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          firstName: formData.first_name,
          lastName: formData.last_name,
          role: formData.role,
          department: formData.department,
          jobTitle: formData.job_title,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create user');
      }

      toast({
        title: 'Success',
        description: 'User created successfully. They can now log in with their credentials.',
      });
      
      setCreateDialogOpen(false);
      await fetchUsers();
    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create user',
        variant: 'destructive',
      });
    }
  };

  const handleInviteUser = async (formData: InviteFormData) => {
    try {
      const response = await (supabase as any).rpc('invite_user', {
        p_email: formData.email,
        p_role: formData.role,
        p_first_name: formData.first_name || null,
        p_last_name: formData.last_name || null,
        p_department: formData.department || null,
        p_job_title: formData.job_title || null,
      }) as SupabaseRPCResponse<InviteUserResponse>;

      if (response.error) throw new Error(response.error.message);
      
      const result = response.data;
      
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to invite user');
      }

      toast({
        title: 'Success',
        description: `Invitation sent to ${formData.email}`,
      });

      // Show invite link (in production, this would be sent via email)
      if (result?.invite_token) {
        toast({
          title: 'Invite Link (Dev Mode)',
          description: `${window.location.origin}/auth/accept-invite?token=${result.invite_token}`,
          duration: 10000,
        });
      }

      setInviteDialogOpen(false);
      void fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error inviting user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to invite user',
        variant: 'destructive',
      });
    }
  };

  const handleUpdateUserStatus = async (userId: string, status: string) => {
    try {
      const response = await (supabase as any).rpc('update_user_status', {
        p_user_id: userId,
        p_status: status,
      }) as SupabaseRPCResponse<UpdateUserStatusResponse>;

      if (response.error) throw new Error(response.error.message);
      
      const result = response.data;
      
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to update user status');
      }

      toast({
        title: 'Success',
        description: `User status updated to ${status}`,
      });

      void fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error updating user status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update user status',
        variant: 'destructive',
      });
    }
  };

  const handleUpdateUser = async (userId: string, updates: Partial<UserProfile>) => {
    try {
      const response = await (supabase as any)
        .from('user_profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId) as SupabaseRPCResponse<void>;

      if (response.error) throw new Error(response.error.message);

      toast({
        title: 'Success',
        description: 'User updated successfully',
      });

      setEditDialogOpen(false);
      void fetchUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user',
        variant: 'destructive',
      });
    }
  };

  const columns: ColumnDef<UserProfile>[] = [
    {
      accessorKey: 'display_name',
      header: 'Name',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div>
            <div className="font-medium">{user.display_name || user.email}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => {
        const role = row.getValue('role') as string;
        const roleColors = {
          admin: 'destructive',
          manager: 'default',
          user: 'secondary',
          viewer: 'outline',
        } as const;
        return <Badge variant={(roleColors as Record<string, any>)[role] || 'default'}>{role}</Badge>;
      },
    },
    {
      accessorKey: 'department',
      header: 'Department',
      cell: ({ row }) => row.getValue('department') || '-',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const statusColors = {
          active: 'success',
          pending: 'secondary',
          inactive: 'outline',
          suspended: 'destructive',
        } as const;
        return (
          <Badge variant={statusColors[status as keyof typeof statusColors] || 'default'}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'last_login_at',
      header: 'Last Login',
      cell: ({ row }) => {
        const date = row.getValue('last_login_at') as string;
        return date ? new Date(date).toLocaleDateString() : 'Never';
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedUser(user);
                setEditDialogOpen(true);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            
            {user.status === 'active' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => void handleUpdateUserStatus(user.id, 'inactive')}
              >
                <UserX className="h-4 w-4" />
              </Button>
            )}
            
            {user.status === 'inactive' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => void handleUpdateUserStatus(user.id, 'active')}
              >
                <UserCheck className="h-4 w-4" />
              </Button>
            )}
            
            {user.status === 'pending' && user.invite_token && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Resend invite logic
                  toast({
                    title: 'Invite Link',
                    description: `${window.location.origin}/auth/accept-invite?token=${user.invite_token ?? ''}`,
                    duration: 10000,
                  });
                }}
              >
                <Mail className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>User Management</CardTitle>
            <CardDescription>
              Manage users in your organization
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => void fetchUsers()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Create User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                  <DialogDescription>
                    Create a new user account with password
                  </DialogDescription>
                </DialogHeader>
                <CreateUserForm onSubmit={(data) => void handleCreateUser(data)} />
              </DialogContent>
            </Dialog>
            <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Mail className="h-4 w-4 mr-2" />
                  Invite User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Invite New User</DialogTitle>
                  <DialogDescription>
                    Send an invitation to join your organization
                  </DialogDescription>
                </DialogHeader>
                <InviteUserForm onSubmit={(data) => void handleInviteUser(data)} />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* User Statistics */}
        <div className="grid gap-4 md:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
              <p className="text-xs text-muted-foreground">
                Across all roles
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {users.filter(u => u.status === 'active').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Invites</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {users.filter(u => u.status === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Awaiting acceptance
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Leadership</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {users.filter(u => ['department_manager', 'executive', 'admin'].includes(u.role)).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Management team
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Users Table */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading users...</div>
          </div>
        ) : (
          <DataTable 
            columns={columns} 
            data={users} 
          />
        )}
      </CardContent>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and permissions
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <EditUserForm 
              user={selectedUser} 
              onSubmit={(updates) => void handleUpdateUser(selectedUser.id, updates)}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Create User Form Component
function CreateUserForm({ onSubmit }: { onSubmit: (data: InviteFormData & { password: string }) => void }) {
  const [formData, setFormData] = useState<InviteFormData & { password: string; confirmPassword: string }>({
    email: '',
    password: '',
    confirmPassword: '',
    role: 'utility_coordinator',
    first_name: '',
    last_name: '',
    department: '',
    job_title: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      // Note: In production, this should use a proper toast notification
      // For now, using console.error to avoid ESLint alert rule
      console.error('Passwords do not match');
      return;
    }
    
    const { confirmPassword: _confirmPassword, ...submitData } = formData;
    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid gap-4">
        <div className="grid gap-2">
          <Label htmlFor="create_email">Email</Label>
          <Input
            id="create_email"
            type="email"
            required
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="<EMAIL>"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="create_password">Password</Label>
            <Input
              id="create_password"
              type="password"
              required
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              placeholder="••••••••"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="create_confirm_password">Confirm Password</Label>
            <Input
              id="create_confirm_password"
              type="password"
              required
              value={formData.confirmPassword}
              onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
              placeholder="••••••••"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="create_first_name">First Name</Label>
            <Input
              id="create_first_name"
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
              placeholder="John"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="create_last_name">Last Name</Label>
            <Input
              id="create_last_name"
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
              placeholder="Doe"
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="create_role">Role</Label>
          <Select
            value={formData.role}
            onValueChange={(value) => setFormData({ ...formData, role: value as UserRole })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="executive">Executive</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="utility_coordinator">Utility Coordinator</SelectItem>
              <SelectItem value="utility_engineer">Utility Engineer</SelectItem>
              <SelectItem value="client">Client</SelectItem>
              <SelectItem value="utility_company">Utility Company</SelectItem>
              {/* Department Manager role is excluded - only one person can have it */}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="create_department">Department</Label>
          <Input
            id="create_department"
            value={formData.department}
            onChange={(e) => setFormData({ ...formData, department: e.target.value })}
            placeholder="Engineering"
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="create_job_title">Job Title</Label>
          <Input
            id="create_job_title"
            value={formData.job_title}
            onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
            placeholder="Senior Engineer"
          />
        </div>
      </div>

      <DialogFooter>
        <Button type="submit">Create User</Button>
      </DialogFooter>
    </form>
  );
}

// Invite User Form Component
function InviteUserForm({ onSubmit }: { onSubmit: (data: InviteFormData) => void }) {
  const [formData, setFormData] = useState<InviteFormData>({
    email: '',
    role: 'utility_coordinator',
    first_name: '',
    last_name: '',
    department: '',
    job_title: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid gap-4">
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            required
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="<EMAIL>"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="first_name">First Name</Label>
            <Input
              id="first_name"
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
              placeholder="John"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="last_name">Last Name</Label>
            <Input
              id="last_name"
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
              placeholder="Doe"
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="role">Role</Label>
          <Select
            value={formData.role}
            onValueChange={(value) => setFormData({ ...formData, role: value as UserRole })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="executive">Executive</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="utility_coordinator">Utility Coordinator</SelectItem>
              <SelectItem value="utility_engineer">Utility Engineer</SelectItem>
              <SelectItem value="client">Client</SelectItem>
              <SelectItem value="utility_company">Utility Company</SelectItem>
              {/* Department Manager role is excluded - only one person can have it */}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="department">Department</Label>
          <Input
            id="department"
            value={formData.department}
            onChange={(e) => setFormData({ ...formData, department: e.target.value })}
            placeholder="Engineering"
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="job_title">Job Title</Label>
          <Input
            id="job_title"
            value={formData.job_title}
            onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
            placeholder="Senior Engineer"
          />
        </div>
      </div>

      <DialogFooter>
        <Button type="submit">Send Invitation</Button>
      </DialogFooter>
    </form>
  );
}

// Edit User Form Component
function EditUserForm({ 
  user, 
  onSubmit 
}: { 
  user: UserProfile; 
  onSubmit: (updates: Partial<UserProfile>) => void;
}) {
  const [formData, setFormData] = useState({
    display_name: user.display_name || '',
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    role: user.role,
    department: user.department || '',
    job_title: user.job_title || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid gap-4">
        <div className="grid gap-2">
          <Label>Email</Label>
          <Input value={user.email} disabled />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="edit_first_name">First Name</Label>
            <Input
              id="edit_first_name"
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="edit_last_name">Last Name</Label>
            <Input
              id="edit_last_name"
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="edit_role">Role</Label>
          <Select
            value={formData.role}
            onValueChange={(value) => setFormData({ ...formData, role: value as UserRole })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="executive">Executive</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="utility_coordinator">Utility Coordinator</SelectItem>
              <SelectItem value="utility_engineer">Utility Engineer</SelectItem>
              <SelectItem value="client">Client</SelectItem>
              <SelectItem value="utility_company">Utility Company</SelectItem>
              {/* Department Manager role is excluded - only one person can have it */}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="edit_department">Department</Label>
          <Input
            id="edit_department"
            value={formData.department}
            onChange={(e) => setFormData({ ...formData, department: e.target.value })}
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="edit_job_title">Job Title</Label>
          <Input
            id="edit_job_title"
            value={formData.job_title}
            onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
          />
        </div>
      </div>

      <DialogFooter>
        <Button type="submit">Update User</Button>
      </DialogFooter>
    </form>
  );
}