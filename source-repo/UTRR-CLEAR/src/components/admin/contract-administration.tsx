'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '~/components/ui/dialog';
import {
  FileSpreadsheet as FileSpreadsheetIcon,
  Plus as PlusIcon,
  File as FileIcon,
  Info as InfoIcon,
  AlertCircle as AlertCircleIcon,
  ExternalLink as ExternalLinkIcon,
  ArrowUpDown as ArrowUpDownIcon,
  Upload as UploadIcon,
  Check as CheckIcon,
  CheckSquare as CheckSquareIcon,
  Download as DownloadIcon,
  Trash2 as TrashIcon,
} from 'lucide-react';
import { ExcelFeeCalculator } from './excel-fee-calculator';
import { Checkbox } from '~/components/ui/checkbox';
import { Textarea } from '~/components/ui/textarea';
import { useToast } from '~/hooks/use-toast';
import { formatDate } from '~/lib/utils';
import { api } from '~/trpc/react';

// Type definitions for MHJ template
interface ProjectFee {
  id: number;
  projectDescription: string;
  clientProjectNumber?: string;
  egisNumber: string;
  client: string;
  workType: string;
  scopeType: string;
  utilitiesOriginal: number;
  utilitiesSupplement1?: number;
  projectManager?: string;
  departmentManager?: string;
  utilityCoordinator: string;
  contractAmount?: string;
  lastStatus?: string;
  latestComment?: string;
  versionNumber?: string;
  exportPath?: string;
  createdAt: string;
  updatedAt: string;
}

interface ContractDocument {
  id: number;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  uploadedBy: string;
  projectId?: number;
  status: 'pending' | 'approved' | 'rejected';
  url: string;
}

interface ContractTask {
  id: number;
  title: string;
  description: string;
  assignee: string;
  dueDate: string;
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  projectId?: number;
}

export function ContractAdministration() {
  const [activeTab, setActiveTab] = useState('overview');
  const [showFeeCalculator, setShowFeeCalculator] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ProjectFee | null>(null);
  const { toast } = useToast();

  // Mock data - would be replaced with tRPC queries
  const projectFees: ProjectFee[] = [
    {
      id: 1,
      projectDescription: 'I-465 Bridge Replacement',
      egisNumber: 'EGIS-2024-001',
      client: 'INDOT',
      workType: 'Bridge Replacement',
      scopeType: 'Standard Utility Coordination',
      utilitiesOriginal: 8,
      utilityCoordinator: 'John Smith',
      contractAmount: '$125,000',
      lastStatus: 'In Review',
      versionNumber: 'v2.1',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z',
    },
    {
      id: 2,
      projectDescription: 'SR-37 Reconstruction Phase 2',
      egisNumber: 'EGIS-2024-002',
      client: 'Hamilton County',
      workType: 'Road Reconstruction',
      scopeType: 'Standard UC Supplement #1',
      utilitiesOriginal: 12,
      utilityCoordinator: 'Sarah Johnson',
      contractAmount: '$180,000',
      lastStatus: 'Approved',
      versionNumber: 'v1.0',
      createdAt: '2024-01-10T09:00:00Z',
      updatedAt: '2024-01-18T16:45:00Z',
    },
  ];

  const contractDocuments: ContractDocument[] = [
    {
      id: 1,
      name: 'Master Service Agreement - INDOT',
      type: 'Contract',
      size: 2.4 * 1024 * 1024, // 2.4 MB
      uploadedAt: '2024-01-15T10:00:00Z',
      uploadedBy: 'Admin User',
      status: 'approved',
      url: '/documents/msa-indot.pdf',
    },
    {
      id: 2,
      name: 'NDA - Hamilton County',
      type: 'Legal',
      size: 512 * 1024, // 512 KB
      uploadedAt: '2024-01-12T14:00:00Z',
      uploadedBy: 'Legal Team',
      status: 'pending',
      url: '/documents/nda-hamilton.pdf',
    },
  ];

  const contractTasks: ContractTask[] = [
    {
      id: 1,
      title: 'Review and approve I-465 fee calculation',
      description: 'Final review of utility coordination fees for bridge replacement project',
      assignee: 'Project Manager',
      dueDate: '2024-01-25T17:00:00Z',
      status: 'in_progress',
      priority: 'high',
      projectId: 1,
    },
    {
      id: 2,
      title: 'Submit SR-37 contract amendment',
      description: 'Process contract amendment for additional utility companies',
      assignee: 'Contract Admin',
      dueDate: '2024-01-30T17:00:00Z',
      status: 'pending',
      priority: 'medium',
      projectId: 2,
    },
  ];

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
      case 'completed':
        return 'default';
      case 'pending':
      case 'in_progress':
        return 'secondary';
      case 'rejected':
      case 'blocked':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'destructive';
      case 'high':
        return 'default';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Contract Administration</h1>
          <p className="text-muted-foreground">
            Manage project fees, contracts, and administrative tasks
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowFeeCalculator(true)} className="flex items-center gap-2">
            <FileSpreadsheetIcon className="h-4 w-4" />
            New Fee Calculation
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <UploadIcon className="h-4 w-4" />
            Upload Document
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Projects</p>
                <p className="text-2xl font-bold">{projectFees.length}</p>
              </div>
              <FileSpreadsheetIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Tasks</p>
                <p className="text-2xl font-bold">
                  {contractTasks.filter((t: any) => t.status === 'pending').length}
                </p>
              </div>
              <CheckSquareIcon className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Documents</p>
                <p className="text-2xl font-bold">{contractDocuments.length}</p>
              </div>
              <FileIcon className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">$305K</p>
              </div>
              <ArrowUpDownIcon className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="projects">Project Fees</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Projects */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Project Fees</CardTitle>
                <CardDescription>Latest fee calculations and project updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projectFees.slice(0, 3).map((project: any) => (
                    <div
                      key={project.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium">{project.projectDescription}</h4>
                        <p className="text-sm text-muted-foreground">
                          {project.egisNumber} • {project.client}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getStatusBadgeVariant(project.lastStatus || 'pending')}>
                          {project.lastStatus}
                        </Badge>
                        <span className="text-sm font-medium">{project.contractAmount}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Pending Tasks */}
            <Card>
              <CardHeader>
                <CardTitle>Pending Tasks</CardTitle>
                <CardDescription>Tasks requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {contractTasks
                    .filter((t: any) => t.status === 'pending' || t.status === 'in_progress')
                    .map((task: any) => (
                      <div
                        key={task.id}
                        className="flex items-start justify-between p-3 border rounded-lg"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium">{task.title}</h4>
                          <p className="text-sm text-muted-foreground">{task.description}</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Due: {formatDate(new Date(task.dueDate))}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <Badge
                            variant={getPriorityBadgeVariant(task.priority)}
                            className="text-xs"
                          >
                            {task.priority}
                          </Badge>
                          <Badge variant={getStatusBadgeVariant(task.status)} className="text-xs">
                            {task.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Fee Management</CardTitle>
              <CardDescription>
                Manage utility coordination fee calculations and project contracts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>EGIS #</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Utilities</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Version</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {projectFees.map((project: any) => (
                    <TableRow key={project.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{project.projectDescription}</div>
                          <div className="text-sm text-muted-foreground">{project.workType}</div>
                        </div>
                      </TableCell>
                      <TableCell>{project.egisNumber}</TableCell>
                      <TableCell>{project.client}</TableCell>
                      <TableCell>{project.utilitiesOriginal}</TableCell>
                      <TableCell>{project.contractAmount}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(project.lastStatus || 'pending')}>
                          {project.lastStatus}
                        </Badge>
                      </TableCell>
                      <TableCell>{project.versionNumber}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm">
                            <ExternalLinkIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <DownloadIcon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedProject(project);
                              setShowFeeCalculator(true);
                            }}
                          >
                            <FileSpreadsheetIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Documents</CardTitle>
              <CardDescription>Manage contracts, agreements, and legal documents</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Uploaded</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {contractDocuments.map((doc: any) => (
                    <TableRow key={doc.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileIcon className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{doc.name}</div>
                            <div className="text-sm text-muted-foreground">
                              Uploaded by {doc.uploadedBy}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{doc.type}</TableCell>
                      <TableCell>{formatFileSize(doc.size)}</TableCell>
                      <TableCell>{formatDate(new Date(doc.uploadedAt))}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(doc.status)}>{doc.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm">
                            <DownloadIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <ExternalLinkIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Administrative Tasks</CardTitle>
              <CardDescription>
                Track and manage contract-related tasks and workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task</TableHead>
                    <TableHead>Assignee</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {contractTasks.map((task: any) => (
                    <TableRow key={task.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{task.title}</div>
                          <div className="text-sm text-muted-foreground">{task.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>{task.assignee}</TableCell>
                      <TableCell>{formatDate(new Date(task.dueDate))}</TableCell>
                      <TableCell>
                        <Badge variant={getPriorityBadgeVariant(task.priority)}>
                          {task.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(task.status)}>{task.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm">
                            <CheckIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <ExternalLinkIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Fee Calculator Dialog */}
      <Dialog open={showFeeCalculator} onOpenChange={setShowFeeCalculator}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
          <ExcelFeeCalculator
            onClose={() => {
              setShowFeeCalculator(false);
              setSelectedProject(null);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
