'use client';

import { useState, useCallback, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { api } from "~/trpc/react";
import { toast } from "~/hooks/use-toast";
import { 
  Plus, 
  Trash2, 
  Edit, 
  ChevronRight, 
  ChevronDown, 
  MoreVertical,
  FileSpreadsheet,
  FileText,
  Save,
  X,
  FolderOpen,
  PlayCircle,
  Copy
} from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Separator } from "~/components/ui/separator";
import { ScrollArea } from "~/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "~/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "~/components/ui/dropdown-menu";
import dynamic from "next/dynamic";
import { Skeleton } from "~/components/ui/skeleton";

// Dynamic imports for heavy components
const ProjectTemplateEditor = dynamic(
  () => import('~/components/templates/project-template-editor').then(mod => ({ default: mod.ProjectTemplateEditor })),
  {
    loading: () => <Skeleton className="h-[600px] w-full" />,
    ssr: false
  }
);

const MarkdownEditor = dynamic(
  () => import('~/components/ui/markdown-editor').then(mod => ({ default: mod.MarkdownEditor })),
  {
    loading: () => <Skeleton className="h-[600px] w-full" />,
    ssr: false
  }
);

// Template categories (can be extended)
const TEMPLATE_CATEGORIES = [
  { id: 'project', name: 'Project Templates', icon: FileSpreadsheet },
  { id: 'document', name: 'Document Templates', icon: FileText },
  { id: 'fee', name: 'Fee & Scope Templates', icon: FileSpreadsheet },
  { id: 'communication', name: 'Communication Templates', icon: FileText },
];

interface Template {
  id: string;
  name: string;
  description?: string;
  category: string;
  type: 'structured' | 'document';
  icon?: string;
  color?: string;
  is_active: boolean;
  is_default: boolean;
  settings?: any;
  content?: string; // For markdown templates
  created_at: Date;
  updated_at: Date;
  created_by?: {
    first_name: string;
    last_name: string;
  };
}

export function TemplateManagerV2() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [playgroundMode, setPlaygroundMode] = useState(false);
  const [playgroundType, setPlaygroundType] = useState<'structured' | 'document'>('structured');
  const [playgroundData, setPlaygroundData] = useState<any>({});
  const [markdownContent, setMarkdownContent] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [templateToSave, setTemplateToSave] = useState({
    name: '',
    description: '',
    category: 'project',
  });
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [showNewCategoryDialog, setShowNewCategoryDialog] = useState(false);
  const [newCategory, setNewCategory] = useState({ id: '', name: '' });

  // Load templates
  const { data: templates = [], isLoading } = api.projectTemplates.getAll.useQuery();

  // Group templates by category
  const templatesByCategory = useMemo(() => {
    const grouped: Record<string, Template[]> = {};
    
    templates.forEach((template: any) => {
      // Add default category and type for templates from API
      const enrichedTemplate = {
        ...template,
        category: template.category || 'project',
        type: template.type || 'structured'
      };
      const category = enrichedTemplate.category;
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(enrichedTemplate);
    });
    
    return grouped;
  }, [templates]);

  // Filter templates by selected category
  const filteredTemplates = useMemo(() => {
    if (selectedCategory === 'all') {
      return templates.map((template: any) => ({
        ...template,
        category: template.category || 'project',
        type: template.type || 'structured'
      }));
    }
    return templatesByCategory[selectedCategory] || [];
  }, [templates, templatesByCategory, selectedCategory]);

  // Save template mutation
  const createTemplate = api.projectTemplates.create.useMutation({
    onSuccess: () => {
      toast({
        title: "Template Created",
        description: "Your template has been saved successfully.",
      });
      setShowSaveDialog(false);
      setPlaygroundMode(false);
      setTemplateToSave({ name: '', description: '', category: 'project' });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Handle playground save
  const handleSaveFromPlayground = () => {
    if (!templateToSave.name) {
      toast({
        title: "Error",
        description: "Please enter a template name",
        variant: "destructive",
      });
      return;
    }

    const templateData = {
      name: templateToSave.name,
      description: templateToSave.description,
      category: templateToSave.category,
      type: playgroundType,
      settings: playgroundType === 'structured' ? playgroundData : undefined,
      content: playgroundType === 'document' ? markdownContent : undefined,
    };

    createTemplate.mutate(templateData as any);
  };

  // Enter playground mode
  const enterPlayground = (type: 'structured' | 'document') => {
    setPlaygroundType(type);
    setPlaygroundMode(true);
    setPlaygroundData({});
    setMarkdownContent('# New Template\n\nStart creating your template here...');
  };

  // Load template into playground
  const loadTemplateToPlayground = (template: Template) => {
    setSelectedTemplate(template);
    setPlaygroundType(template.type);
    setPlaygroundMode(true);
    
    if (template.type === 'structured') {
      setPlaygroundData(template.settings || {});
    } else {
      setMarkdownContent(template.content || '');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Template Manager</h2>
          <p className="text-muted-foreground">
            Create and manage templates for projects, documents, and more
          </p>
        </div>
        {!playgroundMode && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Create Template
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => enterPlayground('structured')}>
                <FileSpreadsheet className="mr-2 h-4 w-4" /> Structured Template (Tables)
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => enterPlayground('document')}>
                <FileText className="mr-2 h-4 w-4" /> Document Template (Markdown)
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {playgroundMode ? (
        // Playground Mode
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Template Playground</CardTitle>
                <CardDescription>
                  {playgroundType === 'structured' 
                    ? 'Design your structured template with columns and tasks'
                    : 'Create your document template using markdown'
                  }
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={() => setPlaygroundMode(false)}>
                  <X className="mr-2 h-4 w-4" /> Exit Playground
                </Button>
                <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Save className="mr-2 h-4 w-4" /> Save as Template
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Save Template</DialogTitle>
                      <DialogDescription>
                        Give your template a name and assign it to a category
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Template Name</Label>
                        <Input 
                          value={templateToSave.name}
                          onChange={(e) => setTemplateToSave({...templateToSave, name: e.target.value})}
                          placeholder="e.g., Standard Utility Coordination Template"
                        />
                      </div>
                      <div>
                        <Label>Description</Label>
                        <Input 
                          value={templateToSave.description}
                          onChange={(e) => setTemplateToSave({...templateToSave, description: e.target.value})}
                          placeholder="Brief description of the template"
                        />
                      </div>
                      <div>
                        <Label>Category</Label>
                        <Select 
                          value={templateToSave.category}
                          onValueChange={(value) => setTemplateToSave({...templateToSave, category: value})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {TEMPLATE_CATEGORIES.map(cat => (
                              <SelectItem key={cat.id} value={cat.id}>
                                {cat.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSaveFromPlayground}>
                        Save Template
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {playgroundType === 'structured' ? (
              <ProjectTemplateEditor 
                data={playgroundData}
                onChange={setPlaygroundData}
                editMode={true}
              />
            ) : (
              <div className="space-y-4">
                <MarkdownEditor
                  content={markdownContent}
                  onChange={setMarkdownContent}
                  className="min-h-[600px]"
                />
                <div className="rounded-lg border p-4">
                  <h4 className="mb-2 font-semibold">Preview</h4>
                  <div 
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ 
                      __html: markdownContent // In production, use a markdown parser
                    }} 
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        // Template Library Mode
        <>
          {/* Category Tabs */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <div className="flex items-center justify-between">
              <TabsList>
                <TabsTrigger value="all">All Templates</TabsTrigger>
                {TEMPLATE_CATEGORIES.map(cat => (
                  <TabsTrigger key={cat.id} value={cat.id}>
                    <cat.icon className="mr-2 h-4 w-4" />
                    {cat.name}
                  </TabsTrigger>
                ))}
              </TabsList>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowNewCategoryDialog(true)}
              >
                <Plus className="mr-2 h-4 w-4" /> Add Category
              </Button>
            </div>

            <TabsContent value={selectedCategory} className="mt-6">
              {isLoading ? (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {[1, 2, 3].map(i => (
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              ) : filteredTemplates.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <FolderOpen className="mb-4 h-12 w-12 text-muted-foreground" />
                    <p className="text-lg font-medium">No templates found</p>
                    <p className="text-sm text-muted-foreground">
                      Create your first template using the playground
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredTemplates.map((template) => (
                    <Card key={template.id} className="relative">
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-lg">{template.name}</CardTitle>
                            <CardDescription className="mt-1">
                              {template.description}
                            </CardDescription>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => loadTemplateToPlayground(template)}>
                                <PlayCircle className="mr-2 h-4 w-4" /> Open in Playground
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Copy className="mr-2 h-4 w-4" /> Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="mr-2 h-4 w-4" /> Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {template.type === 'structured' ? 'Structured' : 'Document'}
                          </Badge>
                          {template.is_default && (
                            <Badge variant="secondary">Default</Badge>
                          )}
                        </div>
                        <div className="mt-4 text-sm text-muted-foreground">
                          Created by {template.created_by?.first_name} {template.created_by?.last_name}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Add Category Dialog */}
          <Dialog open={showNewCategoryDialog} onOpenChange={setShowNewCategoryDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Template Category</DialogTitle>
                <DialogDescription>
                  Create a new category to organize your templates
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Category ID</Label>
                  <Input 
                    value={newCategory.id}
                    onChange={(e) => setNewCategory({...newCategory, id: e.target.value})}
                    placeholder="e.g., meeting"
                  />
                </div>
                <div>
                  <Label>Category Name</Label>
                  <Input 
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({...newCategory, name: e.target.value})}
                    placeholder="e.g., Meeting Templates"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowNewCategoryDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={() => {
                  // In production, save to database
                  TEMPLATE_CATEGORIES.push({
                    id: newCategory.id,
                    name: newCategory.name,
                    icon: FileText
                  });
                  setShowNewCategoryDialog(false);
                  setNewCategory({ id: '', name: '' });
                }}>
                  Add Category
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
}