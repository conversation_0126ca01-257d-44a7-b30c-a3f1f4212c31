'use client';

import { useState, useC<PERSON>back, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { api } from "~/trpc/react";
import { toast } from "~/hooks/use-toast";
import { 
  Plus, 
  Trash2, 
  Edit, 
  ChevronRight, 
  ChevronDown, 
  MoreVertical,
  FileSpreadsheet,
  Save,
  X
} from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Separator } from "~/components/ui/separator";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
} from "~/components/ui/context-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "~/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "~/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  getExpandedRowModel,
} from "@tanstack/react-table";
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  ExpandedState,
} from "@tanstack/react-table";

// Types for template structure
interface TemplateTask {
  id: string;
  name: string;
  section: string;
  taskOwner?: string;
  status: string;
  taskType: string;
  mhjId?: string;
  priority: string;
  targetDuration?: number;
  dependentOn?: string;
  dmFeeHours?: number;
  pmFeeHours?: number;
  ctFeeHours?: number;
  subtasks?: TemplateTask[];
  isSubtask?: boolean;
  parentId?: string;
}

interface TemplateColumn {
  id: string;
  name: string;
  field: string;
  type: 'text' | 'date' | 'number' | 'select' | 'boolean';
  required?: boolean;
  options?: string[];
  width?: number;
  visible: boolean;
  order: number;
}

// Default columns from UC_PLAN_MASTER_TEMPLATE
const DEFAULT_COLUMNS: TemplateColumn[] = [
  { id: 'name', name: 'Name', field: 'name', type: 'text', required: true, visible: true, order: 0, width: 300 },
  { id: 'taskOwner', name: 'Task Owner', field: 'taskOwner', type: 'text', visible: true, order: 1, width: 150 },
  { id: 'status', name: 'Status', field: 'status', type: 'select', 
    options: ['Theoretical | Proposed', 'In Progress', 'Complete', 'N/A', 'Not Specified'], 
    visible: true, order: 2, width: 180 },
  { id: 'targetStart', name: 'Target Schedule - Start', field: 'targetStart', type: 'date', visible: true, order: 3, width: 150 },
  { id: 'targetEnd', name: 'Target Schedule - End', field: 'targetEnd', type: 'date', visible: true, order: 4, width: 150 },
  { id: 'targetDuration', name: 'Target Duration', field: 'targetDuration', type: 'number', visible: true, order: 5, width: 120 },
  { id: 'dependentOn', name: 'Dependent On', field: 'dependentOn', type: 'text', visible: true, order: 6, width: 200 },
  { id: 'taskType', name: 'Task Type', field: 'taskType', type: 'select', 
    options: ['UC Task', 'Egis Staff Task', 'Utility Task', 'Milestone', 'Schedule Task'], 
    visible: true, order: 7, width: 150 },
  { id: 'mhjId', name: 'MHJ ID', field: 'mhjId', type: 'text', visible: true, order: 8, width: 100 },
  { id: 'priority', name: 'Priority', field: 'priority', type: 'select', 
    options: ['High', 'Medium', 'Low', 'Not Specified'], 
    visible: true, order: 9, width: 120 },
];

// Sections from the UC Plan template
const TEMPLATE_SECTIONS = [
  "Section 1 -- Initiation Phase",
  "Section 2 -- Research Phase", 
  "Section 3 -- Initial Coordination Phase",
  "Section 4 -- Verification Phase",
  "Section 5 -- Conflict Review Phase",
  "Section 6 -- Work Plan Development Phase",
  "Section 7 -- Agreement Phase",
  "Section 8 -- Utility Construction Phase"
];

export function ProjectTemplateManager() {
  const [editMode, setEditMode] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TemplateTask | null>(null);
  const [columns, setColumns] = useState<TemplateColumn[]>(DEFAULT_COLUMNS);
  const [tasks, setTasks] = useState<TemplateTask[]>([]);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [showTaskDialog, setShowTaskDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<TemplateTask | null>(null);
  const [newColumn, setNewColumn] = useState<Partial<TemplateColumn>>({
    name: '',
    field: '',
    type: 'text',
    visible: true
  });
  const [newTask, setNewTask] = useState<Partial<TemplateTask>>({
    name: '',
    section: TEMPLATE_SECTIONS[0],
    status: 'Not Specified',
    taskType: 'UC Task',
    priority: 'Not Specified'
  });

  // Load template data
  const { data: template, isLoading } = api.projectTemplates.getByName.useQuery({
    name: "Standard Utility Coordination Template"
  });

  // Save template mutation
  const saveTemplate = api.projectTemplates.update.useMutation({
    onSuccess: () => {
      toast({
        title: "Template Saved",
        description: "Your changes have been saved successfully.",
      });
      setEditMode(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Add column
  const handleAddColumn = () => {
    if (!newColumn.name || !newColumn.field) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const column: TemplateColumn = {
      id: `col_${Date.now()}`,
      name: newColumn.name!,
      field: newColumn.field!,
      type: newColumn.type as any,
      visible: true,
      order: columns.length,
      width: 150
    };

    setColumns([...columns, column]);
    setShowColumnDialog(false);
    setNewColumn({ name: '', field: '', type: 'text', visible: true });
  };

  // Remove column
  const handleRemoveColumn = (columnId: string) => {
    setColumns(columns.filter(col => col.id !== columnId));
  };

  // Add task
  const handleAddTask = () => {
    if (!newTask.name) {
      toast({
        title: "Error",
        description: "Please enter a task name",
        variant: "destructive",
      });
      return;
    }

    const task: TemplateTask = {
      id: `task_${Date.now()}`,
      name: newTask.name!,
      section: newTask.section!,
      status: newTask.status!,
      taskType: newTask.taskType!,
      priority: newTask.priority!,
      targetDuration: newTask.targetDuration,
      dependentOn: newTask.dependentOn,
      mhjId: newTask.mhjId,
    };

    setTasks([...tasks, task]);
    setShowTaskDialog(false);
    setNewTask({
      name: '',
      section: TEMPLATE_SECTIONS[0],
      status: 'Not Specified',
      taskType: 'UC Task',
      priority: 'Not Specified'
    });
  };

  // Delete task
  const handleDeleteTask = (task: TemplateTask) => {
    setTaskToDelete(task);
    setShowDeleteDialog(true);
  };

  const confirmDeleteTask = () => {
    if (taskToDelete) {
      setTasks(tasks.filter(t => t.id !== taskToDelete.id));
      setShowDeleteDialog(false);
      setTaskToDelete(null);
    }
  };

  // Save template
  const handleSaveTemplate = () => {
    const templateData = {
      columns: columns,
      tasks: tasks,
      sections: TEMPLATE_SECTIONS
    };

    saveTemplate.mutate({
      id: template?.id || '',
      settings: templateData
    });
  };

  // Table columns definition
  const tableColumns: ColumnDef<TemplateTask>[] = useMemo(() => {
    const cols: ColumnDef<TemplateTask>[] = [
      {
        id: 'expander',
        header: () => null,
        cell: ({ row }) => {
          if (!row.original.subtasks?.length) return null;
          return (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => row.toggleExpanded()}
            >
              {row.getIsExpanded() ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          );
        },
        size: 40,
      }
    ];

    // Add dynamic columns based on configuration
    columns.filter(col => col.visible).forEach(col => {
      cols.push({
        accessorKey: col.field,
        header: col.name,
        size: col.width,
        cell: ({ row }) => {
          const value = row.original[col.field as keyof TemplateTask];
          
          if (col.type === 'select' && editMode) {
            return (
              <Select 
                value={value as string} 
                onValueChange={(newValue) => {
                  // Update task
                }}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {col.options?.map(opt => (
                    <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            );
          }

          if (col.field === 'name') {
            return (
              <div className="flex items-center gap-2">
                {row.original.isSubtask && <span className="ml-4">↳</span>}
                <span className="font-medium">{value as string}</span>
              </div>
            );
          }

          if (col.field === 'taskType') {
            const typeColors: Record<string, string> = {
              'UC Task': 'bg-blue-100 text-blue-800',
              'Egis Staff Task': 'bg-green-100 text-green-800',
              'Utility Task': 'bg-yellow-100 text-yellow-800',
              'Milestone': 'bg-purple-100 text-purple-800',
              'Schedule Task': 'bg-gray-100 text-gray-800'
            };
            return (
              <Badge className={typeColors[value as string] || ''}>
                {value as string}
              </Badge>
            );
          }

          return <span>{value as string}</span>;
        }
      });
    });

    // Add actions column
    cols.push({
      id: 'actions',
      header: () => null,
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setSelectedTask(row.original)}>
              <Edit className="mr-2 h-4 w-4" /> Edit
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-destructive"
              onClick={() => handleDeleteTask(row.original)}
            >
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      size: 40,
    });

    return cols;
  }, [columns, editMode]);

  // Table instance
  const table = useReactTable({
    data: tasks,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      columnFilters,
      sorting,
      columnVisibility,
      expanded: expandedRows,
    },
  });

  if (isLoading) {
    return <div>Loading template...</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Standard Utility Coordination Template</CardTitle>
              <CardDescription>
                Manage the master template with 87 tasks across 8 phases
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {editMode ? (
                <>
                  <Button variant="outline" onClick={() => setEditMode(false)}>
                    <X className="mr-2 h-4 w-4" /> Cancel
                  </Button>
                  <Button onClick={handleSaveTemplate}>
                    <Save className="mr-2 h-4 w-4" /> Save Changes
                  </Button>
                </>
              ) : (
                <Button onClick={() => setEditMode(true)}>
                  <Edit className="mr-2 h-4 w-4" /> Edit Template
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Column Management */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Template Columns</h3>
              {editMode && (
                <Dialog open={showColumnDialog} onOpenChange={setShowColumnDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="mr-2 h-4 w-4" /> Add Column
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add New Column</DialogTitle>
                      <DialogDescription>
                        Add a new column to the template
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Column Name</Label>
                        <Input 
                          value={newColumn.name} 
                          onChange={(e) => setNewColumn({...newColumn, name: e.target.value})}
                          placeholder="e.g., Custom Field"
                        />
                      </div>
                      <div>
                        <Label>Field ID</Label>
                        <Input 
                          value={newColumn.field} 
                          onChange={(e) => setNewColumn({...newColumn, field: e.target.value})}
                          placeholder="e.g., customField"
                        />
                      </div>
                      <div>
                        <Label>Type</Label>
                        <Select 
                          value={newColumn.type} 
                          onValueChange={(value) => setNewColumn({...newColumn, type: value as any})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">Text</SelectItem>
                            <SelectItem value="number">Number</SelectItem>
                            <SelectItem value="date">Date</SelectItem>
                            <SelectItem value="select">Dropdown</SelectItem>
                            <SelectItem value="boolean">Yes/No</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <Button onClick={handleAddColumn} className="w-full">
                        Add Column
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {columns.map(col => (
                <Badge 
                  key={col.id} 
                  variant={col.visible ? "default" : "outline"}
                  className="flex items-center gap-1"
                >
                  {col.name}
                  {editMode && !col.required && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1"
                      onClick={() => handleRemoveColumn(col.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </Badge>
              ))}
            </div>
          </div>

          <Separator className="my-6" />

          {/* Task Management */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Template Tasks</h3>
              {editMode && (
                <Dialog open={showTaskDialog} onOpenChange={setShowTaskDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="mr-2 h-4 w-4" /> Add Task
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add New Task</DialogTitle>
                      <DialogDescription>
                        Add a new task to the template
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Task Name</Label>
                        <Input 
                          value={newTask.name} 
                          onChange={(e) => setNewTask({...newTask, name: e.target.value})}
                          placeholder="Enter task name"
                        />
                      </div>
                      <div>
                        <Label>Section</Label>
                        <Select 
                          value={newTask.section} 
                          onValueChange={(value) => setNewTask({...newTask, section: value})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {TEMPLATE_SECTIONS.map(section => (
                              <SelectItem key={section} value={section}>{section}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Task Type</Label>
                        <Select 
                          value={newTask.taskType} 
                          onValueChange={(value) => setNewTask({...newTask, taskType: value})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="UC Task">UC Task</SelectItem>
                            <SelectItem value="Egis Staff Task">Egis Staff Task</SelectItem>
                            <SelectItem value="Utility Task">Utility Task</SelectItem>
                            <SelectItem value="Milestone">Milestone</SelectItem>
                            <SelectItem value="Schedule Task">Schedule Task</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>MHJ ID</Label>
                        <Input 
                          value={newTask.mhjId} 
                          onChange={(e) => setNewTask({...newTask, mhjId: e.target.value})}
                          placeholder="e.g., 1.01"
                        />
                      </div>
                      <Button onClick={handleAddTask} className="w-full">
                        Add Task
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Task Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead key={header.id} style={{ width: header.getSize() }}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row) => (
                      <ContextMenu key={row.id}>
                        <ContextMenuTrigger asChild>
                          <TableRow
                            data-state={row.getIsSelected() && "selected"}
                            className="cursor-pointer"
                          >
                            {row.getVisibleCells().map((cell) => (
                              <TableCell key={cell.id}>
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        </ContextMenuTrigger>
                        <ContextMenuContent>
                          <ContextMenuItem onClick={() => setSelectedTask(row.original)}>
                            <Edit className="mr-2 h-4 w-4" /> Edit Task
                          </ContextMenuItem>
                          <ContextMenuItem>
                            <Plus className="mr-2 h-4 w-4" /> Add Subtask
                          </ContextMenuItem>
                          <ContextMenuSeparator />
                          <ContextMenuItem 
                            className="text-destructive"
                            onClick={() => handleDeleteTask(row.original)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" /> Delete Task
                          </ContextMenuItem>
                        </ContextMenuContent>
                      </ContextMenu>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={tableColumns.length}
                        className="h-24 text-center"
                      >
                        No tasks found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Task</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{taskToDelete?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteTask}
              className="bg-destructive text-destructive-foreground"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}