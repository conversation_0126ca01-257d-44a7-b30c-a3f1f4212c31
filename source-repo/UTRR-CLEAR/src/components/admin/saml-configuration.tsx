"use client";

import { useState } from 'react';
import { api } from '~/trpc/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import { <PERSON><PERSON> } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Switch } from '~/components/ui/switch';
import { Badge } from '~/components/ui/badge';
import { useToast } from '~/hooks/use-toast';
import { Loader2, Plus, Shield, Trash2, <PERSON><PERSON>, <PERSON><PERSON>ink, CheckCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const samlConfigSchema = z.object({
  name: z.string().min(1, "Name is required"),
  entryPoint: z.string().url("Entry point must be a valid URL"),
  issuer: z.string().min(1, "Issuer is required"),
  certificate: z.string().min(1, "Certificate is required"),
  signatureAlgorithm: z.enum(["sha256", "sha512"]).optional(),
  metadataUrl: z.string().url().optional().or(z.literal(''))
});

type SAMLConfigFormData = z.infer<typeof samlConfigSchema>;

export function SAMLConfiguration() {
  const { toast } = useToast();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<string | null>(null);
  const [showMetadata, setShowMetadata] = useState(false);

  const { data: configs, isLoading, refetch } = api.saml.getOrganizationConfigs.useQuery();
  const { data: metadata } = api.saml.getMetadata.useQuery(undefined, {
    enabled: showMetadata
  });

  const createConfig = api.saml.createConfig.useMutation({
    onSuccess: () => {
      toast({
        title: "SAML configuration created",
        description: "The SAML configuration has been created successfully.",
      });
      setIsAddDialogOpen(false);
      refetch();
      form.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Error creating configuration",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateConfig = api.saml.updateConfig.useMutation({
    onSuccess: () => {
      toast({
        title: "Configuration updated",
        description: "The SAML configuration has been updated successfully.",
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: "Error updating configuration",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteConfig = api.saml.deleteConfig.useMutation({
    onSuccess: () => {
      toast({
        title: "Configuration deleted",
        description: "The SAML configuration has been deleted successfully.",
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting configuration",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const testConfig = api.saml.testConfig.useMutation({
    onSuccess: (data: any) => {
      toast({
        title: "Test successful",
        description: data.message,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Test failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const form = useForm<SAMLConfigFormData>({
    resolver: zodResolver(samlConfigSchema),
    defaultValues: {
      name: '',
      entryPoint: '',
      issuer: '',
      certificate: '',
      signatureAlgorithm: 'sha256',
      metadataUrl: ''
    }
  });

  const handleCopyMetadata = () => {
    if (metadata?.metadata) {
      navigator.clipboard.writeText(metadata.metadata);
      toast({
        title: "Metadata copied",
        description: "SAML metadata has been copied to clipboard.",
      });
    }
  };

  const handleToggleConfig = (configId: string, enabled: boolean) => {
    updateConfig.mutate({
      configId,
      data: { enabled }
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                SAML Single Sign-On
              </CardTitle>
              <CardDescription>
                Configure SAML 2.0 identity providers for enterprise single sign-on
              </CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add SAML Provider
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[625px]">
                <DialogHeader>
                  <DialogTitle>Add SAML Provider</DialogTitle>
                  <DialogDescription>
                    Configure a new SAML 2.0 identity provider for your organization.
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit((data) => createConfig.mutate(data))} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Provider Name</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Okta, Azure AD" {...field} />
                          </FormControl>
                          <FormDescription>
                            A friendly name to identify this SAML provider
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="entryPoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SSO URL (Entry Point)</FormLabel>
                          <FormControl>
                            <Input placeholder="https://your-idp.com/sso/saml" {...field} />
                          </FormControl>
                          <FormDescription>
                            The URL where users will be redirected for authentication
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="issuer"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Issuer</FormLabel>
                          <FormControl>
                            <Input placeholder="https://your-idp.com" {...field} />
                          </FormControl>
                          <FormDescription>
                            The unique identifier for your identity provider
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="certificate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>X.509 Certificate</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="-----BEGIN CERTIFICATE-----&#10;...&#10;-----END CERTIFICATE-----"
                              className="font-mono text-xs"
                              rows={6}
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            The public certificate from your identity provider
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="signatureAlgorithm"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Signature Algorithm</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select algorithm" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="sha256">SHA-256</SelectItem>
                              <SelectItem value="sha512">SHA-512</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="metadataUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Metadata URL (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="https://your-idp.com/metadata" {...field} />
                          </FormControl>
                          <FormDescription>
                            URL to fetch SAML metadata automatically
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <DialogFooter>
                      <Button type="submit" disabled={createConfig.isPending}>
                        {createConfig.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Create Configuration
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {configs && configs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Shield className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>No SAML providers configured yet.</p>
              <p className="text-sm mt-2">Add a provider to enable SSO for your organization.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {configs?.map((config: any) => (
                <div key={config.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">{config.name}</h3>
                        <Badge variant={config.enabled ? "default" : "secondary"}>
                          {config.enabled ? "Enabled" : "Disabled"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Issuer: {config.issuer}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Entry Point: {config.entry_point}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={config.enabled}
                        onCheckedChange={(checked) => handleToggleConfig(config.id, checked)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testConfig.mutate({ configId: config.id })}
                        disabled={testConfig.isPending}
                      >
                        {testConfig.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                        Test
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteConfig.mutate({ configId: config.id })}
                        disabled={deleteConfig.isPending}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          <div className="mt-6 pt-6 border-t">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Service Provider Metadata</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Use this metadata to configure your identity provider
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowMetadata(!showMetadata)}
                >
                  {showMetadata ? "Hide" : "Show"} Metadata
                </Button>
                {showMetadata && metadata && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyMetadata}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            
            {showMetadata && metadata && (
              <div className="mt-4">
                <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-xs">
                  <code>{metadata.metadata}</code>
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}