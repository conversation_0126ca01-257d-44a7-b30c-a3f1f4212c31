'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Label } from '~/components/ui/label';
import { Switch } from '~/components/ui/switch';
import { Slider } from '~/components/ui/slider';
import { Button } from '~/components/ui/button';
import { Separator } from '~/components/ui/separator';
import { OrganizationLogo } from '~/components/ui/organization-logo';
import { Palette, Save, RefreshCw, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { toast } from 'sonner';
import { api } from '~/trpc/react';
import { cn } from '~/lib/utils';
import { safeLog } from '~/lib/error-handler';

interface LogoDisplayConfig {
  headerLogo: {
    useDarkMode: boolean;
    scale: number; // Percentage scale (0.5 = 50%, 1 = 100%, 1.5 = 150%)
  };
  signinLogo: {
    useDarkMode: boolean;
    scale: number;
  };
  signinFooter: {
    enabled: boolean;
    scale: number;
  };
}

const defaultConfig: LogoDisplayConfig = {
  headerLogo: {
    useDarkMode: false,
    scale: 1, // 100%
  },
  signinLogo: {
    useDarkMode: true,
    scale: 1, // 100%
  },
  signinFooter: {
    enabled: true,
    scale: 1, // 100%
  },
};

// Container constraints
const CONSTRAINTS = {
  header: {
    maxHeight: 48, // Header is typically 48-64px tall
    maxWidth: 200, // Reasonable max width for header logo
  },
  signin: {
    maxHeight: 120, // More space on sign-in page
    maxWidth: 320,
  },
  footer: {
    maxHeight: 200,
    maxWidth: 400,
  },
};

export function LogoDisplaySettings() {
  const [config, setConfig] = useState<LogoDisplayConfig>(defaultConfig);
  const [isLoading, setIsLoading] = useState(false);
  const [logoOriginalSize, setLogoOriginalSize] = useState({ width: 160, height: 48 });
  const [footerOriginalSize, setFooterOriginalSize] = useState({ width: 300, height: 200 });
  
  const utils = api.useUtils();
  
  // Get current organization
  const { data: organization } = api.organizations.getCurrent.useQuery();
  
  // Load logo dimensions when logo URL changes
  React.useEffect(() => {
    if (organization?.logo_url) {
      const img = new window.Image();
      img.onload = () => {
        setLogoOriginalSize({ width: img.width, height: img.height });
      };
      img.src = organization.logo_url;
    }
    
    if ((organization as any)?.signin_footer_url) {
      const img = new window.Image();
      img.onload = () => {
        setFooterOriginalSize({ width: img.width, height: img.height });
      };
      img.src = (organization as any).signin_footer_url;
    }
  }, [organization?.logo_url, (organization as any)?.signin_footer_url]);
  
  // Calculate actual dimensions based on scale
  const calculateDimensions = (originalSize: { width: number; height: number }, scale: number) => {
    return {
      width: Math.round(originalSize.width * scale),
      height: Math.round(originalSize.height * scale),
    };
  };
  
  // Check if dimensions exceed constraints
  const checkConstraints = (dims: { width: number; height: number }, constraints: { maxWidth: number; maxHeight: number }) => {
    return {
      exceedsWidth: dims.width > constraints.maxWidth,
      exceedsHeight: dims.height > constraints.maxHeight,
      widthPercentage: Math.round((dims.width / constraints.maxWidth) * 100),
      heightPercentage: Math.round((dims.height / constraints.maxHeight) * 100),
    };
  };
  
  // Update organization mutation
  const updateTheme = api.organizations.update.useMutation({
    onSuccess: () => {
      toast.success('Logo display settings saved successfully');
      utils.organizations.getCurrent.invalidate();
    },
    onError: (error: unknown) => {
      toast.error('Failed to save logo display settings');
      safeLog.error('Update error:', { error: String(error) });
    },
  });
  
  // Load existing config from organization theme_config
  useEffect(() => {
    if ((organization as any)?.theme_config) {
      try {
        const themeConfig = (organization as any).theme_config;
        if (themeConfig.logoDisplay) {
          setConfig(themeConfig.logoDisplay);
        }
      } catch (error) {
        safeLog.error('Error parsing theme config:', { error: String(error) });
      }
    }
  }, [organization]);
  
  const handleSave = async () => {
    if (!organization) return;
    
    setIsLoading(true);
    try {
      const currentThemeConfig = (organization as any).theme_config || {};
      const updatedThemeConfig = {
        ...currentThemeConfig,
        logoDisplay: config,
      };
      
      // TODO: theme_config needs to be added to organization update schema
      // For now, use theme_colors which is supported
      await updateTheme.mutateAsync({
        theme_colors: updatedThemeConfig,
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleReset = () => {
    setConfig(defaultConfig);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Palette className="mr-2 h-5 w-5" />
          Logo Display Settings
        </CardTitle>
        <CardDescription>
          Configure how your organization logo appears in different contexts
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Header Logo Settings */}
        <div className="space-y-4">
          <h3 className="font-medium">Header Logo</h3>
          <div className="grid gap-4 pl-6">
            <div className="flex items-center justify-between">
              <Label htmlFor="header-dark-mode" className="flex-1">
                <span>Use white text on dark theme</span>
                <p className="text-xs text-muted-foreground font-normal mt-1">
                  Converts dark text to white while preserving brand colors (green, etc.)
                </p>
              </Label>
              <Switch
                id="header-dark-mode"
                checked={config.headerLogo.useDarkMode}
                onCheckedChange={(checked) =>
                  setConfig(prev => ({
                    ...prev,
                    headerLogo: { ...prev.headerLogo, useDarkMode: checked }
                  }))
                }
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Scale: {Math.round(config.headerLogo.scale * 100)}%</Label>
                <span className="text-sm text-muted-foreground">
                  {calculateDimensions(logoOriginalSize, config.headerLogo.scale).width} × {calculateDimensions(logoOriginalSize, config.headerLogo.scale).height}px
                </span>
              </div>
              <Slider
                value={[config.headerLogo.scale * 100]}
                onValueChange={(values) =>
                  setConfig(prev => ({
                    ...prev,
                    headerLogo: { ...prev.headerLogo, scale: (values[0] ?? 100) / 100 }
                  }))
                }
                min={50}
                max={150}
                step={5}
              />
              <p className="text-xs text-muted-foreground">
                Original size: {logoOriginalSize.width} × {logoOriginalSize.height}px
              </p>
            </div>
            
            {(() => {
              const dims = calculateDimensions(logoOriginalSize, config.headerLogo.scale);
              const constraints = checkConstraints(dims, CONSTRAINTS.header);
              
              return constraints.exceedsHeight ? (
                <Alert className="border-amber-200 bg-amber-50">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    Logo height ({dims.height}px) exceeds header height ({CONSTRAINTS.header.maxHeight}px). 
                    The logo will be cut off. Consider reducing the scale to {Math.floor((CONSTRAINTS.header.maxHeight / logoOriginalSize.height) * 100)}% or less.
                  </AlertDescription>
                </Alert>
              ) : constraints.exceedsWidth ? (
                <Alert className="border-amber-200 bg-amber-50">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    Logo width ({dims.width}px) is quite large for the header. 
                    Consider reducing the scale for better layout.
                  </AlertDescription>
                </Alert>
              ) : null;
            })()}
            
            <div className="p-4 border rounded-lg bg-muted/50">
              <p className="text-sm text-muted-foreground mb-2">Preview (actual size):</p>
              <div className="bg-white dark:bg-slate-900 p-2 rounded" style={{ height: CONSTRAINTS.header.maxHeight }}>
                <OrganizationLogo
                  width={calculateDimensions(logoOriginalSize, config.headerLogo.scale).width}
                  height={calculateDimensions(logoOriginalSize, config.headerLogo.scale).height}
                  darkBackground={false}
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Sign-in Page Logo Settings */}
        <div className="space-y-4">
          <h3 className="font-medium">Sign-in Page Logo</h3>
          <div className="grid gap-4 pl-6">
            <div className="flex items-center justify-between">
              <Label htmlFor="signin-dark-mode" className="flex-1">
                <span>Use white text (for dark background)</span>
                <p className="text-xs text-muted-foreground font-normal mt-1">
                  Converts dark text to white while preserving brand colors (green, etc.)
                </p>
              </Label>
              <Switch
                id="signin-dark-mode"
                checked={config.signinLogo.useDarkMode}
                onCheckedChange={(checked) =>
                  setConfig(prev => ({
                    ...prev,
                    signinLogo: { ...prev.signinLogo, useDarkMode: checked }
                  }))
                }
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Scale: {Math.round(config.signinLogo.scale * 100)}%</Label>
                <span className="text-sm text-muted-foreground">
                  {calculateDimensions(logoOriginalSize, config.signinLogo.scale).width} × {calculateDimensions(logoOriginalSize, config.signinLogo.scale).height}px
                </span>
              </div>
              <Slider
                value={[config.signinLogo.scale * 100]}
                onValueChange={(values) =>
                  setConfig(prev => ({
                    ...prev,
                    signinLogo: { ...prev.signinLogo, scale: (values[0] ?? 100) / 100 }
                  }))
                }
                min={50}
                max={200}
                step={5}
              />
              <p className="text-xs text-muted-foreground">
                Original size: {logoOriginalSize.width} × {logoOriginalSize.height}px
              </p>
            </div>
            
            {(() => {
              const dims = calculateDimensions(logoOriginalSize, config.signinLogo.scale);
              const constraints = checkConstraints(dims, CONSTRAINTS.signin);
              
              return constraints.exceedsHeight || constraints.exceedsWidth ? (
                <Alert className="border-amber-200 bg-amber-50">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    Logo is quite large ({dims.width} × {dims.height}px). 
                    This may affect the layout on smaller screens.
                  </AlertDescription>
                </Alert>
              ) : null;
            })()}
            
            <div className="p-4 border rounded-lg bg-[#1e293b]">
              <p className="text-sm text-white/70 mb-2">Preview on dark background (actual size):</p>
              <div className="flex justify-center items-center min-h-[100px]">
                <OrganizationLogo
                  width={calculateDimensions(logoOriginalSize, config.signinLogo.scale).width}
                  height={calculateDimensions(logoOriginalSize, config.signinLogo.scale).height}
                  darkBackground={config.signinLogo.useDarkMode}
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Sign-in Footer Settings */}
        <div className="space-y-4">
          <h3 className="font-medium">Sign-in Footer Image</h3>
          <div className="grid gap-4 pl-6">
            <div className="flex items-center justify-between">
              <Label htmlFor="footer-enabled" className="flex-1">
                Show footer image on sign-in page
              </Label>
              <Switch
                id="footer-enabled"
                checked={config.signinFooter.enabled}
                onCheckedChange={(checked) =>
                  setConfig(prev => ({
                    ...prev,
                    signinFooter: { ...prev.signinFooter, enabled: checked }
                  }))
                }
              />
            </div>
            
            {config.signinFooter.enabled && (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Scale: {Math.round(config.signinFooter.scale * 100)}%</Label>
                    <span className="text-sm text-muted-foreground">
                      {calculateDimensions(footerOriginalSize, config.signinFooter.scale).width} × {calculateDimensions(footerOriginalSize, config.signinFooter.scale).height}px
                    </span>
                  </div>
                  <Slider
                    value={[config.signinFooter.scale * 100]}
                    onValueChange={(values) =>
                      setConfig(prev => ({
                        ...prev,
                        signinFooter: { ...prev.signinFooter, scale: (values[0] ?? 100) / 100 }
                      }))
                    }
                    min={50}
                    max={150}
                    step={5}
                  />
                  <p className="text-xs text-muted-foreground">
                    Original size: {footerOriginalSize.width} × {footerOriginalSize.height}px
                  </p>
                </div>
                
                {(() => {
                  const dims = calculateDimensions(footerOriginalSize, config.signinFooter.scale);
                  const constraints = checkConstraints(dims, CONSTRAINTS.footer);
                  
                  return constraints.exceedsHeight || constraints.exceedsWidth ? (
                    <Alert className="border-blue-200 bg-blue-50">
                      <AlertTriangle className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800">
                        Footer image is large ({dims.width} × {dims.height}px). 
                        This is fine for desktop but may need responsive handling for mobile.
                      </AlertDescription>
                    </Alert>
                  ) : null;
                })()}
              </>
            )}
          </div>
        </div>
        
        <Separator />
        
        {/* Actions */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}