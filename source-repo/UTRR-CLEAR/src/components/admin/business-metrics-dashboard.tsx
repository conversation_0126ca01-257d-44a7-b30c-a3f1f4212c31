"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { 
  <PERSON><PERSON>hart, 
  <PERSON>, 
  <PERSON>C<PERSON>, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Area,
  AreaChart,
} from "recharts";
import {
  ArrowUp,
  ArrowDown,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Briefcase,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  Target,
} from "lucide-react";
import { api } from "~/trpc/react";

const COLORS = {
  primary: "#3b82f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
  purple: "#8b5cf6",
  indigo: "#6366f1",
};

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: "increase" | "decrease";
  icon: React.ReactNode;
  description?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeType,
  icon,
  description,
}) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {change !== undefined && (
          <div className="flex items-center pt-1">
            {changeType === "increase" ? (
              <ArrowUp className="h-4 w-4 text-green-500" />
            ) : (
              <ArrowDown className="h-4 w-4 text-red-500" />
            )}
            <span
              className={`text-xs ${
                changeType === "increase" ? "text-green-500" : "text-red-500"
              }`}
            >
              {Math.abs(change)}%
            </span>
            <span className="text-xs text-muted-foreground ml-1">from last month</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export function BusinessMetricsDashboard() {
  // Fetch real data from API
  const { data: projectStats } = api.projects.getStats.useQuery();
  const { data: userStats } = api.users.getStats.useQuery();
  const { data: revenueData } = (api.admin as any).getRevenueMetrics?.useQuery() || { data: null };
  const { data: performanceData } = (api.admin as any).getPerformanceMetrics?.useQuery() || { data: null };

  // Sample data for charts (replace with real data)
  const monthlyRevenue = [
    { month: "Jan", revenue: 125000, projects: 12 },
    { month: "Feb", revenue: 135000, projects: 15 },
    { month: "Mar", revenue: 142000, projects: 18 },
    { month: "Apr", revenue: 158000, projects: 22 },
    { month: "May", revenue: 165000, projects: 25 },
    { month: "Jun", revenue: 178000, projects: 28 },
  ];

  const projectsByStatus = [
    { name: "Active", value: 45, color: COLORS.success },
    { name: "Planning", value: 23, color: COLORS.primary },
    { name: "On Hold", value: 12, color: COLORS.warning },
    { name: "Completed", value: 67, color: COLORS.indigo },
  ];

  const utilityConflicts = [
    { month: "Jan", detected: 45, resolved: 38 },
    { month: "Feb", detected: 52, resolved: 48 },
    { month: "Mar", detected: 48, resolved: 45 },
    { month: "Apr", detected: 61, resolved: 58 },
    { month: "May", detected: 55, resolved: 53 },
    { month: "Jun", detected: 42, resolved: 41 },
  ];

  const performanceMetrics = [
    { metric: "API Response Time", value: 285, target: 500, unit: "ms" },
    { metric: "Page Load Time", value: 1.8, target: 3, unit: "s" },
    { metric: "Uptime", value: 99.95, target: 99.9, unit: "%" },
    { metric: "Error Rate", value: 0.12, target: 1, unit: "%" },
  ];

  const teamProductivity = [
    { name: "Engineering", completed: 89, inProgress: 23, planned: 15 },
    { name: "Design", completed: 45, inProgress: 12, planned: 8 },
    { name: "Coordination", completed: 67, inProgress: 18, planned: 10 },
    { name: "Admin", completed: 34, inProgress: 8, planned: 5 },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Business Metrics Dashboard</h2>
        <p className="text-muted-foreground">
          Real-time insights into CLEAR application performance and business metrics
        </p>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Revenue"
          value="$878,000"
          change={12.5}
          changeType="increase"
          icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
          description="Year to date"
        />
        <MetricCard
          title="Active Projects"
          value="45"
          change={8.2}
          changeType="increase"
          icon={<Briefcase className="h-4 w-4 text-muted-foreground" />}
          description="Currently in progress"
        />
        <MetricCard
          title="Total Users"
          value="1,234"
          change={5.3}
          changeType="increase"
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
          description="Active this month"
        />
        <MetricCard
          title="Conflict Resolution Rate"
          value="94.7%"
          change={2.1}
          changeType="increase"
          icon={<CheckCircle className="h-4 w-4 text-muted-foreground" />}
          description="Resolved within SLA"
        />
      </div>

      {/* Revenue and Projects Chart */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue & Project Trends</CardTitle>
            <CardDescription>Monthly revenue and project count</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyRevenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="revenue"
                  stroke={COLORS.primary}
                  name="Revenue ($)"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="projects"
                  stroke={COLORS.success}
                  name="Projects"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Project Distribution</CardTitle>
            <CardDescription>Projects by current status</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={projectsByStatus}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {projectsByStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Utility Conflicts Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Utility Conflict Management</CardTitle>
          <CardDescription>Monthly conflict detection and resolution</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={utilityConflicts}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area
                type="monotone"
                dataKey="detected"
                stackId="1"
                stroke={COLORS.warning}
                fill={COLORS.warning}
                fillOpacity={0.6}
                name="Detected"
              />
              <Area
                type="monotone"
                dataKey="resolved"
                stackId="2"
                stroke={COLORS.success}
                fill={COLORS.success}
                fillOpacity={0.6}
                name="Resolved"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>System Performance</CardTitle>
          <CardDescription>Key performance indicators vs targets</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {performanceMetrics.map((metric: any) => {
              const percentage = (metric.value / metric.target) * 100;
              const isGood = metric.metric === "Uptime" 
                ? percentage >= 100 
                : percentage <= 100;
              
              return (
                <div key={metric.metric} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{metric.metric}</span>
                      <Badge variant={isGood ? "success" : "destructive"}>
                        {metric.value}{metric.unit}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      Target: {metric.target}{metric.unit}
                    </span>
                  </div>
                  <Progress 
                    value={Math.min(percentage, 100)} 
                    className={isGood ? "" : "bg-red-100"}
                  />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Team Productivity */}
      <Card>
        <CardHeader>
          <CardTitle>Team Productivity</CardTitle>
          <CardDescription>Task completion by department</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={teamProductivity}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="completed" stackId="a" fill={COLORS.success} name="Completed" />
              <Bar dataKey="inProgress" stackId="a" fill={COLORS.warning} name="In Progress" />
              <Bar dataKey="planned" stackId="a" fill={COLORS.primary} name="Planned" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Average Project Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">4.2 months</div>
              <TrendingDown className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              15% faster than last quarter
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">4.8/5.0</div>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Based on 234 reviews
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Data Processing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">2.3TB</div>
              <Activity className="h-4 w-4 text-blue-500" />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Spatial data processed this month
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}