'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Loader2, Database, Users, Shield, Activity } from 'lucide-react';

export function SupabaseTableViewer() {
  const [tables, setTables] = useState<string[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableData, setTableData] = useState<any[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rlsStatus, setRlsStatus] = useState<Record<string, boolean>>({});
  
  const supabase = createClientComponentClient();

  // Get list of tables
  useEffect(() => {
    async function fetchTables() {
      try {
        // This would need a custom RPC function or API endpoint
        // For now, we'll use a predefined list
        const commonTables = [
          'organizations',
          'users', 
          'projects',
          'tasks',
          'utilities',
          'comments',
          'feature_requests',
          'timesheet_entries',
          'knowledge_articles'
        ];
        setTables(commonTables);
        if (commonTables.length > 0) {
          setSelectedTable(commonTables[0] || '');
        }
      } catch (err) {
        setError('Failed to fetch tables');
      }
    }
    fetchTables();
  }, []);

  // Fetch table data when selected table changes
  useEffect(() => {
    if (selectedTable) {
      fetchTableData();
    }
  }, [selectedTable]);

  async function fetchTableData() {
    setLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from(selectedTable)
        .select('*')
        .limit(100);

      if (error) throw error;

      if (data && data.length > 0) {
        setTableData(data);
        setColumns(Object.keys(data[0]));
      } else {
        setTableData([]);
        setColumns([]);
      }
    } catch (err: any) {
      setError(err.message);
      setTableData([]);
      setColumns([]);
    }
    setLoading(false);
  }

  const stats = [
    { label: 'Total Tables', value: tables.length, icon: Database },
    { label: 'RLS Enabled', value: Object.values(rlsStatus).filter(Boolean).length, icon: Shield },
    { label: 'Total Records', value: tableData.length, icon: Activity },
  ];

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {stats.map((stat) => (
          <Card key={stat.label}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.label}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Table Viewer */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Database Tables</CardTitle>
              <CardDescription>
                View and manage your Supabase database tables
              </CardDescription>
            </div>
            <Select value={selectedTable} onValueChange={setSelectedTable}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select a table" />
              </SelectTrigger>
              <SelectContent>
                {tables.map((table) => (
                  <SelectItem key={table} value={table}>
                    {table}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-destructive">{error}</p>
              <Button onClick={fetchTableData} className="mt-4">
                Retry
              </Button>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    {columns.map((column) => (
                      <TableHead key={column}>{column}</TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableData.map((row, idx) => (
                    <TableRow key={idx}>
                      {columns.map((column) => (
                        <TableCell key={column}>
                          {typeof row[column] === 'object' 
                            ? JSON.stringify(row[column])
                            : String(row[column] ?? '')}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {tableData.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No data available in this table
                </div>
              )}
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage your database directly from the admin panel
          </CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <Button variant="outline" onClick={() => window.open('https://supabase.com/dashboard', '_blank')}>
            Open Supabase Dashboard
          </Button>
          <Button variant="outline" onClick={() => window.open(`https://supabase.com/dashboard/project/${process.env.NEXT_PUBLIC_SUPABASE_URL?.split('.')[0]}/editor`, '_blank')}>
            SQL Editor
          </Button>
          <Button variant="outline" onClick={() => window.open(`https://supabase.com/dashboard/project/${process.env.NEXT_PUBLIC_SUPABASE_URL?.split('.')[0]}/auth/users`, '_blank')}>
            User Management
          </Button>
          <Button variant="outline" onClick={() => window.open(`https://supabase.com/dashboard/project/${process.env.NEXT_PUBLIC_SUPABASE_URL?.split('.')[0]}/database/tables`, '_blank')}>
            Table Editor
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}