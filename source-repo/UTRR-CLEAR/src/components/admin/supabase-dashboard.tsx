'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { ExternalLink, Database, Users, Activity, Settings, BarChart3 } from 'lucide-react';

interface SupabaseDashboardProps {
  className?: string;
}

/**
 * Supabase Admin Dashboard Integration
 * Provides access to Supabase management tools within the CLEAR admin interface
 */
export function SupabaseDashboard({ className = '' }: SupabaseDashboardProps) {
  const [supabaseStatus, setSupabaseStatus] = useState<'loading' | 'connected' | 'disconnected'>('loading');
  const [supabaseStats, setSupabaseStats] = useState({
    databaseConnections: 0,
    activeUsers: 0,
    realtimeConnections: 0,
    apiRequests: 0,
    storageUsage: '0 MB',
  });

  // Supabase local URLs (these would be configurable)
  const supabaseUrls = {
    studio: process.env.NEXT_PUBLIC_SUPABASE_STUDIO_URL || 'http://localhost:54323',
    api: process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321',
    auth: `${process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321'}/auth/v1`,
    storage: `${process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321'}/storage/v1`,
    realtime: process.env.NEXT_PUBLIC_SUPABASE_URL?.replace('http', 'ws') || 'ws://localhost:54321',
  };

  useEffect(() => {
    // Check Supabase connection status
    checkSupabaseStatus();
    loadSupabaseStats();
  }, []);

  const checkSupabaseStatus = async () => {
    try {
      const response = await fetch('/api/supabase-status');
      if (response.ok) {
        setSupabaseStatus('connected');
      } else {
        setSupabaseStatus('disconnected');
      }
    } catch (error) {
      setSupabaseStatus('disconnected');
    }
  };

  const loadSupabaseStats = async () => {
    try {
      // In a real implementation, these would come from Supabase API
      // For now, we'll use mock data
      setSupabaseStats({
        databaseConnections: 12,
        activeUsers: 8,
        realtimeConnections: 5,
        apiRequests: 1247,
        storageUsage: '125 MB',
      });
    } catch (error) {
      console.error('Error loading Supabase stats:', error);
    }
  };

  const openSupabaseStudio = () => {
    window.open(supabaseUrls.studio, '_blank');
  };

  const getStatusColor = () => {
    switch (supabaseStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'disconnected':
        return 'bg-red-500';
      default:
        return 'bg-yellow-500';
    }
  };

  const getStatusText = () => {
    switch (supabaseStatus) {
      case 'connected':
        return 'Connected';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Checking...';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Database className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Supabase Dashboard</h2>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
            <span className="text-sm text-muted-foreground">{getStatusText()}</span>
          </div>
        </div>
        <Button onClick={openSupabaseStudio} className="gap-2">
          <ExternalLink className="h-4 w-4" />
          Open Supabase Studio
        </Button>
      </div>

      {/* Status Alert */}
      {supabaseStatus === 'disconnected' && (
        <Alert variant="destructive">
          <AlertDescription>
            Unable to connect to Supabase. Please ensure the local Supabase instance is running.
          </AlertDescription>
        </Alert>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">DB Connections</p>
                <p className="text-2xl font-bold">{supabaseStats.databaseConnections}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold">{supabaseStats.activeUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">Realtime</p>
                <p className="text-2xl font-bold">{supabaseStats.realtimeConnections}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-sm text-muted-foreground">API Requests</p>
                <p className="text-2xl font-bold">{supabaseStats.apiRequests}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-muted-foreground">Storage</p>
                <p className="text-2xl font-bold">{supabaseStats.storageUsage}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="auth">Authentication</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Service URLs</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">API URL:</span>
                  <Badge variant="outline">{supabaseUrls.api}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Studio URL:</span>
                  <Badge variant="outline">{supabaseUrls.studio}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Auth URL:</span>
                  <Badge variant="outline">{supabaseUrls.auth}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Storage URL:</span>
                  <Badge variant="outline">{supabaseUrls.storage}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  onClick={openSupabaseStudio}
                  className="w-full justify-start gap-2"
                  variant="outline"
                >
                  <Database className="h-4 w-4" />
                  Open Database Studio
                </Button>
                <Button 
                  onClick={() => window.open(`${supabaseUrls.studio}/project/default/auth/users`, '_blank')}
                  className="w-full justify-start gap-2"
                  variant="outline"
                >
                  <Users className="h-4 w-4" />
                  Manage Users
                </Button>
                <Button 
                  onClick={() => window.open(`${supabaseUrls.studio}/project/default/storage/buckets`, '_blank')}
                  className="w-full justify-start gap-2"
                  variant="outline"
                >
                  <Settings className="h-4 w-4" />
                  Storage Buckets
                </Button>
                <Button 
                  onClick={checkSupabaseStatus}
                  className="w-full justify-start gap-2"
                  variant="outline"
                >
                  <Activity className="h-4 w-4" />
                  Check Connection
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="database">
          <Card>
            <CardHeader>
              <CardTitle>Database Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Access the full Supabase Studio for database management, table editing, and SQL queries.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/editor`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <Database className="h-4 w-4" />
                    Table Editor
                  </Button>
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/sql`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <BarChart3 className="h-4 w-4" />
                    SQL Editor
                  </Button>
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/database/schemas`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <Settings className="h-4 w-4" />
                    Schema Manager
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="auth">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Manage users, authentication providers, and security policies.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/auth/users`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <Users className="h-4 w-4" />
                    User Management
                  </Button>
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/auth/policies`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <Settings className="h-4 w-4" />
                    RLS Policies
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="storage">
          <Card>
            <CardHeader>
              <CardTitle>Storage Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Manage file storage, buckets, and storage policies.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/storage/buckets`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <Database className="h-4 w-4" />
                    Storage Buckets
                  </Button>
                  <Button 
                    onClick={() => window.open(`${supabaseUrls.studio}/project/default/storage/policies`, '_blank')}
                    className="gap-2"
                    variant="outline"
                  >
                    <Settings className="h-4 w-4" />
                    Storage Policies
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime">
          <Card>
            <CardHeader>
              <CardTitle>Real-time Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Monitor real-time connections and configure real-time features.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium">Active Connections</h4>
                    <p className="text-2xl font-bold text-green-600">{supabaseStats.realtimeConnections}</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium">WebSocket Status</h4>
                    <Badge variant={supabaseStatus === 'connected' ? 'default' : 'destructive'}>
                      {supabaseStatus === 'connected' ? 'Connected' : 'Disconnected'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Supabase Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Environment Variables</h4>
                    <div className="space-y-1 text-sm">
                      <div>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}</div>
                      <div>NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}</div>
                      <div>SUPABASE_SERVICE_ROLE_KEY: {process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set'}</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Quick Links</h4>
                    <div className="space-y-1">
                      <Button 
                        onClick={() => window.open(`${supabaseUrls.studio}/project/default/settings/general`, '_blank')}
                        size="sm"
                        variant="outline"
                        className="w-full justify-start"
                      >
                        Project Settings
                      </Button>
                      <Button 
                        onClick={() => window.open(`${supabaseUrls.studio}/project/default/settings/api`, '_blank')}
                        size="sm"
                        variant="outline"
                        className="w-full justify-start"
                      >
                        API Settings
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}