'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import { Card, CardContent } from '~/components/ui/card';
import { Label } from '~/components/ui/label';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '~/components/ui/dialog';
import { useToast } from '~/hooks/use-toast';
import { Loader2, Download, Upload, Save, Search, X, Check, Plus } from 'lucide-react';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Checkbox } from '~/components/ui/checkbox';
import { Badge } from '~/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover';
import * as ExcelJS from 'exceljs';
import { api } from '~/trpc/react';
import { safeLog } from '~/lib/error-handler';

interface ExcelFeeCalculatorProps {
  onClose: () => void;
  templateUrl?: string;
}

// Meeting types as they appear in the Excel template
interface MeetingCount {
  constructabilityReviewStage1: number;
  preliminaryFieldCheck: number;
  utilityCoordinationMeetings: number;
  constructabilityReviewStage2: number;
  finalFieldCheck: number;
  constructabilityReviewFinalPlans: number;
  preconstructionConference: number;
  meetingsDuringConstruction: number;
  constructabilityReviewMidConstr: number;
  constructabilityReviewPostConstr: number;
  unassigned: number;
}

// Company trip types
type CompanyTrip = 'Assumed' | 'Floating' | 'none';

// Define stakeholder interface
interface Stakeholder {
  id: number;
  fullName: string;
  companyName: string;
  stakeholderType?: string;
  email?: string;
}

// Utility Company interface
interface UtilityCompany {
  id: number;
  name: string;
  companyName: string;
  selected: boolean;
  supplement?: boolean;
}

export function ExcelFeeCalculator({ onClose, templateUrl }: ExcelFeeCalculatorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [fileName, setFileName] = useState<string>('');
  const { toast } = useToast();

  // Form state
  const [projectDescription, setProjectDescription] = useState('');
  const [clientProjectNumber, setClientProjectNumber] = useState('');
  const [egisNumber, setEgisNumber] = useState('');

  // Utilities state with stakeholder selection
  const [utilityCompanies, setUtilityCompanies] = useState<UtilityCompany[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [openCombobox, setOpenCombobox] = useState(false);
  const [utilitiesOriginal, setUtilitiesOriginal] = useState<number>(0);
  const [utilitiesSupplement1, setUtilitiesSupplement1] = useState<number>(0);

  // Meeting counts
  const [meetings, setMeetings] = useState<MeetingCount>({
    constructabilityReviewStage1: 0,
    preliminaryFieldCheck: 0,
    utilityCoordinationMeetings: 0,
    constructabilityReviewStage2: 0,
    finalFieldCheck: 0,
    constructabilityReviewFinalPlans: 0,
    preconstructionConference: 0,
    meetingsDuringConstruction: 0,
    constructabilityReviewMidConstr: 0,
    constructabilityReviewPostConstr: 0,
    unassigned: 0,
  });

  // Company trip counts
  const [companyTrips, setCompanyTrips] = useState<Record<string, CompanyTrip>>({
    constructabilityReviewStage1: 'none',
    preliminaryFieldCheck: 'none',
    utilityCoordinationMeetings: 'Assumed',
    constructabilityReviewStage2: 'none',
    finalFieldCheck: 'none',
    constructabilityReviewFinalPlans: 'none',
    preconstructionConference: 'none',
    meetingsDuringConstruction: 'Assumed',
    constructabilityReviewMidConstr: 'none',
    constructabilityReviewPostConstr: 'none',
    unassigned: 'Floating',
  });

  const [milesOneWay, setMilesOneWay] = useState<number>(0);
  const [hoursOneWay, setHoursOneWay] = useState<number>(0);
  const [reimbursableUtilities, setReimbursableUtilities] = useState<string>('SELECT ONE');
  const [relocations, setRelocations] = useState<string>('SELECT ONE');
  const [client, setClient] = useState('');
  const [workType, setWorkType] = useState('');
  const [scopeType, setScopeType] = useState('');
  const [activeTab, setActiveTab] = useState('project-input');

  // Utility coordination counts
  const [standardUtilityCoordination, setStandardUtilityCoordination] = useState<number>(0);
  const [standardUCSupplement1, setStandardUCSupplement1] = useState<number>(0);
  const [standardUCSupplement2, setStandardUCSupplement2] = useState<number>(0);
  const [standardUCSupplement3, setStandardUCSupplement3] = useState<number>(0);
  const [standardUCSupplement4, setStandardUCSupplement4] = useState<number>(0);
  const [partialUtilityCoordination, setPartialUtilityCoordination] = useState<number>(0);

  // Fetch stakeholders via tRPC
  const { data: stakeholdersData, isLoading: isLoadingStakeholders } =
    (api.admin as any).getStakeholders?.useQuery() || { data: null, isLoading: false };

  // Update meeting count
  const updateMeetingCount = (field: keyof MeetingCount, value: number) => {
    setMeetings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Update company trip type
  const updateCompanyTrip = (field: string, value: CompanyTrip) => {
    setCompanyTrips((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // The CompanyTripSelect component
  const CompanyTripSelect = ({ fieldName }: { fieldName: string }) => (
    <Select
      value={companyTrips[fieldName] || 'none'}
      onValueChange={(value) => updateCompanyTrip(fieldName, value as CompanyTrip)}
    >
      <SelectTrigger className="bg-white dark:bg-gray-800 h-9">
        <SelectValue placeholder="-" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="none">-</SelectItem>
        <SelectItem value="Assumed">Assumed</SelectItem>
        <SelectItem value="Floating">Floating</SelectItem>
      </SelectContent>
    </Select>
  );

  // Filter stakeholders to only utility companies
  const utilityStakeholders = React.useMemo(() => {
    if (!stakeholdersData) return [];

    return stakeholdersData.filter(
      (stakeholder: any) =>
        stakeholder.stakeholderType === 'Utility Company' ||
        (stakeholder.stakeholderType &&
          stakeholder.stakeholderType.toLowerCase().includes('utility'))
    );
  }, [stakeholdersData]);

  // Process stakeholders into utility companies for selection
  useEffect(() => {
    if (utilityStakeholders && utilityStakeholders.length > 0) {
      const companies = utilityStakeholders.map((stakeholder: any) => ({
        id: stakeholder.id,
        name: stakeholder.full_name,
        companyName: stakeholder.contact_company,
        selected: false,
        supplement: false,
      }));

      setUtilityCompanies(companies);
    }
  }, [utilityStakeholders]);

  // Handle utility company selection
  const toggleUtilitySelection = (id: number, supplementMode = false) => {
    setUtilityCompanies((prev) =>
      prev.map((utility: any) => {
        if (utility.id === id) {
          return {
            ...utility,
            selected: !utility.selected,
            supplement: supplementMode ? !utility.supplement : utility.supplement,
          };
        }
        return utility;
      })
    );
  };

  // Calculate total utilities selected based on supplement status
  useEffect(() => {
    const originalCount = utilityCompanies.filter((u: any) => u.selected && !u.supplement).length;
    setUtilitiesOriginal(originalCount);

    const supplementCount = utilityCompanies.filter((u: any) => u.selected && u.supplement).length;
    setUtilitiesSupplement1(supplementCount);
  }, [utilityCompanies]);

  // Handle file upload from user
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setFileName(file.name);

    const reader = new FileReader();
    reader.onload = async (e) => {
      if (e.target?.result) {
        try {
          const workbook = new ExcelJS.Workbook();
          await workbook.xlsx.load(e.target.result as ArrayBuffer);
          
          const firstWorksheet = workbook.worksheets[0];
          if (!firstWorksheet) throw new Error('No sheets found in workbook');

          toast({
            title: 'File loaded',
            description: 'The Excel template has been loaded successfully.',
          });
        } catch (error) {
          safeLog.error('Error processing Excel file:', { error: String(error) });
          toast({
            title: 'Error processing file',
            description: 'Could not extract data from the file.',
            variant: 'destructive',
          });
        }
        setIsLoading(false);
      }
    };
    reader.onerror = () => {
      toast({
        title: 'Error reading file',
        description: 'Could not read the uploaded file.',
        variant: 'destructive',
      });
      setIsLoading(false);
    };
    reader.readAsArrayBuffer(file);
  };

  // Handle exporting the data as an Excel file
  const handleExport = async () => {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Project Input');

      // Add all the data rows
      worksheet.addRow(['', '', 'Project Description', 'Client Project #', 'EGIS #', '', '', '', 'Worktype']);
      worksheet.addRow(['', '', projectDescription, clientProjectNumber, egisNumber, '', '', '', workType]);
      worksheet.addRow(['', '', '', '', '', '', '', '', '']);
      worksheet.addRow([
        '',
        '',
        'NUMBER OF UTILITIES PER SCOPE:',
        '',
        'Utilities on Project (Original)',
        'Utilities on Project (Supplement #1)',
        '',
        'Anticipated travel trips & Count',
        'Company Trip Count',
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        utilitiesOriginal,
        utilitiesSupplement1,
        '',
        'Constructability Review - Stage 1',
        meetings.constructabilityReviewStage1,
        companyTrips.constructabilityReviewStage1,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Preliminary Field Check',
        meetings.preliminaryFieldCheck,
        companyTrips.preliminaryFieldCheck,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Utility Coordination Meetings',
        meetings.utilityCoordinationMeetings,
        companyTrips.utilityCoordinationMeetings,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Constructability Review - Stage 2',
        meetings.constructabilityReviewStage2,
        companyTrips.constructabilityReviewStage2,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Final Field Check',
        meetings.finalFieldCheck,
        companyTrips.finalFieldCheck,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Constructability Review - Final Plans',
        meetings.constructabilityReviewFinalPlans,
        companyTrips.constructabilityReviewFinalPlans,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Preconstruction Conference',
        meetings.preconstructionConference,
        companyTrips.preconstructionConference,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Meetings During Construction',
        meetings.meetingsDuringConstruction,
        companyTrips.meetingsDuringConstruction,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Constructability Review - Mid-const.',
        meetings.constructabilityReviewMidConstr,
        companyTrips.constructabilityReviewMidConstr,
      ]);
      worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Constructability Review - Post-const.',
        meetings.constructabilityReviewPostConstr,
        companyTrips.constructabilityReviewPostConstr,
      ]);
      worksheet.addRow(['', '', '', '', '', '', '', 'Unassigned', meetings.unassigned, companyTrips.unassigned]);
      worksheet.addRow(['', '', '', '', '', '', '', '', '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', 'Travel to Project', '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', 'Miles One-Way', milesOneWay, '']);
      worksheet.addRow(['', '', '', '', '', '', '', 'Hours One-Way', hoursOneWay, '']);
      worksheet.addRow(['', '', '', '', '', '', '', '', '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', 'REIMBURSABLE UTILITIES', '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', reimbursableUtilities, '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', '', '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', 'RELOCATIONS', '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', relocations, '', '']);
      worksheet.addRow(['', '', '', '', '', '', '', '', '', '']);
      worksheet.addRow(['', 'TOTALS:', '', '', '', '', '', '', '', '']);
      worksheet.addRow([
        'UTILITY COORDINATION - Standard Utility Coordination',
        standardUtilityCoordination,
        '', '', '', '', '', '', '', '',
      ]);
      worksheet.addRow([
        'UTILITY COORDINATION - Standard UC Supplement #1',
        standardUCSupplement1,
        '', '', '', '', '', '', '', '',
      ]);
      worksheet.addRow([
        'UTILITY COORDINATION - Standard UC Supplement #2',
        standardUCSupplement2,
        '', '', '', '', '', '', '', '',
      ]);
      worksheet.addRow([
        'UTILITY COORDINATION - Standard UC Supplement #3',
        standardUCSupplement3,
        '', '', '', '', '', '', '', '',
      ]);
      worksheet.addRow([
        'UTILITY COORDINATION - Standard UC Supplement #4',
        standardUCSupplement4,
        '', '', '', '', '', '', '', '',
      ]);
      worksheet.addRow([
        'UTILITY COORDINATION - Partial Utility Coordination (IAC/IOM)',
        partialUtilityCoordination,
        '', '', '', '', '', '', '', '',
      ]);

      // Generate the Excel file and download it
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || 'fee-calculation.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: 'Export successful',
        description: 'The Excel file has been exported successfully.',
      });
    } catch (error) {
      safeLog.error('Error exporting file:', { error: String(error) });
      toast({
        title: 'Export failed',
        description: 'Could not export the Excel file.',
        variant: 'destructive',
      });
    }
  };

  // Handle saving the data to database
  const handleSave = () => {
    const formData = {
      projectDescription,
      clientProjectNumber,
      egisNumber,
      client,
      workType,
      scopeType,
      utilitiesOriginal,
      utilitiesSupplement1,
      meetings,
      companyTrips,
      milesOneWay,
      hoursOneWay,
      reimbursableUtilities,
      relocations,
      standardUtilityCoordination,
      standardUCSupplement1,
      standardUCSupplement2,
      standardUCSupplement3,
      standardUCSupplement4,
      partialUtilityCoordination,
    };

    safeLog.info('Form data to save:', formData);

    toast({
      title: 'Save feature',
      description: 'Data saved to form state. Database save will be implemented soon.',
    });
  };

  const calculateTotalMeetings = (): number => {
    return Object.values(meetings).reduce((sum: any, count: any) => sum + count, 0);
  };

  return (
    <div className="w-full h-full flex flex-col">
      <DialogHeader>
        <DialogTitle>Man-Hour Justification Calculator</DialogTitle>
        <DialogDescription>
          Complete the form to calculate utility coordination fees based on the MHJ template.
        </DialogDescription>
      </DialogHeader>

      <div className="flex justify-between items-center mb-4 mt-2">
        <div className="flex items-center">
          <Label htmlFor="file-upload" className="cursor-pointer mr-2">
            <Button variant="outline" size="sm" className="mr-2" asChild>
              <div>
                <Upload className="h-4 w-4 mr-1" />
                Upload Template
              </div>
            </Button>
          </Label>
          <input
            id="file-upload"
            type="file"
            accept=".xlsx,.xls"
            onChange={handleFileUpload}
            className="hidden"
          />

          {fileName && <span className="text-sm text-muted-foreground">{fileName}</span>}
        </div>

        <div>
          <Button variant="outline" size="sm" onClick={handleExport} className="mr-2">
            <Download className="h-4 w-4 mr-1" />
            Export Excel
          </Button>

          <Button size="sm" onClick={handleSave}>
            <Save className="h-4 w-4 mr-1" />
            Save Data
          </Button>
        </div>
      </div>

      <div className="flex-1 min-h-[600px]">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading template data...</span>
          </div>
        ) : (
          <Tabs
            defaultValue="project-input"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full h-full"
          >
            <TabsList className="w-full max-w-md mx-auto mb-6">
              <TabsTrigger value="project-input" className="flex-1">
                Project Input
              </TabsTrigger>
              <TabsTrigger value="calculations" className="flex-1">
                Calculations
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex-1">
                Summary
              </TabsTrigger>
            </TabsList>

            <TabsContent value="project-input" className="space-y-6 h-full">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 bg-muted/20 rounded-lg h-full">
                {/* Project Info */}
                <div className="col-span-12 grid grid-cols-12 gap-4 pb-4 border-b">
                  <div className="col-span-12 md:col-span-4">
                    <Label htmlFor="project-description">Project Description</Label>
                    <Input
                      id="project-description"
                      value={projectDescription}
                      onChange={(e: any) => setProjectDescription(e.target.value)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>
                  <div className="col-span-6 md:col-span-2">
                    <Label htmlFor="client-project-number">Client Project #</Label>
                    <Input
                      id="client-project-number"
                      value={clientProjectNumber}
                      onChange={(e: any) => setClientProjectNumber(e.target.value)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>
                  <div className="col-span-6 md:col-span-2">
                    <Label htmlFor="egis-number">EGIS #</Label>
                    <Input
                      id="egis-number"
                      value={egisNumber}
                      onChange={(e: any) => setEgisNumber(e.target.value)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>
                  <div className="col-span-12 md:col-span-4">
                    <Label htmlFor="client">Client</Label>
                    <Select value={client} onValueChange={setClient}>
                      <SelectTrigger className="bg-white dark:bg-gray-800">
                        <SelectValue placeholder="SELECT FROM LIST" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="INDOT">INDOT</SelectItem>
                        <SelectItem value="Marion County">Marion County</SelectItem>
                        <SelectItem value="Hamilton County">Hamilton County</SelectItem>
                        <SelectItem value="Tippecanoe County">Tippecanoe County</SelectItem>
                        <SelectItem value="Town of Fishers">Town of Fishers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Work Type */}
                <div className="col-span-12 grid grid-cols-12 gap-4 py-4 border-b">
                  <div className="col-span-12 md:col-span-4">
                    <Label htmlFor="work-type">Worktype</Label>
                    <Select value={workType} onValueChange={setWorkType}>
                      <SelectTrigger className="bg-white dark:bg-gray-800">
                        <SelectValue placeholder="SELECT FROM LIST" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Bridge Replacement">Bridge Replacement</SelectItem>
                        <SelectItem value="Road Reconstruction">Road Reconstruction</SelectItem>
                        <SelectItem value="Intersection Improvement">
                          Intersection Improvement
                        </SelectItem>
                        <SelectItem value="Trail">Trail</SelectItem>
                        <SelectItem value="Signal Modernization">Signal Modernization</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-12 md:col-span-8">
                    <Label htmlFor="scope-type">Scope Type</Label>
                    <Select value={scopeType} onValueChange={setScopeType}>
                      <SelectTrigger className="bg-white dark:bg-gray-800">
                        <SelectValue placeholder="Select Scope Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Standard Utility Coordination">
                          Standard Utility Coordination
                        </SelectItem>
                        <SelectItem value="Standard UC Supplement #1">
                          Standard UC Supplement #1
                        </SelectItem>
                        <SelectItem value="Standard UC Supplement #2">
                          Standard UC Supplement #2
                        </SelectItem>
                        <SelectItem value="Standard UC Supplement #3">
                          Standard UC Supplement #3
                        </SelectItem>
                        <SelectItem value="Standard UC Supplement #4">
                          Standard UC Supplement #4
                        </SelectItem>
                        <SelectItem value="Partial Utility Coordination">
                          Partial Utility Coordination (IAC/IOM)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Utilities Per Scope section */}
                <div className="col-span-12 grid grid-cols-12 gap-4 py-4 border-b">
                  <div className="col-span-12 mb-2">
                    <h3 className="text-base font-medium bg-purple-100 dark:bg-purple-900 p-2 rounded">
                      NUMBER OF UTILITIES PER SCOPE
                    </h3>
                  </div>

                  <div className="col-span-4">
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-4 rounded-lg h-full flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <p className="text-xs text-muted-foreground">
                          Select utility companies from the list below
                        </p>

                        <div className="flex items-center space-x-2 text-sm">
                          <Badge variant="outline" className="px-2 py-0">
                            Original: {utilitiesOriginal}
                          </Badge>
                          <Badge variant="outline" className="px-2 py-0">
                            Supplement: {utilitiesSupplement1}
                          </Badge>
                        </div>
                      </div>

                      {/* Utility Company Search */}
                      <Popover open={openCombobox} onOpenChange={setOpenCombobox}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openCombobox}
                            className="w-full justify-between bg-white dark:bg-gray-800 mb-3"
                          >
                            <div className="flex items-center gap-2">
                              <Search className="h-4 w-4 text-muted-foreground" />
                              <span>{searchQuery || 'Search utility companies...'}</span>
                            </div>
                            <Plus className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-[--radix-popover-trigger-width] p-0"
                          align="start"
                        >
                          <Command className="w-full">
                            <CommandInput
                              placeholder="Search utility companies..."
                              value={searchQuery}
                              onValueChange={setSearchQuery}
                              className="h-9"
                            />
                            <CommandList>
                              <CommandEmpty>No utility companies found.</CommandEmpty>
                              <CommandGroup className="max-h-[250px] overflow-auto">
                                {utilityStakeholders
                                  .filter(
                                    (stakeholder: any) =>
                                      stakeholder.fullName
                                        ?.toLowerCase()
                                        .includes(searchQuery.toLowerCase()) ||
                                      stakeholder.companyName
                                        ?.toLowerCase()
                                        .includes(searchQuery.toLowerCase()) ||
                                      stakeholder.name
                                        ?.toLowerCase()
                                        .includes(searchQuery.toLowerCase()) ||
                                      stakeholder.company
                                        ?.toLowerCase()
                                        .includes(searchQuery.toLowerCase())
                                  )
                                  .map((stakeholder: any) => {
                                    const utility = utilityCompanies.find(
                                      (u) => u.id === stakeholder.id
                                    );
                                    const isSelected = utility?.selected || false;

                                    return (
                                      <CommandItem
                                        key={stakeholder.id}
                                        value={stakeholder.id.toString()}
                                        onSelect={() => {
                                          toggleUtilitySelection(stakeholder.id);
                                        }}
                                        className="flex items-center justify-between py-2"
                                      >
                                        <div className="flex flex-col">
                                          <span className="font-medium">
                                            {stakeholder.full_name}
                                          </span>
                                          <span className="text-xs text-muted-foreground">
                                            {stakeholder.contact_company}
                                          </span>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                          <Checkbox
                                            checked={isSelected}
                                            onClick={(e: any) => {
                                              e.stopPropagation();
                                              toggleUtilitySelection(stakeholder.id);
                                            }}
                                          />
                                        </div>
                                      </CommandItem>
                                    );
                                  })}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>

                      {/* Selected Utilities List */}
                      <div className="flex-1 overflow-hidden flex flex-col">
                        <div className="flex justify-between items-center mb-2">
                          <Label className="font-medium">Selected Utilities</Label>
                          <Badge>
                            {utilityCompanies.filter((u: any) => u.selected).length} selected
                          </Badge>
                        </div>

                        <ScrollArea className="flex-1 border rounded bg-white dark:bg-gray-800 p-2">
                          {utilityCompanies.filter((u: any) => u.selected).length === 0 ? (
                            <div className="flex items-center justify-center h-full text-muted-foreground py-4">
                              No utilities selected
                            </div>
                          ) : (
                            <div className="space-y-2">
                              {utilityCompanies
                                .filter((utility: any) => utility.selected)
                                .map((utility: any) => (
                                  <div
                                    key={utility.id}
                                    className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
                                  >
                                    <div className="flex-1">
                                      <div className="font-medium">{utility.name}</div>
                                      <div className="text-xs text-muted-foreground">
                                        {utility.companyName}
                                      </div>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                      <div className="flex items-center space-x-1">
                                        <Checkbox
                                          id={`supplement-${utility.id}`}
                                          checked={utility.supplement}
                                          onCheckedChange={() =>
                                            toggleUtilitySelection(utility.id, true)
                                          }
                                        />
                                        <Label
                                          htmlFor={`supplement-${utility.id}`}
                                          className="text-xs cursor-pointer"
                                        >
                                          Supplement
                                        </Label>
                                      </div>

                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => toggleUtilitySelection(utility.id)}
                                        className="h-6 w-6"
                                      >
                                        <X className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          )}
                        </ScrollArea>
                      </div>
                    </div>
                  </div>

                  <div className="col-span-8">
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg h-full">
                      <div className="mb-2 grid grid-cols-12 gap-2">
                        <div className="col-span-8 font-medium">
                          Anticipated travel trips & Count
                        </div>
                        <div className="col-span-4 font-medium text-center">Company Trip Count</div>
                      </div>

                      {/* Meeting options - hardcoded for now, can be made dynamic later */}
                      <div>
                        {[
                          {
                            name: 'Constructability Review - Stage 1',
                            value: 'constructabilityReviewStage1',
                          },
                          { name: 'Preliminary Field Check', value: 'preliminaryFieldCheck' },
                          {
                            name: 'Utility Coordination Meetings',
                            value: 'utilityCoordinationMeetings',
                          },
                          {
                            name: 'Constructability Review - Stage 2',
                            value: 'constructabilityReviewStage2',
                          },
                          { name: 'Final Field Check', value: 'finalFieldCheck' },
                          {
                            name: 'Constructability Review - Final Plans',
                            value: 'constructabilityReviewFinalPlans',
                          },
                          {
                            name: 'Preconstruction Conference',
                            value: 'preconstructionConference',
                          },
                          {
                            name: 'Meetings During Construction',
                            value: 'meetingsDuringConstruction',
                          },
                          {
                            name: 'Constructability Review - Mid-const.',
                            value: 'constructabilityReviewMidConstr',
                          },
                          {
                            name: 'Constructability Review - Post-const.',
                            value: 'constructabilityReviewPostConstr',
                          },
                          { name: 'Unassigned', value: 'unassigned' },
                        ].map((option: any) => (
                          <div key={option.value} className="grid grid-cols-12 gap-2 mb-2">
                            <Label htmlFor={`meeting-${option.value}`} className="col-span-8">
                              {option.name}
                            </Label>
                            <div className="col-span-2">
                              <Input
                                id={`meeting-${option.value}`}
                                type="number"
                                min={0}
                                value={meetings[option.value as keyof MeetingCount] || 0}
                                onChange={(e: any) =>
                                  updateMeetingCount(
                                    option.value as keyof MeetingCount,
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                className="bg-white dark:bg-gray-800 text-center"
                              />
                            </div>
                            <div className="col-span-2 text-center">
                              <CompanyTripSelect fieldName={option.value} />
                            </div>
                          </div>
                        ))}

                        <div className="grid grid-cols-12 gap-2 mt-4 pt-2 border-t border-gray-200 dark:border-gray-700">
                          <Label className="col-span-8 font-medium">Total</Label>
                          <div className="col-span-2 flex items-center justify-center font-medium">
                            {calculateTotalMeetings()}
                          </div>
                          <div className="col-span-2">{/* Placeholder for total */}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Travel section */}
                <div className="col-span-12 grid grid-cols-12 gap-4 py-4 border-b">
                  <div className="col-span-12 mb-2">
                    <h3 className="text-base font-medium bg-purple-100 dark:bg-purple-900 p-2 rounded">
                      Travel to Project
                    </h3>
                  </div>

                  <div className="col-span-3">
                    <Label htmlFor="miles-one-way">Miles One-Way</Label>
                    <Input
                      id="miles-one-way"
                      type="number"
                      min={0}
                      value={milesOneWay}
                      onChange={(e: any) => setMilesOneWay(parseInt(e.target.value) || 0)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>

                  <div className="col-span-3">
                    <Label htmlFor="hours-one-way">Hours One-Way</Label>
                    <Input
                      id="hours-one-way"
                      type="number"
                      min={0}
                      step={0.1}
                      value={hoursOneWay}
                      onChange={(e: any) => setHoursOneWay(parseFloat(e.target.value) || 0)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>

                  <div className="col-span-6">
                    <Label>Travel Both Ways</Label>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded">
                        <div className="text-xs text-center mb-1">Miles</div>
                        <div className="text-center">{milesOneWay * 2}</div>
                      </div>
                      <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded">
                        <div className="text-xs text-center mb-1">Hours</div>
                        <div className="text-center">{(hoursOneWay * 2).toFixed(1)}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Reimbursable & Relocations section */}
                <div className="col-span-12 grid grid-cols-12 gap-4 py-4 border-b">
                  <div className="col-span-6">
                    <div className="bg-purple-100 dark:bg-purple-900/30 p-2 mb-2 rounded">
                      <h3 className="text-base font-medium text-center">REIMBURSABLE UTILITIES</h3>
                    </div>
                    <Select value={reimbursableUtilities} onValueChange={setReimbursableUtilities}>
                      <SelectTrigger className="bg-white dark:bg-gray-800">
                        <SelectValue placeholder="SELECT ONE" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SELECT ONE">SELECT ONE</SelectItem>
                        <SelectItem value="ASSUMED REIMBURSABLE UTILITIES">
                          ASSUMED REIMBURSABLE UTILITIES
                        </SelectItem>
                        <SelectItem value="ACTUAL REIMBURSABLE UTILITIES">
                          ACTUAL REIMBURSABLE UTILITIES
                        </SelectItem>
                        <SelectItem value="SUPPLEMENT IF NEEDED">SUPPLEMENT IF NEEDED</SelectItem>
                        <SelectItem value="N/A">N/A</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="col-span-6">
                    <div className="bg-purple-100 dark:bg-purple-900/30 p-2 mb-2 rounded">
                      <h3 className="text-base font-medium text-center">RELOCATIONS</h3>
                    </div>
                    <Select value={relocations} onValueChange={setRelocations}>
                      <SelectTrigger className="bg-white dark:bg-gray-800">
                        <SelectValue placeholder="SELECT ONE" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SELECT ONE">SELECT ONE</SelectItem>
                        <SelectItem value="ALL RELOCATIONS BY UTILITY OWNERS">
                          ALL RELOCATIONS BY UTILITY OWNERS
                        </SelectItem>
                        <SelectItem value="ALL RELOCATIONS BY CONTRACTOR">
                          ALL RELOCATIONS BY CONTRACTOR
                        </SelectItem>
                        <SelectItem value="MIXED RELOCATIONS (OWNERS AND CONTRACTOR)">
                          MIXED RELOCATIONS (OWNERS AND CONTRACTOR)
                        </SelectItem>
                        <SelectItem value="N/A">N/A</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Utility Coordination Totals */}
                <div className="col-span-12 grid grid-cols-12 gap-4 py-4">
                  <div className="col-span-12 mb-2">
                    <h3 className="text-base font-medium bg-gray-100 dark:bg-gray-700 p-2 rounded">
                      TOTALS:
                    </h3>
                  </div>

                  <div className="col-span-12 md:col-span-6">
                    {[
                      {
                        label: 'UTILITY COORDINATION - Standard Utility Coordination',
                        value: standardUtilityCoordination,
                        setter: setStandardUtilityCoordination,
                      },
                      {
                        label: 'UTILITY COORDINATION - Standard UC Supplement #1',
                        value: standardUCSupplement1,
                        setter: setStandardUCSupplement1,
                      },
                      {
                        label: 'UTILITY COORDINATION - Standard UC Supplement #2',
                        value: standardUCSupplement2,
                        setter: setStandardUCSupplement2,
                      },
                      {
                        label: 'UTILITY COORDINATION - Standard UC Supplement #3',
                        value: standardUCSupplement3,
                        setter: setStandardUCSupplement3,
                      },
                      {
                        label: 'UTILITY COORDINATION - Standard UC Supplement #4',
                        value: standardUCSupplement4,
                        setter: setStandardUCSupplement4,
                      },
                      {
                        label: 'UTILITY COORDINATION - Partial Utility Coordination (IAC/IOM)',
                        value: partialUtilityCoordination,
                        setter: setPartialUtilityCoordination,
                      },
                    ].map((item, index) => (
                      <div key={index} className="grid grid-cols-12 gap-2 mb-2">
                        <Label htmlFor={`uc-${index}`} className="col-span-9">
                          {item.label}
                        </Label>
                        <div className="col-span-3">
                          <Input
                            id={`uc-${index}`}
                            type="number"
                            min={0}
                            value={item.value}
                            onChange={(e: any) => item.setter(parseInt(e.target.value) || 0)}
                            className="bg-white dark:bg-gray-800 text-center"
                          />
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="col-span-12 md:col-span-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="bg-purple-100 dark:bg-purple-900/30 p-2 mb-2 rounded text-center font-medium">
                          Total Reimbursable Utilities:
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg text-center font-medium">
                          0
                        </div>
                      </div>

                      <div>
                        <div className="bg-purple-100 dark:bg-purple-900/30 p-2 mb-2 rounded text-center font-medium">
                          Total Relocating Utilities:
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg text-center font-medium">
                          0
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="calculations" className="h-full">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-medium mb-4">Calculations</h3>
                  <p className="text-muted-foreground">
                    This tab will contain the calculated hours by labor classification based on the
                    project input.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="summary" className="h-full">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-medium mb-4">Summary</h3>
                  <p className="text-muted-foreground">
                    This tab will show the fee calculation summary and totals for the project.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>

      <DialogFooter className="mt-4">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </DialogFooter>
    </div>
  );
}
