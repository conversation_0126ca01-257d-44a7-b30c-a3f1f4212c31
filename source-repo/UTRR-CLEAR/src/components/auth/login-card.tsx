'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '~/components/ui/card';
import { CustomSignIn } from './custom-sign-in';

interface LoginCardProps {
  title?: string;
  description?: string;
  callbackUrl?: string;
  error?: string;
  showBranding?: boolean;
}

export function LoginCard({
  title = 'Welcome to UtilitySync',
  description = 'Streamline utility coordination and project management',
  callbackUrl = '/dashboard',
  error,
  showBranding = true,
}: LoginCardProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 p-4">
      <div className="w-full max-w-md space-y-8">
        {/* Branding Section */}
        {showBranding && (
          <div className="text-center space-y-4">
            {/* Logo */}
            <div className="flex justify-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center shadow-lg">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                  />
                </svg>
              </div>
            </div>

            {/* Title and Description */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-gray-900 tracking-tight">{title}</h1>
              <p className="text-gray-600 max-w-sm mx-auto">{description}</p>
            </div>

            {/* Features List */}
            <div className="grid grid-cols-1 gap-3 text-sm text-gray-600 max-w-sm mx-auto">
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Advanced utility mapping & 3D visualization</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Intelligent conflict detection & resolution</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Streamlined project coordination workflows</span>
              </div>
            </div>
          </div>
        )}

        {/* Login Form */}
        <CustomSignIn callbackUrl={callbackUrl} error={error} />

        {/* Footer */}
        <div className="text-center text-xs text-gray-500 space-y-2">
          <p>
            By signing in, you agree to our{' '}
            <a href="/terms" className="text-green-600 hover:text-green-700 underline">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="text-green-600 hover:text-green-700 underline">
              Privacy Policy
            </a>
          </p>
          <div className="flex items-center justify-center gap-4">
            <a href="/help" className="hover:text-gray-700">
              Help
            </a>
            <span>•</span>
            <a href="/contact" className="hover:text-gray-700">
              Contact
            </a>
            <span>•</span>
            <a href="/status" className="hover:text-gray-700">
              Status
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
