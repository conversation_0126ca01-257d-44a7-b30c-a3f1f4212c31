'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '~/lib/supabase/client';
import type { User, Session } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { safeLog } from '~/lib/error-handler';

// Extended user type with additional user data
interface ExtendedUser extends User {
  role?: string;
  organizationId?: string;
  isAdmin?: boolean;
  avatarUrl?: string;
  username?: string;
  preferences?: {
    theme?: 'light' | 'dark';
    units?: 'metric' | 'imperial';
    language?: string;
  };
}

// Auth context for additional authentication logic
interface AuthContextType {
  session: Session | null;
  loading: boolean;
  isAuthenticated: boolean;
  user: ExtendedUser | null;
  signOut: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component using Supabase
export function NextAuthProvider({ children }: { children: React.ReactNode }) {
  return <AuthWrapper>{children}</AuthWrapper>;
}

// Internal wrapper to provide auth context
function AuthWrapper({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session: initialSession } } = await supabase.auth.getSession();
      setSession(initialSession);
      if (initialSession?.user) {
        // Extend user with metadata
        const extendedUser: ExtendedUser = {
          ...initialSession.user,
          role: initialSession.user.user_metadata?.role || 'user',
          organizationId: initialSession.user.user_metadata?.organization_id,
          isAdmin: initialSession.user.user_metadata?.role === 'admin',
          avatarUrl: initialSession.user.user_metadata?.avatar_url,
          username: initialSession.user.user_metadata?.username || initialSession.user.email,
          preferences: {
            theme: initialSession.user.user_metadata?.theme || 'light',
            units: initialSession.user.user_metadata?.unit_preference || 'imperial',
            language: initialSession.user.user_metadata?.language || 'en'
          }
        };
        setUser(extendedUser);
      } else {
        setUser(null);
      }
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      if (session?.user) {
        const extendedUser: ExtendedUser = {
          ...session.user,
          role: session.user.user_metadata?.role || 'user',
          organizationId: session.user.user_metadata?.organization_id,
          isAdmin: session.user.user_metadata?.role === 'admin',
          avatarUrl: session.user.user_metadata?.avatar_url,
          username: session.user.user_metadata?.username || session.user.email,
          preferences: {
            theme: session.user.user_metadata?.theme || 'light',
            units: session.user.user_metadata?.unit_preference || 'imperial',
            language: session.user.user_metadata?.language || 'en'
          }
        };
        setUser(extendedUser);
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin users have all permissions
    if (user.role === 'admin') return true;
    
    // For now, we'll implement basic role-based permissions
    // This can be extended later with a proper permissions system
    return false;
  };

  // Check if user has specific role
  const hasRole = (role: string): boolean => {
    if (!user?.role) return false;
    return user.role === role || user.role === 'admin';
  };

  // Refresh session data
  const refreshSession = async (): Promise<void> => {
    try {
      const { data: { session: newSession } } = await supabase.auth.getSession();
      setSession(newSession);
      if (newSession?.user) {
        const extendedUser: ExtendedUser = {
          ...newSession.user,
          role: newSession.user.user_metadata?.role || 'user',
          organizationId: newSession.user.user_metadata?.organization_id,
          isAdmin: newSession.user.user_metadata?.role === 'admin',
          avatarUrl: newSession.user.user_metadata?.avatar_url,
          username: newSession.user.user_metadata?.username || newSession.user.email,
          preferences: {
            theme: newSession.user.user_metadata?.theme || 'light',
            units: newSession.user.user_metadata?.unit_preference || 'imperial',
            language: newSession.user.user_metadata?.language || 'en'
          }
        };
        setUser(extendedUser);
      }
    } catch (error) {
      safeLog.error('Failed to refresh session:', { error: String(error) });
    }
  };

  // Custom sign out with cleanup
  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      router.push('/auth/signin');
    } catch (error) {
      safeLog.error('Sign out error:', { error: String(error) });
    }
  };

  const authValue: AuthContextType = {
    session,
    loading,
    isAuthenticated: !!session?.user,
    user,
    signOut: handleSignOut,
    hasPermission,
    hasRole,
    refreshSession,
  };

  return <AuthContext.Provider value={authValue}>{children}</AuthContext.Provider>;
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within a NextAuthProvider');
  }
  return context;
}

// Loading component for authentication states
export function AuthLoadingSpinner() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center shadow-lg mx-auto">
          <svg
            className="w-8 h-8 text-white animate-pulse"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
            />
          </svg>
        </div>
        <div className="space-y-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    </div>
  );
}
