'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import TileLayer from 'ol/layer/Tile.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import OSM from 'ol/source/OSM.js';
import XYZ from 'ol/source/XYZ.js';
import BingMaps from 'ol/source/BingMaps.js';
import { ScaleLine, MousePosition, OverviewMap, ZoomSlider } from 'ol/control.js';
import { defaults as defaultControls } from 'ol/control/defaults.js';
import { createStringXY } from 'ol/coordinate.js';
import { Feature } from 'ol';
import { fromLonLat, transform } from 'ol/proj.js';
import { register } from 'ol/proj/proj4.js';
import proj4 from 'proj4';
import { LineString, Point } from 'ol/geom.js';
import { getLength } from 'ol/sphere.js';
import GeoJSON from 'ol/format/GeoJSON.js';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Select as UISelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Slider } from '~/components/ui/slider';
import { Switch } from '~/components/ui/switch';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Badge } from '~/components/ui/badge';
import { 
  Download, Upload, Ruler, Square, Circle, Spline, Grid3x3, 
  Layers, Map as MapIcon, Satellite, Navigation, Settings,
  FileText, Database, Zap, Droplet, Fuel, Phone, Wifi, Cable,
  Save, FolderOpen, FileInput, Trash2, Copy, Move, RotateCw,
  AlertCircle, MapPin, Gauge, FlameKindling, DropletIcon
} from 'lucide-react';

// Custom modules
import { AdvancedDrawingTools, CADStyleFactory, MeasurementTool, SnapConfiguration, type DrawingMode } from './advanced-draw-tools';
import { FormatManager, CoordinateTransformUtil, DXFHandler } from './format-handlers';
import { UtilityVectorTileLayer, SymbolClusterLayer, WebGLUtilityPoints, DataLoadingStrategies, PerformanceMonitor } from './performance-layers';
import { createCADLineStyle, createSymbolStyle } from './cad-line-style';
import { UTILITY_TYPES, UTILITY_SYMBOLS, type UtilityType, type UtilityProperties } from './utility-types';
import Draw, { DrawEvent } from 'ol/interaction/Draw.js';
import Modify from 'ol/interaction/Modify.js';
import Select from 'ol/interaction/Select.js';
import Translate from 'ol/interaction/Translate.js';
import Snap from 'ol/interaction/Snap.js';
import { Collection } from 'ol';
import { never, shiftKeyOnly, click, pointerMove } from 'ol/events/condition.js';
import Graticule from 'ol/layer/Graticule.js';
import { Stroke } from 'ol/style.js';
import { api } from '~/trpc/react';
import { safeLog } from '~/lib/error-handler';

// Register custom projections for civil engineering
proj4.defs('EPSG:2965', '+proj=tmerc +lat_0=37.5 +lon_0=-85.66666666666667 +k=0.999966667 +x_0=100000 +y_0=250000 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=us-ft +no_defs');
proj4.defs('EPSG:2966', '+proj=tmerc +lat_0=37.5 +lon_0=-87.08333333333333 +k=0.999966667 +x_0=900000 +y_0=250000 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=us-ft +no_defs');
register(proj4);

interface UtilityMap2DCompleteProps {
  projectId: string;
  onSave?: (features: Feature[]) => void;
  initialFeatures?: Feature[];
}

const UtilityMap2DComplete: React.FC<UtilityMap2DCompleteProps> = ({ 
  projectId, 
  onSave,
  initialFeatures = []
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // State management
  const [map, setMap] = useState<Map | null>(null);
  const [activeDrawTool, setActiveDrawTool] = useState<DrawingMode | null>(null);
  const [selectedFeatures, setSelectedFeatures] = useState<Collection<Feature>>(new Collection());
  const [advancedDrawTools, setAdvancedDrawTools] = useState<AdvancedDrawingTools | null>(null);
  const [measurementTool, setMeasurementTool] = useState<MeasurementTool | null>(null);
  const [basemapType, setBasemapType] = useState<'osm' | 'bing-aerial' | 'bing-road'>('osm');
  const [showGrid, setShowGrid] = useState(false);
  const [coordinateSystem, setCoordinateSystem] = useState('EPSG:3857');
  const [performanceMode, setPerformanceMode] = useState<'standard' | 'webgl' | 'vectortile'>('standard');
  
  // Utility drawing properties
  const [selectedTool, setSelectedTool] = useState<'line' | 'symbol' | null>(null);
  const [selectedUtilityType, setSelectedUtilityType] = useState<UtilityType>('electric');
  const [selectedSymbol, setSelectedSymbol] = useState<string>('telecom-pole');
  const [conflicts, setConflicts] = useState<any[]>([]);
  
  // Drawing properties
  const [drawingProps, setDrawingProps] = useState({
    depth: 5,
    aerial: false,
    proposed: true,
    category: 'distribution',
    material: '',
    size: 4, // inches
    pressure: 0,
    voltage: 0,
  });

  // Master relocation mode
  const [relocationMode, setRelocationMode] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<Feature | null>(null);
  
  // Layer management
  const [vectorSource] = useState(new VectorSource({ features: initialFeatures }));
  const [clusterSource] = useState(new VectorSource());
  const [drawSource] = useState(new VectorSource());
  const [vectorLayer, setVectorLayer] = useState<VectorLayer | null>(null);
  const [clusterLayer, setClusterLayer] = useState<VectorLayer | null>(null);
  const [drawLayer, setDrawLayer] = useState<VectorLayer | null>(null);
  
  // Interaction references
  const [drawInteraction, setDrawInteraction] = useState<Draw | null>(null);
  const [selectInteraction, setSelectInteraction] = useState<Select | null>(null);
  const [modifyInteraction, setModifyInteraction] = useState<Modify | null>(null);
  const [translateInteraction, setTranslateInteraction] = useState<Translate | null>(null);
  const [snapInteractions, setSnapInteractions] = useState<any[]>([]);

  // Snap configuration
  const [snapConfig, setSnapConfig] = useState<SnapConfiguration>({
    enabled: true,
    pixelTolerance: 10,
    vertex: true,
    edge: true,
    angle: false,
    angleStep: 45
  });

  const utilityTypeIcons: Record<UtilityType, React.ReactNode> = {
    electric: <Zap className="h-4 w-4" />,
    naturalGas: <Fuel className="h-4 w-4" />,
    gasPipeline: <Fuel className="h-4 w-4" />,
    water: <Droplet className="h-4 w-4" />,
    sanitarySewer: <Cable className="h-4 w-4" />,
    fiber: <Wifi className="h-4 w-4" />,
    telecom: <Phone className="h-4 w-4" />,
    steam: <Gauge className="h-4 w-4" />,
    reclaimedWater: <DropletIcon className="h-4 w-4" />,
  };

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    // Create layers
    const osmLayer = new TileLayer({
      source: new OSM(),
      visible: true,
    });

    const bingAerialLayer = new TileLayer({
      source: new BingMaps({
        key: 'YOUR_BING_MAPS_KEY', // Replace with actual key
        imagerySet: 'Aerial',
      }),
      visible: false,
    });

    const bingRoadLayer = new TileLayer({
      source: new BingMaps({
        key: 'YOUR_BING_MAPS_KEY', // Replace with actual key
        imagerySet: 'Road',
      }),
      visible: false,
    });

    // Vector layer with CAD styling
    const vLayer = new VectorLayer({
      source: vectorSource,
      style: (feature, resolution) => {
        if (feature instanceof Feature) {
          return createCADLineStyle(feature, resolution);
        }
        return [];
      },
    });

    const cLayer = SymbolClusterLayer.create(vectorSource);

    const dLayer = new VectorLayer({
      source: drawSource,
      style: (feature, resolution) => {
        if (feature instanceof Feature) {
          return createCADLineStyle(feature, resolution);
        }
        return [];
      },
    });

    const graticule = new Graticule({
      strokeStyle: new Stroke({
        color: 'rgba(0,0,0,0.2)',
        width: 1,
        lineDash: [0.5, 4],
      }),
      showLabels: true,
      visible: false,
    });

    // Create map
    const mapInstance = new Map({
      target: mapRef.current,
      layers: [
        osmLayer,
        bingAerialLayer,
        bingRoadLayer,
        vLayer,
        cLayer,
        dLayer,
        graticule,
      ],
      view: new View({
        center: fromLonLat([-86.1581, 39.7684]), // Indianapolis
        zoom: 16,
        projection: coordinateSystem,
      }),
      controls: defaultControls().extend([
        new ScaleLine({
          units: 'us',
        }),
        new MousePosition({
          coordinateFormat: createStringXY(4),
          projection: 'EPSG:4326',
        }),
        new OverviewMap({
          collapsed: true,
        }),
        new ZoomSlider(),
      ]),
    });

    // Add select interaction
    const select = new Select();
    mapInstance.addInteraction(select);
    setSelectInteraction(select);

    // Add modify interaction
    const modify = new Modify({
      features: select.getFeatures(),
    });
    mapInstance.addInteraction(modify);
    setModifyInteraction(modify);

    // Add snap interactions
    const snapVectorSource = new Snap({
      source: vectorSource,
      pixelTolerance: snapConfig.pixelTolerance,
    });
    mapInstance.addInteraction(snapVectorSource);
    setSnapInteractions([snapVectorSource]);

    // Initialize advanced tools
    const drawTools = new AdvancedDrawingTools(mapInstance, dLayer);
    const measureTool = new MeasurementTool(drawSource);
    
    setAdvancedDrawTools(drawTools);
    setMeasurementTool(measureTool);
    setVectorLayer(vLayer);
    setClusterLayer(cLayer);
    setDrawLayer(dLayer);
    setMap(mapInstance);

    return () => {
      mapInstance.dispose();
    };
  }, [coordinateSystem, vectorSource, drawSource, clusterSource, snapConfig.pixelTolerance, snapInteractions]);

  // Update snap interactions when config changes
  useEffect(() => {
    if (!map || !snapConfig.enabled) return;

    snapInteractions.forEach(snap => {
      map.removeInteraction(snap);
    });

    if (snapConfig.enabled) {
      const newSnaps = [];
      if (snapConfig.vertex || snapConfig.edge) {
        const snapVectorSource = new Snap({
          source: vectorSource,
          pixelTolerance: snapConfig.pixelTolerance,
          vertex: snapConfig.vertex,
          edge: snapConfig.edge,
        });
        const snapClusterSource = new Snap({
          source: clusterSource,
          pixelTolerance: snapConfig.pixelTolerance,
          vertex: snapConfig.vertex,
          edge: snapConfig.edge,
        });
        map.addInteraction(snapVectorSource);
        map.addInteraction(snapClusterSource);
        newSnaps.push(snapVectorSource, snapClusterSource);
      }
      setSnapInteractions(newSnaps);
    }
  }, [map, snapConfig, vectorSource, clusterSource, snapInteractions]);

  // Check for conflicts
  const checkConflicts = useCallback(() => {
    const features = vectorSource.getFeatures();
    const newConflicts: any[] = [];

    for (let i = 0; i < features.length; i++) {
      for (let j = i + 1; j < features.length; j++) {
        const feat1 = features[i];
        const feat2 = features[j];
        
        if (!feat1 || !feat2) continue;
        
        const geom1 = feat1.getGeometry() as LineString;
        const geom2 = feat2.getGeometry() as LineString;
        
        if (!geom1 || !geom2) continue;

        const props1 = feat1.getProperties() as UtilityProperties;
        const props2 = feat2.getProperties() as UtilityProperties;

        // Skip if either is abandoned
        if (props1.toBeAbandoned || props2.toBeAbandoned) continue;

        // Check for intersections
        const coords1 = geom1.getCoordinates();
        const coords2 = geom2.getCoordinates();

        for (let k = 0; k < coords1.length - 1; k++) {
          for (let l = 0; l < coords2.length - 1; l++) {
            const intersection = getLineIntersection(
              coords1[k]!, coords1[k + 1]!,
              coords2[l]!, coords2[l + 1]!
            );

            if (intersection) {
              const conflictType = Math.abs(props1.depth - props2.depth) < 0.5 
                ? 'hard' 
                : Math.abs(props1.depth - props2.depth) < 2 
                  ? 'soft' 
                  : null;

              if (conflictType) {
                newConflicts.push({
                  id: `${feat1.getId()}-${feat2.getId()}`,
                  type: conflictType,
                  utility1: props1,
                  utility2: props2,
                  location: intersection,
                  depthDifference: Math.abs(props1.depth - props2.depth),
                });
              }
            }
          }
        }
      }
    }

    setConflicts(newConflicts);
  }, [vectorSource]);

  // Start drawing utility
  const startDrawingUtility = (tool: 'line' | 'symbol') => {
    if (!map) return;

    // Clear existing interactions
    if (drawInteraction) {
      map.removeInteraction(drawInteraction);
    }

    // Clear advanced drawing tools
    if (advancedDrawTools) {
      advancedDrawTools.stopDrawing();
    }

    setSelectedTool(tool);

    const newDraw = new Draw({
      source: vectorSource,
      type: tool === 'line' ? 'LineString' : 'Point',
    });

    newDraw.on('drawend', (e: DrawEvent) => {
      const feature = e.feature;
      
      const properties: UtilityProperties = {
        id: `utility-${Date.now()}`,
        type: selectedUtilityType,
        category: drawingProps.category as any,
        material: drawingProps.material,
        size: drawingProps.size,
        depth: drawingProps.depth,
        aerial: drawingProps.aerial,
        proposed: drawingProps.proposed,
        toBeAbandoned: false,
        pressure: drawingProps.pressure,
        voltage: drawingProps.voltage,
      };

      if (tool === 'symbol') {
        properties.symbolType = selectedSymbol;
      }

      feature.setProperties(properties);
      
      // Check for conflicts after drawing
      checkConflicts();
    });

    map.addInteraction(newDraw);
    setDrawInteraction(newDraw);
  };

  // Stop drawing
  const stopDrawing = () => {
    if (!map) return;

    if (drawInteraction) {
      map.removeInteraction(drawInteraction);
      setDrawInteraction(null);
    }

    if (advancedDrawTools) {
      advancedDrawTools.stopDrawing();
    }

    if (measurementTool) {
      measurementTool.deactivate();
    }

    setSelectedTool(null);
    setActiveDrawTool(null);
  };

  // Start advanced drawing
  const startAdvancedDrawing = (mode: DrawingMode) => {
    if (!map || !advancedDrawTools) return;

    stopDrawing();
    setActiveDrawTool(mode);

    switch (mode) {
      case 'perpendicular':
        if (selectedFeature) {
          advancedDrawTools.startPerpendicular(selectedFeature);
        }
        break;
      case 'parallel':
        if (selectedFeature) {
          advancedDrawTools.startParallelDrawing(selectedFeature, 10); // 10 units offset
        }
        break;
      case 'arc':
        advancedDrawTools.startArcDrawing();
        break;
      case 'measure_line':
        measurementTool?.activate(map!, 'LineString');
        break;
      case 'measure_area':
        measurementTool?.activate(map!, 'Polygon');
        break;
    }
  };

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      const content = e.target?.result as string;
      const extension = file.name.split('.').pop()?.toLowerCase();
      
      try {
        const features = await FormatManager.importFeatures(
          content,
          extension as any
        );
        
        features.forEach(feature => {
          vectorSource.addFeature(feature);
        });
        
        if (features.length > 0 && map) {
          const extent = vectorSource.getExtent();
          map.getView().fit(extent, { padding: [50, 50, 50, 50] });
        }

        checkConflicts();
      } catch (error) {
        safeLog.error('Import error:', { error: String(error) });
        alert('Error importing file: ' + error);
      }
    };
    reader.readAsText(file);
  };

  // Handle export
  const handleExport = (format: string) => {
    const features = [...vectorSource.getFeatures(), ...clusterSource.getFeatures()];
    
    try {
      const content = FormatManager.exportFeatures(
        features,
        format as any
      );
      
      const filename = `utilities-${projectId}-${Date.now()}.${format}`;
      FormatManager.downloadFile(content, filename);
    } catch (error) {
      safeLog.error('Export error:', { error: String(error) });
      alert('Error exporting file: ' + error);
    }
  };

  // Basemap switcher
  const switchBasemap = (type: 'osm' | 'bing-aerial' | 'bing-road') => {
    if (!map) return;
    
    map.getLayers().forEach(layer => {
      if (layer instanceof TileLayer) {
        const source = layer.getSource();
        if (source instanceof OSM) {
          layer.setVisible(type === 'osm');
        } else if (source instanceof BingMaps) {
          const imagerySet = source.getImagerySet();
          layer.setVisible(
            (type === 'bing-aerial' && imagerySet === 'Aerial') ||
            (type === 'bing-road' && imagerySet === 'Road')
          );
        }
      }
    });
    
    setBasemapType(type);
  };

  // Mark feature as abandoned
  const markAsAbandoned = () => {
    if (!selectedFeature) return;

    const props = selectedFeature.getProperties() as UtilityProperties;
    props.toBeAbandoned = !props.toBeAbandoned;
    selectedFeature.setProperties(props);
    vectorSource.changed();
  };

  // Delete selected features
  const deleteSelected = () => {
    selectedFeatures.forEach(feature => {
      vectorSource.removeFeature(feature);
      clusterSource.removeFeature(feature);
    });
    selectedFeatures.clear();
    checkConflicts();
  };

  // Get line intersection point
  function getLineIntersection(p1: number[], p2: number[], p3: number[], p4: number[]): number[] | null {
    if (p1[0] === undefined || p1[1] === undefined || p2[0] === undefined || p2[1] === undefined || 
        p3[0] === undefined || p3[1] === undefined || p4[0] === undefined || p4[1] === undefined) {
      return null;
    }
    
    const x1 = p1[0], y1 = p1[1];
    const x2 = p2[0], y2 = p2[1];
    const x3 = p3[0], y3 = p3[1];
    const x4 = p4[0], y4 = p4[1];

    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    if (Math.abs(denom) < 0.0001) return null;

    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return [
        x1 + t * (x2 - x1),
        y1 + t * (y2 - y1)
      ];
    }

    return null;
  }

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <Card className="w-80 flex-shrink-0 overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center justify-between">
            Utility Mapping
            <Badge variant={relocationMode ? "default" : "outline"}>
              {relocationMode ? "Master Relocation" : "Standard"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="draw" className="h-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="draw">Draw</TabsTrigger>
              <TabsTrigger value="layers">Layers</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
              <TabsTrigger value="conflicts">
                Conflicts
                {conflicts.length > 0 && (
                  <Badge variant="destructive" className="ml-1 h-5 w-5 p-0">
                    {conflicts.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Draw Tab */}
            <TabsContent value="draw" className="p-4 space-y-4">
              {/* Master Relocation Toggle */}
              <div className="flex items-center space-x-2 p-3 bg-muted rounded-lg">
                <Switch
                  id="relocation-mode"
                  checked={relocationMode}
                  onCheckedChange={setRelocationMode}
                />
                <Label htmlFor="relocation-mode" className="text-sm font-medium">
                  Master Relocation Mode
                </Label>
              </div>

              {/* Utility Type Selection */}
              <div>
                <Label className="text-sm font-medium mb-2">Utility Type</Label>
                <UISelect value={selectedUtilityType} onValueChange={(v) => setSelectedUtilityType(v as UtilityType)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(UTILITY_TYPES).map(([key, utility]) => (
                      <SelectItem key={key} value={key}>
                        <div className="flex items-center gap-2">
                          {utilityTypeIcons[key as UtilityType]}
                          <span>{utility.name}</span>
                          <Badge variant="outline" className="ml-auto">
                            {utility.code}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </UISelect>
              </div>

              {/* Drawing Tools */}
              <div>
                <Label className="text-sm font-medium mb-2">Drawing Tools</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={selectedTool === 'line' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => startDrawingUtility('line')}
                  >
                    <Spline className="mr-2 h-4 w-4" />
                    Line
                  </Button>
                  <Button
                    variant={selectedTool === 'symbol' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => startDrawingUtility('symbol')}
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    Symbol
                  </Button>
                </div>
              </div>

              {/* Properties */}
              <ScrollArea className="h-80">
                <div className="space-y-4">
                  {/* Category */}
                  <div>
                    <Label className="text-sm">Category</Label>
                    <UISelect 
                      value={drawingProps.category} 
                      onValueChange={(v) => setDrawingProps({...drawingProps, category: v})}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {UTILITY_TYPES[selectedUtilityType].categories.map(cat => (
                          <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                        ))}
                      </SelectContent>
                    </UISelect>
                  </div>

                  {/* Material */}
                  <div>
                    <Label className="text-sm">Material</Label>
                    <UISelect 
                      value={drawingProps.material} 
                      onValueChange={(v) => setDrawingProps({...drawingProps, material: v})}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select material" />
                      </SelectTrigger>
                      <SelectContent>
                        {UTILITY_TYPES[selectedUtilityType].materials.map(mat => (
                          <SelectItem key={mat} value={mat}>{mat}</SelectItem>
                        ))}
                      </SelectContent>
                    </UISelect>
                  </div>

                  {/* Size */}
                  <div>
                    <Label className="text-sm">Size (inches)</Label>
                    <Input
                      type="number"
                      value={drawingProps.size}
                      onChange={(e: any) => setDrawingProps({...drawingProps, size: Number(e.target.value)})}
                      className="h-8"
                    />
                  </div>

                  {/* Depth */}
                  <div>
                    <Label className="text-sm">Depth (ft)</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        value={[drawingProps.depth]}
                        onValueChange={([v]) => setDrawingProps({...drawingProps, depth: v ?? 0})}
                        min={0}
                        max={20}
                        step={0.5}
                        className="h-8"
                      />
                      <span className="text-sm w-12 text-right">{drawingProps.depth}</span>
                    </div>
                  </div>

                  {/* Pressure/Voltage */}
                  {['naturalGas', 'gasPipeline', 'water', 'steam', 'reclaimedWater'].includes(selectedUtilityType) && (
                    <div>
                      <Label className="text-sm">Pressure ({UTILITY_TYPES[selectedUtilityType].units[0]})</Label>
                      <Input
                        type="number"
                        value={drawingProps.pressure}
                        onChange={(e: any) => setDrawingProps({...drawingProps, pressure: Number(e.target.value)})}
                        className="h-8"
                      />
                    </div>
                  )}

                  {selectedUtilityType === 'electric' && (
                    <div>
                      <Label className="text-sm">Voltage ({UTILITY_TYPES[selectedUtilityType].units[0]})</Label>
                      <Input
                        type="number"
                        value={drawingProps.voltage}
                        onChange={(e: any) => setDrawingProps({...drawingProps, voltage: Number(e.target.value)})}
                        className="h-8"
                      />
                    </div>
                  )}

                  {/* Toggles */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="aerial"
                        checked={drawingProps.aerial}
                        onCheckedChange={(v) => setDrawingProps({...drawingProps, aerial: v})}
                      />
                      <Label htmlFor="aerial" className="text-sm">Aerial Installation</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="proposed"
                        checked={drawingProps.proposed}
                        onCheckedChange={(v) => setDrawingProps({...drawingProps, proposed: v})}
                      />
                      <Label htmlFor="proposed" className="text-sm">Proposed (vs Existing)</Label>
                    </div>
                  </div>

                  {/* Symbol Selection */}
                  {selectedTool === 'symbol' && (
                    <div>
                      <Label className="text-sm">Symbol Type</Label>
                      <UISelect value={selectedSymbol} onValueChange={setSelectedSymbol}>
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {UTILITY_SYMBOLS.map(symbol => (
                            <SelectItem key={symbol.id} value={symbol.id}>
                              {symbol.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </UISelect>
                    </div>
                  )}
                </div>
              </ScrollArea>

              {/* CAD Tools */}
              <div>
                <Label className="text-sm font-medium mb-2">CAD Tools</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => startAdvancedDrawing('perpendicular')}
                    disabled={!selectedFeature}
                  >
                    Perpendicular
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => startAdvancedDrawing('parallel')}
                    disabled={!selectedFeature}
                  >
                    Parallel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => startAdvancedDrawing('arc')}
                  >
                    Arc
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => startAdvancedDrawing('measure_line')}
                  >
                    <Ruler className="mr-2 h-4 w-4" />
                    Measure
                  </Button>
                </div>
              </div>

              {/* Actions */}
              {selectedFeature && (
                <div className="pt-2 border-t">
                  <Button
                    variant={selectedFeature.get('toBeAbandoned') ? 'default' : 'outline'}
                    size="sm"
                    className="w-full"
                    onClick={markAsAbandoned}
                  >
                    {selectedFeature.get('toBeAbandoned') ? 'Unmark' : 'Mark'} as Abandoned
                  </Button>
                </div>
              )}

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={stopDrawing}
              >
                Stop Drawing
              </Button>
            </TabsContent>

            {/* Layers Tab */}
            <TabsContent value="layers" className="p-4 space-y-4">
              <div>
                <Label className="text-sm font-medium mb-2">Base Maps</Label>
                <div className="space-y-2">
                  <Button
                    variant={basemapType === 'osm' ? 'default' : 'outline'}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => switchBasemap('osm')}
                  >
                    <MapIcon className="mr-2 h-4 w-4" />
                    OpenStreetMap
                  </Button>
                  <Button
                    variant={basemapType === 'bing-aerial' ? 'default' : 'outline'}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => switchBasemap('bing-aerial')}
                  >
                    <Satellite className="mr-2 h-4 w-4" />
                    Aerial Imagery
                  </Button>
                  <Button
                    variant={basemapType === 'bing-road' ? 'default' : 'outline'}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => switchBasemap('bing-road')}
                  >
                    <Navigation className="mr-2 h-4 w-4" />
                    Road Map
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium mb-2">Overlays</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-grid"
                      checked={showGrid}
                      onCheckedChange={(checked) => {
                        setShowGrid(checked);
                        map?.getLayers().forEach(layer => {
                          if (layer instanceof Graticule) {
                            layer.setVisible(checked);
                          }
                        });
                      }}
                    />
                    <Label htmlFor="show-grid">Show Grid</Label>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium mb-2">Layer Visibility</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Utilities</span>
                    <Switch
                      defaultChecked
                      onCheckedChange={(checked) => {
                        vectorLayer?.setVisible(checked);
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Symbols</span>
                    <Switch
                      defaultChecked
                      onCheckedChange={(checked) => {
                        clusterLayer?.setVisible(checked);
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Drawing</span>
                    <Switch
                      defaultChecked
                      onCheckedChange={(checked) => {
                        drawLayer?.setVisible(checked);
                      }}
                    />
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium mb-2">Performance Mode</Label>
                <UISelect value={performanceMode} onValueChange={(v) => setPerformanceMode(v as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="webgl">WebGL (High Performance)</SelectItem>
                    <SelectItem value="vectortile">Vector Tiles</SelectItem>
                  </SelectContent>
                </UISelect>
              </div>

              <div>
                <Label className="text-sm font-medium mb-2">Coordinate System</Label>
                <UISelect value={coordinateSystem} onValueChange={setCoordinateSystem}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="EPSG:3857">Web Mercator</SelectItem>
                    <SelectItem value="EPSG:4326">WGS84</SelectItem>
                    <SelectItem value="EPSG:2965">Indiana State Plane East</SelectItem>
                    <SelectItem value="EPSG:2966">Indiana State Plane West</SelectItem>
                  </SelectContent>
                </UISelect>
              </div>
            </TabsContent>

            {/* Tools Tab */}
            <TabsContent value="tools" className="p-4 space-y-4">
              <div>
                <Label className="text-sm font-medium mb-2">Import/Export</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExport('geojson')}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    GeoJSON
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExport('dxf')}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    DXF
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExport('kml')}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    KML
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExport('wkt')}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    WKT
                  </Button>
                </div>
                <div className="mt-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileImport}
                    accept=".geojson,.json,.kml,.kmz,.dxf,.wkt,.gml"
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Import File
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium mb-2">Selection Tools</Label>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={deleteSelected}
                    disabled={selectedFeatures.getLength() === 0}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => measurementTool?.clearMeasurements()}
                  >
                    Clear Measurements
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium mb-2">Snap Settings</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="snap-enabled"
                      checked={snapConfig.enabled}
                      onCheckedChange={(v) => setSnapConfig({...snapConfig, enabled: v})}
                    />
                    <Label htmlFor="snap-enabled">Enable Snapping</Label>
                  </div>
                  <div>
                    <Label className="text-xs">Tolerance: {snapConfig.pixelTolerance}px</Label>
                    <Slider
                      value={[snapConfig.pixelTolerance]}
                      onValueChange={([v]) => setSnapConfig({...snapConfig, pixelTolerance: v ?? 10})}
                      max={50}
                      min={5}
                      step={5}
                    />
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button
                  variant="default"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    if (onSave) {
                      onSave([...vectorSource.getFeatures(), ...clusterSource.getFeatures()]);
                    }
                  }}
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save to Database
                </Button>
              </div>
            </TabsContent>

            {/* Conflicts Tab */}
            <TabsContent value="conflicts" className="p-4">
              <ScrollArea className="h-[500px]">
                {conflicts.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No conflicts detected
                  </div>
                ) : (
                  <div className="space-y-2">
                    {conflicts.map((conflict: any) => (
                      <Card key={conflict.id} className="p-3">
                        <div className="flex items-start gap-2">
                          <AlertCircle className={`h-4 w-4 mt-0.5 ${
                            conflict.type === 'hard' ? 'text-destructive' : 'text-yellow-600'
                          }`} />
                          <div className="flex-1 text-sm">
                            <p className="font-medium">
                              {conflict.type === 'hard' ? 'Hard' : 'Soft'} Conflict
                            </p>
                            <p className="text-muted-foreground">
                              {UTILITY_TYPES[conflict.utility1.type as UtilityType]?.name || 'Unknown'} vs {UTILITY_TYPES[conflict.utility2.type as UtilityType]?.name || 'Unknown'}
                            </p>
                            <p className="text-xs">
                              Depth difference: {conflict.depthDifference.toFixed(1)} ft
                            </p>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="mt-1 h-6 text-xs"
                              onClick={() => {
                                if (map) {
                                  map.getView().animate({
                                    center: conflict.location,
                                    zoom: 19,
                                    duration: 1000,
                                  });
                                }
                              }}
                            >
                              Zoom to Location
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Map Container */}
      <div className="flex-1 relative">
        <div ref={mapRef} className="h-full w-full" />
        
        {/* Performance Monitor */}
        {performanceMode !== 'standard' && (
          <div className="absolute top-4 right-4 bg-background/80 backdrop-blur rounded-lg p-2 text-xs">
            <div>FPS: <span id="fps">60</span></div>
            <div>Features: {vectorSource.getFeatures().length}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UtilityMap2DComplete;