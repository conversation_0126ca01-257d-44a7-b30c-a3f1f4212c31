'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the GIS page content with no SSR
const GISProfessionalContent = dynamic(
  () => import('~/app/(protected)/gis-professional/client-page'),
  {
    ssr: false,
    loading: () => (
      <div className="container mx-auto p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-64 bg-muted rounded-lg mb-6" />
          <div className="h-[800px] bg-muted rounded-lg" />
        </div>
      </div>
    ),
  }
);

export default function GISPageWrapper() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-64 bg-muted rounded-lg mb-6" />
          <div className="h-[800px] bg-muted rounded-lg" />
        </div>
      </div>
    );
  }

  return <GISProfessionalContent />;
}