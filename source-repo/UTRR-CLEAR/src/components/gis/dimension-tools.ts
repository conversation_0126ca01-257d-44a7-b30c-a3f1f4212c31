import { Feature } from 'ol';
import { Point, LineString, Polygon } from 'ol/geom.js';
import { Style, Stroke, Fill, Text, Circle as CircleStyle, RegularShape } from 'ol/style.js';
import VectorSource from 'ol/source/Vector.js';
import Draw, { DrawEvent } from 'ol/interaction/Draw.js';
import { Map } from 'ol';
import type { Coordinate } from 'ol/coordinate.js';
import { getLength } from 'ol/sphere.js';
import { fromLonLat, toLonLat } from 'ol/proj.js';

// Dimension types following construction standards
export type DimensionType = 
  | 'linear' 
  | 'aligned' 
  | 'angular' 
  | 'radius' 
  | 'diameter' 
  | 'leader'
  | 'continuous'
  | 'baseline';

// Dimension style configuration
export interface DimensionStyle {
  textHeight: number;
  textColor: string;
  lineColor: string;
  lineWidth: number;
  arrowSize: number;
  extensionLineOffset: number;
  extensionLineOvershoot: number;
  textOffset: number;
  decimalPlaces: number;
  unit: 'ft' | 'm' | 'in' | 'mm';
  showUnit: boolean;
  arrowStyle: 'filled' | 'open' | 'dot' | 'architectural';
  textPosition: 'above' | 'inline' | 'below';
}

// Default CAD-style dimension configuration
export const DEFAULT_DIMENSION_STYLE: DimensionStyle = {
  textHeight: 14,
  textColor: '#000000',
  lineColor: '#0066ff',
  lineWidth: 1,
  arrowSize: 10,
  extensionLineOffset: 3,
  extensionLineOvershoot: 3,
  textOffset: 5,
  decimalPlaces: 2,
  unit: 'ft',
  showUnit: true,
  arrowStyle: 'filled',
  textPosition: 'above',
};

// Dimension data structure
export interface DimensionData {
  type: DimensionType;
  points: Coordinate[];
  value: number;
  text?: string;
  angle?: number;
  radius?: number;
  center?: Coordinate;
}

export class DimensionTools {
  private map: Map;
  private source: VectorSource;
  private currentDraw: Draw | null = null;
  private style: DimensionStyle;
  private temporaryFeatures: Feature[] = [];

  constructor(map: Map, source: VectorSource, style?: Partial<DimensionStyle>) {
    this.map = map;
    this.source = source;
    this.style = { ...DEFAULT_DIMENSION_STYLE, ...style };
  }

  // Start linear dimension (horizontal/vertical)
  startLinearDimension(direction: 'horizontal' | 'vertical' | 'aligned'): void {
    this.stopCurrentDimension();

    // Create temporary draw interaction
    this.currentDraw = new Draw({
      source: this.source,
      type: 'LineString',
      maxPoints: 2,
      style: this.getTemporaryStyle(),
    });

    this.currentDraw.on('drawend', (event: DrawEvent) => {
      const feature = event.feature;
      const geometry = feature.getGeometry() as LineString;
      const coords = geometry.getCoordinates();

      if (coords.length === 2) {
        this.createLinearDimension(coords[0]!, coords[1]!, direction);
        // Remove the temporary line
        this.source.removeFeature(feature);
      }
    });

    this.map.addInteraction(this.currentDraw);
  }

  // Create linear dimension features
  private createLinearDimension(
    p1: Coordinate, 
    p2: Coordinate, 
    direction: 'horizontal' | 'vertical' | 'aligned'
  ): void {
    if (!p1 || !p2 || p1.length < 2 || p2.length < 2) return;

    const features: Feature[] = [];
    
    // Calculate dimension points based on direction
    let dimP1: Coordinate, dimP2: Coordinate;
    
    if (direction === 'horizontal') {
      dimP1 = [p1[0]!, p2[1]!];
      dimP2 = [p2[0]!, p2[1]!];
    } else if (direction === 'vertical') {
      dimP1 = [p2[0]!, p1[1]!];
      dimP2 = [p2[0]!, p2[1]!];
    } else {
      dimP1 = p1;
      dimP2 = p2;
    }

    // Calculate dimension value
    const distance = this.calculateDistance(dimP1, dimP2);
    const dimensionText = this.formatDimensionText(distance);

    // Calculate dimension line offset
    const offset = this.style.textHeight + this.style.textOffset;
    const angle = Math.atan2(dimP2[1]! - dimP1[1]!, dimP2[0]! - dimP1[0]!);
    const perpAngle = angle + Math.PI / 2;

    // Dimension line points
    const dimLineP1: Coordinate = [
      dimP1[0]! + Math.cos(perpAngle) * offset,
      dimP1[1]! + Math.sin(perpAngle) * offset,
    ];
    const dimLineP2: Coordinate = [
      dimP2[0]! + Math.cos(perpAngle) * offset,
      dimP2[1]! + Math.sin(perpAngle) * offset,
    ];

    // Extension lines
    const ext1Start: Coordinate = [
      p1[0]! + Math.cos(perpAngle) * this.style.extensionLineOffset,
      p1[1]! + Math.sin(perpAngle) * this.style.extensionLineOffset,
    ];
    const ext1End: Coordinate = [
      dimLineP1[0]! + Math.cos(perpAngle) * this.style.extensionLineOvershoot,
      dimLineP1[1]! + Math.sin(perpAngle) * this.style.extensionLineOvershoot,
    ];

    const ext2Start: Coordinate = [
      p2[0]! + Math.cos(perpAngle) * this.style.extensionLineOffset,
      p2[1]! + Math.sin(perpAngle) * this.style.extensionLineOffset,
    ];
    const ext2End: Coordinate = [
      dimLineP2[0]! + Math.cos(perpAngle) * this.style.extensionLineOvershoot,
      dimLineP2[1]! + Math.sin(perpAngle) * this.style.extensionLineOvershoot,
    ];

    // Create extension line features
    features.push(
      this.createLineFeature([ext1Start, ext1End], 'extension'),
      this.createLineFeature([ext2Start, ext2End], 'extension')
    );

    // Create dimension line with arrows
    features.push(this.createDimensionLineFeature(dimLineP1, dimLineP2, dimensionText));

    // Add arrows
    features.push(
      this.createArrowFeature(dimLineP1, angle + Math.PI),
      this.createArrowFeature(dimLineP2, angle)
    );

    // Add all features to source
    features.forEach(feature => this.source.addFeature(feature));
  }

  // Start angular dimension
  startAngularDimension(): void {
    this.stopCurrentDimension();

    let clickCount = 0;
    const points: Coordinate[] = [];

    this.currentDraw = new Draw({
      source: this.source,
      type: 'LineString',
      maxPoints: 3,
      style: this.getTemporaryStyle(),
    });

    this.currentDraw.on('drawend', (event: DrawEvent) => {
      const feature = event.feature;
      const geometry = feature.getGeometry() as LineString;
      const coords = geometry.getCoordinates();

      if (coords.length === 3) {
        this.createAngularDimension(coords[0]!, coords[1]!, coords[2]!);
        this.source.removeFeature(feature);
      }
    });

    this.map.addInteraction(this.currentDraw);
  }

  // Create angular dimension
  private createAngularDimension(p1: Coordinate, vertex: Coordinate, p2: Coordinate): void {
    if (!p1 || !vertex || !p2 || 
        p1.length < 2 || vertex.length < 2 || p2.length < 2) return;

    const features: Feature[] = [];

    // Calculate angles
    const angle1 = Math.atan2(p1[1]! - vertex[1]!, p1[0]! - vertex[0]!);
    const angle2 = Math.atan2(p2[1]! - vertex[1]!, p2[0]! - vertex[0]!);
    
    let angleDiff = angle2 - angle1;
    if (angleDiff < 0) angleDiff += 2 * Math.PI;
    if (angleDiff > Math.PI) angleDiff = 2 * Math.PI - angleDiff;

    const angleDegrees = angleDiff * 180 / Math.PI;
    const angleText = `${angleDegrees.toFixed(1)}°`;

    // Calculate arc radius
    const radius = Math.min(
      this.calculateDistance(vertex, p1),
      this.calculateDistance(vertex, p2)
    ) * 0.3;

    // Create arc
    const arcPoints = this.createArcPoints(vertex, radius, angle1, angle2, 20);
    const arcFeature = new Feature(new LineString(arcPoints));
    arcFeature.setStyle(new Style({
      stroke: new Stroke({
        color: this.style.lineColor,
        width: this.style.lineWidth,
      }),
    }));
    features.push(arcFeature);

    // Add angle text
    const midAngle = (angle1 + angle2) / 2;
    const textPos: Coordinate = [
      vertex[0]! + Math.cos(midAngle) * radius * 1.3,
      vertex[1]! + Math.sin(midAngle) * radius * 1.3,
    ];

    const textFeature = new Feature(new Point(textPos));
    textFeature.setStyle(new Style({
      text: new Text({
        text: angleText,
        font: `${this.style.textHeight}px Arial`,
        fill: new Fill({ color: this.style.textColor }),
        stroke: new Stroke({ color: '#ffffff', width: 3 }),
        textAlign: 'center',
      }),
    }));
    features.push(textFeature);

    // Add arrow markers at arc ends
    features.push(
      this.createArrowFeature(arcPoints[0]!, angle1 + Math.PI / 2),
      this.createArrowFeature(arcPoints[arcPoints.length - 1]!, angle2 - Math.PI / 2)
    );

    features.forEach(feature => this.source.addFeature(feature));
  }

  // Start radius dimension
  startRadiusDimension(): void {
    this.stopCurrentDimension();

    this.currentDraw = new Draw({
      source: this.source,
      type: 'Circle',
      style: this.getTemporaryStyle(),
    });

    this.currentDraw.on('drawend', (event: DrawEvent) => {
      const feature = event.feature;
      const geometry = feature.getGeometry() as any; // Circle geometry
      const center = geometry.getCenter();
      const radius = geometry.getRadius();
      
      this.createRadiusDimension(center, radius);
      this.source.removeFeature(feature);
    });

    this.map.addInteraction(this.currentDraw);
  }

  // Create radius dimension
  private createRadiusDimension(center: Coordinate, radius: number): void {
    if (!center || center.length < 2) return;

    const features: Feature[] = [];
    
    // Calculate a point on the circle at 45 degrees
    const angle = Math.PI / 4;
    const endPoint: Coordinate = [
      center[0]! + Math.cos(angle) * radius,
      center[1]! + Math.sin(angle) * radius,
    ];

    // Create dimension line from center to edge
    const dimLine = new Feature(new LineString([center, endPoint]));
    dimLine.setStyle(new Style({
      stroke: new Stroke({
        color: this.style.lineColor,
        width: this.style.lineWidth,
      }),
    }));
    features.push(dimLine);

    // Add center mark
    const centerMark = this.createCenterMark(center);
    features.push(centerMark);

    // Add arrow at radius end
    features.push(this.createArrowFeature(endPoint, angle + Math.PI));

    // Add radius text
    const radiusText = `R${this.formatDimensionText(radius)}`;
    const textPos: Coordinate = [
      center[0]! + Math.cos(angle) * radius * 0.5,
      center[1]! + Math.sin(angle) * radius * 0.5,
    ];

    const textFeature = new Feature(new Point(textPos));
    textFeature.setStyle(new Style({
      text: new Text({
        text: radiusText,
        font: `${this.style.textHeight}px Arial`,
        fill: new Fill({ color: this.style.textColor }),
        stroke: new Stroke({ color: '#ffffff', width: 3 }),
        textAlign: 'left',
        offsetX: 5,
        rotation: angle,
      }),
    }));
    features.push(textFeature);

    features.forEach(feature => this.source.addFeature(feature));
  }

  // Start diameter dimension
  startDiameterDimension(): void {
    this.stopCurrentDimension();

    this.currentDraw = new Draw({
      source: this.source,
      type: 'Circle',
      style: this.getTemporaryStyle(),
    });

    this.currentDraw.on('drawend', (event: DrawEvent) => {
      const feature = event.feature;
      const geometry = feature.getGeometry() as any;
      const center = geometry.getCenter();
      const radius = geometry.getRadius();
      
      this.createDiameterDimension(center, radius);
      this.source.removeFeature(feature);
    });

    this.map.addInteraction(this.currentDraw);
  }

  // Create diameter dimension
  private createDiameterDimension(center: Coordinate, radius: number): void {
    if (!center || center.length < 2) return;

    const features: Feature[] = [];
    
    // Create diameter line at 45 degrees
    const angle = Math.PI / 4;
    const p1: Coordinate = [
      center[0]! - Math.cos(angle) * radius,
      center[1]! - Math.sin(angle) * radius,
    ];
    const p2: Coordinate = [
      center[0]! + Math.cos(angle) * radius,
      center[1]! + Math.sin(angle) * radius,
    ];

    // Create dimension line
    const dimLine = new Feature(new LineString([p1, p2]));
    dimLine.setStyle(new Style({
      stroke: new Stroke({
        color: this.style.lineColor,
        width: this.style.lineWidth,
      }),
    }));
    features.push(dimLine);

    // Add arrows at both ends
    features.push(
      this.createArrowFeature(p1, angle),
      this.createArrowFeature(p2, angle + Math.PI)
    );

    // Add diameter text
    const diameterText = `Ø${this.formatDimensionText(radius * 2)}`;
    const textFeature = new Feature(new Point(center));
    textFeature.setStyle(new Style({
      text: new Text({
        text: diameterText,
        font: `${this.style.textHeight}px Arial`,
        fill: new Fill({ color: this.style.textColor }),
        stroke: new Stroke({ color: '#ffffff', width: 3 }),
        textAlign: 'center',
        offsetY: -this.style.textHeight,
        rotation: angle,
      }),
    }));
    features.push(textFeature);

    features.forEach(feature => this.source.addFeature(feature));
  }

  // Start leader line with text
  startLeaderDimension(defaultText: string = 'NOTE'): void {
    this.stopCurrentDimension();

    let points: Coordinate[] = [];

    this.currentDraw = new Draw({
      source: this.source,
      type: 'LineString',
      style: this.getTemporaryStyle(),
    });

    this.currentDraw.on('drawend', (event: DrawEvent) => {
      const feature = event.feature;
      const geometry = feature.getGeometry() as LineString;
      const coords = geometry.getCoordinates();

      if (coords.length >= 2) {
        // Prompt for text
        const text = prompt('Enter leader text:', defaultText) || defaultText;
        this.createLeaderDimension(coords, text);
        this.source.removeFeature(feature);
      }
    });

    this.map.addInteraction(this.currentDraw);
  }

  // Create leader dimension
  private createLeaderDimension(points: Coordinate[], text: string): void {
    if (points.length < 2) return;

    const features: Feature[] = [];

    // Create leader line
    const leaderLine = new Feature(new LineString(points));
    leaderLine.setStyle(new Style({
      stroke: new Stroke({
        color: this.style.lineColor,
        width: this.style.lineWidth,
      }),
    }));
    features.push(leaderLine);

    // Add arrow at first point
    const firstPoint = points[0];
    const secondPoint = points[1];
    if (firstPoint && secondPoint && 
        firstPoint.length >= 2 && secondPoint.length >= 2) {
      const angle = Math.atan2(
        firstPoint[1]! - secondPoint[1]!, 
        firstPoint[0]! - secondPoint[0]!
      );
      features.push(this.createArrowFeature(firstPoint, angle));
    }

    // Add text at last point
    const lastPoint = points[points.length - 1];
    if (lastPoint && lastPoint.length >= 2) {
      const textFeature = new Feature(new Point(lastPoint));
      
      // Determine text alignment based on leader direction
      const prevPoint = points[points.length - 2];
      let textAlign = 'left';
      if (prevPoint && prevPoint.length >= 2 && lastPoint[0]! < prevPoint[0]!) {
        textAlign = 'right';
      }

      textFeature.setStyle(new Style({
        text: new Text({
          text: text,
          font: `${this.style.textHeight}px Arial`,
          fill: new Fill({ color: this.style.textColor }),
          stroke: new Stroke({ color: '#ffffff', width: 3 }),
          textAlign: textAlign as any,
          offsetX: textAlign === 'left' ? 5 : -5,
        }),
      }));
      features.push(textFeature);
    }

    features.forEach(feature => this.source.addFeature(feature));
  }

  // Helper method to create line feature
  private createLineFeature(coords: Coordinate[], type: string): Feature {
    const feature = new Feature(new LineString(coords));
    const strokeStyle = type === 'extension' 
      ? { width: this.style.lineWidth * 0.5, lineDash: [2, 2] }
      : { width: this.style.lineWidth };

    feature.setStyle(new Style({
      stroke: new Stroke({
        color: this.style.lineColor,
        ...strokeStyle,
      }),
    }));
    
    feature.set('dimensionType', type);
    return feature;
  }

  // Create dimension line with text
  private createDimensionLineFeature(p1: Coordinate, p2: Coordinate, text: string): Feature {
    const feature = new Feature(new LineString([p1, p2]));
    
    // Calculate text position and rotation
    const midPoint: Coordinate = [
      (p1[0]! + p2[0]!) / 2,
      (p1[1]! + p2[1]!) / 2,
    ];
    
    const angle = Math.atan2(p2[1]! - p1[1]!, p2[0]! - p1[0]!);
    let textRotation = angle;
    
    // Keep text upright
    if (textRotation > Math.PI / 2 || textRotation < -Math.PI / 2) {
      textRotation += Math.PI;
    }

    feature.setStyle([
      new Style({
        stroke: new Stroke({
          color: this.style.lineColor,
          width: this.style.lineWidth,
        }),
      }),
      new Style({
        geometry: new Point(midPoint),
        text: new Text({
          text: text,
          font: `${this.style.textHeight}px Arial`,
          fill: new Fill({ color: this.style.textColor }),
          stroke: new Stroke({ color: '#ffffff', width: 3 }),
          textAlign: 'center',
          textBaseline: 'bottom',
          offsetY: -3,
          rotation: textRotation,
        }),
      }),
    ]);

    feature.set('dimensionType', 'dimension-line');
    return feature;
  }

  // Create arrow feature
  private createArrowFeature(position: Coordinate, angle: number): Feature {
    let feature: Feature;
    
    if (this.style.arrowStyle === 'filled') {
      feature = new Feature(new Point(position));
      feature.setStyle(new Style({
        image: new RegularShape({
          points: 3,
          radius: this.style.arrowSize,
          angle: angle,
          fill: new Fill({ color: this.style.lineColor }),
          stroke: new Stroke({ color: this.style.lineColor, width: 1 }),
        }),
      }));
    } else if (this.style.arrowStyle === 'open') {
      // Create open arrow using lines
      const tipX = position[0]!;
      const tipY = position[1]!;
      const arrowAngle = Math.PI / 6; // 30 degrees
      
      const p1: Coordinate = [
        tipX + Math.cos(angle + arrowAngle) * this.style.arrowSize,
        tipY + Math.sin(angle + arrowAngle) * this.style.arrowSize,
      ];
      const p2: Coordinate = [
        tipX + Math.cos(angle - arrowAngle) * this.style.arrowSize,
        tipY + Math.sin(angle - arrowAngle) * this.style.arrowSize,
      ];

      const arrowGeometry = new LineString([p1, position as Coordinate, p2]);
      feature = new Feature(arrowGeometry);
      feature.setStyle(new Style({
        stroke: new Stroke({
          color: this.style.lineColor,
          width: this.style.lineWidth,
        }),
      }));
    } else if (this.style.arrowStyle === 'dot') {
      feature = new Feature(new Point(position));
      feature.setStyle(new Style({
        image: new CircleStyle({
          radius: this.style.arrowSize / 2,
          fill: new Fill({ color: this.style.lineColor }),
        }),
      }));
    } else if (this.style.arrowStyle === 'architectural') {
      // Architectural tick mark
      const tickLength = this.style.arrowSize;
      const perpAngle = angle + Math.PI / 2;
      
      const p1: Coordinate = [
        position[0]! + Math.cos(perpAngle) * tickLength / 2,
        position[1]! + Math.sin(perpAngle) * tickLength / 2,
      ];
      const p2: Coordinate = [
        position[0]! - Math.cos(perpAngle) * tickLength / 2,
        position[1]! - Math.sin(perpAngle) * tickLength / 2,
      ];

      const archLines = new LineString([p1, p2]);
      feature = new Feature(archLines);
      feature.setStyle(new Style({
        stroke: new Stroke({
          color: this.style.lineColor,
          width: this.style.lineWidth * 2,
        }),
      }));
    } else {
      // Default fallback
      feature = new Feature(new Point(position));
    }

    feature.set('dimensionType', 'arrow');
    return feature;
  }

  // Create center mark for radius/diameter dimensions
  private createCenterMark(center: Coordinate): Feature {
    const size = this.style.arrowSize;
    const lines: Coordinate[][] = [
      [[center[0]! - size, center[1]!], [center[0]! + size, center[1]!]],
      [[center[0]!, center[1]! - size], [center[0]!, center[1]! + size]],
    ];

    const feature = new Feature();
    const geometries = lines.map(line => new LineString(line));
    
    feature.setStyle(geometries.map(geom => new Style({
      geometry: geom,
      stroke: new Stroke({
        color: this.style.lineColor,
        width: this.style.lineWidth,
      }),
    })));

    feature.set('dimensionType', 'center-mark');
    return feature;
  }

  // Create arc points
  private createArcPoints(
    center: Coordinate, 
    radius: number, 
    startAngle: number, 
    endAngle: number, 
    segments: number
  ): Coordinate[] {
    const points: Coordinate[] = [];
    let angleDiff = endAngle - startAngle;
    
    // Normalize angle difference
    if (angleDiff < 0) angleDiff += 2 * Math.PI;
    
    for (let i = 0; i <= segments; i++) {
      const angle = startAngle + (angleDiff * i / segments);
      points.push([
        center[0]! + Math.cos(angle) * radius,
        center[1]! + Math.sin(angle) * radius,
      ]);
    }
    
    return points;
  }

  // Calculate distance between two points
  private calculateDistance(p1: Coordinate, p2: Coordinate): number {
    const line = new LineString([p1, p2]);
    return getLength(line, { projection: this.map.getView().getProjection() });
  }

  // Format dimension text with units
  private formatDimensionText(value: number): string {
    // Convert to desired unit
    let convertedValue = value;
    
    switch (this.style.unit) {
      case 'ft':
        convertedValue = value * 3.28084; // meters to feet
        break;
      case 'in':
        convertedValue = value * 39.3701; // meters to inches
        break;
      case 'mm':
        convertedValue = value * 1000; // meters to millimeters
        break;
      // 'm' is default (no conversion needed)
    }

    const formatted = convertedValue.toFixed(this.style.decimalPlaces);
    return this.style.showUnit ? `${formatted} ${this.style.unit}` : formatted;
  }

  // Get temporary drawing style
  private getTemporaryStyle(): any {
    return new Style({
      stroke: new Stroke({
        color: 'rgba(0, 102, 255, 0.5)',
        width: 2,
        lineDash: [5, 5],
      }),
      image: new CircleStyle({
        radius: 5,
        fill: new Fill({ color: 'rgba(0, 102, 255, 0.3)' }),
        stroke: new Stroke({ color: '#0066ff', width: 2 }),
      }),
    });
  }

  // Stop current dimension operation
  stopCurrentDimension(): void {
    if (this.currentDraw) {
      this.map.removeInteraction(this.currentDraw);
      this.currentDraw = null;
    }
    
    // Clear any temporary features
    this.temporaryFeatures.forEach(feature => {
      this.source.removeFeature(feature);
    });
    this.temporaryFeatures = [];
  }

  // Update dimension style
  updateStyle(style: Partial<DimensionStyle>): void {
    this.style = { ...this.style, ...style };
  }

  // Clear all dimensions
  clearAllDimensions(): void {
    const features = this.source.getFeatures();
    const dimensionFeatures = features.filter(f => 
      f.get('dimensionType') !== undefined
    );
    
    dimensionFeatures.forEach(feature => {
      this.source.removeFeature(feature);
    });
  }

  // Export dimensions to DXF format (returns DXF string content)
  exportToDXF(): string {
    const features = this.source.getFeatures();
    const dimensionFeatures = features.filter(f => 
      f.get('dimensionType') !== undefined
    );

    let dxfContent = '0\nSECTION\n2\nENTITIES\n';

    dimensionFeatures.forEach(feature => {
      const geometry = feature.getGeometry();
      
      if (geometry instanceof LineString) {
        const coords = geometry.getCoordinates();
        dxfContent += this.lineToDXF(coords);
      } else if (geometry instanceof Point) {
        const coord = geometry.getCoordinates();
        // Add text entities for dimension text
        const style = feature.getStyle();
        if (Array.isArray(style)) {
          style.forEach(s => {
            const text = s.getText();
            if (text) {
              const textValue = text.getText() || '';
              const textString = Array.isArray(textValue) ? textValue.join(' ') : String(textValue ?? '');
              dxfContent += this.textToDXF(coord, textString);
            }
          });
        }
      }
    });

    dxfContent += '0\nENDSEC\n0\nEOF\n';
    return dxfContent;
  }

  // Convert line to DXF format
  private lineToDXF(coords: Coordinate[]): string {
    let dxf = '';
    for (let i = 0; i < coords.length - 1; i++) {
      dxf += '0\nLINE\n';
      dxf += '8\nDIMENSIONS\n'; // Layer name
      dxf += `10\n${coords[i]![0]}\n`; // Start X
      dxf += `20\n${coords[i]![1]}\n`; // Start Y
      dxf += `11\n${coords[i + 1]![0]}\n`; // End X
      dxf += `21\n${coords[i + 1]![1]}\n`; // End Y
    }
    return dxf;
  }

  // Convert text to DXF format
  private textToDXF(coord: Coordinate, text: string): string {
    let dxf = '0\nTEXT\n';
    dxf += '8\nDIMENSIONS\n'; // Layer name
    dxf += `10\n${coord[0]}\n`; // X
    dxf += `20\n${coord[1]}\n`; // Y
    dxf += `40\n${this.style.textHeight}\n`; // Height
    dxf += `1\n${text}\n`; // Text string
    return dxf;
  }
}

// Dimension preset styles for different use cases
export const DIMENSION_PRESETS = {
  architectural: {
    arrowStyle: 'architectural' as const,
    unit: 'ft' as const,
    decimalPlaces: 2,
    textHeight: 12,
  },
  engineering: {
    arrowStyle: 'filled' as const,
    unit: 'ft' as const,
    decimalPlaces: 3,
    textHeight: 14,
  },
  metric: {
    arrowStyle: 'filled' as const,
    unit: 'm' as const,
    decimalPlaces: 3,
    textHeight: 14,
  },
  survey: {
    arrowStyle: 'open' as const,
    unit: 'ft' as const,
    decimalPlaces: 2,
    textHeight: 10,
  },
};

export default DimensionTools;