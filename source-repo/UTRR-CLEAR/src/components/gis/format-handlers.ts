import { Feature } from 'ol';
import { Geometry, LineString, Circle, Point } from 'ol/geom.js';
import GeoJSON from 'ol/format/GeoJSON.js';
import WKT from 'ol/format/WKT.js';
import KML from 'ol/format/KML.js';
import GML3 from 'ol/format/GML3.js';
import { Vector as VectorSource } from 'ol/source.js';
import { transform } from 'ol/proj.js';
import { safeLog } from '~/lib/error-handler';

interface DXFVertex {
  x: number;
  y: number;
}

// DXF format handler (requires dxf-parser library)
export class DXFHandler {
  // Parse DXF content to features
  static async parseDXF(content: string): Promise<Feature[]> {
    // This would require the dxf-parser library
    // npm install dxf-parser
    try {
      // @ts-ignore - dxf-parser types might not be available
      const DxfParser = (await import('dxf-parser')).default;
      const parser = new DxfParser();
      const dxf = parser.parse(content);
      
      const features: Feature[] = [];
      
      // Convert DXF entities to OpenLayers features
      if (dxf && dxf.entities) {
        dxf.entities.forEach((entity: any) => {
          const feature = this.convertDXFEntity(entity);
          if (feature) features.push(feature);
        });
      }
      
      return features;
    } catch (error) {
      safeLog.error('DXF parsing error:', { error: String(error) });
      return [];
    }
  }

  private static convertDXFEntity(entity: any): Feature | null {
    // Convert DXF entity types to OpenLayers geometries
    switch (entity.type) {
      case 'LINE':
        return this.createLineFeature(entity);
      case 'POLYLINE':
      case 'LWPOLYLINE':
        return this.createPolylineFeature(entity);
      case 'CIRCLE':
        return this.createCircleFeature(entity);
      case 'POINT':
        return this.createPointFeature(entity);
      case 'TEXT':
      case 'MTEXT':
        return this.createTextFeature(entity);
      default:
        return null;
    }
  }

  private static createLineFeature(entity: any): Feature {
    const coordinates = [
      [entity.vertices[0].x, entity.vertices[0].y],
      [entity.vertices[1].x, entity.vertices[1].y],
    ];
    
    const feature = new Feature({
      geometry: new LineString(coordinates),
    });
    
    feature.setProperties({
      layer: entity.layer,
      color: entity.color,
      lineType: entity.lineType,
      entityType: 'LINE',
    });
    
    return feature;
  }

  private static createPolylineFeature(entity: any): Feature {
    const coordinates = entity.vertices.map((v: DXFVertex) => [v.x, v.y]);
    
    const feature = new Feature({
      geometry: new LineString(coordinates),
    });
    
    feature.setProperties({
      layer: entity.layer,
      color: entity.color,
      lineType: entity.lineType,
      entityType: 'POLYLINE',
      closed: entity.closed,
    });
    
    return feature;
  }

  private static createCircleFeature(entity: any): Feature {
    const center = [entity.center.x, entity.center.y];
    
    const feature = new Feature({
      geometry: new Circle(center, entity.radius),
    });
    
    feature.setProperties({
      layer: entity.layer,
      color: entity.color,
      entityType: 'CIRCLE',
    });
    
    return feature;
  }

  private static createPointFeature(entity: any): Feature {
    const coordinate = [entity.position.x, entity.position.y];
    
    const feature = new Feature({
      geometry: new Point(coordinate),
    });
    
    feature.setProperties({
      layer: entity.layer,
      color: entity.color,
      entityType: 'POINT',
    });
    
    return feature;
  }

  private static createTextFeature(entity: any): Feature {
    const coordinate = [entity.position.x, entity.position.y];
    
    const feature = new Feature({
      geometry: new Point(coordinate),
    });
    
    feature.setProperties({
      layer: entity.layer,
      color: entity.color,
      text: entity.text,
      height: entity.height,
      rotation: entity.rotation,
      entityType: 'TEXT',
    });
    
    return feature;
  }

  // Export features to DXF format
  static exportToDXF(features: Feature[]): string {
    // This would require a DXF writer library or custom implementation
    let dxfContent = '0\nSECTION\n2\nENTITIES\n';
    
    features.forEach((feature) => {
      const geometry = feature.getGeometry();
      const props = feature.getProperties();
      
      if (geometry?.getType() === 'LineString') {
        dxfContent += this.lineStringToDXF(geometry as any, props);
      } else if (geometry?.getType() === 'Point') {
        dxfContent += this.pointToDXF(geometry as any, props);
      } else if (geometry?.getType() === 'Circle') {
        dxfContent += this.circleToDXF(geometry as any, props);
      }
    });
    
    dxfContent += '0\nENDSEC\n0\nEOF\n';
    return dxfContent;
  }

  private static lineStringToDXF(geometry: any, props: any): string {
    const coords = geometry.getCoordinates();
    let dxf = '';
    
    for (let i = 0; i < coords.length - 1; i++) {
      dxf += '0\nLINE\n';
      dxf += `8\n${props.layer || '0'}\n`; // Layer
      dxf += `10\n${coords[i][0]}\n20\n${coords[i][1]}\n30\n0.0\n`; // Start point
      dxf += `11\n${coords[i + 1][0]}\n21\n${coords[i + 1][1]}\n31\n0.0\n`; // End point
    }
    
    return dxf;
  }

  private static pointToDXF(geometry: any, props: any): string {
    const coord = geometry.getCoordinates();
    let dxf = '0\nPOINT\n';
    dxf += `8\n${props.layer || '0'}\n`; // Layer
    dxf += `10\n${coord[0]}\n20\n${coord[1]}\n30\n0.0\n`; // Position
    return dxf;
  }

  private static circleToDXF(geometry: any, props: any): string {
    const center = geometry.getCenter();
    const radius = geometry.getRadius();
    let dxf = '0\nCIRCLE\n';
    dxf += `8\n${props.layer || '0'}\n`; // Layer
    dxf += `10\n${center[0]}\n20\n${center[1]}\n30\n0.0\n`; // Center
    dxf += `40\n${radius}\n`; // Radius
    return dxf;
  }
}

// Shapefile handler (requires shapefile.js)
export class ShapefileHandler {
  static async parseShapefile(shpBuffer: ArrayBuffer, dbfBuffer: ArrayBuffer): Promise<Feature[]> {
    try {
      // Check if shapefile is available
      let shapefile;
      try {
        shapefile = await import('shapefile');
      } catch (importError) {
        safeLog.warn('Shapefile library not available:', { error: String(importError) } as Record<string, unknown>);
        return [];
      }
      const features: Feature[] = [];
      
      const source = await shapefile.open(shpBuffer, dbfBuffer);
      let result = await source.read();
      
      while (!result.done) {
        if (result.value) {
          const geoJSON = new GeoJSON();
          if (Array.isArray(result.value)) {
            const readFeatures = geoJSON.readFeatures(result.value);
            features.push(...readFeatures);
          } else {
            const readResult = geoJSON.readFeature(result.value);
            if (Array.isArray(readResult)) {
              features.push(...readResult);
            } else {
              features.push(readResult);
            }
          }
        }
        result = await source.read();
      }
      
      return features;
    } catch (error) {
      safeLog.error('Shapefile parsing error:', { error: String(error) });
      return [];
    }
  }
}

// Enhanced format manager with all formats
export class FormatManager {
  private static formats = {
    geojson: new GeoJSON(),
    wkt: new WKT(),
    kml: new KML(),
    gml: new GML3(),
  };

  // Universal import function
  static async importFeatures(
    content: string | ArrayBuffer,
    format: 'geojson' | 'wkt' | 'kml' | 'gml' | 'dxf' | 'shp',
    options?: any
  ): Promise<Feature[]> {
    try {
      switch (format) {
        case 'dxf':
          return await DXFHandler.parseDXF(content as string);
        
        case 'shp':
          // Shapefile requires both .shp and .dbf files
          throw new Error('Shapefile import requires both .shp and .dbf files');
        
        case 'geojson':
        case 'wkt':
        case 'kml':
        case 'gml':
          const formatter = this.formats[format];
          const features = formatter.readFeatures(content, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:3857',
            ...options,
          });
          return features;
        
        default:
          throw new Error(`Unsupported format: ${format}`);
      }
    } catch (error) {
      safeLog.error(`Error importing ${format}:`, { error: String(error) });
      throw error;
    }
  }

  // Universal export function
  static exportFeatures(
    features: Feature[],
    format: 'geojson' | 'wkt' | 'kml' | 'gml' | 'dxf',
    options?: any
  ): string {
    try {
      switch (format) {
        case 'dxf':
          return DXFHandler.exportToDXF(features);
        
        case 'geojson':
        case 'wkt':
        case 'kml':
        case 'gml':
          const formatter = this.formats[format];
          return formatter.writeFeatures(features, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:3857',
            ...options,
          });
        
        default:
          throw new Error(`Unsupported format: ${format}`);
      }
    } catch (error) {
      safeLog.error(`Error exporting ${format}:`, { error: String(error) });
      throw error;
    }
  }

  // Create download link for exported data
  static downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  }

  // Batch import from multiple files
  static async importMultipleFiles(
    files: { content: string | ArrayBuffer; format: string; name: string }[]
  ): Promise<Feature[]> {
    const allFeatures: Feature[] = [];
    
    for (const file of files) {
      try {
        const features = await this.importFeatures(
          file.content,
          file.format as any
        );
        
        // Add source file info to features
        features.forEach(feature => {
          feature.set('sourceFile', file.name);
          feature.set('sourceFormat', file.format);
        });
        
        allFeatures.push(...features);
      } catch (error) {
        safeLog.error(`Error importing ${file.name}:`, { error: String(error) });
      }
    }
    
    return allFeatures;
  }
}

// Coordinate transformation utilities
export class CoordinateTransformUtil {
  // Common projections for civil engineering
  static readonly PROJECTIONS = {
    'WGS84': 'EPSG:4326',
    'Web Mercator': 'EPSG:3857',
    'NAD83 / Indiana East': 'EPSG:26973',
    'NAD83 / Indiana West': 'EPSG:26974',
    'State Plane Indiana East (US ft)': 'EPSG:2965',
    'State Plane Indiana West (US ft)': 'EPSG:2966',
  };

  // Transform features between projections
  static transformFeatures(
    features: Feature[],
    sourceProj: string,
    targetProj: string
  ): Feature[] {
    return features.map(feature => {
      const clone = feature.clone();
      const geometry = clone.getGeometry();
      
      if (geometry) {
        geometry.transform(sourceProj, targetProj);
      }
      
      return clone;
    });
  }

  // Convert coordinates between projection systems
  static convertCoordinate(
    coordinate: number[],
    sourceProj: string,
    targetProj: string
  ): number[] {
    return transform(coordinate, sourceProj, targetProj);
  }

  // Batch coordinate conversion
  static convertCoordinates(
    coordinates: number[][],
    sourceProj: string,
    targetProj: string
  ): number[][] {
    return coordinates.map(coord => 
      this.convertCoordinate(coord, sourceProj, targetProj)
    );
  }
}