import { LineString, Point } from 'ol/geom.js';
import { Feature } from 'ol';
import { Style, Stroke, Text, Fill, Circle as CircleStyle } from 'ol/style.js';
import { UTILITY_TYPES, LINE_STYLES, type UtilityType, type UtilityProperties } from './utility-types';

// Function to create CAD-style line with utility letters
export function createCADLineStyle(feature: Feature, resolution: number): Style[] {
  const geometry = feature.getGeometry() as LineString;
  if (!geometry) return [];

  const properties = feature.getProperties() as UtilityProperties;
  const utilityType = UTILITY_TYPES[properties.type];
  const lineStyle = getLineStyle(properties);
  
  const styles: Style[] = [];
  
  // Determine color based on state
  let strokeColor = utilityType?.color || '#868e96';
  if (properties.toBeAbandoned) {
    strokeColor = '#868e96'; // grey for abandoned - using valid color from type
  }

  // Create base line style
  const baseStyle = new Style({
    stroke: new Stroke({
      color: strokeColor,
      width: lineStyle?.width ?? 2,
      lineDash: lineStyle?.pattern ?? [],
      lineCap: 'round',
      lineJoin: 'round',
    }),
  });
  styles.push(baseStyle);

  // Add X pattern for abandoned utilities
  if (properties.toBeAbandoned) {
    styles.push(...createAbandonedPattern(geometry, resolution));
  }

  // Add utility letter labels along the line
  if (!properties.toBeAbandoned && utilityType?.code) {
    styles.push(...createUtilityLabels(geometry, utilityType.code, resolution, strokeColor));
  }

  // Add aerial indicator if needed
  if (properties.aerial) {
    styles.push(...createAerialIndicators(geometry, resolution, strokeColor));
  }

  return styles;
}

function getLineStyle(properties: UtilityProperties) {
  if (properties.toBeAbandoned) {
    return LINE_STYLES.abandoned;
  } else if (properties.aerial) {
    return LINE_STYLES.solidDashedAerial;
  }
  return LINE_STYLES.solid;
}

// Create X pattern overlay for abandoned utilities
function createAbandonedPattern(geometry: LineString, resolution: number): Style[] {
  const styles: Style[] = [];
  const coordinates = geometry.getCoordinates();
  const length = geometry.getLength();
  const segmentLength = 50 * resolution; // X marks every 50 pixels
  
  for (let i = 0; i < length; i += segmentLength) {
    const point = geometry.getCoordinateAt(i / length);
    
    if (!point || point.length < 2) continue;
    
    const x = (point && point[0]) ?? 0;
    const y = (point && point[1]) ?? 0;
    
    // Create X mark
    styles.push(new Style({
      geometry: new LineString([
        [x - 5 * resolution, y - 5 * resolution],
        [x + 5 * resolution, y + 5 * resolution],
      ]),
      stroke: new Stroke({
        color: '#868e96',
        width: 2,
      }),
    }));
    
    styles.push(new Style({
      geometry: new LineString([
        [x - 5 * resolution, y + 5 * resolution],
        [x + 5 * resolution, y - 5 * resolution],
      ]),
      stroke: new Stroke({
        color: '#868e96',
        width: 2,
      }),
    }));
  }
  
  return styles;
}

// Create utility letter labels along the line
function createUtilityLabels(
  geometry: LineString,
  code: string,
  resolution: number,
  color: string
): Style[] {
  const styles: Style[] = [];
  const length = geometry.getLength();
  const labelSpacing = 200 * resolution; // Labels every 200 pixels
  
  for (let i = labelSpacing / 2; i < length; i += labelSpacing) {
    const point = geometry.getCoordinateAt(i / length);
    const nextPoint = geometry.getCoordinateAt(Math.min((i + 10) / length, 1));
    
    if (!point || !nextPoint || point.length < 2 || nextPoint.length < 2) continue;
    
    const x1 = point[0] ?? 0;
    const y1 = point[1] ?? 0;
    const x2 = nextPoint[0] ?? 0;
    const y2 = nextPoint[1] ?? 0;
    
    // Calculate rotation angle
    const dx = x2 - x1;
    const dy = y2 - y1;
    let rotation = Math.atan2(dy, dx);
    
    // Keep text upright
    if (rotation > Math.PI / 2 || rotation < -Math.PI / 2) {
      rotation += Math.PI;
    }
    
    styles.push(new Style({
      geometry: new Point(point),
      text: new Text({
        text: code,
        font: 'bold 12px Arial',
        fill: new Fill({ color: '#ffffff' }),
        stroke: new Stroke({ color, width: 3 }),
        rotation: rotation,
        offsetY: -10,
      }),
    }));
  }
  
  return styles;
}

// Create aerial line indicators (support poles/structures)
function createAerialIndicators(
  geometry: LineString,
  resolution: number,
  color: string
): Style[] {
  const styles: Style[] = [];
  const length = geometry.getLength();
  const poleSpacing = 300 * resolution; // Poles every 300 pixels
  
  for (let i = 0; i < length; i += poleSpacing) {
    const point = geometry.getCoordinateAt(i / length);
    
    if (!point || point.length < 2) continue;
    
    const x = (point && point[0]) ?? 0;
    const y = (point && point[1]) ?? 0;
    
    // Create pole symbol as a circle
    styles.push(new Style({
      geometry: new Point(point),
      image: new CircleStyle({
        radius: 6,
        fill: new Fill({ color: '#ffffff' }),
        stroke: new Stroke({ color, width: 2 }),
      }),
    }));
    
    // Add cross for pole
    const crossSize = (8 * resolution) || 8;
    styles.push(new Style({
      geometry: new LineString([
        [x - crossSize, y],
        [x + crossSize, y],
      ]),
      stroke: new Stroke({ color, width: 2 }),
    }));
    
    styles.push(new Style({
      geometry: new LineString([
        [point?.[0] ?? 0, (point?.[1] ?? 0) - crossSize],
        [point?.[0] ?? 0, (point?.[1] ?? 0) + crossSize],
      ]),
      stroke: new Stroke({ color, width: 2 }),
    }));
  }
  
  return styles;
}

// Create symbol styles for utility fixtures
export function createSymbolStyle(symbolType: string, properties: any): Style {
  const symbolConfig = {
    'telecom-pole': {
      image: new CircleStyle({
        radius: 8,
        fill: new Fill({ color: '#ffffff' }),
        stroke: new Stroke({ color: '#69db7c', width: 3 }),
      }),
    },
    'electric-pole': {
      image: new CircleStyle({
        radius: 10,
        fill: new Fill({ color: '#ffffff' }),
        stroke: new Stroke({ color: '#ff6b6b', width: 3 }),
      }),
    },
    'fiber-handhole': {
      // Rectangle represented as text box
      text: new Text({
        text: '□',
        font: 'bold 16px Arial',
        fill: new Fill({ color: '#4dabf7' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    },
    'water-valve': {
      text: new Text({
        text: '◊',
        font: 'bold 14px Arial',
        fill: new Fill({ color: '#fab005' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    },
    'gas-valve': {
      text: new Text({
        text: '▲',
        font: 'bold 14px Arial',
        fill: new Fill({ color: '#fd7e14' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    },
    'storm-inlet': {
      text: new Text({
        text: '●',
        font: 'bold 16px Arial',
        fill: new Fill({ color: '#adb5bd' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    },
    'sewer-manhole': {
      image: new CircleStyle({
        radius: 12,
        fill: new Fill({ color: '#868e96' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    },
  };

  const config = symbolConfig[symbolType as keyof typeof symbolConfig];
  if (!config) {
    // Default symbol
    return new Style({
      image: new CircleStyle({
        radius: 6,
        fill: new Fill({ color: '#868e96' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    });
  }

  return new Style({
    ...('image' in config && config.image && { image: config.image }),
    ...('text' in config && config.text && { text: config.text }),
  });
}
