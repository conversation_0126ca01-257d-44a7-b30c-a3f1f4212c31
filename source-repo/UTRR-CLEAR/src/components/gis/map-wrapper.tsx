'use client';

import dynamic from 'next/dynamic';
import { Skeleton } from '~/components/ui/skeleton';

// Dynamically import the consolidated map components with no SSR
const UtilityMap2D = dynamic(() => import('./utility-map-2d'), {
  ssr: false,
  loading: () => <Skeleton className="h-full w-full" />,
});

const UtilityMap3D = dynamic(() => import('./utility-map-3d'), {
  ssr: false,
  loading: () => <Skeleton className="h-full w-full" />,
});

interface MapWrapperProps {
  projectId: string;
  view: '2d' | '3d';
}

export function MapWrapper({ projectId, view }: MapWrapperProps) {
  return view === '2d' ? (
    <UtilityMap2D projectId={projectId} />
  ) : (
    <UtilityMap3D projectId={projectId} />
  );
}