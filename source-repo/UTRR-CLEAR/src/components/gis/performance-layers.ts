import VectorTileLayer from 'ol/layer/VectorTile.js';
import VectorTileSource from 'ol/source/VectorTile.js';
import MVT from 'ol/format/MVT.js';
import { createXYZ } from 'ol/tilegrid.js';
import Cluster from 'ol/source/Cluster.js';
import VectorSource from 'ol/source/Vector.js';
import VectorLayer from 'ol/layer/Vector.js';
import WebGLPointsLayer from 'ol/layer/WebGLPoints.js';
import { Style, Circle as CircleStyle, Fill, Stroke, Text } from 'ol/style.js';
import { Feature } from 'ol';
import type { FeatureLike } from 'ol/Feature.js';
import { Point } from 'ol/geom.js';
import { bbox as bboxStrategy } from 'ol/loadingstrategy.js';
import { GeoJSON } from 'ol/format.js';
import { safeLog } from '~/lib/error-handler';

// Vector Tile Layer for large utility datasets
export class UtilityVectorTileLayer {
  static create(url: string, styleFunction?: any): VectorTileLayer {
    const source = new VectorTileSource({
      format: new MVT({
        // Specify which layers to render from MVT
        layers: ['utilities', 'symbols', 'labels'],
      }),
      url: url,
      tileGrid: createXYZ({
        maxZoom: 22, // High zoom for detailed utility data
        tileSize: 512, // Larger tiles for better performance
      }),
    });

    return new VectorTileLayer({
      source: source,
      style: styleFunction || this.defaultStyleFunction,
      declutter: true, // Prevent overlapping labels
      renderMode: 'hybrid', // Best performance with interaction
      renderBuffer: 100, // Pixels around tile to render
      updateWhileAnimating: true,
      updateWhileInteracting: true,
    });
  }

  private static defaultStyleFunction(feature: any, resolution: number): Style | Style[] {
    const properties = feature.getProperties();
    const styles: Style[] = [];

    // Line styles based on utility type
    if (feature.getGeometry().getType() === 'LineString') {
      styles.push(new Style({
        stroke: new Stroke({
          color: properties.color || '#000000',
          width: Math.max(1, 3 / resolution),
          lineDash: properties.lineDash,
        }),
      }));

      // Add labels at appropriate zoom levels
      if (resolution < 10) {
        styles.push(new Style({
          text: new Text({
            text: properties.utilityCode,
            font: '12px Arial',
            fill: new Fill({ color: '#000000' }),
            stroke: new Stroke({ color: '#ffffff', width: 2 }),
            placement: 'line',
            textBaseline: 'bottom',
            offsetY: -5,
          }),
        }));
      }
    }

    // Point styles for symbols
    if (feature.getGeometry().getType() === 'Point') {
      styles.push(new Style({
        image: new CircleStyle({
          radius: 6,
          fill: new Fill({ color: properties.color || '#000000' }),
          stroke: new Stroke({ color: '#ffffff', width: 2 }),
        }),
      }));
    }

    return styles;
  }
}

// Clustering for dense symbol areas
export class SymbolClusterLayer {
  static create(source: VectorSource, distance: number = 40): VectorLayer {
    const clusterSource = new Cluster({
      source: source,
      distance: distance,
      minDistance: 20,
      geometryFunction: (feature) => {
        // Only cluster point features
        const geometry = feature.getGeometry();
        if (geometry?.getType() === 'Point') {
          return geometry as Point;
        }
        return null;
      },
    });

    return new VectorLayer({
      source: clusterSource,
      style: this.clusterStyleFunction,
      zIndex: 10, // Above other layers
    });
  }

  private static clusterStyleFunction(feature: FeatureLike): Style | Style[] {
    const size = feature.get('features').length;
    
    if (size === 1) {
      // Single feature - use its original style
      const originalFeature = feature.get('features')[0];
      const props = originalFeature.getProperties();
      
      return new Style({
        image: new CircleStyle({
          radius: 8,
          fill: new Fill({ color: props.color || '#000000' }),
          stroke: new Stroke({ color: '#ffffff', width: 2 }),
        }),
        text: new Text({
          text: props.symbolType || '',
          font: 'bold 10px Arial',
          fill: new Fill({ color: '#000000' }),
          offsetY: 20,
        }),
      });
    } else {
      // Cluster of features
      let radius = 10;
      let fontSize = '12px';
      
      if (size < 10) {
        radius = 15;
        fontSize = '12px';
      } else if (size < 50) {
        radius = 20;
        fontSize = '14px';
      } else {
        radius = 25;
        fontSize = '16px';
      }

      return new Style({
        image: new CircleStyle({
          radius: radius,
          fill: new Fill({ color: 'rgba(255, 153, 0, 0.8)' }),
          stroke: new Stroke({ color: 'rgba(255, 204, 0, 1)', width: 3 }),
        }),
        text: new Text({
          text: size.toString(),
          font: `bold ${fontSize} Arial`,
          fill: new Fill({ color: '#ffffff' }),
        }),
      });
    }
  }
}

// WebGL Points Layer for massive point datasets
export class WebGLUtilityPoints {
  static create(source: VectorSource): WebGLPointsLayer<VectorSource> {
    return new WebGLPointsLayer({
      source: source,
      style: {
        // WebGL style configuration
        'circle-radius': [
          'interpolate',
          ['exponential', 2],
          ['zoom'],
          10, 4,
          20, 20,
        ],
        'circle-fill-color': ['get', 'color'],
        'circle-opacity': 0.9,
      } as any,
      disableHitDetection: false,
    });
  }

  // Create WebGL layer with custom attributes
  static createAdvanced(source: VectorSource): WebGLPointsLayer<VectorSource> {
    return new WebGLPointsLayer({
      source: source,
      style: {
        // WebGL style configuration
        'circle-radius': [
          'case',
          ['==', ['get', 'type'], 'valve'], 12,
          ['==', ['get', 'type'], 'meter'], 10,
          ['==', ['get', 'type'], 'hydrant'], 14,
          8 // default
        ],
        'circle-fill-color': [
          'case',
          ['==', ['get', 'utilityType'], 'water'], '#4dabf7',
          ['==', ['get', 'utilityType'], 'gas'], '#fab005',
          ['==', ['get', 'utilityType'], 'electric'], '#ff6b6b',
          '#868e96' // default
        ],
        'circle-stroke-color': '#ffffff',
        'circle-stroke-width': 2,
      } as any,
    });
  }
}

// Optimized data loading strategies
export class DataLoadingStrategies {
  // Progressive loading based on zoom level
  static createProgressiveLoader(baseUrl: string): VectorSource {
    const source = new VectorSource({
      format: new GeoJSON(),
      strategy: bboxStrategy,
      loader: async (extent, resolution, projection, success, failure) => {
        // Determine detail level based on resolution
        let detailLevel = 'low';
        if (resolution < 5) {
          detailLevel = 'high';
        } else if (resolution < 20) {
          detailLevel = 'medium';
        }

        const url = `${baseUrl}?bbox=${extent.join(',')}&detail=${detailLevel}&projection=${projection.getCode()}`;

        try {
          const response = await fetch(url);
          const data = await response.json();
          const features = new GeoJSON().readFeatures(data, {
            featureProjection: projection,
          });
          
          source.addFeatures(features);
          success!(features);
        } catch (error) {
          failure!();
        }
      },
    });

    return source;
  }

  // Level-of-detail (LOD) loading
  static createLODSource(urls: { [key: number]: string }): VectorSource {
    const source = new VectorSource();
    let currentZoom = -1;

    const loadFeaturesForZoom = async (zoom: number) => {
      // Find appropriate URL for zoom level
      const zoomLevels = Object.keys(urls).map(Number).sort((a: any, b: any) => a - b);
      let selectedZoom = zoomLevels[0] ?? 0;
      
      for (const level of zoomLevels) {
        if (zoom >= level) {
          selectedZoom = level;
        } else {
          break;
        }
      }

      if (selectedZoom !== currentZoom) {
        currentZoom = selectedZoom;
        source.clear();

        try {
          const url = urls[selectedZoom];
          if (!url) {
            safeLog.warn(`No URL found for zoom level ${selectedZoom}`);
            return;
          }
          const response = await fetch(url);
          const data = await response.json();
          const features = new GeoJSON().readFeatures(data);
          source.addFeatures(features);
        } catch (error) {
          safeLog.error('Error loading LOD data:', { error: String(error) });
        }
      }
    };

    // Return source with zoom change handler
    (source as any).loadFeaturesForZoom = loadFeaturesForZoom;
    return source;
  }

  // Quadtree-based spatial indexing for efficient queries
  static createSpatialIndexedSource(features: Feature[]): VectorSource {
    const source = new VectorSource();
    
    // Build spatial index
    const spatialIndex = new Map<string, Feature[]>();
    const tileSize = 1000; // meters

    features.forEach(feature => {
      const geometry = feature.getGeometry();
      if (geometry) {
        const extent = geometry.getExtent();
        const tileX = Math.floor((extent[0] ?? 0) / tileSize);
        const tileY = Math.floor((extent[1] ?? 0) / tileSize);
        const tileKey = `${tileX},${tileY}`;

        if (!spatialIndex.has(tileKey)) {
          spatialIndex.set(tileKey, []);
        }
        spatialIndex.get(tileKey)!.push(feature);
      }
    });

    // Custom loader that uses spatial index
    (source as any).getFeaturesInExtent = (extent: number[]) => {
      const features: Feature[] = [];
      const minTileX = Math.floor((extent[0] ?? 0) / tileSize);
      const minTileY = Math.floor((extent[1] ?? 0) / tileSize);
      const maxTileX = Math.ceil((extent[2] ?? 0) / tileSize);
      const maxTileY = Math.ceil((extent[3] ?? 0) / tileSize);

      for (let x = minTileX; x <= maxTileX; x++) {
        for (let y = minTileY; y <= maxTileY; y++) {
          const tileKey = `${x},${y}`;
          const tileFeatures = spatialIndex.get(tileKey) || [];
          features.push(...tileFeatures);
        }
      }

      return features;
    };

    source.addFeatures(features);
    return source;
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static renderTimes: number[] = [];
  private static featureCounts: Map<string, number> = new Map();

  static startMonitoring(map: any) {
    let frameCount = 0;
    let lastTime = performance.now();

    map.on('postrender', () => {
      frameCount++;
      const currentTime = performance.now();
      const deltaTime = currentTime - lastTime;

      if (deltaTime > 1000) { // Every second
        const fps = (frameCount * 1000) / deltaTime;
        safeLog.info(`FPS: ${fps.toFixed(1)}`);
        
        // Monitor layer performance
        map.getLayers().forEach((layer: any) => {
          if (layer.getSource) {
            const source = layer.getSource();
            if (source.getFeatures) {
              const features = source.getFeatures();
              safeLog.info(`Layer ${layer.get('name') || 'unnamed'}: ${features.length} features`);
            }
          }
        });

        frameCount = 0;
        lastTime = currentTime;
      }
    });
  }

  static measureRenderTime(callback: () => void): number {
    const start = performance.now();
    callback();
    const end = performance.now();
    const renderTime = end - start;
    
    this.renderTimes.push(renderTime);
    if (this.renderTimes.length > 100) {
      this.renderTimes.shift();
    }

    return renderTime;
  }

  static getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    const sum = this.renderTimes.reduce((a: any, b: any) => a + b, 0);
    return sum / this.renderTimes.length;
  }

  static logPerformanceReport() {
    safeLog.info('=== Performance Report ===');
    safeLog.info(`Average render time: ${this.getAverageRenderTime().toFixed(2)}ms`);
    
    this.featureCounts.forEach((count, layerName) => {
      safeLog.info(`${layerName}: ${count} features`);
    });
  }
}