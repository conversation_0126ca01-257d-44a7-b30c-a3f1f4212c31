'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Canvas, useFrame, useThree, extend } from '@react-three/fiber';
import { 
  OrbitControls, 
  PerspectiveCamera,
  Line,
  Text,
  Box,
  Cylinder,
  Sphere,
  Cone,
  Torus,
  Plane,
  Grid,
  GizmoHelper,
  GizmoViewport,
  Environment,
  ContactShadows,
  Sky,
  PerformanceMonitor,
  Stats,
  Html,
  Billboard,
  Edges,
  useTexture,
  MeshReflectorMaterial,
  Float,
  SpotLight,
  useDepthBuffer
} from '@react-three/drei';
import * as THREE from 'three';
import { EffectComposer, Outline, SSAO, Bloom, DepthOfField } from '@react-three/postprocessing';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Slider } from '~/components/ui/slider';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import { Select as UISelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { 
  Eye, EyeOff, Sun, Moon, Camera, Layers, Settings, 
  Download, Upload, Maximize2, Grid3x3, Box as BoxIcon,
  Lightbulb, Ruler, Navigation, Home
} from 'lucide-react';
import { safeLog } from '~/lib/error-handler';
import { UTILITY_TYPES, type UtilityType, type UtilityProperties } from './utility-types';

interface Utility {
  id: string;
  type: string;
  properties: UtilityProperties;
  geometry?: any;
  points?: any[];
  position?: any;
  symbolType?: string;
}

// Custom shaders for utility visualization
const utilityVertexShader = `
  uniform float time;
  uniform float flowSpeed;
  varying vec2 vUv;
  varying vec3 vPosition;
  
  void main() {
    vUv = uv;
    vPosition = position;
    
    // Add subtle animation for active utilities
    vec3 pos = position;
    pos.y += sin(position.x * 0.1 + time * flowSpeed) * 0.05;
    
    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
  }
`;

const utilityFragmentShader = `
  uniform vec3 color;
  uniform float time;
  uniform float flowSpeed;
  uniform bool isActive;
  uniform bool hasFlow;
  varying vec2 vUv;
  varying vec3 vPosition;
  
  void main() {
    vec3 finalColor = color;
    
    // Flow animation for active utilities
    if (isActive && hasFlow) {
      float flow = mod(vUv.x * 10.0 - time * flowSpeed, 1.0);
      finalColor = mix(color, vec3(1.0), smoothstep(0.0, 0.1, flow) * smoothstep(0.3, 0.2, flow) * 0.5);
    }
    
    // Depth fade
    float depth = smoothstep(-20.0, 0.0, vPosition.y);
    finalColor *= depth;
    
    gl_FragColor = vec4(finalColor, 1.0);
  }
`;

// Enhanced utility line component with flow animation
function EnhancedUtilityLine({ 
  points, 
  properties,
  selected = false,
  showFlow = true 
}: { 
  points: number[][], 
  properties: UtilityProperties,
  selected?: boolean,
  showFlow?: boolean
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  const materialRef = useRef<THREE.ShaderMaterial>(null);
  
  const linePoints = useMemo(() => 
    points.map(p => new THREE.Vector3((p[0] ?? 0) / 1000, -properties.depth, (p[2] ?? 0) / 1000)),
    [points, properties.depth]
  );
  
  const utilityType = UTILITY_TYPES[properties.type];
  const color = new THREE.Color(utilityType.color);
  
  // Create tube geometry for pipes
  const curve = useMemo(() => {
    const curve = new THREE.CatmullRomCurve3(linePoints);
    return curve;
  }, [linePoints]);
  
  const tubeGeometry = useMemo(() => {
    const radius = (properties.size || 4) / 100; // Convert inches to visual units
    return new THREE.TubeGeometry(curve, 64, radius, 8, false);
  }, [curve, properties.size]);
  
  // Animate flow for active utilities
  useFrame((state) => {
    if (materialRef.current?.uniforms?.time && showFlow && !properties.toBeAbandoned) {
      materialRef.current.uniforms.time.value = state.clock.elapsedTime;
    }
  });
  
  const isFlowUtility = ['water', 'gas', 'sewer', 'steam'].includes(properties.type);
  
  return (
    <group>
      {/* Main utility pipe */}
      <mesh ref={meshRef} geometry={tubeGeometry}>
        <shaderMaterial
          ref={materialRef}
          vertexShader={utilityVertexShader}
          fragmentShader={utilityFragmentShader}
          uniforms={{
            color: { value: color },
            time: { value: 0 },
            flowSpeed: { value: isFlowUtility ? 1.0 : 0.0 },
            isActive: { value: !properties.toBeAbandoned },
            hasFlow: { value: isFlowUtility && showFlow },
          }}
          transparent={properties.toBeAbandoned}
          opacity={properties.toBeAbandoned ? 0.3 : 1}
          side={THREE.DoubleSide}
        />
        {selected && <Edges color="yellow" linewidth={3} />}
      </mesh>
      
      {/* Utility labels */}
      {linePoints.map((point, idx) => {
        if (idx % 10 === 0) { // Show label every 10th point
          return (
            <Billboard key={idx} position={point} follow={true}>
              <Text
                fontSize={0.3}
                color={utilityType.color}
                anchorX="center"
                anchorY="middle"
                outlineWidth={0.02}
                outlineColor="#000000"
              >
                {utilityType.code}
                {properties.pressure && ` ${properties.pressure}PSI`}
                {properties.voltage && ` ${properties.voltage}kV`}
              </Text>
            </Billboard>
          );
        }
        return null;
      })}
      
      {/* Aerial support structures */}
      {properties.aerial && linePoints.map((point, idx) => {
        if (idx % 5 === 0) { // Place poles every 5th point
          return (
            <group key={`support-${idx}`} position={[point.x, 0, point.z]}>
              {/* Pole */}
              <Cylinder
                args={[0.1, 0.1, Math.abs(point.y) + 5]}
                position={[0, (point.y + 5) / 2, 0]}
              >
                <meshStandardMaterial color="#8b4513" metalness={0.3} roughness={0.8} />
              </Cylinder>
              {/* Cross arm */}
              <Box args={[2, 0.1, 0.1]} position={[0, point.y + 5, 0]}>
                <meshStandardMaterial color="#8b4513" metalness={0.3} roughness={0.8} />
              </Box>
              {/* Insulators */}
              {[-0.8, 0, 0.8].map((offset, i) => (
                <Sphere key={i} args={[0.05]} position={[offset, point.y + 5.1, 0]}>
                  <meshStandardMaterial color="#333333" metalness={0.9} roughness={0.1} />
                </Sphere>
              ))}
            </group>
          );
        }
        return null;
      })}

      {/* X marks for abandoned utilities */}
      {properties.toBeAbandoned && linePoints.map((point, idx) => {
        if (idx % 3 === 0) { // Show X every 3rd point
          return (
            <group key={`x-${idx}`} position={point}>
              <Line
                points={[
                  new THREE.Vector3(-0.2, -0.2, 0),
                  new THREE.Vector3(0.2, 0.2, 0)
                ]}
                color="#6c757d"
                lineWidth={3}
              />
              <Line
                points={[
                  new THREE.Vector3(-0.2, 0.2, 0),
                  new THREE.Vector3(0.2, -0.2, 0)
                ]}
                color="#6c757d"
                lineWidth={3}
              />
            </group>
          );
        }
        return null;
      })}

      {/* Flow direction indicators */}
      {isFlowUtility && !properties.toBeAbandoned && (
        <>
          {linePoints.map((point, idx) => {
            if (idx % 20 === 0 && idx < linePoints.length - 1) {
              const nextPoint = linePoints[idx + 1];
              if (!nextPoint) return null;
              const direction = new THREE.Vector3().subVectors(nextPoint, point).normalize();
              const quaternion = new THREE.Quaternion().setFromUnitVectors(
                new THREE.Vector3(0, 1, 0),
                direction
              );
              
              return (
                <Cone
                  key={`arrow-${idx}`}
                  args={[0.1, 0.3, 4]}
                  position={point}
                  quaternion={quaternion}
                >
                  <meshStandardMaterial color={utilityType.color} emissive={utilityType.color} emissiveIntensity={0.2} />
                </Cone>
              );
            }
            return null;
          })}
        </>
      )}
    </group>
  );
}

// Enhanced symbol component with better materials
function EnhancedUtilitySymbol({ 
  position, 
  type, 
  properties,
  selected = false 
}: { 
  position: number[], 
  type: string, 
  properties: any,
  selected?: boolean
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  const pos = new THREE.Vector3((position[0] ?? 0) / 1000, -properties.depth, (position[2] ?? 0) / 1000);
  
  // Floating animation for selected items
  useFrame((state) => {
    if (meshRef.current && selected) {
      meshRef.current.position.y = pos.y + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });
  
  const symbolComponents: Record<string, React.ReactNode> = {
    'telecom-pole': (
      <group ref={meshRef}>
        <Cylinder args={[0.2, 0.2, 10]} position={[0, 5, 0]}>
          <meshStandardMaterial color="#69db7c" metalness={0.3} roughness={0.7} />
        </Cylinder>
        <Html position={[0, 10.5, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Telecom Pole
          </div>
        </Html>
      </group>
    ),
    'electric-pole': (
      <group ref={meshRef}>
        <Cylinder args={[0.3, 0.3, 12]} position={[0, 6, 0]}>
          <meshStandardMaterial color="#ff6b6b" metalness={0.3} roughness={0.7} />
        </Cylinder>
        <Box args={[3, 0.2, 0.2]} position={[0, 10, 0]}>
          <meshStandardMaterial color="#8b4513" />
        </Box>
        <Html position={[0, 12.5, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Electric Pole
          </div>
        </Html>
      </group>
    ),
    'fiber-handhole': (
      <group ref={meshRef}>
        <Box args={[0.6, 0.2, 0.9]} position={[0, -0.1, 0]}>
          <meshStandardMaterial color="#cc5de8" metalness={0.6} roughness={0.3} />
        </Box>
        <Html position={[0, 0.3, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Fiber Handhole
          </div>
        </Html>
      </group>
    ),
    'water-valve': (
      <group ref={meshRef}>
        <Cylinder args={[0.2, 0.2, 0.3]} position={[0, 0, 0]} rotation={[Math.PI / 2, 0, 0]}>
          <meshStandardMaterial color="#4dabf7" metalness={0.8} roughness={0.2} />
        </Cylinder>
        <Box args={[0.6, 0.1, 0.1]}>
          <meshStandardMaterial color="#1864ab" metalness={0.8} roughness={0.2} />
        </Box>
        <Html position={[0, 0.5, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Water Valve
          </div>
        </Html>
      </group>
    ),
    'gas-valve': (
      <group ref={meshRef}>
        <Cylinder args={[0.15, 0.15, 0.3]} position={[0, 0, 0]} rotation={[Math.PI / 2, 0, 0]}>
          <meshStandardMaterial color="#fab005" metalness={0.8} roughness={0.2} />
        </Cylinder>
        <Box args={[0.5, 0.08, 0.08]}>
          <meshStandardMaterial color="#e67700" metalness={0.8} roughness={0.2} />
        </Box>
        <Html position={[0, 0.5, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Gas Valve
          </div>
        </Html>
      </group>
    ),
    'electric-transformer': (
      <group ref={meshRef}>
        <Box args={[1.2, 1.2, 1.2]} position={[0, 0.6, 0]}>
          <meshStandardMaterial color="#ff6b6b" metalness={0.7} roughness={0.3} />
        </Box>
        <Cylinder args={[0.05, 0.05, 1.5]} position={[-0.4, 1.5, 0]}>
          <meshStandardMaterial color="#333333" metalness={0.9} roughness={0.1} />
        </Cylinder>
        <Cylinder args={[0.05, 0.05, 1.5]} position={[0.4, 1.5, 0]}>
          <meshStandardMaterial color="#333333" metalness={0.9} roughness={0.1} />
        </Cylinder>
        <Html position={[0, 2, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Transformer
          </div>
        </Html>
      </group>
    ),
    'sewer-manhole': (
      <group ref={meshRef}>
        <Cylinder args={[0.6, 0.6, 0.2]} position={[0, -0.1, 0]}>
          <meshStandardMaterial color="#868e96" metalness={0.8} roughness={0.4} />
        </Cylinder>
        <Torus args={[0.5, 0.05, 8, 16]} position={[0, 0, 0]} rotation={[Math.PI / 2, 0, 0]}>
          <meshStandardMaterial color="#495057" metalness={0.9} roughness={0.2} />
        </Torus>
        <Html position={[0, 0.5, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            Manhole
          </div>
        </Html>
      </group>
    ),
    'valve': (
      <group ref={meshRef}>
        <Cylinder args={[0.3, 0.3, 0.1]} rotation={[Math.PI / 2, 0, 0]}>
          <meshStandardMaterial color="#4dabf7" metalness={0.8} roughness={0.2} />
        </Cylinder>
        <Box args={[0.6, 0.1, 0.1]}>
          <meshStandardMaterial color="#1864ab" metalness={0.8} roughness={0.2} />
        </Box>
        <Html position={[0, 0.5, 0]} center>
          <div className="bg-black/80 text-white px-2 py-1 rounded text-xs">
            {properties.id || 'Valve'}
          </div>
        </Html>
      </group>
    ),
    'transformer': (
      <group ref={meshRef}>
        <Box args={[1.5, 1.5, 1.5]}>
          <meshStandardMaterial color="#ff6b6b" metalness={0.6} roughness={0.3} />
        </Box>
        <Cylinder args={[0.1, 0.1, 2]} position={[0, 1.5, 0]}>
          <meshStandardMaterial color="#495057" metalness={0.9} roughness={0.1} />
        </Cylinder>
        {/* Insulators */}
        {[-0.5, 0, 0.5].map((x, i) => (
          <Cylinder key={i} args={[0.05, 0.1, 0.3]} position={[x, 1.7, 0]}>
            <meshStandardMaterial color="#f8f9fa" />
          </Cylinder>
        ))}
      </group>
    ),
    'manhole': (
      <group ref={meshRef}>
        <Cylinder args={[0.8, 0.8, 0.2]}>
          <meshStandardMaterial color="#868e96" metalness={0.9} roughness={0.1} />
        </Cylinder>
        <Torus args={[0.7, 0.1, 8, 16]} rotation={[Math.PI / 2, 0, 0]}>
          <meshStandardMaterial color="#495057" metalness={0.9} roughness={0.1} />
        </Torus>
      </group>
    ),
  };
  
  return (
    <group position={pos}>
      {symbolComponents[type] || (
        <Sphere args={[0.3]}>
          <meshStandardMaterial color="#999999" />
        </Sphere>
      )}
      {selected && (
        <Sphere args={[1.5]} position={[0, 0, 0]}>
          <meshBasicMaterial color="yellow" wireframe opacity={0.3} transparent />
        </Sphere>
      )}
    </group>
  );
}

// Conflict visualization with effects
function ConflictMarker({ position, type, severity }: { position: number[], type: 'hard' | 'soft', severity: number }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const pos = new THREE.Vector3((position[0] ?? 0) / 1000, position[1] ?? 0, (position[2] ?? 0) / 1000);
  
  useFrame((state) => {
    if (meshRef.current) {
      // Pulsing effect
      const scale = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.2;
      meshRef.current.scale.setScalar(scale);
      
      // Rotation
      meshRef.current.rotation.y = state.clock.elapsedTime;
    }
  });
  
  const color = type === 'hard' ? '#ff0000' : '#ffaa00';
  const size = 0.3 + (severity * 0.2);
  
  return (
    <group position={pos}>
      <Sphere ref={meshRef} args={[size]}>
        <meshStandardMaterial 
          color={color} 
          emissive={color} 
          emissiveIntensity={0.5}
          transparent
          opacity={0.8}
        />
      </Sphere>
      <pointLight color={color} intensity={0.5} distance={5} />
      <Billboard follow={true} lockX={false} lockY={false} lockZ={false}>
        <Text
          fontSize={0.3}
          color="#ffffff"
          anchorX="center"
          anchorY="bottom"
          position={[0, size + 0.2, 0]}
        >
          {type === 'hard' ? '⚠️ HARD' : '⚡ SOFT'}
        </Text>
      </Billboard>
    </group>
  );
}

// Ground with realistic materials
function RealisticGround() {
  const depthBuffer = useDepthBuffer({ frames: 1 });
  
  return (
    <group>
      {/* Main ground */}
      <Plane args={[200, 200]} rotation={[-Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
        <MeshReflectorMaterial
          blur={[300, 100]}
          resolution={2048}
          mixBlur={1}
          mixStrength={40}
          roughness={0.8}
          depthScale={1.2}
          minDepthThreshold={0.4}
          maxDepthThreshold={1.4}
          color="#2b2b2b"
          metalness={0.5}
          envMapIntensity={0}
        />
      </Plane>
      
      {/* Grid overlay */}
      <Grid
        args={[200, 200]}
        cellSize={1}
        cellThickness={0.5}
        cellColor="#444444"
        sectionSize={10}
        sectionThickness={1}
        sectionColor="#666666"
        fadeDistance={100}
        fadeStrength={1}
        infiniteGrid
      />
      
      {/* Depth layers */}
      {[5, 10, 15, 20].map((depth: any) => (
        <Plane
          key={depth}
          args={[200, 200]}
          rotation={[-Math.PI / 2, 0, 0]}
          position={[0, -depth, 0]}
        >
          <meshBasicMaterial
            color="#1a1a1a"
            transparent
            opacity={0.1}
            side={THREE.DoubleSide}
          />
        </Plane>
      ))}
    </group>
  );
}

// Measurement tools
function MeasurementTool({ start, end }: { start: THREE.Vector3, end: THREE.Vector3 }) {
  const distance = start.distanceTo(end);
  const midPoint = new THREE.Vector3().lerpVectors(start, end, 0.5);
  
  return (
    <group>
      <Line
        points={[start, end]}
        color="#00ff00"
        lineWidth={2}
        dashed
        dashSize={0.1}
        gapSize={0.05}
      />
      <Billboard position={midPoint}>
        <Text
          fontSize={0.3}
          color="#00ff00"
          anchorX="center"
          anchorY="middle"
          outlineWidth={0.02}
          outlineColor="#000000"
        >
          {`${(distance * 1000).toFixed(2)}m`}
        </Text>
      </Billboard>
    </group>
  );
}

// Main 3D scene component
function Scene({ utilities, conflicts, settings }: any) {
  const { camera } = useThree();
  
  return (
    <>
      {/* Lighting setup */}
      <ambientLight intensity={settings.ambientIntensity} />
      <directionalLight
        position={[10, 20, 10]}
        intensity={settings.sunIntensity}
        castShadow
        shadow-mapSize={[2048, 2048]}
        shadow-camera-far={50}
        shadow-camera-left={-50}
        shadow-camera-right={50}
        shadow-camera-top={50}
        shadow-camera-bottom={-50}
      />
      
      {settings.spotlights && (
        <>
          <SpotLight
            position={[10, 10, 10]}
            angle={0.3}
            penumbra={1}
            intensity={0.5}
            castShadow
            color="#4dabf7"
          />
          <SpotLight
            position={[-10, 10, -10]}
            angle={0.3}
            penumbra={1}
            intensity={0.5}
            castShadow
            color="#ff6b6b"
          />
        </>
      )}
      
      {/* Environment */}
      {settings.skybox && (
        <Sky
          distance={450000}
          sunPosition={[0, 1, 0]}
          inclination={0.6}
          azimuth={0.25}
          rayleigh={settings.rayleigh}
        />
      )}
      
      {settings.environment && (
        <Environment
          preset={settings.environmentPreset}
          background={false}
        />
      )}
      
      {/* Ground and grid */}
      <RealisticGround />
      
      {/* Utilities */}
      {utilities.map((utility: Utility) => {
        if (!settings.visibleTypes[utility.properties.type]) return null;
        if (!settings.showAbandoned && utility.properties.toBeAbandoned) return null;
        if (!settings.showAerial && utility.properties.aerial) return null;
        
        if (utility.type === 'line') {
          return (
            <EnhancedUtilityLine
              key={utility.id}
              points={utility.points || []}
              properties={utility.properties}
              selected={settings.selectedUtilities.includes(utility.id)}
              showFlow={settings.showFlow}
            />
          );
        } else if (utility.type === 'symbol') {
          return (
            <EnhancedUtilitySymbol
              key={utility.id}
              position={utility.position}
              type={utility.symbolType || 'default'}
              properties={utility.properties}
              selected={settings.selectedUtilities.includes(utility.id)}
            />
          );
        }
        return null;
      })}
      
      {/* Conflicts */}
      {settings.showConflicts && conflicts.map((conflict: any, idx: number) => (
        <ConflictMarker
          key={idx}
          position={conflict.position}
          type={conflict.type}
          severity={conflict.severity || 1}
        />
      ))}
      
      {/* Measurement tools */}
      {settings.measurementPoints.length === 2 && (
        <MeasurementTool
          start={settings.measurementPoints[0]}
          end={settings.measurementPoints[1]}
        />
      )}
      
      {/* Camera controls */}
      <OrbitControls
        enableDamping
        dampingFactor={0.05}
        maxPolarAngle={Math.PI / 2}
        minDistance={5}
        maxDistance={200}
        makeDefault
      />
      
      {/* Gizmo */}
      <GizmoHelper alignment="bottom-right" margin={[80, 80]}>
        <GizmoViewport axisColors={['red', 'green', 'blue']} labelColor="black" />
      </GizmoHelper>
    </>
  );
}

// Main component
interface Utility3DProfessionalProps {
  projectId: string;
  utilities?: Utility[];
  conflicts?: any[];
}

const Utility3DProfessional: React.FC<Utility3DProfessionalProps> = ({
  projectId,
  utilities = [],
  conflicts = []
}) => {
  const [settings, setSettings] = useState({
    visibleTypes: Object.keys(UTILITY_TYPES).reduce((acc: any, type: any) => ({
      ...acc,
      [type]: true,
    }), {} as Record<UtilityType, boolean>),
    selectedUtilities: [] as string[],
    showConflicts: true,
    showFlow: true,
    showGrid: true,
    showAbandoned: true,
    showAerial: true,
    skybox: true,
    environment: true,
    environmentPreset: 'city' as any,
    ambientIntensity: 0.5,
    sunIntensity: 1,
    spotlights: false,
    shadows: true,
    postProcessing: true,
    stats: false,
    rayleigh: 0.5,
    measurementPoints: [] as THREE.Vector3[],
    viewMode: 'orbit' as 'orbit' | 'first-person' | 'top-down',
  });

  // Mock data for demonstration
  const mockUtilities = [
    {
      id: '1',
      type: 'line',
      points: [[0, 0, 0], [10000, 0, 0], [10000, 0, 10000]],
      properties: {
        type: 'water' as UtilityType,
        depth: 5,
        size: 12,
        pressure: 60,
        material: 'ductile iron',
        proposed: true,
        toBeAbandoned: false,
      },
    },
    {
      id: '2',
      type: 'line',
      points: [[0, 0, 5000], [10000, 0, 5000]],
      properties: {
        type: 'electric' as UtilityType,
        depth: -5, // Negative depth for aerial
        voltage: 12.5,
        aerial: true,
        proposed: true,
        toBeAbandoned: false,
      },
    },
    {
      id: '3',
      type: 'line',
      points: [[5000, 0, 0], [5000, 0, 10000]],
      properties: {
        type: 'naturalGas' as UtilityType,
        depth: 4,
        size: 6,
        pressure: 30,
        material: 'steel',
        proposed: false,
        toBeAbandoned: true,
      },
    },
    {
      id: '4',
      type: 'symbol',
      position: [5000, 0, 5000],
      symbolType: 'valve',
      properties: {
        type: 'water' as UtilityType,
        depth: 5,
        id: 'WV-001',
      },
    },
  ];

  const mockConflicts = [
    { position: [5000, -4, 5000], type: 'hard' as const, severity: 1 },
    { position: [7500, -3, 2500], type: 'soft' as const, severity: 0.5 },
  ];

  const handleExportScene = () => {
    // Export scene as GLTF
    if (typeof window !== 'undefined') {
      import('three/examples/jsm/exporters/GLTFExporter.js').then(({ GLTFExporter }) => {
        const exporter = new GLTFExporter();
        const scene = document.querySelector('canvas')?.parentElement;
        // Implementation would export the Three.js scene
        safeLog.info('Exporting 3D scene...');
      });
    }
  };

  return (
    <Card className="w-full h-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle>Professional 3D Utility Visualization</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleExportScene}>
              <Download className="mr-2 h-4 w-4" />
              Export 3D
            </Button>
            <Button variant="outline" size="sm">
              <Camera className="mr-2 h-4 w-4" />
              Screenshot
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="flex h-[800px]">
          {/* 3D View */}
          <div className="flex-1 relative">
            <Canvas
              shadows={settings.shadows}
              camera={{ position: [20, 20, 20], fov: 60 }}
              gl={{
                antialias: true,
                alpha: false,
                powerPreference: "high-performance",
                stencil: false,
                depth: true,
              }}
            >
              <Scene
                utilities={utilities.length > 0 ? utilities : mockUtilities}
                conflicts={conflicts.length > 0 ? conflicts : mockConflicts}
                settings={settings}
              />
              
              {/* Post-processing effects */}
              {settings.postProcessing && (
                <EffectComposer>
                  <SSAO
                    samples={30}
                    radius={10}
                    intensity={30}
                    luminanceInfluence={0.1}
                  />
                  <Bloom
                    intensity={0.5}
                    luminanceThreshold={0.9}
                    luminanceSmoothing={0.025}
                  />
                  <DepthOfField
                    focusDistance={0}
                    focalLength={0.02}
                    bokehScale={2}
                    height={480}
                  />
                </EffectComposer>
              )}
              
              {/* Performance monitor */}
              {settings.stats && <Stats />}
              <PerformanceMonitor
                onDecline={() => {
                  safeLog.info('Performance declining, reducing quality...');
                  setSettings(prev => ({ ...prev, shadows: false, postProcessing: false }));
                }}
              />
            </Canvas>
            
            {/* View mode indicator */}
            <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded">
              {settings.viewMode.toUpperCase()} VIEW
            </div>
          </div>

          {/* Controls Sidebar */}
          <div className="w-80 border-l overflow-y-auto">
            <Tabs defaultValue="visibility" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="visibility">Visibility</TabsTrigger>
                <TabsTrigger value="environment">Environment</TabsTrigger>
                <TabsTrigger value="tools">Tools</TabsTrigger>
              </TabsList>

              {/* Visibility Controls */}
              <TabsContent value="visibility" className="p-4 space-y-4">
                <div>
                  <Label className="text-sm font-medium mb-2">Utility Types</Label>
                  <div className="space-y-2">
                    {(Object.keys(UTILITY_TYPES) as UtilityType[]).map((type: UtilityType) => (
                      <div key={type} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: (UTILITY_TYPES as any)[type]?.color || '#gray' }}
                          />
                          <span className="text-sm">{(UTILITY_TYPES as any)[type]?.name || type}</span>
                        </div>
                        <Switch
                          checked={settings.visibleTypes[type]}
                          onCheckedChange={(checked) => {
                            setSettings(prev => ({
                              ...prev,
                              visibleTypes: { ...prev.visibleTypes, [type]: checked }
                            }));
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-abandoned">Show Abandoned</Label>
                    <Switch
                      id="show-abandoned"
                      checked={settings.showAbandoned}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, showAbandoned: checked }));
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-aerial">Show Aerial</Label>
                    <Switch
                      id="show-aerial"
                      checked={settings.showAerial}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, showAerial: checked }));
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-conflicts">Show Conflicts</Label>
                    <Switch
                      id="show-conflicts"
                      checked={settings.showConflicts}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, showConflicts: checked }));
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-flow">Show Flow Animation</Label>
                    <Switch
                      id="show-flow"
                      checked={settings.showFlow}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, showFlow: checked }));
                      }}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Environment Controls */}
              <TabsContent value="environment" className="p-4 space-y-4">
                <div>
                  <Label className="text-sm font-medium mb-2">Lighting</Label>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="ambient">Ambient Light</Label>
                      <Slider
                        id="ambient"
                        value={[settings.ambientIntensity]}
                        onValueChange={([value]) => {
                          setSettings(prev => ({ ...prev, ambientIntensity: value ?? 0.5 }));
                        }}
                        max={2}
                        step={0.1}
                      />
                    </div>
                    <div>
                      <Label htmlFor="sun">Sun Intensity</Label>
                      <Slider
                        id="sun"
                        value={[settings.sunIntensity]}
                        onValueChange={([value]) => {
                          setSettings(prev => ({ ...prev, sunIntensity: value ?? 1 }));
                        }}
                        max={3}
                        step={0.1}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="spotlights">Spotlights</Label>
                      <Switch
                        id="spotlights"
                        checked={settings.spotlights}
                        onCheckedChange={(checked) => {
                          setSettings(prev => ({ ...prev, spotlights: checked }));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-2">Environment</Label>
                  <UISelect 
                    value={settings.environmentPreset} 
                    onValueChange={(value: any) => {
                      setSettings(prev => ({ ...prev, environmentPreset: value }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="city">City</SelectItem>
                      <SelectItem value="sunset">Sunset</SelectItem>
                      <SelectItem value="dawn">Dawn</SelectItem>
                      <SelectItem value="night">Night</SelectItem>
                      <SelectItem value="warehouse">Warehouse</SelectItem>
                      <SelectItem value="forest">Forest</SelectItem>
                      <SelectItem value="apartment">Apartment</SelectItem>
                      <SelectItem value="studio">Studio</SelectItem>
                    </SelectContent>
                  </UISelect>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="skybox">Skybox</Label>
                    <Switch
                      id="skybox"
                      checked={settings.skybox}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, skybox: checked }));
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="shadows">Shadows</Label>
                    <Switch
                      id="shadows"
                      checked={settings.shadows}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, shadows: checked }));
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="post-processing">Post Processing</Label>
                    <Switch
                      id="post-processing"
                      checked={settings.postProcessing}
                      onCheckedChange={(checked) => {
                        setSettings(prev => ({ ...prev, postProcessing: checked }));
                      }}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Tools */}
              <TabsContent value="tools" className="p-4 space-y-4">
                <div>
                  <Label className="text-sm font-medium mb-2">View Mode</Label>
                  <div className="grid grid-cols-1 gap-2">
                    <Button
                      variant={settings.viewMode === 'orbit' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSettings(prev => ({ ...prev, viewMode: 'orbit' }))}
                    >
                      <Navigation className="mr-2 h-4 w-4" />
                      Orbit
                    </Button>
                    <Button
                      variant={settings.viewMode === 'first-person' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSettings(prev => ({ ...prev, viewMode: 'first-person' }))}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      First Person
                    </Button>
                    <Button
                      variant={settings.viewMode === 'top-down' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSettings(prev => ({ ...prev, viewMode: 'top-down' }))}
                    >
                      <Grid3x3 className="mr-2 h-4 w-4" />
                      Top Down
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-2">Measurement</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      // Toggle measurement mode
                      safeLog.info('Measurement mode');
                    }}
                  >
                    <Ruler className="mr-2 h-4 w-4" />
                    Measure Distance
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="stats">Performance Stats</Label>
                  <Switch
                    id="stats"
                    checked={settings.stats}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({ ...prev, stats: checked }));
                    }}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Utility3DProfessional;