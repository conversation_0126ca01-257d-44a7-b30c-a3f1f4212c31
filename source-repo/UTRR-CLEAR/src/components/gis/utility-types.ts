// Utility type definitions and configurations

export const UTILITY_TYPES = {
  electric: {
    name: 'Electric',
    code: 'E',
    color: '#ff6b6b',
    categories: ['distribution', 'transmission'],
    units: ['kV', 'V'],
    materials: ['copper', 'aluminum'],
  },
  naturalGas: {
    name: 'Natural Gas',
    code: 'G',
    color: '#fab005',
    categories: ['distribution', 'transmission'],
    units: ['PSI', 'bar'],
    materials: ['steel', 'polyethylene', 'cast iron'],
  },
  gasPipeline: {
    name: 'Gas Pipeline',
    code: 'GP',
    color: '#fd7e14',
    categories: ['transmission', 'gathering'],
    units: ['PSI', 'bar'],
    materials: ['steel', 'composite'],
  },
  water: {
    name: 'Water',
    code: 'W',
    color: '#4dabf7',
    categories: ['distribution', 'transmission'],
    units: ['PSI', 'bar'],
    materials: ['ductile iron', 'PVC', 'HDPE', 'concrete', 'steel'],
  },
  sanitarySewer: {
    name: 'Sanitary Sewer',
    code: 'SS',
    color: '#868e96',
    categories: ['gravity', 'force main'],
    units: ['inches', 'feet'],
    materials: ['PVC', 'clay', 'concrete', 'HDPE'],
  },
  fiber: {
    name: 'Fiber Optic',
    code: 'F',
    color: '#cc5de8',
    categories: ['backbone', 'distribution'],
    units: ['strands', 'cores'],
    materials: ['single-mode', 'multi-mode'],
  },
  telecom: {
    name: 'Telecom',
    code: 'T',
    color: '#69db7c',
    categories: ['copper', 'coax'],
    units: ['pairs', 'cables'],
    materials: ['copper', 'coaxial'],
  },
  steam: {
    name: 'Steam',
    code: 'ST',
    color: '#adb5bd',
    categories: ['high-pressure', 'low-pressure'],
    units: ['PSI', 'bar'],
    materials: ['steel', 'pre-insulated'],
  },
  reclaimedWater: {
    name: 'Reclaimed Water',
    code: 'RW',
    color: '#845ef7',
    categories: ['distribution', 'transmission'],
    units: ['PSI', 'bar'],
    materials: ['PVC', 'HDPE'],
  },
} as const;

export type UtilityType = keyof typeof UTILITY_TYPES;

export interface UtilitySymbol {
  id: string;
  name: string;
  icon: string;
  category: 'pole' | 'handhole' | 'valve' | 'meter' | 'transformer' | 'junction';
  defaultSize?: {
    width?: number;
    height?: number;
    radius?: number;
    diameter?: number;
  };
  sizeUnit?: 'inches' | 'feet';
}

export const UTILITY_SYMBOLS: UtilitySymbol[] = [
  {
    id: 'telecom-pole',
    name: 'Telecom Pole',
    icon: 'circle',
    category: 'pole',
    defaultSize: { diameter: 12 },
    sizeUnit: 'inches',
  },
  {
    id: 'electric-pole',
    name: 'Electric Pole',
    icon: 'square',
    category: 'pole',
    defaultSize: { diameter: 14 },
    sizeUnit: 'inches',
  },
  {
    id: 'fiber-handhole',
    name: 'Fiber Handhole',
    icon: 'rectangle',
    category: 'handhole',
    defaultSize: { width: 24, height: 36 },
    sizeUnit: 'inches',
  },
  {
    id: 'water-valve',
    name: 'Water Valve',
    icon: 'diamond',
    category: 'valve',
    defaultSize: { diameter: 8 },
    sizeUnit: 'inches',
  },
  {
    id: 'gas-valve',
    name: 'Gas Valve',
    icon: 'triangle',
    category: 'valve',
    defaultSize: { diameter: 6 },
    sizeUnit: 'inches',
  },
  {
    id: 'electric-transformer',
    name: 'Transformer',
    icon: 'hexagon',
    category: 'transformer',
    defaultSize: { width: 48, height: 48 },
    sizeUnit: 'inches',
  },
  {
    id: 'sewer-manhole',
    name: 'Sewer Manhole',
    icon: 'circle',
    category: 'handhole',
    defaultSize: { diameter: 48 },
    sizeUnit: 'inches',
  },
];

export interface LineStyle {
  pattern?: number[];
  width: number;
  letterSpacing?: number;
  aerial?: boolean;
  abandoned?: boolean;
}

export const LINE_STYLES: Record<string, LineStyle> = {
  solid: {
    pattern: undefined,
    width: 3,
  },
  solidAerial: {
    pattern: undefined,
    width: 3,
    aerial: true,
  },
  solidDashedAerial: {
    pattern: [10, 5, 2, 5], // solid-dash-dot pattern
    width: 3,
    aerial: true,
  },
  abandoned: {
    pattern: [5, 5],
    width: 2,
    abandoned: true,
  },
};

export interface UtilityProperties {
  id: string;
  type: UtilityType;
  category: string;
  material?: string;
  size?: number; // in inches for pipes
  unit?: string;
  voltage?: number;
  pressure?: number;
  depth: number;
  aerial: boolean;
  proposed: boolean;
  toBeAbandoned: boolean;
  description?: string;
  symbolType?: string;
}