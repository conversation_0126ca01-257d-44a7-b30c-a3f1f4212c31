# Dimension Tools for CAD-Style Drawing

This module provides construction-style dimensioning tools for creating professional CAD drawings in web mapping applications.

## Features

### Dimension Types

1. **Linear Dimensions**
   - Horizontal dimensions
   - Vertical dimensions
   - Aligned dimensions (parallel to measured line)

2. **Angular Dimensions**
   - Measure angles between two lines
   - Display in degrees with customizable precision

3. **Radius Dimensions**
   - Measure and display radius of circles/arcs
   - Shows dimension from center to edge

4. **Diameter Dimensions**
   - Measure full diameter of circles
   - Shows dimension line across circle

5. **Leader Lines**
   - Annotation lines with text
   - Useful for notes and callouts

### Styling Options

- **Text Style**: Customizable height, color, and position
- **Line Style**: Color, width, and dash patterns
- **Arrow Styles**: Filled, open, dot, or architectural tick marks
- **Units**: Support for feet, meters, inches, and millimeters
- **Precision**: Configurable decimal places

## Usage

### Basic Integration

```typescript
import DimensionTools from '~/components/gis/dimension-tools';
import VectorSource from 'ol/source/Vector';
import Map from 'ol/Map';

// Create dimension tools
const dimensionSource = new VectorSource();
const dimensionTools = new DimensionTools(map, dimensionSource);

// Start drawing a linear dimension
dimensionTools.startLinearDimension('horizontal');

// Start drawing an angular dimension
dimensionTools.startAngularDimension();

// Clear all dimensions
dimensionTools.clearAllDimensions();

// Export to DXF
const dxfContent = dimensionTools.exportToDXF();
```

### Using the React Component

```tsx
import { DimensionToolbar } from '~/components/mapping/dimension-toolbar';

function MyMapComponent() {
  const [map, setMap] = useState<Map | null>(null);
  const [source, setSource] = useState<VectorSource | null>(null);

  return (
    <div>
      <DimensionToolbar 
        map={map}
        source={source}
        onDimensionStart={(type) => console.log('Started:', type)}
        onDimensionEnd={() => console.log('Dimension completed')}
      />
    </div>
  );
}
```

### Using the Custom Hook

```tsx
import { useDimensionTools } from '~/hooks/use-dimension-tools';

function MyComponent() {
  const {
    activeTool,
    startLinearDimension,
    startAngularDimension,
    clearAllDimensions,
    updateStyle,
  } = useDimensionTools({
    map: myMap,
    source: mySource,
    initialStyle: {
      unit: 'ft',
      textHeight: 14,
    },
  });

  // Use the dimension tools
  return (
    <div>
      <button onClick={() => startLinearDimension()}>
        Linear Dimension
      </button>
      <button onClick={() => startAngularDimension()}>
        Angular Dimension
      </button>
    </div>
  );
}
```

## Style Configuration

### Default Style

```typescript
const DEFAULT_DIMENSION_STYLE = {
  textHeight: 14,
  textColor: '#000000',
  lineColor: '#0066ff',
  lineWidth: 1,
  arrowSize: 10,
  extensionLineOffset: 3,
  extensionLineOvershoot: 3,
  textOffset: 5,
  decimalPlaces: 2,
  unit: 'ft',
  showUnit: true,
  arrowStyle: 'filled',
  textPosition: 'above',
};
```

### Preset Styles

```typescript
import { DIMENSION_PRESETS } from '~/components/gis/dimension-tools';

// Architectural style
const archTools = new DimensionTools(map, source, DIMENSION_PRESETS.architectural);

// Engineering style
const engTools = new DimensionTools(map, source, DIMENSION_PRESETS.engineering);

// Metric style
const metricTools = new DimensionTools(map, source, DIMENSION_PRESETS.metric);

// Survey style
const surveyTools = new DimensionTools(map, source, DIMENSION_PRESETS.survey);
```

## Complete Example

See `src/components/mapping/cad-drawing-interface.tsx` for a complete example of integrating dimension tools with utility drawing tools in a professional CAD interface.

## DXF Export

The dimension tools support exporting to DXF format:

```typescript
// Export all dimensions to DXF
const dxfContent = dimensionTools.exportToDXF();

// Save as file
const blob = new Blob([dxfContent], { type: 'application/dxf' });
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'dimensions.dxf';
a.click();
```

## Construction Standards

The dimension tools follow standard construction drawing conventions:

- Extension lines extend slightly beyond dimension lines
- Arrows point inward for standard dimensions
- Text is always readable (rotates to stay upright)
- Dimension lines avoid overlapping with measured objects
- Leader lines use appropriate arrow styles

## Integration with Existing Tools

The dimension tools are designed to work alongside:

- Drawing tools for creating utilities
- Measurement tools for quick measurements
- CAD line styles for utility representation
- Conflict detection systems
- Layer management

## Performance Considerations

- Dimensions are rendered as vector features
- Styles are cached for performance
- DXF export is optimized for large drawings
- Works with WebGL rendering when available