import { describe, it, expect, beforeEach, afterEach, vi, type Mock, type MockedFunction } from 'vitest';
import { Map } from 'ol';
import VectorSource from 'ol/source/Vector.js';
import Draw from 'ol/interaction/Draw.js';
import Snap from 'ol/interaction/Snap.js';
import Modify from 'ol/interaction/Modify.js';
import Select from 'ol/interaction/Select.js';
import { Feature } from 'ol';
import { LineString, Polygon, Point } from 'ol/geom.js';
import type { Coordinate } from 'ol/coordinate.js';

// Mock OpenLayers modules
vi.mock('ol/Map');
vi.mock('ol/interaction/Draw');
vi.mock('ol/interaction/Snap');
vi.mock('ol/interaction/Modify');
vi.mock('ol/interaction/Select');
vi.mock('ol/source/Vector');
vi.mock('ol/Feature');

// DrawModeManager class for testing
class DrawModeManager {
  private map: Map | null = null;
  private source: VectorSource;
  private draw: Draw | null = null;
  private snap: Snap | null = null;
  private modify: Modify | null = null;
  private select: Select | null = null;
  private currentMode: string | null = null;

  constructor(source: VectorSource) {
    this.source = source;
  }

  setMap(map: Map) {
    this.map = map;
  }

  activateDrawMode(mode: 'Point' | 'LineString' | 'Polygon') {
    this.deactivateAll();
    
    if (!this.map) return;

    this.currentMode = mode;
    this.draw = new Draw({
      source: this.source,
      type: mode,
    });

    this.snap = new Snap({
      source: this.source,
    });

    this.map.addInteraction(this.draw);
    this.map.addInteraction(this.snap);

    this.draw.on('drawend', (evt) => {
      // Handle draw end
      const feature = evt.feature;
      feature.set('mode', mode);
    });
  }

  activateModifyMode() {
    this.deactivateAll();
    
    if (!this.map) return;

    this.currentMode = 'modify';
    this.select = new Select();
    this.modify = new Modify({
      features: this.select.getFeatures(),
    });

    this.map.addInteraction(this.select);
    this.map.addInteraction(this.modify);
  }

  deactivateAll() {
    if (!this.map) return;

    if (this.draw) {
      this.map.removeInteraction(this.draw);
      this.draw = null;
    }

    if (this.snap) {
      this.map.removeInteraction(this.snap);
      this.snap = null;
    }

    if (this.modify) {
      this.map.removeInteraction(this.modify);
      this.modify = null;
    }

    if (this.select) {
      this.map.removeInteraction(this.select);
      this.select = null;
    }

    this.currentMode = null;
  }

  getCurrentMode() {
    return this.currentMode;
  }

  clearFeatures() {
    this.source.clear();
  }
}

describe('DrawModeManager', () => {
  let manager: DrawModeManager;
  let mockMap: Map;
  let mockSource: VectorSource;

  // Create typed mocks for all functions
  const mockMapAddInteraction = vi.fn();
  const mockMapRemoveInteraction = vi.fn();
  const mockDrawOn = vi.fn();
  const mockSnapSetMap = vi.fn();
  const mockDrawSetMap = vi.fn();
  const mockModifySetMap = vi.fn();
  const mockSelectSetMap = vi.fn();
  const mockSelectGetFeatures = vi.fn();
  const mockSourceClear = vi.fn();
  const mockFeatureSet = vi.fn();

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();

    // Set up Map mock
    vi.mocked(Map).mockImplementation(() => ({
      addInteraction: mockMapAddInteraction,
      removeInteraction: mockMapRemoveInteraction,
      addOverlay: vi.fn(),
      removeOverlay: vi.fn(),
      on: vi.fn(),
      un: vi.fn(),
    } as any));

    // Set up Draw mock
    vi.mocked(Draw).mockImplementation(() => ({
      on: mockDrawOn,
      un: vi.fn(),
      setActive: vi.fn(),
      setMap: mockDrawSetMap,
    } as any));

    // Set up Snap mock
    vi.mocked(Snap).mockImplementation(() => ({
      setActive: vi.fn(),
      setMap: mockSnapSetMap,
    } as any));

    // Set up Modify mock
    vi.mocked(Modify).mockImplementation(() => ({
      on: vi.fn(),
      un: vi.fn(),
      setActive: vi.fn(),
      setMap: mockModifySetMap,
    } as any));

    // Set up Select mock
    const mockFeatures = {
      clear: vi.fn(),
      push: vi.fn(),
    };
    mockSelectGetFeatures.mockReturnValue(mockFeatures);
    
    vi.mocked(Select).mockImplementation(() => ({
      getFeatures: mockSelectGetFeatures,
      on: vi.fn(),
      un: vi.fn(),
      setActive: vi.fn(),
      setMap: mockSelectSetMap,
    } as any));

    // Set up VectorSource mock
    vi.mocked(VectorSource).mockImplementation(() => ({
      addFeature: vi.fn(),
      removeFeature: vi.fn(),
      clear: mockSourceClear,
      getFeatures: vi.fn().mockReturnValue([]),
      on: vi.fn(),
      un: vi.fn(),
    } as any));

    // Set up Feature mock
    vi.mocked(Feature).mockImplementation(() => ({
      set: mockFeatureSet,
      get: vi.fn(),
      getGeometry: vi.fn(),
    } as any));

    // Create instances
    mockSource = new VectorSource();
    mockMap = new Map();
    
    manager = new DrawModeManager(mockSource);
    manager.setMap(mockMap);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Draw Mode', () => {
    it('should activate point drawing mode', () => {
      manager.activateDrawMode('Point');

      expect(Draw).toHaveBeenCalledWith({
        source: mockSource,
        type: 'Point',
      });

      expect(Snap).toHaveBeenCalledWith({
        source: mockSource,
      });

      expect(mockMapAddInteraction).toHaveBeenCalledTimes(2);
      expect(manager.getCurrentMode()).toBe('Point');
    });

    it('should activate line drawing mode', () => {
      manager.activateDrawMode('LineString');

      expect(Draw).toHaveBeenCalledWith({
        source: mockSource,
        type: 'LineString',
      });

      expect(mockMapAddInteraction).toHaveBeenCalledTimes(2);
      expect(manager.getCurrentMode()).toBe('LineString');
    });

    it('should activate polygon drawing mode', () => {
      manager.activateDrawMode('Polygon');

      expect(Draw).toHaveBeenCalledWith({
        source: mockSource,
        type: 'Polygon',
      });

      expect(mockMapAddInteraction).toHaveBeenCalledTimes(2);
      expect(manager.getCurrentMode()).toBe('Polygon');
    });

    it('should handle draw end event', () => {
      manager.activateDrawMode('LineString');

      expect(mockDrawOn).toHaveBeenCalledWith('drawend', expect.any(Function));

      // Simulate draw end event
      const drawEndHandler = mockDrawOn.mock.calls[0]?.[1] as (evt: any) => void;
      const mockFeature = new Feature();

      drawEndHandler({ feature: mockFeature });

      expect(mockFeatureSet).toHaveBeenCalledWith('mode', 'LineString');
    });
  });

  describe('Modify Mode', () => {
    it('should activate modify mode', () => {
      manager.activateModifyMode();

      expect(Select).toHaveBeenCalled();
      expect(Modify).toHaveBeenCalled();

      expect(mockMapAddInteraction).toHaveBeenCalledTimes(2);
      expect(manager.getCurrentMode()).toBe('modify');
    });

    it('should pass selected features to modify interaction', () => {
      manager.activateModifyMode();

      const mockFeatures = mockSelectGetFeatures();

      expect(Modify).toHaveBeenCalledWith({
        features: mockFeatures,
      });
    });
  });

  describe('Deactivate', () => {
    it('should remove all interactions when deactivating', () => {
      // Activate draw mode first
      manager.activateDrawMode('Point');
      
      // Clear previous calls
      vi.clearAllMocks();

      // Deactivate
      manager.deactivateAll();

      expect(mockMapRemoveInteraction).toHaveBeenCalledTimes(2); // Draw and Snap
      expect(manager.getCurrentMode()).toBeNull();
    });

    it('should deactivate previous mode when activating new mode', () => {
      manager.activateDrawMode('Point');
      
      // Clear previous calls
      vi.clearAllMocks();

      manager.activateDrawMode('LineString');

      expect(mockMapRemoveInteraction).toHaveBeenCalledTimes(2); // Previous Draw and Snap
    });
  });

  describe('Clear Features', () => {
    it('should clear all features from source', () => {
      manager.clearFeatures();

      expect(mockSourceClear).toHaveBeenCalled();
    });
  });

  describe('No Map', () => {
    it('should not activate draw mode without map', () => {
      const managerWithoutMap = new DrawModeManager(mockSource);
      managerWithoutMap.activateDrawMode('Point');

      expect(Draw).not.toHaveBeenCalled();
      expect(Snap).not.toHaveBeenCalled();
    });

    it('should not activate modify mode without map', () => {
      const managerWithoutMap = new DrawModeManager(mockSource);
      managerWithoutMap.activateModifyMode();

      expect(Select).not.toHaveBeenCalled();
      expect(Modify).not.toHaveBeenCalled();
    });
  });
});