import { describe, it, expect, beforeEach, vi } from 'vitest';
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import VectorSource from 'ol/source/Vector.js';
import { Feature } from 'ol';
import { LineString, Point } from 'ol/geom.js';
import DimensionTools, { DEFAULT_DIMENSION_STYLE, DIMENSION_PRESETS } from '../dimension-tools';

// Mock OpenLayers Map
vi.mock('ol/Map');
vi.mock('ol/interaction/Draw');

describe('DimensionTools', () => {
  let map: Map;
  let source: VectorSource;
  let dimensionTools: DimensionTools;

  beforeEach(() => {
    // Create mock map
    map = new Map({
      view: new View({
        center: [0, 0],
        zoom: 10,
      }),
    });

    // Create vector source
    source = new VectorSource();

    // Create dimension tools instance
    dimensionTools = new DimensionTools(map, source);
  });

  describe('initialization', () => {
    it('should create dimension tools with default style', () => {
      expect(dimensionTools).toBeDefined();
    });

    it('should create dimension tools with custom style', () => {
      const customStyle = {
        textHeight: 16,
        textColor: '#FF0000',
        lineColor: '#00FF00',
      };

      const customDimensionTools = new DimensionTools(map, source, customStyle);
      expect(customDimensionTools).toBeDefined();
    });
  });

  describe('dimension creation', () => {
    it('should start linear dimension drawing', () => {
      const spy = vi.spyOn(map, 'addInteraction');
      dimensionTools.startLinearDimension('horizontal');
      expect(spy).toHaveBeenCalled();
    });

    it('should start angular dimension drawing', () => {
      const spy = vi.spyOn(map, 'addInteraction');
      dimensionTools.startAngularDimension();
      expect(spy).toHaveBeenCalled();
    });

    it('should start radius dimension drawing', () => {
      const spy = vi.spyOn(map, 'addInteraction');
      dimensionTools.startRadiusDimension();
      expect(spy).toHaveBeenCalled();
    });

    it('should start diameter dimension drawing', () => {
      const spy = vi.spyOn(map, 'addInteraction');
      dimensionTools.startDiameterDimension();
      expect(spy).toHaveBeenCalled();
    });

    it('should start leader dimension drawing', () => {
      const spy = vi.spyOn(map, 'addInteraction');
      dimensionTools.startLeaderDimension('Test Note');
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('dimension management', () => {
    it('should stop current dimension operation', () => {
      const spy = vi.spyOn(map, 'removeInteraction');
      dimensionTools.startLinearDimension('horizontal');
      dimensionTools.stopCurrentDimension();
      expect(spy).toHaveBeenCalled();
    });

    it('should clear all dimensions from source', () => {
      // Add some test features with dimension type
      const feature1 = new Feature(new LineString([[0, 0], [100, 0]]));
      feature1.set('dimensionType', 'dimension-line');
      const feature2 = new Feature(new Point([50, 50]));
      feature2.set('dimensionType', 'arrow');
      
      source.addFeature(feature1);
      source.addFeature(feature2);
      
      expect(source.getFeatures()).toHaveLength(2);
      
      dimensionTools.clearAllDimensions();
      
      expect(source.getFeatures()).toHaveLength(0);
    });
  });

  describe('style management', () => {
    it('should update dimension style', () => {
      const newStyle = {
        textHeight: 18,
        textColor: '#0000FF',
      };

      dimensionTools.updateStyle(newStyle);
      // Style should be updated internally
      expect(dimensionTools).toBeDefined();
    });

    it('should apply preset styles correctly', () => {
      const architecturalTools = new DimensionTools(
        map, 
        source, 
        DIMENSION_PRESETS.architectural
      );
      expect(architecturalTools).toBeDefined();
    });
  });

  describe('DXF export', () => {
    it('should export empty DXF when no dimensions exist', () => {
      const dxfContent = dimensionTools.exportToDXF();
      expect(dxfContent).toContain('0\nSECTION');
      expect(dxfContent).toContain('2\nENTITIES');
      expect(dxfContent).toContain('0\nENDSEC');
      expect(dxfContent).toContain('0\nEOF');
    });

    it('should export dimensions to DXF format', () => {
      // Add a dimension line feature
      const feature = new Feature(new LineString([[0, 0], [100, 0]]));
      feature.set('dimensionType', 'dimension-line');
      source.addFeature(feature);

      const dxfContent = dimensionTools.exportToDXF();
      expect(dxfContent).toContain('0\nLINE');
      expect(dxfContent).toContain('8\nDIMENSIONS'); // Layer name
    });
  });

  describe('dimension calculations', () => {
    it('should format dimension text with units', () => {
      const tools = new DimensionTools(map, source, {
        unit: 'ft',
        showUnit: true,
        decimalPlaces: 2,
      });

      // The formatting is internal, but we can test by creating a dimension
      expect(tools).toBeDefined();
    });

    it('should handle different unit conversions', () => {
      const metersTools = new DimensionTools(map, source, { unit: 'm' });
      const feetTools = new DimensionTools(map, source, { unit: 'ft' });
      const inchesTools = new DimensionTools(map, source, { unit: 'in' });
      const mmTools = new DimensionTools(map, source, { unit: 'mm' });

      expect(metersTools).toBeDefined();
      expect(feetTools).toBeDefined();
      expect(inchesTools).toBeDefined();
      expect(mmTools).toBeDefined();
    });
  });

  describe('dimension types', () => {
    it('should support all dimension types', () => {
      const dimensionTypes = [
        'linear',
        'aligned',
        'angular',
        'radius',
        'diameter',
        'leader',
        'continuous',
        'baseline',
      ];

      // Types are defined in the module
      expect(dimensionTypes).toBeDefined();
    });

    it('should support different arrow styles', () => {
      const arrowStyles = ['filled', 'open', 'dot', 'architectural'];
      
      arrowStyles.forEach(style => {
        const tools = new DimensionTools(map, source, {
          arrowStyle: style as any,
        });
        expect(tools).toBeDefined();
      });
    });
  });
});