import Draw from 'ol/interaction/Draw.js';
import Snap from 'ol/interaction/Snap.js';
import Modify from 'ol/interaction/Modify.js';
import Select from 'ol/interaction/Select.js';
import { Circle as CircleStyle, Fill, Stroke, Style, Text, Icon, RegularShape } from 'ol/style.js';
import { Point, LineString, Polygon, Circle } from 'ol/geom.js';
import VectorSource from 'ol/source/Vector.js';
import { Feature } from 'ol';
import { getArea, getLength } from 'ol/sphere.js';
import type { Coordinate } from 'ol/coordinate.js';
import { Map } from 'ol';
import Overlay from 'ol/Overlay.js';

// Advanced style factory for CAD-like rendering
export class CADStyleFactory {
  // Create style with text along line
  static createLineStyleWithText(
    text: string,
    color: string,
    width: number,
    lineDash?: number[],
    textColor: string = '#ffffff'
  ): Style[] {
    return [
      new Style({
        stroke: new Stroke({
          color: color,
          width: width,
          lineDash: lineDash,
          lineCap: 'round',
          lineJoin: 'round',
        }),
      }),
      new Style({
        text: new Text({
          text: text,
          font: 'bold 12px Arial',
          fill: new Fill({ color: textColor }),
          stroke: new Stroke({ color: color, width: 3 }),
          placement: 'line', // Text along line
          textBaseline: 'bottom',
          offsetY: -5,
          overflow: true,
        }),
      }),
    ];
  }

  // Create custom symbol styles
  static createSymbolStyle(type: string, size: number, color: string): Style {
    const symbolStyles: Record<string, Style> = {
      'valve-circle': new Style({
        image: new RegularShape({
          points: 4,
          radius: size,
          angle: Math.PI / 4,
          fill: new Fill({ color: '#ffffff' }),
          stroke: new Stroke({ color: color, width: 3 }),
        }),
      }),
      'valve-gate': new Style({
        image: new RegularShape({
          points: 4,
          radius: size,
          radius2: size * 0.5,
          angle: 0,
          fill: new Fill({ color: '#ffffff' }),
          stroke: new Stroke({ color: color, width: 3 }),
        }),
      }),
      'manhole': new Style({
        image: new CircleStyle({
          radius: size,
          fill: new Fill({ color: color }),
          stroke: new Stroke({ color: '#ffffff', width: 2 }),
        }),
      }),
      'transformer': new Style({
        image: new RegularShape({
          points: 6,
          radius: size,
          fill: new Fill({ color: color }),
          stroke: new Stroke({ color: '#ffffff', width: 2 }),
        }),
      }),
      'pole': new Style({
        image: new RegularShape({
          points: 4,
          radius: size,
          angle: Math.PI / 4,
          fill: new Fill({ color: '#ffffff' }),
          stroke: new Stroke({ color: color, width: 3 }),
        }),
        text: new Text({
          text: '⊕',
          font: `${size * 2}px Arial`,
          fill: new Fill({ color: color }),
        }),
      }),
      'handhole': new Style({
        image: new RegularShape({
          points: 4,
          radius: size,
          angle: 0,
          fill: new Fill({ color: '#ffffff' }),
          stroke: new Stroke({ color: color, width: 3 }),
        }),
      }),
    };

    return symbolStyles[type] || symbolStyles['valve-circle']!;
  }

  // Create pattern fill style (using canvas pattern)
  static createPatternFill(pattern: 'hatch' | 'cross' | 'dots', color: string): Fill {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    canvas.width = 10;
    canvas.height = 10;
    
    context.strokeStyle = color;
    context.lineWidth = 1;

    switch (pattern) {
      case 'hatch':
        context.moveTo(0, 10);
        context.lineTo(10, 0);
        context.stroke();
        break;
      case 'cross':
        context.moveTo(0, 5);
        context.lineTo(10, 5);
        context.moveTo(5, 0);
        context.lineTo(5, 10);
        context.stroke();
        break;
      case 'dots':
        context.fillStyle = color;
        context.beginPath();
        context.arc(5, 5, 1, 0, 2 * Math.PI);
        context.fill();
        break;
    }

    return new Fill({
      color: context.createPattern(canvas, 'repeat') as any,
    });
  }
}

// Advanced measurement tool
export class MeasurementTool {
  private measureOverlay: Overlay | null = null;
  private measureElement: HTMLElement | null = null;
  private draw: Draw | null = null;
  private source: VectorSource;
  private map: Map | null = null;

  constructor(source: VectorSource) {
    this.source = source;
  }

  activate(map: Map, type: 'LineString' | 'Polygon') {
    this.map = map;
    this.createMeasureOverlay();

    // Create draw interaction with measurement
    this.draw = new Draw({
      source: this.source,
      type: type,
      style: new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)',
        }),
        stroke: new Stroke({
          color: 'rgba(0, 0, 0, 0.5)',
          lineDash: [10, 10],
          width: 2,
        }),
        image: new CircleStyle({
          radius: 5,
          stroke: new Stroke({
            color: 'rgba(0, 0, 0, 0.7)',
          }),
          fill: new Fill({
            color: 'rgba(255, 255, 255, 0.2)',
          }),
        }),
      }),
    });

    // Update measurement while drawing
    this.draw.on('drawstart', (evt) => {
      const sketch = evt.feature;
      let tooltipCoord: Coordinate | undefined;

      const listener = sketch.getGeometry()?.on('change', (evt) => {
        const geom = evt.target;
        let output = '';
        
        if (geom instanceof Polygon) {
          output = this.formatArea(geom);
          tooltipCoord = geom.getInteriorPoint().getCoordinates();
        } else if (geom instanceof LineString) {
          output = this.formatLength(geom);
          tooltipCoord = geom.getLastCoordinate();
        }

        if (this.measureElement) {
          this.measureElement.innerHTML = output;
        }
        if (this.measureOverlay && tooltipCoord) {
          this.measureOverlay.setPosition(tooltipCoord);
        }
      });

      // Store listener for cleanup
      (sketch as any).listener = listener;
    });

    this.draw.on('drawend', (evt) => {
      // Clean up
      if ((evt.feature as any).listener) {
        (evt.feature.getGeometry() as any).un('change', (evt.feature as any).listener);
      }
      
      // Create persistent measurement label
      this.createPersistentMeasurement(evt.feature);
      
      // Clear tooltip
      if (this.measureElement) {
        this.measureElement.innerHTML = '';
      }
    });

    map.addInteraction(this.draw);
  }

  deactivate() {
    if (this.draw && this.map) {
      this.map.removeInteraction(this.draw);
    }
    if (this.measureOverlay && this.map) {
      this.map.removeOverlay(this.measureOverlay);
    }
    this.draw = null;
    this.measureOverlay = null;
    this.measureElement = null;
  }

  private createMeasureOverlay() {
    if (!this.map) return;

    this.measureElement = document.createElement('div');
    this.measureElement.className = 'ol-tooltip ol-tooltip-measure';
    this.measureElement.style.cssText = `
      position: relative;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 4px;
      color: white;
      padding: 4px 8px;
      font-size: 12px;
      white-space: nowrap;
      pointer-events: none;
    `;

    this.measureOverlay = new Overlay({
      element: this.measureElement,
      offset: [0, -15],
      positioning: 'bottom-center',
    });

    this.map.addOverlay(this.measureOverlay);
  }

  private createPersistentMeasurement(feature: Feature) {
    const geom = feature.getGeometry();
    let measurement = '';
    let position: Coordinate | undefined;

    if (geom instanceof Polygon) {
      measurement = this.formatArea(geom);
      position = geom.getInteriorPoint().getCoordinates();
    } else if (geom instanceof LineString) {
      measurement = this.formatLength(geom);
      position = geom.getCoordinates()[Math.floor(geom.getCoordinates().length / 2)];
    }

    if (position && measurement) {
      feature.setStyle(new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)',
        }),
        stroke: new Stroke({
          color: '#0099ff',
          width: 2,
        }),
        text: new Text({
          text: measurement,
          font: '14px Arial',
          fill: new Fill({ color: '#000000' }),
          stroke: new Stroke({ color: '#ffffff', width: 3 }),
          offsetY: -20,
        }),
      }));
    }
  }

  private formatLength(line: LineString): string {
    const length = getLength(line, { projection: 'EPSG:3857' });
    let output = '';
    if (length > 100) {
      output = Math.round((length / 1000) * 100) / 100 + ' km';
    } else {
      output = Math.round(length * 100) / 100 + ' m';
    }
    return output;
  }

  private formatArea(polygon: Polygon): string {
    const area = getArea(polygon, { projection: 'EPSG:3857' });
    let output = '';
    if (area > 10000) {
      output = Math.round((area / 1000000) * 100) / 100 + ' km²';
    } else {
      output = Math.round(area * 100) / 100 + ' m²';
    }
    return output;
  }

  clearMeasurements(): void {
    this.source.clear();
    if (this.measureOverlay && this.map) {
      this.map.removeOverlay(this.measureOverlay);
      this.measureOverlay = null;
    }
    if (this.measureElement) {
      this.measureElement = null;
    }
  }
}

// Snap configuration for precise drawing
export class SnapConfiguration {
  static createSnapInteractions(sources: VectorSource[]): Snap[] {
    return sources.map(source => new Snap({
      source: source,
      edge: true,
      vertex: true,
      pixelTolerance: 15,
    }));
  }
}

// Advanced drawing modes
export class DrawingModes {
  // Create perpendicular line drawing
  static createPerpendicularDraw(source: VectorSource): Draw {
    return new Draw({
      source: source,
      type: 'LineString',
      maxPoints: 2,
      geometryFunction: (coordinates, geometry) => {
        if (!geometry) {
          geometry = new LineString([]);
        }
        if (coordinates.length === 2) {
          // Make line perpendicular
          const [start, end] = coordinates as Coordinate[];
          if (start && end) {
            const dx = end[0]! - start[0]!;
            const dy = end[1]! - start[1]!;
            // Rotate 90 degrees
            const perpEnd = [
              start[0]! - dy,
              start[1]! + dx,
            ];
            (geometry as LineString).setCoordinates([start, perpEnd]);
          }
        }
        return geometry;
      },
    });
  }

  // Create parallel line drawing
  static createParallelDraw(source: VectorSource, referenceFeature: Feature, offset: number): Draw {
    const refGeom = referenceFeature.getGeometry() as LineString;
    return new Draw({
      source: source,
      type: 'LineString',
      geometryFunction: (coordinates, geometry) => {
        if (!geometry) {
          geometry = new LineString([]);
        }
        // Create parallel line with offset
        const coords = refGeom.getCoordinates();
        const parallelCoords = this.offsetLine(coords, offset);
        (geometry as LineString).setCoordinates(parallelCoords);
        return geometry;
      },
    });
  }

  private static offsetLine(coords: Coordinate[], offset: number): Coordinate[] {
    const result: Coordinate[] = [];
    for (let i = 0; i < coords.length - 1; i++) {
      const dx = coords[i + 1]![0]! - coords[i]![0]!;
      const dy = coords[i + 1]![1]! - coords[i]![1]!;
      const len = Math.sqrt(dx * dx + dy * dy);
      const offsetX = -dy / len * offset;
      const offsetY = dx / len * offset;
      
      if (i === 0) {
        result.push([coords[i]![0]! + offsetX, coords[i]![1]! + offsetY]);
      }
      result.push([coords[i + 1]![0]! + offsetX, coords[i + 1]![1]! + offsetY]);
    }
    return result;
  }

  // Create arc drawing (for curved utilities)
  static createArcDraw(source: VectorSource): Draw {
    return new Draw({
      source: source,
      type: 'LineString',
      maxPoints: 3,
      geometryFunction: (coordinates, geometry) => {
        if (!geometry) {
          geometry = new LineString([]);
        }
        if (coordinates.length === 3) {
          // Create arc through 3 points
          const arcCoords = this.createArc(
            coordinates[0] as Coordinate,
            coordinates[1] as Coordinate,
            coordinates[2] as Coordinate,
            32 // segments
          );
          (geometry as LineString).setCoordinates(arcCoords);
        }
        return geometry;
      },
    });
  }

  private static createArc(p1: Coordinate, p2: Coordinate, p3: Coordinate, segments: number): Coordinate[] {
    // Validate input coordinates
    if (p1.length < 2 || p2.length < 2 || p3.length < 2) {
      return [p1, p2, p3];
    }

    // Calculate circle center from 3 points
    const center = this.getCircleCenter(p1, p2, p3);
    if (!center || center.length < 2) return [p1, p2, p3];

    // Ensure center coordinates are defined
    const centerX = center[0];
    const centerY = center[1];
    if (centerX === undefined || centerY === undefined) {
      return [p1, p2, p3];
    }

    // Ensure point coordinates are defined
    const p1X = p1[0], p1Y = p1[1];
    const p2X = p2[0], p2Y = p2[1];
    const p3X = p3[0], p3Y = p3[1];
    
    if (p1X === undefined || p1Y === undefined || 
        p2X === undefined || p2Y === undefined || 
        p3X === undefined || p3Y === undefined) {
      return [p1, p2, p3];
    }

    const radius = Math.sqrt(
      Math.pow(centerX - p1X, 2) + Math.pow(centerY - p1Y, 2)
    );

    // Calculate angles
    const angle1 = Math.atan2(p1Y - centerY, p1X - centerX);
    const angle2 = Math.atan2(p2Y - centerY, p2X - centerX);
    const angle3 = Math.atan2(p3Y - centerY, p3X - centerX);

    // Determine arc direction
    let startAngle = angle1;
    let endAngle = angle3;
    let angleRange = endAngle - startAngle;

    // Normalize angle range
    if (angleRange > Math.PI) angleRange -= 2 * Math.PI;
    if (angleRange < -Math.PI) angleRange += 2 * Math.PI;

    // Generate arc points
    const coords: Coordinate[] = [];
    for (let i = 0; i <= segments; i++) {
      const angle = startAngle + (angleRange * i / segments);
      coords.push([
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle),
      ]);
    }

    return coords;
  }

  private static getCircleCenter(p1: Coordinate, p2: Coordinate, p3: Coordinate): Coordinate | null {
    // Ensure coordinates have required elements
    if (p1.length < 2 || p2.length < 2 || p3.length < 2) return null;
    
    const ax = p1[0];
    const ay = p1[1];
    const bx = p2[0];
    const by = p2[1];
    const cx = p3[0];
    const cy = p3[1];

    // Check if any coordinates are undefined
    if (ax === undefined || ay === undefined || bx === undefined || 
        by === undefined || cx === undefined || cy === undefined) {
      return null;
    }

    const d = 2 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by));
    if (Math.abs(d) < 0.0001) return null;

    const ux = ((ax * ax + ay * ay) * (by - cy) + (bx * bx + by * by) * (cy - ay) + (cx * cx + cy * cy) * (ay - by)) / d;
    const uy = ((ax * ax + ay * ay) * (cx - bx) + (bx * bx + by * by) * (ax - cx) + (cx * cx + cy * cy) * (bx - ax)) / d;

    return [ux, uy];
  }
}

export class AdvancedDrawingTools {
  private map: Map;
  private drawLayer: any;
  private measureLayer: any;
  private currentDraw: Draw | null = null;
  private snapInteraction: Snap | null = null;
  private measureTooltipElement: HTMLElement | null = null;
  private measureTooltip: any = null;
  private drawListeners: any[] = [];

  constructor(map: Map, drawLayer: any) {
    this.map = map;
    this.drawLayer = drawLayer;
    this.measureLayer = drawLayer;
  }

  startPerpendicular(baseFeature: Feature): void {
    if (!baseFeature || !baseFeature.getGeometry()) return;
    
    const geometry = baseFeature.getGeometry();
    if (!(geometry instanceof LineString)) return;

    this.stopDrawing();
    
    const source = this.drawLayer.getSource();
    if (!source) return;

    this.currentDraw = new Draw({
      source: source,
      type: 'LineString',
      maxPoints: 2,
    });

    this.map.addInteraction(this.currentDraw);
  }

  startArcDrawing(numPoints: number = 50): void {
    this.stopDrawing();
    
    const source = this.drawLayer.getSource();
    if (!source) return;

    this.currentDraw = new Draw({
      source: source,
      type: 'LineString',
      freehand: false,
    });

    this.map.addInteraction(this.currentDraw);
  }

  startParallelDrawing(baseFeature: Feature, offset: number): void {
    if (!baseFeature || !baseFeature.getGeometry()) return;
    
    const geometry = baseFeature.getGeometry();
    if (!(geometry instanceof LineString)) return;

    this.stopDrawing();
    
    const source = this.drawLayer.getSource();
    if (!source) return;

    const coords = geometry.getCoordinates();
    const parallelCoords: Coordinate[] = [];
    
    for (let i = 0; i < coords.length; i++) {
      const current = coords[i];
      if (!current || current.length < 2) continue;
      
      const currentX = current[0];
      const currentY = current[1];
      if (currentX === undefined || currentY === undefined) continue;

      if (i === 0) {
        const next = coords[i + 1];
        if (!next || next.length < 2) continue;
        
        const nextX = next[0];
        const nextY = next[1];
        if (nextX === undefined || nextY === undefined) continue;
        
        const angle = Math.atan2(nextY - currentY, nextX - currentX) + Math.PI / 2;
        parallelCoords.push([
          currentX + Math.cos(angle) * offset,
          currentY + Math.sin(angle) * offset,
        ]);
      } else if (i === coords.length - 1) {
        const prev = coords[i - 1];
        if (!prev || prev.length < 2) continue;
        
        const prevX = prev[0];
        const prevY = prev[1];
        if (prevX === undefined || prevY === undefined) continue;
        
        const angle = Math.atan2(currentY - prevY, currentX - prevX) + Math.PI / 2;
        parallelCoords.push([
          currentX + Math.cos(angle) * offset,
          currentY + Math.sin(angle) * offset,
        ]);
      } else {
        const prev = coords[i - 1];
        const next = coords[i + 1];
        
        if (!prev || prev.length < 2 || !next || next.length < 2) continue;
        
        const prevX = prev[0];
        const prevY = prev[1];
        const nextX = next[0];
        const nextY = next[1];
        
        if (prevX === undefined || prevY === undefined || 
            nextX === undefined || nextY === undefined) continue;
        
        const angle1 = Math.atan2(currentY - prevY, currentX - prevX) + Math.PI / 2;
        const angle2 = Math.atan2(nextY - currentY, nextX - currentX) + Math.PI / 2;
        const avgAngle = (angle1 + angle2) / 2;
        
        parallelCoords.push([
          currentX + Math.cos(avgAngle) * offset,
          currentY + Math.sin(avgAngle) * offset,
        ]);
      }
    }
    
    const parallelFeature = new Feature(new LineString(parallelCoords));
    parallelFeature.setProperties(baseFeature.getProperties());
    source.addFeature(parallelFeature);
  }

  enableSnapping(pixelTolerance: number = 10): void {
    this.disableSnapping();
    
    const source = this.drawLayer.getSource();
    if (!source) return;

    this.snapInteraction = new Snap({
      source: source,
      pixelTolerance: pixelTolerance,
    });
    
    this.map.addInteraction(this.snapInteraction);
  }

  disableSnapping(): void {
    if (this.snapInteraction) {
      this.map.removeInteraction(this.snapInteraction);
      this.snapInteraction = null;
    }
  }

  stopDrawing(): void {
    if (this.currentDraw) {
      this.map.removeInteraction(this.currentDraw);
      this.currentDraw = null;
    }
    
    this.drawListeners.forEach((listener: any) => {
      if (listener && listener.unByKey) {
        listener.unByKey();
      }
    });
    this.drawListeners = [];
    
    if (this.measureTooltipElement) {
      this.measureTooltipElement.remove();
      this.measureTooltipElement = null;
    }
    
    if (this.measureTooltip) {
      this.map.removeOverlay(this.measureTooltip);
      this.measureTooltip = null;
    }
  }
}

export type DrawingMode = 
  | 'line'
  | 'perpendicular'
  | 'parallel'
  | 'arc'
  | 'rectangle'
  | 'circle'
  | 'polygon'
  | 'measure_line'
  | 'measure_area';

export interface SnapConfiguration {
  enabled: boolean;
  pixelTolerance: number;
  vertex: boolean;
  edge: boolean;
  angle: boolean;
  angleStep: number;
}

export default AdvancedDrawingTools;