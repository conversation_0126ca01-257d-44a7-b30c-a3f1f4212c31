'use client';

import * as React from 'react';
import Link from 'next/link.js';
import { usePathname } from 'next/navigation';
import { useAuth } from '~/hooks/use-auth';
import { cn } from '~/lib/utils';
import {
  BarChart,
  BookOpen,
  Calendar,
  Database,
  FileSpreadsheet,
  FileText,
  LayoutDashboard,
  Lightbulb,
  LogOut,
  MapPin,
  Settings,
  ShieldCheck,
  UserCheck,
  Users,
  Layers,
} from 'lucide-react';
import { Button } from '~/components/ui/button';

export function Sidebar() {
  const pathname = usePathname();
  const { session, user, signOut } = useAuth();

  // Determine if user has admin access
  const isAdmin = user && user.role === 'admin';

  const handleLogout = async () => {
    await signOut();
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      active: pathname === '/dashboard',
    },
    {
      name: 'Projects',
      href: '/projects',
      icon: FileText,
      active: pathname === '/projects' || pathname?.startsWith('/projects/'),
    },
    {
      name: 'Utility Coordination',
      href: '/utility-coordination',
      icon: MapPin,
      active: pathname === '/utility-coordination',
    },
    {
      name: 'GIS Test',
      href: '/gis-test',
      icon: MapPin,
      active: pathname === '/gis-test',
    },
    {
      name: 'Stakeholders',
      href: '/stakeholders',
      icon: Users,
      active: pathname === '/stakeholders',
    },
    {
      name: 'Timesheet',
      href: '/timesheet',
      icon: Calendar,
      active: pathname === '/timesheet',
    },
    {
      name: 'Sub Invoices',
      href: '/sub-invoices',
      icon: FileSpreadsheet,
      active: pathname === '/sub-invoices',
    },
    {
      name: 'Feature Requests',
      href: '/feature-requests',
      icon: Lightbulb,
      active: pathname === '/feature-requests',
    },
    {
      name: 'Knowledge Base',
      href: '/knowledge-base',
      icon: BookOpen,
      active: pathname === '/knowledge-base',
    },
  ];

  const adminNavigation = [
    {
      name: 'Admin Dashboard',
      href: '/admin',
      icon: ShieldCheck,
      active: pathname === '/admin' || pathname?.startsWith('/admin/'),
    },
    {
      name: 'User Management',
      href: '/admin/user-management',
      icon: Users,
      active: pathname === '/admin/user-management',
    },
    {
      name: 'Template Center',
      href: '/admin/templates',
      icon: Layers,
      active: pathname === '/admin/templates' || pathname?.startsWith('/admin/templates/'),
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart,
      active: pathname === '/admin/analytics',
    },
    {
      name: 'Database Management',
      href: '/admin/database-management',
      icon: Database,
      active: pathname === '/admin/database-management',
    },
  ];

  return (
    <div className="flex h-full w-64 flex-col bg-gray-900 text-white">
      <div className="flex h-16 items-center justify-center border-b border-gray-700">
        <h1 className="text-xl font-bold">UtilitySync T3</h1>
      </div>

      <nav className="flex-1 space-y-1 px-2 py-4">
        {navigation.map((item: any) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                item.active
                  ? 'bg-background text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              )}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          );
        })}

        {isAdmin && (
          <>
            <div className="border-t border-gray-700 my-4" />
            <div className="px-2 py-2">
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                Administration
              </h3>
            </div>
            {adminNavigation.map((item: any) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                    item.active
                      ? 'bg-background text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  )}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              );
            })}
          </>
        )}
      </nav>

      <div className="border-t border-gray-700 p-4">
        <div className="flex items-center">
          <div className="flex-1">
            <p className="text-sm font-medium text-white">
              {user?.user_metadata?.full_name || user?.email}
            </p>
            <p className="text-xs text-muted-foreground">{user?.role}</p>
          </div>
        </div>
        <div className="mt-3 space-y-1">
          <Link
            href="/settings"
            className="group flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
          >
            <Settings className="mr-3 h-4 w-4" />
            Settings
          </Link>
          <Button
            variant="ghost"
            onClick={handleLogout}
            className="w-full justify-start text-gray-300 hover:bg-gray-700 hover:text-white p-2 h-auto"
          >
            <LogOut className="mr-3 h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </div>
    </div>
  );
}
