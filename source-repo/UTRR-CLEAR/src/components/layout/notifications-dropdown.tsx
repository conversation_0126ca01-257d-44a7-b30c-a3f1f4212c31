'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { Button } from '~/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Badge } from '~/components/ui/badge';
import {
  Bell,
  CheckCheck,
  Clock,
  FileText,
  AlertTriangle,
  Calendar,
  MessageSquare,
  X,
  Loader2,
  User,
  FolderOpen,
  CheckCircle,
  Upload,
  AlertCircle,
  UserPlus,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '~/lib/utils';
import { useSupabaseRealtimeNotifications } from '~/hooks/use-supabase-realtime';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { api } from '~/trpc/react';
import { useRouter } from 'next/navigation';
import { toast } from '~/hooks/use-toast';

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  priority: string;
  read: boolean;
  read_at: Date | null;
  action_url: string | null;
  action_label: string | null;
  created_at: Date;
  data?: any;
}

interface RealtimeMessage {
  event: string;
  data: any;
  timestamp?: Date;
}

// Mock useRealtime hook until real implementation is available
const useRealtime = (config: any) => ({
  connectionStatus: 'connected' as const,
  lastMessage: null as RealtimeMessage | null,
  subscribe: () => {},
  isConnected: true,
});

// Define this interface since it's referenced in the file but not defined
interface BatchPayload {
  count: number;
}

// Convert real-time message to notification
const convertMessageToNotification = (message: RealtimeMessage): Notification | null => {
  const data = message.data as any;
  
  // Handle real notification messages
  if (message.event === 'notification:new' && data.id) {
    return {
      id: data.id,
      type: data.type,
      title: data.title,
      message: data.content || data.message,
      priority: data.priority || 'normal',
      read: data.read || false,
      read_at: null,
      action_url: data.actionUrl,
      action_label: data.actionLabel,
      created_at: message.timestamp ? new Date(message.timestamp) : new Date(),
      data: data.data
    };
  }
  
  return null;
};

export function NotificationsDropdown() {
  const { user } = useAuth();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  
  // Real-time notifications are handled by the useRealtime hook below
  
  // Query notifications from API
  const { data: notificationsData, isLoading, refetch } = api.notifications.getAll.useQuery({
    limit: 20,
    offset: 0,
    unreadOnly: false
  }, {
    enabled: !!user,
    refetchInterval: 30000 // Refetch every 30 seconds
  });

  // Query unread count
  const { data: unreadCount = 0 } = api.notifications.getUnreadCount.useQuery(undefined, {
    enabled: !!user,
    refetchInterval: 15000 // Refetch every 15 seconds
  });

  // Mutations
  const markAsReadMutation = api.notifications.markAsRead.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  const markAllAsReadMutation = api.notifications.markAllAsRead.useMutation({
    onSuccess: () => {
      refetch();
      toast({
        title: "All notifications marked as read"
      });
    }
  });

  const deleteMutation = api.notifications.delete.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  // Initialize real-time connection for notifications
  const { 
    connectionStatus, 
    lastMessage, 
    subscribe, 
    isConnected 
  } = useRealtime({
    pollingInterval: 15000, // 15 seconds for notifications
    debug: false
  });

  // Subscribe to notification events
  useEffect(() => {
    // subscribe(['notification:new']); // Commented out until subscribe function is properly implemented
  }, [subscribe]);

  // Handle new real-time messages
  useEffect(() => {
    if (lastMessage) {
      const newNotification = convertMessageToNotification(lastMessage);
      if (newNotification) {
        // Refetch notifications when a new one arrives
        refetch();
        
        // Show a toast for high priority notifications
        if (newNotification.priority === 'high' || newNotification.priority === 'urgent') {
          toast({
            title: newNotification.title,
            description: newNotification.message,
            variant: newNotification.priority === 'urgent' ? 'destructive' : 'default'
          });
        }
      }
    }
  }, [lastMessage, refetch]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'task_assigned':
      case 'task_completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'project_update':
        return <FolderOpen className="h-4 w-4" />;
      case 'conflict_detected':
        return <AlertTriangle className="h-4 w-4" />;
      case 'document_uploaded':
        return <Upload className="h-4 w-4" />;
      case 'comment_added':
        return <MessageSquare className="h-4 w-4" />;
      case 'stakeholder_added':
        return <UserPlus className="h-4 w-4" />;
      case 'deadline_approaching':
        return <Clock className="h-4 w-4" />;
      case 'system_alert':
        return <AlertCircle className="h-4 w-4" />;
      case 'approval_required':
        return <FileText className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string, priority: string) => {
    if (priority === 'urgent') {
      return 'text-red-600 bg-red-50';
    }
    
    switch (type) {
      case 'task_assigned':
      case 'task_completed':
        return 'text-blue-600 bg-blue-50';
      case 'project_update':
        return 'text-purple-600 bg-purple-50';
      case 'conflict_detected':
        return 'text-orange-600 bg-orange-50';
      case 'document_uploaded':
        return 'text-green-600 bg-green-50';
      case 'comment_added':
        return 'text-yellow-600 bg-yellow-50';
      case 'deadline_approaching':
        return priority === 'high' ? 'text-red-600 bg-red-50' : 'text-amber-600 bg-amber-50';
      case 'system_alert':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read if not already
    if (!notification.read) {
      await markAsReadMutation.mutateAsync({ notificationId: notification.id });
    }
    
    // Navigate to action URL if provided
    if (notification.action_url) {
      setOpen(false);
      router.push(notification.action_url);
    }
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsReadMutation.mutateAsync();
  };

  const handleClearNotification = async (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    await deleteMutation.mutateAsync({ notificationId: id });
  };

  const notifications = notificationsData?.notifications || [];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative flex items-center space-x-1"
        >
          <Bell className="h-4 w-4" />
          <span className="hidden sm:inline-block">Notifications</span>
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
          {isConnected && (
            <div className="absolute top-0 right-0 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold">Notifications</h3>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {unreadCount} unread
              </Badge>
            )}
          </div>
          {notifications.length > 0 && unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              disabled={(markAllAsReadMutation as any).isPending || false}
              className="text-xs"
            >
              <CheckCheck className="h-3 w-3 mr-1" />
              Mark all read
            </Button>
          )}
        </div>

        <ScrollArea className="h-[400px]">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Bell className="h-12 w-12 text-muted-foreground mb-3" />
              <p className="text-sm font-medium text-muted-foreground">
                No notifications
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                You&apos;re all caught up!
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {notifications.map((notification: any) => (
                <div
                  key={notification.id}
                  className={cn(
                    'relative p-4 hover:bg-muted/50 transition-colors cursor-pointer group',
                    !notification.read && 'bg-muted/30'
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex gap-3">
                    <div
                      className={cn(
                        'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
                        getTypeColor(notification.type, notification.priority)
                      )}
                    >
                      {getIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground line-clamp-1">
                            {notification.title}
                          </p>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-0.5">
                            {notification.message}
                          </p>
                          {notification.action_label && (
                            <Button
                              variant="link"
                              size="sm"
                              className="h-auto p-0 text-xs mt-1"
                            >
                              {notification.action_label} →
                            </Button>
                          )}
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDistanceToNow(new Date(notification.created_at), {
                              addSuffix: true,
                            })}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                          onClick={(e: any) => handleClearNotification(e, notification.id)}
                          disabled={(deleteMutation as any).isPending || false}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    {!notification.read && (
                      <div className="absolute top-4 right-4 w-2 h-2 bg-blue-600 rounded-full" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {notifications.length > 0 && notificationsData?.hasMore && (
          <div className="p-3 border-t">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => {
                setOpen(false);
                router.push('/notifications');
              }}
            >
              View all notifications
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}