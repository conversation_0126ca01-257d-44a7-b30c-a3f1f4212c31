import { Suspense } from 'react';
import { HydrateClient } from '~/trpc/server';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { Skeleton } from '~/components/ui/skeleton';

interface OptimizedPageWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

/**
 * Reusable wrapper for optimized page loading with proper hydration
 * Use this to wrap client pages that need better loading performance
 */
export function OptimizedPageWrapper({ 
  children, 
  fallback,
  className = "container mx-auto px-4 py-6" 
}: OptimizedPageWrapperProps) {
  const defaultFallback = (
    <div className={className}>
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <HydrateClient>
      <Suspense fallback={fallback || defaultFallback}>
        <div className={className}>
          {children}
        </div>
      </Suspense>
    </HydrateClient>
  );
}