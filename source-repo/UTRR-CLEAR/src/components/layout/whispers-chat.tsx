'use client';

import * as React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { useRealtimeNotifications } from '~/hooks/use-realtime-events';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { Button } from '~/components/ui/button';
import { 
  ChatBubble, 
  ChatBubbleAvatar, 
  ChatBubbleMessage, 
  ChatBubbleTimestamp,
  ChatInput, 
  ChatMessageList
} from '~/components/ui/chat';
import { 
  MessageSquare, 
  Users, 
  X, 
  ArrowLeft, 
  ChevronRight, 
  Send,
  Shield,
  Clock,
  Info
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Checkbox } from '~/components/ui/checkbox';
import { api } from '~/trpc/react';
import { safeLog } from '~/lib/error-handler';

// Types
interface User {
  id: number;
  name: string | null;
  username: string;
  email: string;
}

interface WhisperMessage {
  id: string;
  fromUserId: number;
  toUserId: number;
  message: string;
  timestamp: string;
  fromUserName: string;
  toUserName: string;
}

interface WhispersChatProps {
  onUnreadCountChange: (count: number) => void;
}

export function WhispersChat({ onUnreadCountChange }: WhispersChatProps) {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<WhisperMessage[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [localUnreadCount, setLocalUnreadCount] = useState(0);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);

  // Add real-time notifications for whispers
  useRealtimeNotifications();

  // Constants
  const MESSAGE_TTL = 5 * 60 * 1000; // 5 minutes
  const STORAGE_KEY = 'whispers_messages';

  // Use tRPC to get online users
  const { data: onlineUsers } = api.whispers.getOnlineUsers.useQuery(
    undefined,
    {
      enabled: !!user,
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  // Update activity periodically with error handling
  const [activityFailureCount, setActivityFailureCount] = useState(0);
  const [lastActivityUpdate, setLastActivityUpdate] = useState<Date | null>(null);
  const [updatesPaused, setUpdatesPaused] = useState(false);
  
  const updateActivity = api.whispers.updateActivity.useMutation({
    onSuccess: () => {
      setActivityFailureCount(0);
      setLastActivityUpdate(new Date());
      setUpdatesPaused(false);
    },
    onError: () => {
      const newCount = activityFailureCount + 1;
      setActivityFailureCount(newCount);
      
      if (newCount >= 3) {
        setUpdatesPaused(true);
        if (newCount === 3) {
          safeLog.warn('Whispers activity updates paused due to repeated failures');
        }
      }
    },
    retry: false,
  });
  
  // Stable callback for updating activity with strict rate limiting
  const updateActivityCallback = useCallback(() => {
    if (!user || updatesPaused) return;
    
    const now = new Date();
    const timeSinceLastUpdate = lastActivityUpdate ? now.getTime() - lastActivityUpdate.getTime() : Infinity;
    
    const minInterval = activityFailureCount > 0 ? 5 * 60 * 1000 : 2 * 60 * 1000;
    
    if (timeSinceLastUpdate >= minInterval) {
      updateActivity.mutate();
    }
  }, [user, updateActivity, activityFailureCount, lastActivityUpdate, updatesPaused]);
  
  useEffect(() => {
    if (!user || updatesPaused) return;
    
    const timeout = setTimeout(() => {
      updateActivityCallback();
    }, 30000);
    
    const baseInterval = 5 * 60 * 1000;
    const actualInterval = baseInterval * Math.max(1, Math.pow(2, Math.min(activityFailureCount, 2)));
    
    const interval = setInterval(() => {
      updateActivityCallback();
    }, actualInterval);
    
    return () => {
      clearTimeout(timeout);
      clearInterval(interval);
    };
  }, [user, updateActivityCallback, activityFailureCount, updatesPaused]);

  // Check if privacy modal should be shown
  useEffect(() => {
    const hasSeenPrivacyModal = localStorage.getItem('whispers_privacy_modal_seen');
    if (!hasSeenPrivacyModal && isOpen) {
      setShowPrivacyModal(true);
    }
  }, [isOpen]);

  // Handle privacy modal acceptance
  const handlePrivacyModalAccept = () => {
    if (dontShowAgain) {
      localStorage.setItem('whispers_privacy_modal_seen', 'true');
    }
    setShowPrivacyModal(false);
  };

  // Update users when data changes
  useEffect(() => {
    if (onlineUsers) {
      setUsers(onlineUsers);
    }
  }, [onlineUsers]);

  // Helper to get/set messages from localStorage
  const getStoredMessages = useCallback((): WhisperMessage[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) return [];
      
      const allMessages: WhisperMessage[] = JSON.parse(stored);
      const now = Date.now();
      
      const validMessages = allMessages.filter(msg => {
        const msgTime = new Date(msg.timestamp).getTime();
        return (now - msgTime) < MESSAGE_TTL;
      });
      
      if (validMessages.length !== allMessages.length) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(validMessages));
      }
      
      return validMessages;
    } catch {
      return [];
    }
  }, [MESSAGE_TTL]);

  const storeMessage = (newMessage: WhisperMessage) => {
    const messages = getStoredMessages();
    messages.push(newMessage);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(messages));
    
    window.dispatchEvent(new StorageEvent('storage', {
      key: STORAGE_KEY,
      newValue: JSON.stringify(messages),
      storageArea: localStorage
    }));
  };

  // Listen for messages from other tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEY && selectedUser) {
        const allMessages = getStoredMessages();
        const conversationMessages = allMessages.filter(msg => 
          (msg.fromUserId === Number(user?.id) && msg.toUserId === selectedUser.id) ||
          (msg.fromUserId === selectedUser.id && msg.toUserId === Number(user?.id))
        );
        setMessages(conversationMessages);
        
        const unreadMessages = allMessages.filter(msg => 
          msg.toUserId === Number(user?.id) && 
          msg.fromUserId !== selectedUser.id &&
          new Date(msg.timestamp).getTime() > Date.now() - 10000
        );
        const count = unreadMessages.length;
        setLocalUnreadCount(count);
        onUnreadCountChange(count);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [selectedUser, user, onUnreadCountChange, getStoredMessages]);

  // Load messages for selected user
  useEffect(() => {
    if (!selectedUser || !user?.id) return;

    const loadConversation = () => {
      const allMessages = getStoredMessages();
      const conversationMessages = allMessages.filter(msg => 
        (msg.fromUserId === Number(user.id) && msg.toUserId === selectedUser.id) ||
        (msg.fromUserId === selectedUser.id && msg.toUserId === Number(user.id))
      );
      setMessages(conversationMessages);
    };

    loadConversation();
    
    const interval = setInterval(loadConversation, 1000);
    return () => clearInterval(interval);
  }, [selectedUser, user, getStoredMessages]);

  // Send message (pure client-side)
  const handleSendMessage = () => {
    if (!selectedUser || !message.trim() || !user) return;

    const newMessage: WhisperMessage = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromUserId: Number(user.id),
      toUserId: selectedUser.id,
      message: message.trim(),
      timestamp: new Date().toISOString(),
      fromUserName: user.user_metadata?.full_name || user.email || 'You',
      toUserName: selectedUser.name || selectedUser.username || 'User'
    };

    storeMessage(newMessage);
    setMessage('');
    
    setMessages(prev => [...prev, newMessage]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle user selection
  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    setLocalUnreadCount(0);
    onUnreadCountChange(0);
  };

  // Cleanup expired messages periodically
  useEffect(() => {
    const cleanup = () => {
      getStoredMessages();
    };
    
    const interval = setInterval(cleanup, 30000);
    return () => clearInterval(interval);
  }, [getStoredMessages]);

  // Format time remaining for messages
  const getTimeRemaining = (timestamp: string) => {
    const msgTime = new Date(timestamp).getTime();
    const now = Date.now();
    const remaining = MESSAGE_TTL - (now - msgTime);
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    
    if (remaining <= 0) return 'Expired';
    if (minutes > 0) return `${minutes}m ${seconds}s`;
    return `${seconds}s`;
  };

  if (!user) {
    return null;
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 relative"
      >
        <MessageSquare className="h-4 w-4" />
        <span className="hidden sm:inline-block">myWhispers</span>
        {localUnreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {localUnreadCount}
          </span>
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-background border border-border rounded-lg shadow-xl z-50">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-base">myWhispers</h3>
                <RealtimeIndicator />
                <div className="flex items-center gap-1">
                  <Shield className="h-3 w-3 text-green-500" />
                  <Clock className="h-3 w-3 text-amber-500" />
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
              <Shield className="h-3 w-3" />
              Ephemeral messages • 5min expiry • Local storage only
            </p>
          </div>

          {!selectedUser ? (
            <div className="p-4">
              {users.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground">
                    No other users are currently online
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Check back later to send private messages
                  </p>
                </div>
              ) : (
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-3">
                    Online Users ({users.length})
                  </h4>
                  <div className="space-y-1 max-h-64 overflow-y-auto">
                    {users.map((user: any) => (
                      <button
                        key={user.id}
                        onClick={() => handleUserSelect(user)}
                        className="w-full text-left p-3 hover:bg-muted rounded-lg flex items-center gap-3 transition-colors"
                      >
                        <div className="relative">
                          <ChatBubbleAvatar 
                            fallback={(user.name || user.username || 'U')[0]?.toUpperCase() || 'U'}
                            className="h-8 w-8"
                          />
                          <div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-background"></div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">
                            {user.name || user.username}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Click to message privately
                          </p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col h-96">
              <div className="p-3 border-b border-border flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedUser(null)}
                  className="p-1"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <ChatBubbleAvatar 
                  fallback={(selectedUser?.name || selectedUser?.username || 'U')[0]?.toUpperCase()}
                  className="h-6 w-6"
                />
                <span className="font-medium text-sm">
                  {selectedUser.name || selectedUser.username}
                </span>
                <div className="flex items-center gap-1 ml-auto">
                  <Shield className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Private</span>
                </div>
              </div>

              <div className="flex-1 overflow-hidden">
                <ChatMessageList className="h-full">
                  {messages.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Start a private conversation</p>
                      <p className="text-xs mt-1 flex items-center justify-center gap-1">
                        <Clock className="h-3 w-3" />
                        Messages expire after 5 minutes
                      </p>
                    </div>
                  ) : (
                    messages.map((msg: any) => {
                      const isFromSelectedUser = msg.fromUserId === selectedUser.id;
                      return (
                        <ChatBubble
                          key={msg.id}
                          variant={isFromSelectedUser ? 'received' : 'sent'}
                        >
                          <ChatBubbleAvatar 
                            fallback={isFromSelectedUser 
                              ? (selectedUser?.name || selectedUser?.username || 'U')[0]?.toUpperCase()
                              : (user?.user_metadata?.full_name || 'Y')[0]?.toUpperCase()
                            }
                            className="h-7 w-7"
                          />
                          <div className="flex flex-col gap-1">
                            <ChatBubbleMessage variant={isFromSelectedUser ? 'received' : 'sent'}>
                              {msg.message}
                            </ChatBubbleMessage>
                            <ChatBubbleTimestamp 
                              timestamp={`${new Date(msg.timestamp).toLocaleTimeString([], { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })} • ${getTimeRemaining(msg.timestamp)}`}
                            />
                          </div>
                        </ChatBubble>
                      );
                    })
                  )}
                </ChatMessageList>
              </div>

              <div className="p-3 border-t border-border">
                <div className="flex gap-2">
                  <ChatInput
                    placeholder="Type a private message..."
                    value={message}
                    onChange={(e: any) => setMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!message.trim()}
                    size="icon"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground mt-2 flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  End-to-end privacy • No server storage • Auto-expires
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Privacy Explanation Modal */}
      <Dialog open={showPrivacyModal} onOpenChange={setShowPrivacyModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Whispers Privacy & Security
            </DialogTitle>
            <DialogDescription>
              Understanding how your private messages work in CLEAR
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">No Server Storage</h4>
                  <p className="text-sm text-muted-foreground">
                    Messages are stored only in your browser's local storage. They never touch our servers.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">Auto-Expiration</h4>
                  <p className="text-sm text-muted-foreground">
                    All messages automatically expire and are deleted after 5 minutes for maximum privacy.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">Ephemeral Communication</h4>
                  <p className="text-sm text-muted-foreground">
                    Perfect for quick team coordination that doesn't need permanent records.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">Technical Details</h4>
                  <p className="text-sm text-muted-foreground">
                    Uses browser localStorage with timestamp-based TTL cleanup. Messages sync across your open tabs but remain isolated per user session.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <strong className="text-blue-800 dark:text-blue-200">Why Whispers?</strong>
                  <p className="text-blue-700 dark:text-blue-300">
                    For sensitive discussions, quick questions, or coordination that doesn't need to be part of the permanent project record.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="dont-show-again" 
                checked={dontShowAgain}
                onCheckedChange={(checked) => setDontShowAgain(checked === true)}
              />
              <label htmlFor="dont-show-again" className="text-sm text-muted-foreground">
                Don't show this again
              </label>
            </div>
            <Button onClick={handlePrivacyModalAccept}>
              Got it, Start Whispering
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 