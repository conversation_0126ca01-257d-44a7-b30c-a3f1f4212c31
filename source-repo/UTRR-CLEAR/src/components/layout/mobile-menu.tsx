'use client';

import { useState } from 'react';
import Link from 'next/link.js';
import { usePathname } from 'next/navigation';
import { useAuth } from '~/hooks/use-auth';
import { Button } from '~/components/ui/button';
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from '~/components/ui/sheet';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import {
  Menu,
  X,
  LayoutDashboard,
  FileText,
  Users,
  Calendar,
  CheckSquare,
  ShieldAlert,
  BarChart3,
  Database,
  Tags,
  HelpCircle,
  BookOpen,
  MessageSquare,
  User,
  Settings,
  LogOut,
} from 'lucide-react';
import { cn } from '~/lib/utils';

interface MenuItem {
  name: string;
  href: string;
  icon: React.ElementType;
  description?: string;
  active?: boolean;
  comingSoon?: boolean;
  children?: MenuItem[];
}

interface MobileMenuProps {
  onLogout: () => void;
}

export function MobileMenu({ onLogout }: MobileMenuProps) {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const { user } = useAuth();

  if (!user) return null;

  // Menu structure
  const menuSections = [
    {
      title: 'MyHUB',
      items: [
        {
          name: 'Dashboard',
          href: '/dashboard',
          icon: LayoutDashboard,
          active: pathname === '/' || pathname === '/dashboard',
        },
        {
          name: 'My Tasks',
          href: '/tasks',
          icon: CheckSquare,
          active: pathname === '/tasks',
          comingSoon: true,
        },
        {
          name: 'My Time',
          href: '/timesheet',
          icon: Calendar,
          active: pathname === '/timesheet',
        },
        {
          name: 'My Projects',
          href: '/my-projects',
          icon: FileText,
          active: pathname === '/my-projects',
        },
      ],
    },
    {
      title: 'Project Administration',
      items: [
        {
          name: 'Project Portfolio',
          href: '/projects',
          icon: FileText,
          active: pathname === '/projects' || pathname?.startsWith('/projects/'),
        },
        {
          name: 'Stakeholders',
          href: '/stakeholders',
          icon: Users,
          active: pathname === '/stakeholders',
        },
        {
          name: 'Sub Invoices',
          href: '/sub-invoices',
          icon: FileText,
          active: pathname === '/sub-invoices',
        },
      ],
    },
    {
      title: 'Help & Support',
      items: [
        {
          name: 'Knowledge Base',
          href: '/knowledge',
          icon: BookOpen,
          active: pathname === '/knowledge',
        },
        {
          name: 'Feature Requests',
          href: '/feature-requests',
          icon: MessageSquare,
          active: pathname === '/feature-requests' || pathname?.startsWith('/feature-requests/'),
        },
      ],
    },
  ];

  // Add admin section if user has appropriate role
  if (user.role === 'Admin' || user.role === 'Manager') {
    menuSections.push({
      title: 'Administration',
      items: [
        {
          name: 'User Management',
          href: '/admin/user-management',
          icon: Users,
          active: pathname === '/admin/user-management',
        },
        {
          name: 'Analytics',
          href: '/admin/analytics',
          icon: BarChart3,
          active: pathname === '/admin/analytics',
        },
        {
          name: 'Contract Admin',
          href: '/admin/contract-administration',
          icon: FileText,
          active: pathname === '/admin/contract-administration',
        },
        {
          name: 'Database',
          href: '/admin/database',
          icon: Database,
          active: pathname === '/admin/database',
        },
        {
          name: 'Versions',
          href: '/admin/versions',
          icon: Tags,
          active: pathname === '/admin/versions',
        },
      ],
    });
  }

  const handleItemClick = () => {
    setOpen(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <SheetHeader className="border-b p-4">
          <SheetTitle>Menu</SheetTitle>
        </SheetHeader>
        
        <ScrollArea className="h-[calc(100vh-8rem)]">
          <div className="p-4">
            {/* User Info */}
            <div className="mb-6 p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-medium">
                  {(user.user_metadata?.full_name?.[0] || user.email?.[0] || 'U').toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{user.user_metadata?.full_name || user.email}</p>
                  <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                </div>
              </div>
            </div>

            {/* Menu Sections */}
            <Accordion type="multiple" className="space-y-2">
              {menuSections.map((section, index) => (
                <AccordionItem key={section.title} value={`section-${index}`} className="border-none">
                  <AccordionTrigger className="hover:no-underline py-2">
                    <span className="text-sm font-medium">{section.title}</span>
                  </AccordionTrigger>
                  <AccordionContent className="pb-2">
                    <div className="space-y-1">
                      {section.items.map((item: any) => (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={handleItemClick}
                          className={cn(
                            'flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors',
                            item.active
                              ? 'bg-primary text-primary-foreground'
                              : 'hover:bg-muted',
                            item.comingSoon && 'opacity-50 cursor-not-allowed'
                          )}
                        >
                          <item.icon className="h-4 w-4" />
                          <span className="flex-1">{item.name}</span>
                          {item.comingSoon && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">
                              Soon
                            </span>
                          )}
                        </Link>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>

            {/* Quick Actions */}
            <div className="mt-6 pt-6 border-t space-y-2">
              <Link
                href="/profile"
                onClick={handleItemClick}
                className="flex items-center gap-3 px-3 py-2 rounded-md text-sm hover:bg-muted transition-colors"
              >
                <User className="h-4 w-4" />
                My Profile
              </Link>
              <Link
                href="/settings"
                onClick={handleItemClick}
                className="flex items-center gap-3 px-3 py-2 rounded-md text-sm hover:bg-muted transition-colors"
              >
                <Settings className="h-4 w-4" />
                Settings
              </Link>
              <button
                onClick={() => {
                  onLogout();
                  setOpen(false);
                }}
                className="flex items-center gap-3 px-3 py-2 rounded-md text-sm hover:bg-muted transition-colors w-full text-left text-destructive"
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </button>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}