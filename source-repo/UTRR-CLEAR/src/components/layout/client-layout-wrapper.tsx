'use client';

import { OrganizationThemeProvider } from '~/components/providers/organization-theme-provider';
import { KeyboardShortcutsProvider } from '~/hooks/use-keyboard-shortcuts';
import { CommandPaletteProvider } from '~/components/ui/command-palette';
import { CommentProvider } from '~/components/comments/comment-provider';
import { ContextMenuProvider } from '~/components/context-menu/context-menu-provider';
import { OrganizationSetupGuard } from '~/components/organization/organization-setup-guard';
import { CommentDrawer } from '~/components/comments/comment-drawer';
import { ErrorBoundary } from '~/components/ui/error-boundary';
import { AuthLoadingWrapper } from './auth-loading-wrapper';

export function ClientLayoutWrapper({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <AuthLoadingWrapper>
        <OrganizationThemeProvider>
          <KeyboardShortcutsProvider>
            <CommandPaletteProvider>
              <CommentProvider>
                <ContextMenuProvider>
                  <OrganizationSetupGuard>
                    {children}
                    <CommentDrawer />
                  </OrganizationSetupGuard>
                </ContextMenuProvider>
              </CommentProvider>
            </CommandPaletteProvider>
          </KeyboardShortcutsProvider>
        </OrganizationThemeProvider>
      </AuthLoadingWrapper>
    </ErrorBoundary>
  );
}