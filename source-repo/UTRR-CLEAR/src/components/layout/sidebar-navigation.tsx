'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '~/hooks/use-auth';
import {
  LayoutDashboard,
  FileText,
  Users,
  BookOpen,
  MessageSquare,
  Calendar,
  BarChart3,
  Settings,
  Building,
  Database,
  Tags,
  TrendingUp,
  ChevronRight,
  ChevronDown,
  Home,
  Clock,
  FolderOpen,
  HelpCircle,
  ShieldCheck,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Button } from '~/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '~/components/ui/collapsible';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  active?: boolean;
  badge?: string;
}

interface NavigationSection {
  title: string;
  items: NavigationItem[];
  adminOnly?: boolean;
  collapsible?: boolean;
}

export function SidebarNavigation() {
  const { user } = useAuth();
  const pathname = usePathname();
  const [adminSectionOpen, setAdminSectionOpen] = useState(false);
  const [helpSectionOpen, setHelpSectionOpen] = useState(false);

  const navigationSections: NavigationSection[] = [
    {
      title: 'Main',
      items: [
        {
          name: 'Dashboard',
          href: '/dashboard',
          icon: LayoutDashboard,
          description: 'Overview and key metrics',
          active: pathname === '/dashboard',
        },
        {
          name: 'Projects',
          href: '/projects',
          icon: FileText,
          description: 'Utility coordination projects',
          active: pathname === '/projects' || pathname?.startsWith('/projects/'),
        },
        {
          name: 'Timesheet',
          href: '/timesheet',
          icon: Clock,
          description: 'Track time and billable hours',
          active: pathname === '/timesheet',
        },
        {
          name: 'Stakeholders',
          href: '/stakeholders',
          icon: Users,
          description: 'Manage stakeholders',
          active: pathname === '/stakeholders',
        },
      ],
    },
    {
      title: 'Resources',
      items: [
        {
          name: 'Templates',
          href: '/templates',
          icon: FolderOpen,
          description: 'Project and document templates',
          active: pathname === '/templates' || pathname?.startsWith('/templates/'),
        },
        {
          name: 'Knowledge Base',
          href: '/knowledge',
          icon: BookOpen,
          description: 'Documentation and guides',
          active: pathname === '/knowledge',
        },
        {
          name: 'Feature Requests',
          href: '/feature-requests',
          icon: MessageSquare,
          description: 'Submit feedback and requests',
          active: pathname === '/feature-requests' || pathname?.startsWith('/feature-requests/'),
        },
      ],
    },
    {
      title: 'Administration',
      adminOnly: true,
      collapsible: true,
      items: [
        {
          name: 'Admin Dashboard',
          href: '/admin',
          icon: LayoutDashboard,
          description: 'Administrative overview',
          active: pathname === '/admin',
        },
        {
          name: 'Organization Settings',
          href: '/admin/organization-settings',
          icon: Building,
          description: 'Manage organization',
          active: pathname === '/admin/organization-settings',
        },
        {
          name: 'User Management',
          href: '/admin/user-management',
          icon: Users,
          description: 'Manage users and permissions',
          active: pathname === '/admin/user-management',
        },
        {
          name: 'Analytics',
          href: '/admin/analytics',
          icon: BarChart3,
          description: 'System analytics',
          active: pathname === '/admin/analytics',
        },
        {
          name: 'Performance',
          href: '/admin/performance',
          icon: TrendingUp,
          description: 'Performance monitoring',
          active: pathname === '/admin/performance',
        },
        {
          name: 'Database Management',
          href: '/admin/database-management',
          icon: Database,
          description: 'Database administration',
          active: pathname === '/admin/database-management',
        },
      ],
    },
  ];

  const isAdmin = user?.role === 'admin';

  const renderNavigationItem = (item: NavigationItem) => (
    <Link
      key={item.href}
      href={item.href}
      className={cn(
        'flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground',
        item.active
          ? 'bg-accent text-accent-foreground font-medium'
          : 'text-muted-foreground'
      )}
    >
      <item.icon className="h-4 w-4" />
      <span className="flex-1">{item.name}</span>
      {item.badge && (
        <span className="ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground">
          {item.badge}
        </span>
      )}
    </Link>
  );

  const renderNavigationSection = (section: NavigationSection) => {
    if (section.adminOnly && !isAdmin) {
      return null;
    }

    if (section.collapsible && section.title === 'Administration') {
      return (
        <Collapsible
          key={section.title}
          open={adminSectionOpen}
          onOpenChange={setAdminSectionOpen}
          className="space-y-2"
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-between px-3 py-2 text-left"
            >
              <span className="flex items-center gap-2">
                <ShieldCheck className="h-4 w-4" />
                {section.title}
              </span>
              {adminSectionOpen ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1 pl-3">
            {section.items.map(renderNavigationItem)}
          </CollapsibleContent>
        </Collapsible>
      );
    }

    return (
      <div key={section.title} className="space-y-2">
        <h4 className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
          {section.title}
        </h4>
        <div className="space-y-1">
          {section.items.map(renderNavigationItem)}
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full w-64 flex-col border-r bg-background">
      <div className="p-6">
        <Link href="/dashboard" className="flex items-center gap-2">
          <Home className="h-6 w-6" />
          <span className="text-lg font-semibold">CLEAR</span>
        </Link>
      </div>
      
      <ScrollArea className="flex-1 px-3">
        <div className="space-y-6 pb-6">
          {navigationSections.map(renderNavigationSection)}
        </div>
      </ScrollArea>
      
      <div className="border-t p-3">
        <div className="flex items-center gap-3 px-3 py-2 text-sm">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            {user?.email?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div className="flex-1 truncate">
            <p className="truncate font-medium">{user?.user_metadata?.full_name || 'User'}</p>
            <p className="truncate text-xs text-muted-foreground">{user?.role || 'user'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}