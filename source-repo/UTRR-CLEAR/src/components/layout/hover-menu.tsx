import * as React from 'react';
import Link from 'next/link.js';
import { cn } from '~/lib/utils';

interface HoverMenuProps {
  trigger: React.ReactNode;
  align?: 'start' | 'center' | 'end';
  width?: string;
  children: React.ReactNode;
}

export function HoverMenu({
  trigger,
  align = 'start',
  width = 'w-[200px]',
  children,
}: HoverMenuProps) {
  return (
    <div className="relative group">
      {trigger}
      <div
        className={cn(
          'absolute z-50 mt-1 invisible group-hover:visible opacity-0 group-hover:opacity-100 ',
          'transition-all duration-200 origin-top-left',
          'rounded-md border bg-popover text-popover-foreground shadow-md',
          width,
          {
            'left-0': align === 'start',
            'right-0': align === 'end',
            'left-1/2 -translate-x-1/2': align === 'center',
          }
        )}
      >
        {children}
      </div>
    </div>
  );
}
