import * as React from 'react';
import Link from 'next/link.js';
import { usePathname } from 'next/navigation';
import { useAuth } from '~/hooks/use-auth';
import { cn } from '~/lib/utils';
import {
  LayoutDashboard,
  FileText,
  Calendar,
  BookOpen,
  Settings,
  Users,
  BarChart,
  Database,
  LogOut,
  ShieldCheck,
  Lightbulb,
} from 'lucide-react';
import { Progress } from '~/components/ui/progress';

export function SidebarEnhanced() {
  const pathname = usePathname();
  const { user } = useAuth();

  // Determine if user has admin access
  const isAdmin = user && user.role === 'admin';

  // Simulate weekly hours data
  const weeklyHoursValue = 32.5;
  const weeklyHoursPercentage = Math.min(100, Math.round((weeklyHoursValue / 40) * 100));

  const { signOut } = useAuth();
  
  const handleLogout = () => {
    signOut();
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/',
      icon: LayoutDashboard,
      active: pathname === '/' || pathname === '/dashboard',
    },
    {
      name: 'Project Administration',
      href: '/projects',
      icon: FileText,
      active:
        pathname === '/projects' ||
        pathname.startsWith('/project/') ||
        pathname === '/stakeholders' ||
        pathname === '/sub-invoices',
    },
    {
      name: 'Stakeholders',
      href: '/stakeholders',
      icon: Users,
      active: pathname === '/stakeholders',
      indent: true,
    },
    {
      name: 'Sub Invoices',
      href: '/sub-invoices',
      icon: FileText,
      active: pathname === '/sub-invoices',
      indent: true,
    },
    {
      name: 'Timesheet',
      href: '/timesheet',
      icon: Calendar,
      active: pathname === '/timesheet',
    },
    {
      name: 'Feature Requests',
      href: '/feature-requests',
      icon: Lightbulb,
      active: pathname === '/feature-requests' || pathname.startsWith('/feature-request/'),
    },
    {
      name: 'Knowledge Base',
      href: '/knowledge',
      icon: BookOpen,
      active: pathname === '/knowledge',
    },
  ];

  // Admin navigation - only shown to admin/manager users
  const adminNavigation = [
    {
      name: 'User Management',
      href: '/admin/user-management',
      icon: Users,
      active: pathname === '/admin/user-management',
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart,
      active: pathname === '/admin/analytics',
    },
    {
      name: 'Contract Administration',
      href: '/admin/contract-administration',
      icon: FileText,
      active: pathname === '/admin/contract-administration',
    },
    {
      name: 'Database Management',
      href: '/admin/database',
      icon: Database,
      active: pathname === '/admin/database',
    },
  ];

  const secondaryNavigation = [
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      active: pathname === '/settings',
    },
  ];

  return (
    <aside className="w-64 bg-white border-r border-border p-4 hidden md:block h-[calc(100vh-57px)] overflow-y-auto">
      <nav className="space-y-1">
        {navigation.map((item: any) => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center space-x-2 text-sm px-3 py-2 rounded-md font-medium',
              item.active ? 'bg-primary text-primary-foreground' : 'hover:bg-muted text-foreground',
              item.indent ? 'ml-4' : ''
            )}
          >
            <item.icon className="h-4 w-4" />
            <span>{item.name}</span>
          </Link>
        ))}
      </nav>

      <hr className="my-4 border-border" />

      {/* Admin navigation section - only visible to users with Admin or Manager roles */}
      {isAdmin && (
        <>
          <div className="flex items-center justify-between py-2 px-3">
            <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
              Admin
            </h3>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </div>
          <nav className="space-y-1 mb-4">
            {adminNavigation.map((item: any) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center space-x-2 text-sm px-3 py-2 rounded-md font-medium',
                  item.active
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted text-foreground'
                )}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.name}</span>
              </Link>
            ))}
          </nav>
          <hr className="my-4 border-border" />
        </>
      )}

      <nav className="space-y-1">
        {secondaryNavigation.map((item: any) => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center space-x-2 text-sm px-3 py-2 rounded-md font-medium',
              item.active ? 'bg-primary text-primary-foreground' : 'hover:bg-muted text-foreground'
            )}
          >
            <item.icon className="h-4 w-4" />
            <span>{item.name}</span>
          </Link>
        ))}

        <button
          onClick={handleLogout}
          className="w-full flex items-center space-x-2 text-sm px-3 py-2 rounded-md hover:bg-muted text-foreground font-medium"
        >
          <LogOut className="h-4 w-4" />
          <span>Logout</span>
        </button>
      </nav>

      <div className="mt-6 pt-6 border-t border-border">
        <div className="bg-muted rounded-lg p-3">
          <h4 className="text-sm font-medium mb-2">Weekly Hours</h4>
          <div className="flex justify-between items-center">
            <div className="text-2xl font-bold">{weeklyHoursValue.toFixed(1)}</div>
            <div className="text-xs text-muted-foreground">of 40</div>
          </div>
          <Progress value={weeklyHoursPercentage} className="h-2 mt-2" />
        </div>
      </div>
    </aside>
  );
}
