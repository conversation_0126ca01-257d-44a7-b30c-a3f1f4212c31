"use client";

import * as React from "react";
import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { safeLog } from '~/lib/error-handler';

interface ShellProps {
  children: React.ReactNode;
}

export function Shell({ children }: ShellProps) {
  const pathname = usePathname();
  
  // Initialize analytics and track page views
  useEffect(() => {
    // Track page view when location changes
    if (pathname) {
      // Simple page view tracking - can be enhanced with analytics service
      safeLog.info(`Page view: ${pathname}`);
    }
    
    // Set up error tracking on global window object
    const originalOnError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
      // Track the error
      if (error && typeof message === 'string') {
        safeLog.error("Tracked error:", { message, error });
      }
      
      // Call the original error handler if it exists
      if (originalOnError) {
        return originalOnError.apply(this, arguments as any);
      }
      
      // Return false to let the default error handling happen
      return false;
    };
    
    // Clean up event listeners when component unmounts
    return () => {
      window.onerror = originalOnError;
    };
  }, [pathname]);
  
  return (
    <div className="w-full min-h-full bg-background">
      {children}
    </div>
  );
}
