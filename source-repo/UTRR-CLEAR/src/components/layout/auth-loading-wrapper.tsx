'use client';

import { useAuth } from '~/hooks/use-auth';
import { useEffect, useState } from 'react';
import { Skeleton } from '~/components/ui/skeleton';

export function AuthLoadingWrapper({ children }: { children: React.ReactNode }) {
  const { loading } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch by showing loading state until mounted
  if (!mounted || loading) {
    return (
      <div className="flex flex-col h-screen bg-background">
        <header className="bg-background border-b border-border sticky top-0 z-10 h-[57px]">
          <div className="container mx-auto px-2 py-3 flex justify-between items-center h-full">
            <Skeleton className="h-8 w-32" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
          </div>
        </header>
        <div className="flex flex-1 overflow-hidden">
          <div className="flex flex-col flex-1 overflow-hidden">
            <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="container mx-auto px-4 py-4">
                <Skeleton className="h-4 w-48" />
              </div>
            </div>
            <main className="flex-1 overflow-y-auto p-6">
              <Skeleton className="h-8 w-64 mb-4" />
              <Skeleton className="h-64 w-full" />
            </main>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}