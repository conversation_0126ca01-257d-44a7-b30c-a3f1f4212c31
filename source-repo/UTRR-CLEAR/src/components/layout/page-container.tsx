import * as React from 'react';
import { Breadcrumb } from '~/components/ui/breadcrumb';

interface PageContainerProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  showBreadcrumb?: boolean;
}

export function PageContainer({ title, description, icon, children, showBreadcrumb = true }: PageContainerProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        {showBreadcrumb && (
          <div className="container mx-auto px-4 py-2">
            <Breadcrumb />
          </div>
        )}
        <div className="bg-accent/30">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center space-x-2">
              {icon && <div className="text-primary">{icon}</div>}
              <div>
                <h1 className="text-2xl font-semibold">{title}</h1>
                {description && <p className="text-muted-foreground mt-1">{description}</p>}
              </div>
            </div>
          </div>
        </div>
        <main>{children}</main>
      </div>
    </div>
  );
}
