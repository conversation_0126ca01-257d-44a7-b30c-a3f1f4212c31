'use client';

import dynamic from 'next/dynamic';

// Use dynamic import to avoid SSR issues with the header
const Header = dynamic(() => import('./header'), {
  ssr: false,
  loading: () => (
    <header className="bg-background border-b border-border sticky top-0 z-10 h-[57px]">
      <div className="container mx-auto px-2 py-3 flex justify-between items-center h-full">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-muted rounded" />
        </div>
      </div>
    </header>
  ),
});

export default function HeaderWrapper() {
  return <Header />;
}