'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link.js';
import { useAuth } from '~/hooks/use-auth';
import { usePathname, useRouter } from 'next/navigation';
import { api } from '~/trpc/react';
import {
  ArrowLeft,
  Bell,
  ChevronDown,
  ChevronRight,
  FileText,
  HelpCircle,
  LogOut,
  MessageSquare,
  Send,
  Settings,
  User,
  LayoutDashboard,
  Calendar,
  BookOpen,
  ShieldAlert,
  Users,
  BarChart3,
  Database,
  Tags,
  CheckSquare,
  X,
  Mail,
  Building,
  TrendingUp,
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '~/components/ui/sheet';
import { cn } from '~/lib/utils';
import { Input } from '~/components/ui/input';
import { ScrollArea } from '~/components/ui/scroll-area';
import { ThemeToggle } from '~/components/ui/theme-toggle';
import { useTheme } from 'next-themes';
import { GlobalSearch } from '~/components/ui/global-search';
import EgisLogo from '~/components/ui/egis-logo';
import Image from 'next/image';
import { OrganizationLogoStatic } from '~/components/ui/organization-logo-static';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { LogoSkeletonHeader } from '~/components/ui/image-skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip';
import { NotificationsDropdown } from './notifications-dropdown';
import { MobileMenu } from './mobile-menu';
import { EmailClient } from '~/components/email/email-client-minimal';
import { safeLog } from '~/lib/error-handler';

// Import the new WhispersChat component
import { WhispersChat } from './whispers-chat';

// Old WhispersChat function removed - now using the new shadcn-chat based component

// EmailButton Component
function EmailButton() {
  const [mounted, setMounted] = useState(false);
  const { data: isEmailEnabled } = api.email.isEnabled.useQuery(undefined, {
    enabled: mounted,
  });
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Only show if email integration is enabled
  if (!mounted || !isEmailEnabled) {
    return null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative"
              >
                <Mail className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Email & Calendar</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] p-0">
        <EmailClient />
      </SheetContent>
    </Sheet>
  );
}

export default function Header() {
  const { session, user, signOut } = useAuth();
  // user is already available from useAuth, no need to extract from session
  const pathname = usePathname();
  const router = useRouter();
  const [unreadCount, setUnreadCount] = useState(0);
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Fetch organization data
  const { data: organization, isLoading: orgLoading } = api.organizations.getCurrent.useQuery(undefined, {
    enabled: !!user && mounted,
  });
  
  // Extract logo display settings from theme config
  const logoConfig = React.useMemo(() => {
    if ((organization as any)?.theme_config) {
      try {
        const themeConfig = (organization as any).theme_config;
        return themeConfig.logoDisplay?.headerLogo || {
          useDarkMode: false,
          scale: 0.75, // Default to 75% for header
        };
      } catch {
        // Fallback to defaults
      }
    }
    return {
      useDarkMode: false,
      scale: 0.75,
    };
  }, [organization]);

  // Clear all stored messages for current user (on logout)
  const clearAllStoredMessages = () => {
    if (typeof window !== 'undefined' && user?.id) {
      // Clear from localStorage instead of sessionStorage
      localStorage.removeItem('whispers_messages');
    }
  };

  const handleLogout = async () => {
    // Clear all stored messages before logout
    clearAllStoredMessages();
    
    // Use Supabase signOut from auth provider
    await signOut();
    // Router push is handled by the auth provider
  };

  // MyHub dropdown items
  const myHubItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      description: 'View your personalized dashboard',
      active: pathname === '/' || pathname === '/dashboard',
    },
    {
      name: 'My Tasks',
      href: '/tasks',
      icon: CheckSquare,
      description: 'Coming Soon: View and manage your assigned tasks',
      active: pathname === '/tasks',
      comingSoon: true,
    },
    {
      name: 'My Time',
      href: '/timesheet',
      icon: Calendar,
      description: 'Manage your timesheet entries',
      active: pathname === '/timesheet',
    },
    {
      name: 'My Projects',
      href: '/my-projects',
      icon: FileText,
      description: 'View your assigned projects',
      active: pathname === '/my-projects',
    },
  ];

  // Project Administration dropdown items
  const projectAdminItems = [
    {
      name: 'Project Portfolio',
      href: '/projects',
      icon: FileText,
      description: 'View and manage all utility coordination projects',
      active: pathname === '/projects' || pathname?.startsWith('/projects/'),
    },
    {
      name: 'Stakeholders',
      href: '/stakeholders',
      icon: Users,
      description: 'Manage client and utility company stakeholders',
      active: pathname === '/stakeholders',
    },
    {
      name: 'Sub Invoices',
      href: '/sub-invoices',
      icon: FileText,
      description: 'Manage sub-consultant invoices for all projects',
      active: pathname === '/sub-invoices',
    },
  ];

  // Admin dropdown items
  const adminItems = [
    {
      name: 'Admin Dashboard',
      href: '/admin',
      icon: LayoutDashboard,
      description: 'Administrative overview and quick actions',
      active: pathname === '/admin',
    },
    {
      name: 'Organization Settings',
      href: '/admin/organization-settings',
      icon: Building,
      description: 'Manage organization info, logos, and branding',
      active: pathname === '/admin/organization-settings',
    },
    {
      name: 'User Management',
      href: '/admin/user-management',
      icon: Users,
      description: 'Manage system users and permissions',
      active: pathname === '/admin/user-management',
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      description: 'View system-wide analytics and reports',
      active: pathname === '/admin/analytics',
    },
    {
      name: 'Performance',
      href: '/admin/performance',
      icon: TrendingUp,
      description: 'Monitor page load times and performance metrics',
      active: pathname === '/admin/performance',
    },
    {
      name: 'Contract Administration',
      href: '/admin/contract-administration',
      icon: FileText,
      description: 'Manage project fees, supplements, and sub-consultant invoices',
      active: pathname === '/admin/contract-administration',
    },
    {
      name: 'Database Management',
      href: '/admin/database',
      icon: Database,
      description: 'Manage and configure database settings',
      active: pathname === '/admin/database',
    },
    {
      name: 'Version Management',
      href: '/admin/versions',
      icon: Tags,
      description: 'Create and manage application versions and updates',
      active: pathname === '/admin/versions',
    },
  ];

  // Help dropdown items
  const helpItems = [
    {
      name: 'Knowledge Base',
      href: '/knowledge',
      icon: BookOpen,
      description: 'Browse articles and documentation',
      active: pathname === '/knowledge',
    },
    {
      name: 'Feature Requests',
      href: '/feature-requests',
      icon: MessageSquare,
      description: 'Submit and track feature requests, bugs, and issues',
      active: pathname === '/feature-requests' || pathname?.startsWith('/feature-requests/'),
    },
  ];

  // Return skeleton while mounting to prevent hydration errors
  if (!mounted) {
    return (
      <header className="bg-background border-b border-border sticky top-0 z-10 h-[57px]">
        <div className="container mx-auto px-2 py-3 flex justify-between items-center h-full">
          <div className="flex items-center space-x-6">
            <Link href="/" className="flex items-center cursor-pointer ml-0 pl-0">
              <div className="flex items-center gap-3">
                <div className="w-32 md:w-40 h-12 flex items-center -ml-2">
                  <OrganizationLogoStatic
                    className="w-full h-full object-contain"
                    scale={0.75}
                    darkBackground={theme === 'dark'}
                  />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="bg-background border-b border-border sticky top-0 z-10 h-[57px]">
      <div className="container mx-auto px-2 py-3 flex justify-between items-center h-full">
        <div className="flex items-center space-x-6">
          {/* Mobile Menu - Only visible on mobile */}
          {user && <MobileMenu onLogout={handleLogout} />}
          
          <Link href="/" className="flex items-center cursor-pointer ml-0 pl-0">
            <div className="flex items-center gap-3">
              <div className="w-32 md:w-40 h-12 flex items-center -ml-2">
                <OrganizationLogoStatic
                  className="w-full h-full object-contain"
                  scale={logoConfig.scale}
                  darkBackground={theme === 'dark'}
                />
              </div>
              {user && <RealtimeIndicator />}
            </div>
          </Link>

          {user && (
            <div className="hidden md:flex space-x-2">
              {/* MyHUB Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={myHubItems.some((item) => item.active) ? 'secondary' : 'ghost'}
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <LayoutDashboard className="h-4 w-4 mr-1" />
                    <span>MyHUB</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[350px]">
                  <DropdownMenuLabel>MyHUB</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {myHubItems.map((item: any) => (
                    <DropdownMenuItem key={item.href} asChild disabled={item.comingSoon}>
                      <Link href={item.href} className={cn(item.comingSoon && 'cursor-not-allowed')}>
                        <div className="flex items-center w-full">
                          <item.icon className="h-4 w-4 mr-3" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-muted-foreground">{item.description}</p>
                          </div>
                          {item.comingSoon && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full ml-2">
                              Soon
                            </span>
                          )}
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Project Administration Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={projectAdminItems.some((item) => item.active) ? 'secondary' : 'ghost'}
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    <span>Project Administration</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[350px]">
                  <DropdownMenuLabel>Project Administration</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {projectAdminItems.map((item: any) => (
                    <DropdownMenuItem key={item.href} asChild>
                      <Link href={item.href}>
                        <div className="flex items-center w-full">
                          <item.icon className="h-4 w-4 mr-3" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-muted-foreground">{item.description}</p>
                          </div>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Help Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={helpItems.some((item) => item.active) ? 'secondary' : 'ghost'}
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <HelpCircle className="h-4 w-4 mr-1" />
                    <span>Help</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[350px]">
                  <DropdownMenuLabel>Help & Support</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {helpItems.map((item: any) => (
                    <DropdownMenuItem key={item.href} asChild>
                      <Link href={item.href}>
                        <div className="flex items-center w-full">
                          <item.icon className="h-4 w-4 mr-3" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-muted-foreground">{item.description}</p>
                          </div>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-destructive cursor-pointer">
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Admin Button - Only visible to users with admin access */}
              {user?.isAdmin && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant={pathname?.startsWith('/admin') ? 'secondary' : 'ghost'}
                      size="sm"
                      className="flex items-center space-x-1"
                    >
                      <ShieldAlert className="h-4 w-4 mr-1" />
                      <span>Admin</span>
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-[350px]">
                    <DropdownMenuLabel>Administration</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {adminItems.map((item: any) => (
                      <DropdownMenuItem key={item.href} asChild>
                        <Link href={item.href}>
                          <div className="flex items-center w-full">
                            <item.icon className="h-4 w-4 mr-3" />
                            <div className="flex-1">
                              <p className="text-sm font-medium">{item.name}</p>
                              <p className="text-xs text-muted-foreground">{item.description}</p>
                            </div>
                          </div>
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          )}
        </div>

        {/* Global Search - Hidden on small mobile */}
        {user && (
          <div className="hidden sm:block flex-1 max-w-md mx-6">
            <GlobalSearch placeholder="Search projects, stakeholders..." />
          </div>
        )}

        {user && (
          <div className="flex items-center space-x-2 md:space-x-6">
            <div className="hidden sm:block">
              <WhispersChat onUnreadCountChange={setUnreadCount} />
            </div>

            <EmailButton />

            <NotificationsDropdown />

            <div className="hidden sm:block">
              <ThemeToggle variant="dropdown" size="sm" />
            </div>

            <div className="flex items-center space-x-2 border-l border-border pl-2 md:pl-6">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center space-x-2 cursor-pointer group">
                    <Avatar className="w-8 h-8 group-hover:ring-2 group-hover:ring-primary/50 transition-all">
                      <AvatarImage 
                        src={user?.user_metadata?.avatar_url || ''} 
                        alt={user?.user_metadata?.full_name || user?.email || 'User'} 
                      />
                      <AvatarFallback className="bg-primary text-white font-medium">
                        {(user?.user_metadata?.full_name?.[0] || user?.email?.[0] || 'U').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="hidden sm:block">
                      <div className="flex items-center">
                        <p className="text-sm font-medium">{user?.user_metadata?.full_name || user?.email}</p>
                        <ChevronDown className="h-3 w-3 ml-1" />
                      </div>
                      <p className="text-xs text-muted-foreground">{user?.role || 'User'}</p>
                    </div>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64" align="end">
                  <div className="flex items-center p-2">
                    <Avatar className="w-10 h-10 mr-3">
                      <AvatarImage 
                        src={user?.user_metadata?.avatar_url || ''} 
                        alt={user?.user_metadata?.full_name || user?.email || 'User'} 
                      />
                      <AvatarFallback className="bg-primary text-white font-medium">
                        {(user?.user_metadata?.full_name?.[0] || user?.email?.[0] || 'U').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-sm">{user?.user_metadata?.full_name || user?.email}</p>
                      <p className="text-xs text-muted-foreground">{user?.email}</p>
                      <p className="text-xs font-medium mt-0.5">{user?.role}</p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="cursor-pointer">
                      <User className="h-4 w-4 mr-2" />
                      My Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/notebook" className="cursor-pointer">
                      <FileText className="h-4 w-4 mr-2" />
                      My Notebook
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings" className="cursor-pointer">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="text-destructive focus:text-destructive cursor-pointer"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}