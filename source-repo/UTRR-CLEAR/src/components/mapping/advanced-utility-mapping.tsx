'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Slider } from '~/components/ui/slider';
import { Switch } from '~/components/ui/switch';
import { Checkbox } from '~/components/ui/checkbox';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import {
  MapPin,
  Layers,
  Eye,
  EyeOff,
  Settings,
  ZoomIn,
  ZoomOut,
  Maximize,
  Download,
  Upload,
  Save,
  Ruler,
  PenTool,
  Trash2,
  Undo,
  Redo,
  RotateCcw,
  Home,
  Filter,
  Box,
  AlertCircle,
  Router,
  Cable,
  Droplets,
  Flame,
  Phone,
  Info,
  Loader2,
} from 'lucide-react';
import { useToast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';

// OpenLayers imports
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import TileLayer from 'ol/layer/Tile.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import OSM from 'ol/source/OSM.js';
import { fromLonLat, toLonLat } from 'ol/proj.js';
import { Feature } from 'ol';
import { Point, LineString, Polygon } from 'ol/geom.js';
import { Style, Stroke, Fill, Circle as CircleStyle, Icon } from 'ol/style.js';
import { Draw, Modify, Snap, Select as OLSelect } from 'ol/interaction.js';
import { getArea, getLength } from 'ol/sphere.js';
import { defaults as defaultControls } from 'ol/control.js';

// Define utility types for styling
interface UtilityType {
  id: string;
  name: string;
  color: string;
  lineStyle: string;
  visible: boolean;
  icon: React.ReactNode;
  zIndex: number;
}

// Define drawing modes
type DrawingMode = 'Point' | 'LineString' | 'Polygon' | 'Circle' | null;

// Define measurement types
type MeasurementType = 'length' | 'area' | null;

interface AdvancedUtilityMappingProps {
  projectId?: string;
  height?: string;
  width?: string;
  showControls?: boolean;
  interactive?: boolean;
}

export function AdvancedUtilityMapping({
  projectId,
  height = '600px',
  width = '100%',
  showControls = true,
  interactive = true,
}: AdvancedUtilityMappingProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<Map | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [activeTab, setActiveTab] = useState('layers');
  const [drawingMode, setDrawingMode] = useState<DrawingMode>(null);
  const [measurementType, setMeasurementType] = useState<MeasurementType>(null);
  const [is3DMode, setIs3DMode] = useState(false);
  const [showConflicts, setShowConflicts] = useState(true);
  const [showMeasurements, setShowMeasurements] = useState(false);
  const { toast } = useToast();

  // Layer references
  const utilityLayerRef = useRef<VectorLayer<VectorSource> | null>(null);
  const drawLayerRef = useRef<VectorLayer<VectorSource> | null>(null);
  const conflictLayerRef = useRef<VectorLayer<VectorSource> | null>(null);

  // Interaction references
  const drawInteractionRef = useRef<Draw | null>(null);
  const modifyInteractionRef = useRef<Modify | null>(null);
  const snapInteractionRef = useRef<Snap | null>(null);
  const selectInteractionRef = useRef<OLSelect | null>(null);

  // Utility types configuration
  const [utilityTypes, setUtilityTypes] = useState<UtilityType[]>([
    {
      id: 'electric',
      name: 'Electric',
      color: '#FF0000',
      lineStyle: 'solid',
      visible: true,
      icon: <Router className="h-4 w-4" />,
      zIndex: 5,
    },
    {
      id: 'gas',
      name: 'Gas',
      color: '#FFFF00',
      lineStyle: 'dashed',
      visible: true,
      icon: <Flame className="h-4 w-4" />,
      zIndex: 4,
    },
    {
      id: 'water',
      name: 'Water',
      color: '#0000FF',
      lineStyle: 'solid',
      visible: true,
      icon: <Droplets className="h-4 w-4" />,
      zIndex: 3,
    },
    {
      id: 'sewer',
      name: 'Sewer',
      color: '#008000',
      lineStyle: 'solid',
      visible: true,
      icon: <Droplets className="h-4 w-4" />,
      zIndex: 2,
    },
    {
      id: 'telecom',
      name: 'Telecommunications',
      color: '#FF8C00',
      lineStyle: 'dotted',
      visible: true,
      icon: <Phone className="h-4 w-4" />,
      zIndex: 6,
    },
    {
      id: 'fiber',
      name: 'Fiber Optic',
      color: '#8A2BE2',
      lineStyle: 'dashed',
      visible: true,
      icon: <Cable className="h-4 w-4" />,
      zIndex: 7,
    },
  ]);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Create base layers
    const osmLayer = new TileLayer({
      source: new OSM(),
      zIndex: 0,
    });

    // Create utility layer
    const utilitySource = new VectorSource();
    const utilityLayer = new VectorLayer({
      source: utilitySource,
      zIndex: 1,
      style: (feature) => {
        const utilityType = feature.get('utilityType') as string;
        const type = utilityTypes.find((t: any) => t.id === utilityType);
        if (!type || !type.visible) return new Style();

        const color = type.color;
        const lineStyle = type.lineStyle;

        let lineDash: number[] = [];
        if (lineStyle === 'dashed') lineDash = [10, 5];
        else if (lineStyle === 'dotted') lineDash = [2, 2];

        return new Style({
          stroke: new Stroke({
            color: color,
            width: 3,
            lineDash: lineDash,
          }),
          fill: new Fill({
            color: color + '20', // 20% opacity
          }),
          image: new CircleStyle({
            radius: 6,
            fill: new Fill({ color: color }),
            stroke: new Stroke({ color: '#ffffff', width: 2 }),
          }),
        });
      },
    });
    utilityLayerRef.current = utilityLayer;

    // Create drawing layer
    const drawSource = new VectorSource();
    const drawLayer = new VectorLayer({
      source: drawSource,
      zIndex: 2,
      style: new Style({
        stroke: new Stroke({
          color: '#1976d2',
          width: 2,
        }),
        fill: new Fill({
          color: 'rgba(25, 118, 210, 0.1)',
        }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({ color: '#1976d2' }),
          stroke: new Stroke({ color: '#ffffff', width: 1 }),
        }),
      }),
    });
    drawLayerRef.current = drawLayer;

    // Create conflict layer
    const conflictSource = new VectorSource();
    const conflictLayer = new VectorLayer({
      source: conflictSource,
      zIndex: 3,
      style: new Style({
        image: new CircleStyle({
          radius: 8,
          fill: new Fill({ color: '#ff4444' }),
          stroke: new Stroke({ color: '#ffffff', width: 2 }),
        }),
      }),
    });
    conflictLayerRef.current = conflictLayer;

    // Create map
    const map = new Map({
      target: mapRef.current,
      layers: [osmLayer, utilityLayer, drawLayer, conflictLayer],
      view: new View({
        center: fromLonLat([-86.1581, 39.7684]), // Indianapolis coordinates
        zoom: 10,
      }),
      controls: defaultControls({
        attribution: false,
        zoom: false,
        rotate: false,
      }),
    });

    mapInstanceRef.current = map;
    setIsMapLoaded(true);

    // Add sample utility features
    addSampleUtilities();

    // Add sample conflicts
    addSampleConflicts();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setTarget(undefined);
        mapInstanceRef.current = null;
      }
    };
  }, [utilityTypes]);

  // Add sample utility data
  const addSampleUtilities = () => {
    if (!utilityLayerRef.current) return;

    const source = utilityLayerRef.current.getSource();
    if (!source) return;

    // Sample electric line
    const electricLine = new Feature({
      geometry: new LineString([
        fromLonLat([-86.16, 39.77]),
        fromLonLat([-86.15, 39.76]),
        fromLonLat([-86.14, 39.765]),
      ]),
      utilityType: 'electric',
      name: 'Duke Energy - Main Feeder',
      depth: 3.5,
      diameter: 6,
    });

    // Sample gas line
    const gasLine = new Feature({
      geometry: new LineString([
        fromLonLat([-86.158, 39.768]),
        fromLonLat([-86.155, 39.766]),
        fromLonLat([-86.152, 39.764]),
      ]),
      utilityType: 'gas',
      name: 'Citizens Gas - Distribution',
      depth: 4.0,
      diameter: 8,
    });

    // Sample water line
    const waterLine = new Feature({
      geometry: new LineString([
        fromLonLat([-86.162, 39.77]),
        fromLonLat([-86.156, 39.765]),
        fromLonLat([-86.15, 39.76]),
      ]),
      utilityType: 'water',
      name: 'Citizens Water - Main',
      depth: 5.0,
      diameter: 12,
    });

    source.addFeatures([electricLine, gasLine, waterLine]);
  };

  // Add sample conflicts
  const addSampleConflicts = () => {
    if (!conflictLayerRef.current) return;

    const source = conflictLayerRef.current.getSource();
    if (!source) return;

    // Sample conflict points
    const conflict1 = new Feature({
      geometry: new Point(fromLonLat([-86.155, 39.766])),
      conflictType: 'clearance',
      severity: 'high',
      description: 'Electric and gas lines too close - clearance issue',
    });

    const conflict2 = new Feature({
      geometry: new Point(fromLonLat([-86.152, 39.764])),
      conflictType: 'crossing',
      severity: 'medium',
      description: 'Water main crosses gas line',
    });

    source.addFeatures([conflict1, conflict2]);
  };

  // Toggle utility type visibility
  const toggleUtilityType = (typeId: string) => {
    setUtilityTypes((prev) =>
      prev.map((type: any) => (type.id === typeId ? { ...type, visible: !type.visible } : type))
    );

    // Refresh the layer
    if (utilityLayerRef.current) {
      utilityLayerRef.current.changed();
    }
  };

  // Start drawing
  const startDrawing = (mode: DrawingMode) => {
    if (!mapInstanceRef.current || !drawLayerRef.current) return;

    // Remove existing drawing interaction
    if (drawInteractionRef.current) {
      mapInstanceRef.current.removeInteraction(drawInteractionRef.current);
    }

    if (!mode) {
      setDrawingMode(null);
      return;
    }

    const source = drawLayerRef.current.getSource();
    if (!source) return;

    const drawInteraction = new Draw({
      source: source,
      type: mode === 'Circle' ? 'Circle' : mode,
    });

    drawInteraction.on('drawend', (event) => {
      const feature = event.feature;

      // Add metadata to the feature
      feature.setProperties({
        type: 'user_drawn',
        createdAt: new Date().toISOString(),
      });

      toast({
        title: 'Feature Added',
        description: `${mode} has been added to the map.`,
      });
    });

    mapInstanceRef.current.addInteraction(drawInteraction);
    drawInteractionRef.current = drawInteraction;
    setDrawingMode(mode);
  };

  // Start measurement
  const startMeasurement = (type: MeasurementType) => {
    if (!type) {
      setMeasurementType(null);
      return;
    }

    const mode: DrawingMode = type === 'length' ? 'LineString' : 'Polygon';
    startDrawing(mode);
    setMeasurementType(type);
    setShowMeasurements(true);
  };

  // Clear drawings
  const clearDrawings = () => {
    if (drawLayerRef.current) {
      const source = drawLayerRef.current.getSource();
      if (source) {
        source.clear();
      }
    }
    toast({
      title: 'Drawings Cleared',
      description: 'All drawings have been removed from the map.',
    });
  };

  // Zoom to home extent
  const zoomToHome = () => {
    if (!mapInstanceRef.current) return;

    const view = mapInstanceRef.current.getView();
    view.animate({
      center: fromLonLat([-86.1581, 39.7684]),
      zoom: 10,
      duration: 1000,
    });
  };

  // Zoom in
  const zoomIn = () => {
    if (!mapInstanceRef.current) return;
    const view = mapInstanceRef.current.getView();
    const zoom = view.getZoom();
    if (zoom !== undefined) {
      view.animate({ zoom: zoom + 1, duration: 250 });
    }
  };

  // Zoom out
  const zoomOut = () => {
    if (!mapInstanceRef.current) return;
    const view = mapInstanceRef.current.getView();
    const zoom = view.getZoom();
    if (zoom !== undefined) {
      view.animate({ zoom: zoom - 1, duration: 250 });
    }
  };

  // Export map data
  const exportMapData = () => {
    if (!utilityLayerRef.current || !drawLayerRef.current) return;

    const utilitySource = utilityLayerRef.current.getSource();
    const drawSource = drawLayerRef.current.getSource();

    if (!utilitySource || !drawSource) return;

    const data = {
      utilities: utilitySource.getFeatures().map((feature: any) => ({
        type: feature.getGeometry()?.getType(),
        coordinates: (feature.getGeometry() as any)?.getCoordinates(),
        properties: feature.getProperties(),
      })),
      drawings: drawSource.getFeatures().map((feature: any) => ({
        type: feature.getGeometry()?.getType(),
        coordinates: (feature.getGeometry() as any)?.getCoordinates(),
        properties: feature.getProperties(),
      })),
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `utility-map-${projectId || 'export'}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: 'Map data has been exported successfully.',
    });
  };

  // Undo last drawing action
  const handleUndo = () => {
    if (!drawLayerRef.current) return;
    
    const source = drawLayerRef.current.getSource();
    if (!source) return;
    
    const features = source.getFeatures();
    if (features.length > 0) {
      const lastFeature = features[features.length - 1];
      if (lastFeature) {
        source.removeFeature(lastFeature);
        toast({
          title: 'Undo',
          description: 'Last drawing action has been undone.',
        });
      }
    }
  };

  // Redo functionality (placeholder - would need to implement history tracking)
  const handleRedo = () => {
    toast({
      title: 'Redo',
      description: 'Redo functionality will be available in a future update.',
    });
  };

  // Handle file upload
  const handleUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.geojson';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const data = JSON.parse(event.target?.result as string);
          // TODO: Import the data to the map
          toast({
            title: 'Upload Complete',
            description: 'Map data has been uploaded successfully.',
          });
        } catch (error) {
          toast({
            title: 'Upload Failed',
            description: 'Failed to parse the uploaded file.',
            variant: 'destructive',
          });
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };

  // Save map data
  const handleSave = () => {
    // This would typically save to a backend/database
    toast({
      title: 'Save',
      description: 'Map data saved successfully.',
    });
  };

  // Toggle utility layer visibility
  const toggleUtilityLayer = (utilityId: string) => {
    setUtilityTypes((prev) =>
      prev.map((type: any) =>
        type.id === utilityId ? { ...type, visible: !type.visible } : type
      )
    );
    
    // Refresh the map layer
    if (utilityLayerRef.current) {
      utilityLayerRef.current.changed();
    }
  };

  return (
    <div className="relative w-full" style={{ height }}>
      {/* Map Container */}
      <div
        ref={mapRef}
        className="w-full h-full bg-gray-100 rounded-lg"
        style={{ width, height }}
      />

      {/* Loading Overlay */}
      {!isMapLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
          <div className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading map...</span>
          </div>
        </div>
      )}

      {/* Map Controls */}
      {showControls && isMapLoaded && (
        <>
          {/* Zoom Controls */}
          <div className="absolute top-4 right-4 flex flex-col gap-1">
            <Button size="sm" variant="outline" onClick={zoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={zoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={zoomToHome}>
              <Home className="h-4 w-4" />
            </Button>
          </div>

          {/* Drawing Tools */}
          {interactive && (
            <div className="absolute top-4 left-4">
              <Card className="w-64">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Map Tools</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="layers" className="text-xs">
                        Layers
                      </TabsTrigger>
                      <TabsTrigger value="draw" className="text-xs">
                        Draw
                      </TabsTrigger>
                      <TabsTrigger value="measure" className="text-xs">
                        Measure
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="layers" className="space-y-3 mt-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs font-medium">Utility Types</Label>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => setShowConflicts(!showConflicts)}
                          >
                            {showConflicts ? (
                              <Eye className="h-3 w-3" />
                            ) : (
                              <EyeOff className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        <ScrollArea className="h-40">
                          <div className="space-y-1">
                            {utilityTypes.map((type: any) => (
                              <div key={type.id} className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Checkbox
                                    checked={type.visible}
                                    onCheckedChange={() => toggleUtilityType(type.id)}
                                    className="h-3 w-3"
                                  />
                                  <div className="flex items-center gap-1">
                                    {type.icon}
                                    <span className="text-xs">{type.name}</span>
                                  </div>
                                </div>
                                <div
                                  className="w-3 h-3 rounded border"
                                  style={{ backgroundColor: type.color }}
                                />
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </div>

                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Show Conflicts</Label>
                        <Switch
                          checked={showConflicts}
                          onCheckedChange={setShowConflicts}
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="draw" className="space-y-3 mt-3">
                      <div className="grid grid-cols-2 gap-1">
                        <Button
                          size="sm"
                          variant={drawingMode === 'Point' ? 'default' : 'outline'}
                          onClick={() => startDrawing(drawingMode === 'Point' ? null : 'Point')}
                        >
                          <MapPin className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant={drawingMode === 'LineString' ? 'default' : 'outline'}
                          onClick={() =>
                            startDrawing(drawingMode === 'LineString' ? null : 'LineString')
                          }
                        >
                          <PenTool className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant={drawingMode === 'Polygon' ? 'default' : 'outline'}
                          onClick={() => startDrawing(drawingMode === 'Polygon' ? null : 'Polygon')}
                        >
                          <Box className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant={drawingMode === 'Circle' ? 'default' : 'outline'}
                          onClick={() => startDrawing(drawingMode === 'Circle' ? null : 'Circle')}
                        >
                          <div className="w-3 h-3 rounded-full border border-current" />
                        </Button>
                      </div>

                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={clearDrawings}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleUndo}>
                          <Undo className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleRedo}>
                          <Redo className="h-3 w-3" />
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="measure" className="space-y-3 mt-3">
                      <div className="space-y-2">
                        <Button
                          size="sm"
                          variant={measurementType === 'length' ? 'default' : 'outline'}
                          onClick={() =>
                            startMeasurement(measurementType === 'length' ? null : 'length')
                          }
                          className="w-full justify-start"
                        >
                          <Ruler className="h-3 w-3 mr-2" />
                          Measure Length
                        </Button>
                        <Button
                          size="sm"
                          variant={measurementType === 'area' ? 'default' : 'outline'}
                          onClick={() =>
                            startMeasurement(measurementType === 'area' ? null : 'area')
                          }
                          className="w-full justify-start"
                        >
                          <Box className="h-3 w-3 mr-2" />
                          Measure Area
                        </Button>
                      </div>

                      {showMeasurements && (
                        <div className="space-y-1">
                          <Label className="text-xs">Measurements</Label>
                          <div className="text-xs text-muted-foreground">
                            Click on the map to start measuring
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>

                  <div className="flex justify-between items-center mt-3 pt-2 border-t">
                    <Button size="sm" variant="outline" onClick={exportMapData}>
                      <Download className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleUpload}>
                      <Upload className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleSave}>
                      <Save className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Status Bar */}
          <div className="absolute bottom-4 left-4 right-4">
            <Card>
              <CardContent className="py-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-4">
                    <span>
                      Utilities: {utilityTypes.filter((t: any) => t.visible).length}/
                      {utilityTypes.length}
                    </span>
                    {showConflicts && <span className="text-red-600">Conflicts: 2</span>}
                    {drawingMode && (
                      <Badge variant="secondary" className="text-xs">
                        Drawing: {drawingMode}
                      </Badge>
                    )}
                    {measurementType && (
                      <Badge variant="secondary" className="text-xs">
                        Measuring: {measurementType}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {is3DMode && (
                      <Badge variant="outline" className="text-xs">
                        3D Mode
                      </Badge>
                    )}
                    <span className="text-muted-foreground">
                      Coordinate System: Indiana State Plane
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
