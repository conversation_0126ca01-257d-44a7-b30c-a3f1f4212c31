'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import { Slider } from '~/components/ui/slider';
import { MapPin, Layers, Settings, Info } from 'lucide-react';

// CAD Components
import CadToolbar from './cad-ui/cad-toolbar';
import { CadCommand, CadMode } from '~/hooks/use-cad-shortcuts';
import { useSnap } from '~/hooks/use-snap';
import SnapManager, { SnapType } from './precision-tools/snap-manager';
import MeasurementManager from './measurement-tools/measurement-manager';
import OrthoMode from './drawing-modes/ortho-mode';
import AdvancedSnapping from './precision-tools/advanced-snapping';

interface CadIntegrationExampleProps {
  className?: string;
}

export const CadIntegrationExample: React.FC<CadIntegrationExampleProps> = ({
  className = ""
}) => {
  // State management
  const [activeTool, setActiveTool] = useState<CadCommand | null>(null);
  const [activePoint, setActivePoint] = useState<[number, number] | null>(null);
  const [measurementMode, setMeasurementMode] = useState<string>('distance');
  const [snapSettings, setSnapSettings] = useState({
    enabled: true,
    tolerance: 10,
    showIndicators: true
  });
  const [orthoSettings, setOrthoSettings] = useState({
    enabled: false,
    angle: 90
  });
  const [gridSettings, setGridSettings] = useState({
    visible: false,
    size: 10
  });

  // Refs for managers
  const snapManagerRef = useRef<SnapManager | null>(null);
  const measurementManagerRef = useRef<MeasurementManager | null>(null);
  const orthoModeRef = useRef<OrthoMode | null>(null);
  const advancedSnappingRef = useRef<AdvancedSnapping | null>(null);
  const mapRef = useRef<HTMLDivElement>(null);

  // Mock drawing canvas state
  const [drawnFeatures, setDrawnFeatures] = useState<Array<{
    id: string;
    type: string;
    coordinates: number[][];
    properties: Record<string, any>;
  }>>([]);

  // Initialize CAD managers
  useEffect(() => {
    // Initialize snap manager
    if (!snapManagerRef.current) {
      snapManagerRef.current = new SnapManager();
    }

    // Initialize ortho mode
    if (!orthoModeRef.current) {
      orthoModeRef.current = new OrthoMode({
        enabled: orthoSettings.enabled,
        constraintAngles: [0, 45, 90, 135, 180, 225, 270, 315],
        tolerance: 5
      });
    }

    // Initialize advanced snapping
    if (!advancedSnappingRef.current) {
      advancedSnappingRef.current = new AdvancedSnapping();
    }

    // Mock measurement manager initialization
    // In real implementation, this would connect to actual vector source
    
  }, []);

  // Custom snap hook integration
  const {
    snap,
    updateSettings: updateSnapSettings,
    toggleSnapType,
    isSnapTypeEnabled,
    getSnapInfo
  } = useSnap({
    enabled: snapSettings.enabled,
    initialSettings: {
      tolerance: snapSettings.tolerance,
      showIndicators: snapSettings.showIndicators
    }
  });

  // Handle tool changes
  const handleToolChange = useCallback((tool: CadCommand | null) => {
    setActiveTool(tool);
    console.log('Active tool changed to:', tool);
    
    // Reset measurement mode when tool changes
    if (tool?.startsWith('MEASURE_')) {
      setMeasurementMode(tool.replace('MEASURE_', '').toLowerCase());
    }
  }, []);

  // Handle mode toggles
  const handleModeToggle = useCallback((mode: CadMode, enabled: boolean) => {
    console.log(`Mode ${mode} toggled:`, enabled);
    
    switch (mode) {
      case CadMode.ORTHO:
        setOrthoSettings(prev => ({ ...prev, enabled }));
        if (orthoModeRef.current) {
          if (enabled) {
            orthoModeRef.current.enable();
          } else {
            orthoModeRef.current.disable();
          }
        }
        break;
        
      case CadMode.SNAP:
        setSnapSettings(prev => ({ ...prev, enabled }));
        updateSnapSettings({ enabled });
        break;
        
      case CadMode.GRID:
        setGridSettings(prev => ({ ...prev, visible: enabled }));
        break;
        
      case CadMode.POLAR_TRACKING:
        if (orthoModeRef.current) {
          orthoModeRef.current.setPolarTrackingEnabled(enabled);
        }
        break;
    }
  }, [updateSnapSettings]);

  // Handle coordinate input
  const handleCoordinateInput = useCallback((coordinate: [number, number]) => {
    setActivePoint(coordinate);
    console.log('Coordinate input:', coordinate);

    // Apply snapping if enabled
    if (snapSettings.enabled && snapManagerRef.current) {
      // Mock map object for snapping
      const mockMap = {
        getPixelFromCoordinate: (coord: any) => [coord[0] * 2, coord[1] * 2]
      };
      
      const snapResult = snap(coordinate, mockMap);
      if (snapResult.snapped) {
        console.log('Snapped to:', snapResult.snappedCoordinate);
        setActivePoint([snapResult.snappedCoordinate[0] ?? 0, snapResult.snappedCoordinate[1] ?? 0]);
      }
    }

    // Apply ortho constraints if enabled
    if (orthoSettings.enabled && orthoModeRef.current && activePoint) {
      const constraintResult = orthoModeRef.current.constrainPoint(coordinate, activePoint);
      if (constraintResult.constrained) {
        console.log('Ortho constrained to:', constraintResult.constrainedCoordinate);
        setActivePoint([constraintResult.constrainedCoordinate[0] ?? 0, constraintResult.constrainedCoordinate[1] ?? 0]);
      }
    }

    // Handle tool-specific actions
    if (activeTool) {
      handleToolAction(activeTool, coordinate);
    }
  }, [activeTool, activePoint, snapSettings.enabled, orthoSettings.enabled, snap]);

  // Handle tool-specific actions
  const handleToolAction = useCallback((tool: CadCommand, coordinate: [number, number]) => {
    const newFeature = {
      id: `feature_${Date.now()}`,
      type: tool,
      coordinates: [coordinate],
      properties: {
        createdAt: new Date().toISOString(),
        tool
      }
    };

    switch (tool) {
      case CadCommand.LINE:
        if (activePoint) {
          newFeature.coordinates = [activePoint, coordinate];
          setDrawnFeatures(prev => [...prev, newFeature]);
          setActivePoint(null);
        }
        break;
        
      case CadCommand.CIRCLE:
        if (activePoint) {
          const radius = Math.sqrt(
            Math.pow(coordinate[0] - activePoint[0], 2) + 
            Math.pow(coordinate[1] - activePoint[1], 2)
          );
          (newFeature.properties as any).center = activePoint;
          (newFeature.properties as any).radius = radius;
          setDrawnFeatures(prev => [...prev, newFeature]);
          setActivePoint(null);
        }
        break;
        
      case CadCommand.RECTANGLE:
        if (activePoint) {
          newFeature.coordinates = [
            activePoint,
            [coordinate[0], activePoint[1]],
            coordinate,
            [activePoint[0], coordinate[1]],
            activePoint
          ];
          setDrawnFeatures(prev => [...prev, newFeature]);
          setActivePoint(null);
        }
        break;
        
      case CadCommand.TRIM:
      case CadCommand.EXTEND:
      case CadCommand.MIRROR:
      case CadCommand.SCALE:
        // These tools require special handling with the CAD tool manager
        console.log(`Advanced tool ${tool} requires CAD tool manager integration`);
        if (!activePoint) {
          setActivePoint(coordinate);
        } else {
          // Simulated action for demo
          console.log(`${tool} operation from`, activePoint, 'to', coordinate);
          setActivePoint(null);
        }
        break;
        
      default:
        console.log(`Tool ${tool} action not implemented`);
    }
  }, [activePoint]);

  // Mock snap type toggles
  const handleSnapTypeToggle = useCallback((snapType: SnapType) => {
    toggleSnapType(snapType);
    console.log(`Snap type ${snapType} toggled`);
  }, [toggleSnapType]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            CAD-Style Precision Tools Integration Example
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This demonstrates the integration of CAD-style tools for professional utility coordination. 
              All tools support INDOT CAD standards for precision engineering workflows.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* CAD Toolbar */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">CAD Toolbar</CardTitle>
            </CardHeader>
            <CardContent>
              <CadToolbar
                onToolChange={handleToolChange}
                onModeToggle={handleModeToggle}
                onCoordinateInput={handleCoordinateInput}
                orientation="horizontal"
                compact={false}
              />
            </CardContent>
          </Card>

          {/* Mock Drawing Canvas */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-sm">Drawing Canvas (Mock)</CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                ref={mapRef}
                className="h-64 bg-gray-50 border-2 border-dashed border-gray-300 relative overflow-hidden cursor-crosshair"
                onClick={(e: any) => {
                  const rect = e.currentTarget.getBoundingClientRect();
                  const x = e.clientX - rect.left;
                  const y = e.clientY - rect.top;
                  handleCoordinateInput([x, y]);
                }}
              >
                {/* Grid overlay */}
                {gridSettings.visible && (
                  <div className="absolute inset-0 opacity-30">
                    <svg width="100%" height="100%">
                      <defs>
                        <pattern id="grid" width={gridSettings.size} height={gridSettings.size} patternUnits="userSpaceOnUse">
                          <path d={`M ${gridSettings.size} 0 L 0 0 0 ${gridSettings.size}`} fill="none" stroke="gray" strokeWidth="0.5"/>
                        </pattern>
                      </defs>
                      <rect width="100%" height="100%" fill="url(#grid)" />
                    </svg>
                  </div>
                )}

                {/* Drawn features */}
                {drawnFeatures.map((feature, index) => (
                  <div key={feature.id} className="absolute">
                    {feature.type === CadCommand.LINE && (
                      <svg className="absolute inset-0" width="100%" height="100%">
                        <line
                          x1={feature.coordinates[0]?.[0] ?? 0}
                          y1={feature.coordinates[0]?.[1] ?? 0}
                          x2={feature.coordinates[1]?.[0] ?? 0}
                          y2={feature.coordinates[1]?.[1] ?? 0}
                          stroke="blue"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                    {feature.type === CadCommand.CIRCLE && feature.properties.center && (
                      <svg className="absolute inset-0" width="100%" height="100%">
                        <circle
                          cx={feature.properties.center[0]}
                          cy={feature.properties.center[1]}
                          r={feature.properties.radius}
                          fill="none"
                          stroke="green"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                    {feature.type === CadCommand.RECTANGLE && (
                      <svg className="absolute inset-0" width="100%" height="100%">
                        <polygon
                          points={feature.coordinates.map(coord => `${coord[0]},${coord[1]}`).join(' ')}
                          fill="none"
                          stroke="red"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </div>
                ))}

                {/* Active point indicator */}
                {activePoint && (
                  <div 
                    className="absolute w-2 h-2 bg-yellow-500 border border-yellow-700 rounded-full transform -translate-x-1 -translate-y-1"
                    style={{ left: activePoint[0], top: activePoint[1] }}
                  />
                )}

                {/* Instructions */}
                <div className="absolute top-2 left-2 bg-white/90 p-2 rounded text-xs">
                  {activeTool ? (
                    <span>Click to use {activeTool.toLowerCase()} tool</span>
                  ) : (
                    <span>Select a tool from the toolbar above</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Panel */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Settings className="h-4 w-4" />
                CAD Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="snap" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="snap" className="text-xs">Snap</TabsTrigger>
                  <TabsTrigger value="ortho" className="text-xs">Ortho</TabsTrigger>
                  <TabsTrigger value="grid" className="text-xs">Grid</TabsTrigger>
                </TabsList>
                
                <TabsContent value="snap" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="snap-enabled">Enable Snapping</Label>
                      <Switch
                        id="snap-enabled"
                        checked={snapSettings.enabled}
                        onCheckedChange={(enabled) => {
                          setSnapSettings(prev => ({ ...prev, enabled }));
                          updateSnapSettings({ enabled });
                        }}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Snap Tolerance: {snapSettings.tolerance}px</Label>
                      <Slider
                        value={[snapSettings.tolerance]}
                        onValueChange={([value]) => {
                          const tolerance = value ?? 10;
                          setSnapSettings(prev => ({ ...prev, tolerance }));
                          updateSnapSettings({ tolerance });
                        }}
                        max={50}
                        min={1}
                        step={1}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Snap Types</Label>
                      {Object.values(SnapType).map(snapType => (
                        <div key={snapType} className="flex items-center justify-between">
                          <span className="text-xs">{snapType}</span>
                          <Switch
                            checked={isSnapTypeEnabled(snapType)}
                            onCheckedChange={() => handleSnapTypeToggle(snapType)}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="ortho" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="ortho-enabled">Enable Ortho Mode</Label>
                      <Switch
                        id="ortho-enabled"
                        checked={orthoSettings.enabled}
                        onCheckedChange={(enabled) => {
                          setOrthoSettings(prev => ({ ...prev, enabled }));
                          handleModeToggle(CadMode.ORTHO, enabled);
                        }}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Constraint Angle: {orthoSettings.angle}°</Label>
                      <Slider
                        value={[orthoSettings.angle]}
                        onValueChange={([value]) => {
                          const angle = value ?? 90;
                          setOrthoSettings(prev => ({ ...prev, angle }));
                        }}
                        max={360}
                        min={0}
                        step={15}
                      />
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="grid" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="grid-visible">Show Grid</Label>
                      <Switch
                        id="grid-visible"
                        checked={gridSettings.visible}
                        onCheckedChange={(visible) => {
                          setGridSettings(prev => ({ ...prev, visible }));
                          handleModeToggle(CadMode.GRID, visible);
                        }}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Grid Size: {gridSettings.size}px</Label>
                      <Slider
                        value={[gridSettings.size]}
                        onValueChange={([value]) => {
                          const size = value ?? 10;
                          setGridSettings(prev => ({ ...prev, size }));
                        }}
                        max={50}
                        min={5}
                        step={5}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Status Display */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-sm">Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs">Active Tool:</span>
                <Badge variant={activeTool ? "default" : "outline"}>
                  {activeTool || 'None'}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-xs">Current Point:</span>
                <span className="text-xs font-mono">
                  {activePoint ? `${activePoint[0].toFixed(1)}, ${activePoint[1].toFixed(1)}` : 'None'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-xs">Features Drawn:</span>
                <Badge variant="outline">{drawnFeatures.length}</Badge>
              </div>
              
              <div className="space-y-1">
                <span className="text-xs font-medium">Modes:</span>
                <div className="flex flex-wrap gap-1">
                  <Badge variant={snapSettings.enabled ? "default" : "outline"} className="text-xs">
                    Snap {snapSettings.enabled ? 'ON' : 'OFF'}
                  </Badge>
                  <Badge variant={orthoSettings.enabled ? "default" : "outline"} className="text-xs">
                    Ortho {orthoSettings.enabled ? 'ON' : 'OFF'}
                  </Badge>
                  <Badge variant={gridSettings.visible ? "default" : "outline"} className="text-xs">
                    Grid {gridSettings.visible ? 'ON' : 'OFF'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Feature List */}
          {drawnFeatures.length > 0 && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Layers className="h-4 w-4" />
                  Drawn Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {drawnFeatures.map((feature, index) => (
                    <div key={feature.id} className="flex justify-between items-center text-xs">
                      <span>{feature.type} {index + 1}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setDrawnFeatures(prev => prev.filter(f => f.id !== feature.id));
                        }}
                        className="text-xs px-2 py-1 h-auto"
                      >
                        Delete
                      </Button>
                    </div>
                  ))}
                </div>
                
                {drawnFeatures.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDrawnFeatures([])}
                    className="w-full mt-2 text-xs"
                  >
                    Clear All
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default CadIntegrationExample;