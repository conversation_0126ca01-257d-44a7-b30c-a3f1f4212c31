'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';
import * as THREE from 'three';
import { useFrame, useThree } from '@react-three/fiber';
// Text component from drei removed - using Three.js sprites for better type safety

interface SubsurfaceVisualizationProps {
  width?: number;
  depth?: number;
  mode: 'full3d' | 'cross_section';
  soilLayers?: {
    name: string;
    thickness: number;
    color: string;
    description?: string;
    material?: 'clay' | 'sand' | 'rock' | 'soil' | 'mixed';
    density?: number; // Soil density (g/cm³)
    permeability?: number; // Permeability coefficient
  }[];
  pavement?: {
    thickness: number;
    color: string;
    type?: 'asphalt' | 'concrete' | 'gravel';
  };
  groundwaterLevel?: number;
  transparentSurface?: boolean;
  showLabels?: boolean;
  geologyData?: {
    bedrockDepth?: number;
    bedrockType?: string;
    faultLines?: [number, number, number][][];
  };
}

// Placeholder functions for missing imports
const createCrossSectionMesh = (width: number, depth: number) => {
  // Placeholder implementation
  return null;
};

const createTerrainMesh = (width: number, depth: number): THREE.Mesh => {
  // Create a plane geometry for the terrain surface
  const geometry = new THREE.PlaneGeometry(width, depth, 32, 32);
  
  // Create a standard material for the terrain
  const material = new THREE.MeshStandardMaterial({
    color: '#8b7355', // Default terrain color
    roughness: 0.9,
    metalness: 0.1,
  });
  
  // Create the mesh
  const mesh = new THREE.Mesh(geometry, material);
  
  // Rotate to be horizontal (plane is vertical by default)
  mesh.rotation.x = -Math.PI / 2;
  
  // Enable shadow receiving
  mesh.receiveShadow = true;
  mesh.castShadow = false; // Terrain typically doesn't cast shadows
  
  return mesh;
};

/**
 * Component for visualizing subsurface conditions in the 3D view
 */
export function SubsurfaceVisualization({
  width = 100,
  depth = 15,
  mode = 'full3d',
  soilLayers,
  pavement,
  groundwaterLevel,
  transparentSurface = false,
  showLabels = true,
  geologyData,
}: SubsurfaceVisualizationProps) {
  const groupRef = useRef<THREE.Group>(null);
  const terrainRef = useRef<THREE.Mesh>(null);
  const crossSectionRef = useRef<THREE.Mesh>(null);
  const groundwaterRef = useRef<THREE.Mesh>(null);
  const soilLayersRef = useRef<THREE.Group>(null);
  const [hoveredLayer, setHoveredLayer] = useState<string | null>(null);

  // Default soil layers if none provided
  const defaultSoilLayers = useMemo(() => [
    {
      name: 'Topsoil',
      thickness: 0.5,
      color: '#5b4120',
      material: 'soil' as const,
      description: 'Organic-rich top layer',
    },
    {
      name: 'Subsoil',
      thickness: 1.0,
      color: '#7f5d2d',
      material: 'soil' as const,
      description: 'Clay and mineral subsoil layer',
    },
    {
      name: 'Clay',
      thickness: 2.5,
      color: '#9a764a',
      material: 'clay' as const,
      description: 'Dense clay layer',
    },
    {
      name: 'Sandy Clay',
      thickness: 3.0,
      color: '#a3856c',
      material: 'mixed' as const,
      description: 'Mixed clay and sand layer',
    },
    {
      name: 'Sand',
      thickness: 4.0,
      color: '#d6c2a0',
      material: 'sand' as const,
      description: 'Coarse sand with high permeability',
    },
    {
      name: 'Bedrock',
      thickness: 4.0,
      color: '#8c8c8c',
      material: 'rock' as const,
      description: 'Solid bedrock foundation',
    },
  ], []);

  // Create the subsurface visualization based on mode
  useEffect(() => {
    if (!groupRef.current) return;

    // Clear previous meshes
    if (groupRef.current && groupRef.current.children.length > 0) {
      const firstChild = groupRef.current.children[0];
      if (firstChild) {
        groupRef.current.remove(firstChild);
      }
    }

    if (mode === 'full3d') {
      // Create terrain mesh with improved appearance
      const terrain = createTerrainMesh(width, width);
      if (terrain) {
        terrain.receiveShadow = true;

        // Make terrain semi-transparent if needed
        if (transparentSurface) {
          (terrain.material as THREE.MeshStandardMaterial).transparent = true;
          (terrain.material as THREE.MeshStandardMaterial).opacity = 0.4;
        } else {
          // Apply a terrain-like texture
          (terrain.material as THREE.MeshStandardMaterial).color = new THREE.Color('#8b7355');
          (terrain.material as THREE.MeshStandardMaterial).roughness = 0.9;
          (terrain.material as THREE.MeshStandardMaterial).metalness = 0.1;
        }

        // Add terrain to the group
        if (groupRef.current) {
          groupRef.current.add(terrain);
        }
        terrainRef.current = terrain;
      }

      // Add soil cutaway slice if terrain is transparent
      if (transparentSurface) {
        const layers = soilLayers || defaultSoilLayers;
        soilLayersRef.current = new THREE.Group();

        // Create soil layers in 3D
        let currentDepth = 0;
        layers.forEach((layer, index) => {
          // Calculate dimensions and position for this layer
          // Create a rectangular slice that shows where different soil layers are
          const sliceWidth = width / 3;
          const sliceLength = width / 3;

          // Create geometry for soil layer
          const layerGeometry = new THREE.BoxGeometry(sliceWidth, layer.thickness, sliceLength);
          const layerMaterial = new THREE.MeshStandardMaterial({
            color: layer.color,
            transparent: true,
            opacity: 0.85,
            roughness: layer.material === 'rock' ? 0.9 : layer.material === 'clay' ? 0.7 : 0.5,
            metalness: layer.material === 'rock' ? 0.3 : 0.1,
          });

          const layerMesh = new THREE.Mesh(layerGeometry, layerMaterial);

          // Position soil layer slice at back corner of terrain
          layerMesh.position.set(
            -width / 4, // Offset from center
            -currentDepth - layer.thickness / 2, // Depth position
            -width / 4 // Offset from center
          );

          // Add to layers group
          if (soilLayersRef.current) {
            soilLayersRef.current.add(layerMesh);
          }

          // Add layer label if enabled
          if (showLabels) {
            const labelPosition: [number, number, number] = [
              -width / 4 - sliceWidth / 2 + 0.5, // Left edge with small margin
              -currentDepth - layer.thickness / 2, // Center of layer
              -width / 4, // Aligned with the layer front
            ];

            // Create a text sprite for the label
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 256;
            canvas.height = 64;
            
            if (context) {
              context.fillStyle = 'rgba(0, 0, 0, 0.7)';
              context.fillRect(0, 0, canvas.width, canvas.height);
              context.fillStyle = '#ffffff';
              context.font = '24px Arial';
              context.fillText(layer.name, 10, 40);
            }
            
            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
            const label = new THREE.Sprite(spriteMaterial);
            label.scale.set(2, 0.5, 1);
            
            // Position the sprite
            label.position.set(labelPosition[0], labelPosition[1], labelPosition[2]);

            // Add to soil layers group
            if (soilLayersRef.current) {
              soilLayersRef.current.add(label);
            }
          }

          // Update current depth for next layer
          currentDepth += layer.thickness;
        });

        // Add all soil layers to the main group
        groupRef.current.add(soilLayersRef.current);
      }

      // Add pavement if specified
      if (pavement) {
        const pavementGeometry = new THREE.PlaneGeometry(width, width);
        pavementGeometry.rotateX(-Math.PI / 2); // Rotate to be horizontal

        // Create pavement material with appropriate appearance
        const pavementMaterial = new THREE.MeshStandardMaterial({
          color: pavement.color || '#454545',
          roughness: pavement.type === 'asphalt' ? 0.9 : pavement.type === 'concrete' ? 0.6 : 0.8,
          metalness: 0.1,
        });

        const pavementMesh = new THREE.Mesh(pavementGeometry, pavementMaterial);
        pavementMesh.position.y = 0.01; // Slightly above ground level
        pavementMesh.receiveShadow = true;

        // Only show pavement in areas that would have roads (center strip)
        pavementMesh.scale.set(0.5, 1, 0.5);

        groupRef.current.add(pavementMesh);
      }

      // Add groundwater if specified
      if (groundwaterLevel !== undefined) {
        const waterGeometry = new THREE.PlaneGeometry(width, width);
        waterGeometry.rotateX(-Math.PI / 2); // Rotate to be horizontal

        const waterMaterial = new THREE.MeshStandardMaterial({
          color: '#3b82f6', // Blue
          transparent: true,
          opacity: 0.6,
          roughness: 0.1,
          metalness: 0.3,
        });

        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.y = -groundwaterLevel; // Set to groundwater level

        // Add subtle animation to water material
        const waterNormalMap = new THREE.TextureLoader().load(
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        );
        waterMaterial.normalMap = waterNormalMap;
        waterMaterial.normalScale.set(0.1, 0.1);

        groupRef.current.add(water);
        groundwaterRef.current = water;

        // Add depth marker for groundwater if transparent mode is on
        if (transparentSurface && showLabels) {
          const markerGeometry = new THREE.CylinderGeometry(0.1, 0.1, groundwaterLevel, 8);
          const markerMaterial = new THREE.MeshBasicMaterial({ color: '#3b82f6' });
          const marker = new THREE.Mesh(markerGeometry, markerMaterial);

          // Position marker at corner of terrain
          marker.position.set(width / 3, -groundwaterLevel / 2, width / 3);
          groupRef.current.add(marker);

          // Add label
          const waterCanvas = document.createElement('canvas');
          const waterContext = waterCanvas.getContext('2d');
          waterCanvas.width = 256;
          waterCanvas.height = 64;
          
          if (waterContext) {
            waterContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
            waterContext.fillRect(0, 0, waterCanvas.width, waterCanvas.height);
            waterContext.fillStyle = '#ffffff';
            waterContext.font = '24px Arial';
            waterContext.fillText('Groundwater Level', 10, 40);
          }
          
          const waterTexture = new THREE.CanvasTexture(waterCanvas);
          const waterSpriteMaterial = new THREE.SpriteMaterial({ map: waterTexture });
          const waterLabel = new THREE.Sprite(waterSpriteMaterial);
          waterLabel.scale.set(2, 0.5, 1);
          
          // Position the label next to the marker
          waterLabel.position.set(width / 3 + 1, -groundwaterLevel / 2, width / 3);
          groupRef.current.add(waterLabel);
        }
      }

      // Add bedrock fault lines if specified in geology data and in transparent mode
      if (transparentSurface && geologyData?.faultLines && geologyData.faultLines.length > 0) {
        geologyData.faultLines.forEach((faultLine, index) => {
          // Create 3D line for the fault
          const faultGeometry = new THREE.BufferGeometry();
          const faultPositions = faultLine.map((point: any) => {
            // Position fault line in 3D space
            return new THREE.Vector3(
              point[0] - width / 4, // Offset for visibility
              -point[1], // Depth
              point[2] - width / 4 // Z coordinate with offset
            );
          });
          faultGeometry.setFromPoints(faultPositions);

          const faultMaterial = new THREE.LineBasicMaterial({
            color: '#ff0000',
            linewidth: 3,
          });

          const fault = new THREE.Line(faultGeometry, faultMaterial);
          if (groupRef.current) {
            groupRef.current.add(fault);
          }
        });
      }
    } else {
      // Create cross-section view with detailed soil layers
      const layers = soilLayers || defaultSoilLayers;
      soilLayersRef.current = new THREE.Group();

      // Calculate total height of all layers
      const totalThickness = layers.reduce((sum: any, layer: any) => sum + layer.thickness, 0);

      // Create soil layer meshes
      let currentDepth = 0;
      layers.forEach((layer, index) => {
        // Create layer mesh
        const layerGeometry = new THREE.BoxGeometry(width, layer.thickness, 0.2);
        const layerMaterial = new THREE.MeshStandardMaterial({
          color: layer.color,
          roughness: layer.material === 'rock' ? 0.9 : layer.material === 'clay' ? 0.7 : 0.5,
          metalness: layer.material === 'rock' ? 0.3 : 0.1,
          // Apply a subtle texture based on material type
          wireframe: layer.material === 'rock' ? true : false,
          wireframeLinewidth: 0.1,
          flatShading: layer.material === 'rock' || layer.material === 'clay',
        });

        const layerMesh = new THREE.Mesh(layerGeometry, layerMaterial);

        // Position the layer correctly
        layerMesh.position.set(0, -(currentDepth + layer.thickness / 2), -width / 2 + 0.1);
        layerMesh.userData = {
          name: layer.name,
          depth: currentDepth,
          thickness: layer.thickness,
          description: layer.description,
          material: layer.material,
        };

        // Add to soil layers group
        if (soilLayersRef.current) {
          soilLayersRef.current.add(layerMesh);
        }

        // Add layer label if enabled
        if (showLabels) {
          const labelPosition: [number, number, number] = [
            -width / 2 + 2, // X position (left side with small margin)
            -(currentDepth + layer.thickness / 2), // Y position (center of layer)
            -width / 2 + 0.3, // Z position (in front of layers)
          ];

          // Create a text sprite for the layer label
          const layerCanvas = document.createElement('canvas');
          const layerContext = layerCanvas.getContext('2d');
          layerCanvas.width = 256;
          layerCanvas.height = 64;
          
          if (layerContext) {
            layerContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
            layerContext.fillRect(0, 0, layerCanvas.width, layerCanvas.height);
            layerContext.fillStyle = '#ffffff';
            layerContext.font = '24px Arial';
            layerContext.fillText(layer.name, 10, 40);
          }
          
          const layerTexture = new THREE.CanvasTexture(layerCanvas);
          const layerSpriteMaterial = new THREE.SpriteMaterial({ map: layerTexture });
          const label = new THREE.Sprite(layerSpriteMaterial);
          label.scale.set(2, 0.5, 1);
          
          // Position the sprite
          label.position.set(labelPosition[0], labelPosition[1], labelPosition[2]);

          // Add to soil layers group
          if (soilLayersRef.current) {
            soilLayersRef.current.add(label);
          }
        }

        // Update current depth for next layer
        currentDepth += layer.thickness;
      });

      // Add all soil layers to the main group
      groupRef.current.add(soilLayersRef.current);

      // Add groundwater if specified
      if (groundwaterLevel !== undefined) {
        const waterGeometry = new THREE.PlaneGeometry(width, totalThickness * 2); // Make water plane larger than needed

        const waterMaterial = new THREE.MeshStandardMaterial({
          color: '#3b82f6', // Blue
          transparent: true,
          opacity: 0.7,
          roughness: 0.1,
          metalness: 0.1,
          side: THREE.DoubleSide,
        });

        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.set(0, -groundwaterLevel, -width / 2 + 0.2); // Slightly in front of soil layers

        // Add a wavy texture to the water surface
        const waterNormalMap = new THREE.TextureLoader().load(
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        );
        waterMaterial.normalMap = waterNormalMap;
        waterMaterial.normalScale.set(0.1, 0.1);

        groupRef.current.add(water);
        groundwaterRef.current = water;

        // Add water level label
        if (showLabels) {
          // Create a text sprite for the groundwater label
          const gwCanvas = document.createElement('canvas');
          const gwContext = gwCanvas.getContext('2d');
          gwCanvas.width = 256;
          gwCanvas.height = 64;
          
          if (gwContext) {
            gwContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
            gwContext.fillRect(0, 0, gwCanvas.width, gwCanvas.height);
            gwContext.fillStyle = '#ffffff';
            gwContext.font = '20px Arial';
            gwContext.fillText('Groundwater Level', 10, 40);
          }
          
          const gwTexture = new THREE.CanvasTexture(gwCanvas);
          const gwSpriteMaterial = new THREE.SpriteMaterial({ map: gwTexture });
          const waterLabel = new THREE.Sprite(gwSpriteMaterial);
          waterLabel.scale.set(1.8, 0.45, 1);
          
          // Position the water label
          waterLabel.position.set(-width / 2 + 2, -groundwaterLevel, -width / 2 + 0.3);
          groupRef.current.add(waterLabel);
        }
      }

      // Add a background for better visibility
      const backgroundGeometry = new THREE.PlaneGeometry(width * 1.5, totalThickness * 1.5);
      const backgroundMaterial = new THREE.MeshBasicMaterial({
        color: '#111111',
        transparent: true,
        opacity: 0.2,
        side: THREE.DoubleSide,
      });

      const background = new THREE.Mesh(backgroundGeometry, backgroundMaterial);
      background.position.set(0, -totalThickness / 2, -width / 2);
      groupRef.current.add(background);

      // Add bedrock fault lines if specified in geology data
      if (geologyData?.faultLines && geologyData.faultLines.length > 0) {
        geologyData.faultLines.forEach((faultLine, index) => {
          const faultGeometry = new THREE.BufferGeometry();
          const faultPositions = faultLine.map(
            (point) => new THREE.Vector3(point[0], -point[1], -width / 2 + 0.3)
          );
          faultGeometry.setFromPoints(faultPositions);

          const faultMaterial = new THREE.LineBasicMaterial({
            color: '#ff0000',
            linewidth: 2,
            linecap: 'round',
            linejoin: 'round',
          });

          const fault = new THREE.Line(faultGeometry, faultMaterial);
          if (groupRef.current) {
            groupRef.current.add(fault);
          }
        });
      }
    }
  }, [mode, width, depth, groundwaterLevel, transparentSurface, defaultSoilLayers, geologyData?.faultLines, pavement, showLabels, soilLayers]);

  // Add interactive layer info on hover
  function LayerHoverEffect() {
    const { raycaster, camera, mouse, scene } = useThree();
    const [hovered, setHovered] = useState<THREE.Intersection | null>(null);
    const infoRef = useRef<THREE.Group>(null);

    // Create info panel to display layer details
    useEffect(() => {
      if (!infoRef.current) {
        const infoGroup = new THREE.Group();
        infoGroup.visible = false;

        // Create info background
        const bgGeometry = new THREE.PlaneGeometry(5, 3);
        const bgMaterial = new THREE.MeshBasicMaterial({
          color: '#000000',
          transparent: true,
          opacity: 0.7,
        });
        const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);
        infoGroup.add(bgMesh);

        // Add to scene
        scene.add(infoGroup);
        infoRef.current = infoGroup;
      }

      return () => {
        if (infoRef.current) {
          scene.remove(infoRef.current);
        }
      };
    }, [scene]);

    // Handle hover detection and info panel update
    useFrame(() => {
      // Only check for hovers when in cross-section mode and soil layers exist
      if (mode !== 'cross_section' || !soilLayersRef.current) return;

      // Update the raycaster from the mouse position
      raycaster.setFromCamera(mouse, camera);

      // Check for intersections with soil layers
      const intersects = raycaster.intersectObjects(soilLayersRef.current.children, true);

      if (intersects.length > 0) {
        const intersection = intersects[0];

        if (
          intersection &&
          intersection.object instanceof THREE.Mesh &&
          intersection.object.userData &&
          intersection.object.userData.name
        ) {
          setHovered(intersection);
          setHoveredLayer(intersection.object.userData.name);
          
          if (showLabels && infoRef.current) {
            // Show layer information - this would need to be handled via React state
            // for proper DOM rendering, not Three.js objects
            infoRef.current.position.copy(intersection.point);
          }
        }
      } else {
        setHovered(null);
        setHoveredLayer(null);

        // Hide info panel if it exists
        if (infoRef.current) {
          infoRef.current.visible = false;
        }
      }
    });

    return null;
  }

  // Animate groundwater and other elements
  useFrame((state, delta) => {
    const time = state.clock.getElapsedTime();

    // Animate groundwater if present
    if (groundwaterRef.current) {
      // Get the water material
      const material = groundwaterRef.current.material as THREE.MeshStandardMaterial;

      // For cross-section mode, animate water level slightly
      if (mode === 'cross_section') {
        groundwaterRef.current.position.y = -groundwaterLevel! + Math.sin(time) * 0.05;
      }

      // For both modes, animate water appearance
      material.opacity = 0.7 + Math.sin(time * 0.5) * 0.1;

      // Animate water displacement pattern
      if (material.normalScale) {
        material.normalScale.set(
          0.1 + Math.sin(time * 0.8) * 0.05,
          0.1 + Math.cos(time * 0.7) * 0.05
        );
        material.needsUpdate = true;
      }
    }

    // Animate soil layers on hover if in cross-section mode
    if (mode === 'cross_section' && soilLayersRef.current && hoveredLayer) {
      // Find the hovered layer mesh
      soilLayersRef.current.children.forEach((child: any) => {
        if (child instanceof THREE.Mesh && child.userData && child.userData.name === hoveredLayer) {
          // Apply subtle animation to the layer (slight expansion)
          const scaleAmount = 1 + Math.sin(time * 3) * 0.01;
          child.scale.set(1, scaleAmount, 1);

          // Make the hovered layer slightly more opaque
          const material = child.material as THREE.MeshStandardMaterial;
          material.opacity = 0.95;
          material.emissive = new THREE.Color('#333333');
        } else if (child instanceof THREE.Mesh) {
          // Reset non-hovered layers
          child.scale.set(1, 1, 1);
          const material = child.material as THREE.MeshStandardMaterial;
          material.opacity = 0.85;
          material.emissive = new THREE.Color('#000000');
        }
      });
    }
  });

  return <group ref={groupRef}>{mode === 'cross_section' && <LayerHoverEffect />}</group>;
}

/**
 * Component to visualize a slice of soil for a profile view
 */
export function SoilProfileSlice({
  width = 20,
  height = 15,
  position = [0, 0, 0],
  soilLayers = [
    { name: 'Topsoil', thickness: 0.5, color: '#5b4120' },
    { name: 'Subsoil', thickness: 1.0, color: '#7f5d2d' },
    { name: 'Clay', thickness: 1.5, color: '#9a764a' },
    { name: 'Clay/Sand', thickness: 2.0, color: '#a3856c' },
    { name: 'Sandstone', thickness: 10.0, color: '#b69b7d' },
  ],
}: {
  width?: number;
  height?: number;
  position?: [number, number, number];
  soilLayers?: {
    name: string;
    thickness: number;
    color: string;
  }[];
}) {
  // Create separate meshes for each soil layer
  return (
    <group position={new THREE.Vector3(...position)}>
      {soilLayers.map((layer, index) => {
        // Calculate position of this layer (each layer starts where the previous ended)
        let layerTop = 0;
        for (let i = 0; i < index; i++) {
          const layer = soilLayers[i];
          if (layer) {
            layerTop -= layer.thickness;
          }
        }

        const layerPosition = layerTop - layer.thickness / 2;

        return (
          <mesh key={index} position={[0, layerPosition, 0]} castShadow receiveShadow>
            <boxGeometry args={[width, layer.thickness, 0.2]} />
            <meshStandardMaterial color={layer.color} roughness={0.9} metalness={0.1} />
          </mesh>
        );
      })}
    </group>
  );
}

/**
 * Component to show pavement and road structure
 */
export function PavementLayer({
  width = 20,
  position = [0, 0, 0],
  thickness = 0.5,
  color = '#454545',
}: {
  width?: number;
  position?: [number, number, number];
  thickness?: number;
  color?: string;
}) {
  return (
    <mesh position={new THREE.Vector3(...position)} receiveShadow>
      <boxGeometry args={[width, thickness, width]} />
      <meshStandardMaterial color={color} roughness={0.7} metalness={0.3} />
    </mesh>
  );
}
