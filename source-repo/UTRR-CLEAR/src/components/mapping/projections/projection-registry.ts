import { register } from 'ol/proj/proj4.js';
import { get as getProjection } from 'ol/proj.js';
import proj4 from 'proj4';
import { safeLog } from '~/lib/error-handler';

// Define coordinate system configurations
export interface CoordinateSystem {
  code: string;
  name: string;
  definition: string;
  extent?: [number, number, number, number];
  units: string;
  description: string;
  authority: string;
}

// Indiana State Plane coordinate systems
export const INDIANA_COORDINATE_SYSTEMS: CoordinateSystem[] = [
  {
    code: 'EPSG:2965',
    name: 'NAD83 / Indiana East',
    definition:
      '+proj=tmerc +lat_0=37.5 +lon_0=-85.66666666666667 +k=0.999966667 +x_0=100000 +y_0=250000 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs',
    extent: [-88.1607, 37.7554, -84.7844, 41.7753],
    units: 'meters',
    description: 'Indiana State Plane East Zone, NAD83 datum',
    authority: 'EPSG',
  },
  {
    code: 'EPSG:2966',
    name: 'NAD83 / Indiana West',
    definition:
      '+proj=tmerc +lat_0=37.5 +lon_0=-87.08333333333333 +k=0.999966667 +x_0=900000 +y_0=250000 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs',
    extent: [-88.1607, 37.7554, -86.2456, 41.7753],
    units: 'meters',
    description: 'Indiana State Plane West Zone, NAD83 datum',
    authority: 'EPSG',
  },
  {
    code: 'EPSG:3745',
    name: 'NAD83 / Indiana East (ftUS)',
    definition:
      '+proj=tmerc +lat_0=37.5 +lon_0=-85.66666666666667 +k=0.999966667 +x_0=328083.333 +y_0=820208.333 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=us-ft +no_defs',
    extent: [-88.1607, 37.7554, -84.7844, 41.7753],
    units: 'US survey feet',
    description: 'Indiana State Plane East Zone, NAD83 datum, US Survey Feet',
    authority: 'EPSG',
  },
  {
    code: 'EPSG:3746',
    name: 'NAD83 / Indiana West (ftUS)',
    definition:
      '+proj=tmerc +lat_0=37.5 +lon_0=-87.08333333333333 +k=0.999966667 +x_0=2952750 +y_0=820208.333 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=us-ft +no_defs',
    extent: [-88.1607, 37.7554, -86.2456, 41.7753],
    units: 'US survey feet',
    description: 'Indiana State Plane West Zone, NAD83 datum, US Survey Feet',
    authority: 'EPSG',
  },
];

// Additional commonly used coordinate systems
export const COMMON_COORDINATE_SYSTEMS: CoordinateSystem[] = [
  {
    code: 'EPSG:4326',
    name: 'WGS84 Geographic',
    definition: '+proj=longlat +datum=WGS84 +no_defs',
    extent: [-180, -90, 180, 90],
    units: 'degrees',
    description: 'World Geodetic System 1984, Geographic coordinates',
    authority: 'EPSG',
  },
  {
    code: 'EPSG:3857',
    name: 'Web Mercator',
    definition:
      '+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs',
    extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34],
    units: 'meters',
    description: 'Web Mercator projection, used by most web mapping services',
    authority: 'EPSG',
  },
  {
    code: 'EPSG:2154',
    name: 'RGF93 / Lambert-93',
    definition:
      '+proj=lcc +lat_1=49 +lat_2=44 +lat_0=46.5 +lon_0=3 +x_0=700000 +y_0=6600000 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs',
    extent: [-9.86, 41.15, 10.38, 51.56],
    units: 'meters',
    description: 'French national coordinate system',
    authority: 'EPSG',
  },
];

export class ProjectionRegistry {
  private registeredSystems: Map<string, CoordinateSystem>;
  private initialized: boolean = false;

  constructor() {
    this.registeredSystems = new Map();
  }

  // Initialize the projection registry
  initialize(): void {
    if (this.initialized) return;

    // Register Indiana coordinate systems
    INDIANA_COORDINATE_SYSTEMS.forEach((cs) => {
      this.registerCoordinateSystem(cs);
    });

    // Register common coordinate systems
    COMMON_COORDINATE_SYSTEMS.forEach((cs) => {
      this.registerCoordinateSystem(cs);
    });

    this.initialized = true;
  }

  // Register a coordinate system
  registerCoordinateSystem(coordinateSystem: CoordinateSystem): void {
    try {
      // Add to proj4 registry
      proj4.defs(coordinateSystem.code, coordinateSystem.definition);

      // Register with OpenLayers
      register(proj4);

      // Store in our registry
      this.registeredSystems.set(coordinateSystem.code, coordinateSystem);

      safeLog.info(
        `Registered coordinate system: ${coordinateSystem.code} - ${coordinateSystem.name}`
      );
    } catch (error) {
      safeLog.error(`Failed to register coordinate system ${coordinateSystem.code}:`, { error: String(error) });
    }
  }

  // Get coordinate system by code
  getCoordinateSystem(code: string): CoordinateSystem | undefined {
    return this.registeredSystems.get(code);
  }

  // Get all registered coordinate systems
  getAllCoordinateSystems(): CoordinateSystem[] {
    return Array.from(this.registeredSystems.values());
  }

  // Get Indiana coordinate systems only
  getIndianaCoordinateSystems(): CoordinateSystem[] {
    return INDIANA_COORDINATE_SYSTEMS.filter((cs) => this.registeredSystems.has(cs.code));
  }

  // Get coordinate systems by units
  getCoordinateSystemsByUnits(units: string): CoordinateSystem[] {
    return Array.from(this.registeredSystems.values()).filter((cs) =>
      cs.units.toLowerCase().includes(units.toLowerCase())
    );
  }

  // Check if coordinate system is registered
  isRegistered(code: string): boolean {
    return this.registeredSystems.has(code);
  }

  // Get the OpenLayers projection object
  getProjection(code: string) {
    if (!this.isRegistered(code)) {
      safeLog.warn(`Coordinate system ${code} is not registered`);
      return null;
    }
    return getProjection(code);
  }

  // Get coordinate system info for display
  getCoordinateSystemInfo(code: string): string {
    const cs = this.getCoordinateSystem(code);
    if (!cs) return 'Unknown coordinate system';

    return `${cs.name} (${cs.code}) - ${cs.units}`;
  }

  // Transform coordinates between systems
  transformCoordinates(
    coordinates: [number, number],
    fromCode: string,
    toCode: string
  ): [number, number] | null {
    try {
      if (!this.isRegistered(fromCode) || !this.isRegistered(toCode)) {
        safeLog.error('One or both coordinate systems are not registered');
        return null;
      }

      return proj4(fromCode, toCode, coordinates);
    } catch (error) {
      safeLog.error('Coordinate transformation failed:', { error: String(error) });
      return null;
    }
  }

  // Get extent in target coordinate system
  getExtentInSystem(code: string): [number, number, number, number] | null {
    const cs = this.getCoordinateSystem(code);
    if (!cs || !cs.extent) return null;

    // If the extent is already in the target system, return it
    if (code === 'EPSG:4326') {
      return cs.extent;
    }

    try {
      // Transform extent from WGS84 to target system
      const bottomLeft = this.transformCoordinates([cs.extent[0], cs.extent[1]], 'EPSG:4326', code);
      const topRight = this.transformCoordinates([cs.extent[2], cs.extent[3]], 'EPSG:4326', code);

      if (bottomLeft && topRight) {
        return [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
      }
    } catch (error) {
      safeLog.error('Failed to transform extent:', { error: String(error) });
    }

    return null;
  }

  // Get default coordinate system for Indiana projects
  getDefaultCoordinateSystem(): CoordinateSystem {
    const defaultSystem = this.getCoordinateSystem('EPSG:2965');
    return defaultSystem || INDIANA_COORDINATE_SYSTEMS[0]!;
  }

  // Validate coordinate system definition
  validateCoordinateSystem(coordinateSystem: CoordinateSystem): boolean {
    try {
      // Test the proj4 definition
      proj4.defs('test', coordinateSystem.definition);
      const testTransform = proj4('EPSG:4326', 'test', [-86.1581, 39.7684]);
      return Array.isArray(testTransform) && testTransform.length === 2;
    } catch (error) {
      safeLog.error('Invalid coordinate system definition:', { error: String(error) });
      return false;
    }
  }

  // Get coordinate system suitable for measurements
  getMeasurementSystem(): CoordinateSystem {
    // Return a metric system for accurate measurements
    return this.getCoordinateSystem('EPSG:2965') || this.getDefaultCoordinateSystem();
  }

  // Get coordinate system display name
  getDisplayName(code: string): string {
    const cs = this.getCoordinateSystem(code);
    return cs ? cs.name : code;
  }

  // Reset registry
  reset(): void {
    this.registeredSystems.clear();
    this.initialized = false;
  }

  // Export registry for backup/sharing
  exportRegistry(): CoordinateSystem[] {
    return Array.from(this.registeredSystems.values());
  }

  // Import coordinate systems from backup
  importRegistry(coordinateSystems: CoordinateSystem[]): void {
    coordinateSystems.forEach((cs) => {
      this.registerCoordinateSystem(cs);
    });
  }
}

// Create singleton instance
export const projectionRegistry = new ProjectionRegistry();

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  projectionRegistry.initialize();
}
