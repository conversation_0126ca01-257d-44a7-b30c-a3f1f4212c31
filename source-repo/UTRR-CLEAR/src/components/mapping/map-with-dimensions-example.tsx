'use client';

import React, { useEffect, useRef, useState } from 'react';
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import TileLayer from 'ol/layer/Tile.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import OSM from 'ol/source/OSM.js';
import { fromLonLat } from 'ol/proj.js';
import { defaults as defaultControls } from 'ol/control.js';

// Import the dimension toolbar and drawing toolbar
import { DimensionToolbar } from './dimension-toolbar';
import { DrawingToolbar } from './drawing-toolbar';
import type { DimensionType } from '~/components/gis/dimension-tools';

interface MapWithDimensionsProps {
  height?: string;
  width?: string;
}

/**
 * Example map component showing how to integrate the dimension toolbar
 * alongside the existing drawing toolbar
 */
export function MapWithDimensionsExample({
  height = '600px',
  width = '100%',
}: MapWithDimensionsProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<Map | null>(null);
  const [drawingSource, setDrawingSource] = useState<VectorSource | null>(null);
  const [dimensionSource, setDimensionSource] = useState<VectorSource | null>(null);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    // Create vector sources
    const drawSource = new VectorSource();
    const dimSource = new VectorSource();

    // Create vector layers
    const drawingLayer = new VectorLayer({
      source: drawSource,
      zIndex: 10,
    });

    const dimensionLayer = new VectorLayer({
      source: dimSource,
      zIndex: 20, // Higher z-index so dimensions appear on top
    });

    // Create map instance
    const mapInstance = new Map({
      target: mapRef.current,
      layers: [
        new TileLayer({
          source: new OSM(),
        }),
        drawingLayer,
        dimensionLayer,
      ],
      view: new View({
        center: fromLonLat([-86.7816, 36.1627]), // Nashville, TN
        zoom: 13,
      }),
      controls: defaultControls({
        attribution: false,
      }),
    });

    setMap(mapInstance);
    setDrawingSource(drawSource);
    setDimensionSource(dimSource);

    // Cleanup
    return () => {
      mapInstance.setTarget(undefined);
    };
  }, []);

  const handleDimensionStart = (type: DimensionType) => {
    console.log('Starting dimension:', type);
  };

  const handleDimensionEnd = () => {
    console.log('Dimension completed');
  };

  return (
    <div className="relative">
      {/* Map container */}
      <div
        ref={mapRef}
        style={{ height, width }}
        className="relative rounded-lg overflow-hidden border"
      />

      {/* Toolbars positioned absolutely over the map */}
      <div className="absolute top-4 left-4 flex flex-col gap-2 z-20">
        {/* Drawing toolbar */}
        {map && drawingSource && (
          <DrawingToolbar
            map={map}
            drawingLayer={
              map.getLayers().getArray().find(
                layer => layer instanceof VectorLayer && layer.getSource() === drawingSource
              ) as VectorLayer<VectorSource>
            }
          />
        )}
      </div>

      {/* Dimension toolbar positioned at the top */}
      <div className="absolute top-4 left-1/2 -translate-x-1/2 z-20">
        <DimensionToolbar
          map={map}
          source={dimensionSource}
          onDimensionStart={handleDimensionStart}
          onDimensionEnd={handleDimensionEnd}
        />
      </div>

      {/* Additional UI elements can go here */}
      <div className="absolute bottom-4 left-4 bg-background/90 backdrop-blur-sm rounded-md p-2 text-xs">
        <div>Click dimension tools to add construction-style dimensions</div>
        <div>Use drawing tools for general shapes and measurements</div>
      </div>
    </div>
  );
}