'use client';

import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import * as THREE from 'three';
import { Canvas, useThree, use<PERSON>rame, type ThreeEvent } from '@react-three/fiber';
import {
  OrbitControls,
  PerspectiveCamera,
  Environment,
  Grid,
  useGLTF,
  Preload,
  AdaptiveDpr,
  AdaptiveEvents,
  PerformanceMonitor,
  Text,
  Line,
  Box,
  Cylinder,
  Sphere,
} from '@react-three/drei';
import type { Feature } from 'ol';
import type { LineString, Geometry } from 'ol/geom.js';
import type { Map as OLMap } from 'ol';
import type { LayerManager } from './layers/layer-manager';
import type VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import { CoordinateTransformer } from '~/lib/mapping/coordinate-utils';
import {
  getUtilityDepth,
  detectVerticalConflicts,
  type VerticalConflict,
} from '~/lib/mapping/vertical-conflict-detection';
import { getUtilityTypeColor } from '~/lib/mapping/utility-styles';
import { createUtilityMaterial } from '~/lib/mapping/three-utility-models';
import type { UtilityType } from '~/lib/mapping/types';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Slider } from '~/components/ui/slider';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Eye, EyeOff, RotateCcw, Download, Maximize2 } from 'lucide-react';
import { safeLog } from '~/lib/error-handler';

// Use imported functions and types

export interface ThreeJSMapProps {
  map: OLMap | null;
  layerManager?: LayerManager | null;
  active: boolean;
  width?: number | string;
  height?: number | string;
  exaggerationFactor?: number; // Vertical exaggeration factor for better visualization
  showConflicts?: boolean; // Whether to show conflict indicators
  conflictThreshold?: number; // Distance threshold for conflict detection in meters
}

// ConflictData from vertical-conflict-detection used instead

// Use imported detectVerticalConflicts function

// Main Three.js visualization component
export function ThreeJSMap({
  map,
  layerManager,
  active,
  width = '100%',
  height = '100%',
  exaggerationFactor = 5, // Exaggerate vertical scale for better visibility
  showConflicts = true, // Show conflicts by default
  conflictThreshold = 0.5, // Default 0.5 meter threshold
}: ThreeJSMapProps) {
  const [utilities, setUtilities] = useState<
    {
      feature: Feature<LineString>;
      type: UtilityType;
      color: string;
      depth: number;
      diameter: number;
      coordinates: number[][];
    }[]
  >([]);

  // State for vertical conflicts
  const [conflicts, setConflicts] = useState<VerticalConflict[]>([]);

  // Extract utility data when component becomes active
  useEffect(() => {
    if (!map || !layerManager || !active) return;

    // Get utility features from the layer manager
    const utilityLayer = layerManager.getLayer('utility_lines');
    if (!utilityLayer) return;

    const source = utilityLayer.getSource();
    if (!source) return;
    
    // Type guard to check if source is a VectorSource
    if (!(source instanceof VectorSource)) return;

    const features = source.getFeatures();
    
    // Convert OpenLayers features to Three.js coordinates
    const convertedFeatures = features.map((feature: Feature) => {
      const geometry = feature.getGeometry();
      if (!geometry) return null;
      
      let coordinates: number[][] = [];
      
      if (geometry.getType() === 'LineString') {
        coordinates = (geometry as LineString).getCoordinates().map((coord: number[]) => [
          coord[0] || 0, // x
          0, // y (elevation will be set based on utility depth)
          coord[1] ? -coord[1] : 0, // z in Three.js = negative latitude in OpenLayers (for better camera orientation)
        ]);
      }

      const utilityType = feature.get('utilityType') as UtilityType;
      const depth = getUtilityDepth(feature);
      const diameter = feature.get('diameter') || 0.3; // Default 30cm diameter if not specified
      const color = getUtilityTypeColor(utilityType);

      return {
        feature,
        type: utilityType,
        color,
        depth,
        diameter,
        coordinates,
      };
    })
    .filter(Boolean) as {
      feature: Feature<LineString>;
      type: UtilityType;
      color: string;
      depth: number;
      diameter: number;
      coordinates: number[][];
    }[];

    setUtilities(convertedFeatures);

    // Detect vertical conflicts if enabled
    if (showConflicts && convertedFeatures.length >= 2) {
      const features = convertedFeatures.map(c => c.feature);
      const detectedConflicts = detectVerticalConflicts(features, {
        depthToleranceHard: conflictThreshold || 0.5,
        depthToleranceSoft: (conflictThreshold || 0.5) * 2,
        checkIntersections: true,
      });
      
      // Convert ConflictData to VerticalConflict format
      const verticalConflicts: VerticalConflict[] = detectedConflicts.map(conflict => {
        const util1 = convertedFeatures.find((c: any) => c.feature.getId()?.toString() === conflict.utility1Id);
        const util2 = convertedFeatures.find((c: any) => c.feature.getId()?.toString() === conflict.utility2Id);
        
        return {
          utility1: util1?.feature as Feature<Geometry>,
          utility2: util2?.feature as Feature<Geometry>,
          utility1Type: util1?.type || 'unknown',
          utility2Type: util2?.type || 'unknown',
          severity: conflict.type === 'hard' ? 'high' : 'medium',
          distance: conflict.depthDifference,
          requiredClearance: conflict.type === 'hard' ? 0.5 : 1.0,
        };
      });
      
      setConflicts(verticalConflicts);
      safeLog.info(`Detected ${detectedConflicts.length} vertical conflicts for 3D visualization`);
    } else {
      setConflicts([]);
    }
  }, [map, layerManager, active, exaggerationFactor, showConflicts, conflictThreshold]);

  // Performance state
  const [perfSetting, setPerfSetting] = useState<number>(1); // Default to medium quality

  // Hide when not active
  if (!active) {
    return null;
  }

  return (
    <div style={{ width, height, position: 'absolute', top: 0, left: 0 }}>
      <Canvas shadows dpr={[1, 2]} gl={{ antialias: true }}>
        {/* Default camera */}
        <PerspectiveCamera makeDefault position={[0, 10, 20]} />

        {/* Controls with optimized event handling */}
        <OrbitControls
          enableDamping
          dampingFactor={0.05}
          screenSpacePanning
          minDistance={1}
          maxDistance={500}
        />

        {/* Performance monitoring and adaptive quality */}
        <PerformanceMonitor
          onIncline={() => setPerfSetting(Math.min(perfSetting + 1, 3))}
          onDecline={() => setPerfSetting(Math.max(perfSetting - 1, 1))}
        >
          {/* Ground and scene lighting */}
          <Scene
            utilities={utilities}
            conflicts={conflicts}
            exaggerationFactor={exaggerationFactor}
            qualityLevel={perfSetting}
          />

          {/* Adaptive DPR adjusts resolution based on performance */}
          <AdaptiveDpr pixelated />

          {/* Optimize event handling for better performance */}
          <AdaptiveEvents />
        </PerformanceMonitor>

        {/* Environment map for reflections */}
        <Environment preset="city" />

        {/* Preload assets for better performance */}
        <Preload all />
      </Canvas>
    </div>
  );
}

// Scene component with ground, utilities and lighting
function Scene({
  utilities,
  conflicts = [],
  exaggerationFactor = 5,
  qualityLevel = 1, // 1 = low, 2 = medium, 3 = high
}: {
  utilities: {
    feature: Feature<LineString>;
    type: UtilityType;
    color: string;
    depth: number;
    diameter: number;
    coordinates: number[][];
  }[];
  conflicts?: VerticalConflict[];
  exaggerationFactor?: number;
  qualityLevel?: number;
}) {
  const { scene } = useThree();
  const groupRef = useRef<THREE.Group>(null);

  // Center the scene on the first render
  useEffect(() => {
    if (utilities.length === 0 || !groupRef.current) return;

    // Find the center point of all utilities
    let minX = Infinity,
      maxX = -Infinity;
    let minZ = Infinity,
      maxZ = -Infinity;

    utilities.forEach((utility: any) => {
      utility.coordinates.forEach((coord: number[]) => {
        if (coord && coord.length >= 3) {
          minX = Math.min(minX, coord[0] || 0);
          maxX = Math.max(maxX, coord[0] || 0);
          minZ = Math.min(minZ, coord[2] || 0);
          maxZ = Math.max(maxZ, coord[2] || 0);
        }
      });
    });

    const centerX = (minX + maxX) / 2;
    const centerZ = (minZ + maxZ) / 2;

    // Move the group to center the scene
    if (groupRef.current) {
      groupRef.current.position.set(-centerX, 0, -centerZ);
    }

    // Set the scene background
    scene.background = new THREE.Color(0xf0f0f0);
  }, [utilities, scene]);

  // Memoize the lights to prevent unnecessary re-renders
  const Lights = useMemo(() => {
    // Determine shadow map size based on quality level
    const shadowMapSize = qualityLevel === 1 ? 1024 : qualityLevel === 2 ? 2048 : 4096;

    return (
      <>
        <ambientLight intensity={0.5} />
        <directionalLight
          position={[100, 100, 100]}
          intensity={1.5}
          castShadow
          shadow-mapSize={[shadowMapSize, shadowMapSize]}
          // Optimize shadow settings
          shadow-bias={-0.001}
        >
          {/* Optimize shadow camera parameters */}
          <orthographicCamera attach="shadow-camera" args={[-100, 100, 100, -100, 0.1, 500]} />
        </directionalLight>
        <directionalLight position={[-100, 100, -100]} intensity={0.8} />
      </>
    );
  }, [qualityLevel]);

  // Memoize ground plane and grid for performance
  const Ground = useMemo(() => {
    return (
      <>
        {/* Ground surface optimized based on quality */}
        <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]} receiveShadow>
          <planeGeometry args={[1000, 1000]} />
          <meshStandardMaterial
            color="#a0a0a0"
            opacity={0.5}
            transparent
            roughness={1}
            metalness={0}
            // Reduce quality for low-end devices
            flatShading={qualityLevel === 1}
          />
        </mesh>

        {/* Grid with optimized settings */}
        <Grid
          position={[0, 0.01, 0]}
          cellSize={1}
          cellThickness={0.5}
          cellColor="#a0a0a0"
          sectionSize={5}
          sectionThickness={1}
          sectionColor="#606060"
          fadeDistance={qualityLevel === 1 ? 50 : qualityLevel === 2 ? 100 : 150}
          infiniteGrid
        />
      </>
    );
  }, [qualityLevel]);

  // Memoize the utilities list to prevent unnecessary rebuilds
  const UtilityLines = useMemo(() => {
    return utilities.map((utility, index) => (
      <UtilityLine key={`utility-${index}`} utility={utility} qualityLevel={qualityLevel} />
    ));
  }, [utilities, qualityLevel]);

  // Memoize the conflicts list
  const ConflictIndicators = useMemo(() => {
    if (conflicts.length === 0) return null;

    return conflicts.map((conflict, index) => {
      // Find the utility objects that correspond to the conflict
      const utility1 = utilities.find((u: any) => u.feature === conflict.utility1);
      const utility2 = utilities.find((u: any) => u.feature === conflict.utility2);

      if (!utility1 || !utility2) return null;

      // Get the first coordinate of each utility to place the conflict indicator
      const point1 = utility1.coordinates.length > 0 ? utility1.coordinates[0] : null;
      const point2 = utility2.coordinates.length > 0 ? utility2.coordinates[0] : null;

      if (!point1 || !point2) return null;

      return (
        <ConflictIndicator
          key={`conflict-${index}`}
          point1={point1}
          point2={point2}
          severity={conflict.severity}
          distance={conflict.distance}
          requiredClearance={conflict.requiredClearance}
          qualityLevel={qualityLevel}
        />
      );
    });
  }, [conflicts, utilities, qualityLevel]);

  // Render the optimized scene
  return (
    <>
      {/* Use memoized components */}
      {Lights}
      {Ground}

      {/* Group for all dynamic elements */}
      <group ref={groupRef}>
        {UtilityLines}
        {ConflictIndicators}
      </group>
    </>
  );
}

// Individual utility line visualization
function UtilityLine({ utility, qualityLevel = 2 }: { 
  utility: {
    feature: Feature<LineString>;
    type: UtilityType;
    color: string;
    depth: number;
    diameter: number;
    coordinates: number[][];
  }; 
  qualityLevel?: number 
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [showInfo, setShowInfo] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const { camera } = useThree();

  useEffect(() => {
    if (!meshRef.current) return;

    // Add metadata to mesh userData
    meshRef.current.userData = {
      id: utility.feature.getId() || utility.feature.get('id') || 'unknown',
      type: utility.type,
      depth: utility.depth,
      diameter: utility.diameter,
      owner: utility.feature.get('owner') || 'Unknown',
      material: utility.feature.get('material') || 'Unknown',
      installationType: utility.feature.get('installationType') || 'Unknown',
    };
  }, [utility]);

  // Calculate the points based on utility coordinates
  const points = useMemo(
    () =>
      utility.coordinates.map((coord: number[]) => new THREE.Vector3(coord[0], coord[1], coord[2])),
    [utility.coordinates]
  );

  // Create a smooth curve from the points
  const curve = useMemo(() => new THREE.CatmullRomCurve3(points), [points]);

  // Determine quality settings based on the parent's qualityLevel
  // and the distance from the camera
  const [quality, setQuality] = useState({
    tubularSegments: 64,
    radialSegments: 8,
  });

  // Update geometry quality based on distance from camera and global quality setting
  useFrame(() => {
    if (!meshRef.current) return;

    // Calculate distance to camera
    const distance = camera.position.distanceTo(meshRef.current.position);

    // Determine optimal quality based on distance and global setting
    // Higher tubularSegments = more detailed curves
    // Higher radialSegments = more detailed cross-section
    let newTubularSegments = 64;
    let newRadialSegments = 8;

    if (distance > 50) {
      // Far away objects can use lower quality
      newTubularSegments = 16;
      newRadialSegments = 4;
    } else if (distance > 20) {
      // Medium distance
      newTubularSegments = 32;
      newRadialSegments = 6;
    } else {
      // Close up - use high quality based on global setting
      newTubularSegments = qualityLevel === 1 ? 32 : qualityLevel === 2 ? 64 : 96;
      newRadialSegments = qualityLevel === 1 ? 6 : qualityLevel === 2 ? 8 : 12;
    }

    // Only update if quality changed to avoid unnecessary re-renders
    if (
      newTubularSegments !== quality.tubularSegments ||
      newRadialSegments !== quality.radialSegments
    ) {
      setQuality({
        tubularSegments: newTubularSegments,
        radialSegments: newRadialSegments,
      });
    }
  });

  // Handle mouse events for showing utility information
  const handlePointerOver = (e: ThreeEvent<PointerEvent>) => {
    e.stopPropagation?.();
    setIsHovered(true);
  };

  const handlePointerOut = (e: ThreeEvent<PointerEvent>) => {
    e.stopPropagation?.();
    setIsHovered(false);
  };

  const handleClick = (e: ThreeEvent<MouseEvent>) => {
    e.stopPropagation?.();
    setShowInfo(!showInfo);
  };

  // Calculate where to position the info panel
  const midPoint = useMemo(() => {
    if (points.length === 0) return new THREE.Vector3(0, 0, 0);

    if (points.length === 1) return points[0] || new THREE.Vector3(0, 0, 0);

    // Get middle point of the utility line
    const middleIndex = Math.floor(points.length / 2);
    return points[middleIndex] || new THREE.Vector3(0, 0, 0);
  }, [points]);

  // Use a tube geometry for the utility line with optimized settings
  return (
    <>
      <mesh
        ref={meshRef}
        castShadow
        receiveShadow
        onPointerOver={handlePointerOver}
        onPointerOut={handlePointerOut}
        onClick={handleClick}
      >
        <tubeGeometry
          args={[
            curve,
            quality.tubularSegments,
            utility.diameter / 2,
            quality.radialSegments,
            false,
          ]}
        />
        <meshStandardMaterial
          color={isHovered ? '#ffffff' : utility.color}
          roughness={0.4}
          metalness={0.6}
          // Optimize material settings
          toneMapped={true}
          // Use flat shading for low quality to improve performance
          flatShading={quality.radialSegments <= 6}
          // Add emissive effect when hovered
          emissive={isHovered ? utility.color : '#000000'}
          emissiveIntensity={isHovered ? 0.5 : 0}
        />
      </mesh>

      {/* Information panel shown when utility is clicked */}
      {showInfo && (
        <group position={[midPoint.x, midPoint.y + 1, midPoint.z]}>
          {/* Background panel */}
          <mesh position={[0, 0, 0]}>
            <planeGeometry args={[5, 3]} />
            <meshBasicMaterial color="#000000" transparent opacity={0.7} side={THREE.DoubleSide} />
          </mesh>

          {/* Utility information text */}
          <Text
            position={[0, 0, 0.01]}
            fontSize={0.3}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            maxWidth={4.5}
            textAlign="left"
            lineHeight={1.4}
          >
            {`Type: ${utility.type}\n` +
              `ID: ${meshRef.current?.userData.id || 'Unknown'}\n` +
              `Depth: ${utility.depth.toFixed(2)}m\n` +
              `Diameter: ${(utility.diameter * 100).toFixed(1)}cm\n` +
              `Material: ${meshRef.current?.userData.material || 'Unknown'}\n` +
              `Owner: ${meshRef.current?.userData.owner || 'Unknown'}\n` +
              `Installation: ${meshRef.current?.userData.installationType || 'Unknown'}`}
          </Text>

          {/* Close button */}
          <mesh
            position={[2.2, 1.2, 0.01]}
            onClick={(e: any) => {
              e.stopPropagation();
              setShowInfo(false);
            }}
          >
            <planeGeometry args={[0.4, 0.4]} />
            <meshBasicMaterial color="#ff0000" transparent opacity={0.8} />
          </mesh>
          <Text
            position={[2.2, 1.2, 0.02]}
            fontSize={0.3}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
          >
            X
          </Text>
        </group>
      )}
    </>
  );
}

// Component to display utility conflict indicators
function ConflictIndicator({
  point1,
  point2,
  severity,
  distance,
  requiredClearance,
  qualityLevel = 2,
}: {
  point1: number[];
  point2: number[];
  severity: 'high' | 'medium' | 'low';
  distance: number;
  requiredClearance: number;
  qualityLevel?: number;
}) {
  // Choose color based on severity
  let color = '#fbbf24'; // amber for low
  if (severity === 'high') {
    color = '#ef4444'; // red for high
  } else if (severity === 'medium') {
    color = '#f97316'; // orange for medium
  }

  // Create a line between the two conflict points - memoize for performance
  const points = useMemo(
    () => [
      new THREE.Vector3(point1[0], point1[1], point1[2]),
      new THREE.Vector3(point2[0], point2[1], point2[2]),
    ],
    [point1, point2]
  );

  const lineGeometry = useMemo(() => new THREE.BufferGeometry().setFromPoints(points), [points]);

  // Reference for the line
  const lineRef = useRef<THREE.Line>(null);

  // Create the warning cylinder (vertical clearance indicator)
  // Use memoization to avoid unnecessary recalculations
  const midPoint = useMemo(
    () => [
      ((point1[0] ?? 0) + (point2[0] ?? 0)) / 2, 
      ((point1[1] ?? 0) + (point2[1] ?? 0)) / 2, 
      ((point1[2] ?? 0) + (point2[2] ?? 0)) / 2
    ],
    [point1, point2]
  );

  // Calculate distance between the two points
  const actualDistance = useMemo(
    () =>
      Math.sqrt(
        Math.pow((point2[0] ?? 0) - (point1[0] ?? 0), 2) +
          Math.pow((point2[1] ?? 0) - (point1[1] ?? 0), 2) +
          Math.pow((point2[2] ?? 0) - (point1[2] ?? 0), 2)
      ),
    [point1, point2]
  );

  // Optimize geometry segments based on quality level
  const cylinderSegments = useMemo(
    () => (qualityLevel === 1 ? 6 : qualityLevel === 2 ? 8 : 12),
    [qualityLevel]
  );

  const sphereSegments = useMemo(
    () => (qualityLevel === 1 ? 8 : qualityLevel === 2 ? 12 : 16),
    [qualityLevel]
  );

  // Animation effect for the conflict indicator with performance optimizations
  useFrame((state) => {
    if (lineRef.current && state.clock.elapsedTime % 0.1 < 0.05) {
      // Only update every ~100ms
      // Pulse the indicator based on severity
      const pulseSpeed = severity === 'high' ? 4 : severity === 'medium' ? 2 : 1;
      const opacity = Math.abs(Math.sin(state.clock.elapsedTime * pulseSpeed)) * 0.5 + 0.5;

      // Access material properties
      if (lineRef.current && lineRef.current.material && 'opacity' in lineRef.current.material) {
        (lineRef.current.material as THREE.LineBasicMaterial).opacity = opacity;
      }
    }
  });

  return (
    <group>
      {/* Conflict line indicator */}
      <Line
        points={points}
        color={color}
        lineWidth={3}
      />

      {/* Vertical caution cylinder to show clearance issue with quality-based segments */}
      <mesh position={[midPoint[0] ?? 0, midPoint[1] ?? 0, midPoint[2] ?? 0]} rotation={[Math.PI / 2, 0, 0]}>
        <cylinderGeometry args={[0.15, 0.15, actualDistance, cylinderSegments, 1, true]} />
        <meshStandardMaterial
          color={color}
          transparent={true}
          opacity={0.4}
          side={THREE.DoubleSide}
          emissive={color}
          emissiveIntensity={0.3}
          // Optimization for lower quality settings
          flatShading={qualityLevel === 1}
        />
      </mesh>

      {/* Indicator sphere at conflict point with quality-based segments */}
      <mesh position={[midPoint[0] ?? 0, midPoint[1] ?? 0, midPoint[2] ?? 0]}>
        <sphereGeometry args={[0.2, sphereSegments, sphereSegments]} />
        <meshStandardMaterial
          color={color}
          emissive={color}
          emissiveIntensity={0.3}
          // Optimization for lower quality settings
          flatShading={qualityLevel === 1}
        />
      </mesh>
    </group>
  );
}

// End of main component implementations

// Helper to transform OpenLayers coordinates to Three.js coordinates
export function transformCoordinates(
  olCoord: number[],
  depth: number = 0,
  exaggerationFactor: number = 5
): THREE.Vector3 {
  return new THREE.Vector3(olCoord[0] ?? 0, -depth * exaggerationFactor, -(olCoord[1] ?? 0));
}
