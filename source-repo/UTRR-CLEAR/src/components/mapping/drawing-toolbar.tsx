'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Separator } from '~/components/ui/separator';
import { Badge } from '~/components/ui/badge';
import { useToast } from '~/hooks/use-toast';
import {
  MapPin,
  PenTool,
  Square,
  Circle,
  Triangle,
  Minus,
  Ruler,
  Type,
  Palette,
  Trash2,
  Undo,
  Redo,
  Save,
  Download,
  Upload,
  RotateCcw,
  Copy,
  Move3D,
  Layers,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { FileUpload } from '~/components/ui/file-upload';

// OpenLayers imports
import Map from 'ol/Map.js';
import { Draw, Modify, Snap, Select as OLSelect } from 'ol/interaction.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import { Style, Stroke, Fill, Circle as CircleStyle, Text } from 'ol/style.js';
import { Feature } from 'ol';
import { Point, LineString, Polygon, Circle as CircleGeom } from 'ol/geom.js';
import { getArea, getLength } from 'ol/sphere.js';
import { safeLog } from '~/lib/error-handler';

// Drawing modes
export type DrawingMode = 'Point' | 'LineString' | 'Polygon' | 'Circle' | 'Text' | null;

// Drawing styles
interface DrawingStyle {
  strokeColor: string;
  strokeWidth: number;
  fillColor: string;
  fillOpacity: number;
  pointRadius: number;
}

interface DrawingToolbarProps {
  map: Map;
  drawingLayer: VectorLayer<VectorSource>;
  onDrawingComplete?: (feature: Feature) => void;
  onMeasurement?: (measurement: { type: string; value: number; unit: string }) => void;
}

export function DrawingToolbar({
  map,
  drawingLayer,
  onDrawingComplete,
  onMeasurement,
}: DrawingToolbarProps) {
  const [activeMode, setActiveMode] = useState<DrawingMode>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isMeasuring, setIsMeasuring] = useState(false);
  const [measurements, setMeasurements] = useState<
    Array<{ id: string; type: string; value: number; unit: string }>
  >([]);
  const { toast } = useToast();

  // Drawing style state
  const [drawingStyle, setDrawingStyle] = useState<DrawingStyle>({
    strokeColor: '#1976d2',
    strokeWidth: 2,
    fillColor: '#1976d2',
    fillOpacity: 0.2,
    pointRadius: 5,
  });

  // Interaction references
  const drawInteractionRef = useRef<Draw | null>(null);
  const modifyInteractionRef = useRef<Modify | null>(null);
  const snapInteractionRef = useRef<Snap | null>(null);
  const selectInteractionRef = useRef<OLSelect | null>(null);

  // History for undo/redo
  const [history, setHistory] = useState<Feature[][]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Import dialog state
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);

  // Initialize interactions
  useEffect(() => {
    if (!map || !drawingLayer) return;

    // Initialize modify interaction
    const source = drawingLayer.getSource();
    if (source) {
      const modifyInteraction = new Modify({ source });
      modifyInteractionRef.current = modifyInteraction;
      map.addInteraction(modifyInteraction);

      // Initialize snap interaction
      const snapInteraction = new Snap({ source });
      snapInteractionRef.current = snapInteraction;
      map.addInteraction(snapInteraction);

      // Initialize select interaction
      const selectInteraction = new OLSelect({
        layers: [drawingLayer],
      });
      selectInteractionRef.current = selectInteraction;
      map.addInteraction(selectInteraction);
    }

    return () => {
      if (modifyInteractionRef.current) {
        map.removeInteraction(modifyInteractionRef.current);
      }
      if (snapInteractionRef.current) {
        map.removeInteraction(snapInteractionRef.current);
      }
      if (selectInteractionRef.current) {
        map.removeInteraction(selectInteractionRef.current);
      }
    };
  }, [map, drawingLayer]);

  // Create current drawing style
  const createCurrentStyle = (): Style => {
    return new Style({
      stroke: new Stroke({
        color: drawingStyle.strokeColor,
        width: drawingStyle.strokeWidth,
      }),
      fill: new Fill({
        color:
          drawingStyle.fillColor +
          Math.round(drawingStyle.fillOpacity * 255)
            .toString(16)
            .padStart(2, '0'),
      }),
      image: new CircleStyle({
        radius: drawingStyle.pointRadius,
        fill: new Fill({ color: drawingStyle.fillColor }),
        stroke: new Stroke({
          color: drawingStyle.strokeColor,
          width: 1,
        }),
      }),
    });
  };

  // Start drawing with specified mode
  const startDrawing = (mode: DrawingMode) => {
    if (!map || !drawingLayer) return;

    // Clear previous drawing interaction
    if (drawInteractionRef.current) {
      map.removeInteraction(drawInteractionRef.current);
      drawInteractionRef.current = null;
    }

    if (!mode) {
      setActiveMode(null);
      setIsDrawing(false);
      return;
    }

    const source = drawingLayer.getSource();
    if (!source) return;

    // Create drawing interaction
    const drawInteraction = new Draw({
      source: source,
      type: mode === 'Circle' ? 'Circle' : mode === 'Text' ? 'Point' : mode,
      style: createCurrentStyle(),
    });

    // Handle drawing start
    drawInteraction.on('drawstart', () => {
      setIsDrawing(true);
    });

    // Handle drawing end
    drawInteraction.on('drawend', (event) => {
      const feature = event.feature;

      // Add metadata to feature
      feature.setProperties({
        drawingType: mode,
        createdAt: new Date().toISOString(),
        style: { ...drawingStyle },
      });

      // Set feature style
      feature.setStyle(createCurrentStyle());

      // Add to history
      saveToHistory();

      // Handle measurements
      if (isMeasuring) {
        handleMeasurement(feature, mode);
      }

      // Callback
      if (onDrawingComplete) {
        onDrawingComplete(feature);
      }

      setIsDrawing(false);

      toast({
        title: 'Drawing Complete',
        description: `${mode} has been added to the map.`,
      });
    });

    map.addInteraction(drawInteraction);
    drawInteractionRef.current = drawInteraction;
    setActiveMode(mode);
  };

  // Handle measurements
  const handleMeasurement = (feature: Feature, mode: DrawingMode) => {
    const geometry = feature.getGeometry();
    if (!geometry) return;

    let measurement: { type: string; value: number; unit: string } | null = null;

    if (mode === 'LineString' && geometry instanceof LineString) {
      const length = getLength(geometry);
      measurement = {
        type: 'length',
        value: length > 1000 ? length / 1000 : length,
        unit: length > 1000 ? 'km' : 'm',
      };
    } else if (
      (mode === 'Polygon' || mode === 'Circle') &&
      (geometry instanceof Polygon || geometry instanceof CircleGeom)
    ) {
      const area = getArea(geometry);
      measurement = {
        type: 'area',
        value: area > 10000 ? area / 1000000 : area,
        unit: area > 10000 ? 'km²' : 'm²',
      };
    }

    if (measurement) {
      const id = `measure_${Date.now()}`;
      const newMeasurement = { id, ...measurement };

      setMeasurements((prev) => [...prev, newMeasurement]);

      // Add measurement label to feature
      const labelText = `${measurement.value.toFixed(2)} ${measurement.unit}`;
      const style = createCurrentStyle();
      style.setText(
        new Text({
          text: labelText,
          font: '12px Calibri,sans-serif',
          fill: new Fill({ color: '#000' }),
          stroke: new Stroke({
            color: '#fff',
            width: 3,
          }),
          offsetY: -15,
        })
      );
      feature.setStyle(style);

      if (onMeasurement) {
        onMeasurement(measurement);
      }
    }
  };

  // Save current state to history
  const saveToHistory = () => {
    const source = drawingLayer.getSource();
    if (!source) return;

    const features = source.getFeatures().map((f: any) => f.clone());
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(features);

    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Undo last action
  const undo = () => {
    if (historyIndex > 0) {
      const source = drawingLayer.getSource();
      if (!source) return;

      const previousState = history[historyIndex - 1];
      if (previousState) {
        source.clear();
        source.addFeatures(previousState.map((f: any) => f.clone()));
        setHistoryIndex(historyIndex - 1);
      }

      toast({
        title: 'Undone',
        description: 'Last action has been undone.',
      });
    }
  };

  // Redo last undone action
  const redo = () => {
    if (historyIndex < history.length - 1) {
      const source = drawingLayer.getSource();
      if (!source) return;

      const nextState = history[historyIndex + 1];
      if (nextState) {
        source.clear();
        source.addFeatures(nextState.map((f: any) => f.clone()));
        setHistoryIndex(historyIndex + 1);
      }

      toast({
        title: 'Redone',
        description: 'Action has been redone.',
      });
    }
  };

  // Clear all drawings
  const clearAll = () => {
    const source = drawingLayer.getSource();
    if (source) {
      source.clear();
      setMeasurements([]);
      saveToHistory();

      toast({
        title: 'Drawings Cleared',
        description: 'All drawings have been removed.',
      });
    }
  };

  // Delete selected features
  const deleteSelected = () => {
    if (!selectInteractionRef.current) return;

    const selectedFeatures = selectInteractionRef.current.getFeatures();
    const source = drawingLayer.getSource();

    if (source && selectedFeatures.getLength() > 0) {
      selectedFeatures.forEach((feature: any) => {
        source.removeFeature(feature);
      });
      selectedFeatures.clear();
      saveToHistory();

      toast({
        title: 'Features Deleted',
        description: `${selectedFeatures.getLength()} feature(s) deleted.`,
      });
    }
  };

  // Export drawings
  const exportDrawings = () => {
    const source = drawingLayer.getSource();
    if (!source) return;

    const features = source.getFeatures();
    const data = {
      type: 'FeatureCollection',
      features: features.map((feature: any) => ({
        type: 'Feature',
        geometry: (feature.getGeometry() as any)?.getCoordinates(),
        properties: feature.getProperties(),
      })),
      measurements: measurements,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `drawings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: 'Drawings exported successfully.',
    });
  };

  // Import drawings
  const handleFileImport = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const data = JSON.parse(content);
        
        if (data.type === 'FeatureCollection' && Array.isArray(data.features)) {
          const source = drawingLayer.getSource();
          if (!source) return;

          // Clear existing features if needed
          // source.clear();

          data.features.forEach((featureData: any) => {
            try {
              // Create geometry based on type
              let geometry;
              const coords = featureData.geometry;
              const geomType = featureData.properties?.geometryType || 
                             (Array.isArray(coords) && coords.length === 2 && typeof coords[0] === 'number' ? 'Point' :
                              Array.isArray(coords) && Array.isArray(coords[0]) && coords[0].length === 2 ? 'LineString' :
                              'Polygon');

              switch (geomType) {
                case 'Point':
                  geometry = new Point(coords);
                  break;
                case 'LineString':
                  geometry = new LineString(coords);
                  break;
                case 'Polygon':
                  geometry = new Polygon([coords]);
                  break;
                case 'Circle':
                  geometry = new CircleGeom(coords.center, coords.radius);
                  break;
                default:
                  safeLog.warn('Unknown geometry type:', geomType);
                  return;
              }

              const feature = new Feature({
                geometry,
                ...featureData.properties,
              });

              source.addFeature(feature);
            } catch (featureError) {
              safeLog.error('Error processing feature:', { error: String(featureError) });
            }
          });

          // Import measurements if available
          if (data.measurements && Array.isArray(data.measurements)) {
            setMeasurements(data.measurements);
          }

          setIsImportDialogOpen(false);
          toast({
            title: 'Import Successful',
            description: `${data.features.length} drawing(s) imported successfully.`,
          });
        } else {
          throw new Error('Invalid file format. Expected GeoJSON FeatureCollection.');
        }
      } catch (error) {
        safeLog.error('Import error:', { error: String(error) });
        toast({
          title: 'Import Failed',
          description: error instanceof Error ? error.message : 'Failed to parse file.',
          variant: 'destructive',
        });
      }
    };

    reader.onerror = () => {
      toast({
        title: 'Import Failed',
        description: 'Failed to read file.',
        variant: 'destructive',
      });
    };

    reader.readAsText(file);
  };

  // Drawing mode buttons
  const drawingModes = [
    { mode: 'Point' as DrawingMode, icon: MapPin, label: 'Point' },
    { mode: 'LineString' as DrawingMode, icon: PenTool, label: 'Line' },
    { mode: 'Polygon' as DrawingMode, icon: Square, label: 'Polygon' },
    { mode: 'Circle' as DrawingMode, icon: Circle, label: 'Circle' },
  ];

  return (
    <Card className="w-80">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <PenTool className="h-4 w-4" />
          Drawing Tools
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Drawing Mode Buttons */}
        <div>
          <Label className="text-xs font-medium mb-2 block">Drawing Mode</Label>
          <div className="grid grid-cols-2 gap-2">
            {drawingModes.map(({ mode, icon: Icon, label }) => (
              <Button
                key={mode}
                size="sm"
                variant={activeMode === mode ? 'default' : 'outline'}
                onClick={() => startDrawing(activeMode === mode ? null : mode)}
                className="flex items-center gap-1"
              >
                <Icon className="h-3 w-3" />
                <span className="text-xs">{label}</span>
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Style Controls */}
        <div className="space-y-3">
          <Label className="text-xs font-medium">Style Settings</Label>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Stroke Color</Label>
              <div className="flex items-center gap-1">
                <input
                  type="color"
                  value={drawingStyle.strokeColor}
                  onChange={(e: any) =>
                    setDrawingStyle((prev) => ({ ...prev, strokeColor: e.target.value }))
                  }
                  className="w-6 h-6 rounded border"
                />
                <Input
                  value={drawingStyle.strokeColor}
                  onChange={(e: any) =>
                    setDrawingStyle((prev) => ({ ...prev, strokeColor: e.target.value }))
                  }
                  className="text-xs h-6 flex-1"
                />
              </div>
            </div>

            <div>
              <Label className="text-xs">Fill Color</Label>
              <div className="flex items-center gap-1">
                <input
                  type="color"
                  value={drawingStyle.fillColor}
                  onChange={(e: any) =>
                    setDrawingStyle((prev) => ({ ...prev, fillColor: e.target.value }))
                  }
                  className="w-6 h-6 rounded border"
                />
                <Input
                  value={drawingStyle.fillColor}
                  onChange={(e: any) =>
                    setDrawingStyle((prev) => ({ ...prev, fillColor: e.target.value }))
                  }
                  className="text-xs h-6 flex-1"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            <div>
              <Label className="text-xs">Stroke Width</Label>
              <Input
                type="number"
                min="1"
                max="10"
                value={drawingStyle.strokeWidth}
                onChange={(e: any) =>
                  setDrawingStyle((prev) => ({
                    ...prev,
                    strokeWidth: parseInt(e.target.value) || 1,
                  }))
                }
                className="text-xs h-6"
              />
            </div>

            <div>
              <Label className="text-xs">Fill Opacity</Label>
              <Input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={drawingStyle.fillOpacity}
                onChange={(e: any) =>
                  setDrawingStyle((prev) => ({
                    ...prev,
                    fillOpacity: parseFloat(e.target.value) || 0,
                  }))
                }
                className="text-xs h-6"
              />
            </div>

            <div>
              <Label className="text-xs">Point Size</Label>
              <Input
                type="number"
                min="2"
                max="20"
                value={drawingStyle.pointRadius}
                onChange={(e: any) =>
                  setDrawingStyle((prev) => ({
                    ...prev,
                    pointRadius: parseInt(e.target.value) || 5,
                  }))
                }
                className="text-xs h-6"
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Measurement Controls */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label className="text-xs font-medium">Measurements</Label>
            <Button
              size="sm"
              variant={isMeasuring ? 'default' : 'outline'}
              onClick={() => setIsMeasuring(!isMeasuring)}
              className="h-6 px-2"
            >
              <Ruler className="h-3 w-3" />
            </Button>
          </div>

          {measurements.length > 0 && (
            <div className="space-y-1">
              {measurements.slice(-3).map((measurement: any) => (
                <div key={measurement.id} className="flex justify-between text-xs">
                  <span className="capitalize">{measurement.type}:</span>
                  <Badge variant="outline" className="text-xs">
                    {measurement.value.toFixed(2)} {measurement.unit}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="grid grid-cols-3 gap-1">
          <Button
            size="sm"
            variant="outline"
            onClick={undo}
            disabled={historyIndex <= 0}
            title="Undo"
          >
            <Undo className="h-3 w-3" />
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={redo}
            disabled={historyIndex >= history.length - 1}
            title="Redo"
          >
            <Redo className="h-3 w-3" />
          </Button>

          <Button size="sm" variant="outline" onClick={deleteSelected} title="Delete Selected">
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>

        <div className="grid grid-cols-3 gap-1">
          <Button size="sm" variant="outline" onClick={clearAll} title="Clear All">
            <RotateCcw className="h-3 w-3" />
          </Button>

          <Button size="sm" variant="outline" onClick={exportDrawings} title="Export">
            <Download className="h-3 w-3" />
          </Button>

          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline" title="Import">
                <Upload className="h-3 w-3" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Import Drawings</DialogTitle>
              </DialogHeader>
              <FileUpload
                onFileSelect={handleFileImport}
                accept={{
                  'application/json': ['.json'],
                  'application/geo+json': ['.geojson'],
                }}
                maxSize={10 * 1024 * 1024} // 10MB
                uploadEndpoint={undefined} // Don't auto-upload, process locally
                className="mt-4"
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Status */}
        {isDrawing && (
          <div className="text-xs text-center text-muted-foreground">
            Drawing {activeMode}... Click to add points
          </div>
        )}

        {activeMode && !isDrawing && (
          <div className="text-xs text-center text-muted-foreground">
            {activeMode} mode active. Click on map to start drawing.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
