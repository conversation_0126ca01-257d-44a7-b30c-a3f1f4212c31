'use client';

import dynamic from 'next/dynamic';
import { Skeleton } from '~/components/ui/skeleton';

// Loading component while Three.js loads
const LoadingPlaceholder = () => (
  <div className="w-full h-[600px] flex items-center justify-center bg-muted rounded-lg">
    <div className="space-y-4 text-center">
      <Skeleton className="h-12 w-12 rounded-full mx-auto" />
      <Skeleton className="h-4 w-48 mx-auto" />
      <p className="text-sm text-muted-foreground">Loading subsurface visualization...</p>
    </div>
  </div>
);

// Dynamic import with loading state
export const SubsurfaceVisualization = dynamic(
  () => import('./subsurface-visualization').then((mod: any) => ({ default: mod.SubsurfaceVisualization })),
  {
    loading: () => <LoadingPlaceholder />,
    ssr: false, // Disable SSR for Three.js
  }
);

// Props type is defined in ./subsurface-visualization but not exported
// If needed, import directly from that file