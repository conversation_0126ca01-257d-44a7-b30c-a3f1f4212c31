'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '~/components/ui/tooltip';
import { 
  ToggleGroup, 
  ToggleGroupItem 
} from '~/components/ui/toggle-group';
import {
  Minus, Square, Circle, Triangle, Move, Copy, RotateCw, 
  Ruler, Calculator, Navigation, Grid, Target, Crosshair,
  Terminal, Settings, HelpCircle, Scissors, Maximize2, 
  FlipHorizontal, Scaling
} from 'lucide-react';
import { CadCommand, CadMode, useCadShortcuts } from '~/hooks/use-cad-shortcuts';
import CadCommandLine from './command-line';
import PrecisionCoordinateInput from '../precision-tools/precision-input';

interface CadToolbarProps {
  onToolChange?: (tool: CadCommand | null) => void;
  onModeToggle?: (mode: CadMode, enabled: boolean) => void;
  onCoordinateInput?: (coordinate: [number, number]) => void;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  compact?: boolean;
}

export const CadToolbar: React.FC<CadToolbarProps> = ({
  onToolChange,
  onModeToggle,
  onCoordinateInput,
  className = "",
  orientation = 'horizontal',
  compact = false
}) => {
  const [commandLineVisible, setCommandLineVisible] = useState<boolean>(false);
  const [precisionInputVisible, setPrecisionInputVisible] = useState<boolean>(false);
  const [settingsVisible, setSettingsVisible] = useState<boolean>(false);
  
  const mapRef = useRef<HTMLDivElement>(null);

  const {
    cadState,
    shortcuts,
    executeCommand,
    toggleMode,
    cancelCurrentOperation,
    isModeActive,
    getActiveCommand
  } = useCadShortcuts({
    onCommandChange: onToolChange,
    onModeToggle: onModeToggle,
    mapRef
  });

  // Tool groups
  const drawTools = [
    { command: CadCommand.LINE, icon: Minus, label: 'Line (L)', shortcut: 'L' },
    { command: CadCommand.CIRCLE, icon: Circle, label: 'Circle (C)', shortcut: 'C' },
    { command: CadCommand.RECTANGLE, icon: Square, label: 'Rectangle (R)', shortcut: 'R' },
    { command: CadCommand.POLYGON, icon: Triangle, label: 'Polygon (P)', shortcut: 'P' },
    { command: CadCommand.ARC, icon: Navigation, label: 'Arc (A)', shortcut: 'A' }
  ];

  const modifyTools = [
    { command: CadCommand.MOVE, icon: Move, label: 'Move (M)', shortcut: 'M' },
    { command: CadCommand.COPY, icon: Copy, label: 'Copy (Shift+C)', shortcut: 'Shift+C' },
    { command: CadCommand.ROTATE, icon: RotateCw, label: 'Rotate (Shift+R)', shortcut: 'Shift+R' },
    { command: CadCommand.SCALE, icon: Scaling, label: 'Scale (S)', shortcut: 'S' },
    { command: CadCommand.MIRROR, icon: FlipHorizontal, label: 'Mirror (MI)', shortcut: 'MI' },
    { command: CadCommand.TRIM, icon: Scissors, label: 'Trim (TR)', shortcut: 'TR' },
    { command: CadCommand.EXTEND, icon: Maximize2, label: 'Extend (EX)', shortcut: 'EX' }
  ];

  const measureTools = [
    { command: CadCommand.MEASURE_DISTANCE, icon: Ruler, label: 'Distance (DI)', shortcut: 'DI' },
    { command: CadCommand.MEASURE_AREA, icon: Calculator, label: 'Area (DA)', shortcut: 'DA' },
    { command: CadCommand.MEASURE_ANGLE, icon: Navigation, label: 'Angle (DG)', shortcut: 'DG' }
  ];

  const modes = [
    { mode: CadMode.ORTHO, icon: Grid, label: 'Ortho (F8)', shortcut: 'F8' },
    { mode: CadMode.SNAP, icon: Target, label: 'Snap (F9)', shortcut: 'F9' },
    { mode: CadMode.GRID, icon: Grid, label: 'Grid (F7)', shortcut: 'F7' },
    { mode: CadMode.POLAR_TRACKING, icon: Crosshair, label: 'Polar (F10)', shortcut: 'F10' }
  ];

  const handleToolClick = useCallback((command: CadCommand) => {
    const isActive = getActiveCommand() === command;
    if (isActive) {
      cancelCurrentOperation();
    } else {
      executeCommand(command);
    }
  }, [executeCommand, cancelCurrentOperation, getActiveCommand]);

  const handleModeToggle = useCallback((mode: CadMode) => {
    toggleMode(mode);
  }, [toggleMode]);

  const handleCommandLineToggle = useCallback(() => {
    setCommandLineVisible(!commandLineVisible);
  }, [commandLineVisible]);

  const handlePrecisionInputToggle = useCallback(() => {
    setPrecisionInputVisible(!precisionInputVisible);
  }, [precisionInputVisible]);

  const renderToolButton = (tool: any, isActive: boolean) => {
    const Icon = tool.icon;
    
    return (
      <TooltipProvider key={tool.command || tool.mode}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={isActive ? "default" : "outline"}
              size={compact ? "sm" : "default"}
              onClick={() => tool.command ? handleToolClick(tool.command) : handleModeToggle(tool.mode)}
              className={`relative ${isActive ? 'bg-primary text-primary-foreground' : ''}`}
            >
              <Icon className={compact ? "h-3 w-3" : "h-4 w-4"} />
              {!compact && (
                <span className="ml-2 hidden lg:inline">{tool.label.split(' (')[0]}</span>
              )}
              {isActive && (
                <Badge variant="secondary" className="absolute -top-1 -right-1 text-xs px-1">
                  ●
                </Badge>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tool.label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const renderModeToggle = (mode: any) => {
    const Icon = mode.icon;
    const isActive = isModeActive(mode.mode);
    
    return (
      <TooltipProvider key={mode.mode}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={isActive ? "default" : "outline"}
              size={compact ? "sm" : "default"}
              onClick={() => handleModeToggle(mode.mode)}
              className={`relative ${isActive ? 'bg-primary text-primary-foreground' : ''}`}
            >
              <Icon className={compact ? "h-3 w-3" : "h-4 w-4"} />
              {!compact && (
                <span className="ml-2 hidden lg:inline">{mode.label.split(' (')[0]}</span>
              )}
              {isActive && (
                <Badge variant="secondary" className="absolute -top-1 -right-1 text-xs px-1">
                  ●
                </Badge>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{mode.label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const layoutClass = orientation === 'vertical' 
    ? 'flex-col space-y-2' 
    : 'flex-row space-x-2 flex-wrap';

  return (
    <div className={`relative ${className}`} ref={mapRef}>
      <Card className="shadow-lg">
        <CardContent className={`p-2 ${layoutClass} flex items-center`}>
          {/* Draw Tools */}
          <div className={`flex ${orientation === 'vertical' ? 'flex-col space-y-1' : 'space-x-1'}`}>
            <div className="text-xs text-muted-foreground px-2 py-1">Draw</div>
            {drawTools.map(tool => renderToolButton(tool, getActiveCommand() === tool.command))}
          </div>

          <Separator orientation={orientation === 'vertical' ? 'horizontal' : 'vertical'} />

          {/* Modify Tools */}
          <div className={`flex ${orientation === 'vertical' ? 'flex-col space-y-1' : 'space-x-1'}`}>
            <div className="text-xs text-muted-foreground px-2 py-1">Modify</div>
            {modifyTools.map(tool => renderToolButton(tool, getActiveCommand() === tool.command))}
          </div>

          <Separator orientation={orientation === 'vertical' ? 'horizontal' : 'vertical'} />

          {/* Measure Tools */}
          <div className={`flex ${orientation === 'vertical' ? 'flex-col space-y-1' : 'space-x-1'}`}>
            <div className="text-xs text-muted-foreground px-2 py-1">Measure</div>
            {measureTools.map(tool => renderToolButton(tool, getActiveCommand() === tool.command))}
          </div>

          <Separator orientation={orientation === 'vertical' ? 'horizontal' : 'vertical'} />

          {/* Mode Toggles */}
          <div className={`flex ${orientation === 'vertical' ? 'flex-col space-y-1' : 'space-x-1'}`}>
            <div className="text-xs text-muted-foreground px-2 py-1">Modes</div>
            {modes.map(mode => renderModeToggle(mode))}
          </div>

          <Separator orientation={orientation === 'vertical' ? 'horizontal' : 'vertical'} />

          {/* Utility Tools */}
          <div className={`flex ${orientation === 'vertical' ? 'flex-col space-y-1' : 'space-x-1'}`}>
            <div className="text-xs text-muted-foreground px-2 py-1">Tools</div>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={commandLineVisible ? "default" : "outline"}
                    size={compact ? "sm" : "default"}
                    onClick={handleCommandLineToggle}
                  >
                    <Terminal className={compact ? "h-3 w-3" : "h-4 w-4"} />
                    {!compact && <span className="ml-2 hidden lg:inline">Command</span>}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Command Line (Ctrl+Shift+C)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={precisionInputVisible ? "default" : "outline"}
                    size={compact ? "sm" : "default"}
                    onClick={handlePrecisionInputToggle}
                  >
                    <Calculator className={compact ? "h-3 w-3" : "h-4 w-4"} />
                    {!compact && <span className="ml-2 hidden lg:inline">Coords</span>}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Precision Coordinates</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size={compact ? "sm" : "default"}
                    onClick={() => setSettingsVisible(!settingsVisible)}
                  >
                    <Settings className={compact ? "h-3 w-3" : "h-4 w-4"} />
                    {!compact && <span className="ml-2 hidden lg:inline">Settings</span>}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>CAD Settings</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </CardContent>
      </Card>

      {/* Active Command Display */}
      {getActiveCommand() && (
        <Card className="mt-2 bg-primary/10 border-primary/20">
          <CardContent className="p-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">
                Active: {getActiveCommand()?.toUpperCase()}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={cancelCurrentOperation}
                className="text-xs"
              >
                Cancel (Esc)
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mode Status */}
      <Card className="mt-2">
        <CardContent className="p-2">
          <div className="flex flex-wrap gap-1">
            {modes.map(mode => (
              <Badge
                key={mode.mode}
                variant={isModeActive(mode.mode) ? "default" : "outline"}
                className="text-xs"
              >
                {mode.label.split(' (')[0]}
                {isModeActive(mode.mode) ? ' ON' : ' OFF'}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Command Line */}
      <CadCommandLine
        visible={commandLineVisible}
        onVisibilityChange={setCommandLineVisible}
        onCoordinateInput={onCoordinateInput}
        className="mt-2"
      />

      {/* Precision Input */}
      {precisionInputVisible && (
        <div className="absolute top-full left-0 mt-2 z-50">
          <PrecisionCoordinateInput
            onCoordinateSubmit={(coord) => {
              onCoordinateInput?.([coord[0] ?? 0, coord[1] ?? 0]);
              setPrecisionInputVisible(false);
            }}
            onCoordinateChange={(coord, isValid) => {
              if (isValid && coord && coord[0] !== undefined && coord[1] !== undefined) {
                onCoordinateInput?.([coord[0], coord[1]]);
              }
            }}
            className="w-80"
          />
        </div>
      )}

      {/* Keyboard Shortcuts Reference */}
      {compact && (
        <Card className="mt-2 bg-muted/50">
          <CardContent className="p-2">
            <div className="text-xs space-y-1">
              <div className="font-medium">Quick Keys:</div>
              <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                <span>L - Line</span>
                <span>C - Circle</span>
                <span>R - Rectangle</span>
                <span>M - Move</span>
                <span>F8 - Ortho</span>
                <span>F9 - Snap</span>
                <span>Esc - Cancel</span>
                <span>? - Help</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CadToolbar;