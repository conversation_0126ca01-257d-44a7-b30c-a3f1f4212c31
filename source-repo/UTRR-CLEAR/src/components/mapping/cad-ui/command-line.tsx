'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Command } from 'cmdk';
import { Input } from '~/components/ui/input';
import { Card, CardContent } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Button } from '~/components/ui/button';
import { Terminal, History, HelpCircle, X } from 'lucide-react';
import { CadCommand } from '~/hooks/use-cad-shortcuts';

export interface CadCommandDefinition {
  command: string;
  aliases: string[];
  description: string;
  category: 'Draw' | 'Modify' | 'Measure' | 'View' | 'Utility' | 'Settings';
  parameters: CommandParameter[];
  examples: string[];
  handler: (args: string[]) => Promise<CommandResult>;
}

export interface CommandParameter {
  name: string;
  type: 'number' | 'coordinate' | 'string' | 'option' | 'boolean';
  required: boolean;
  description: string;
  default?: any;
  options?: string[]; // For option type
  validation?: (value: any) => boolean;
}

export interface CommandResult {
  success: boolean;
  message: string;
  data?: any;
  coordinate?: [number, number];
  error?: string;
}

export interface CommandHistory {
  input: string;
  timestamp: Date;
  result: CommandResult;
}

interface CadCommandLineProps {
  onCommandExecute?: (command: string, args: string[]) => Promise<CommandResult>;
  onCoordinateInput?: (coordinate: [number, number]) => void;
  visible?: boolean;
  onVisibilityChange?: (visible: boolean) => void;
  className?: string;
}

export const CadCommandLine: React.FC<CadCommandLineProps> = ({
  onCommandExecute,
  onCoordinateInput,
  visible = false,
  onVisibilityChange,
  className = ""
}) => {
  const [input, setInput] = useState<string>('');
  const [history, setHistory] = useState<CommandHistory[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [suggestions, setSuggestions] = useState<CadCommandDefinition[]>([]);
  const [showHelp, setShowHelp] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const commandHistoryRef = useRef<HTMLDivElement>(null);

  // Built-in command definitions
  const commands: CadCommandDefinition[] = [
    {
      command: 'line',
      aliases: ['l', 'ln'],
      description: 'Draw a line between two points',
      category: 'Draw',
      parameters: [
        { name: 'start', type: 'coordinate', required: true, description: 'Starting point' },
        { name: 'end', type: 'coordinate', required: true, description: 'Ending point' }
      ],
      examples: ['line 0,0 100,100', 'l @10,20 @30,40'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Line command requires start and end coordinates', error: 'Missing parameters' };
        }
        return { success: true, message: `Line drawn from ${args[0]} to ${args[1]}` };
      }
    },
    {
      command: 'circle',
      aliases: ['c', 'cir'],
      description: 'Draw a circle with center and radius',
      category: 'Draw',
      parameters: [
        { name: 'center', type: 'coordinate', required: true, description: 'Center point' },
        { name: 'radius', type: 'number', required: true, description: 'Circle radius' }
      ],
      examples: ['circle 50,50 25', 'c @0,0 100'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Circle command requires center and radius', error: 'Missing parameters' };
        }
        return { success: true, message: `Circle created at ${args[0]} with radius ${args[1]}` };
      }
    },
    {
      command: 'rectangle',
      aliases: ['rect', 'r'],
      description: 'Draw a rectangle',
      category: 'Draw',
      parameters: [
        { name: 'corner1', type: 'coordinate', required: true, description: 'First corner' },
        { name: 'corner2', type: 'coordinate', required: true, description: 'Opposite corner' }
      ],
      examples: ['rectangle 0,0 100,50', 'rect @10,10 @60,40'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Rectangle command requires two corner points', error: 'Missing parameters' };
        }
        return { success: true, message: `Rectangle created from ${args[0]} to ${args[1]}` };
      }
    },
    {
      command: 'move',
      aliases: ['m', 'mv'],
      description: 'Move selected objects',
      category: 'Modify',
      parameters: [
        { name: 'from', type: 'coordinate', required: true, description: 'Base point' },
        { name: 'to', type: 'coordinate', required: true, description: 'Destination point' }
      ],
      examples: ['move 0,0 100,100', 'm @50,50 @150,150'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Move command requires from and to coordinates', error: 'Missing parameters' };
        }
        return { success: true, message: `Objects moved from ${args[0]} to ${args[1]}` };
      }
    },
    {
      command: 'copy',
      aliases: ['cp', 'co'],
      description: 'Copy selected objects',
      category: 'Modify',
      parameters: [
        { name: 'from', type: 'coordinate', required: true, description: 'Base point' },
        { name: 'to', type: 'coordinate', required: true, description: 'Destination point' }
      ],
      examples: ['copy 0,0 100,100', 'cp @25,25 @75,75'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Copy command requires from and to coordinates', error: 'Missing parameters' };
        }
        return { success: true, message: `Objects copied from ${args[0]} to ${args[1]}` };
      }
    },
    {
      command: 'rotate',
      aliases: ['ro', 'rot'],
      description: 'Rotate selected objects',
      category: 'Modify',
      parameters: [
        { name: 'center', type: 'coordinate', required: true, description: 'Rotation center' },
        { name: 'angle', type: 'number', required: true, description: 'Rotation angle in degrees' }
      ],
      examples: ['rotate 50,50 45', 'ro @0,0 90'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Rotate command requires center point and angle', error: 'Missing parameters' };
        }
        return { success: true, message: `Objects rotated ${args[1]}° around ${args[0]}` };
      }
    },
    {
      command: 'distance',
      aliases: ['dist', 'di'],
      description: 'Measure distance between two points',
      category: 'Measure',
      parameters: [
        { name: 'point1', type: 'coordinate', required: true, description: 'First point' },
        { name: 'point2', type: 'coordinate', required: true, description: 'Second point' }
      ],
      examples: ['distance 0,0 100,100', 'di @10,10 @50,50'],
      handler: async (args: string[]) => {
        if (args.length < 2) {
          return { success: false, message: 'Distance command requires two points', error: 'Missing parameters' };
        }
        // Calculate distance (simplified)
        try {
          const [x1, y1] = parseCoordinate(args[0]!);
          const [x2, y2] = parseCoordinate(args[1]!);
          const distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
          return { success: true, message: `Distance: ${distance.toFixed(2)} units` };
        } catch (error) {
          return { success: false, message: 'Invalid coordinate format', error: 'Parse error' };
        }
      }
    },
    {
      command: 'area',
      aliases: ['ar'],
      description: 'Measure area of selected objects',
      category: 'Measure',
      parameters: [],
      examples: ['area'],
      handler: async (args: string[]) => {
        return { success: true, message: 'Area measurement started. Select objects to measure.' };
      }
    },
    {
      command: 'zoom',
      aliases: ['z'],
      description: 'Zoom to fit, extents, or specific area',
      category: 'View',
      parameters: [
        { name: 'option', type: 'option', required: false, description: 'Zoom option', 
          options: ['extents', 'fit', 'in', 'out', 'previous'], default: 'extents' }
      ],
      examples: ['zoom extents', 'z fit', 'zoom in'],
      handler: async (args: string[]) => {
        const option = args[0] || 'extents';
        return { success: true, message: `Zoomed to ${option}` };
      }
    },
    {
      command: 'pan',
      aliases: ['p'],
      description: 'Pan the view',
      category: 'View',
      parameters: [
        { name: 'direction', type: 'coordinate', required: true, description: 'Pan direction or target point' }
      ],
      examples: ['pan 100,100', 'p @50,50'],
      handler: async (args: string[]) => {
        if (args.length < 1) {
          return { success: false, message: 'Pan command requires direction or target point', error: 'Missing parameters' };
        }
        return { success: true, message: `Panned to ${args[0]}` };
      }
    },
    {
      command: 'help',
      aliases: ['h', '?'],
      description: 'Show command help',
      category: 'Utility',
      parameters: [
        { name: 'command', type: 'string', required: false, description: 'Specific command to get help for' }
      ],
      examples: ['help', 'help line', '? circle'],
      handler: async (args: string[]) => {
        if (args.length > 0) {
          const cmd = findCommand(args[0]!);
          if (cmd) {
            return { success: true, message: `${cmd.command}: ${cmd.description}\nExamples: ${cmd.examples.join(', ')}` };
          } else {
            return { success: false, message: `Command '${args[0]}' not found`, error: 'Unknown command' };
          }
        }
        setShowHelp(true);
        return { success: true, message: 'Help dialog opened' };
      }
    },
    {
      command: 'clear',
      aliases: ['cls'],
      description: 'Clear command history',
      category: 'Utility',
      parameters: [],
      examples: ['clear', 'cls'],
      handler: async (args: string[]) => {
        setHistory([]);
        return { success: true, message: 'Command history cleared' };
      }
    }
  ];

  // Focus input when component becomes visible
  useEffect(() => {
    if (visible && inputRef.current) {
      inputRef.current.focus();
    }
  }, [visible]);

  // Auto-scroll to bottom of history
  useEffect(() => {
    if (commandHistoryRef.current) {
      commandHistoryRef.current.scrollTop = commandHistoryRef.current.scrollHeight;
    }
  }, [history]);

  // Find command by name or alias
  const findCommand = useCallback((name: string): CadCommandDefinition | undefined => {
    const lowerName = name.toLowerCase();
    return commands.find((cmd: any) => 
      cmd.command.toLowerCase() === lowerName || 
      cmd.aliases.some((alias: any) => alias.toLowerCase() === lowerName)
    );
  }, [commands]);

  // Parse coordinate from string (e.g., "100,50", "@10,20")
  const parseCoordinate = useCallback((coordStr: string): [number, number] => {
    const cleaned = coordStr.replace('@', '').trim();
    const parts = cleaned.split(',').map(p => parseFloat(p.trim()));
    
    const x = parts[0];
    const y = parts[1];
    if (parts.length >= 2 && typeof x === 'number' && typeof y === 'number' && !isNaN(x) && !isNaN(y)) {
      return [x, y];
    }
    
    throw new Error(`Invalid coordinate format: ${coordStr}`);
  }, []);

  // Update suggestions based on input
  const updateSuggestions = useCallback((input: string) => {
    if (!input.trim()) {
      setSuggestions([]);
      return;
    }

    const searchTerm = input.toLowerCase();
    const filtered = commands.filter(cmd => 
      cmd.command.toLowerCase().includes(searchTerm) ||
      cmd.aliases.some((alias: any) => alias.toLowerCase().includes(searchTerm)) ||
      cmd.description.toLowerCase().includes(searchTerm)
    );

    setSuggestions(filtered.slice(0, 10)); // Limit to 10 suggestions
  }, [commands]);

  // Handle input change
  const handleInputChange = useCallback((value: string) => {
    setInput(value);
    setHistoryIndex(-1);
    updateSuggestions(value);
  }, [updateSuggestions]);

  // Execute command
  const executeCommand = useCallback(async (commandLine: string) => {
    if (!commandLine.trim()) return;

    setIsProcessing(true);
    
    try {
      const parts = commandLine.trim().split(/\s+/);
      const commandName = parts[0]?.toLowerCase() ?? '';
      const args = parts.slice(1);

      let result: CommandResult;

      // Check for coordinate input
      if (commandLine.includes(',') && !commandName) {
        try {
          const coordinate = parseCoordinate(commandLine);
          onCoordinateInput?.(coordinate);
          result = { success: true, message: `Coordinate entered: ${coordinate[0]}, ${coordinate[1]}`, coordinate };
        } catch (error) {
          result = { success: false, message: 'Invalid coordinate format', error: 'Parse error' };
        }
      } else {
        // Find and execute command
        const command = findCommand(commandName);
        
        if (command) {
          try {
            result = await command.handler(args);
          } catch (error) {
            result = { success: false, message: 'Command execution failed', error: String(error) };
          }
        } else if (onCommandExecute) {
          // Try external command handler
          result = await onCommandExecute(commandName, args);
        } else {
          result = { success: false, message: `Unknown command: ${commandName}`, error: 'Unknown command' };
        }
      }

      // Add to history
      const historyEntry: CommandHistory = {
        input: commandLine,
        timestamp: new Date(),
        result
      };

      setHistory(prev => [...prev, historyEntry]);
      setInput('');
      setSuggestions([]);
      setHistoryIndex(-1);

    } catch (error) {
      const errorResult: CommandResult = {
        success: false,
        message: 'Command failed',
        error: String(error) || 'Unknown error'
      };
      
      setHistory(prev => [...prev, {
        input: commandLine,
        timestamp: new Date(),
        result: errorResult
      }]);
    }
    
    setIsProcessing(false);
  }, [findCommand, onCommandExecute, onCoordinateInput, parseCoordinate]);

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
        e.preventDefault();
        if (input.trim()) {
          executeCommand(input);
        }
        break;
        
      case 'Escape':
        e.preventDefault();
        setInput('');
        setSuggestions([]);
        onVisibilityChange?.(false);
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        if (history.length > 0) {
          const newIndex = Math.min(historyIndex + 1, history.length - 1);
          setHistoryIndex(newIndex);
          setInput(history[history.length - 1 - newIndex]?.input ?? '');
        }
        break;
        
      case 'ArrowDown':
        e.preventDefault();
        if (historyIndex > 0) {
          const newIndex = historyIndex - 1;
          setHistoryIndex(newIndex);
          setInput(history[history.length - 1 - newIndex]?.input ?? '');
        } else if (historyIndex === 0) {
          setHistoryIndex(-1);
          setInput('');
        }
        break;
        
      case 'Tab':
        e.preventDefault();
        if (suggestions.length > 0 && suggestions[0]) {
          setInput(suggestions[0].command + ' ');
          setSuggestions([]);
        }
        break;
    }
  }, [input, history, historyIndex, suggestions, executeCommand, onVisibilityChange]);

  // Handle suggestion click
  const handleSuggestionClick = useCallback((command: CadCommandDefinition) => {
    setInput(command.command + ' ');
    setSuggestions([]);
    inputRef.current?.focus();
  }, []);

  if (!visible) return null;

  return (
    <Card className={`fixed bottom-4 left-4 right-4 z-50 ${className}`}>
      <CardContent className="p-0">
        {/* Command History */}
        {history.length > 0 && (
          <ScrollArea 
            className="h-32 p-3 border-b" 
            ref={commandHistoryRef}
          >
            <div className="space-y-1 text-sm font-mono">
              {history.slice(-10).map((entry, index) => (
                <div key={index} className="space-y-1">
                  <div className="text-muted-foreground">
                    Command: {entry.input}
                  </div>
                  <div className={entry.result.success ? 'text-green-600' : 'text-red-600'}>
                    {entry.result.success ? '✓' : '✗'} {entry.result.message}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}

        {/* Suggestions */}
        {suggestions.length > 0 && (
          <div className="border-b bg-muted/50">
            <ScrollArea className="max-h-32">
              {suggestions.map((cmd, index) => (
                <button
                  key={index}
                  className="w-full text-left p-2 hover:bg-accent hover:text-accent-foreground text-sm"
                  onClick={() => handleSuggestionClick(cmd)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-mono font-semibold">{cmd.command}</span>
                      {cmd.aliases.length > 0 && (
                        <span className="text-muted-foreground ml-2">
                          ({cmd.aliases.join(', ')})
                        </span>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {cmd.category}
                    </Badge>
                  </div>
                  <div className="text-muted-foreground text-xs mt-1">
                    {cmd.description}
                  </div>
                </button>
              ))}
            </ScrollArea>
          </div>
        )}

        {/* Input Area */}
        <div className="flex items-center p-3 gap-2">
          <Terminal className="h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            value={input}
            onChange={(e: any) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Enter command or coordinates..."
            className="font-mono"
            disabled={isProcessing}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowHelp(true)}
            className="px-2"
          >
            <HelpCircle className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onVisibilityChange?.(false)}
            className="px-2"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Status */}
        {isProcessing && (
          <div className="p-2 bg-muted text-center text-sm">
            Processing command...
          </div>
        )}
      </CardContent>

      {/* Help Dialog */}
      {showHelp && (
        <Card className="absolute bottom-full left-0 right-0 mb-2 max-h-96">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">CAD Commands</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHelp(false)}
                className="px-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <ScrollArea className="max-h-64">
              <div className="space-y-3">
                {Object.entries(
                  commands.reduce((acc: any, cmd: any) => {
                    if (!acc[cmd.category]) acc[cmd.category] = [];
                    acc[cmd.category].push(cmd);
                    return acc;
                  }, {} as Record<string, CadCommandDefinition[]>)
                ).map(([category, categoryCommands]) => (
                  <div key={category}>
                    <h4 className="font-semibold text-sm mb-2">{category}</h4>
                    <div className="space-y-1 ml-2">
                      {(categoryCommands as CadCommandDefinition[]).map((cmd: CadCommandDefinition, index: number) => (
                        <div key={index} className="text-sm">
                          <span className="font-mono font-semibold">{cmd.command}</span>
                          {cmd.aliases.length > 0 && (
                            <span className="text-muted-foreground ml-1">
                              ({cmd.aliases.join(', ')})
                            </span>
                          )}
                          <div className="text-muted-foreground ml-4">
                            {cmd.description}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
              <p><kbd>Enter</kbd> - Execute command</p>
              <p><kbd>Esc</kbd> - Close command line</p>
              <p><kbd>↑/↓</kbd> - Navigate history</p>
              <p><kbd>Tab</kbd> - Accept suggestion</p>
              <p>Coordinates: x,y or @x,y (relative)</p>
            </div>
          </CardContent>
        </Card>
      )}
    </Card>
  );
};

export default CadCommandLine;