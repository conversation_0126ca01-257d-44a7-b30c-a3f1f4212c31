import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Switch } from '~/components/ui/switch';
import { Badge } from '~/components/ui/badge';
import { Slider } from '~/components/ui/slider';
import { Separator } from '~/components/ui/separator';
import { Label } from '~/components/ui/label';
import {
  Layers,
  Eye,
  EyeOff,
  Settings,
  ChevronDown,
  ChevronRight,
  MapPin,
  Globe,
  Box,
} from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '~/components/ui/collapsible';
import type Map from 'ol/Map.js';
import type { Layer } from 'ol/layer.js';
import { UTILITY_TYPES } from '../../gis/utility-types';

// Define types locally until we have a proper types file
export interface MapLayer {
  name: string;
  title: string;
  type: 'vector' | 'tile' | 'raster' | '3d';
  visible: boolean;
  groupId?: string;
  isGroup?: boolean;
  supports3D?: boolean;
  render3D?: boolean;
  visualMode?: string;
  verticalScale?: number;
  elevationOffset?: number;
}

interface LayerGroup {
  id: string;
  title: string;
  expanded: boolean;
  layers: MapLayer[];
  supports3D?: boolean;
}

interface LayerMenuProps {
  map: Map | null;
  active: boolean;
  mode3D?: boolean;
  onModeChange?: (mode: '2d' | '3d') => void;
  onLayerVisibilityChange?: (layerName: string, visible: boolean) => void;
  onLayer3DToggle?: (layerName: string, enabled: boolean) => void;
}

export function LayerMenu({
  map,
  active,
  mode3D = false,
  onModeChange,
  onLayerVisibilityChange,
  onLayer3DToggle,
}: LayerMenuProps) {
  const [layerGroups, setLayerGroups] = useState<LayerGroup[]>([]);
  const [opacity, setOpacity] = useState<Record<string, number>>({});
  const [visible, setVisible] = useState<boolean>(false);

  // Initialize layer data
  useEffect(() => {
    const initialGroups: LayerGroup[] = [
      {
        id: 'utilities',
        title: 'Utilities',
        expanded: true,
        supports3D: true,
        layers: [
          {
            name: 'water_lines',
            title: 'Water Lines',
            type: 'vector',
            visible: true,
            groupId: 'utilities',
            supports3D: true,
            render3D: false,
            visualMode: 'surface',
          },
          {
            name: 'gas_lines',
            title: 'Gas Lines',
            type: 'vector',
            visible: true,
            groupId: 'utilities',
            supports3D: true,
            render3D: false,
            visualMode: 'underground',
          },
          {
            name: 'electric_lines',
            title: 'Electric Lines',
            type: 'vector',
            visible: true,
            groupId: 'utilities',
            supports3D: true,
            render3D: false,
            visualMode: 'overhead',
          },
          {
            name: 'telecom_lines',
            title: 'Telecom Lines',
            type: 'vector',
            visible: false,
            groupId: 'utilities',
            supports3D: true,
            render3D: false,
            visualMode: 'underground',
          },
          {
            name: 'sewer_lines',
            title: 'Sewer Lines',
            type: 'vector',
            visible: false,
            groupId: 'utilities',
            supports3D: true,
            render3D: false,
            visualMode: 'underground',
          },
        ],
      },
      {
        id: 'basemaps',
        title: 'Base Maps',
        expanded: false,
        layers: [
          {
            name: 'satellite',
            title: 'Satellite Imagery',
            type: 'tile',
            visible: false,
          },
          {
            name: 'streets',
            title: 'Street Map',
            type: 'tile',
            visible: true,
          },
          {
            name: 'terrain',
            title: 'Terrain',
            type: 'tile',
            visible: false,
          },
        ],
      },
      {
        id: 'references',
        title: 'Reference Layers',
        expanded: false,
        layers: [
          {
            name: 'parcels',
            title: 'Property Parcels',
            type: 'vector',
            visible: false,
          },
          {
            name: 'right_of_way',
            title: 'Right of Way',
            type: 'vector',
            visible: false,
          },
          {
            name: 'easements',
            title: 'Utility Easements',
            type: 'vector',
            visible: false,
          },
        ],
      },
      {
        id: 'analysis',
        title: 'Analysis',
        expanded: false,
        supports3D: true,
        layers: [
          {
            name: 'conflicts',
            title: 'Utility Conflicts',
            type: 'vector',
            visible: true,
            groupId: 'analysis',
            supports3D: true,
            render3D: false,
          },
          {
            name: 'clearance_zones',
            title: 'Clearance Zones',
            type: 'vector',
            visible: false,
            groupId: 'analysis',
            supports3D: true,
            render3D: false,
          },
        ],
      },
    ];

    setLayerGroups(initialGroups);

    // Initialize opacity values
    const opacityValues: Record<string, number> = {};
    initialGroups.forEach((group: any) => {
      group.layers.forEach((layer: any) => {
        opacityValues[layer.name] = 100;
      });
    });
    setOpacity(opacityValues);
  }, []);

  useEffect(() => {
    setVisible(active);
  }, [active]);

  const toggleLayerVisibility = (layerName: string) => {
    setLayerGroups((prev) =>
      prev.map((group: any) => ({
        ...group,
        layers: group.layers.map((layer: any) =>
          layer.name === layerName ? { ...layer, visible: !layer.visible } : layer
        ),
      }))
    );

    const layer = layerGroups.flatMap((g) => g.layers).find((l: any) => l.name === layerName);
    if (layer && onLayerVisibilityChange) {
      onLayerVisibilityChange(layerName, !layer.visible);
    }
  };

  const toggleGroupExpansion = (groupId: string) => {
    setLayerGroups((prev) =>
      prev.map((group: any) => (group.id === groupId ? { ...group, expanded: !group.expanded } : group))
    );
  };

  const handleOpacityChange = (layerName: string, value: number[]) => {
    setOpacity((prev) => ({ ...prev, [layerName]: value[0] ?? 1 }));
  };

  const toggle3DMode = () => {
    const newMode = mode3D ? '2d' : '3d';
    if (onModeChange) {
      onModeChange(newMode);
    }
  };

  const toggle3DLayer = (layerName: string) => {
    setLayerGroups((prev) =>
      prev.map((group: any) => ({
        ...group,
        layers: group.layers.map((layer: any) =>
          layer.name === layerName ? { ...layer, render3D: !layer.render3D } : layer
        ),
      }))
    );

    const layer = layerGroups.flatMap((g) => g.layers).find((l: any) => l.name === layerName);
    if (layer && onLayer3DToggle) {
      onLayer3DToggle(layerName, !layer.render3D);
    }
  };

  const getLayerIcon = (layer: MapLayer) => {
    if (layer.type === '3d') return <Box className="h-4 w-4" />;
    if (layer.type === 'tile' || layer.type === 'raster') return <Globe className="h-4 w-4" />;
    return <MapPin className="h-4 w-4" />;
  };

  const getUtilityColor = (layerName: string) => {
    if (layerName.includes('water')) return '#3b82f6';
    if (layerName.includes('gas')) return '#eab308';
    if (layerName.includes('electric')) return '#ef4444';
    if (layerName.includes('telecom')) return '#22c55e';
    if (layerName.includes('sewer')) return '#a855f7';
    return '#6b7280';
  };

  if (!visible) {
    return null;
  }

  return (
    <div className="absolute top-4 left-4 z-[1000] w-80">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Layers className="h-5 w-5" />
              Layers
            </CardTitle>
            <div className="flex items-center gap-2">
              <Label htmlFor="3d-mode" className="text-sm">
                3D
              </Label>
              <Switch id="3d-mode" checked={mode3D} onCheckedChange={toggle3DMode} />
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {layerGroups.map((group: any) => (
            <div key={group.id} className="space-y-2">
              <Collapsible
                open={group.expanded}
                onOpenChange={() => toggleGroupExpansion(group.id)}
              >
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between p-2 h-auto">
                    <span className="font-medium">{group.title}</span>
                    <div className="flex items-center gap-2">
                      {group.supports3D && mode3D && (
                        <Badge variant="outline" className="text-xs">
                          3D
                        </Badge>
                      )}
                      {group.expanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </CollapsibleTrigger>

                <CollapsibleContent className="space-y-2 pl-4">
                  {group.layers.map((layer: any) => (
                    <div key={layer.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="flex items-center gap-2">
                            {getLayerIcon(layer)}
                            <div
                              className="w-3 h-3 rounded-full border"
                              style={{ backgroundColor: getUtilityColor(layer.name) }}
                            />
                            <span className="text-sm">{layer.title}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={layer.visible}
                              onCheckedChange={() => toggleLayerVisibility(layer.name)}
                            />
                            {layer.supports3D && mode3D && (
                              <Switch
                                checked={layer.render3D}
                                onCheckedChange={() => toggle3DLayer(layer.name)}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {layer.visible && (
                        <div className="pl-5">
                          <div className="flex items-center gap-2">
                            <Label className="text-xs">Opacity:</Label>
                            <Slider
                              value={[opacity[layer.name] || 1]}
                              onValueChange={(value) => handleOpacityChange(layer.name, value)}
                              max={1}
                              min={0}
                              step={0.1}
                              className="flex-1"
                            />
                            <span className="text-xs w-8">
                              {Math.round((opacity[layer.name] || 1) * 100)}%
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}