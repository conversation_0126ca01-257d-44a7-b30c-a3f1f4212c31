import React, { useState, useEffect } from 'react';
import type { Coordinate } from 'ol/coordinate.js';
import { Card, CardContent } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Info, X, Edit2, Save, XCircle } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Separator } from '~/components/ui/separator';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import type Map from 'ol/Map.js';
import type Feature from 'ol/Feature.js';
import { toLonLat } from 'ol/proj.js';
import { UTILITY_TYPES } from '../../gis/utility-types';

// Define UtilityType locally until we have a proper types file
export type UtilityType = 'water' | 'gas' | 'electric' | 'telecom' | 'sewer' | 'steam';

interface FeatureInfoProps {
  map: Map | null;
  active: boolean;
  onFeatureUpdate?: (feature: Feature, properties: Record<string, any>) => void;
}

interface FeatureProperties {
  utilityType?: UtilityType;
  diameter?: number;
  material?: string;
  depth?: number;
  pressure?: number;
  installationDate?: string;
  owner?: string;
  status?: string;
  notes?: string;
  confidence?: string;
  conflictId?: string;
  coordinates?: Coordinate;
}

export function FeatureInfo({ map, active, onFeatureUpdate }: FeatureInfoProps) {
  const [featureInfo, setFeatureInfo] = useState<FeatureProperties | null>(null);
  const [selectedFeature, setSelectedFeature] = useState<Feature | null>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [editableInfo, setEditableInfo] = useState<FeatureProperties>({});
  const [isDirty, setIsDirty] = useState<boolean>(false);

  // Reset edit mode when component becomes inactive
  useEffect(() => {
    if (!active) {
      setEditMode(false);
      setEditableInfo({});
      setIsDirty(false);
    }
  }, [active]);

  useEffect(() => {
    if (!map || !active) {
      setFeatureInfo(null);
      setVisible(false);
      return;
    }

    const clickListener = map.on('click', (event) => {
      // Exit edit mode when clicking on a new feature
      if (editMode) {
        if (isDirty && !window.confirm('You have unsaved changes. Discard changes?')) {
          return;
        }
        setEditMode(false);
        setEditableInfo({});
        setIsDirty(false);
      }

      // Get clicked features
      const features = map.getFeaturesAtPixel(event.pixel);

      if (features && features.length > 0) {
        const feature = features[0] as Feature;
        const properties = feature.getProperties();
        const geometry = feature.getGeometry();

        // Get coordinates
        let coordinates: Coordinate | undefined;
        if (geometry) {
          const extent = geometry.getExtent();
          const center = [
            ((extent?.[0] ?? 0) + (extent?.[2] ?? 0)) / 2, 
            ((extent?.[1] ?? 0) + (extent?.[3] ?? 0)) / 2
          ];
          coordinates = toLonLat(center);
        }

        const info: FeatureProperties = {
          utilityType: properties.utilityType || 'water',
          diameter: properties.diameter,
          material: properties.material,
          depth: properties.depth,
          pressure: properties.pressure,
          installationDate: properties.installationDate,
          owner: properties.owner,
          status: properties.status || 'active',
          notes: properties.notes,
          confidence: properties.confidence || 'high',
          conflictId: properties.conflictId,
          coordinates,
        };

        setFeatureInfo(info);
        setSelectedFeature(feature);
        setEditableInfo(info);
        setVisible(true);
      } else {
        setFeatureInfo(null);
        setVisible(false);
      }
    });

    return () => {
      // Remove listener when component unmounts or becomes inactive
      if (clickListener) {
        map.un('click', clickListener.listener);
      }
    };
  }, [map, active, editMode, isDirty]);

  const handleEdit = () => {
    setEditMode(true);
    setEditableInfo(featureInfo || {});
    setIsDirty(false);
  };

  const handleSave = () => {
    if (selectedFeature && onFeatureUpdate) {
      onFeatureUpdate(selectedFeature, editableInfo);
    }
    setFeatureInfo(editableInfo);
    setEditMode(false);
    setIsDirty(false);
  };

  const handleCancel = () => {
    setEditableInfo(featureInfo || {});
    setEditMode(false);
    setIsDirty(false);
  };

  const handleInputChange = (field: keyof FeatureProperties, value: any) => {
    setEditableInfo((prev) => ({ ...prev, [field]: value }));
    setIsDirty(true);
  };

  const getUtilityBadgeColor = (type: UtilityType) => {
    const colors = {
      water: 'bg-blue-100 text-blue-800',
      gas: 'bg-yellow-100 text-yellow-800',
      electric: 'bg-red-100 text-red-800',
      telecom: 'bg-green-100 text-green-800',
      sewer: 'bg-purple-100 text-purple-800',
      steam: 'bg-orange-100 text-orange-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'abandoned':
        return 'bg-gray-100 text-gray-800';
      case 'proposed':
        return 'bg-blue-100 text-blue-800';
      case 'under_construction':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!visible || !featureInfo) {
    return null;
  }

  return (
    <div className="absolute top-4 right-4 z-[1000] w-80">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              <span className="font-medium">Feature Information</span>
            </div>
            <div className="flex items-center gap-1">
              {!editMode && (
                <Button size="sm" variant="outline" onClick={handleEdit}>
                  <Edit2 className="h-3 w-3" />
                </Button>
              )}
              <Button size="sm" variant="outline" onClick={() => setVisible(false)}>
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {editMode && (
            <div className="flex items-center gap-2 mb-3">
              <Button size="sm" onClick={handleSave} disabled={!isDirty}>
                <Save className="h-3 w-3 mr-1" />
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel}>
                <XCircle className="h-3 w-3 mr-1" />
                Cancel
              </Button>
            </div>
          )}

          <div className="space-y-3">
            {/* Utility Type */}
            <div>
              <Label className="text-xs font-medium text-gray-500">Utility Type</Label>
              {editMode ? (
                <Select
                  value={editableInfo.utilityType}
                  onValueChange={(value) => handleInputChange('utilityType', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(UTILITY_TYPES).map((type: any) => (
                      <SelectItem key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Badge className={getUtilityBadgeColor(featureInfo.utilityType || 'water')}>
                  {(featureInfo.utilityType?.charAt(0).toUpperCase() ?? '') +
                    (featureInfo.utilityType?.slice(1) ?? '')}
                </Badge>
              )}
            </div>

            {/* Status */}
            <div>
              <Label className="text-xs font-medium text-gray-500">Status</Label>
              {editMode ? (
                <Select
                  value={editableInfo.status}
                  onValueChange={(value) => handleInputChange('status', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="abandoned">Abandoned</SelectItem>
                    <SelectItem value="proposed">Proposed</SelectItem>
                    <SelectItem value="under_construction">Under Construction</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge className={getStatusBadgeColor(featureInfo.status || 'active')}>
                  {featureInfo.status?.replace('_', ' ') || 'Active'}
                </Badge>
              )}
            </div>

            <Separator />

            {/* Physical Properties */}
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs font-medium text-gray-500">Diameter</Label>
                {editMode ? (
                  <Input
                    type="number"
                    value={editableInfo.diameter || ''}
                    onChange={(e: any) => handleInputChange('diameter', parseFloat(e.target.value))}
                    className="h-8"
                    placeholder="inches"
                  />
                ) : (
                  <div className="text-sm">
                    {featureInfo.diameter ? `${featureInfo.diameter}"` : 'N/A'}
                  </div>
                )}
              </div>

              <div>
                <Label className="text-xs font-medium text-gray-500">Depth</Label>
                {editMode ? (
                  <Input
                    type="number"
                    value={editableInfo.depth || ''}
                    onChange={(e: any) => handleInputChange('depth', parseFloat(e.target.value))}
                    className="h-8"
                    placeholder="feet"
                  />
                ) : (
                  <div className="text-sm">
                    {featureInfo.depth ? `${featureInfo.depth} ft` : 'N/A'}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Label className="text-xs font-medium text-gray-500">Material</Label>
              {editMode ? (
                <Input
                  value={editableInfo.material || ''}
                  onChange={(e: any) => handleInputChange('material', e.target.value)}
                  className="h-8"
                  placeholder="e.g., PVC, Steel, Cast Iron"
                />
              ) : (
                <div className="text-sm">{featureInfo.material || 'N/A'}</div>
              )}
            </div>

            {/* Owner */}
            <div>
              <Label className="text-xs font-medium text-gray-500">Owner</Label>
              {editMode ? (
                <Input
                  value={editableInfo.owner || ''}
                  onChange={(e: any) => handleInputChange('owner', e.target.value)}
                  className="h-8"
                  placeholder="Utility company name"
                />
              ) : (
                <div className="text-sm">{featureInfo.owner || 'N/A'}</div>
              )}
            </div>

            {/* Coordinates */}
            {featureInfo.coordinates && (
              <div>
                <Label className="text-xs font-medium text-gray-500">Coordinates</Label>
                <div className="text-xs text-gray-600">
                  {featureInfo.coordinates?.[1]?.toFixed(6)}, {featureInfo.coordinates?.[0]?.toFixed(6)}
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <Label className="text-xs font-medium text-gray-500">Notes</Label>
              {editMode ? (
                <Textarea
                  value={editableInfo.notes || ''}
                  onChange={(e: any) => handleInputChange('notes', e.target.value)}
                  className="h-16 text-sm"
                  placeholder="Additional information..."
                />
              ) : (
                <div className="text-sm text-gray-600">
                  {featureInfo.notes || 'No notes available'}
                </div>
              )}
            </div>

            {/* Confidence Level */}
            <div>
              <Label className="text-xs font-medium text-gray-500">Confidence</Label>
              {editMode ? (
                <Select
                  value={editableInfo.confidence}
                  onValueChange={(value) => handleInputChange('confidence', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge variant={featureInfo.confidence === 'high' ? 'default' : 'secondary'}>
                  {featureInfo.confidence || 'High'}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
