'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Ruler, X } from 'lucide-react';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import type Map from 'ol/Map.js';
import Draw from 'ol/interaction/Draw.js';
import Overlay from 'ol/Overlay.js';
import { Vector as VectorSource } from 'ol/source.js';
import { Vector as VectorLayer } from 'ol/layer.js';
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style.js';
import { LineString, Polygon } from 'ol/geom.js';
import { getArea, getLength } from 'ol/sphere.js';
import { unByKey } from 'ol/Observable.js';
import { CoordinateTransformer } from '~/lib/mapping/coordinate-utils';
import type { ExtendedCoordinate } from '~/lib/mapping/coordinate-utils';
import { toExtendedCoordinate } from '~/lib/mapping/geometry-utils';
import { safeLog } from '~/lib/error-handler';
import type { Coordinate } from 'ol/coordinate.js';

interface MeasureControlProps {
  map: Map | null;
  active: boolean;
  elevationSource?: any;
}

type MeasureType = 'line' | 'area' | '3d-line' | null;

export function MeasureControl({ map, active, elevationSource }: MeasureControlProps) {
  const [measureType, setMeasureType] = useState<MeasureType>(null);
  const [result, setResult] = useState<string>('');
  const [activeMeasurement, setActiveMeasurement] = useState<boolean>(false);

  const measureSourceRef = useRef<VectorSource | null>(null);
  const measureLayerRef = useRef<VectorLayer<VectorSource> | null>(null);
  const drawInteractionRef = useRef<Draw | null>(null);
  const overlaysRef = useRef<Overlay[]>([]);
  const listenerKeysRef = useRef<any[]>([]);

  const cleanupMeasurement = useCallback(() => {
    if (map && drawInteractionRef.current) {
      map.removeInteraction(drawInteractionRef.current);
      drawInteractionRef.current = null;
    }

    listenerKeysRef.current.forEach((key: any) => unByKey(key));
    listenerKeysRef.current = [];

    if (map) {
      overlaysRef.current.forEach((overlay: any) => map.removeOverlay(overlay));
      overlaysRef.current = [];
    }

    if (measureSourceRef.current) {
      measureSourceRef.current.clear();
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const source = new VectorSource();
    measureSourceRef.current = source;

    const layer = new VectorLayer({
      source,
      style: new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)',
        }),
        stroke: new Stroke({
          color: '#ffcc33',
          width: 2,
        }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({
            color: '#ffcc33',
          }),
        }),
      }),
      properties: {
        name: 'measure_layer',
        title: 'Measurements',
      },
    });

    measureLayerRef.current = layer;
    map.addLayer(layer);

    return () => {
      if (map && measureLayerRef.current) {
        map.removeLayer(measureLayerRef.current);
      }

      cleanupMeasurement();
    };
  }, [map, cleanupMeasurement]);

  useEffect(() => {
    if (!active) {
      cleanupMeasurement();
      setMeasureType(null);
      setResult('');
      setActiveMeasurement(false);
    }
  }, [active, cleanupMeasurement]);

  useEffect(() => {
    if (!map || !active || !measureType) {
      cleanupMeasurement();
      return;
    }

    if (measureSourceRef.current) {
      const type = measureType === 'area' ? 'Polygon' : 'LineString';
      const interaction = new Draw({
        source: measureSourceRef.current,
        type,
        style: new Style({
          fill: new Fill({
            color: 'rgba(255, 255, 255, 0.2)',
          }),
          stroke: new Stroke({
            color: 'rgba(0, 0, 0, 0.5)',
            lineDash: [10, 10],
            width: 2,
          }),
          image: new CircleStyle({
            radius: 5,
            stroke: new Stroke({
              color: 'rgba(0, 0, 0, 0.7)',
            }),
            fill: new Fill({
              color: 'rgba(255, 255, 255, 0.2)',
            }),
          }),
        }),
      });

      map.addInteraction(interaction);
      drawInteractionRef.current = interaction;

      let listener: any;

      const createMeasureTooltip = () => {
        const element = document.createElement('div');
        element.className =
          'ol-tooltip ol-tooltip-measure bg-[#24292f] text-white px-2 py-1 text-xs rounded shadow';
        element.style.position = 'relative';
        element.style.background = '#24292f';

        const tooltip = new Overlay({
          element,
          offset: [0, -15],
          positioning: 'bottom-center',
          stopEvent: false,
          insertFirst: false,
        });

        map.addOverlay(tooltip);
        overlaysRef.current.push(tooltip);
        return tooltip;
      };

      const measureTooltip = createMeasureTooltip();

      const formatLength = (line: LineString) => {
        const length = getLength(line);
        let output;

        if (length > 1000) {
          output = `${(length / 1000).toFixed(2)} km`;
        } else {
          output = `${length.toFixed(2)} m`;
        }

        return output;
      };

      const formatArea = (polygon: Polygon) => {
        const area = getArea(polygon);
        let output;

        if (area > 10000) {
          output = `${(area / 1000000).toFixed(2)} km²`;
        } else {
          output = `${area.toFixed(2)} m²`;
        }

        return output;
      };

      const format3DDistance = (line: LineString) => {
        const length2D = getLength(line);
        const rawCoordinates = line.getCoordinates();
        const coordinates = rawCoordinates.map(coord => toExtendedCoordinate(coord));

        if (coordinates.length < 2) {
          return `0 m`;
        }

        let distance3D = 0;
        let elevationChange = 0;
        let maxElevation = Number.NEGATIVE_INFINITY;
        let minElevation = Number.POSITIVE_INFINITY;

        for (let i = 1; i < coordinates.length; i++) {
          const start = coordinates[i - 1];
          const end = coordinates[i];

          if (!start || !end) continue;

          let startZ = CoordinateTransformer.getZCoordinate(start as Coordinate);
          let endZ = CoordinateTransformer.getZCoordinate(end as Coordinate);

          if ((startZ === null || endZ === null) && elevationSource) {
            try {
              startZ = startZ ?? elevationSource.getElevationAtCoordinate(start);
              endZ = endZ ?? elevationSource.getElevationAtCoordinate(end);
            } catch (error) {
              safeLog.warn('Failed to get elevation data:', { error: String(error) });
              startZ = startZ ?? 0;
              endZ = endZ ?? 0;
            }
          } else {
            startZ = startZ ?? 0;
            endZ = endZ ?? 0;
          }

          maxElevation = Math.max(maxElevation ?? 0, startZ ?? 0, endZ ?? 0);
          minElevation = Math.min(minElevation ?? 0, startZ ?? 0, endZ ?? 0);

          const segmentDistance = CoordinateTransformer.calculate3DDistance(
            [start[0] ?? 0, start[1] ?? 0, startZ ?? 0],
            [end[0] ?? 0, end[1] ?? 0, endZ ?? 0]
          );

          distance3D += segmentDistance;
          elevationChange += Math.abs((endZ ?? 0) - (startZ ?? 0));
        }

        const totalElevationDiff = maxElevation - minElevation;

        let output;
        if (distance3D > 1000) {
          output = `${(distance3D / 1000).toFixed(2)} km (2D: ${(length2D / 1000).toFixed(2)} km)`;
        } else {
          output = `${distance3D.toFixed(2)} m (2D: ${length2D.toFixed(2)} m)`;
        }

        output += `\nElevation change: ${elevationChange.toFixed(2)} m`;
        output += `\nMin-Max difference: ${totalElevationDiff.toFixed(2)} m`;

        return output;
      };

      listener = interaction.on('drawstart', (event) => {
        if (measureSourceRef.current) {
          measureSourceRef.current.clear();
        }

        overlaysRef.current.forEach((overlay: any) => {
          if (overlay !== measureTooltip) {
            map.removeOverlay(overlay);
          }
        });

        overlaysRef.current = [measureTooltip];

        let currentMeasure = '';
        let tooltipCoord: number[] | undefined;

        const listener = event.feature.getGeometry()?.on('change', (e) => {
          const geom = e.target;

          if (geom instanceof LineString) {
            if (measureType === '3d-line') {
              currentMeasure = format3DDistance(geom);
            } else {
              currentMeasure = formatLength(geom);
            }
            tooltipCoord = geom.getLastCoordinate();
          } else if (geom instanceof Polygon) {
            currentMeasure = formatArea(geom);
            tooltipCoord = geom.getInteriorPoint().getCoordinates();
          }

          const tooltipElement = measureTooltip.getElement();
          if (tooltipCoord && tooltipElement) {
            tooltipElement.innerHTML = currentMeasure.replace(/\n/g, '<br/>');
            tooltipElement.style.whiteSpace = 'nowrap';
            tooltipElement.style.minWidth = '200px';
            measureTooltip.setPosition(tooltipCoord);
            setResult(currentMeasure);
          }
        });

        listenerKeysRef.current.push(listener);
      });

      interaction.on('drawend', () => {
        setActiveMeasurement(false);
      });

      listenerKeysRef.current.push(listener);
      setActiveMeasurement(true);
    }

    return () => {
      cleanupMeasurement();
    };
  }, [map, active, measureType, elevationSource, cleanupMeasurement]);

  const handleMeasure = (type: MeasureType) => {
    if (measureType === type) {
      setMeasureType(null);
      cleanupMeasurement();
    } else {
      setMeasureType(type);
      setResult('');
    }
  };

  if (!active) return null;

  return (
    <Card className="absolute bottom-4 left-4 z-10 shadow-md">
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center text-sm font-medium">
            <Ruler className="h-4 w-4 mr-1" />
            <span>Measure</span>
          </div>
          {activeMeasurement && (
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
              onClick={() => cleanupMeasurement()}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            size="sm"
            variant={measureType === 'line' ? 'default' : 'outline'}
            onClick={() => handleMeasure('line')}
            className="px-2 py-1 h-auto text-xs"
          >
            Distance (2D)
          </Button>

          <Button
            size="sm"
            variant={measureType === '3d-line' ? 'default' : 'outline'}
            onClick={() => handleMeasure('3d-line')}
            className="px-2 py-1 h-auto text-xs"
          >
            Distance (3D)
          </Button>

          <Button
            size="sm"
            variant={measureType === 'area' ? 'default' : 'outline'}
            onClick={() => handleMeasure('area')}
            className="px-2 py-1 h-auto text-xs"
          >
            Area
          </Button>
        </div>

        {result && (
          <div className="mt-2">
            <div className="bg-secondary text-secondary-foreground px-2.5 py-1 rounded-md text-xs whitespace-pre-line">
              {result}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
