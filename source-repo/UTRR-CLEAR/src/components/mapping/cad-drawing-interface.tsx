'use client';

import React, { useEffect, useRef, useState } from 'react';
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import TileLayer from 'ol/layer/Tile.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import OSM from 'ol/source/OSM.js';
import { fromLonLat } from 'ol/proj.js';
import { defaults as defaultControls } from 'ol/control.js';
import { Feature } from 'ol';

// Import components
import { DimensionToolbar } from './dimension-toolbar';
import { DrawingToolbar } from './drawing-toolbar';
import { Card } from '~/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { 
  Download, 
  Upload, 
  Layers, 
  PenTool, 
  Ruler,
  Settings,
  FileDown,
  Eye,
  EyeOff
} from 'lucide-react';

// Import utility tools
import { AdvancedDrawingTools } from '~/components/gis/advanced-draw-tools';
import { UTILITY_TYPES, type UtilityType } from '~/components/gis/utility-types';
import type { DimensionType } from '~/components/gis/dimension-tools';

interface CADDrawingInterfaceProps {
  projectId?: string;
  onSave?: (features: Feature[]) => void;
}

/**
 * Complete CAD-style drawing interface with utility lines and dimensioning tools
 */
export function CADDrawingInterface({ 
  projectId, 
  onSave 
}: CADDrawingInterfaceProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<Map | null>(null);
  
  // Layer sources
  const [utilitySource, setUtilitySource] = useState<VectorSource | null>(null);
  const [dimensionSource, setDimensionSource] = useState<VectorSource | null>(null);
  const [drawingSource, setDrawingSource] = useState<VectorSource | null>(null);
  
  // UI state
  const [activeTab, setActiveTab] = useState('utilities');
  const [selectedUtilityType, setSelectedUtilityType] = useState<UtilityType>('electric');
  const [layerVisibility, setLayerVisibility] = useState({
    utilities: true,
    dimensions: true,
    drawings: true,
  });

  // Statistics
  const [stats, setStats] = useState({
    utilities: 0,
    dimensions: 0,
    drawings: 0,
  });

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    // Create sources
    const utilSource = new VectorSource();
    const dimSource = new VectorSource();
    const drawSource = new VectorSource();

    // Create layers with distinct styling
    const utilityLayer = new VectorLayer({
      source: utilSource,
      zIndex: 10,
      visible: true,
    });

    const dimensionLayer = new VectorLayer({
      source: dimSource,
      zIndex: 20, // Dimensions on top
      visible: true,
    });

    const drawingLayer = new VectorLayer({
      source: drawSource,
      zIndex: 5,
      visible: true,
    });

    // Create map
    const mapInstance = new Map({
      target: mapRef.current,
      layers: [
        new TileLayer({
          source: new OSM(),
        }),
        drawingLayer,
        utilityLayer,
        dimensionLayer,
      ],
      view: new View({
        center: fromLonLat([-86.7816, 36.1627]), // Nashville, TN
        zoom: 16, // Closer zoom for CAD work
      }),
      controls: defaultControls({
        attribution: false,
        zoom: true,
      }),
    });

    // Set states
    setMap(mapInstance);
    setUtilitySource(utilSource);
    setDimensionSource(dimSource);
    setDrawingSource(drawSource);

    // Listen for feature changes
    const updateStats = () => {
      setStats({
        utilities: utilSource.getFeatures().length,
        dimensions: dimSource.getFeatures().length,
        drawings: drawSource.getFeatures().length,
      });
    };

    utilSource.on('change', updateStats);
    dimSource.on('change', updateStats);
    drawSource.on('change', updateStats);

    // Cleanup
    return () => {
      mapInstance.setTarget(undefined);
    };
  }, []);

  // Toggle layer visibility
  const toggleLayerVisibility = (layer: keyof typeof layerVisibility) => {
    setLayerVisibility(prev => ({
      ...prev,
      [layer]: !prev[layer],
    }));

    // Update map layer visibility
    if (map) {
      const layers = map.getLayers().getArray();
      const layerIndex = layer === 'utilities' ? 2 : layer === 'dimensions' ? 3 : 1;
      const targetLayer = layers[layerIndex];
      if (targetLayer) {
        targetLayer.setVisible(!layerVisibility[layer]);
      }
    }
  };

  // Export all features to DXF
  const exportToDXF = () => {
    // This would combine all features from all sources
    // and export them as a single DXF file
    console.log('Exporting to DXF...');
    // Implementation would go here
  };

  // Save features
  const handleSave = () => {
    if (!utilitySource || !dimensionSource || !drawingSource) return;

    const allFeatures = [
      ...utilitySource.getFeatures(),
      ...dimensionSource.getFeatures(),
      ...drawingSource.getFeatures(),
    ];

    onSave?.(allFeatures);
  };

  return (
    <div className="h-full w-full flex">
      {/* Left Panel - Tools and Controls */}
      <div className="w-80 bg-background border-r flex flex-col">
        <div className="p-4 border-b">
          <h3 className="font-semibold">CAD Drawing Tools</h3>
          <p className="text-sm text-muted-foreground">
            Professional utility mapping interface
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="utilities">Utilities</TabsTrigger>
            <TabsTrigger value="dimensions">Dimensions</TabsTrigger>
            <TabsTrigger value="layers">Layers</TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1">
            <TabsContent value="utilities" className="p-4 space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Utility Types</h4>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(UTILITY_TYPES).map(([key, utility]) => (
                    <Button
                      key={key}
                      variant={selectedUtilityType === key ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedUtilityType(key as UtilityType)}
                      className="justify-start"
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: utility.color }}
                      />
                      {utility.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-2">Drawing Tools</h4>
                <p className="text-xs text-muted-foreground mb-2">
                  Use the drawing toolbar on the map to draw utilities
                </p>
              </div>
            </TabsContent>

            <TabsContent value="dimensions" className="p-4 space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Dimension Tools</h4>
                <p className="text-xs text-muted-foreground">
                  Use the dimension toolbar to add construction-style dimensions
                </p>
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-2">Quick Tips</h4>
                <ul className="text-xs space-y-1 text-muted-foreground">
                  <li>• Linear: Click two points for distance</li>
                  <li>• Angular: Click three points for angle</li>
                  <li>• Radius: Click and drag for circle</li>
                  <li>• Leader: Draw line with text annotation</li>
                </ul>
              </div>
            </TabsContent>

            <TabsContent value="layers" className="p-4 space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Layer Visibility</h4>
                <div className="space-y-2">
                  {Object.entries(layerVisibility).map(([layer, visible]) => (
                    <div
                      key={layer}
                      className="flex items-center justify-between p-2 rounded-md hover:bg-accent"
                    >
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => toggleLayerVisibility(layer as keyof typeof layerVisibility)}
                        >
                          {visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                        </Button>
                        <span className="text-sm capitalize">{layer}</span>
                      </div>
                      <Badge variant="secondary">
                        {stats[layer as keyof typeof stats]}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Button 
                  className="w-full" 
                  size="sm"
                  onClick={handleSave}
                >
                  <FileDown className="h-4 w-4 mr-2" />
                  Save Project
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full" 
                  size="sm"
                  onClick={exportToDXF}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export to DXF
                </Button>
              </div>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </div>

      {/* Right Panel - Map */}
      <div className="flex-1 relative">
        <div ref={mapRef} className="absolute inset-0" />

        {/* Dimension Toolbar - Top Center */}
        <div className="absolute top-4 left-1/2 -translate-x-1/2 z-20">
          {map && dimensionSource && (
            <DimensionToolbar
              map={map}
              source={dimensionSource}
            />
          )}
        </div>

        {/* Drawing Toolbar - Left Side */}
        <div className="absolute top-20 left-4 z-20">
          {map && drawingSource && (
            <DrawingToolbar
              map={map}
              drawingLayer={
                map.getLayers().getArray().find(
                  layer => layer instanceof VectorLayer && layer.getSource() === drawingSource
                ) as VectorLayer<VectorSource>
              }
            />
          )}
        </div>

        {/* Coordinates Display */}
        <div className="absolute bottom-4 left-4 bg-background/90 backdrop-blur-sm rounded-md px-3 py-2 text-xs">
          <div>Project: {projectId || 'Untitled'}</div>
          <div>Features: {stats.utilities + stats.dimensions + stats.drawings}</div>
        </div>

        {/* Scale */}
        <div className="absolute bottom-4 right-4 bg-background/90 backdrop-blur-sm rounded-md px-3 py-2 text-xs">
          Scale: 1:1000
        </div>
      </div>
    </div>
  );
}