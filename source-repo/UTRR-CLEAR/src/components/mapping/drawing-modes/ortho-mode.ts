import type { Coordinate } from 'ol/coordinate.js';
import { getDistance } from 'ol/sphere.js';

export interface OrthoConstraint {
  angle: number; // in degrees
  description: string;
  tolerance: number; // in degrees
}

export interface PolarTrackingSettings {
  enabled: boolean;
  angles: number[]; // constraint angles in degrees
  tolerance: number; // degrees
  showGuides: boolean;
  incrementalAngles: boolean;
  customIncrements: number[];
}

export interface OrthoModeSettings {
  enabled: boolean;
  constraintAngles: number[]; // Standard: [0, 45, 90, 135, 180, 225, 270, 315]
  tolerance: number; // degrees
  snapToAngles: boolean;
  preserveDistance: boolean;
  showGuides: boolean;
  polarTracking: PolarTrackingSettings;
}

export interface ConstraintResult {
  constrained: boolean;
  originalCoordinate: Coordinate;
  constrainedCoordinate: Coordinate;
  angle: number;
  distance: number;
  constraintType: 'ortho' | 'polar' | 'none';
  guideLine?: {
    start: Coordinate;
    end: Coordinate;
    angle: number;
  };
}

export class OrthoMode {
  private settings: OrthoModeSettings;
  private basePoint: Coordinate | null = null;
  private lastConstrainedPoint: Coordinate | null = null;
  private activeConstraints: OrthoConstraint[] = [];

  constructor(settings?: Partial<OrthoModeSettings>) {
    this.settings = {
      enabled: true,
      constraintAngles: [0, 45, 90, 135, 180, 225, 270, 315],
      tolerance: 5, // degrees
      snapToAngles: true,
      preserveDistance: true,
      showGuides: true,
      polarTracking: {
        enabled: true,
        angles: [15, 30, 45, 60, 90, 120, 135, 150, 180, 210, 225, 240, 270, 300, 315, 330],
        tolerance: 2,
        showGuides: true,
        incrementalAngles: true,
        customIncrements: [15, 30, 45]
      },
      ...settings
    };

    this.initializeConstraints();
  }

  private initializeConstraints(): void {
    this.activeConstraints = this.settings.constraintAngles.map(angle => ({
      angle,
      description: this.getAngleDescription(angle),
      tolerance: this.settings.tolerance
    }));
  }

  private getAngleDescription(angle: number): string {
    const normalizedAngle = this.normalizeAngle(angle);
    
    const directions: Record<number, string> = {
      0: 'East (0°)',
      45: 'Northeast (45°)',
      90: 'North (90°)',
      135: 'Northwest (135°)',
      180: 'West (180°)',
      225: 'Southwest (225°)',
      270: 'South (270°)',
      315: 'Southeast (315°)'
    };

    return directions[normalizedAngle] || `${normalizedAngle}°`;
  }

  setSettings(settings: Partial<OrthoModeSettings>): void {
    this.settings = { ...this.settings, ...settings };
    this.initializeConstraints();
  }

  getSettings(): OrthoModeSettings {
    return { ...this.settings };
  }

  setBasePoint(coordinate: Coordinate): void {
    this.basePoint = coordinate;
    this.lastConstrainedPoint = null;
  }

  getBasePoint(): Coordinate | null {
    return this.basePoint;
  }

  clearBasePoint(): void {
    this.basePoint = null;
    this.lastConstrainedPoint = null;
  }

  constrainPoint(
    targetCoordinate: Coordinate, 
    baseCoordinate?: Coordinate
  ): ConstraintResult {
    if (!this.settings.enabled) {
      return {
        constrained: false,
        originalCoordinate: targetCoordinate,
        constrainedCoordinate: targetCoordinate,
        angle: 0,
        distance: 0,
        constraintType: 'none'
      };
    }

    const effectiveBase = baseCoordinate || this.basePoint;
    
    if (!effectiveBase) {
      return {
        constrained: false,
        originalCoordinate: targetCoordinate,
        constrainedCoordinate: targetCoordinate,
        angle: 0,
        distance: 0,
        constraintType: 'none'
      };
    }

    // Calculate current angle and distance
    const deltaX = (targetCoordinate[0] ?? 0) - (effectiveBase[0] ?? 0);
    const deltaY = (targetCoordinate[1] ?? 0) - (effectiveBase[1] ?? 0);
    const currentAngle = this.calculateAngle(effectiveBase, targetCoordinate);
    const currentDistance = getDistance(effectiveBase, targetCoordinate);

    // Try ortho mode constraints first
    const orthoResult = this.applyOrthoConstraints(
      targetCoordinate,
      effectiveBase,
      currentAngle,
      currentDistance
    );

    if (orthoResult.constrained) {
      this.lastConstrainedPoint = orthoResult.constrainedCoordinate;
      return orthoResult;
    }

    // Try polar tracking constraints
    if (this.settings.polarTracking.enabled) {
      const polarResult = this.applyPolarTrackingConstraints(
        targetCoordinate,
        effectiveBase,
        currentAngle,
        currentDistance
      );

      if (polarResult.constrained) {
        this.lastConstrainedPoint = polarResult.constrainedCoordinate;
        return polarResult;
      }
    }

    // No constraints applied
    return {
      constrained: false,
      originalCoordinate: targetCoordinate,
      constrainedCoordinate: targetCoordinate,
      angle: currentAngle,
      distance: currentDistance,
      constraintType: 'none'
    };
  }

  private applyOrthoConstraints(
    targetCoordinate: Coordinate,
    baseCoordinate: Coordinate,
    currentAngle: number,
    currentDistance: number
  ): ConstraintResult {
    let bestConstraint: OrthoConstraint | null = null;
    let smallestDifference = Infinity;

    // Find the closest constraint angle
    for (const constraint of this.activeConstraints) {
      const angleDifference = this.getAngleDifference(currentAngle, constraint.angle);
      
      if (angleDifference <= constraint.tolerance && angleDifference < smallestDifference) {
        bestConstraint = constraint;
        smallestDifference = angleDifference;
      }
    }

    if (!bestConstraint) {
      return {
        constrained: false,
        originalCoordinate: targetCoordinate,
        constrainedCoordinate: targetCoordinate,
        angle: currentAngle,
        distance: currentDistance,
        constraintType: 'none'
      };
    }

    // Apply the constraint
    const constrainedCoordinate = this.calculateConstrainedPoint(
      baseCoordinate,
      bestConstraint.angle,
      this.settings.preserveDistance ? currentDistance : currentDistance
    );

    return {
      constrained: true,
      originalCoordinate: targetCoordinate,
      constrainedCoordinate,
      angle: bestConstraint.angle,
      distance: currentDistance,
      constraintType: 'ortho',
      guideLine: this.settings.showGuides ? {
        start: baseCoordinate,
        end: this.extendLine(baseCoordinate, bestConstraint.angle, currentDistance * 2),
        angle: bestConstraint.angle
      } : undefined
    };
  }

  private applyPolarTrackingConstraints(
    targetCoordinate: Coordinate,
    baseCoordinate: Coordinate,
    currentAngle: number,
    currentDistance: number
  ): ConstraintResult {
    const polarSettings = this.settings.polarTracking;
    let bestAngle: number | null = null;
    let smallestDifference = Infinity;

    // Check standard polar tracking angles
    for (const trackingAngle of polarSettings.angles) {
      const angleDifference = this.getAngleDifference(currentAngle, trackingAngle);
      
      if (angleDifference <= polarSettings.tolerance && angleDifference < smallestDifference) {
        bestAngle = trackingAngle;
        smallestDifference = angleDifference;
      }
    }

    // Check incremental angles if enabled
    if (polarSettings.incrementalAngles && !bestAngle) {
      for (const increment of polarSettings.customIncrements) {
        for (let angle = 0; angle < 360; angle += increment) {
          const angleDifference = this.getAngleDifference(currentAngle, angle);
          
          if (angleDifference <= polarSettings.tolerance && angleDifference < smallestDifference) {
            bestAngle = angle;
            smallestDifference = angleDifference;
          }
        }
      }
    }

    if (bestAngle === null) {
      return {
        constrained: false,
        originalCoordinate: targetCoordinate,
        constrainedCoordinate: targetCoordinate,
        angle: currentAngle,
        distance: currentDistance,
        constraintType: 'none'
      };
    }

    // Apply polar tracking constraint
    const constrainedCoordinate = this.calculateConstrainedPoint(
      baseCoordinate,
      bestAngle,
      currentDistance
    );

    return {
      constrained: true,
      originalCoordinate: targetCoordinate,
      constrainedCoordinate,
      angle: bestAngle,
      distance: currentDistance,
      constraintType: 'polar',
      guideLine: polarSettings.showGuides ? {
        start: baseCoordinate,
        end: this.extendLine(baseCoordinate, bestAngle, currentDistance * 2),
        angle: bestAngle
      } : undefined
    };
  }

  private calculateConstrainedPoint(
    baseCoordinate: Coordinate,
    angle: number,
    distance: number
  ): Coordinate {
    const angleRad = (angle * Math.PI) / 180;
    
    return [
      (baseCoordinate[0] ?? 0) + distance * Math.cos(angleRad),
      (baseCoordinate[1] ?? 0) + distance * Math.sin(angleRad),
      baseCoordinate[2] || 0
    ];
  }

  private extendLine(
    baseCoordinate: Coordinate,
    angle: number,
    distance: number
  ): Coordinate {
    return this.calculateConstrainedPoint(baseCoordinate, angle, distance);
  }

  private calculateAngle(start: Coordinate, end: Coordinate): number {
    const deltaX = (end[0] ?? 0) - (start[0] ?? 0);
    const deltaY = (end[1] ?? 0) - (start[1] ?? 0);
    
    // Calculate angle in degrees (0° = East, 90° = North)
    let angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
    
    return this.normalizeAngle(angle);
  }

  private normalizeAngle(angle: number): number {
    while (angle < 0) angle += 360;
    while (angle >= 360) angle -= 360;
    return angle;
  }

  private getAngleDifference(angle1: number, angle2: number): number {
    const diff = Math.abs(this.normalizeAngle(angle1) - this.normalizeAngle(angle2));
    return Math.min(diff, 360 - diff);
  }

  // Additional utility methods
  isEnabled(): boolean {
    return this.settings.enabled;
  }

  enable(): void {
    this.settings.enabled = true;
  }

  disable(): void {
    this.settings.enabled = false;
  }

  addConstraintAngle(angle: number): void {
    const normalizedAngle = this.normalizeAngle(angle);
    if (!this.settings.constraintAngles.includes(normalizedAngle)) {
      this.settings.constraintAngles.push(normalizedAngle);
      this.initializeConstraints();
    }
  }

  removeConstraintAngle(angle: number): void {
    const normalizedAngle = this.normalizeAngle(angle);
    const index = this.settings.constraintAngles.indexOf(normalizedAngle);
    if (index > -1) {
      this.settings.constraintAngles.splice(index, 1);
      this.initializeConstraints();
    }
  }

  getActiveConstraints(): OrthoConstraint[] {
    return [...this.activeConstraints];
  }

  setTolerance(tolerance: number): void {
    this.settings.tolerance = Math.max(0.1, Math.min(45, tolerance));
    this.initializeConstraints();
  }

  getTolerance(): number {
    return this.settings.tolerance;
  }

  setPolarTrackingEnabled(enabled: boolean): void {
    this.settings.polarTracking.enabled = enabled;
  }

  isPolarTrackingEnabled(): boolean {
    return this.settings.polarTracking.enabled;
  }

  addPolarTrackingAngle(angle: number): void {
    const normalizedAngle = this.normalizeAngle(angle);
    if (!this.settings.polarTracking.angles.includes(normalizedAngle)) {
      this.settings.polarTracking.angles.push(normalizedAngle);
    }
  }

  removePolarTrackingAngle(angle: number): void {
    const normalizedAngle = this.normalizeAngle(angle);
    const index = this.settings.polarTracking.angles.indexOf(normalizedAngle);
    if (index > -1) {
      this.settings.polarTracking.angles.splice(index, 1);
    }
  }

  getConstraintInfo(): {
    enabled: boolean;
    constraintAngles: number[];
    tolerance: number;
    polarTrackingEnabled: boolean;
    lastConstrainedPoint: Coordinate | null;
  } {
    return {
      enabled: this.settings.enabled,
      constraintAngles: [...this.settings.constraintAngles],
      tolerance: this.settings.tolerance,
      polarTrackingEnabled: this.settings.polarTracking.enabled,
      lastConstrainedPoint: this.lastConstrainedPoint
    };
  }

  // Static helper methods
  static createStandardConstraints(): number[] {
    return [0, 45, 90, 135, 180, 225, 270, 315];
  }

  static createCustomConstraints(increment: number): number[] {
    const constraints: number[] = [];
    for (let angle = 0; angle < 360; angle += increment) {
      constraints.push(angle);
    }
    return constraints;
  }

  static createQuadrantConstraints(): number[] {
    return [0, 90, 180, 270];
  }

  static createArchitecturalConstraints(): number[] {
    return [0, 30, 45, 60, 90, 120, 135, 150, 180, 210, 225, 240, 270, 300, 315, 330];
  }
}

export default OrthoMode;