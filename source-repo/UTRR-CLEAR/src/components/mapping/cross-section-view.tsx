'use client';

import React, { useEffect, useRef, useState } from 'react';
import type { Map as OLMap } from 'ol';
import type { LineString, Geometry } from 'ol/geom.js';
import type { Feature } from 'ol';
import type { ViewMode } from '~/lib/mapping/types';
import type { LayerManager } from './layers/layer-manager';
import { getUtilityTypeColor } from '~/lib/mapping/utility-styles';
import {
  detectVerticalConflicts,
  getUtilityDepth,
  createConflictPoints,
  type VerticalConflict,
} from '~/lib/mapping/vertical-conflict-detection';

interface CrossSectionViewProps {
  map: OLMap | null;
  layerManager?: LayerManager;
  active: boolean;
  conflictThreshold?: number; // Distance in meters to consider a conflict
}

/**
 * Cross-section view for displaying vertical conflicts between utilities
 * Renders a 2D side-view showing utilities at their respective depths
 */
export function CrossSectionView({
  map,
  layerManager,
  active,
  conflictThreshold = 0.5, // Default 0.5 meter threshold
}: CrossSectionViewProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [conflicts, setConflicts] = useState<VerticalConflict[]>([]);

  // Detect conflicts when active
  useEffect(() => {
    if (!map || !layerManager || !active) {
      return;
    }

    // Get utility lines
    const utilityLayer = layerManager.getVectorLayer('utility_lines');
    if (!utilityLayer) return;

    const utilities = utilityLayer.getSource()?.getFeatures() as Feature<LineString>[];
    if (!utilities || utilities.length < 2) return;

    // Detect vertical conflicts between utilities using our dedicated module
    const detectedConflicts = detectVerticalConflicts(utilities, {
      depthToleranceHard: conflictThreshold || 1.0,
      depthToleranceSoft: (conflictThreshold || 1.0) * 2,
      checkIntersections: true,
    });
    // Convert ConflictData to VerticalConflict
    const convertedConflicts = detectedConflicts.map(conflict => {
      // Find the actual features by ID
      const utility1 = utilities.find((u: any) => u.getId()?.toString() === conflict.utility1Id);
      const utility2 = utilities.find((u: any) => u.getId()?.toString() === conflict.utility2Id);
      
      if (!utility1 || !utility2) {
        return null;
      }

      return {
        utility1: utility1 as Feature<Geometry>,
        utility2: utility2 as Feature<Geometry>, 
        utility1Type: utility1.get('type') || 'unknown',
        utility2Type: utility2.get('type') || 'unknown',
        severity: conflict.type === 'hard' ? 'high' : conflict.type === 'soft' ? 'medium' : 'low',
        distance: conflict.depthDifference,
        requiredClearance: conflict.type === 'hard' ? 1.0 : 2.0,
      };
    }).filter(c => c !== null) as VerticalConflict[];

    setConflicts(convertedConflicts);

    // Create conflict points on the map
    createConflictPoints(detectedConflicts, layerManager);
  }, [map, layerManager, active, conflictThreshold]);

  // Render the cross-section view when active
  useEffect(() => {
    if (!active || !canvasRef.current || conflicts.length === 0) {
      return;
    }

    renderCrossSection(canvasRef.current, conflicts);
  }, [active, conflicts]);

  // Hide when not active
  if (!active) {
    return null;
  }

  return (
    <div className="absolute bottom-4 left-4 right-4 h-32 bg-background/80 backdrop-blur-sm rounded-md shadow-md p-2">
      <h3 className="text-sm font-semibold mb-1">Cross-Section View (Vertical Conflicts)</h3>
      {conflicts.length === 0 ? (
        <p className="text-xs text-muted-foreground">No vertical conflicts detected</p>
      ) : (
        <>
          <canvas ref={canvasRef} className="w-full h-20 border border-border/50 rounded-sm" />
          <p className="text-xs text-muted-foreground mt-1">
            {conflicts.length} potential {conflicts.length === 1 ? 'conflict' : 'conflicts'}{' '}
            detected
          </p>
        </>
      )}
    </div>
  );
}

/**
 * Render the cross-section view on a canvas
 */
function renderCrossSection(canvas: HTMLCanvasElement, conflicts: VerticalConflict[]) {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Set up the canvas dimensions
  const width = canvas.width;
  const height = canvas.height;

  // Ground level is 20% from the top
  const groundY = height * 0.2;

  // Draw ground level
  ctx.beginPath();
  ctx.moveTo(0, groundY);
  ctx.lineTo(width, groundY);
  ctx.strokeStyle = '#6b7280';
  ctx.lineWidth = 2;
  ctx.stroke();

  // Find max depth among all utilities
  let maxDepth = 0;
  conflicts.forEach((conflict: any) => {
    const depth1 = getUtilityDepth(conflict.utility1);
    const depth2 = getUtilityDepth(conflict.utility2);
    maxDepth = Math.max(maxDepth, depth1, depth2);
  });

  // Calculate scale (pixels per meter)
  const depthScale = (height - groundY - 20) / (maxDepth > 0 ? maxDepth : 10);

  // Draw each conflict
  conflicts.forEach((conflict, index) => {
    // Calculate horizontal position for this conflict
    const x = (width * (index + 1)) / (conflicts.length + 1);

    // Get utility details
    const utility1Type = conflict.utility1Type;
    const utility2Type = conflict.utility2Type;
    const depth1 = getUtilityDepth(conflict.utility1);
    const depth2 = getUtilityDepth(conflict.utility2);
    const diameter1 = conflict.utility1.get('diameter') || 0.1;
    const diameter2 = conflict.utility2.get('diameter') || 0.1;

    // Calculate vertical positions
    const y1 = groundY + depth1 * depthScale;
    const y2 = groundY + depth2 * depthScale;

    // Draw utility lines
    // Utility 1
    ctx.beginPath();
    ctx.moveTo(x - 30, y1);
    ctx.lineTo(x + 30, y1);
    ctx.strokeStyle = getUtilityTypeColor(utility1Type);
    ctx.lineWidth = diameter1 * depthScale || 2;
    ctx.stroke();

    // Utility 2
    ctx.beginPath();
    ctx.moveTo(x - 30, y2);
    ctx.lineTo(x + 30, y2);
    ctx.strokeStyle = getUtilityTypeColor(utility2Type);
    ctx.lineWidth = diameter2 * depthScale || 2;
    ctx.stroke();

    // Draw conflict indicator
    let conflictColor = '#fbbf24'; // Amber for low
    if (conflict.severity === 'high') {
      conflictColor = '#ef4444'; // Red for high
    } else if (conflict.severity === 'medium') {
      conflictColor = '#f97316'; // Orange for medium
    }

    ctx.beginPath();
    ctx.moveTo(x, y1);
    ctx.lineTo(x, y2);
    ctx.strokeStyle = conflictColor;
    ctx.lineWidth = 1;
    ctx.setLineDash([3, 3]);
    ctx.stroke();
    ctx.setLineDash([]);

    // Label conflict with distance and show required clearance
    ctx.font = '10px Arial';
    ctx.fillStyle = '#000000';
    ctx.textAlign = 'center';

    // Format measurements for display
    const distanceInches = (conflict.distance * 39.37).toFixed(1);
    const requiredInches = conflict.requiredClearance;

    // Show actual vs required distance
    ctx.fillText(`${distanceInches}" / ${requiredInches}" req.`, x, (y1 + y2) / 2 - 5);
  });
}
