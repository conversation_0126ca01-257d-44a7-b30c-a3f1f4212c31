import { Feature } from 'ol';
import { Geometry, Point, LineString, Polygon, Circle } from 'ol/geom.js';
import type { Coordinate } from 'ol/coordinate.js';
import { getDistance } from 'ol/sphere.js';
import VectorSource from 'ol/source/Vector.js';
import { SnapManager, SnapType } from './snap-manager';
import type { SnapPoint, SnapResult } from './snap-manager';

export enum AdvancedSnapType {
  // Basic snaps (inherited from SnapManager)
  ENDPOINT = 'endpoint',
  MIDPOINT = 'midpoint',
  CENTER = 'center',
  INTERSECTION = 'intersection',
  GRID = 'grid',
  
  // Advanced snaps
  QUADRANT = 'quadrant',
  PERPENDICULAR = 'perpendicular',
  TANGENT = 'tangent',
  EXTENSION = 'extension',
  PARALLEL = 'parallel',
  NEAREST = 'nearest',
  NODE = 'node',
  INSERT = 'insert',
  APPARENT_INTERSECTION = 'apparent_intersection',
  
  // Specialized utility snaps
  UTILITY_CONNECTION = 'utility_connection',
  PIPE_END = 'pipe_end',
  MANHOLE_CENTER = 'manhole_center',
  VALVE_CENTER = 'valve_center',
  POLE_BASE = 'pole_base',
  STRUCTURE_CORNER = 'structure_corner',
  
  // Precision snaps
  COORDINATE_POINT = 'coordinate_point',
  BEARING_INTERSECTION = 'bearing_intersection',
  DISTANCE_FROM_POINT = 'distance_from_point'
}

export interface AdvancedSnapSettings {
  // Basic settings
  enabled: boolean;
  tolerance: number;
  pixelTolerance: number;
  showIndicators: boolean;
  
  // Advanced settings
  enabledTypes: Set<AdvancedSnapType>;
  priority: Record<AdvancedSnapType, number>;
  
  // Snap filtering
  layerFilters: string[];
  geometryTypeFilters: string[];
  attributeFilters: Record<string, any>;
  
  // Visual settings
  snapIndicatorSize: number;
  snapIndicatorColors: Record<AdvancedSnapType, string>;
  
  // Performance settings
  maxCandidates: number;
  useQuadtree: boolean;
  cachingEnabled: boolean;
  
  // Utility-specific settings
  utilitySnapRadius: number;
  connectionTolerance: number;
  structureSnapPriority: boolean;
  
  // Extension and parallel settings
  extensionDistance: number;
  parallelTolerance: number;
  tangentTolerance: number;
}

export interface AdvancedSnapPoint extends SnapPoint {
  snapType: AdvancedSnapType;
  confidence: number; // 0-1 confidence score
  layerId?: string;
  featureId?: string | number;
  properties?: Record<string, any>;
  visualHint?: {
    color: string;
    size: number;
    symbol: string;
  };
}

export interface SnapCandidate {
  point: AdvancedSnapPoint;
  score: number; // Combined priority + distance + confidence score
}

export class AdvancedSnapping extends SnapManager {
  private advancedSettings: AdvancedSnapSettings;
  private advancedSnapCache = new Map<string, AdvancedSnapPoint[]>();
  private quadtreeIndex?: any; // Would use a proper quadtree implementation
  private utilityFeatures = new Map<string, Feature[]>();
  
  constructor() {
    super();
    this.advancedSettings = this.getDefaultAdvancedSettings();
  }

  private getDefaultAdvancedSettings(): AdvancedSnapSettings {
    return {
      enabled: true,
      tolerance: 10,
      pixelTolerance: 15,
      showIndicators: true,
      
      enabledTypes: new Set([
        AdvancedSnapType.ENDPOINT,
        AdvancedSnapType.MIDPOINT,
        AdvancedSnapType.CENTER,
        AdvancedSnapType.INTERSECTION,
        AdvancedSnapType.PERPENDICULAR,
        AdvancedSnapType.GRID,
        AdvancedSnapType.NEAREST
      ]),
      
      priority: {
        [AdvancedSnapType.ENDPOINT]: 100,
        [AdvancedSnapType.INTERSECTION]: 95,
        [AdvancedSnapType.UTILITY_CONNECTION]: 90,
        [AdvancedSnapType.PIPE_END]: 88,
        [AdvancedSnapType.MANHOLE_CENTER]: 85,
        [AdvancedSnapType.VALVE_CENTER]: 85,
        [AdvancedSnapType.POLE_BASE]: 80,
        [AdvancedSnapType.MIDPOINT]: 75,
        [AdvancedSnapType.CENTER]: 70,
        [AdvancedSnapType.QUADRANT]: 65,
        [AdvancedSnapType.PERPENDICULAR]: 60,
        [AdvancedSnapType.TANGENT]: 55,
        [AdvancedSnapType.STRUCTURE_CORNER]: 50,
        [AdvancedSnapType.GRID]: 45,
        [AdvancedSnapType.NEAREST]: 40,
        [AdvancedSnapType.EXTENSION]: 35,
        [AdvancedSnapType.PARALLEL]: 30,
        [AdvancedSnapType.NODE]: 25,
        [AdvancedSnapType.INSERT]: 20,
        [AdvancedSnapType.APPARENT_INTERSECTION]: 15,
        [AdvancedSnapType.COORDINATE_POINT]: 10,
        [AdvancedSnapType.BEARING_INTERSECTION]: 10,
        [AdvancedSnapType.DISTANCE_FROM_POINT]: 5
      },
      
      layerFilters: [],
      geometryTypeFilters: [],
      attributeFilters: {},
      
      snapIndicatorSize: 8,
      snapIndicatorColors: {
        [AdvancedSnapType.ENDPOINT]: '#ff0000',
        [AdvancedSnapType.MIDPOINT]: '#00ff00',
        [AdvancedSnapType.CENTER]: '#0000ff',
        [AdvancedSnapType.INTERSECTION]: '#ff00ff',
        [AdvancedSnapType.PERPENDICULAR]: '#ffff00',
        [AdvancedSnapType.TANGENT]: '#00ffff',
        [AdvancedSnapType.QUADRANT]: '#ff8000',
        [AdvancedSnapType.GRID]: '#888888',
        [AdvancedSnapType.NEAREST]: '#008000',
        [AdvancedSnapType.EXTENSION]: '#800080',
        [AdvancedSnapType.PARALLEL]: '#008080',
        [AdvancedSnapType.UTILITY_CONNECTION]: '#ff4444',
        [AdvancedSnapType.PIPE_END]: '#4444ff',
        [AdvancedSnapType.MANHOLE_CENTER]: '#44ff44',
        [AdvancedSnapType.VALVE_CENTER]: '#ffaa44',
        [AdvancedSnapType.POLE_BASE]: '#aa44ff',
        [AdvancedSnapType.STRUCTURE_CORNER]: '#44aaff',
        [AdvancedSnapType.NODE]: '#aaaaaa',
        [AdvancedSnapType.INSERT]: '#666666',
        [AdvancedSnapType.APPARENT_INTERSECTION]: '#ff6666',
        [AdvancedSnapType.COORDINATE_POINT]: '#666600',
        [AdvancedSnapType.BEARING_INTERSECTION]: '#006666',
        [AdvancedSnapType.DISTANCE_FROM_POINT]: '#660066'
      },
      
      maxCandidates: 50,
      useQuadtree: true,
      cachingEnabled: true,
      
      utilitySnapRadius: 25,
      connectionTolerance: 5,
      structureSnapPriority: true,
      
      extensionDistance: 100,
      parallelTolerance: 2,
      tangentTolerance: 1
    };
  }

  setAdvancedSettings(settings: Partial<AdvancedSnapSettings>): void {
    this.advancedSettings = { ...this.advancedSettings, ...settings };
    this.clearAdvancedCache();
  }

  getAdvancedSettings(): AdvancedSnapSettings {
    return { ...this.advancedSettings };
  }

  // Override the main snap method to use advanced snapping
  snap(coordinate: Coordinate, map: any, excludeFeature?: Feature): SnapResult {
    if (!this.advancedSettings.enabled) {
      return super.snap(coordinate, map, excludeFeature);
    }

    const candidates = this.findAdvancedSnapCandidates(coordinate, map, excludeFeature);
    const bestSnap = this.selectBestAdvancedSnap(candidates, coordinate);

    if (bestSnap) {
      return {
        snapped: true,
        snapPoint: bestSnap,
        originalCoordinate: coordinate,
        snappedCoordinate: bestSnap.coordinate,
        snapIndicator: {
          type: bestSnap.snapType as any,
          coordinate: bestSnap.coordinate,
          angle: bestSnap.metadata?.angle
        }
      };
    }

    return {
      snapped: false,
      originalCoordinate: coordinate,
      snappedCoordinate: coordinate
    };
  }

  private findAdvancedSnapCandidates(
    coordinate: Coordinate,
    map: any,
    excludeFeature?: Feature
  ): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];

    // Basic snap candidates (from parent class)
    const basicCandidates = this.findBasicSnapCandidates(coordinate, map, excludeFeature);
    candidates.push(...basicCandidates);

    // Advanced geometric snaps
    const geometricCandidates = this.findGeometricSnapCandidates(coordinate, map, excludeFeature);
    candidates.push(...geometricCandidates);

    // Utility-specific snaps
    const utilityCandidates = this.findUtilitySnapCandidates(coordinate, map, excludeFeature);
    candidates.push(...utilityCandidates);

    // Extension and parallel snaps
    const extensionCandidates = this.findExtensionSnapCandidates(coordinate, map, excludeFeature);
    candidates.push(...extensionCandidates);

    // Apply filters
    const filteredCandidates = this.applyCandidateFilters(candidates);

    // Limit candidates for performance
    return filteredCandidates
      .sort((a: any, b: any) => b.score - a.score)
      .slice(0, this.advancedSettings.maxCandidates);
  }

  private findBasicSnapCandidates(
    coordinate: Coordinate,
    map: any,
    excludeFeature?: Feature
  ): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Use parent class functionality but convert to advanced format
    const basicResult = super.snap(coordinate, map, excludeFeature);
    if (basicResult.snapped && basicResult.snapPoint) {
      const snapType = this.convertSnapType(basicResult.snapPoint.type);
      const advancedPoint: AdvancedSnapPoint = {
        ...basicResult.snapPoint,
        snapType,
        confidence: 1.0,
        visualHint: {
          color: this.advancedSettings.snapIndicatorColors[snapType] || '#ff0000',
          size: this.advancedSettings.snapIndicatorSize,
          symbol: this.getSnapSymbol(snapType)
        }
      };

      candidates.push({
        point: advancedPoint,
        score: this.calculateSnapScore(advancedPoint, coordinate)
      });
    }

    return candidates;
  }

  private findGeometricSnapCandidates(
    coordinate: Coordinate,
    map: any,
    excludeFeature?: Feature
  ): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];

    // Quadrant snaps for circles
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.QUADRANT)) {
      const quadrantSnaps = this.findQuadrantSnaps(coordinate, excludeFeature);
      candidates.push(...quadrantSnaps);
    }

    // Tangent snaps
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.TANGENT)) {
      const tangentSnaps = this.findTangentSnaps(coordinate, excludeFeature);
      candidates.push(...tangentSnaps);
    }

    // Apparent intersection snaps
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.APPARENT_INTERSECTION)) {
      const apparentIntersections = this.findApparentIntersections(coordinate, excludeFeature);
      candidates.push(...apparentIntersections);
    }

    return candidates;
  }

  private findUtilitySnapCandidates(
    coordinate: Coordinate,
    map: any,
    excludeFeature?: Feature
  ): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];

    // Utility connection points
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.UTILITY_CONNECTION)) {
      const connectionSnaps = this.findUtilityConnections(coordinate, excludeFeature);
      candidates.push(...connectionSnaps);
    }

    // Pipe endpoints
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.PIPE_END)) {
      const pipeEndSnaps = this.findPipeEndpoints(coordinate, excludeFeature);
      candidates.push(...pipeEndSnaps);
    }

    // Manhole centers
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.MANHOLE_CENTER)) {
      const manholeSnaps = this.findManholeCenters(coordinate, excludeFeature);
      candidates.push(...manholeSnaps);
    }

    // Valve centers
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.VALVE_CENTER)) {
      const valveSnaps = this.findValveCenters(coordinate, excludeFeature);
      candidates.push(...valveSnaps);
    }

    // Pole bases
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.POLE_BASE)) {
      const poleSnaps = this.findPoleBases(coordinate, excludeFeature);
      candidates.push(...poleSnaps);
    }

    return candidates;
  }

  private findExtensionSnapCandidates(
    coordinate: Coordinate,
    map: any,
    excludeFeature?: Feature
  ): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];

    // Extension snaps
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.EXTENSION)) {
      const extensionSnaps = this.findExtensionSnaps(coordinate, excludeFeature);
      candidates.push(...extensionSnaps);
    }

    // Parallel snaps
    if (this.advancedSettings.enabledTypes.has(AdvancedSnapType.PARALLEL)) {
      const parallelSnaps = this.findParallelSnaps(coordinate, excludeFeature);
      candidates.push(...parallelSnaps);
    }

    return candidates;
  }

  private findQuadrantSnaps(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    for (const source of this.getSources()) {
      const features = source.getFeatures();
      
      for (const feature of features) {
        if (excludeFeature && feature === excludeFeature) continue;
        
        const geometry = feature.getGeometry();
        if (!geometry || geometry.getType() !== 'Circle') continue;
        
        const circle = geometry as Circle;
        const center = circle.getCenter();
        const radius = circle.getRadius();
        
        // Check if coordinate is close to circle
        const distanceToCenter = getDistance(coordinate, center);
        if (Math.abs(distanceToCenter - radius) > this.advancedSettings.tolerance) continue;
        
        // Calculate quadrant points
        const quadrantPoints: Coordinate[] = [
          [(center[0] ?? 0) + radius, center[1] ?? 0], // East
          [center[0] ?? 0, (center[1] ?? 0) + radius], // North
          [(center[0] ?? 0) - radius, center[1] ?? 0], // West
          [center[0] ?? 0, (center[1] ?? 0) - radius]  // South
        ];
        
        for (const quadPoint of quadrantPoints) {
          const distance = getDistance(coordinate, quadPoint);
          if (distance <= this.advancedSettings.tolerance) {
            const snapPoint: AdvancedSnapPoint = {
              coordinate: quadPoint,
              type: SnapType.CENTER, // Use existing type for compatibility
              snapType: AdvancedSnapType.QUADRANT,
              feature,
              distance,
              priority: this.advancedSettings.priority[AdvancedSnapType.QUADRANT],
              confidence: Math.max(0, 1 - distance / this.advancedSettings.tolerance),
              visualHint: {
                color: this.advancedSettings.snapIndicatorColors[AdvancedSnapType.QUADRANT],
                size: this.advancedSettings.snapIndicatorSize,
                symbol: 'quadrant'
              }
            };
            
            candidates.push({
              point: snapPoint,
              score: this.calculateSnapScore(snapPoint, coordinate)
            });
          }
        }
      }
    }
    
    return candidates;
  }

  private findTangentSnaps(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for tangent snaps
    // This would involve complex geometric calculations for finding tangent points
    // to circles from the coordinate position
    
    return candidates;
  }

  private findApparentIntersections(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for apparent intersections
    // These are intersections that would occur if lines were extended
    
    return candidates;
  }

  private findUtilityConnections(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Look for utility connection points based on feature attributes
    for (const [layerId, features] of this.utilityFeatures) {
      for (const feature of features) {
        if (excludeFeature && feature === excludeFeature) continue;
        
        const properties = feature.getProperties();
        
        // Check if this is a connection feature
        if (properties.type === 'connection' || properties.utility_type === 'connection') {
          const geometry = feature.getGeometry();
          if (!geometry) continue;
          
          if (geometry.getType() === 'Point') {
            const point = geometry as Point;
            const pointCoord = point.getCoordinates();
            const distance = getDistance(coordinate, pointCoord);
            
            if (distance <= this.advancedSettings.utilitySnapRadius) {
              const snapPoint: AdvancedSnapPoint = {
                coordinate: pointCoord,
                type: SnapType.ENDPOINT,
                snapType: AdvancedSnapType.UTILITY_CONNECTION,
                feature,
                distance,
                priority: this.advancedSettings.priority[AdvancedSnapType.UTILITY_CONNECTION],
                confidence: Math.max(0, 1 - distance / this.advancedSettings.utilitySnapRadius),
                layerId,
                featureId: feature.getId(),
                properties,
                visualHint: {
                  color: this.advancedSettings.snapIndicatorColors[AdvancedSnapType.UTILITY_CONNECTION],
                  size: this.advancedSettings.snapIndicatorSize,
                  symbol: 'connection'
                }
              };
              
              candidates.push({
                point: snapPoint,
                score: this.calculateSnapScore(snapPoint, coordinate)
              });
            }
          }
        }
      }
    }
    
    return candidates;
  }

  private findPipeEndpoints(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for pipe endpoint snapping
    // Similar to utility connections but specific to pipe features
    
    return candidates;
  }

  private findManholeCenters(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for manhole center snapping
    
    return candidates;
  }

  private findValveCenters(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for valve center snapping
    
    return candidates;
  }

  private findPoleBases(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for pole base snapping
    
    return candidates;
  }

  private findExtensionSnaps(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for line extension snapping
    
    return candidates;
  }

  private findParallelSnaps(coordinate: Coordinate, excludeFeature?: Feature): SnapCandidate[] {
    const candidates: SnapCandidate[] = [];
    
    // Implementation for parallel line snapping
    
    return candidates;
  }

  private applyCandidateFilters(candidates: SnapCandidate[]): SnapCandidate[] {
    return candidates.filter(candidate => {
      // Layer filters
      if (this.advancedSettings.layerFilters.length > 0) {
        if (!candidate.point.layerId || 
            !this.advancedSettings.layerFilters.includes(candidate.point.layerId)) {
          return false;
        }
      }
      
      // Geometry type filters
      if (this.advancedSettings.geometryTypeFilters.length > 0) {
        const geometry = candidate.point.feature?.getGeometry();
        if (!geometry || 
            !this.advancedSettings.geometryTypeFilters.includes(geometry.getType())) {
          return false;
        }
      }
      
      // Attribute filters
      for (const [key, value] of Object.entries(this.advancedSettings.attributeFilters)) {
        const featureValue = candidate.point.properties?.[key];
        if (featureValue !== value) {
          return false;
        }
      }
      
      return true;
    });
  }

  private selectBestAdvancedSnap(candidates: SnapCandidate[], coordinate: Coordinate): AdvancedSnapPoint | null {
    if (candidates.length === 0) return null;
    
    // Sort by score (highest first)
    candidates.sort((a: any, b: any) => b.score - a.score);
    
    // Apply additional selection logic
    let bestCandidate = candidates[0];
    if (!bestCandidate) {
      return null;
    }
    
    // Prefer structural/utility snaps if enabled
    if (this.advancedSettings.structureSnapPriority) {
      const structureCandidate = candidates.find(c => 
        [
          AdvancedSnapType.UTILITY_CONNECTION,
          AdvancedSnapType.PIPE_END,
          AdvancedSnapType.MANHOLE_CENTER,
          AdvancedSnapType.VALVE_CENTER,
          AdvancedSnapType.POLE_BASE
        ].includes(c.point.snapType)
      );
      
      if (structureCandidate && structureCandidate.score >= bestCandidate.score * 0.8) {
        bestCandidate = structureCandidate;
      }
    }
    
    return bestCandidate?.point || null;
  }

  private calculateSnapScore(snapPoint: AdvancedSnapPoint, coordinate: Coordinate): number {
    const distanceScore = Math.max(0, 1 - snapPoint.distance / this.advancedSettings.tolerance);
    const priorityScore = snapPoint.priority / 100;
    const confidenceScore = snapPoint.confidence;
    
    return (distanceScore * 0.4) + (priorityScore * 0.4) + (confidenceScore * 0.2);
  }

  private getSnapSymbol(snapType: AdvancedSnapType): string {
    const symbols: Record<AdvancedSnapType, string> = {
      [AdvancedSnapType.ENDPOINT]: 'square',
      [AdvancedSnapType.MIDPOINT]: 'triangle',
      [AdvancedSnapType.CENTER]: 'circle',
      [AdvancedSnapType.INTERSECTION]: 'x',
      [AdvancedSnapType.GRID]: 'plus',
      [AdvancedSnapType.QUADRANT]: 'diamond',
      [AdvancedSnapType.PERPENDICULAR]: 'perpendicular',
      [AdvancedSnapType.TANGENT]: 'tangent',
      [AdvancedSnapType.EXTENSION]: 'arrow',
      [AdvancedSnapType.PARALLEL]: 'parallel',
      [AdvancedSnapType.NEAREST]: 'dot',
      [AdvancedSnapType.NODE]: 'node',
      [AdvancedSnapType.INSERT]: 'insert',
      [AdvancedSnapType.APPARENT_INTERSECTION]: 'x-dashed',
      [AdvancedSnapType.UTILITY_CONNECTION]: 'connection',
      [AdvancedSnapType.PIPE_END]: 'pipe',
      [AdvancedSnapType.MANHOLE_CENTER]: 'manhole',
      [AdvancedSnapType.VALVE_CENTER]: 'valve',
      [AdvancedSnapType.POLE_BASE]: 'pole',
      [AdvancedSnapType.STRUCTURE_CORNER]: 'corner',
      [AdvancedSnapType.COORDINATE_POINT]: 'coordinate',
      [AdvancedSnapType.BEARING_INTERSECTION]: 'bearing',
      [AdvancedSnapType.DISTANCE_FROM_POINT]: 'distance'
    };
    
    return symbols[snapType] || 'default';
  }

  // Utility layer management
  addUtilityLayer(layerId: string, features: Feature[]): void {
    this.utilityFeatures.set(layerId, features);
    this.clearAdvancedCache();
  }

  removeUtilityLayer(layerId: string): void {
    this.utilityFeatures.delete(layerId);
    this.clearAdvancedCache();
  }

  updateUtilityFeatures(layerId: string, features: Feature[]): void {
    this.utilityFeatures.set(layerId, features);
    this.clearAdvancedCache();
  }

  private clearAdvancedCache(): void {
    this.advancedSnapCache.clear();
  }

  // Public API methods
  enableSnapType(snapType: AdvancedSnapType): void {
    this.advancedSettings.enabledTypes.add(snapType);
  }

  disableSnapType(snapType: AdvancedSnapType): void {
    this.advancedSettings.enabledTypes.delete(snapType);
  }

  isSnapTypeEnabled(snapType: AdvancedSnapType | SnapType): boolean {
    if (typeof snapType === 'string') {
      // Handle both AdvancedSnapType and SnapType
      if (Object.values(AdvancedSnapType).includes(snapType as AdvancedSnapType)) {
        return this.advancedSettings.enabledTypes.has(snapType as AdvancedSnapType);
      } else {
        // Convert SnapType to AdvancedSnapType and check
        const advancedType = this.convertSnapType(snapType as SnapType);
        return this.advancedSettings.enabledTypes.has(advancedType);
      }
    }
    return false;
  }

  setSnapPriority(snapType: AdvancedSnapType, priority: number): void {
    this.advancedSettings.priority[snapType] = priority;
  }

  getSnapPriority(snapType: AdvancedSnapType): number {
    return this.advancedSettings.priority[snapType] || 0;
  }

  getAdvancedSnapInfo(): {
    enabled: boolean;
    enabledTypes: AdvancedSnapType[];
    utilityLayers: string[];
    cacheSize: number;
  } {
    return {
      enabled: this.advancedSettings.enabled,
      enabledTypes: Array.from(this.advancedSettings.enabledTypes),
      utilityLayers: Array.from(this.utilityFeatures.keys()),
      cacheSize: this.advancedSnapCache.size
    };
  }

  // Helper methods
  private convertSnapType(snapType: SnapType): AdvancedSnapType {
    // Map basic SnapType to AdvancedSnapType
    switch (snapType) {
      case 'endpoint':
        return AdvancedSnapType.ENDPOINT;
      case 'midpoint':
        return AdvancedSnapType.MIDPOINT;
      case 'center':
        return AdvancedSnapType.CENTER;
      case 'intersection':
        return AdvancedSnapType.INTERSECTION;
      case 'grid':
        return AdvancedSnapType.GRID;
      case 'perpendicular':
        return AdvancedSnapType.PERPENDICULAR;
      case 'tangent':
        return AdvancedSnapType.TANGENT;
      case 'extension':
        return AdvancedSnapType.EXTENSION;
      case 'parallel':
        return AdvancedSnapType.PARALLEL;
      case 'nearest':
        return AdvancedSnapType.NEAREST;
      default:
        return AdvancedSnapType.NEAREST; // fallback
    }
  }

  protected getSources(): VectorSource[] {
    // Access parent's private sources through reflection or provide a protected method
    // For now, use a workaround - this should be made protected in the parent class
    return (this as any).sources || [];
  }
}

export default AdvancedSnapping;