import proj4 from 'proj4';
import { logger } from '~/lib/logger';

export interface CoordinateSystem {
  id: string;
  name: string;
  epsgCode: string;
  proj4String: string;
  units: 'meters' | 'feet' | 'degrees';
  displayFormat: 'decimal' | 'dms' | 'engineering';
  precision: number;
  zone?: string;
  authority: string;
  extent?: [number, number, number, number]; // [minX, minY, maxX, maxY]
  description?: string;
}

export interface CoordinateValue {
  x: number;
  y: number;
  z?: number;
  system: string;
  accuracy?: number;
  metadata?: {
    elevation?: number;
    sourceSystem?: string;
    transformationMethod?: string;
    timestamp?: Date;
  };
}

export interface TransformationResult {
  success: boolean;
  coordinate?: CoordinateValue;
  error?: string;
  accuracy?: number;
  method?: string;
}

export class CoordinateSystemManager {
  private systems: Map<string, CoordinateSystem> = new Map();
  private transformCache = new Map<string, CoordinateValue>();
  private registeredProjections = new Set<string>();

  constructor() {
    this.initializeStandardSystems();
    this.registerProjections();
  }

  private initializeStandardSystems(): void {
    // WGS84 Geographic
    this.addSystem({
      id: 'wgs84',
      name: 'WGS84 Geographic',
      epsgCode: 'EPSG:4326',
      proj4String: '+proj=longlat +datum=WGS84 +no_defs',
      units: 'degrees',
      displayFormat: 'dms',
      precision: 8,
      authority: 'EPSG',
      extent: [-180, -90, 180, 90],
      description: 'World Geodetic System 1984'
    });

    // Web Mercator
    this.addSystem({
      id: 'web-mercator',
      name: 'Web Mercator',
      epsgCode: 'EPSG:3857',
      proj4String: '+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs',
      units: 'meters',
      displayFormat: 'decimal',
      precision: 2,
      authority: 'EPSG',
      description: 'Web Mercator projection'
    });

    // Indiana State Plane Coordinate Systems
    this.addSystem({
      id: 'in-east',
      name: 'Indiana State Plane East',
      epsgCode: 'EPSG:2965',
      proj4String: '+proj=tmerc +lat_0=37.5 +lon_0=-85.66666666666667 +k=0.999966667 +x_0=100000 +y_0=250000 +ellps=GRS80 +datum=NAD83 +units=m +no_defs',
      units: 'meters',
      displayFormat: 'engineering',
      precision: 3,
      zone: 'East Zone',
      authority: 'EPSG',
      extent: [-86.8, 37.5, -84.7, 41.8],
      description: 'Indiana State Plane Coordinate System - East Zone (NAD83)'
    });

    this.addSystem({
      id: 'in-west',
      name: 'Indiana State Plane West',
      epsgCode: 'EPSG:2966',
      proj4String: '+proj=tmerc +lat_0=37.5 +lon_0=-87.08333333333333 +k=0.999966667 +x_0=900000 +y_0=250000 +ellps=GRS80 +datum=NAD83 +units=m +no_defs',
      units: 'meters',
      displayFormat: 'engineering',
      precision: 3,
      zone: 'West Zone',
      authority: 'EPSG',
      extent: [-88.2, 37.5, -86.8, 41.8],
      description: 'Indiana State Plane Coordinate System - West Zone (NAD83)'
    });

    // Indiana State Plane (US Survey Feet)
    this.addSystem({
      id: 'in-east-ft',
      name: 'Indiana State Plane East (US Survey Feet)',
      epsgCode: 'EPSG:1301',
      proj4String: '+proj=tmerc +lat_0=37.5 +lon_0=-85.66666666666667 +k=0.999966667 +x_0=328083.3333 +y_0=820208.3333 +ellps=GRS80 +datum=NAD83 +units=us-ft +no_defs',
      units: 'feet',
      displayFormat: 'engineering',
      precision: 2,
      zone: 'East Zone',
      authority: 'EPSG',
      extent: [-86.8, 37.5, -84.7, 41.8],
      description: 'Indiana State Plane Coordinate System - East Zone (NAD83, US Survey Feet)'
    });

    this.addSystem({
      id: 'in-west-ft',
      name: 'Indiana State Plane West (US Survey Feet)',
      epsgCode: 'EPSG:1302',
      proj4String: '+proj=tmerc +lat_0=37.5 +lon_0=-87.08333333333333 +k=0.999966667 +x_0=2952750 +y_0=820208.3333 +ellps=GRS80 +datum=NAD83 +units=us-ft +no_defs',
      units: 'feet',
      displayFormat: 'engineering',
      precision: 2,
      zone: 'West Zone',
      authority: 'EPSG',
      extent: [-88.2, 37.5, -86.8, 41.8],
      description: 'Indiana State Plane Coordinate System - West Zone (NAD83, US Survey Feet)'
    });

    // UTM Zones for Indiana
    this.addSystem({
      id: 'utm-16n',
      name: 'UTM Zone 16N',
      epsgCode: 'EPSG:32616',
      proj4String: '+proj=utm +zone=16 +datum=WGS84 +units=m +no_defs',
      units: 'meters',
      displayFormat: 'engineering',
      precision: 2,
      zone: 'Zone 16N',
      authority: 'EPSG',
      extent: [-90, 0, -84, 84],
      description: 'UTM Zone 16 North (WGS84)'
    });

    this.addSystem({
      id: 'utm-17n',
      name: 'UTM Zone 17N',
      epsgCode: 'EPSG:32617',
      proj4String: '+proj=utm +zone=17 +datum=WGS84 +units=m +no_defs',
      units: 'meters',
      displayFormat: 'engineering',
      precision: 2,
      zone: 'Zone 17N',
      authority: 'EPSG',
      extent: [-84, 0, -78, 84],
      description: 'UTM Zone 17 North (WGS84)'
    });

    logger.info('Initialized coordinate systems', { count: this.systems.size });
  }

  private registerProjections(): void {
    for (const system of this.systems.values()) {
      if (!this.registeredProjections.has(system.epsgCode)) {
        try {
          proj4.defs(system.epsgCode, system.proj4String);
          this.registeredProjections.add(system.epsgCode);
          logger.debug('Registered projection', { epsgCode: system.epsgCode });
        } catch (error) {
          logger.error('Failed to register projection', { epsgCode: system.epsgCode, error });
        }
      }
    }
  }

  addSystem(system: CoordinateSystem): void {
    this.systems.set(system.id, system);
    
    // Register with proj4 if not already registered
    if (!this.registeredProjections.has(system.epsgCode)) {
      try {
        proj4.defs(system.epsgCode, system.proj4String);
        this.registeredProjections.add(system.epsgCode);
        logger.debug('Added and registered coordinate system', { name: system.name });
      } catch (error) {
        logger.error('Failed to register projection', { epsgCode: system.epsgCode, error });
      }
    }
  }

  getSystem(id: string): CoordinateSystem | undefined {
    return this.systems.get(id);
  }

  getAllSystems(): CoordinateSystem[] {
    return Array.from(this.systems.values());
  }

  getSystemsByRegion(region: 'indiana' | 'utm' | 'global'): CoordinateSystem[] {
    const systems = Array.from(this.systems.values());
    
    switch (region) {
      case 'indiana':
        return systems.filter(s => s.id.startsWith('in-'));
      case 'utm':
        return systems.filter(s => s.id.startsWith('utm-'));
      case 'global':
        return systems.filter(s => s.id === 'wgs84' || s.id === 'web-mercator');
      default:
        return systems;
    }
  }

  transform(
    coordinate: CoordinateValue,
    targetSystemId: string
  ): TransformationResult {
    const sourceSystem = this.systems.get(coordinate.system);
    const targetSystem = this.systems.get(targetSystemId);

    if (!sourceSystem || !targetSystem) {
      return {
        success: false,
        error: `Unknown coordinate system: ${!sourceSystem ? coordinate.system : targetSystemId}`
      };
    }

    if (sourceSystem.id === targetSystem.id) {
      return {
        success: true,
        coordinate: { ...coordinate, system: targetSystemId }
      };
    }

    // Check cache
    const cacheKey = `${coordinate.x},${coordinate.y},${coordinate.z || 0}:${sourceSystem.epsgCode}->${targetSystem.epsgCode}`;
    const cached = this.transformCache.get(cacheKey);
    if (cached) {
      return {
        success: true,
        coordinate: cached,
        method: 'cached'
      };
    }

    try {
      const sourceProj = proj4(sourceSystem.epsgCode);
      const targetProj = proj4(targetSystem.epsgCode);
      
      const inputCoord = coordinate.z !== undefined 
        ? [coordinate.x, coordinate.y, coordinate.z]
        : [coordinate.x, coordinate.y];

      const transformedCoord = proj4(sourceSystem.epsgCode, targetSystem.epsgCode, inputCoord);
      
      const result: CoordinateValue = {
        x: (transformedCoord as number[])[0] ?? 0,
        y: (transformedCoord as number[])[1] ?? 0,
        z: (transformedCoord as number[]).length > 2 ? (transformedCoord as number[])[2] : coordinate.z,
        system: targetSystemId,
        accuracy: this.calculateTransformationAccuracy(sourceSystem, targetSystem),
        metadata: {
          ...coordinate.metadata,
          sourceSystem: coordinate.system,
          transformationMethod: 'proj4',
          timestamp: new Date()
        }
      };

      // Cache the result
      this.transformCache.set(cacheKey, result);

      return {
        success: true,
        coordinate: result,
        accuracy: result.accuracy,
        method: 'proj4'
      };

    } catch (error) {
      logger.error('Coordinate transformation failed', { error });
      return {
        success: false,
        error: `Transformation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private calculateTransformationAccuracy(
    source: CoordinateSystem,
    target: CoordinateSystem
  ): number {
    // Simple accuracy estimation based on coordinate system types
    if (source.authority === target.authority && source.authority === 'EPSG') {
      if (source.units === target.units) {
        return 0.01; // High accuracy for same datum/unit transformations
      }
      return 0.1; // Good accuracy for unit conversions
    }
    
    if (source.epsgCode.includes('4326') || target.epsgCode.includes('4326')) {
      return 1.0; // Moderate accuracy when involving geographic coordinates
    }
    
    return 2.0; // Conservative accuracy for complex transformations
  }

  formatCoordinate(
    coordinate: CoordinateValue,
    options?: {
      precision?: number;
      format?: 'decimal' | 'dms' | 'engineering';
      units?: boolean;
    }
  ): {
    x: string;
    y: string;
    z?: string;
    display: string;
  } {
    const system = this.systems.get(coordinate.system);
    if (!system) {
      return {
        x: coordinate.x.toString(),
        y: coordinate.y.toString(),
        z: coordinate.z?.toString(),
        display: `${coordinate.x}, ${coordinate.y}`
      };
    }

    const precision = options?.precision ?? system.precision;
    const format = options?.format ?? system.displayFormat;
    const showUnits = options?.units ?? true;

    let xStr: string;
    let yStr: string;
    let display: string;

    switch (format) {
      case 'dms':
        if (system.units === 'degrees') {
          xStr = this.formatDMS(coordinate.x, 'longitude');
          yStr = this.formatDMS(coordinate.y, 'latitude');
          display = `${yStr} ${xStr}`;
        } else {
          xStr = coordinate.x.toFixed(precision);
          yStr = coordinate.y.toFixed(precision);
          display = `${xStr}, ${yStr}`;
        }
        break;
        
      case 'engineering':
        xStr = this.formatEngineering(coordinate.x, precision);
        yStr = this.formatEngineering(coordinate.y, precision);
        display = `N ${yStr}, E ${xStr}`;
        break;
        
      default: // decimal
        xStr = coordinate.x.toFixed(precision);
        yStr = coordinate.y.toFixed(precision);
        display = `${xStr}, ${yStr}`;
        break;
    }

    if (showUnits && system.units !== 'degrees') {
      const unitAbbr = system.units === 'meters' ? 'm' : 'ft';
      display += ` ${unitAbbr}`;
    }

    const result = {
      x: xStr,
      y: yStr,
      display
    };

    if (coordinate.z !== undefined) {
      const zStr = coordinate.z.toFixed(precision);
      result.display += `, Z ${zStr}`;
      return { ...result, z: zStr };
    }

    return result;
  }

  private formatDMS(decimal: number, type: 'latitude' | 'longitude'): string {
    const abs = Math.abs(decimal);
    const degrees = Math.floor(abs);
    const minutes = Math.floor((abs - degrees) * 60);
    const seconds = ((abs - degrees) * 60 - minutes) * 60;
    
    let direction: string;
    if (type === 'latitude') {
      direction = decimal >= 0 ? 'N' : 'S';
    } else {
      direction = decimal >= 0 ? 'E' : 'W';
    }
    
    return `${degrees}°${minutes.toString().padStart(2, '0')}'${seconds.toFixed(2).padStart(5, '0')}"${direction}`;
  }

  private formatEngineering(value: number, precision: number): string {
    // Format large numbers with commas for engineering notation
    return value.toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  parseCoordinate(
    input: string,
    systemId: string,
    options?: {
      defaultZ?: number;
      validateExtent?: boolean;
    }
  ): { success: boolean; coordinate?: CoordinateValue; error?: string } {
    const system = this.systems.get(systemId);
    if (!system) {
      return {
        success: false,
        error: `Unknown coordinate system: ${systemId}`
      };
    }

    try {
      let x: number, y: number, z: number | undefined;

      // Clean up input
      const cleaned = input.trim().replace(/[,\s]+/g, ' ');

      if (system.displayFormat === 'dms' && system.units === 'degrees') {
        // Parse DMS format
        const parsed = this.parseDMS(cleaned);
        if (!parsed) {
          return { success: false, error: 'Invalid DMS format' };
        }
        x = parsed.longitude;
        y = parsed.latitude;
      } else {
        // Parse decimal coordinates
        const parts = cleaned.split(/[\s,]+/);
        
        if (parts.length < 2) {
          return { success: false, error: 'At least X and Y coordinates required' };
        }

        // Handle different coordinate order conventions
        if (system.displayFormat === 'engineering') {
          // Engineering format: typically "N Y, E X" or "Y X"
          const yMatch = parts[0]?.match(/N?\s*([0-9.-]+)/i);
          const xMatch = parts[1]?.match(/E?\s*([0-9.-]+)/i);
          
          y = yMatch ? parseFloat(yMatch[1]!) : parseFloat(parts[0]!);
          x = xMatch ? parseFloat(xMatch[1]!) : parseFloat(parts[1]!);
        } else {
          // Standard format: X Y (longitude latitude)
          x = parseFloat(parts[0]!);
          y = parseFloat(parts[1]!);
        }

        if (parts.length > 2) {
          const zMatch = parts[2]!.match(/Z?\s*([0-9.-]+)/i);
          z = zMatch ? parseFloat(zMatch[1]!) : parseFloat(parts[2]!);
        } else if (options?.defaultZ !== undefined) {
          z = options.defaultZ;
        }

        if (isNaN(x) || isNaN(y)) {
          return { success: false, error: 'Invalid numeric coordinates' };
        }
      }

      // Validate extent if requested
      if (options?.validateExtent && system.extent) {
        const [minX, minY, maxX, maxY] = system.extent;
        if (x < minX || x > maxX || y < minY || y > maxY) {
          return {
            success: false,
            error: `Coordinates outside valid extent for ${system.name}`
          };
        }
      }

      const coordinate: CoordinateValue = {
        x,
        y,
        z,
        system: systemId,
        metadata: {
          timestamp: new Date()
        }
      };

      return { success: true, coordinate };

    } catch (error) {
      return {
        success: false,
        error: `Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private parseDMS(input: string): { latitude: number; longitude: number } | null {
    // Simplified DMS parser - would need more robust implementation
    const dmsRegex = /(\d+)°(\d+)'([\d.]+)"?([NSEW])/gi;
    const matches = [...input.matchAll(dmsRegex)];
    
    if (matches.length !== 2) return null;
    
    const coords: { latitude?: number; longitude?: number } = {};
    
    for (const match of matches) {
      const degrees = parseInt(match[1]!);
      const minutes = parseInt(match[2]!);
      const seconds = parseFloat(match[3]!);
      const direction = match[4]!.toUpperCase();
      
      let decimal = degrees + minutes / 60 + seconds / 3600;
      if (direction === 'S' || direction === 'W') {
        decimal = -decimal;
      }
      
      if (direction === 'N' || direction === 'S') {
        coords.latitude = decimal;
      } else {
        coords.longitude = decimal;
      }
    }
    
    if (coords.latitude !== undefined && coords.longitude !== undefined) {
      return { latitude: coords.latitude, longitude: coords.longitude };
    }
    
    return null;
  }

  clearCache(): void {
    this.transformCache.clear();
    logger.debug('Transformation cache cleared');
  }

  getTransformationInfo(fromSystemId: string, toSystemId: string): {
    supported: boolean;
    accuracy?: number;
    method?: string;
    description?: string;
  } {
    const fromSystem = this.systems.get(fromSystemId);
    const toSystem = this.systems.get(toSystemId);
    
    if (!fromSystem || !toSystem) {
      return { supported: false };
    }
    
    if (fromSystem.id === toSystem.id) {
      return {
        supported: true,
        accuracy: 0,
        method: 'identity',
        description: 'No transformation needed'
      };
    }
    
    return {
      supported: true,
      accuracy: this.calculateTransformationAccuracy(fromSystem, toSystem),
      method: 'proj4',
      description: `Transform from ${fromSystem.name} to ${toSystem.name}`
    };
  }
}

export default CoordinateSystemManager;