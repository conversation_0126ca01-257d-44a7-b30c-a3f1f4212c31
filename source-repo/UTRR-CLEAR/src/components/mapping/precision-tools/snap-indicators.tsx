'use client';

import React from 'react';
import { SnapType, type SnapPoint } from './snap-manager';

export interface SnapIndicatorProps {
  snapPoint: SnapPoint;
  visible: boolean;
  pixelPosition: [number, number];
}

export interface SnapIndicatorsProps {
  snapPoints: SnapPoint[];
  activeSnap?: SnapPoint;
  mapElement: HTMLElement | null;
}

const SNAP_COLORS: Record<SnapType, string> = {
  [SnapType.ENDPOINT]: '#ff6b6b',      // Red
  [SnapType.MIDPOINT]: '#4ecdc4',      // Teal
  [SnapType.INTERSECTION]: '#45b7d1',  // Blue
  [SnapType.PERPENDICULAR]: '#96ceb4', // Green
  [SnapType.CENTER]: '#feca57',        // Yellow
  [SnapType.GRID]: '#9c88ff',          // Purple
  [SnapType.TANGENT]: '#fd79a8',       // Pink
  [SnapType.NEAREST]: '#fdcb6e',       // Orange
  [SnapType.EXTENSION]: '#e17055',     // Orange-red
  [SnapType.PARALLEL]: '#74b9ff'       // Light blue
};

const SNAP_SYMBOLS: Record<SnapType, string> = {
  [SnapType.ENDPOINT]: '●',
  [SnapType.MIDPOINT]: '△',
  [SnapType.INTERSECTION]: '✕',
  [SnapType.PERPENDICULAR]: '⊥',
  [SnapType.CENTER]: '○',
  [SnapType.GRID]: '⊞',
  [SnapType.TANGENT]: '○',
  [SnapType.NEAREST]: '◊',
  [SnapType.EXTENSION]: '→',
  [SnapType.PARALLEL]: '∥'
};

const SnapIndicator: React.FC<SnapIndicatorProps> = ({ 
  snapPoint, 
  visible, 
  pixelPosition 
}) => {
  if (!visible) return null;

  const color = SNAP_COLORS[snapPoint.type];
  const symbol = SNAP_SYMBOLS[snapPoint.type];
  const isActive = snapPoint.priority > 5;

  return (
    <div
      className="absolute pointer-events-none z-50"
      style={{
        left: pixelPosition[0] - 12,
        top: pixelPosition[1] - 12,
        transform: 'translate(-50%, -50%)',
      }}
    >
      {/* Main indicator */}
      <div
        className={`
          w-6 h-6 rounded-full border-2 flex items-center justify-center
          text-xs font-bold transition-all duration-150
          ${isActive ? 'scale-110 shadow-lg' : 'scale-100'}
        `}
        style={{
          backgroundColor: color,
          borderColor: 'white',
          color: 'white',
          textShadow: '0 0 2px rgba(0,0,0,0.8)'
        }}
      >
        {symbol}
      </div>
      
      {/* Snap type label */}
      <div
        className="absolute top-7 left-1/2 transform -translate-x-1/2 
                   bg-gray-900 text-white text-xs px-2 py-1 rounded
                   whitespace-nowrap opacity-90"
        style={{ fontSize: '10px' }}
      >
        {snapPoint.type.toUpperCase()}
      </div>
      
      {/* Distance indicator for debugging */}
      {process.env.NODE_ENV === 'development' && (
        <div
          className="absolute -top-6 left-1/2 transform -translate-x-1/2
                     bg-gray-700 text-white text-xs px-1 rounded opacity-75"
          style={{ fontSize: '9px' }}
        >
          {snapPoint.distance.toFixed(2)}m
        </div>
      )}
    </div>
  );
};

export const SnapIndicators: React.FC<SnapIndicatorsProps> = ({ 
  snapPoints, 
  activeSnap,
  mapElement 
}) => {
  if (!mapElement || snapPoints.length === 0) {
    return null;
  }

  return (
    <div className="absolute inset-0 pointer-events-none z-40">
      {snapPoints.map((snapPoint, index) => {
        const isActive = activeSnap === snapPoint;
        // Convert map coordinates to pixel positions
        // This would need to be connected to the actual map instance
        const pixelPosition: [number, number] = [0, 0]; // Placeholder
        
        return (
          <SnapIndicator
            key={`${snapPoint.type}-${index}`}
            snapPoint={snapPoint}
            visible={true}
            pixelPosition={pixelPosition}
          />
        );
      })}
    </div>
  );
};

export default SnapIndicators;