'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { Switch } from '~/components/ui/switch';
import { Slider } from '~/components/ui/slider';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { SnapType, type SnapSettings } from './snap-manager';

export interface SnapSettingsPanelProps {
  settings: SnapSettings;
  onSettingsChange: (settings: Partial<SnapSettings>) => void;
  snapInfo?: {
    enabled: boolean;
    activeTypes: SnapType[];
    tolerance: number;
    lastSnap?: any;
  };
  onClose?: () => void;
}

const SNAP_TYPE_CONFIG: Record<SnapType, {
  label: string;
  description: string;
  icon: string;
  color: string;
}> = {
  [SnapType.ENDPOINT]: {
    label: 'Endpoint',
    description: 'Snap to line and curve endpoints',
    icon: '●',
    color: 'bg-red-500'
  },
  [SnapType.MIDPOINT]: {
    label: 'Midpoint',
    description: 'Snap to midpoints of segments',
    icon: '△',
    color: 'bg-teal-500'
  },
  [SnapType.INTERSECTION]: {
    label: 'Intersection',
    description: 'Snap to geometry intersections',
    icon: '✕',
    color: 'bg-blue-500'
  },
  [SnapType.PERPENDICULAR]: {
    label: 'Perpendicular',
    description: 'Snap perpendicular to segments',
    icon: '⊥',
    color: 'bg-green-500'
  },
  [SnapType.CENTER]: {
    label: 'Center',
    description: 'Snap to geometry centers',
    icon: '○',
    color: 'bg-yellow-500'
  },
  [SnapType.GRID]: {
    label: 'Grid',
    description: 'Snap to grid points',
    icon: '⊞',
    color: 'bg-purple-500'
  },
  [SnapType.TANGENT]: {
    label: 'Tangent',
    description: 'Snap tangent to curves',
    icon: '○',
    color: 'bg-pink-500'
  },
  [SnapType.NEAREST]: {
    label: 'Nearest',
    description: 'Snap to nearest point on geometry',
    icon: '◊',
    color: 'bg-orange-500'
  },
  [SnapType.EXTENSION]: {
    label: 'Extension',
    description: 'Snap to line extensions',
    icon: '→',
    color: 'bg-red-400'
  },
  [SnapType.PARALLEL]: {
    label: 'Parallel',
    description: 'Snap parallel to existing lines',
    icon: '∥',
    color: 'bg-blue-400'
  }
};

export const SnapSettingsPanel: React.FC<SnapSettingsPanelProps> = ({
  settings,
  onSettingsChange,
  snapInfo,
  onClose
}) => {
  const handleToggleSnapType = (type: SnapType) => {
    const newTypes = new Set(settings.types);
    if (newTypes.has(type)) {
      newTypes.delete(type);
    } else {
      newTypes.add(type);
    }
    onSettingsChange({ types: newTypes });
  };

  const handleToleranceChange = (value: number[]) => {
    onSettingsChange({ tolerance: value[0] });
  };

  const handleGridSizeChange = (value: number[]) => {
    onSettingsChange({ gridSize: value[0] });
  };

  const enableAllSnaps = () => {
    onSettingsChange({ 
      types: new Set(Object.values(SnapType)) 
    });
  };

  const disableAllSnaps = () => {
    onSettingsChange({ 
      types: new Set() 
    });
  };

  const resetToDefaults = () => {
    onSettingsChange({
      enabled: true,
      tolerance: 10,
      types: new Set([
        SnapType.ENDPOINT,
        SnapType.MIDPOINT,
        SnapType.INTERSECTION,
        SnapType.GRID
      ]),
      gridSize: 1.0,
      showIndicators: true,
      magneticSnap: true,
      autoSnap: true
    });
  };

  return (
    <Card className="w-80 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Snap Settings</CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          )}
        </div>
        
        {snapInfo?.lastSnap && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span className="w-3 h-3 rounded-full bg-green-500"></span>
            Last snap: {snapInfo.lastSnap.type}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Master Enable/Disable */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium">Enable Snapping</label>
            <p className="text-xs text-muted-foreground">
              Master control for all snap functionality
            </p>
          </div>
          <Switch
            checked={settings.enabled}
            onCheckedChange={(enabled) => onSettingsChange({ enabled })}
          />
        </div>

        <Separator />

        {/* Quick Actions */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Quick Actions</label>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={enableAllSnaps}
              disabled={!settings.enabled}
            >
              Enable All
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={disableAllSnaps}
              disabled={!settings.enabled}
            >
              Disable All
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={resetToDefaults}
            >
              Reset
            </Button>
          </div>
        </div>

        <Separator />

        {/* Tolerance Setting */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Snap Tolerance</label>
            <Badge variant="secondary">{settings.tolerance}px</Badge>
          </div>
          <Slider
            value={[settings.tolerance]}
            onValueChange={handleToleranceChange}
            min={5}
            max={50}
            step={1}
            disabled={!settings.enabled}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            Distance in pixels to activate snapping
          </p>
        </div>

        <Separator />

        {/* Grid Settings */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Grid Size</label>
            <Badge variant="secondary">{settings.gridSize}m</Badge>
          </div>
          <Slider
            value={[settings.gridSize || 1]}
            onValueChange={handleGridSizeChange}
            min={0.1}
            max={10}
            step={0.1}
            disabled={!settings.enabled || !settings.types.has(SnapType.GRID)}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            Grid spacing in meters
          </p>
        </div>

        <Separator />

        {/* Advanced Options */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Advanced Options</label>
          
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm">Show Indicators</span>
              <p className="text-xs text-muted-foreground">
                Display visual snap indicators
              </p>
            </div>
            <Switch
              checked={settings.showIndicators}
              onCheckedChange={(showIndicators) => onSettingsChange({ showIndicators })}
              disabled={!settings.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm">Magnetic Snap</span>
              <p className="text-xs text-muted-foreground">
                Prefer previous snap points
              </p>
            </div>
            <Switch
              checked={settings.magneticSnap}
              onCheckedChange={(magneticSnap) => onSettingsChange({ magneticSnap })}
              disabled={!settings.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm">Auto Snap</span>
              <p className="text-xs text-muted-foreground">
                Automatically snap without confirmation
              </p>
            </div>
            <Switch
              checked={settings.autoSnap}
              onCheckedChange={(autoSnap) => onSettingsChange({ autoSnap })}
              disabled={!settings.enabled}
            />
          </div>
        </div>

        <Separator />

        {/* Snap Types */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Snap Types</label>
            <Badge variant="outline">
              {settings.types.size} active
            </Badge>
          </div>
          
          <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
            {Object.entries(SNAP_TYPE_CONFIG).map(([type, config]) => (
              <div
                key={type}
                className="flex items-center justify-between p-2 rounded-lg
                         hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded flex items-center justify-center text-white text-sm ${config.color}`}>
                    {config.icon}
                  </div>
                  <div>
                    <div className="text-sm font-medium">{config.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {config.description}
                    </div>
                  </div>
                </div>
                
                <Switch
                  checked={settings.types.has(type as SnapType)}
                  onCheckedChange={() => handleToggleSnapType(type as SnapType)}
                  disabled={!settings.enabled}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Status Information */}
        {snapInfo && (
          <>
            <Separator />
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Enabled:</span>
                  <Badge variant={snapInfo.enabled ? "default" : "secondary"}>
                    {snapInfo.enabled ? "Yes" : "No"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Active Types:</span>
                  <Badge variant="outline">
                    {snapInfo.activeTypes.length}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Tolerance:</span>
                  <Badge variant="outline">
                    {snapInfo.tolerance}px
                  </Badge>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default SnapSettingsPanel;