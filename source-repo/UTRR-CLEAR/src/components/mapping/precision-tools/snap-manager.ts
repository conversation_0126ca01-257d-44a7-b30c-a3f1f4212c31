import { Feature } from 'ol';
import { Geometry, Point, LineString, Polygon } from 'ol/geom.js';
import type { Coordinate } from 'ol/coordinate.js';
import { getDistance } from 'ol/sphere.js';
import VectorSource from 'ol/source/Vector.js';
import { logger } from '~/lib/logger';

export interface SnapPoint {
  coordinate: Coordinate;
  type: SnapType;
  feature?: Feature<Geometry>;
  distance: number;
  priority: number;
  metadata?: {
    segmentIndex?: number;
    parameter?: number;
    angle?: number;
  };
}

export enum SnapType {
  ENDPOINT = 'endpoint',
  MIDPOINT = 'midpoint',
  INTERSECTION = 'intersection',
  PERPENDICULAR = 'perpendicular',
  CENTER = 'center',
  GRID = 'grid',
  TANGENT = 'tangent',
  NEAREST = 'nearest',
  EXTENSION = 'extension',
  PARALLEL = 'parallel'
}

export interface SnapSettings {
  enabled: boolean;
  tolerance: number; // pixels
  pixelTolerance: number;
  types: Set<SnapType>;
  priority: Record<SnapType, number>;
  gridSize?: number;
  showIndicators: boolean;
  magneticSnap: boolean;
  autoSnap: boolean;
  snapToSelf: boolean;
}

export interface SnapResult {
  snapped: boolean;
  snapPoint?: SnapPoint;
  originalCoordinate: Coordinate;
  snappedCoordinate: Coordinate;
  snapIndicator?: {
    type: SnapType;
    coordinate: Coordinate;
    angle?: number;
  };
}

export class SnapManager {
  private logger = logger;
  private settings: SnapSettings;
  private sources: VectorSource[] = [];
  private gridOrigin: Coordinate = [0, 0];
  private snapCache = new Map<string, SnapPoint[]>();
  private lastSnapPoint?: SnapPoint;
  private snapHistory: SnapPoint[] = [];
  
  constructor() {
    this.settings = this.getDefaultSettings();
  }
  
  private getDefaultSettings(): SnapSettings {
    return {
      enabled: true,
      tolerance: 10, // pixels
      pixelTolerance: 15,
      types: new Set([
        SnapType.ENDPOINT,
        SnapType.MIDPOINT,
        SnapType.INTERSECTION,
        SnapType.GRID
      ]),
      priority: {
        [SnapType.ENDPOINT]: 10,
        [SnapType.INTERSECTION]: 9,
        [SnapType.MIDPOINT]: 8,
        [SnapType.PERPENDICULAR]: 7,
        [SnapType.CENTER]: 6,
        [SnapType.TANGENT]: 5,
        [SnapType.GRID]: 4,
        [SnapType.NEAREST]: 3,
        [SnapType.EXTENSION]: 2,
        [SnapType.PARALLEL]: 1
      },
      gridSize: 1.0, // meters
      showIndicators: true,
      magneticSnap: true,
      autoSnap: true,
      snapToSelf: false
    };
  }
  
  setSettings(settings: Partial<SnapSettings>): void {
    this.settings = { ...this.settings, ...settings };
    this.clearCache();
    this.logger.debug('Snap settings updated', { settings: this.settings });
  }
  
  getSettings(): SnapSettings {
    return { ...this.settings };
  }
  
  addSource(source: VectorSource): void {
    if (!this.sources.includes(source)) {
      this.sources.push(source);
      this.clearCache();
      this.logger.debug('Vector source added to snap manager');
    }
  }
  
  removeSource(source: VectorSource): void {
    const index = this.sources.indexOf(source);
    if (index > -1) {
      this.sources.splice(index, 1);
      this.clearCache();
      this.logger.debug('Vector source removed from snap manager');
    }
  }
  
  setGridOrigin(coordinate: Coordinate): void {
    this.gridOrigin = coordinate;
    this.clearCache();
  }
  
  setGridSize(size: number): void {
    this.settings.gridSize = size;
    this.clearCache();
  }
  
  snap(coordinate: Coordinate, map: any, excludeFeature?: Feature): SnapResult {
    if (!this.settings.enabled) {
      return {
        snapped: false,
        originalCoordinate: coordinate,
        snappedCoordinate: coordinate
      };
    }
    
    const snapCandidates = this.findSnapCandidates(coordinate, map, excludeFeature);
    const bestSnap = this.selectBestSnap(snapCandidates, coordinate);
    
    if (bestSnap) {
      this.lastSnapPoint = bestSnap;
      this.addToHistory(bestSnap);
      
      return {
        snapped: true,
        snapPoint: bestSnap,
        originalCoordinate: coordinate,
        snappedCoordinate: bestSnap.coordinate,
        snapIndicator: {
          type: bestSnap.type,
          coordinate: bestSnap.coordinate,
          angle: bestSnap.metadata?.angle
        }
      };
    }
    
    return {
      snapped: false,
      originalCoordinate: coordinate,
      snappedCoordinate: coordinate
    };
  }
  
  private findSnapCandidates(
    coordinate: Coordinate, 
    map: any, 
    excludeFeature?: Feature
  ): SnapPoint[] {
    const candidates: SnapPoint[] = [];
    const pixel = map.getPixelFromCoordinate(coordinate);
    const tolerance = this.settings.tolerance;
    
    // Grid snapping
    if (this.settings.types.has(SnapType.GRID)) {
      const gridSnap = this.findGridSnap(coordinate);
      if (gridSnap) {
        candidates.push(gridSnap);
      }
    }
    
    // Feature-based snapping
    for (const source of this.sources) {
      const features = source.getFeatures();
      
      for (const feature of features) {
        if (excludeFeature && feature === excludeFeature) {
          continue;
        }
        
        const geometry = feature.getGeometry();
        if (!geometry) continue;
        
        const featureSnaps = this.findFeatureSnaps(
          coordinate, 
          feature, 
          geometry, 
          pixel, 
          tolerance, 
          map
        );
        candidates.push(...featureSnaps);
      }
    }
    
    // Intersection snapping between features
    if (this.settings.types.has(SnapType.INTERSECTION)) {
      const intersectionSnaps = this.findIntersectionSnaps(coordinate, map);
      candidates.push(...intersectionSnaps);
    }
    
    return candidates;
  }
  
  private findFeatureSnaps(
    coordinate: Coordinate,
    feature: Feature,
    geometry: Geometry,
    pixel: number[],
    tolerance: number,
    map: any
  ): SnapPoint[] {
    const snaps: SnapPoint[] = [];
    const type = geometry.getType();
    
    switch (type) {
      case 'Point':
        if (this.settings.types.has(SnapType.ENDPOINT)) {
          const pointSnap = this.findPointSnap(coordinate, geometry as Point, feature);
          if (pointSnap) snaps.push(pointSnap);
        }
        break;
        
      case 'LineString':
        snaps.push(...this.findLineStringSnaps(coordinate, geometry as LineString, feature, map));
        break;
        
      case 'Polygon':
        snaps.push(...this.findPolygonSnaps(coordinate, geometry as Polygon, feature, map));
        break;
        
      case 'MultiPoint':
      case 'MultiLineString':
      case 'MultiPolygon':
        // Handle multi-geometries recursively
        const multiGeom = geometry as any;
        const geometries = multiGeom.getGeometries();
        for (const subGeometry of geometries) {
          snaps.push(...this.findFeatureSnaps(coordinate, feature, subGeometry, pixel, tolerance, map));
        }
        break;
    }
    
    return snaps;
  }
  
  private findPointSnap(coordinate: Coordinate, point: Point, feature: Feature): SnapPoint | null {
    const pointCoord = point.getCoordinates();
    const distance = getDistance(coordinate, pointCoord);
    
    if (distance <= this.settings.tolerance) {
      return {
        coordinate: pointCoord,
        type: SnapType.ENDPOINT,
        feature,
        distance,
        priority: this.settings.priority[SnapType.ENDPOINT]
      };
    }
    
    return null;
  }
  
  private findLineStringSnaps(
    coordinate: Coordinate, 
    lineString: LineString, 
    feature: Feature,
    map: any
  ): SnapPoint[] {
    const snaps: SnapPoint[] = [];
    const coordinates = lineString.getCoordinates();
    
    // Endpoint snapping
    if (this.settings.types.has(SnapType.ENDPOINT)) {
      for (let i = 0; i < coordinates.length; i += coordinates.length - 1) {
        const endPoint = coordinates[i];
        if (!endPoint) continue;
        const distance = getDistance(coordinate, endPoint);
        
        if (distance <= this.settings.tolerance) {
          snaps.push({
            coordinate: endPoint,
            type: SnapType.ENDPOINT,
            feature,
            distance,
            priority: this.settings.priority[SnapType.ENDPOINT]
          });
        }
      }
    }
    
    // Midpoint and nearest point snapping
    for (let i = 0; i < coordinates.length - 1; i++) {
      const start = coordinates[i];
      const end = coordinates[i + 1];
      
      if (!start || !end) continue;
      
      // Midpoint snapping
      if (this.settings.types.has(SnapType.MIDPOINT)) {
        const midpoint: Coordinate = [
          ((start[0] ?? 0) + (end[0] ?? 0)) / 2,
          ((start[1] ?? 0) + (end[1] ?? 0)) / 2
        ];
        const distance = getDistance(coordinate, midpoint);
        
        if (distance <= this.settings.tolerance) {
          snaps.push({
            coordinate: midpoint,
            type: SnapType.MIDPOINT,
            feature,
            distance,
            priority: this.settings.priority[SnapType.MIDPOINT],
            metadata: { segmentIndex: i }
          });
        }
      }
      
      // Nearest point on segment
      if (this.settings.types.has(SnapType.NEAREST)) {
        const nearestPoint = this.findNearestPointOnSegment(coordinate, start, end);
        if (nearestPoint) {
          const distance = getDistance(coordinate, nearestPoint.coordinate);
          
          if (distance <= this.settings.tolerance) {
            snaps.push({
              coordinate: nearestPoint.coordinate,
              type: SnapType.NEAREST,
              feature,
              distance,
              priority: this.settings.priority[SnapType.NEAREST],
              metadata: { 
                segmentIndex: i, 
                parameter: nearestPoint.parameter 
              }
            });
          }
        }
      }
      
      // Perpendicular snapping
      if (this.settings.types.has(SnapType.PERPENDICULAR)) {
        const perpPoint = this.findPerpendicularPoint(coordinate, start, end);
        if (perpPoint) {
          const distance = getDistance(coordinate, perpPoint.coordinate);
          
          if (distance <= this.settings.tolerance) {
            snaps.push({
              coordinate: perpPoint.coordinate,
              type: SnapType.PERPENDICULAR,
              feature,
              distance,
              priority: this.settings.priority[SnapType.PERPENDICULAR],
              metadata: { 
                segmentIndex: i,
                angle: perpPoint.angle
              }
            });
          }
        }
      }
    }
    
    return snaps;
  }
  
  private findPolygonSnaps(
    coordinate: Coordinate, 
    polygon: Polygon, 
    feature: Feature,
    map: any
  ): SnapPoint[] {
    const snaps: SnapPoint[] = [];
    const rings = polygon.getLinearRings();
    
    for (const ring of rings) {
      const lineString = new LineString(ring.getCoordinates());
      snaps.push(...this.findLineStringSnaps(coordinate, lineString, feature, map));
    }
    
    // Center point snapping
    if (this.settings.types.has(SnapType.CENTER)) {
      const center = polygon.getInteriorPoint().getCoordinates();
      const distance = getDistance(coordinate, center);
      
      if (distance <= this.settings.tolerance) {
        snaps.push({
          coordinate: center,
          type: SnapType.CENTER,
          feature,
          distance,
          priority: this.settings.priority[SnapType.CENTER]
        });
      }
    }
    
    return snaps;
  }
  
  private findGridSnap(coordinate: Coordinate): SnapPoint | null {
    if (!this.settings.gridSize) return null;
    
    const gridSize = this.settings.gridSize;
    const origin = this.gridOrigin;
    
    // Calculate nearest grid point
    const x = Math.round(((coordinate[0] ?? 0) - (origin[0] ?? 0)) / gridSize) * gridSize + (origin[0] ?? 0);
    const y = Math.round(((coordinate[1] ?? 0) - (origin[1] ?? 0)) / gridSize) * gridSize + (origin[1] ?? 0);
    
    const gridPoint: Coordinate = [x, y];
    const distance = getDistance(coordinate, gridPoint);
    
    if (distance <= this.settings.tolerance) {
      return {
        coordinate: gridPoint,
        type: SnapType.GRID,
        distance,
        priority: this.settings.priority[SnapType.GRID]
      };
    }
    
    return null;
  }
  
  private findIntersectionSnaps(coordinate: Coordinate, map: any): SnapPoint[] {
    const snaps: SnapPoint[] = [];
    const features = this.getAllFeatures();
    
    // Find intersections between different features
    for (let i = 0; i < features.length; i++) {
      for (let j = i + 1; j < features.length; j++) {
        const geom1 = features[i]?.getGeometry();
        const geom2 = features[j]?.getGeometry();
        if (!geom1 || !geom2) continue;
        
        const intersections = this.findGeometryIntersections(geom1, geom2);
        
        for (const intersection of intersections) {
          const distance = getDistance(coordinate, intersection);
          
          if (distance <= this.settings.tolerance) {
            snaps.push({
              coordinate: intersection,
              type: SnapType.INTERSECTION,
              distance,
              priority: this.settings.priority[SnapType.INTERSECTION]
            });
          }
        }
      }
    }
    
    return snaps;
  }
  
  private findNearestPointOnSegment(
    point: Coordinate, 
    start: Coordinate, 
    end: Coordinate
  ): { coordinate: Coordinate; parameter: number } | null {
    const dx = (end[0] ?? 0) - (start[0] ?? 0);
    const dy = (end[1] ?? 0) - (start[1] ?? 0);
    
    if (dx === 0 && dy === 0) {
      return { coordinate: start, parameter: 0 };
    }
    
    const t = (((point[0] ?? 0) - (start[0] ?? 0)) * dx + ((point[1] ?? 0) - (start[1] ?? 0)) * dy) / (dx * dx + dy * dy);
    const clampedT = Math.max(0, Math.min(1, t));
    
    const nearestPoint: Coordinate = [
      (start[0] ?? 0) + clampedT * dx,
      (start[1] ?? 0) + clampedT * dy
    ];
    
    return { coordinate: nearestPoint, parameter: clampedT };
  }
  
  private findPerpendicularPoint(
    point: Coordinate,
    start: Coordinate,
    end: Coordinate
  ): { coordinate: Coordinate; angle: number } | null {
    const dx = (end[0] ?? 0) - (start[0] ?? 0);
    const dy = (end[1] ?? 0) - (start[1] ?? 0);
    
    if (dx === 0 && dy === 0) {
      return null;
    }
    
    const t = (((point[0] ?? 0) - (start[0] ?? 0)) * dx + ((point[1] ?? 0) - (start[1] ?? 0)) * dy) / (dx * dx + dy * dy);
    
    const perpPoint: Coordinate = [
      (start[0] ?? 0) + t * dx,
      (start[1] ?? 0) + t * dy
    ];
    
    const angle = Math.atan2(dy, dx);
    
    return { coordinate: perpPoint, angle };
  }
  
  private findGeometryIntersections(geom1: Geometry, geom2: Geometry): Coordinate[] {
    // Simplified intersection detection
    // In a real implementation, use a more robust geometric library
    const intersections: Coordinate[] = [];
    
    // This would require a more sophisticated geometric computation library
    // For now, return empty array as placeholder
    
    return intersections;
  }
  
  private selectBestSnap(candidates: SnapPoint[], coordinate: Coordinate): SnapPoint | null {
    if (candidates.length === 0) return null;
    
    // Sort by priority first, then by distance
    candidates.sort((a: any, b: any) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // Higher priority first
      }
      return a.distance - b.distance; // Closer distance second
    });
    
    const bestCandidate = candidates[0];
    if (!bestCandidate) return null;
    
    // Apply magnetic snapping if enabled
    if (this.settings.magneticSnap && this.lastSnapPoint) {
      const magneticDistance = getDistance(coordinate, this.lastSnapPoint.coordinate);
      const candidateDistance = getDistance(coordinate, bestCandidate.coordinate);
      
      // Prefer to stay on the same snap point if within magnetic range
      if (magneticDistance <= this.settings.tolerance * 1.5 && 
          magneticDistance <= candidateDistance * 1.2) {
        return this.lastSnapPoint;
      }
    }
    
    return bestCandidate;
  }
  
  private getAllFeatures(): Feature[] {
    const allFeatures: Feature[] = [];
    for (const source of this.sources) {
      allFeatures.push(...source.getFeatures());
    }
    return allFeatures;
  }
  
  private clearCache(): void {
    this.snapCache.clear();
  }
  
  private addToHistory(snapPoint: SnapPoint): void {
    this.snapHistory.unshift(snapPoint);
    if (this.snapHistory.length > 10) {
      this.snapHistory.pop();
    }
  }
  
  getLastSnapPoint(): SnapPoint | undefined {
    return this.lastSnapPoint;
  }
  
  getSnapHistory(): SnapPoint[] {
    return [...this.snapHistory];
  }
  
  clearSnapHistory(): void {
    this.snapHistory = [];
    this.lastSnapPoint = undefined;
  }
  
  // Utility methods for external components
  isSnapTypeEnabled(type: SnapType): boolean {
    return this.settings.types.has(type);
  }
  
  toggleSnapType(type: SnapType): void {
    if (this.settings.types.has(type)) {
      this.settings.types.delete(type);
    } else {
      this.settings.types.add(type);
    }
    this.clearCache();
  }
  
  setSnapTolerance(tolerance: number): void {
    this.settings.tolerance = Math.max(1, Math.min(50, tolerance));
    this.clearCache();
  }
  
  getSnapInfo(): {
    enabled: boolean;
    activeTypes: SnapType[];
    tolerance: number;
    lastSnap?: SnapPoint;
  } {
    return {
      enabled: this.settings.enabled,
      activeTypes: Array.from(this.settings.types),
      tolerance: this.settings.tolerance,
      lastSnap: this.lastSnapPoint
    };
  }
}

export default SnapManager;