'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import type { Coordinate } from 'ol/coordinate.js';
import { transform, fromLonLat, toLonLat } from 'ol/proj.js';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Calculator, Target, MapPin, Crosshair } from 'lucide-react';

export enum CoordinateSystem {
  GEOGRAPHIC_WGS84 = 'EPSG:4326',
  INDIANA_STATE_PLANE_EAST = 'EPSG:2965',
  INDIANA_STATE_PLANE_WEST = 'EPSG:2966',
  UTM_ZONE_16N = 'EPSG:32616',
  WEB_MERCATOR = 'EPSG:3857'
}

export enum CoordinateFormat {
  DECIMAL_DEGREES = 'decimal_degrees',
  DEGREES_MINUTES_SECONDS = 'dms',
  DEGREES_DECIMAL_MINUTES = 'ddm',
  FEET = 'feet',
  METERS = 'meters',
  SURVEY_FEET = 'survey_feet'
}

export interface CoordinateInput {
  x: number | string;
  y: number | string;
  z?: number | string;
  system: CoordinateSystem;
  format: CoordinateFormat;
}

export interface PrecisionInputSettings {
  defaultSystem: CoordinateSystem;
  defaultFormat: CoordinateFormat;
  precision: number;
  showElevation: boolean;
  enablePolarInput: boolean;
  enableRelativeInput: boolean;
  enableBearingDistance: boolean;
  autoValidate: boolean;
}

interface PrecisionCoordinateInputProps {
  onCoordinateChange?: (coordinate: Coordinate | null, isValid: boolean) => void;
  onCoordinateSubmit?: (coordinate: Coordinate) => void;
  initialCoordinate?: Coordinate;
  baseCoordinate?: Coordinate; // For relative input
  settings?: Partial<PrecisionInputSettings>;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const PrecisionCoordinateInput: React.FC<PrecisionCoordinateInputProps> = ({
  onCoordinateChange,
  onCoordinateSubmit,
  initialCoordinate,
  baseCoordinate,
  settings = {},
  disabled = false,
  placeholder = "Enter coordinates...",
  className = ""
}) => {
  const defaultSettings: PrecisionInputSettings = {
    defaultSystem: CoordinateSystem.INDIANA_STATE_PLANE_EAST,
    defaultFormat: CoordinateFormat.FEET,
    precision: 2,
    showElevation: false,
    enablePolarInput: true,
    enableRelativeInput: true,
    enableBearingDistance: true,
    autoValidate: true,
    ...settings
  };

  const [inputMode, setInputMode] = useState<'absolute' | 'relative' | 'polar' | 'bearing'>('absolute');
  const [coordinateSystem, setCoordinateSystem] = useState<CoordinateSystem>(defaultSettings.defaultSystem);
  const [coordinateFormat, setCoordinateFormat] = useState<CoordinateFormat>(defaultSettings.defaultFormat);
  
  // Input values
  const [xInput, setXInput] = useState<string>('');
  const [yInput, setYInput] = useState<string>('');
  const [zInput, setZInput] = useState<string>('');
  
  // Polar input values
  const [distanceInput, setDistanceInput] = useState<string>('');
  const [angleInput, setAngleInput] = useState<string>('');
  
  // Bearing/Distance input values
  const [bearingInput, setBearingInput] = useState<string>('');
  const [bearingDistanceInput, setBearingDistanceInput] = useState<string>('');
  
  const [isValid, setIsValid] = useState<boolean>(false);
  const [validationMessage, setValidationMessage] = useState<string>('');
  const [currentCoordinate, setCurrentCoordinate] = useState<Coordinate | null>(null);
  
  const xInputRef = useRef<HTMLInputElement>(null);
  const yInputRef = useRef<HTMLInputElement>(null);

  // Parse bearing from various formats (e.g., "N 45° E", "45.5", "N45E")
  const parseBearing = useCallback((bearingStr: string): number | null => {
    const cleaned = bearingStr.trim().toUpperCase();
    
    // Simple decimal degrees
    const decimal = parseFloat(cleaned);
    if (!isNaN(decimal)) {
      return decimal % 360;
    }
    
    // Quadrant bearing format (e.g., "N 45° E")
    const quadrantMatch = cleaned.match(/^([NS])\s*(\d+(?:\.\d+)?)[°]?\s*([EW])$/);
    if (quadrantMatch && quadrantMatch.length >= 4) {
      const ns = quadrantMatch[1];
      const angle = quadrantMatch[2];
      const ew = quadrantMatch[3];
      
      if (!ns || !angle || !ew) return null;
      
      const angleVal = parseFloat(angle);
      if (isNaN(angleVal)) return null;
      
      if (ns === 'N' && ew === 'E') return angleVal;
      if (ns === 'N' && ew === 'W') return 360 - angleVal;
      if (ns === 'S' && ew === 'E') return 180 - angleVal;
      if (ns === 'S' && ew === 'W') return 180 + angleVal;
    }
    
    return null;
  }, []);

  // Validate coordinate ranges for different systems
  const validateCoordinateRange = useCallback((coord: Coordinate, system: CoordinateSystem): { valid: boolean; coordinate: Coordinate | null; message: string } => {
    if (!coord || coord.length < 2) {
      return { valid: false, coordinate: null, message: 'Invalid coordinate' };
    }
    
    const x = coord[0] ?? 0;
    const y = coord[1] ?? 0;
    
    switch (system) {
      case CoordinateSystem.GEOGRAPHIC_WGS84:
        if (x < -180 || x > 180 || y < -90 || y > 90) {
          return { valid: false, coordinate: null, message: 'Coordinates outside valid geographic range' };
        }
        break;
        
      case CoordinateSystem.INDIANA_STATE_PLANE_EAST:
      case CoordinateSystem.INDIANA_STATE_PLANE_WEST:
        // Typical Indiana State Plane ranges (feet)
        if (x < 100000 || x > 2000000 || y < 1100000 || y > 1600000) {
          return { valid: false, coordinate: null, message: 'Coordinates outside typical Indiana State Plane range' };
        }
        break;
        
      case CoordinateSystem.UTM_ZONE_16N:
        // UTM Zone 16N ranges
        if (x < 166000 || x > 834000 || y < 0 || y > 10000000) {
          return { valid: false, coordinate: null, message: 'Coordinates outside UTM Zone 16N range' };
        }
        break;
    }
    
    return { valid: true, coordinate: coord, message: 'Valid coordinate range' };
  }, []);

  // Initialize with initial coordinate
  useEffect(() => {
    if (initialCoordinate && initialCoordinate.length >= 2) {
      const x = initialCoordinate[0] ?? 0;
      const y = initialCoordinate[1] ?? 0;
      const z = initialCoordinate[2];
      setXInput(x.toFixed(defaultSettings.precision));
      setYInput(y.toFixed(defaultSettings.precision));
      if (z !== undefined) {
        setZInput(z.toFixed(defaultSettings.precision));
      }
    }
  }, [initialCoordinate, defaultSettings.precision]);

  // Validation function
  const validateInput = useCallback((): { valid: boolean; coordinate: Coordinate | null; message: string } => {
    try {
      let coordinate: Coordinate | null = null;
      
      switch (inputMode) {
        case 'absolute':
          if (!xInput.trim() || !yInput.trim()) {
            return { valid: false, coordinate: null, message: 'X and Y coordinates are required' };
          }
          
          const x = parseFloat(xInput);
          const y = parseFloat(yInput);
          const z = zInput ? parseFloat(zInput) : undefined;
          
          if (isNaN(x) || isNaN(y)) {
            return { valid: false, coordinate: null, message: 'Invalid numeric values' };
          }
          
          coordinate = z !== undefined ? [x, y, z] : [x, y];
          break;
          
        case 'relative':
          if (!baseCoordinate || baseCoordinate.length < 2) {
            return { valid: false, coordinate: null, message: 'Base coordinate required for relative input' };
          }
          
          if (!xInput.trim() || !yInput.trim()) {
            return { valid: false, coordinate: null, message: 'Delta X and Y values are required' };
          }
          
          const dx = parseFloat(xInput);
          const dy = parseFloat(yInput);
          const dz = zInput ? parseFloat(zInput) : 0;
          
          if (isNaN(dx) || isNaN(dy) || (zInput && isNaN(dz))) {
            return { valid: false, coordinate: null, message: 'Invalid delta values' };
          }
          
          const relativeBaseX = baseCoordinate[0] ?? 0;
          const relativeBaseY = baseCoordinate[1] ?? 0;
          const relativeBaseZ = baseCoordinate[2] ?? 0;
          
          coordinate = [
            relativeBaseX + dx,
            relativeBaseY + dy,
            relativeBaseZ + dz
          ];
          break;
          
        case 'polar':
          if (!baseCoordinate || baseCoordinate.length < 2) {
            return { valid: false, coordinate: null, message: 'Base coordinate required for polar input' };
          }
          
          if (!distanceInput.trim() || !angleInput.trim()) {
            return { valid: false, coordinate: null, message: 'Distance and angle are required' };
          }
          
          const distance = parseFloat(distanceInput);
          const angle = parseFloat(angleInput);
          
          if (isNaN(distance) || isNaN(angle)) {
            return { valid: false, coordinate: null, message: 'Invalid distance or angle values' };
          }
          
          // Convert angle to radians
          const angleRad = (angle * Math.PI) / 180;
          const polarBaseX = baseCoordinate[0] ?? 0;
          const polarBaseY = baseCoordinate[1] ?? 0;
          const polarBaseZ = baseCoordinate[2] ?? 0;
          
          coordinate = [
            polarBaseX + distance * Math.cos(angleRad),
            polarBaseY + distance * Math.sin(angleRad),
            polarBaseZ
          ];
          break;
          
        case 'bearing':
          if (!baseCoordinate || baseCoordinate.length < 2) {
            return { valid: false, coordinate: null, message: 'Base coordinate required for bearing input' };
          }
          
          if (!bearingInput.trim() || !bearingDistanceInput.trim()) {
            return { valid: false, coordinate: null, message: 'Bearing and distance are required' };
          }
          
          const bearing = parseBearing(bearingInput);
          const bearingDistance = parseFloat(bearingDistanceInput);
          
          if (bearing === null || isNaN(bearingDistance)) {
            return { valid: false, coordinate: null, message: 'Invalid bearing or distance values' };
          }
          
          // Convert bearing to mathematical angle (counterclockwise from east)
          const mathAngle = (90 - bearing) * Math.PI / 180;
          const bearingBaseX = baseCoordinate[0] ?? 0;
          const bearingBaseY = baseCoordinate[1] ?? 0;
          const bearingBaseZ = baseCoordinate[2] ?? 0;
          
          coordinate = [
            bearingBaseX + bearingDistance * Math.cos(mathAngle),
            bearingBaseY + bearingDistance * Math.sin(mathAngle),
            bearingBaseZ
          ];
          break;
      }
      
      if (!coordinate) {
        return { valid: false, coordinate: null, message: 'Unable to calculate coordinate' };
      }
      
      // Validate coordinate ranges based on coordinate system
      const validation = validateCoordinateRange(coordinate, coordinateSystem);
      if (!validation.valid) {
        return validation;
      }
      
      return { valid: true, coordinate, message: 'Valid coordinate' };
      
    } catch (error) {
      return { valid: false, coordinate: null, message: 'Invalid input format' };
    }
  }, [inputMode, xInput, yInput, zInput, distanceInput, angleInput, bearingInput, bearingDistanceInput, baseCoordinate, coordinateSystem, parseBearing, validateCoordinateRange]);

  // Auto-validate on input change
  useEffect(() => {
    if (defaultSettings.autoValidate) {
      const validation = validateInput();
      setIsValid(validation.valid);
      setValidationMessage(validation.message);
      setCurrentCoordinate(validation.coordinate);
      
      onCoordinateChange?.(validation.coordinate, validation.valid);
    }
  }, [validateInput, onCoordinateChange, defaultSettings.autoValidate]);

  // Handle coordinate system change
  const handleSystemChange = useCallback((newSystem: CoordinateSystem) => {
    if (currentCoordinate && currentCoordinate.length >= 2) {
      // Transform existing coordinate to new system
      try {
        const transformed = transform(currentCoordinate, coordinateSystem, newSystem);
        if (transformed && transformed.length >= 2) {
          const x = transformed[0] ?? 0;
          const y = transformed[1] ?? 0;
          const z = transformed[2];
          
          setXInput(x.toFixed(defaultSettings.precision));
          setYInput(y.toFixed(defaultSettings.precision));
          if (z !== undefined) {
            setZInput(z.toFixed(defaultSettings.precision));
          }
        }
      } catch (error) {
        // If transformation fails, clear inputs
        setXInput('');
        setYInput('');
        setZInput('');
      }
    }
    
    setCoordinateSystem(newSystem);
    
    // Update format based on system
    if (newSystem === CoordinateSystem.GEOGRAPHIC_WGS84) {
      setCoordinateFormat(CoordinateFormat.DECIMAL_DEGREES);
    } else {
      setCoordinateFormat(CoordinateFormat.FEET);
    }
  }, [currentCoordinate, coordinateSystem, defaultSettings.precision]);

  // Handle submit
  const handleSubmit = useCallback(() => {
    const validation = validateInput();
    if (validation.valid && validation.coordinate) {
      onCoordinateSubmit?.(validation.coordinate);
    }
  }, [validateInput, onCoordinateSubmit]);

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Tab' && e.target === xInputRef.current) {
      e.preventDefault();
      yInputRef.current?.focus();
    }
  }, [handleSubmit]);

  // Render coordinate system options
  const renderCoordinateSystemSelect = () => (
    <Select value={coordinateSystem} onValueChange={handleSystemChange}>
      <SelectTrigger className="w-full">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value={CoordinateSystem.INDIANA_STATE_PLANE_EAST}>
          Indiana State Plane East
        </SelectItem>
        <SelectItem value={CoordinateSystem.INDIANA_STATE_PLANE_WEST}>
          Indiana State Plane West
        </SelectItem>
        <SelectItem value={CoordinateSystem.UTM_ZONE_16N}>
          UTM Zone 16N
        </SelectItem>
        <SelectItem value={CoordinateSystem.GEOGRAPHIC_WGS84}>
          Geographic (WGS84)
        </SelectItem>
        <SelectItem value={CoordinateSystem.WEB_MERCATOR}>
          Web Mercator
        </SelectItem>
      </SelectContent>
    </Select>
  );

  // Render absolute coordinate input
  const renderAbsoluteInput = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="x-input">X Coordinate</Label>
          <Input
            id="x-input"
            ref={xInputRef}
            type="text"
            value={xInput}
            onChange={(e: any) => setXInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="X..."
            disabled={disabled}
          />
        </div>
        <div>
          <Label htmlFor="y-input">Y Coordinate</Label>
          <Input
            id="y-input"
            ref={yInputRef}
            type="text"
            value={yInput}
            onChange={(e: any) => setYInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Y..."
            disabled={disabled}
          />
        </div>
      </div>
      
      {defaultSettings.showElevation && (
        <div>
          <Label htmlFor="z-input">Z Coordinate (Elevation)</Label>
          <Input
            id="z-input"
            type="text"
            value={zInput}
            onChange={(e: any) => setZInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Z..."
            disabled={disabled}
          />
        </div>
      )}
    </div>
  );

  // Render relative coordinate input
  const renderRelativeInput = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="dx-input">ΔX (Delta X)</Label>
          <Input
            id="dx-input"
            type="text"
            value={xInput}
            onChange={(e: any) => setXInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="±X..."
            disabled={disabled}
          />
        </div>
        <div>
          <Label htmlFor="dy-input">ΔY (Delta Y)</Label>
          <Input
            id="dy-input"
            type="text"
            value={yInput}
            onChange={(e: any) => setYInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="±Y..."
            disabled={disabled}
          />
        </div>
      </div>
      
      {baseCoordinate && baseCoordinate.length >= 2 && (
        <div className="text-sm text-muted-foreground">
          Base: ({(baseCoordinate[0] ?? 0).toFixed(2)}, {(baseCoordinate[1] ?? 0).toFixed(2)})
        </div>
      )}
    </div>
  );

  // Render polar coordinate input
  const renderPolarInput = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="distance-input">Distance</Label>
          <Input
            id="distance-input"
            type="text"
            value={distanceInput}
            onChange={(e: any) => setDistanceInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Distance..."
            disabled={disabled}
          />
        </div>
        <div>
          <Label htmlFor="angle-input">Angle (degrees)</Label>
          <Input
            id="angle-input"
            type="text"
            value={angleInput}
            onChange={(e: any) => setAngleInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="0-360°..."
            disabled={disabled}
          />
        </div>
      </div>
      
      {baseCoordinate && baseCoordinate.length >= 2 && (
        <div className="text-sm text-muted-foreground">
          From: ({(baseCoordinate[0] ?? 0).toFixed(2)}, {(baseCoordinate[1] ?? 0).toFixed(2)})
        </div>
      )}
    </div>
  );

  // Render bearing/distance input
  const renderBearingInput = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="bearing-input">Bearing</Label>
          <Input
            id="bearing-input"
            type="text"
            value={bearingInput}
            onChange={(e: any) => setBearingInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="N 45° E, 45.5..."
            disabled={disabled}
          />
        </div>
        <div>
          <Label htmlFor="bearing-distance-input">Distance</Label>
          <Input
            id="bearing-distance-input"
            type="text"
            value={bearingDistanceInput}
            onChange={(e: any) => setBearingDistanceInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Distance..."
            disabled={disabled}
          />
        </div>
      </div>
      
      {baseCoordinate && baseCoordinate.length >= 2 && (
        <div className="text-sm text-muted-foreground">
          From: ({(baseCoordinate[0] ?? 0).toFixed(2)}, {(baseCoordinate[1] ?? 0).toFixed(2)})
        </div>
      )}
    </div>
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Calculator className="h-4 w-4" />
          Precision Coordinate Input
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Coordinate System Selection */}
        <div>
          <Label>Coordinate System</Label>
          {renderCoordinateSystemSelect()}
        </div>
        
        {/* Input Mode Tabs */}
        <Tabs value={inputMode} onValueChange={(value) => setInputMode(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="absolute" className="text-xs">
              <MapPin className="h-3 w-3 mr-1" />
              Absolute
            </TabsTrigger>
            <TabsTrigger value="relative" className="text-xs" disabled={!baseCoordinate}>
              <Target className="h-3 w-3 mr-1" />
              Relative
            </TabsTrigger>
            <TabsTrigger value="polar" className="text-xs" disabled={!baseCoordinate || !defaultSettings.enablePolarInput}>
              <Crosshair className="h-3 w-3 mr-1" />
              Polar
            </TabsTrigger>
            <TabsTrigger value="bearing" className="text-xs" disabled={!baseCoordinate || !defaultSettings.enableBearingDistance}>
              Bearing
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="absolute" className="mt-4">
            {renderAbsoluteInput()}
          </TabsContent>
          
          <TabsContent value="relative" className="mt-4">
            {renderRelativeInput()}
          </TabsContent>
          
          <TabsContent value="polar" className="mt-4">
            {renderPolarInput()}
          </TabsContent>
          
          <TabsContent value="bearing" className="mt-4">
            {renderBearingInput()}
          </TabsContent>
        </Tabs>
        
        {/* Validation Status */}
        {validationMessage && (
          <div className="flex items-center gap-2">
            <Badge variant={isValid ? "default" : "destructive"} className="text-xs">
              {isValid ? 'Valid' : 'Invalid'}
            </Badge>
            <span className="text-xs text-muted-foreground">{validationMessage}</span>
          </div>
        )}
        
        {/* Action Button */}
        <Button 
          onClick={handleSubmit} 
          disabled={!isValid || disabled}
          className="w-full"
          size="sm"
        >
          Apply Coordinate
        </Button>
      </CardContent>
    </Card>
  );
};

export default PrecisionCoordinateInput;