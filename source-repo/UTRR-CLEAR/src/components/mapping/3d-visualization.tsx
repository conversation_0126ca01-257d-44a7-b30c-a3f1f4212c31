'use client';

import React, { useEffect, useRef, useState } from 'react';
import type { Map as OLMap } from 'ol';
import type { Feature } from 'ol';
import type { LineString } from 'ol/geom.js';
import type { ViewMode, ViewModeOptions } from '~/lib/mapping/types';
import type { LayerManager } from './layers/layer-manager';
import { create3DStyle, createUtilityStyle, styleRegistry, get3DStyleFunction } from '~/lib/mapping/styles';
import { safeLog } from '~/lib/error-handler';

interface ThreeDVisualizationProps {
  map: OLMap | null;
  viewMode: ViewMode | ViewModeOptions;
  layerManager?: LayerManager;
}

/**
 * 3D visualization component for utility data
 * Manages switching between different view modes (2D, 3D, cross-section)
 */
export function ThreeDVisualization({ map, viewMode, layerManager }: ThreeDVisualizationProps) {
  const [webGLSupported, setWebGLSupported] = useState<boolean>(false);
  const previousViewMode = useRef<'2d' | '3d' | 'cross-section'>('2d');

  // Check for WebGL support on component mount
  useEffect(() => {
    try {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      setWebGLSupported(!!context);

      if (!context) {
        safeLog.warn('WebGL is not supported - 3D visualization will be disabled');
      }
    } catch (e) {
      safeLog.error('Error checking WebGL support:', { error: String(e) } as Record<string, unknown>);
      setWebGLSupported(false);
    }
  }, []);

  // Handle view mode changes
  useEffect(() => {
    if (!map || !layerManager || (viewMode && previousViewMode.current && 
        viewMode === previousViewMode.current)) {
      return;
    }

    // Extract view mode from options if needed
    const currentViewMode = typeof viewMode === 'string' ? viewMode : viewMode.type;

    // Update view mode in style registry
    styleRegistry.setViewMode(currentViewMode);

    // Switch between view modes
    if (webGLSupported) {
      // Tell the layer manager to switch to the selected view mode
      layerManager.switchViewMode(currentViewMode);

      // Apply appropriate styles based on view mode
      applyViewModeStyles(map, currentViewMode, layerManager);
    } else if (currentViewMode !== '2d') {
      // If WebGL is not supported, fall back to 2D
      safeLog.warn('WebGL not supported - falling back to 2D mode');
      layerManager.switchViewMode('2d');
    }

    // Update previous view mode reference
    previousViewMode.current = currentViewMode;
  }, [map, viewMode, layerManager, webGLSupported]);

  // No UI is rendered by this component
  return null;
}

/**
 * Apply appropriate styles to features based on view mode
 */
function applyViewModeStyles(map: OLMap, viewMode: ViewMode, layerManager: LayerManager) {
  if (!map || !layerManager) return;

  // Get appropriate style function for the current view mode
  const styleFunction = create3DStyle;

  // Apply styles to utility lines layer
  const utilityLineLayer = layerManager.getVectorLayer('utility_lines');
  if (utilityLineLayer) {
    // Get all features
    const features = utilityLineLayer.getSource()?.getFeatures() || [];

    // Apply appropriate styles to features
    features.forEach((feature: any) => {
      if (viewMode === '2d') {
        // In 2D mode, use standard styling
        const utilityType = feature.get('utilityType');
        const installationType = feature.get('installationType');
        const standardStyle = createUtilityStyle(feature);
        feature.setStyle(standardStyle);
      } else {
        // In 3D or cross-section mode, use the specialized style function
        feature.setStyle(styleFunction(feature));
      }
    });
  }

  // Apply appropriate view angle/rotation for 3D view
  if (viewMode === '3d') {
    // For a true 3D view we would use a WebGL renderer here
    // This is a simplified version that just uses styling to indicate depth
    safeLog.info('Switched to 3D visualization mode');
  } else if (viewMode === 'cross-section') {
    // Cross-section view would typically show a profile view
    safeLog.info('Switched to cross-section visualization mode');
  }
}
