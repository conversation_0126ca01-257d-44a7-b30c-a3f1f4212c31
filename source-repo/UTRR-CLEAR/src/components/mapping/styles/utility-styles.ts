import { Style, Stroke, Fill, Circle as CircleStyle, Icon, Text } from 'ol/style.js';
import { Feature } from 'ol';

// Utility type definitions
export interface UtilityStyleConfig {
  id: string;
  name: string;
  color: string;
  lineStyle: 'solid' | 'dashed' | 'dotted';
  lineWidth: number;
  zIndex: number;
  symbol?: string;
  labelField?: string;
  showLabels: boolean;
}

// Standard utility type configurations
export const UTILITY_STYLE_CONFIGS: UtilityStyleConfig[] = [
  {
    id: 'electric_overhead',
    name: 'Electric - Overhead',
    color: '#FF0000',
    lineStyle: 'solid',
    lineWidth: 3,
    zIndex: 10,
    symbol: 'triangle',
    labelField: 'voltage',
    showLabels: true,
  },
  {
    id: 'electric_underground',
    name: 'Electric - Underground',
    color: '#FF0000',
    lineStyle: 'dashed',
    lineWidth: 3,
    zIndex: 9,
    symbol: 'circle',
    labelField: 'voltage',
    showLabels: true,
  },
  {
    id: 'gas_transmission',
    name: 'Gas - Transmission',
    color: '#FFFF00',
    lineStyle: 'solid',
    lineWidth: 4,
    zIndex: 8,
    symbol: 'diamond',
    labelField: 'pressure',
    showLabels: true,
  },
  {
    id: 'gas_distribution',
    name: 'Gas - Distribution',
    color: '#FFD700',
    lineStyle: 'dashed',
    lineWidth: 2,
    zIndex: 7,
    symbol: 'square',
    labelField: 'diameter',
    showLabels: false,
  },
  {
    id: 'water_main',
    name: 'Water - Main',
    color: '#0000FF',
    lineStyle: 'solid',
    lineWidth: 3,
    zIndex: 6,
    symbol: 'circle',
    labelField: 'diameter',
    showLabels: true,
  },
  {
    id: 'water_service',
    name: 'Water - Service',
    color: '#4169E1',
    lineStyle: 'dotted',
    lineWidth: 2,
    zIndex: 5,
    symbol: 'circle',
    labelField: 'diameter',
    showLabels: false,
  },
  {
    id: 'sewer_gravity',
    name: 'Sewer - Gravity',
    color: '#008000',
    lineStyle: 'solid',
    lineWidth: 3,
    zIndex: 4,
    symbol: 'circle',
    labelField: 'diameter',
    showLabels: true,
  },
  {
    id: 'sewer_force',
    name: 'Sewer - Force Main',
    color: '#228B22',
    lineStyle: 'dashed',
    lineWidth: 3,
    zIndex: 3,
    symbol: 'arrow',
    labelField: 'diameter',
    showLabels: true,
  },
  {
    id: 'telecom_fiber',
    name: 'Telecommunications - Fiber',
    color: '#8A2BE2',
    lineStyle: 'dashed',
    lineWidth: 2,
    zIndex: 12,
    symbol: 'star',
    labelField: 'provider',
    showLabels: false,
  },
  {
    id: 'telecom_copper',
    name: 'Telecommunications - Copper',
    color: '#FF8C00',
    lineStyle: 'dotted',
    lineWidth: 2,
    zIndex: 11,
    symbol: 'circle',
    labelField: 'provider',
    showLabels: false,
  },
  {
    id: 'cable_tv',
    name: 'Cable TV',
    color: '#800080',
    lineStyle: 'dashed',
    lineWidth: 2,
    zIndex: 2,
    symbol: 'square',
    labelField: 'provider',
    showLabels: false,
  },
  {
    id: 'storm_sewer',
    name: 'Storm Sewer',
    color: '#87CEEB',
    lineStyle: 'solid',
    lineWidth: 3,
    zIndex: 1,
    symbol: 'triangle',
    labelField: 'diameter',
    showLabels: true,
  },
];

// Conflict severity styles
export const CONFLICT_STYLES = {
  low: {
    color: '#FFFF00',
    radius: 6,
    strokeColor: '#FFA500',
    strokeWidth: 2,
  },
  medium: {
    color: '#FFA500',
    radius: 8,
    strokeColor: '#FF4500',
    strokeWidth: 2,
  },
  high: {
    color: '#FF4500',
    radius: 10,
    strokeColor: '#FF0000',
    strokeWidth: 3,
  },
  critical: {
    color: '#FF0000',
    radius: 12,
    strokeColor: '#8B0000',
    strokeWidth: 3,
  },
};

// Quality level styles (SUE - Subsurface Utility Engineering)
export const QUALITY_LEVEL_STYLES = {
  A: {
    // Precise location
    strokeWidth: 4,
    confidence: 1.0,
    pattern: 'solid',
  },
  B: {
    // Accurate location
    strokeWidth: 3,
    confidence: 0.8,
    pattern: 'solid',
  },
  C: {
    // Approximate location
    strokeWidth: 2,
    confidence: 0.6,
    pattern: 'dashed',
  },
  D: {
    // Estimated location
    strokeWidth: 1,
    confidence: 0.4,
    pattern: 'dotted',
  },
};

export class UtilityStyleRegistry {
  private styles: Map<string, UtilityStyleConfig>;
  private compiledStyles: Map<string, Style>;

  constructor() {
    this.styles = new Map();
    this.compiledStyles = new Map();
    this.initializeDefaultStyles();
  }

  // Initialize default utility styles
  private initializeDefaultStyles(): void {
    UTILITY_STYLE_CONFIGS.forEach((config) => {
      this.styles.set(config.id, config);
      this.compiledStyles.set(config.id, this.createStyle(config));
    });
  }

  // Create OpenLayers style from configuration
  private createStyle(config: UtilityStyleConfig): Style {
    let lineDash: number[] = [];
    if (config.lineStyle === 'dashed') {
      lineDash = [10, 5];
    } else if (config.lineStyle === 'dotted') {
      lineDash = [2, 2];
    }

    const style = new Style({
      stroke: new Stroke({
        color: config.color,
        width: config.lineWidth,
        lineDash: lineDash,
      }),
      fill: new Fill({
        color: config.color + '20', // 20% opacity
      }),
      image: this.createSymbolStyle(config),
      zIndex: config.zIndex,
    });

    return style;
  }

  // Create symbol style for points
  private createSymbolStyle(config: UtilityStyleConfig): CircleStyle {
    const radius = config.lineWidth + 2;

    return new CircleStyle({
      radius: radius,
      fill: new Fill({ color: config.color }),
      stroke: new Stroke({
        color: '#ffffff',
        width: 1,
      }),
    });
  }

  // Get style by utility type
  getStyle(utilityType: string): Style | undefined {
    return this.compiledStyles.get(utilityType);
  }

  // Get style configuration
  getStyleConfig(utilityType: string): UtilityStyleConfig | undefined {
    return this.styles.get(utilityType);
  }

  // Create dynamic style function for features
  createStyleFunction(): (feature: Feature) => Style | Style[] {
    return (feature: Feature) => {
      const utilityType = feature.get('utilityType') as string;
      const qualityLevel = feature.get('qualityLevel') as string;
      const conflictSeverity = feature.get('conflictSeverity') as string;

      // Get base style
      let style = this.getStyle(utilityType);
      if (!style) {
        // Fallback to default style
        style = this.createDefaultStyle();
      }

      // Clone the style to modify it
      const clonedStyle = style.clone();

      // Apply quality level modifications
      if (qualityLevel && QUALITY_LEVEL_STYLES[qualityLevel as keyof typeof QUALITY_LEVEL_STYLES]) {
        const qualityStyle =
          QUALITY_LEVEL_STYLES[qualityLevel as keyof typeof QUALITY_LEVEL_STYLES];
        const stroke = clonedStyle.getStroke();
        if (stroke) {
          stroke.setWidth(qualityStyle.strokeWidth);

          // Set line dash based on quality
          if (qualityStyle.pattern === 'dashed') {
            stroke.setLineDash([8, 4]);
          } else if (qualityStyle.pattern === 'dotted') {
            stroke.setLineDash([2, 2]);
          }
        }
      }

      // Apply conflict severity styles for conflict markers
      if (conflictSeverity && CONFLICT_STYLES[conflictSeverity as keyof typeof CONFLICT_STYLES]) {
        const conflictStyle = CONFLICT_STYLES[conflictSeverity as keyof typeof CONFLICT_STYLES];
        clonedStyle.setImage(
          new CircleStyle({
            radius: conflictStyle.radius,
            fill: new Fill({ color: conflictStyle.color }),
            stroke: new Stroke({
              color: conflictStyle.strokeColor,
              width: conflictStyle.strokeWidth,
            }),
          })
        );
      }

      // Add labels if configured
      const config = this.getStyleConfig(utilityType);
      if (config && config.showLabels && config.labelField) {
        const labelValue = feature.get(config.labelField);
        if (labelValue) {
          clonedStyle.setText(
            new Text({
              text: String(labelValue),
              font: '12px Calibri,sans-serif',
              fill: new Fill({ color: '#000' }),
              stroke: new Stroke({
                color: '#fff',
                width: 3,
              }),
              offsetY: -15,
            })
          );
        }
      }

      return clonedStyle;
    };
  }

  // Create default style for unknown utility types
  private createDefaultStyle(): Style {
    return new Style({
      stroke: new Stroke({
        color: '#808080',
        width: 2,
        lineDash: [5, 5],
      }),
      fill: new Fill({
        color: 'rgba(128, 128, 128, 0.2)',
      }),
      image: new CircleStyle({
        radius: 4,
        fill: new Fill({ color: '#808080' }),
        stroke: new Stroke({ color: '#ffffff', width: 1 }),
      }),
    });
  }

  // Register new utility style
  registerStyle(config: UtilityStyleConfig): void {
    this.styles.set(config.id, config);
    this.compiledStyles.set(config.id, this.createStyle(config));
  }

  // Update existing style
  updateStyle(id: string, updates: Partial<UtilityStyleConfig>): void {
    const existing = this.styles.get(id);
    if (existing) {
      const updated = { ...existing, ...updates };
      this.styles.set(id, updated);
      this.compiledStyles.set(id, this.createStyle(updated));
    }
  }

  // Get all registered utility types
  getAllUtilityTypes(): UtilityStyleConfig[] {
    return Array.from(this.styles.values());
  }

  // Get utility types by category
  getUtilityTypesByCategory(): Record<string, UtilityStyleConfig[]> {
    const categories: Record<string, UtilityStyleConfig[]> = {};

    this.styles.forEach((config) => {
      const category = config.id.split('_')[0]; // Get first part as category
      if (category && !categories[category]) {
        categories[category] = [];
      }
      if (category && categories[category]) {
        categories[category].push(config);
      }
    });

    return categories;
  }

  // Create conflict style
  createConflictStyle(severity: string = 'medium'): Style {
    const conflictConfig =
      CONFLICT_STYLES[severity as keyof typeof CONFLICT_STYLES] || CONFLICT_STYLES.medium;

    return new Style({
      image: new CircleStyle({
        radius: conflictConfig.radius,
        fill: new Fill({ color: conflictConfig.color }),
        stroke: new Stroke({
          color: conflictConfig.strokeColor,
          width: conflictConfig.strokeWidth,
        }),
      }),
      zIndex: 1000, // Always on top
    });
  }

  // Create measurement style
  createMeasurementStyle(): Style {
    return new Style({
      stroke: new Stroke({
        color: '#1976d2',
        width: 3,
        lineDash: [5, 10],
      }),
      fill: new Fill({
        color: 'rgba(25, 118, 210, 0.1)',
      }),
      image: new CircleStyle({
        radius: 6,
        fill: new Fill({ color: '#1976d2' }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    });
  }

  // Create drawing style
  createDrawingStyle(): Style {
    return new Style({
      stroke: new Stroke({
        color: '#ff6b6b',
        width: 2,
      }),
      fill: new Fill({
        color: 'rgba(255, 107, 107, 0.1)',
      }),
      image: new CircleStyle({
        radius: 5,
        fill: new Fill({ color: '#ff6b6b' }),
        stroke: new Stroke({ color: '#ffffff', width: 1 }),
      }),
    });
  }

  // Export styles configuration
  exportStyles(): UtilityStyleConfig[] {
    return Array.from(this.styles.values());
  }

  // Import styles configuration
  importStyles(configs: UtilityStyleConfig[]): void {
    configs.forEach((config) => {
      this.registerStyle(config);
    });
  }

  // Reset to default styles
  resetToDefault(): void {
    this.styles.clear();
    this.compiledStyles.clear();
    this.initializeDefaultStyles();
  }
}

// Create singleton instance
export const utilityStyleRegistry = new UtilityStyleRegistry();
