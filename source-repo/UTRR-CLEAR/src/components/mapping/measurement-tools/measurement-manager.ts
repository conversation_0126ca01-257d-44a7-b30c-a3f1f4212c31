import { Feature } from 'ol';
import { Geometry, Point, LineString, Polygon, Circle } from 'ol/geom.js';
import type { Coordinate } from 'ol/coordinate.js';
import { getDistance, getArea } from 'ol/sphere.js';
import { fromLonLat, toLonLat } from 'ol/proj.js';
import VectorSource from 'ol/source/Vector.js';
import { Style, Stroke, Fill, Text, Circle as CircleStyle } from 'ol/style.js';
import { safeLog } from '~/lib/error-handler';

export interface MeasurementResult {
  id: string;
  type: MeasurementType;
  value: number;
  unit: MeasurementUnit;
  coordinates: Coordinate[];
  geometry: Geometry;
  annotation?: AnnotationData;
  precision: number;
  metadata: {
    bearing?: number;
    perimeter?: number;
    segments?: Array<{
      length: number;
      bearing: number;
      start: Coordinate;
      end: Coordinate;
    }>;
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface AnnotationData {
  text: string;
  position: Coordinate;
  style: {
    fontSize: number;
    fontFamily: string;
    color: string;
    backgroundColor?: string;
    borderColor?: string;
    padding: number;
  };
  leader?: {
    start: Coordinate;
    end: Coordinate;
    style: 'line' | 'arrow' | 'dimension';
  };
}

export enum MeasurementType {
  DISTANCE = 'distance',
  AREA = 'area',
  PERIMETER = 'perimeter',
  ANGLE = 'angle',
  RADIUS = 'radius',
  ELEVATION = 'elevation',
  VOLUME = 'volume',
  BEARING = 'bearing',
  COORDINATES = 'coordinates'
}

export enum MeasurementUnit {
  // Length units
  FEET = 'feet',
  METERS = 'meters',
  INCHES = 'inches',
  CENTIMETERS = 'centimeters',
  YARDS = 'yards',
  KILOMETERS = 'kilometers',
  MILES = 'miles',
  
  // Area units
  SQUARE_FEET = 'square_feet',
  SQUARE_METERS = 'square_meters',
  ACRES = 'acres',
  HECTARES = 'hectares',
  SQUARE_MILES = 'square_miles',
  
  // Angle units
  DEGREES = 'degrees',
  RADIANS = 'radians',
  GRADIANS = 'gradians',
  
  // Volume units
  CUBIC_FEET = 'cubic_feet',
  CUBIC_METERS = 'cubic_meters',
  CUBIC_YARDS = 'cubic_yards'
}

export interface MeasurementSettings {
  primaryLengthUnit: MeasurementUnit;
  primaryAreaUnit: MeasurementUnit;
  primaryAngleUnit: MeasurementUnit;
  precision: number; // decimal places
  showAnnotations: boolean;
  showSegmentMeasurements: boolean;
  showBearings: boolean;
  showCoordinates: boolean;
  coordinateFormat: 'decimal' | 'dms' | 'utm' | 'state_plane';
  elevationSource: 'none' | 'dem' | 'manual';
  annotationStyle: {
    fontSize: number;
    fontFamily: string;
    color: string;
    backgroundColor: string;
    borderColor: string;
  };
}

export class MeasurementManager {
  private measurements = new Map<string, MeasurementResult>();
  private vectorSource: VectorSource;
  private settings: MeasurementSettings;
  private activeMode: MeasurementType | null = null;
  private tempCoordinates: Coordinate[] = [];
  private currentMeasurementId: string | null = null;

  constructor(vectorSource: VectorSource) {
    this.vectorSource = vectorSource;
    this.settings = this.getDefaultSettings();
  }

  private getDefaultSettings(): MeasurementSettings {
    return {
      primaryLengthUnit: MeasurementUnit.FEET,
      primaryAreaUnit: MeasurementUnit.SQUARE_FEET,
      primaryAngleUnit: MeasurementUnit.DEGREES,
      precision: 2,
      showAnnotations: true,
      showSegmentMeasurements: true,
      showBearings: true,
      showCoordinates: false,
      coordinateFormat: 'decimal',
      elevationSource: 'none',
      annotationStyle: {
        fontSize: 12,
        fontFamily: 'Arial, sans-serif',
        color: '#000000',
        backgroundColor: '#ffffff',
        borderColor: '#cccccc',
      },
    };
  }

  setSettings(settings: Partial<MeasurementSettings>): void {
    this.settings = { ...this.settings, ...settings };
    // Update existing measurements with new units/precision
    this.updateAllMeasurements();
  }

  getSettings(): MeasurementSettings {
    return { ...this.settings };
  }

  startMeasurement(type: MeasurementType): string {
    this.finishCurrentMeasurement();
    this.activeMode = type;
    this.tempCoordinates = [];
    this.currentMeasurementId = this.generateId();
    
    safeLog.info('Started measurement', { type, id: this.currentMeasurementId });
    return this.currentMeasurementId;
  }

  addPoint(coordinate: Coordinate, elevation?: number): void {
    if (!this.activeMode || !this.currentMeasurementId) return;

    // Add elevation to coordinate if provided
    const fullCoordinate = elevation !== undefined 
      ? [coordinate[0] ?? 0, coordinate[1] ?? 0, elevation]
      : coordinate;

    this.tempCoordinates.push(fullCoordinate);
    this.updateCurrentMeasurement();
  }

  removeLastPoint(): void {
    if (this.tempCoordinates.length > 0) {
      this.tempCoordinates.pop();
      this.updateCurrentMeasurement();
    }
  }

  finishCurrentMeasurement(): MeasurementResult | null {
    if (!this.activeMode || !this.currentMeasurementId || this.tempCoordinates.length < 2) {
      this.resetCurrentMeasurement();
      return null;
    }

    const measurement = this.createMeasurement(
      this.currentMeasurementId,
      this.activeMode,
      this.tempCoordinates
    );

    if (measurement) {
      this.measurements.set(measurement.id, measurement);
      this.addMeasurementToMap(measurement);
    }

    this.resetCurrentMeasurement();
    return measurement;
  }

  cancelCurrentMeasurement(): void {
    this.resetCurrentMeasurement();
  }

  private resetCurrentMeasurement(): void {
    this.activeMode = null;
    this.tempCoordinates = [];
    this.currentMeasurementId = null;
  }

  private updateCurrentMeasurement(): void {
    if (!this.activeMode || !this.currentMeasurementId) return;

    // Create temporary measurement for preview
    const tempMeasurement = this.createMeasurement(
      'temp',
      this.activeMode,
      this.tempCoordinates
    );

    if (tempMeasurement) {
      // Update preview on map (implementation would depend on map library)
      this.updateMeasurementPreview(tempMeasurement);
    }
  }

  private createMeasurement(
    id: string,
    type: MeasurementType,
    coordinates: Coordinate[]
  ): MeasurementResult | null {
    if (coordinates.length < 2 && type !== MeasurementType.COORDINATES) {
      return null;
    }

    let geometry: Geometry;
    let value: number;
    let unit: MeasurementUnit;
    let metadata: any = {
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    switch (type) {
      case MeasurementType.DISTANCE:
        geometry = new LineString(coordinates);
        value = this.calculateDistance(coordinates);
        unit = this.settings.primaryLengthUnit;
        const firstCoord = coordinates[0];
        const lastCoord = coordinates[coordinates.length - 1];
        if (firstCoord && lastCoord) {
          metadata.bearing = this.calculateBearing(firstCoord, lastCoord);
        }
        metadata.segments = this.calculateSegments(coordinates);
        break;

      case MeasurementType.AREA:
        if (coordinates.length < 3) return null;
        const closedCoords = [...coordinates];
        const firstAreaCoord = coordinates[0];
        const lastAreaCoord = coordinates[coordinates.length - 1];
        if (firstAreaCoord && lastAreaCoord && !this.coordinatesEqual(firstAreaCoord, lastAreaCoord)) {
          closedCoords.push(firstAreaCoord); // Close the polygon
        }
        geometry = new Polygon([closedCoords]);
        value = this.calculateArea(closedCoords);
        unit = this.settings.primaryAreaUnit;
        metadata.perimeter = this.calculatePerimeter(closedCoords);
        break;

      case MeasurementType.PERIMETER:
        if (coordinates.length < 3) return null;
        const firstPerimCoord = coordinates[0];
        if (!firstPerimCoord) return null;
        const perimeterCoords = [...coordinates, firstPerimCoord]; // Close the shape
        geometry = new LineString(perimeterCoords);
        value = this.calculatePerimeter(perimeterCoords.filter((coord): coord is Coordinate => coord != null));
        unit = this.settings.primaryLengthUnit;
        break;

      case MeasurementType.ANGLE:
        if (coordinates.length !== 3) return null;
        const coord1 = coordinates[0];
        const coord2 = coordinates[1];
        const coord3 = coordinates[2];
        if (!coord1 || !coord2 || !coord3) return null;
        geometry = new LineString(coordinates);
        value = this.calculateAngle(coord1, coord2, coord3);
        unit = this.settings.primaryAngleUnit;
        break;

      case MeasurementType.RADIUS:
        if (coordinates.length !== 2) return null;
        const radius = this.calculateDistance(coordinates);
        const centerCoord = coordinates[0];
        if (!centerCoord) return null;
        geometry = new Circle(centerCoord, radius);
        value = radius;
        unit = this.settings.primaryLengthUnit;
        break;

      case MeasurementType.BEARING:
        if (coordinates.length !== 2) return null;
        const startCoord = coordinates[0];
        const endCoord = coordinates[1];
        if (!startCoord || !endCoord) return null;
        geometry = new LineString(coordinates);
        value = this.calculateBearing(startCoord, endCoord);
        unit = MeasurementUnit.DEGREES;
        metadata.distance = this.calculateDistance(coordinates);
        break;

      case MeasurementType.COORDINATES:
        if (coordinates.length !== 1) return null;
        const pointCoord = coordinates[0];
        if (!pointCoord) return null;
        geometry = new Point(pointCoord);
        value = 0; // Not applicable for coordinates
        unit = this.settings.primaryLengthUnit; // Placeholder
        metadata.formattedCoordinates = this.formatCoordinates(pointCoord);
        break;

      default:
        return null;
    }

    const measurement: MeasurementResult = {
      id,
      type,
      value: this.convertToUnit(value, this.getPrimaryUnit(type), unit),
      unit,
      coordinates,
      geometry,
      precision: this.settings.precision,
      metadata,
    };

    // Add annotation if enabled
    if (this.settings.showAnnotations) {
      measurement.annotation = this.createAnnotation(measurement);
    }

    return measurement;
  }

  private calculateDistance(coordinates: Coordinate[]): number {
    let totalDistance = 0;
    for (let i = 0; i < coordinates.length - 1; i++) {
      const coord1 = coordinates[i];
      const coord2 = coordinates[i + 1];
      if (coord1 && coord2) {
        totalDistance += getDistance(coord1, coord2);
      }
    }
    return totalDistance;
  }

  private calculateArea(coordinates: Coordinate[]): number {
    // Convert to polygon and calculate area
    const polygon = new Polygon([coordinates]);
    return getArea(polygon);
  }

  private calculatePerimeter(coordinates: Coordinate[]): number {
    return this.calculateDistance(coordinates);
  }

  private calculateAngle(point1: Coordinate, vertex: Coordinate, point2: Coordinate): number {
    const p1x = point1[0] ?? 0;
    const p1y = point1[1] ?? 0;
    const vx = vertex[0] ?? 0;
    const vy = vertex[1] ?? 0;
    const p2x = point2[0] ?? 0;
    const p2y = point2[1] ?? 0;
    
    const angle1 = Math.atan2(p1y - vy, p1x - vx);
    const angle2 = Math.atan2(p2y - vy, p2x - vx);
    let angle = Math.abs(angle2 - angle1);
    
    // Ensure angle is between 0 and 180 degrees
    if (angle > Math.PI) {
      angle = 2 * Math.PI - angle;
    }
    
    return this.convertAngle(angle, MeasurementUnit.RADIANS, this.settings.primaryAngleUnit);
  }

  private calculateBearing(start: Coordinate, end: Coordinate): number {
    const deltaX = (end[0] ?? 0) - (start[0] ?? 0);
    const deltaY = (end[1] ?? 0) - (start[1] ?? 0);
    let bearing = Math.atan2(deltaX, deltaY) * (180 / Math.PI);
    
    // Normalize to 0-360 degrees
    if (bearing < 0) {
      bearing += 360;
    }
    
    return bearing;
  }

  private calculateSegments(coordinates: Coordinate[]): Array<{
    length: number;
    bearing: number;
    start: Coordinate;
    end: Coordinate;
  }> {
    const segments = [];
    for (let i = 0; i < coordinates.length - 1; i++) {
      const start = coordinates[i];
      const end = coordinates[i + 1];
      if (start && end) {
        segments.push({
          length: getDistance(start, end),
          bearing: this.calculateBearing(start, end),
          start,
          end,
        });
      }
    }
    return segments;
  }

  private formatCoordinates(coordinate: Coordinate): string {
    const [lon, lat] = toLonLat(coordinate);
    const safelat = lat ?? 0;
    const safeLon = lon ?? 0;
    
    switch (this.settings.coordinateFormat) {
      case 'decimal':
        return `${safelat.toFixed(6)}, ${safeLon.toFixed(6)}`;
      
      case 'dms':
        return this.toDMS(safelat, safeLon);
      
      case 'utm':
        return this.toUTM(coordinate);
      
      case 'state_plane':
        return this.toStatePlane(coordinate);
      
      default:
        return `${safelat.toFixed(6)}, ${safeLon.toFixed(6)}`;
    }
  }

  private toDMS(lat: number, lon: number): string {
    const formatDMS = (decimal: number, isLongitude: boolean): string => {
      const absolute = Math.abs(decimal);
      const degrees = Math.floor(absolute);
      const minutes = Math.floor((absolute - degrees) * 60);
      const seconds = ((absolute - degrees) * 60 - minutes) * 60;
      
      const direction = isLongitude 
        ? (decimal >= 0 ? 'E' : 'W')
        : (decimal >= 0 ? 'N' : 'S');
      
      return `${degrees}°${minutes}'${seconds.toFixed(2)}"${direction}`;
    };

    return `${formatDMS(lat, false)}, ${formatDMS(lon, true)}`;
  }

  private toUTM(coordinate: Coordinate): string {
    // Simplified UTM conversion - in production, use a proper projection library
    const x = coordinate[0] ?? 0;
    const y = coordinate[1] ?? 0;
    return `UTM: ${x.toFixed(2)}, ${y.toFixed(2)}`;
  }

  private toStatePlane(coordinate: Coordinate): string {
    // Simplified State Plane conversion - in production, use proper projection
    const x = coordinate[0] ?? 0;
    const y = coordinate[1] ?? 0;
    return `State Plane: ${x.toFixed(2)}, ${y.toFixed(2)}`;
  }

  private createAnnotation(measurement: MeasurementResult): AnnotationData {
    const centroid = this.calculateCentroid(measurement.coordinates);
    const text = this.formatMeasurementText(measurement);

    return {
      text,
      position: centroid,
      style: {
        fontSize: this.settings.annotationStyle.fontSize,
        fontFamily: this.settings.annotationStyle.fontFamily,
        color: this.settings.annotationStyle.color,
        backgroundColor: this.settings.annotationStyle.backgroundColor,
        borderColor: this.settings.annotationStyle.borderColor,
        padding: 4,
      },
    };
  }

  private formatMeasurementText(measurement: MeasurementResult): string {
    const value = measurement.value.toFixed(measurement.precision);
    const unit = this.getUnitSymbol(measurement.unit);

    switch (measurement.type) {
      case MeasurementType.DISTANCE:
        let text = `${value} ${unit}`;
        if (this.settings.showBearings && measurement.metadata.bearing) {
          text += `\nBearing: ${measurement.metadata.bearing.toFixed(1)}°`;
        }
        return text;

      case MeasurementType.AREA:
        let areaText = `Area: ${value} ${unit}`;
        if (measurement.metadata.perimeter) {
          const perimeterUnit = this.getUnitSymbol(this.settings.primaryLengthUnit);
          areaText += `\nPerimeter: ${measurement.metadata.perimeter.toFixed(measurement.precision)} ${perimeterUnit}`;
        }
        return areaText;

      case MeasurementType.ANGLE:
        return `${value}${unit}`;

      case MeasurementType.BEARING:
        let bearingText = `${value}°`;
        if ((measurement.metadata as any).distance) {
          const distUnit = this.getUnitSymbol(this.settings.primaryLengthUnit);
          bearingText += `\n${(measurement.metadata as any).distance.toFixed(measurement.precision)} ${distUnit}`;
        }
        return bearingText;

      case MeasurementType.COORDINATES:
        return (measurement.metadata as any).formattedCoordinates || `${value} ${unit}`;

      default:
        return `${value} ${unit}`;
    }
  }

  private calculateCentroid(coordinates: Coordinate[]): Coordinate {
    const x = coordinates.reduce((sum: number, coord: Coordinate) => sum + (coord[0] ?? 0), 0) / coordinates.length;
    const y = coordinates.reduce((sum: number, coord: Coordinate) => sum + (coord[1] ?? 0), 0) / coordinates.length;
    return [x, y];
  }

  private getPrimaryUnit(type: MeasurementType): MeasurementUnit {
    switch (type) {
      case MeasurementType.AREA:
        return this.settings.primaryAreaUnit;
      case MeasurementType.ANGLE:
        return this.settings.primaryAngleUnit;
      default:
        return this.settings.primaryLengthUnit;
    }
  }

  private convertToUnit(value: number, fromUnit: MeasurementUnit, toUnit: MeasurementUnit): number {
    if (fromUnit === toUnit) return value;

    // Length conversions (base: meters)
    const lengthConversions: Record<MeasurementUnit, number> = {
      [MeasurementUnit.METERS]: 1,
      [MeasurementUnit.FEET]: 0.3048,
      [MeasurementUnit.INCHES]: 0.0254,
      [MeasurementUnit.CENTIMETERS]: 0.01,
      [MeasurementUnit.YARDS]: 0.9144,
      [MeasurementUnit.KILOMETERS]: 1000,
      [MeasurementUnit.MILES]: 1609.344,
    } as any;

    // Area conversions (base: square meters)
    const areaConversions: Record<MeasurementUnit, number> = {
      [MeasurementUnit.SQUARE_METERS]: 1,
      [MeasurementUnit.SQUARE_FEET]: 0.092903,
      [MeasurementUnit.ACRES]: 4046.86,
      [MeasurementUnit.HECTARES]: 10000,
      [MeasurementUnit.SQUARE_MILES]: 2589988.11,
    } as any;

    if (lengthConversions[fromUnit] && lengthConversions[toUnit]) {
      return (value * lengthConversions[fromUnit]) / lengthConversions[toUnit];
    }

    if (areaConversions[fromUnit] && areaConversions[toUnit]) {
      return (value * areaConversions[fromUnit]) / areaConversions[toUnit];
    }

    return value; // No conversion available
  }

  private convertAngle(value: number, fromUnit: MeasurementUnit, toUnit: MeasurementUnit): number {
    if (fromUnit === toUnit) return value;

    // Convert to radians first
    let radians = value;
    if (fromUnit === MeasurementUnit.DEGREES) {
      radians = value * (Math.PI / 180);
    } else if (fromUnit === MeasurementUnit.GRADIANS) {
      radians = value * (Math.PI / 200);
    }

    // Convert from radians to target unit
    if (toUnit === MeasurementUnit.DEGREES) {
      return radians * (180 / Math.PI);
    } else if (toUnit === MeasurementUnit.GRADIANS) {
      return radians * (200 / Math.PI);
    }

    return radians;
  }

  private getUnitSymbol(unit: MeasurementUnit): string {
    const symbols: Record<MeasurementUnit, string> = {
      [MeasurementUnit.FEET]: 'ft',
      [MeasurementUnit.METERS]: 'm',
      [MeasurementUnit.INCHES]: 'in',
      [MeasurementUnit.CENTIMETERS]: 'cm',
      [MeasurementUnit.YARDS]: 'yd',
      [MeasurementUnit.KILOMETERS]: 'km',
      [MeasurementUnit.MILES]: 'mi',
      [MeasurementUnit.SQUARE_FEET]: 'ft²',
      [MeasurementUnit.SQUARE_METERS]: 'm²',
      [MeasurementUnit.ACRES]: 'ac',
      [MeasurementUnit.HECTARES]: 'ha',
      [MeasurementUnit.SQUARE_MILES]: 'mi²',
      [MeasurementUnit.DEGREES]: '°',
      [MeasurementUnit.RADIANS]: 'rad',
      [MeasurementUnit.GRADIANS]: 'gon',
      [MeasurementUnit.CUBIC_FEET]: 'ft³',
      [MeasurementUnit.CUBIC_METERS]: 'm³',
      [MeasurementUnit.CUBIC_YARDS]: 'yd³',
    };

    return symbols[unit] || unit;
  }

  private coordinatesEqual(coord1: Coordinate, coord2: Coordinate, tolerance = 1e-6): boolean {
    const x1 = coord1[0] ?? 0;
    const y1 = coord1[1] ?? 0;
    const x2 = coord2[0] ?? 0;
    const y2 = coord2[1] ?? 0;
    return Math.abs(x1 - x2) < tolerance && Math.abs(y1 - y2) < tolerance;
  }

  private generateId(): string {
    return `measurement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateMeasurementPreview(measurement: MeasurementResult): void {
    // Implementation would depend on the specific map library
    // This would update the visual preview of the measurement being created
  }

  private addMeasurementToMap(measurement: MeasurementResult): void {
    const feature = new Feature(measurement.geometry);
    feature.setId(measurement.id);
    feature.set('measurement', measurement);
    
    // Apply styling based on measurement type
    feature.setStyle(this.createMeasurementStyle(measurement));
    
    this.vectorSource.addFeature(feature);
  }

  private createMeasurementStyle(measurement: MeasurementResult): Style {
    const color = this.getMeasurementColor(measurement.type);
    
    return new Style({
      stroke: new Stroke({
        color,
        width: 2,
      }),
      fill: new Fill({
        color: color + '20', // 20% opacity
      }),
      text: measurement.annotation ? new Text({
        text: measurement.annotation.text,
        font: `${measurement.annotation.style.fontSize}px ${measurement.annotation.style.fontFamily}`,
        fill: new Fill({
          color: measurement.annotation.style.color,
        }),
        stroke: new Stroke({
          color: measurement.annotation.style.backgroundColor,
          width: 3,
        }),
        backgroundFill: new Fill({
          color: measurement.annotation.style.backgroundColor,
        }),
        padding: [2, 4, 2, 4],
      }) : undefined,
    });
  }

  private getMeasurementColor(type: MeasurementType): string {
    const colors: Record<MeasurementType, string> = {
      [MeasurementType.DISTANCE]: '#007bff',
      [MeasurementType.AREA]: '#28a745',
      [MeasurementType.PERIMETER]: '#ffc107',
      [MeasurementType.ANGLE]: '#dc3545',
      [MeasurementType.RADIUS]: '#6f42c1',
      [MeasurementType.ELEVATION]: '#fd7e14',
      [MeasurementType.VOLUME]: '#20c997',
      [MeasurementType.BEARING]: '#6c757d',
      [MeasurementType.COORDINATES]: '#e83e8c',
    };
    return colors[type] || '#007bff';
  }

  private updateAllMeasurements(): void {
    // Recalculate all measurements with new settings
    for (const measurement of this.measurements.values()) {
      const updated = this.createMeasurement(measurement.id, measurement.type, measurement.coordinates);
      if (updated) {
        this.measurements.set(measurement.id, updated);
        this.updateMeasurementOnMap(updated);
      }
    }
  }

  private updateMeasurementOnMap(measurement: MeasurementResult): void {
    const feature = this.vectorSource.getFeatureById(measurement.id);
    if (feature) {
      feature.set('measurement', measurement);
      feature.setStyle(this.createMeasurementStyle(measurement));
    }
  }

  // Public API methods
  getMeasurement(id: string): MeasurementResult | undefined {
    return this.measurements.get(id);
  }

  getAllMeasurements(): MeasurementResult[] {
    return Array.from(this.measurements.values());
  }

  deleteMeasurement(id: string): boolean {
    const deleted = this.measurements.delete(id);
    if (deleted) {
      const feature = this.vectorSource.getFeatureById(id);
      if (feature) {
        this.vectorSource.removeFeature(feature);
      }
    }
    return deleted;
  }

  clearAllMeasurements(): void {
    this.measurements.clear();
    this.vectorSource.clear();
  }

  exportMeasurements(format: 'json' | 'csv' | 'kml' = 'json'): string {
    const measurements = this.getAllMeasurements();
    
    switch (format) {
      case 'csv':
        return this.exportToCSV(measurements);
      case 'kml':
        return this.exportToKML(measurements);
      default:
        return JSON.stringify(measurements, null, 2);
    }
  }

  private exportToCSV(measurements: MeasurementResult[]): string {
    const headers = ['ID', 'Type', 'Value', 'Unit', 'Created At'];
    const rows = measurements.map(m => [
      m.id,
      m.type,
      m.value.toFixed(m.precision),
      m.unit,
      m.metadata.createdAt.toISOString()
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private exportToKML(measurements: MeasurementResult[]): string {
    // Simplified KML export - in production, use a proper KML library
    const kmlFeatures = measurements.map(m => {
      const coords = m.coordinates.map(c => c.join(',')).join(' ');
      return `<Placemark><name>${m.type}: ${m.value.toFixed(m.precision)} ${m.unit}</name><LineString><coordinates>${coords}</coordinates></LineString></Placemark>`;
    }).join('\n');
    
    return `<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>${kmlFeatures}</Document></kml>`;
  }
}

export default MeasurementManager;