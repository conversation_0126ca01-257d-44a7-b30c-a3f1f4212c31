'use client';

import { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import {
  Eye,
  EyeOff,
  Layers,
  Map,
  MapPin,
  Maximize2,
  Minimize2,
  ZoomIn,
  ZoomOut,
} from 'lucide-react';

interface UtilityLayer {
  id: string;
  name: string;
  type: string;
  visible: boolean;
  color: string;
}

interface MapViewerProps {
  projectId?: string;
  height?: string;
}

export function MapViewer({ projectId, height = '400px' }: MapViewerProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showLayers, setShowLayers] = useState(true);
  const [layers, setLayers] = useState<UtilityLayer[]>([
    { id: 'electric', name: 'Electric Lines', type: 'electric', visible: true, color: '#ef4444' },
    { id: 'gas', name: 'Gas Lines', type: 'gas', visible: true, color: '#f59e0b' },
    { id: 'water', name: 'Water Lines', type: 'water', visible: true, color: '#3b82f6' },
    { id: 'sewer', name: 'Sewer Lines', type: 'sewer', visible: true, color: '#8b5cf6' },
    { id: 'telecom', name: 'Telecom Lines', type: 'telecom', visible: true, color: '#10b981' },
  ]);

  useEffect(() => {
    // Initialize map here - for now we'll show a placeholder
    // In a real implementation, this would initialize OpenLayers or similar
    if (mapRef.current && !mapRef.current.hasChildNodes()) {
      const placeholder = document.createElement('div');
      placeholder.className =
        'w-full h-full bg-gray-100 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg';
      placeholder.innerHTML = `
        <div class="text-center">
          <div class="mx-auto h-12 w-12 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 01.553-.894L9 2l6 3 5.447-2.724A1 1 0 0121 3.382v10.764a1 1 0 01-.553.894L15 18l-6-3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Interactive Map</h3>
          <p class="text-sm text-gray-500">3D utility mapping and visualization will be implemented in Phase 4</p>
          ${projectId ? `<p class="text-xs text-gray-400 mt-2">Project ID: ${projectId}</p>` : ''}
        </div>
      `;
      mapRef.current.appendChild(placeholder);
    }
  }, [projectId]);

  const toggleLayer = (layerId: string) => {
    setLayers((prev) =>
      prev.map((layer: any) => (layer.id === layerId ? { ...layer, visible: !layer.visible } : layer))
    );
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    // In a real implementation, this would handle fullscreen API
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      <Card className="h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Map className="mr-2 h-5 w-5" />
                Utility Map
              </CardTitle>
              <CardDescription>
                Interactive 3D visualization of utility infrastructure
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => setShowLayers(!showLayers)}>
                <Layers className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={toggleFullscreen}>
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="relative">
            <div
              ref={mapRef}
              style={{ height: isFullscreen ? 'calc(100vh - 120px)' : height }}
              className="w-full"
            />

            {/* Map Controls */}
            <div className="absolute top-4 right-4 flex flex-col space-y-2">
              <Button variant="outline" size="sm">
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <MapPin className="h-4 w-4" />
              </Button>
            </div>

            {/* Layer Control Panel */}
            {showLayers && (
              <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 max-w-xs">
                <h4 className="font-medium mb-3">Utility Layers</h4>
                <div className="space-y-2">
                  {layers.map((layer: any) => (
                    <div key={layer.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: layer.color }} />
                        <span className="text-sm">{layer.name}</span>
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => toggleLayer(layer.id)}>
                        {layer.visible ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <EyeOff className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-3 border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span>Visible Layers:</span>
                    <Badge variant="outline">
                      {layers.filter((l: any) => l.visible).length}/{layers.length}
                    </Badge>
                  </div>
                </div>
              </div>
            )}

            {/* Status Bar */}
            <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4">
                  <span>Zoom: 1:5000</span>
                  <span>Coords: 39.7684° N, 86.1581° W</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">3D Ready</Badge>
                  <Badge variant="outline">Real-time</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
