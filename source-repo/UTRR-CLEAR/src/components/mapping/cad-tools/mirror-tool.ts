'use client';

import { Feature } from 'ol';
import { Geometry, LineString, Point, Polygon, Circle as CircleGeom } from 'ol/geom.js';
import VectorSource from 'ol/source/Vector.js';
import { getDistance } from 'ol/sphere.js';
import type { Coordinate } from 'ol/coordinate.js';

export interface MirrorToolOptions {
  source: VectorSource;
  keepOriginal?: boolean;
}

export interface MirrorAxis {
  start: Coordinate;
  end: Coordinate;
}

export class MirrorTool {
  private source: VectorSource;
  private keepOriginal: boolean;
  private mirrorAxis: MirrorAxis | null = null;
  private mirroredFeatures: Feature<Geometry>[] = [];

  constructor(options: MirrorToolOptions) {
    this.source = options.source;
    this.keepOriginal = options.keepOriginal ?? true;
  }

  /**
   * Set the mirror axis
   */
  setMirrorAxis(start: Coordinate, end: Coordinate): void {
    this.mirrorAxis = { start, end };
  }

  /**
   * Mirror a single feature across the defined axis
   */
  mirrorFeature(feature: Feature<Geometry>): Feature<Geometry> | null {
    if (!this.mirrorAxis) {
      throw new Error('Mirror axis not defined');
    }

    const geometry = feature.getGeometry();
    if (!geometry) {
      return null;
    }

    const mirroredGeometry = this.mirrorGeometry(geometry);
    if (!mirroredGeometry) {
      return null;
    }

    const mirroredFeature = feature.clone();
    mirroredFeature.setGeometry(mirroredGeometry);
    
    // Copy properties but mark as mirrored
    mirroredFeature.setProperties({
      ...feature.getProperties(),
      mirrored: true,
      originalId: feature.getId() || (feature as any).ol_uid
    });

    this.mirroredFeatures.push(mirroredFeature);
    return mirroredFeature;
  }

  /**
   * Mirror multiple features
   */
  mirrorFeatures(features: Feature<Geometry>[]): Feature<Geometry>[] {
    const mirrored: Feature<Geometry>[] = [];
    
    features.forEach((feature) => {
      const mirroredFeature = this.mirrorFeature(feature);
      if (mirroredFeature) {
        mirrored.push(mirroredFeature);
      }
    });

    return mirrored;
  }

  /**
   * Mirror a geometry across the axis
   */
  private mirrorGeometry(geometry: Geometry): Geometry | null {
    if (!this.mirrorAxis) {
      return null;
    }

    if (geometry instanceof Point) {
      return this.mirrorPoint(geometry);
    } else if (geometry instanceof LineString) {
      return this.mirrorLineString(geometry);
    } else if (geometry instanceof Polygon) {
      return this.mirrorPolygon(geometry);
    } else if (geometry instanceof CircleGeom) {
      return this.mirrorCircle(geometry);
    }

    return null;
  }

  /**
   * Mirror a point geometry
   */
  private mirrorPoint(point: Point): Point {
    const coord = point.getCoordinates();
    const mirroredCoord = this.mirrorCoordinate(coord);
    return new Point(mirroredCoord);
  }

  /**
   * Mirror a line string geometry
   */
  private mirrorLineString(line: LineString): LineString {
    const coords = line.getCoordinates();
    const mirroredCoords = coords.map(coord => this.mirrorCoordinate(coord));
    return new LineString(mirroredCoords);
  }

  /**
   * Mirror a polygon geometry
   */
  private mirrorPolygon(polygon: Polygon): Polygon {
    const rings = polygon.getLinearRings();
    const mirroredRings = rings.map(ring => {
      const coords = ring.getCoordinates();
      return coords.map(coord => this.mirrorCoordinate(coord));
    });
    
    // Reverse the order of vertices to maintain correct winding
    mirroredRings.forEach(ring => ring.reverse());
    
    return new Polygon(mirroredRings);
  }

  /**
   * Mirror a circle geometry
   */
  private mirrorCircle(circle: CircleGeom): CircleGeom {
    const center = circle.getCenter();
    const radius = circle.getRadius();
    const mirroredCenter = this.mirrorCoordinate(center);
    return new CircleGeom(mirroredCenter, radius);
  }

  /**
   * Mirror a single coordinate across the axis
   */
  private mirrorCoordinate(coord: Coordinate): Coordinate {
    if (!this.mirrorAxis) {
      return coord;
    }

    const { start, end } = this.mirrorAxis;
    
    // Calculate axis direction vector
    const axisX = (end[0] ?? 0) - (start[0] ?? 0);
    const axisY = (end[1] ?? 0) - (start[1] ?? 0);
    const axisLength = Math.sqrt(axisX * axisX + axisY * axisY);
    
    if (axisLength === 0) {
      return coord;
    }

    // Normalize axis vector
    const axisNormX = axisX / axisLength;
    const axisNormY = axisY / axisLength;

    // Vector from axis start to point
    const toPointX = (coord[0] ?? 0) - (start[0] ?? 0);
    const toPointY = (coord[1] ?? 0) - (start[1] ?? 0);

    // Project point onto axis
    const projection = toPointX * axisNormX + toPointY * axisNormY;
    const projectedX = (start[0] ?? 0) + projection * axisNormX;
    const projectedY = (start[1] ?? 0) + projection * axisNormY;

    // Mirror point across axis
    const mirroredX = 2 * projectedX - (coord[0] ?? 0);
    const mirroredY = 2 * projectedY - (coord[1] ?? 0);

    return [mirroredX, mirroredY];
  }

  /**
   * Apply mirroring to selected features and add to source
   */
  applyMirror(features: Feature<Geometry>[]): Feature<Geometry>[] {
    const mirroredFeatures = this.mirrorFeatures(features);
    
    // Add mirrored features to source
    mirroredFeatures.forEach(feature => {
      this.source.addFeature(feature);
    });

    // Remove originals if not keeping them
    if (!this.keepOriginal) {
      features.forEach(feature => {
        this.source.removeFeature(feature);
      });
    }

    return mirroredFeatures;
  }

  /**
   * Preview mirror result without adding to source
   */
  previewMirror(features: Feature<Geometry>[]): Feature<Geometry>[] {
    return this.mirrorFeatures(features);
  }

  /**
   * Get the current mirror axis
   */
  getMirrorAxis(): MirrorAxis | null {
    return this.mirrorAxis;
  }

  /**
   * Clear the mirror axis
   */
  clearMirrorAxis(): void {
    this.mirrorAxis = null;
  }

  /**
   * Set whether to keep original features
   */
  setKeepOriginal(keep: boolean): void {
    this.keepOriginal = keep;
  }

  /**
   * Get the last mirrored features for undo
   */
  getLastMirrored(): Feature<Geometry>[] {
    return this.mirroredFeatures;
  }

  /**
   * Clear mirror history
   */
  clearHistory(): void {
    this.mirroredFeatures = [];
  }

  /**
   * Create a visual representation of the mirror axis
   */
  createAxisFeature(): Feature<LineString> | null {
    if (!this.mirrorAxis) {
      return null;
    }

    const axisFeature = new Feature({
      geometry: new LineString([this.mirrorAxis.start, this.mirrorAxis.end])
    });

    axisFeature.setProperties({
      type: 'mirror-axis',
      temporary: true
    });

    return axisFeature;
  }
}

export default MirrorTool;