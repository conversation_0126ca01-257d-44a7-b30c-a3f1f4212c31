'use client';

import { Map as OLMap } from 'ol';
import VectorSource from 'ol/source/Vector.js';
import { Feature } from 'ol';
import { Geometry } from 'ol/geom.js';
import { CadCommand } from '~/hooks/use-cad-shortcuts';

import { TrimTool } from './trim-tool';
import { ExtendTool } from './extend-tool';
import { MirrorTool } from './mirror-tool';
import { ScaleTool } from './scale-tool';

export interface CadToolManagerOptions {
  map: OLMap;
  source: VectorSource;
  onToolActivated?: (tool: CadCommand) => void;
  onToolDeactivated?: (tool: CadCommand) => void;
  onFeatureModified?: (features: Feature<Geometry>[]) => void;
}

export class CadToolManager {
  private map: OLMap;
  private source: VectorSource;
  private activeTool: CadCommand | null = null;
  
  // Tool instances
  private trimTool: TrimTool;
  private extendTool: ExtendTool;
  private mirrorTool: MirrorTool;
  private scaleTool: ScaleTool;
  
  // Callbacks
  private onToolActivated?: (tool: CadCommand) => void;
  private onToolDeactivated?: (tool: CadCommand) => void;
  private onFeatureModified?: (features: Feature<Geometry>[]) => void;
  
  // Tool state
  private selectedFeatures: Feature<Geometry>[] = [];
  private toolState: Map<string, any> = new Map();

  constructor(options: CadToolManagerOptions) {
    this.map = options.map;
    this.source = options.source;
    this.onToolActivated = options.onToolActivated;
    this.onToolDeactivated = options.onToolDeactivated;
    this.onFeatureModified = options.onFeatureModified;

    // Initialize tools
    this.trimTool = new TrimTool({ source: this.source });
    this.extendTool = new ExtendTool({ source: this.source });
    this.mirrorTool = new MirrorTool({ source: this.source });
    this.scaleTool = new ScaleTool({ source: this.source });
  }

  /**
   * Activate a CAD tool
   */
  activateTool(tool: CadCommand): void {
    // Deactivate current tool if different
    if (this.activeTool && this.activeTool !== tool) {
      this.deactivateTool();
    }

    this.activeTool = tool;
    this.onToolActivated?.(tool);

    // Initialize tool-specific state
    switch (tool) {
      case CadCommand.TRIM:
        this.initializeTrimTool();
        break;
      case CadCommand.EXTEND:
        this.initializeExtendTool();
        break;
      case CadCommand.MIRROR:
        this.initializeMirrorTool();
        break;
      case CadCommand.SCALE:
        this.initializeScaleTool();
        break;
    }
  }

  /**
   * Deactivate the current tool
   */
  deactivateTool(): void {
    if (!this.activeTool) return;

    // Clean up tool-specific state
    switch (this.activeTool) {
      case CadCommand.TRIM:
        this.trimTool.clearBoundary();
        this.trimTool.clearHistory();
        break;
      case CadCommand.EXTEND:
        this.extendTool.clearBoundary();
        this.extendTool.clearHistory();
        break;
      case CadCommand.MIRROR:
        this.mirrorTool.clearMirrorAxis();
        this.mirrorTool.clearHistory();
        break;
      case CadCommand.SCALE:
        this.scaleTool.clearHistory();
        break;
    }

    this.onToolDeactivated?.(this.activeTool);
    this.activeTool = null;
    this.selectedFeatures = [];
    this.toolState.clear();
  }

  /**
   * Handle feature selection for active tool
   */
  selectFeatures(features: Feature<Geometry>[]): void {
    this.selectedFeatures = features;
  }

  /**
   * Initialize trim tool
   */
  private initializeTrimTool(): void {
    this.toolState.set(CadCommand.TRIM, {
      mode: 'select-boundary', // 'select-boundary' | 'trim-features'
      boundary: null
    });
  }

  /**
   * Handle trim tool interaction
   */
  handleTrimClick(coordinate: number[], pixelCoordinate?: number[]): void {
    const state = this.toolState.get(CadCommand.TRIM);
    if (!state) return;

    if (state.mode === 'select-boundary') {
      const boundary = this.trimTool.selectBoundary(coordinate);
      if (boundary) {
        state.boundary = boundary;
        state.mode = 'trim-features';
        this.toolState.set(CadCommand.TRIM, state);
      }
    } else if (state.mode === 'trim-features' && this.selectedFeatures.length > 0) {
      const trimmedFeatures: Feature<Geometry>[] = [];
      
      this.selectedFeatures.forEach(feature => {
        const trimmed = this.trimTool.trimFeature(feature, coordinate);
        trimmedFeatures.push(...trimmed);
        
        // Remove original and add trimmed
        this.source.removeFeature(feature);
        trimmed.forEach(f => this.source.addFeature(f));
      });

      this.onFeatureModified?.(trimmedFeatures);
      
      // Reset to boundary selection
      state.mode = 'select-boundary';
      this.toolState.set(CadCommand.TRIM, state);
    }
  }

  /**
   * Initialize extend tool
   */
  private initializeExtendTool(): void {
    this.toolState.set(CadCommand.EXTEND, {
      mode: 'select-boundary', // 'select-boundary' | 'extend-features'
      boundary: null
    });
  }

  /**
   * Handle extend tool interaction
   */
  handleExtendClick(coordinate: number[], pixelCoordinate?: number[]): void {
    const state = this.toolState.get(CadCommand.EXTEND);
    if (!state) return;

    if (state.mode === 'select-boundary') {
      const boundary = this.extendTool.selectBoundary(coordinate);
      if (boundary) {
        state.boundary = boundary;
        state.mode = 'extend-features';
        this.toolState.set(CadCommand.EXTEND, state);
      }
    } else if (state.mode === 'extend-features' && this.selectedFeatures.length > 0) {
      const extendedFeatures: Feature<Geometry>[] = [];
      
      this.selectedFeatures.forEach(feature => {
        // Determine which end to extend based on click position
        const geometry = feature.getGeometry();
        if (geometry && 'getCoordinates' in geometry) {
          const coords = (geometry as any).getCoordinates();
          const startDist = this.getDistance(coordinate, coords[0]);
          const endDist = this.getDistance(coordinate, coords[coords.length - 1]);
          const fromEnd = endDist < startDist;
          
          const extended = this.extendTool.extendFeature(feature, fromEnd);
          if (extended) {
            extendedFeatures.push(extended);
            
            // Remove original and add extended
            this.source.removeFeature(feature);
            this.source.addFeature(extended);
          }
        }
      });

      this.onFeatureModified?.(extendedFeatures);
      
      // Reset to boundary selection
      state.mode = 'select-boundary';
      this.toolState.set(CadCommand.EXTEND, state);
    }
  }

  /**
   * Initialize mirror tool
   */
  private initializeMirrorTool(): void {
    this.toolState.set(CadCommand.MIRROR, {
      mode: 'select-axis-start', // 'select-axis-start' | 'select-axis-end' | 'mirror-features'
      axisStart: null,
      axisEnd: null
    });
  }

  /**
   * Handle mirror tool interaction
   */
  handleMirrorClick(coordinate: number[], pixelCoordinate?: number[]): void {
    const state = this.toolState.get(CadCommand.MIRROR);
    if (!state) return;

    if (state.mode === 'select-axis-start') {
      state.axisStart = coordinate;
      state.mode = 'select-axis-end';
      this.toolState.set(CadCommand.MIRROR, state);
    } else if (state.mode === 'select-axis-end') {
      state.axisEnd = coordinate;
      this.mirrorTool.setMirrorAxis(state.axisStart, state.axisEnd);
      state.mode = 'mirror-features';
      this.toolState.set(CadCommand.MIRROR, state);
      
      // Create visual axis line
      const axisFeature = this.mirrorTool.createAxisFeature();
      if (axisFeature) {
        this.source.addFeature(axisFeature);
      }
    } else if (state.mode === 'mirror-features' && this.selectedFeatures.length > 0) {
      const mirroredFeatures = this.mirrorTool.applyMirror(this.selectedFeatures);
      
      this.onFeatureModified?.(mirroredFeatures);
      
      // Reset to axis selection
      state.mode = 'select-axis-start';
      state.axisStart = null;
      state.axisEnd = null;
      this.toolState.set(CadCommand.MIRROR, state);
      
      // Remove axis visualization
      const axisFeatures = this.source.getFeatures().filter(f => 
        f.get('type') === 'mirror-axis'
      );
      axisFeatures.forEach(f => this.source.removeFeature(f));
    }
  }

  /**
   * Initialize scale tool
   */
  private initializeScaleTool(): void {
    this.toolState.set(CadCommand.SCALE, {
      mode: 'select-base-point', // 'select-base-point' | 'select-reference' | 'select-scale'
      basePoint: null,
      referencePoint: null,
      scaleHandles: []
    });
  }

  /**
   * Handle scale tool interaction
   */
  handleScaleClick(coordinate: number[], pixelCoordinate?: number[]): void {
    const state = this.toolState.get(CadCommand.SCALE);
    if (!state) return;

    if (state.mode === 'select-base-point') {
      state.basePoint = coordinate;
      state.mode = 'select-reference';
      this.toolState.set(CadCommand.SCALE, state);
      
      // Create scale handles
      if (this.selectedFeatures.length > 0) {
        const handles = this.scaleTool.createScaleHandles(this.selectedFeatures);
        handles.forEach(h => this.source.addFeature(h));
        state.scaleHandles = handles;
      }
    } else if (state.mode === 'select-reference') {
      state.referencePoint = coordinate;
      state.mode = 'select-scale';
      this.toolState.set(CadCommand.SCALE, state);
    } else if (state.mode === 'select-scale' && this.selectedFeatures.length > 0) {
      const scaledFeatures = this.scaleTool.scaleByReference(
        this.selectedFeatures,
        state.basePoint,
        state.referencePoint,
        coordinate
      );
      
      // Apply scaling
      this.selectedFeatures.forEach((feature, index) => {
        this.source.removeFeature(feature);
        if (scaledFeatures[index]) {
          this.source.addFeature(scaledFeatures[index]);
        }
      });
      
      this.onFeatureModified?.(scaledFeatures);
      
      // Clean up handles
      state.scaleHandles.forEach((h: Feature) => this.source.removeFeature(h));
      
      // Reset
      state.mode = 'select-base-point';
      state.basePoint = null;
      state.referencePoint = null;
      state.scaleHandles = [];
      this.toolState.set(CadCommand.SCALE, state);
    }
  }

  /**
   * Handle click events based on active tool
   */
  handleClick(coordinate: number[], pixelCoordinate?: number[]): void {
    if (!this.activeTool) return;

    switch (this.activeTool) {
      case CadCommand.TRIM:
        this.handleTrimClick(coordinate, pixelCoordinate);
        break;
      case CadCommand.EXTEND:
        this.handleExtendClick(coordinate, pixelCoordinate);
        break;
      case CadCommand.MIRROR:
        this.handleMirrorClick(coordinate, pixelCoordinate);
        break;
      case CadCommand.SCALE:
        this.handleScaleClick(coordinate, pixelCoordinate);
        break;
    }
  }

  /**
   * Get current tool state
   */
  getToolState(): any {
    if (!this.activeTool) return null;
    return this.toolState.get(this.activeTool);
  }

  /**
   * Get active tool
   */
  getActiveTool(): CadCommand | null {
    return this.activeTool;
  }

  /**
   * Calculate distance between two points
   */
  private getDistance(p1: number[], p2: number[]): number {
    const dx = (p2[0] ?? 0) - (p1[0] ?? 0);
    const dy = (p2[1] ?? 0) - (p1[1] ?? 0);
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Undo last operation
   */
  undo(): void {
    if (!this.activeTool) return;

    switch (this.activeTool) {
      case CadCommand.TRIM:
        const trimmed = this.trimTool.getLastTrimmed();
        // Implement undo logic for trim
        break;
      case CadCommand.EXTEND:
        const extended = this.extendTool.getLastExtended();
        // Implement undo logic for extend
        break;
      case CadCommand.MIRROR:
        const mirrored = this.mirrorTool.getLastMirrored();
        // Implement undo logic for mirror
        break;
      case CadCommand.SCALE:
        const scaled = this.scaleTool.getLastScaled();
        this.scaleTool.resetToOriginal(scaled);
        break;
    }
  }
}

export default CadToolManager;