'use client';

import { Feature } from 'ol';
import { Geometry, LineString, Point, Polygon, Circle as CircleGeom, LinearRing } from 'ol/geom.js';
import VectorSource from 'ol/source/Vector.js';
import type { Coordinate } from 'ol/coordinate.js';
import { getCenter } from 'ol/extent.js';

export interface ScaleToolOptions {
  source: VectorSource;
  uniformScale?: boolean;
  minScale?: number;
  maxScale?: number;
}

export interface ScaleParameters {
  scaleX: number;
  scaleY: number;
  origin: Coordinate;
}

export class ScaleTool {
  private source: VectorSource;
  private uniformScale: boolean;
  private minScale: number;
  private maxScale: number;
  private scaledFeatures: Feature<Geometry>[] = [];
  private originalFeatures: Map<string, Feature<Geometry>> = new Map();

  constructor(options: ScaleToolOptions) {
    this.source = options.source;
    this.uniformScale = options.uniformScale ?? true;
    this.minScale = options.minScale ?? 0.1;
    this.maxScale = options.maxScale ?? 10;
  }

  /**
   * Scale a single feature
   */
  scaleFeature(
    feature: Feature<Geometry>,
    scaleParams: ScaleParameters
  ): Feature<Geometry> | null {
    const geometry = feature.getGeometry();
    if (!geometry) {
      return null;
    }

    // Store original for undo
    const featureId = feature.getId() || (feature as any).ol_uid || `scale_${Date.now()}`;
    if (!this.originalFeatures.has(featureId as string)) {
      this.originalFeatures.set(featureId as string, feature.clone());
    }

    const scaledGeometry = this.scaleGeometry(geometry, scaleParams);
    if (!scaledGeometry) {
      return null;
    }

    const scaledFeature = feature.clone();
    scaledFeature.setGeometry(scaledGeometry);
    
    // Update properties
    scaledFeature.setProperties({
      ...feature.getProperties(),
      scaled: true,
      scaleX: scaleParams.scaleX,
      scaleY: scaleParams.scaleY,
      scaleOrigin: scaleParams.origin
    });

    this.scaledFeatures.push(scaledFeature);
    return scaledFeature;
  }

  /**
   * Scale multiple features
   */
  scaleFeatures(
    features: Feature<Geometry>[],
    scaleParams: ScaleParameters
  ): Feature<Geometry>[] {
    const scaled: Feature<Geometry>[] = [];
    
    features.forEach((feature) => {
      const scaledFeature = this.scaleFeature(feature, scaleParams);
      if (scaledFeature) {
        scaled.push(scaledFeature);
      }
    });

    return scaled;
  }

  /**
   * Scale features with a reference base point and scale point
   */
  scaleByReference(
    features: Feature<Geometry>[],
    basePoint: Coordinate,
    fromPoint: Coordinate,
    toPoint: Coordinate
  ): Feature<Geometry>[] {
    // Calculate scale factor from reference points
    const fromDistance = this.getDistance(basePoint, fromPoint);
    const toDistance = this.getDistance(basePoint, toPoint);
    
    if (fromDistance === 0) {
      return [];
    }

    const scaleFactor = toDistance / fromDistance;
    
    // Apply constraints
    const constrainedScale = Math.max(this.minScale, Math.min(this.maxScale, scaleFactor));
    
    const scaleParams: ScaleParameters = {
      scaleX: constrainedScale,
      scaleY: this.uniformScale ? constrainedScale : constrainedScale,
      origin: basePoint
    };

    return this.scaleFeatures(features, scaleParams);
  }

  /**
   * Scale a geometry
   */
  private scaleGeometry(
    geometry: Geometry,
    scaleParams: ScaleParameters
  ): Geometry | null {
    if (geometry instanceof Point) {
      return this.scalePoint(geometry, scaleParams);
    } else if (geometry instanceof LineString) {
      return this.scaleLineString(geometry, scaleParams);
    } else if (geometry instanceof Polygon) {
      return this.scalePolygon(geometry, scaleParams);
    } else if (geometry instanceof CircleGeom) {
      return this.scaleCircle(geometry, scaleParams);
    }

    return null;
  }

  /**
   * Scale a point geometry
   */
  private scalePoint(point: Point, scaleParams: ScaleParameters): Point {
    const coord = point.getCoordinates();
    const scaledCoord = this.scaleCoordinate(coord, scaleParams);
    return new Point(scaledCoord);
  }

  /**
   * Scale a line string geometry
   */
  private scaleLineString(line: LineString, scaleParams: ScaleParameters): LineString {
    const coords = line.getCoordinates();
    const scaledCoords = coords.map(coord => this.scaleCoordinate(coord, scaleParams));
    return new LineString(scaledCoords);
  }

  /**
   * Scale a polygon geometry
   */
  private scalePolygon(polygon: Polygon, scaleParams: ScaleParameters): Polygon {
    const rings = polygon.getLinearRings();
    const scaledRings = rings.map(ring => {
      const coords = ring.getCoordinates();
      const scaledCoords = coords.map(coord => this.scaleCoordinate(coord, scaleParams));
      return scaledCoords;
    });
    
    return new Polygon(scaledRings);
  }

  /**
   * Scale a circle geometry
   */
  private scaleCircle(circle: CircleGeom, scaleParams: ScaleParameters): CircleGeom {
    const center = circle.getCenter();
    const radius = circle.getRadius();
    
    const scaledCenter = this.scaleCoordinate(center, scaleParams);
    
    // For circles, use average of X and Y scale for radius
    const avgScale = (scaleParams.scaleX + scaleParams.scaleY) / 2;
    const scaledRadius = radius * avgScale;
    
    return new CircleGeom(scaledCenter, scaledRadius);
  }

  /**
   * Scale a single coordinate
   */
  private scaleCoordinate(
    coord: Coordinate,
    scaleParams: ScaleParameters
  ): Coordinate {
    const { scaleX, scaleY, origin } = scaleParams;
    
    // Translate to origin
    const translatedX = (coord[0] ?? 0) - (origin[0] ?? 0);
    const translatedY = (coord[1] ?? 0) - (origin[1] ?? 0);
    
    // Apply scale
    const scaledX = translatedX * scaleX;
    const scaledY = translatedY * scaleY;
    
    // Translate back
    return [
      scaledX + (origin[0] ?? 0),
      scaledY + (origin[1] ?? 0)
    ];
  }

  /**
   * Calculate distance between two points
   */
  private getDistance(p1: Coordinate, p2: Coordinate): number {
    const dx = (p2[0] ?? 0) - (p1[0] ?? 0);
    const dy = (p2[1] ?? 0) - (p1[1] ?? 0);
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Get the center point of multiple features
   */
  getFeaturesCenter(features: Feature<Geometry>[]): Coordinate {
    if (features.length === 0) {
      return [0, 0];
    }

    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;

    features.forEach(feature => {
      const geometry = feature.getGeometry();
      if (geometry) {
        const extent = geometry.getExtent();
        minX = Math.min(minX, extent[0] ?? 0);
        minY = Math.min(minY, extent[1] ?? 0);
        maxX = Math.max(maxX, extent[2] ?? 0);
        maxY = Math.max(maxY, extent[3] ?? 0);
      }
    });

    return [(minX + maxX) / 2, (minY + maxY) / 2];
  }

  /**
   * Apply scaling to features and update source
   */
  applyScale(
    features: Feature<Geometry>[],
    scaleParams: ScaleParameters
  ): Feature<Geometry>[] {
    const scaledFeatures = this.scaleFeatures(features, scaleParams);
    
    // Remove original features and add scaled ones
    features.forEach((feature, index) => {
      this.source.removeFeature(feature);
      if (scaledFeatures[index]) {
        this.source.addFeature(scaledFeatures[index]);
      }
    });

    return scaledFeatures;
  }

  /**
   * Preview scale result without modifying source
   */
  previewScale(
    features: Feature<Geometry>[],
    scaleParams: ScaleParameters
  ): Feature<Geometry>[] {
    return this.scaleFeatures(features, scaleParams);
  }

  /**
   * Set uniform scale mode
   */
  setUniformScale(uniform: boolean): void {
    this.uniformScale = uniform;
  }

  /**
   * Set scale constraints
   */
  setScaleConstraints(min: number, max: number): void {
    this.minScale = Math.max(0.01, min);
    this.maxScale = Math.min(100, max);
  }

  /**
   * Reset features to original size
   */
  resetToOriginal(features: Feature<Geometry>[]): void {
    features.forEach(feature => {
      const featureId = feature.getId() || (feature as any).ol_uid;
      if (featureId && this.originalFeatures.has(featureId as string)) {
        const original = this.originalFeatures.get(featureId as string)!;
        const originalGeometry = original.getGeometry();
        if (originalGeometry) {
          feature.setGeometry(originalGeometry.clone());
        }
      }
    });
  }

  /**
   * Get the last scaled features for undo
   */
  getLastScaled(): Feature<Geometry>[] {
    return this.scaledFeatures;
  }

  /**
   * Clear scale history
   */
  clearHistory(): void {
    this.scaledFeatures = [];
    this.originalFeatures.clear();
  }

  /**
   * Create scale handle features for interactive scaling
   */
  createScaleHandles(features: Feature<Geometry>[]): Feature<Point>[] {
    const handles: Feature<Point>[] = [];
    
    if (features.length === 0) {
      return handles;
    }

    // Get bounding box of all features
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;

    features.forEach(feature => {
      const geometry = feature.getGeometry();
      if (geometry) {
        const extent = geometry.getExtent();
        minX = Math.min(minX, extent[0] ?? 0);
        minY = Math.min(minY, extent[1] ?? 0);
        maxX = Math.max(maxX, extent[2] ?? 0);
        maxY = Math.max(maxY, extent[3] ?? 0);
      }
    });

    // Create handles at corners and midpoints
    const handlePositions: Coordinate[] = [
      [minX, minY], // Bottom-left
      [maxX, minY], // Bottom-right
      [maxX, maxY], // Top-right
      [minX, maxY], // Top-left
      [(minX + maxX) / 2, minY], // Bottom-center
      [maxX, (minY + maxY) / 2], // Right-center
      [(minX + maxX) / 2, maxY], // Top-center
      [minX, (minY + maxY) / 2], // Left-center
    ];

    handlePositions.forEach((pos, index) => {
      const handle = new Feature({
        geometry: new Point(pos)
      });
      
      handle.setProperties({
        type: 'scale-handle',
        handleIndex: index,
        temporary: true
      });
      
      handles.push(handle);
    });

    return handles;
  }
}

export default ScaleTool;