'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { 
  Info, FileText, Scissors, Maximize2, FlipHorizontal, 
  Scaling, MousePointer, CheckCircle 
} from 'lucide-react';

// OpenLayers imports
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import { Tile as TileLayer } from 'ol/layer.js';
import { OSM } from 'ol/source.js';
import { Feature } from 'ol';
import { LineString, Point } from 'ol/geom.js';
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style.js';
import { Draw, Select, Modify } from 'ol/interaction.js';
import { click } from 'ol/events/condition.js';

// CAD Tools
import { CadToolManager } from './cad-tool-manager';
import { CadCommand } from '~/hooks/use-cad-shortcuts';

interface CadToolsDemoProps {
  className?: string;
}

export const CadToolsDemo: React.FC<CadToolsDemoProps> = ({ className = '' }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<Map | null>(null);
  const [vectorSource, setVectorSource] = useState<VectorSource | null>(null);
  const [toolManager, setToolManager] = useState<CadToolManager | null>(null);
  const [activeTool, setActiveTool] = useState<CadCommand | null>(null);
  const [selectedFeatures, setSelectedFeatures] = useState<Feature[]>([]);
  const [toolStatus, setToolStatus] = useState<string>('Select a tool to begin');

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    const source = new VectorSource();
    const vectorLayer = new VectorLayer({
      source: source,
      style: new Style({
        stroke: new Stroke({
          color: '#2563eb',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(37, 99, 235, 0.1)'
        }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({ color: '#2563eb' }),
          stroke: new Stroke({ color: '#1e40af', width: 2 })
        })
      })
    });

    const mapInstance = new Map({
      target: mapRef.current,
      layers: [
        new TileLayer({
          source: new OSM()
        }),
        vectorLayer
      ],
      view: new View({
        center: [0, 0],
        zoom: 16
      })
    });

    // Add some demo features
    createDemoFeatures(source);

    // Initialize tool manager
    const manager = new CadToolManager({
      map: mapInstance,
      source: source,
      onToolActivated: (tool) => {
        setActiveTool(tool);
        updateToolStatus(tool, 'activated');
      },
      onToolDeactivated: (tool) => {
        setActiveTool(null);
        setToolStatus('Tool deactivated');
      },
      onFeatureModified: (features) => {
        console.log('Features modified:', features);
      }
    });

    // Add select interaction
    const select = new Select({
      condition: click,
      style: new Style({
        stroke: new Stroke({
          color: '#dc2626',
          width: 3
        }),
        fill: new Fill({
          color: 'rgba(220, 38, 38, 0.2)'
        }),
        image: new CircleStyle({
          radius: 6,
          fill: new Fill({ color: '#dc2626' }),
          stroke: new Stroke({ color: '#991b1b', width: 2 })
        })
      })
    });

    select.on('select', (e) => {
      const features = e.selected;
      setSelectedFeatures(features);
      manager.selectFeatures(features);
    });

    mapInstance.addInteraction(select);

    // Handle map clicks for tools
    mapInstance.on('click', (e) => {
      if (manager.getActiveTool()) {
        manager.handleClick(e.coordinate, e.pixel);
      }
    });

    setMap(mapInstance);
    setVectorSource(source);
    setToolManager(manager);

    return () => {
      mapInstance.setTarget(undefined);
    };
  }, []);

  // Create demo features
  const createDemoFeatures = (source: VectorSource) => {
    // Add some lines
    const line1 = new Feature({
      geometry: new LineString([
        [-100, 0],
        [100, 0]
      ]),
      name: 'Horizontal Line'
    });

    const line2 = new Feature({
      geometry: new LineString([
        [0, -100],
        [0, 100]
      ]),
      name: 'Vertical Line'
    });

    const line3 = new Feature({
      geometry: new LineString([
        [-50, -50],
        [50, 50]
      ]),
      name: 'Diagonal Line'
    });

    const line4 = new Feature({
      geometry: new LineString([
        [-80, 30],
        [-30, 80]
      ]),
      name: 'Short Line'
    });

    source.addFeatures([line1, line2, line3, line4]);
  };

  // Update tool status
  const updateToolStatus = (tool: CadCommand, action: string) => {
    const messages: Partial<Record<CadCommand, string>> = {
      [CadCommand.TRIM]: 'Click to select cutting edge, then click lines to trim',
      [CadCommand.EXTEND]: 'Click to select boundary, then click lines to extend',
      [CadCommand.MIRROR]: 'Click two points to define mirror axis, then select features',
      [CadCommand.SCALE]: 'Click base point, reference point, then new size point',
      [CadCommand.LINE]: 'Click two points to draw a line',
      [CadCommand.CIRCLE]: 'Click center, then radius point',
      [CadCommand.RECTANGLE]: 'Click two corners to draw a rectangle',
      [CadCommand.POLYGON]: 'Click points to draw polygon, double-click to finish',
      [CadCommand.ARC]: 'Click start, middle, and end points',
      [CadCommand.MOVE]: 'Select features, then click base and target points',
      [CadCommand.COPY]: 'Select features, then click base and target points',
      [CadCommand.ROTATE]: 'Select features, click center, then rotation angle',
      [CadCommand.DELETE]: 'Select features to delete',
      [CadCommand.MEASURE_DISTANCE]: 'Click two points to measure distance',
      [CadCommand.MEASURE_AREA]: 'Click points to define area, double-click to finish',
      [CadCommand.MEASURE_ANGLE]: 'Click three points to measure angle'
    };

    setToolStatus(messages[tool] || `${tool} ${action}`);
  };

  // Handle tool activation
  const handleToolActivation = (tool: CadCommand) => {
    if (!toolManager) return;

    if (activeTool === tool) {
      toolManager.deactivateTool();
    } else {
      toolManager.activateTool(tool);
    }
  };

  // Tool information
  const toolInfo: Partial<Record<CadCommand, { name: string; description: string; steps: string[] }>> = {
    [CadCommand.TRIM]: {
      name: 'Trim',
      description: 'Cut lines at their intersection with a boundary',
      steps: [
        'Select the cutting edge (boundary line)',
        'Click on the portions of lines to remove',
        'Lines will be trimmed at the intersection'
      ]
    },
    [CadCommand.EXTEND]: {
      name: 'Extend',
      description: 'Extend lines to meet a boundary',
      steps: [
        'Select the boundary to extend to',
        'Click near the end of lines to extend',
        'Lines will extend to the boundary'
      ]
    },
    [CadCommand.MIRROR]: {
      name: 'Mirror',
      description: 'Create a mirrored copy of features',
      steps: [
        'Click two points to define the mirror axis',
        'Select features to mirror',
        'Mirrored copies will be created'
      ]
    },
    [CadCommand.SCALE]: {
      name: 'Scale',
      description: 'Resize features proportionally',
      steps: [
        'Click the base point (scale origin)',
        'Click a reference point',
        'Click to define the new size'
      ]
    }
  };

  const tools = [
    { command: CadCommand.TRIM, icon: Scissors },
    { command: CadCommand.EXTEND, icon: Maximize2 },
    { command: CadCommand.MIRROR, icon: FlipHorizontal },
    { command: CadCommand.SCALE, icon: Scaling }
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>CAD Tools Demo - Trim, Extend, Mirror, Scale</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This demo shows the new CAD modification tools. Select a tool below and follow the instructions.
            </AlertDescription>
          </Alert>

          <div className="mt-4 space-y-4">
            {/* Tool Buttons */}
            <div className="flex gap-2">
              {tools.map(({ command, icon: Icon }) => (
                <Button
                  key={command}
                  variant={activeTool === command ? 'default' : 'outline'}
                  onClick={() => handleToolActivation(command)}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {toolInfo[command]?.name}
                </Button>
              ))}
            </div>

            {/* Status Bar */}
            <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
              <MousePointer className="h-4 w-4" />
              <span className="text-sm">{toolStatus}</span>
              {selectedFeatures.length > 0 && (
                <Badge variant="secondary" className="ml-auto">
                  {selectedFeatures.length} selected
                </Badge>
              )}
            </div>

            {/* Map Container */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <div className="lg:col-span-2">
                <div 
                  ref={mapRef} 
                  className="h-[500px] border rounded-md overflow-hidden"
                />
              </div>

              {/* Tool Info Panel */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Tool Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {activeTool && toolInfo[activeTool] ? (
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium">{toolInfo[activeTool].name}</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {toolInfo[activeTool].description}
                          </p>
                        </div>

                        <div>
                          <h5 className="text-sm font-medium mb-2">Steps:</h5>
                          <ol className="space-y-1">
                            {toolInfo[activeTool].steps.map((step, index) => (
                              <li key={index} className="text-sm flex items-start gap-2">
                                <span className="text-muted-foreground">{index + 1}.</span>
                                <span>{step}</span>
                              </li>
                            ))}
                          </ol>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        Select a tool to see instructions
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Quick Tips */}
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-sm">Quick Tips</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-sm space-y-1">
                      <p>• Click on features to select them</p>
                      <p>• Selected features appear in red</p>
                      <p>• Press ESC to cancel operations</p>
                      <p>• Tools work with multiple selections</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CadToolsDemo;