'use client';

import { Feature } from 'ol';
import { Geometry, LineString, Point } from 'ol/geom.js';
import VectorSource from 'ol/source/Vector.js';
import { getDistance } from 'ol/sphere.js';
import type { Coordinate } from 'ol/coordinate.js';

export interface TrimToolOptions {
  source: VectorSource;
  tolerance?: number;
}

export class TrimTool {
  private source: VectorSource;
  private tolerance: number;
  private selectedBoundary: Feature<Geometry> | null = null;
  private trimmedFeatures: Feature<Geometry>[] = [];

  constructor(options: TrimToolOptions) {
    this.source = options.source;
    this.tolerance = options.tolerance || 10; // pixels
  }

  /**
   * Select a cutting edge/boundary for trimming
   */
  selectBoundary(coordinate: Coordinate, pixelTolerance?: number): Feature<Geometry> | null {
    const tolerance = pixelTolerance || this.tolerance;
    const features = this.source.getFeatures();
    
    let closestFeature: Feature<Geometry> | null = null;
    let minDistance = Infinity;

    features.forEach((feature) => {
      const geometry = feature.getGeometry();
      if (geometry instanceof LineString) {
        const closestPoint = geometry.getClosestPoint(coordinate);
        const distance = getDistance(coordinate, closestPoint);
        
        if (distance < minDistance && distance < tolerance) {
          minDistance = distance;
          closestFeature = feature;
        }
      }
    });

    this.selectedBoundary = closestFeature;
    return closestFeature;
  }

  /**
   * Trim a feature at the intersection with the boundary
   */
  trimFeature(targetFeature: Feature<Geometry>, trimPoint: Coordinate): Feature<Geometry>[] {
    if (!this.selectedBoundary) {
      throw new Error('No boundary selected for trimming');
    }

    const targetGeometry = targetFeature.getGeometry();
    const boundaryGeometry = this.selectedBoundary.getGeometry();

    if (!(targetGeometry instanceof LineString) || !(boundaryGeometry instanceof LineString)) {
      throw new Error('Both target and boundary must be LineString geometries');
    }

    // Find intersection points
    const intersections = this.findIntersections(targetGeometry, boundaryGeometry);
    
    if (intersections.length === 0) {
      return [targetFeature]; // No intersection, return original
    }

    // Determine which side of the intersection to keep based on trim point
    const trimmedSegments = this.splitLineAtIntersections(targetGeometry, intersections, trimPoint);
    
    // Create new features for the trimmed segments
    const trimmedFeatures = trimmedSegments.map((segment) => {
      const newFeature = targetFeature.clone();
      newFeature.setGeometry(segment);
      return newFeature;
    });

    // Store for undo capability
    this.trimmedFeatures.push(...trimmedFeatures);

    return trimmedFeatures;
  }

  /**
   * Find intersection points between two line strings
   */
  private findIntersections(line1: LineString, line2: LineString): Coordinate[] {
    const coords1 = line1.getCoordinates();
    const coords2 = line2.getCoordinates();
    const intersections: Coordinate[] = [];

    for (let i = 0; i < coords1.length - 1; i++) {
      for (let j = 0; j < coords2.length - 1; j++) {
        const intersection = this.lineSegmentIntersection(
          coords1[i] ?? [0, 0],
          coords1[i + 1] ?? [0, 0],
          coords2[j] ?? [0, 0],
          coords2[j + 1] ?? [0, 0]
        );
        
        if (intersection) {
          intersections.push(intersection);
        }
      }
    }

    return intersections;
  }

  /**
   * Calculate intersection point between two line segments
   */
  private lineSegmentIntersection(
    p1: Coordinate,
    p2: Coordinate,
    p3: Coordinate,
    p4: Coordinate
  ): Coordinate | null {
    const x1 = p1[0] ?? 0, y1 = p1[1] ?? 0;
    const x2 = p2[0] ?? 0, y2 = p2[1] ?? 0;
    const x3 = p3[0] ?? 0, y3 = p3[1] ?? 0;
    const x4 = p4[0] ?? 0, y4 = p4[1] ?? 0;

    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    
    if (Math.abs(denom) < 1e-10) {
      return null; // Lines are parallel
    }

    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return [
        x1 + t * (x2 - x1),
        y1 + t * (y2 - y1)
      ];
    }

    return null;
  }

  /**
   * Split a line at intersection points and keep segments based on trim point
   */
  private splitLineAtIntersections(
    line: LineString,
    intersections: Coordinate[],
    trimPoint: Coordinate
  ): LineString[] {
    const coords = line.getCoordinates();
    const segments: LineString[] = [];
    
    // Sort intersections along the line
    intersections.sort((a: any, b: any) => {
      const distA = this.getDistanceAlongLine(coords, a);
      const distB = this.getDistanceAlongLine(coords, b);
      return distA - distB;
    });

    // Create segments
    let currentStart = 0;
    intersections.forEach((intersection, index) => {
      const intersectionIndex = this.findClosestSegmentIndex(coords, intersection);
      
      // Create segment from current start to intersection
      const segmentCoords = coords.slice(currentStart, intersectionIndex + 1);
      segmentCoords.push(intersection);
      
      if (segmentCoords.length >= 2) {
        segments.push(new LineString(segmentCoords));
      }
      
      // Next segment starts from intersection
      currentStart = intersectionIndex + 1;
      if (index === intersections.length - 1 && currentStart < coords.length) {
        const remainingCoords = [intersection, ...coords.slice(currentStart)];
        if (remainingCoords.length >= 2) {
          segments.push(new LineString(remainingCoords));
        }
      }
    });

    // Determine which segments to keep based on trim point
    const keepSegments: LineString[] = [];
    segments.forEach((segment) => {
      const distance = segment.getClosestPoint(trimPoint);
      const dist = getDistance(trimPoint, distance);
      
      if (dist > this.tolerance) {
        keepSegments.push(segment);
      }
    });

    return keepSegments;
  }

  /**
   * Get distance along a line to a point
   */
  private getDistanceAlongLine(coords: Coordinate[], point: Coordinate): number {
    let totalDistance = 0;
    let minDistance = Infinity;
    let distanceToPoint = 0;

    for (let i = 0; i < coords.length - 1; i++) {
      const segmentLength = getDistance(coords[i] ?? [0, 0], coords[i + 1] ?? [0, 0]);
      const distToSegment = this.pointToSegmentDistance(point, coords[i] ?? [0, 0], coords[i + 1] ?? [0, 0]);
      
      if (distToSegment < minDistance) {
        minDistance = distToSegment;
        distanceToPoint = totalDistance + this.getProjectionDistance(point, coords[i] ?? [0, 0], coords[i + 1] ?? [0, 0]);
      }
      
      totalDistance += segmentLength;
    }

    return distanceToPoint;
  }

  /**
   * Find the index of the line segment closest to a point
   */
  private findClosestSegmentIndex(coords: Coordinate[], point: Coordinate): number {
    let minDistance = Infinity;
    let closestIndex = 0;

    for (let i = 0; i < coords.length - 1; i++) {
      const dist = this.pointToSegmentDistance(point, coords[i] ?? [0, 0], coords[i + 1] ?? [0, 0]);
      if (dist < minDistance) {
        minDistance = dist;
        closestIndex = i;
      }
    }

    return closestIndex;
  }

  /**
   * Calculate distance from a point to a line segment
   */
  private pointToSegmentDistance(point: Coordinate, segStart: Coordinate, segEnd: Coordinate): number {
    const dx = (segEnd[0] ?? 0) - (segStart[0] ?? 0);
    const dy = (segEnd[1] ?? 0) - (segStart[1] ?? 0);
    
    if (dx === 0 && dy === 0) {
      return getDistance(point, segStart);
    }

    const t = Math.max(0, Math.min(1, 
      (((point[0] ?? 0) - (segStart[0] ?? 0)) * dx + ((point[1] ?? 0) - (segStart[1] ?? 0)) * dy) / 
      (dx * dx + dy * dy)
    ));

    const projection: Coordinate = [
      (segStart[0] ?? 0) + t * dx,
      (segStart[1] ?? 0) + t * dy
    ];

    return getDistance(point, projection);
  }

  /**
   * Get projection distance along a segment
   */
  private getProjectionDistance(point: Coordinate, segStart: Coordinate, segEnd: Coordinate): number {
    const dx = (segEnd[0] ?? 0) - (segStart[0] ?? 0);
    const dy = (segEnd[1] ?? 0) - (segStart[1] ?? 0);
    const segmentLength = Math.sqrt(dx * dx + dy * dy);
    
    if (segmentLength === 0) return 0;

    const t = Math.max(0, Math.min(1, 
      (((point[0] ?? 0) - (segStart[0] ?? 0)) * dx + ((point[1] ?? 0) - (segStart[1] ?? 0)) * dy) / 
      (dx * dx + dy * dy)
    ));

    return t * segmentLength;
  }

  /**
   * Clear the selected boundary
   */
  clearBoundary(): void {
    this.selectedBoundary = null;
  }

  /**
   * Get the last trimmed features for undo
   */
  getLastTrimmed(): Feature<Geometry>[] {
    return this.trimmedFeatures;
  }

  /**
   * Clear trim history
   */
  clearHistory(): void {
    this.trimmedFeatures = [];
  }
}

export default TrimTool;