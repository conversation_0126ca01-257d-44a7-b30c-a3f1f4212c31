# CAD Tools for CLEAR

This directory contains professional CAD-style tools for utility infrastructure mapping, designed to meet INDOT CAD standards.

## Available Tools

### Modification Tools

#### 1. **Trim Tool** (`trim-tool.ts`)
- Cut lines at their intersection with a boundary
- Select a cutting edge, then click portions of lines to remove
- Supports multiple trim operations with the same boundary

#### 2. **Extend Tool** (`extend-tool.ts`)
- Extend lines to meet a boundary
- Automatically determines which end of the line to extend based on click position
- Configurable maximum extension length

#### 3. **Mirror Tool** (`mirror-tool.ts`)
- Create mirrored copies of features across a defined axis
- Define mirror axis with two clicks
- Option to keep or delete original features

#### 4. **Scale Tool** (`scale-tool.ts`)
- Resize features proportionally or non-uniformly
- Three-point scaling: base point, reference point, and new size point
- Visual scale handles for interactive scaling
- Undo capability with original size tracking

## Integration

### Using with OpenLayers

```typescript
import { CadToolManager } from './cad-tool-manager';
import { CadCommand } from '~/hooks/use-cad-shortcuts';

// Initialize the tool manager
const toolManager = new CadToolManager({
  map: olMap,
  source: vectorSource,
  onToolActivated: (tool) => console.log(`Activated: ${tool}`),
  onFeatureModified: (features) => console.log('Modified:', features)
});

// Activate a tool
toolManager.activateTool(CadCommand.TRIM);

// Handle map clicks
olMap.on('click', (e) => {
  toolManager.handleClick(e.coordinate, e.pixel);
});
```

### Keyboard Shortcuts

- `TR` - Activate Trim tool
- `EX` - Activate Extend tool
- `MI` - Activate Mirror tool
- `S` - Activate Scale tool
- `ESC` - Cancel current operation

## Tool-Specific Usage

### Trim Tool
1. Activate the trim tool
2. Click on the cutting edge (boundary line)
3. Click on portions of lines to trim at the intersection

### Extend Tool
1. Activate the extend tool
2. Click on the boundary to extend to
3. Click near the end of lines to extend them to the boundary

### Mirror Tool
1. Activate the mirror tool
2. Click two points to define the mirror axis
3. Select features to mirror
4. Mirrored copies will be created across the axis

### Scale Tool
1. Activate the scale tool
2. Click the base point (origin for scaling)
3. Click a reference point
4. Click to define the new size

## Architecture

- Each tool is a standalone class with its own logic
- `CadToolManager` coordinates tool activation and interaction
- Tools work with OpenLayers `Feature` and `Geometry` objects
- All tools support undo operations

## TypeScript Types

```typescript
interface TrimToolOptions {
  source: VectorSource;
  tolerance?: number;
}

interface ExtendToolOptions {
  source: VectorSource;
  tolerance?: number;
  maxExtensionLength?: number;
}

interface MirrorToolOptions {
  source: VectorSource;
  keepOriginal?: boolean;
}

interface ScaleToolOptions {
  source: VectorSource;
  uniformScale?: boolean;
  minScale?: number;
  maxScale?: number;
}
```

## Future Enhancements

- Offset tool for parallel lines
- Array tool for creating multiple copies
- Fillet/Chamfer tools for corner modifications
- Dimension tools for measurements
- Block/Symbol management