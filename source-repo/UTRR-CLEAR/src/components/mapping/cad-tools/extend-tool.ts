'use client';

import { Feature } from 'ol';
import { Geometry, LineString, Point } from 'ol/geom.js';
import VectorSource from 'ol/source/Vector.js';
import { getDistance } from 'ol/sphere.js';
import type { Coordinate } from 'ol/coordinate.js';

export interface ExtendToolOptions {
  source: VectorSource;
  tolerance?: number;
  maxExtensionLength?: number;
}

export class ExtendTool {
  private source: VectorSource;
  private tolerance: number;
  private maxExtensionLength: number;
  private selectedBoundary: Feature<Geometry> | null = null;
  private extendedFeatures: Feature<Geometry>[] = [];

  constructor(options: ExtendToolOptions) {
    this.source = options.source;
    this.tolerance = options.tolerance || 10; // pixels
    this.maxExtensionLength = options.maxExtensionLength || 1000; // units
  }

  /**
   * Select a boundary to extend to
   */
  selectBoundary(coordinate: Coordinate, pixelTolerance?: number): Feature<Geometry> | null {
    const tolerance = pixelTolerance || this.tolerance;
    const features = this.source.getFeatures();
    
    let closestFeature: Feature<Geometry> | null = null;
    let minDistance = Infinity;

    features.forEach((feature) => {
      const geometry = feature.getGeometry();
      if (geometry instanceof LineString) {
        const closestPoint = geometry.getClosestPoint(coordinate);
        const distance = getDistance(coordinate, closestPoint);
        
        if (distance < minDistance && distance < tolerance) {
          minDistance = distance;
          closestFeature = feature;
        }
      }
    });

    this.selectedBoundary = closestFeature;
    return closestFeature;
  }

  /**
   * Extend a feature to meet the boundary
   */
  extendFeature(targetFeature: Feature<Geometry>, extendFromEnd: boolean = true): Feature<Geometry> | null {
    if (!this.selectedBoundary) {
      throw new Error('No boundary selected for extending');
    }

    const targetGeometry = targetFeature.getGeometry();
    const boundaryGeometry = this.selectedBoundary.getGeometry();

    if (!(targetGeometry instanceof LineString) || !(boundaryGeometry instanceof LineString)) {
      throw new Error('Both target and boundary must be LineString geometries');
    }

    // Extend the line and find intersection
    const extendedLine = this.extendLine(targetGeometry, extendFromEnd);
    const intersection = this.findNearestIntersection(extendedLine, boundaryGeometry, extendFromEnd);

    if (!intersection) {
      return null; // No intersection found within max extension length
    }

    // Create extended line to intersection point
    const coords = targetGeometry.getCoordinates();
    let newCoords: Coordinate[];

    if (extendFromEnd) {
      newCoords = [...coords, intersection];
    } else {
      newCoords = [intersection, ...coords];
    }

    // Create new feature with extended geometry
    const extendedFeature = targetFeature.clone();
    extendedFeature.setGeometry(new LineString(newCoords));

    // Store for undo capability
    this.extendedFeatures.push(extendedFeature);

    return extendedFeature;
  }

  /**
   * Extend a line in its direction
   */
  private extendLine(line: LineString, fromEnd: boolean): LineString {
    const coords = line.getCoordinates();
    
    if (coords.length < 2) {
      return line;
    }

    let extendedCoords: Coordinate[];
    
    if (fromEnd) {
      // Extend from the end
      const lastTwo = coords.slice(-2);
      const direction = this.getDirection(lastTwo[0] ?? [0, 0], lastTwo[1] ?? [0, 0]);
      const extensionPoint = this.projectPoint(coords[coords.length - 1] ?? [0, 0], direction, this.maxExtensionLength);
      extendedCoords = [...coords, extensionPoint];
    } else {
      // Extend from the start
      const firstTwo = coords.slice(0, 2);
      const direction = this.getDirection(firstTwo[1] ?? [0, 0], firstTwo[0] ?? [0, 0]);
      const extensionPoint = this.projectPoint(coords[0] ?? [0, 0], direction, this.maxExtensionLength);
      extendedCoords = [extensionPoint, ...coords];
    }

    return new LineString(extendedCoords);
  }

  /**
   * Get direction vector between two points
   */
  private getDirection(from: Coordinate, to: Coordinate): [number, number] {
    const dx = (to[0] ?? 0) - (from[0] ?? 0);
    const dy = (to[1] ?? 0) - (from[1] ?? 0);
    const length = Math.sqrt(dx * dx + dy * dy);
    
    if (length === 0) {
      return [0, 0];
    }

    return [dx / length, dy / length];
  }

  /**
   * Project a point in a given direction
   */
  private projectPoint(point: Coordinate, direction: [number, number], distance: number): Coordinate {
    return [
      (point[0] ?? 0) + direction[0] * distance,
      (point[1] ?? 0) + direction[1] * distance
    ];
  }

  /**
   * Find the nearest intersection between extended line and boundary
   */
  private findNearestIntersection(
    extendedLine: LineString,
    boundary: LineString,
    fromEnd: boolean
  ): Coordinate | null {
    const extendedCoords = extendedLine.getCoordinates();
    const boundaryCoords = boundary.getCoordinates();
    let nearestIntersection: Coordinate | null = null;
    let minDistance = Infinity;

    // Reference point for distance calculation
    const referencePoint = fromEnd ? 
      (extendedCoords[extendedCoords.length - 2] ?? [0, 0]) : // Second to last for end extension
      (extendedCoords[1] ?? [0, 0]); // Second point for start extension

    // Check only the extension segment
    const segmentStart = fromEnd ? (extendedCoords[extendedCoords.length - 2] ?? [0, 0]) : (extendedCoords[0] ?? [0, 0]);
    const segmentEnd = fromEnd ? (extendedCoords[extendedCoords.length - 1] ?? [0, 0]) : (extendedCoords[1] ?? [0, 0]);

    for (let j = 0; j < boundaryCoords.length - 1; j++) {
      const intersection = this.lineSegmentIntersection(
        segmentStart,
        segmentEnd,
        boundaryCoords[j] ?? [0, 0],
        boundaryCoords[j + 1] ?? [0, 0]
      );
      
      if (intersection) {
        const distance = getDistance(referencePoint, intersection);
        if (distance < minDistance) {
          minDistance = distance;
          nearestIntersection = intersection;
        }
      }
    }

    return nearestIntersection;
  }

  /**
   * Calculate intersection point between two line segments
   */
  private lineSegmentIntersection(
    p1: Coordinate,
    p2: Coordinate,
    p3: Coordinate,
    p4: Coordinate
  ): Coordinate | null {
    const x1 = p1[0] ?? 0, y1 = p1[1] ?? 0;
    const x2 = p2[0] ?? 0, y2 = p2[1] ?? 0;
    const x3 = p3[0] ?? 0, y3 = p3[1] ?? 0;
    const x4 = p4[0] ?? 0, y4 = p4[1] ?? 0;

    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    
    if (Math.abs(denom) < 1e-10) {
      return null; // Lines are parallel
    }

    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return [
        x1 + t * (x2 - x1),
        y1 + t * (y2 - y1)
      ];
    }

    return null;
  }

  /**
   * Extend multiple features to a boundary
   */
  extendMultiple(features: Feature<Geometry>[], extendFromEnd: boolean = true): Feature<Geometry>[] {
    const extended: Feature<Geometry>[] = [];
    
    features.forEach((feature) => {
      const extendedFeature = this.extendFeature(feature, extendFromEnd);
      if (extendedFeature) {
        extended.push(extendedFeature);
      }
    });

    return extended;
  }

  /**
   * Clear the selected boundary
   */
  clearBoundary(): void {
    this.selectedBoundary = null;
  }

  /**
   * Get the last extended features for undo
   */
  getLastExtended(): Feature<Geometry>[] {
    return this.extendedFeatures;
  }

  /**
   * Clear extension history
   */
  clearHistory(): void {
    this.extendedFeatures = [];
  }

  /**
   * Check if a feature can be extended to the boundary
   */
  canExtend(feature: Feature<Geometry>, fromEnd: boolean = true): boolean {
    if (!this.selectedBoundary) {
      return false;
    }

    const geometry = feature.getGeometry();
    if (!(geometry instanceof LineString)) {
      return false;
    }

    const extendedLine = this.extendLine(geometry, fromEnd);
    const boundaryGeometry = this.selectedBoundary.getGeometry();
    
    if (!(boundaryGeometry instanceof LineString)) {
      return false;
    }

    const intersection = this.findNearestIntersection(extendedLine, boundaryGeometry, fromEnd);
    return intersection !== null;
  }
}

export default ExtendTool;