'use client';

import React, { useState, useCallback } from 'react';
import {
  Layers,
  LocateFixed,
  ZoomIn,
  ZoomOut,
  Plus,
  Ruler,
  Info,
  Save,
  Undo2,
  Download,
  Box,
  SplitSquareVertical,
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import { LayerMenu } from './controls/layer-menu';
import { MeasureControl } from './controls/measure-control';
import { FeatureInfo } from './controls/feature-info';
import type Map from 'ol/Map.js';
import type { ViewMode } from '~/lib/mapping/types';

interface MapToolbarProps {
  map: Map | null;
  zoomIn: () => void;
  zoomOut: () => void;
  resetView: () => void;
  saveFeatures?: () => Promise<void>;
  importFeatures?: () => Promise<void>;
  setViewMode?: (mode: ViewMode) => void;
  has3DSupport?: boolean;
}

type ToolbarState = {
  layerMenuOpen: boolean;
  measureActive: boolean;
  infoActive: boolean;
  viewMode: ViewMode;
};

/**
 * Map toolbar with controls for various map functions
 * Enhanced with 3D visualization controls
 */
export function MapToolbar({
  map,
  zoomIn,
  zoomOut,
  resetView,
  saveFeatures,
  importFeatures,
  setViewMode,
  has3DSupport = false,
}: MapToolbarProps) {
  const [toolState, setToolState] = useState<ToolbarState>({
    layerMenuOpen: false,
    measureActive: false,
    infoActive: false,
    viewMode: '2d',
  });

  // Handlers for toggling tool states
  const toggleLayerMenu = useCallback(() => {
    setToolState((prev) => ({
      ...prev,
      layerMenuOpen: !prev.layerMenuOpen,
    }));
  }, []);

  const toggleMeasureTool = useCallback(() => {
    setToolState((prev) => ({
      ...prev,
      measureActive: !prev.measureActive,
      // Disable other exclusive tools
      infoActive: prev.measureActive ? prev.infoActive : false,
    }));
  }, []);

  const toggleInfoTool = useCallback(() => {
    setToolState((prev) => ({
      ...prev,
      infoActive: !prev.infoActive,
      // Disable other exclusive tools
      measureActive: prev.infoActive ? prev.measureActive : false,
    }));
  }, []);

  // Handler for switching view modes (2D/3D/Cross-section)
  const switchViewMode = useCallback(
    (mode: ViewMode) => {
      // Return early if the requested mode is already active
      if (toolState.viewMode === mode) return;

      // Update internal state
      setToolState((prev) => ({
        ...prev,
        viewMode: mode,
      }));

      // Call parent component's handler if provided
      if (setViewMode) {
        setViewMode(mode);
      }
    },
    [toolState.viewMode, setViewMode]
  );

  return (
    <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
      {/* Main toolbar buttons */}
      <div className="bg-background/80 backdrop-blur-sm rounded-md shadow-md p-1.5 flex flex-col gap-1.5">
        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={zoomIn} title="Zoom In">
          <ZoomIn className="h-5 w-5" />
        </Button>

        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={zoomOut} title="Zoom Out">
          <ZoomOut className="h-5 w-5" />
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={resetView}
          title="Reset View"
        >
          <LocateFixed className="h-5 w-5" />
        </Button>

        <div className="w-full h-px bg-border/50 my-0.5" />

        <Button
          variant={toolState.layerMenuOpen ? 'secondary' : 'ghost'}
          size="icon"
          className="h-8 w-8"
          onClick={toggleLayerMenu}
          title="Layer Controls"
        >
          <Layers className="h-5 w-5" />
        </Button>

        <Button
          variant={toolState.measureActive ? 'secondary' : 'ghost'}
          size="icon"
          className="h-8 w-8"
          onClick={toggleMeasureTool}
          title="Measure Tool"
        >
          <Ruler className="h-5 w-5" />
        </Button>

        <Button
          variant={toolState.infoActive ? 'secondary' : 'ghost'}
          size="icon"
          className="h-8 w-8"
          onClick={toggleInfoTool}
          title="Feature Info"
        >
          <Info className="h-5 w-5" />
        </Button>

        {/* 3D visualization controls - only show if 3D is supported */}
        {has3DSupport && (
          <>
            <div className="w-full h-px bg-border/50 my-0.5" />

            <Button
              variant={toolState.viewMode === '2d' ? 'secondary' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => switchViewMode('2d')}
              title="2D View"
            >
              <Layers className="h-5 w-5" />
            </Button>

            <Button
              variant={toolState.viewMode === '3d' ? 'secondary' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => switchViewMode('3d')}
              title="3D View"
            >
              <Box className="h-5 w-5" />
            </Button>

            <Button
              variant={toolState.viewMode === 'cross-section' ? 'secondary' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => switchViewMode('cross-section')}
              title="Cross-Section View"
            >
              <SplitSquareVertical className="h-5 w-5" />
            </Button>
          </>
        )}

        {saveFeatures && (
          <>
            <div className="w-full h-px bg-border/50 my-0.5" />

            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={saveFeatures}
              title="Save Features"
            >
              <Save className="h-5 w-5" />
            </Button>
          </>
        )}

        {importFeatures && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={importFeatures}
            title="Import Features"
          >
            <Download className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Extended controls that appear when buttons are toggled */}
      {toolState.layerMenuOpen && map && <LayerMenu map={map} active={toolState.layerMenuOpen} />}

      {/* Measure tool */}
      <MeasureControl map={map} active={toolState.measureActive} />

      {/* Feature info panel */}
      <FeatureInfo map={map} active={toolState.infoActive} />
    </div>
  );
}
