'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '~/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '~/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover';
import { Label } from '~/components/ui/label';
import { Input } from '~/components/ui/input';
import { Slider } from '~/components/ui/slider';
import { Switch } from '~/components/ui/switch';
import {
  Ruler,
  Move,
  RotateCw,
  Circle,
  Type,
  Settings,
  Trash2,
  Download,
  ChevronDown,
} from 'lucide-react';
import DimensionTools, {
  type DimensionStyle,
  type DimensionType,
  DIMENSION_PRESETS,
  DEFAULT_DIMENSION_STYLE,
} from '~/components/gis/dimension-tools';
import type { Map } from 'ol';
import type VectorSource from 'ol/source/Vector.js';

interface DimensionToolbarProps {
  map: Map | null;
  source: VectorSource | null;
  onDimensionStart?: (type: DimensionType) => void;
  onDimensionEnd?: () => void;
}

export function DimensionToolbar({
  map,
  source,
  onDimensionStart,
  onDimensionEnd,
}: DimensionToolbarProps) {
  const [dimensionTool, setDimensionTool] = useState<DimensionTools | null>(null);
  const [activeTool, setActiveTool] = useState<DimensionType | null>(null);
  const [dimensionStyle, setDimensionStyle] = useState<DimensionStyle>(DEFAULT_DIMENSION_STYLE);
  const [selectedPreset, setSelectedPreset] = useState<string>('engineering');

  // Initialize dimension tool when map and source are available
  React.useEffect(() => {
    if (map && source && !dimensionTool) {
      const tool = new DimensionTools(map, source, dimensionStyle);
      setDimensionTool(tool);
    }
  }, [map, source, dimensionStyle]);

  // Update dimension tool style when it changes
  React.useEffect(() => {
    if (dimensionTool) {
      dimensionTool.updateStyle(dimensionStyle);
    }
  }, [dimensionTool, dimensionStyle]);

  const handleToolSelect = useCallback((toolType: DimensionType | 'clear' | 'export') => {
    if (!dimensionTool) return;

    // Stop current dimension
    dimensionTool.stopCurrentDimension();
    
    if (toolType === 'clear') {
      dimensionTool.clearAllDimensions();
      setActiveTool(null);
      return;
    }

    if (toolType === 'export') {
      const dxfContent = dimensionTool.exportToDXF();
      const blob = new Blob([dxfContent], { type: 'application/dxf' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'dimensions.dxf';
      a.click();
      URL.revokeObjectURL(url);
      return;
    }

    // Start new dimension tool
    setActiveTool(toolType);
    onDimensionStart?.(toolType);

    switch (toolType) {
      case 'linear':
        dimensionTool.startLinearDimension('aligned');
        break;
      case 'aligned':
        dimensionTool.startLinearDimension('horizontal');
        break;
      case 'angular':
        dimensionTool.startAngularDimension();
        break;
      case 'radius':
        dimensionTool.startRadiusDimension();
        break;
      case 'diameter':
        dimensionTool.startDiameterDimension();
        break;
      case 'leader':
        dimensionTool.startLeaderDimension();
        break;
    }
  }, [dimensionTool, onDimensionStart]);

  const handlePresetChange = useCallback((preset: string) => {
    setSelectedPreset(preset);
    const presetStyle = DIMENSION_PRESETS[preset as keyof typeof DIMENSION_PRESETS];
    if (presetStyle) {
      setDimensionStyle(current => ({ ...current, ...presetStyle }));
    }
  }, []);

  const handleStyleChange = useCallback((updates: Partial<DimensionStyle>) => {
    setDimensionStyle(current => ({ ...current, ...updates }));
  }, []);

  if (!map || !source) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 rounded-md bg-background p-2 shadow-sm border">
      {/* Linear Dimensions */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant={activeTool === 'linear' || activeTool === 'aligned' ? 'default' : 'outline'} 
            size="sm"
            className="gap-1"
          >
            <Ruler className="h-4 w-4" />
            Linear
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => handleToolSelect('aligned')}>
            <Move className="mr-2 h-4 w-4" />
            Aligned
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleToolSelect('linear')}>
            <Ruler className="mr-2 h-4 w-4" />
            Horizontal/Vertical
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Angular Dimension */}
      <Button
        variant={activeTool === 'angular' ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleToolSelect('angular')}
        title="Angular Dimension"
      >
        <RotateCw className="h-4 w-4" />
      </Button>

      {/* Radius/Diameter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant={activeTool === 'radius' || activeTool === 'diameter' ? 'default' : 'outline'} 
            size="sm"
            className="gap-1"
          >
            <Circle className="h-4 w-4" />
            Circle
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => handleToolSelect('radius')}>
            Radius
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleToolSelect('diameter')}>
            Diameter
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Leader */}
      <Button
        variant={activeTool === 'leader' ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleToolSelect('leader')}
        title="Leader with Text"
      >
        <Type className="h-4 w-4" />
      </Button>

      <div className="mx-2 h-6 w-px bg-border" />

      {/* Style Settings */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" title="Dimension Settings">
            <Settings className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Dimension Style</h4>
              
              {/* Preset Styles */}
              <div className="space-y-2">
                <Label>Preset Style</Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full justify-between">
                      {selectedPreset.charAt(0).toUpperCase() + selectedPreset.slice(1)}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full">
                    <DropdownMenuRadioGroup value={selectedPreset} onValueChange={handlePresetChange}>
                      <DropdownMenuRadioItem value="architectural">Architectural</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="engineering">Engineering</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="metric">Metric</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="survey">Survey</DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Text Height */}
              <div className="space-y-2">
                <Label>Text Height: {dimensionStyle.textHeight}px</Label>
                <Slider
                  value={[dimensionStyle.textHeight]}
                  onValueChange={([value]) => handleStyleChange({ textHeight: value })}
                  min={8}
                  max={24}
                  step={1}
                />
              </div>

              {/* Arrow Size */}
              <div className="space-y-2">
                <Label>Arrow Size: {dimensionStyle.arrowSize}px</Label>
                <Slider
                  value={[dimensionStyle.arrowSize]}
                  onValueChange={([value]) => handleStyleChange({ arrowSize: value })}
                  min={5}
                  max={20}
                  step={1}
                />
              </div>

              {/* Units */}
              <div className="space-y-2">
                <Label>Units</Label>
                <div className="flex gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="flex-1">
                        {dimensionStyle.unit}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuRadioGroup 
                        value={dimensionStyle.unit} 
                        onValueChange={(value) => handleStyleChange({ unit: value as any })}
                      >
                        <DropdownMenuRadioItem value="ft">Feet (ft)</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="m">Meters (m)</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="in">Inches (in)</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="mm">Millimeters (mm)</DropdownMenuRadioItem>
                      </DropdownMenuRadioGroup>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={dimensionStyle.showUnit}
                      onCheckedChange={(checked) => handleStyleChange({ showUnit: checked })}
                    />
                    <Label className="text-sm">Show Unit</Label>
                  </div>
                </div>
              </div>

              {/* Decimal Places */}
              <div className="space-y-2">
                <Label>Decimal Places</Label>
                <Input
                  type="number"
                  value={dimensionStyle.decimalPlaces}
                  onChange={(e: any) => handleStyleChange({ decimalPlaces: parseInt(e.target.value) || 0 })}
                  min={0}
                  max={6}
                  className="w-20"
                />
              </div>

              {/* Colors */}
              <div className="space-y-2">
                <Label>Colors</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">Line Color</Label>
                    <Input
                      type="color"
                      value={dimensionStyle.lineColor}
                      onChange={(e: any) => handleStyleChange({ lineColor: e.target.value })}
                      className="h-8"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Text Color</Label>
                    <Input
                      type="color"
                      value={dimensionStyle.textColor}
                      onChange={(e: any) => handleStyleChange({ textColor: e.target.value })}
                      className="h-8"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Clear Dimensions */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleToolSelect('clear')}
        title="Clear All Dimensions"
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      {/* Export to DXF */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleToolSelect('export')}
        title="Export Dimensions to DXF"
      >
        <Download className="h-4 w-4" />
      </Button>
    </div>
  );
}