import { Feature } from 'ol';
import type { Geometry } from 'ol/geom.js';
import { LineString, Point, Polygon } from 'ol/geom.js';
import type BaseLayer from 'ol/layer/Base.js';
import VectorLayer from 'ol/layer/Vector.js';
import WebGLPointsLayer from 'ol/layer/WebGLPoints.js';
import type VectorSource from 'ol/source/Vector.js';
import { Style } from 'ol/style.js';
import type { ViewMode } from '~/lib/mapping/types';
import { CoordinateTransformer } from '~/lib/mapping/coordinate-utils';
import { safeLog } from '~/lib/error-handler';

/**
 * Configuration for 3D layer styling
 */
export interface ThreeDLayerStyle {
  // Base properties
  color?: string | ((feature: Feature<Geometry>) => string);
  opacity?: number;
  strokeWidth?: number;

  // 3D specific properties
  elevation?: number | ((feature: Feature<Geometry>) => number);
  verticalScale?: number; // Exaggeration factor for elevation

  // Symbol properties for point features
  symbolType?: 'circle' | 'square' | 'triangle' | 'icon';
  symbolSize?: number | ((feature: Feature<Geometry>) => number);

  // Line properties for line features
  lineWidth?: number;
  lineJoin?: 'round' | 'bevel' | 'miter';

  // Fill properties for polygon features
  fillColor?: string | ((feature: Feature<Geometry>) => string);
  fillOpacity?: number;
}

/**
 * Options for creating a 3D layer
 */
export interface ThreeDLayerOptions {
  source: VectorSource;
  style: ThreeDLayerStyle;
  name: string;
  title: string;
  visible?: boolean;
  zIndex?: number;
  verticalScale?: number;
  elevationAttribute?: string; // Feature attribute containing elevation values
  depthAttribute?: string; // Feature attribute containing depth values
  elevationOffset?: number; // Offset to apply to elevation values
}

/**
 * Base configuration for a 3D-capable layer
 */
export interface ThreeDLayerConfig {
  name: string;
  title: string;
  type: '3d' | 'webgl';
  source?: VectorSource;
  style?: ThreeDLayerStyle;
  visible: boolean;
  zIndex?: number;
  verticalScale?: number;
  elevationAttribute?: string;
  depthAttribute?: string;
  elevationOffset?: number;
  baseLayer?: string; // Name of the 2D base layer
  supports3D: true; // Always true for 3D layers
  render3D: true; // Always true for 3D layers
  visualMode: '3d' | 'cross_section'; // Visual mode
}

/**
 * Type guard to check if a layer is a 3D-capable WebGL layer
 * @param layer Layer to check
 * @returns Whether the layer is a 3D-capable WebGL layer
 */
export function is3DLayer(layer: BaseLayer): boolean {
  const props = layer.getProperties();
  return (
    props.supports3D === true &&
    props.render3D === true &&
    (props.visualMode === '3d' || props.visualMode === 'cross_section')
  );
}

/**
 * Check if WebGL is supported in the browser
 * @returns boolean indicating WebGL support
 */
export function checkWebGLSupport(): boolean {
  try {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!context;
  } catch (e) {
    safeLog.warn('WebGL not supported - 3D visualizations will be disabled');
    return false;
  }
}

/**
 * Create a WebGL-based 3D layer for points
 * @param options Options for the 3D layer
 * @returns WebGL points layer for 3D visualization
 */
export function create3DPointsLayer(options: ThreeDLayerOptions): WebGLPointsLayer<VectorSource<Feature<Geometry>>> | null {
  if (!checkWebGLSupport()) {
    safeLog.warn('Cannot create 3D layer: WebGL not supported');
    return null;
  }

  const {
    source,
    name,
    title,
    visible = false,
    zIndex = 10,
    verticalScale = 1,
    elevationAttribute = 'elevation',
    depthAttribute = 'depth',
    elevationOffset = 0,
    style,
  } = options;

  try {
    // Create WebGL style
    const webGLStyle: any = {
      symbol: {
        symbolType: style.symbolType || 'circle',
        size: style.symbolSize || 8,
        color: style.color || [255, 0, 0, 1],
        opacity: style.opacity || 1,
        // Use elevation or depth for 3D positioning
        elevation: [
          'case',
          ['has', elevationAttribute],
          ['*', ['get', elevationAttribute], verticalScale],
          ['has', depthAttribute],
          ['*', ['-', 0, ['get', depthAttribute]], verticalScale],
          elevationOffset,
        ],
      },
    };

    // Create the WebGL layer
    const webGLLayer = new WebGLPointsLayer({
      source,
      style: webGLStyle,
      visible,
      zIndex,
      properties: {
        name,
        title,
        type: 'webgl',
        supports3D: true,
        render3D: true,
        visualMode: '3d',
        verticalScale,
        elevationAttribute,
        depthAttribute,
        elevationOffset,
      },
    });

    return webGLLayer;
  } catch (error) {
    safeLog.error('Error creating 3D points layer:', { error: String(error) });
    return null;
  }
}

/**
 * Convert a 2D vector layer to a 3D-capable layer by adding elevation data
 * @param layer The 2D vector layer to convert
 * @param elevationGetter Function to get elevation for a feature
 * @returns The same layer with updated properties for 3D support
 */
export function enhanceLayerWith3D(
  layer: VectorLayer<VectorSource>,
  elevationGetter?: (feature: Feature<Geometry>) => number
): VectorLayer<VectorSource> {
  // Set 3D properties on the layer
  layer.set('supports3D', true);
  layer.set('render3D', false); // Initially not rendering in 3D
  layer.set('visualMode', '2d'); // Initially in 2D mode

  // If we have an elevation getter, set elevation on all features
  if (elevationGetter) {
    const source = layer.getSource();
    const features = source?.getFeatures() || [];

    features.forEach((feature) => {
      const elevation = elevationGetter(feature);
      if (elevation !== undefined) {
        feature.set('elevation', elevation);
      }
    });
  }

  return layer;
}

/**
 * Get the elevation or depth of a feature
 * @param feature The feature to get elevation for
 * @param elevationAttribute The attribute containing elevation values
 * @param depthAttribute The attribute containing depth values
 * @returns The elevation value, or null if not found
 */
export function getFeatureElevation(
  feature: Feature<Geometry>,
  elevationAttribute: string = 'elevation',
  depthAttribute: string = 'depth'
): number | null {
  // Try to get elevation directly
  let elevation = feature.get(elevationAttribute);
  if (elevation !== undefined && typeof elevation === 'number') {
    return elevation;
  }

  // Try to get depth and convert to elevation (negative depth)
  const depth = feature.get(depthAttribute);
  if (depth !== undefined && typeof depth === 'number') {
    return -depth; // Convert depth to elevation (negative)
  }

  // Try to get Z coordinate from geometry
  const geometry = feature.getGeometry();
  if (!geometry) return null;

  if (geometry instanceof Point) {
    const coords = geometry.getCoordinates();
    return CoordinateTransformer.getZCoordinate(coords);
  } else if (geometry instanceof LineString) {
    // For linestrings, get average of first and last point
    const coords = geometry.getCoordinates();
    if (coords.length > 0) {
      const firstCoord = coords[0];
      const lastCoord = coords[coords.length - 1];
      
      if (!firstCoord || !lastCoord) return null;
      
      const firstZ = firstCoord ? CoordinateTransformer.getZCoordinate(firstCoord) : null;
      const lastZ = lastCoord ? CoordinateTransformer.getZCoordinate(lastCoord) : null;

      if (firstZ !== null && lastZ !== null) {
        return (firstZ + lastZ) / 2;
      } else if (firstZ !== null) {
        return firstZ;
      } else if (lastZ !== null) {
        return lastZ;
      }
    }
  }

  // No elevation found
  return null;
}

/**
 * Apply vertical exaggeration to feature elevations
 * @param features Features to modify
 * @param scale Exaggeration factor to apply
 * @param elevationAttribute Attribute containing elevation values
 * @returns Modified features with exaggerated elevations
 */
/**
 * Options for creating a 3D vector layer
 */
export interface Vector3DLayerOptions extends ThreeDLayerOptions {
  // Additional options specific to vector 3D layers
  geometryType?: 'point' | 'line' | 'polygon';
  extrudeHeight?: number | ((feature: Feature<Geometry>) => number);
  showLabels?: boolean;
  labelAttribute?: string;
  labelStyle?: object;
}

/**
 * Create a 3D vector layer
 * @param options Options for creating the 3D vector layer
 * @returns VectorLayer with 3D properties set
 */
export function createVector3DLayer(options: Vector3DLayerOptions): VectorLayer<VectorSource> {
  const {
    source,
    name,
    title,
    visible = false,
    zIndex = 10,
    verticalScale = 1,
    elevationAttribute = 'elevation',
    depthAttribute = 'depth',
    elevationOffset = 0,
    style,
    geometryType = 'point',
    extrudeHeight = 0,
    showLabels = false,
    labelAttribute = 'name',
  } = options;

  // Create standard style function that considers 3D properties
  const styleFunction = (feature: Feature<Geometry>) => {
    // Base style
    const baseStyle: Style = new Style({
      // Style properties would go here, using style parameters from options
    });

    return baseStyle;
  };

  // Create the layer
  const layer = new VectorLayer({
    source,
    style: (feature, resolution) => {
      if (feature instanceof Feature) {
        return styleFunction(feature);
      }
      return [];
    },
    visible,
    zIndex,
    properties: {
      name,
      title,
      type: '3d',
      supports3D: true,
      render3D: true,
      visualMode: '3d',
      verticalScale,
      elevationAttribute,
      depthAttribute,
      elevationOffset,
      geometryType,
      extrudeHeight,
      showLabels,
      labelAttribute,
    },
  });

  return layer;
}

export function applyVerticalExaggeration(
  features: Feature<Geometry>[],
  scale: number = 1,
  elevationAttribute: string = 'elevation'
): Feature<Geometry>[] {
  if (scale === 1) return features; // No change needed

  return features.map((feature) => {
    const elevation = feature.get(elevationAttribute);
    if (elevation !== undefined && typeof elevation === 'number') {
      // Clone the feature to avoid modifying the original
      const newFeature = feature.clone();
      newFeature.set(elevationAttribute, elevation * scale);
      return newFeature;
    }
    return feature;
  });
}

/**
 * Calculate the elevation range of features in a layer
 * @param source The vector source containing the features
 * @param elevationAttribute Attribute containing elevation values
 * @param depthAttribute Attribute containing depth values
 * @returns The min and max elevation values, or null if no elevation data found
 */
export function getLayerElevationRange(
  source: VectorSource,
  elevationAttribute: string = 'elevation',
  depthAttribute: string = 'depth'
): { min: number; max: number } | null {
  const features = source.getFeatures();
  if (features.length === 0) return null;

  let min = Number.POSITIVE_INFINITY;
  let max = Number.NEGATIVE_INFINITY;
  let hasElevation = false;

  features.forEach((feature) => {
    const elevation = getFeatureElevation(feature, elevationAttribute, depthAttribute);
    if (elevation !== null) {
      min = Math.min(min, elevation);
      max = Math.max(max, elevation);
      hasElevation = true;
    }
  });

  return hasElevation ? { min, max } : null;
}

/**
 * Switch a layer between 2D and 3D modes
 * @param layer The layer to switch
 * @param mode The view mode to switch to
 * @param verticalScale Exaggeration factor for elevations
 */
export function switchLayerViewMode(
  layer: BaseLayer,
  mode: ViewMode,
  verticalScale: number = 1
): void {
  // Check if this layer supports 3D
  const supports3D = layer.get('supports3D') === true;
  if (!supports3D && mode !== '2d') {
    safeLog.warn(`Layer ${layer.get('name')} does not support 3D visualization`);
    return;
  }

  // Update layer properties
  layer.set('visualMode', mode);

  if (mode === '3d') {
    layer.set('render3D', true);
    layer.set('verticalScale', verticalScale);
  } else {
    layer.set('render3D', false);
  }

  // For vector layers, we might want to update the style to match the mode
  if (layer instanceof VectorLayer) {
    // In a real implementation, you would update the style based on the mode
    // For now, we just log it
    safeLog.info(`Switched layer ${layer.get('name')} to ${mode} mode`);
  }
}
