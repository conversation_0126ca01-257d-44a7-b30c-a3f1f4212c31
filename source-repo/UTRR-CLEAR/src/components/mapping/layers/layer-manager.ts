import OLMap from 'ol/Map.js';
import TileLayer from 'ol/layer/Tile.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import OSM from 'ol/source/OSM.js';
import XYZ from 'ol/source/XYZ.js';
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style.js';
import { safeLog } from '~/lib/error-handler';
import type { ViewMode } from '~/lib/mapping/types';

// Layer configuration interface
export interface LayerConfig {
  id: string;
  name: string;
  type: 'base' | 'utility' | 'annotation' | 'overlay';
  visible: boolean;
  opacity: number;
  zIndex: number;
  style?: Style;
  source?: any;
}

// Utility type configuration
export interface UtilityTypeConfig {
  id: string;
  name: string;
  color: string;
  lineStyle: 'solid' | 'dashed' | 'dotted';
  zIndex: number;
  visible: boolean;
}

export class LayerManager {
  private map: OLMap;
  private layers = new Map<string, TileLayer | VectorLayer<VectorSource>>();
  private utilityTypes = new Map<string, UtilityTypeConfig>();

  constructor(map: OLMap) {
    this.map = map;
    this.initializeDefaultUtilityTypes();
  }

  // Initialize default utility types
  private initializeDefaultUtilityTypes() {
    const defaultTypes: UtilityTypeConfig[] = [
      {
        id: 'electric',
        name: 'Electric',
        color: '#FF0000',
        lineStyle: 'solid',
        zIndex: 5,
        visible: true,
      },
      {
        id: 'gas',
        name: 'Gas',
        color: '#FFFF00',
        lineStyle: 'dashed',
        zIndex: 4,
        visible: true,
      },
      {
        id: 'water',
        name: 'Water',
        color: '#0000FF',
        lineStyle: 'solid',
        zIndex: 3,
        visible: true,
      },
      {
        id: 'sewer',
        name: 'Sewer',
        color: '#008000',
        lineStyle: 'solid',
        zIndex: 2,
        visible: true,
      },
      {
        id: 'telecom',
        name: 'Telecommunications',
        color: '#FF8C00',
        lineStyle: 'dotted',
        zIndex: 6,
        visible: true,
      },
      {
        id: 'fiber',
        name: 'Fiber Optic',
        color: '#8A2BE2',
        lineStyle: 'dashed',
        zIndex: 7,
        visible: true,
      },
    ];

    defaultTypes.forEach((type) => {
      this.utilityTypes.set(type.id, type);
    });
  }

  // Create base map layers
  createBaseLayers(): void {
    // OpenStreetMap layer
    const osmLayer = new TileLayer({
      source: new OSM(),
      zIndex: 0,
    });
    this.addLayer('osm', osmLayer, {
      id: 'osm',
      name: 'OpenStreetMap',
      type: 'base',
      visible: true,
      opacity: 1,
      zIndex: 0,
    });

    // Satellite imagery (example using a public tile service)
    const satelliteLayer = new TileLayer({
      source: new XYZ({
        url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        attributions: 'Tiles © Esri',
      }),
      visible: false,
      zIndex: 0,
    });
    this.addLayer('satellite', satelliteLayer, {
      id: 'satellite',
      name: 'Satellite',
      type: 'base',
      visible: false,
      opacity: 1,
      zIndex: 0,
    });
  }

  // Create utility layers for each utility type
  createUtilityLayers(): void {
    this.utilityTypes.forEach((config: any, typeId: string) => {
      const vectorSource = new VectorSource();
      const vectorLayer = new VectorLayer({
        source: vectorSource,
        zIndex: config.zIndex,
        visible: config.visible,
        style: this.createUtilityStyle(config),
      });

      this.addLayer(`utility_${typeId}`, vectorLayer, {
        id: `utility_${typeId}`,
        name: config.name,
        type: 'utility',
        visible: config.visible,
        opacity: 1,
        zIndex: config.zIndex,
      });
    });
  }

  // Create annotation/drawing layers
  createAnnotationLayers(): void {
    // Drawing layer for user annotations
    const drawingSource = new VectorSource();
    const drawingLayer = new VectorLayer({
      source: drawingSource,
      zIndex: 100,
      style: new Style({
        stroke: new Stroke({
          color: '#1976d2',
          width: 2,
        }),
        fill: new Fill({
          color: 'rgba(25, 118, 210, 0.1)',
        }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({ color: '#1976d2' }),
          stroke: new Stroke({ color: '#ffffff', width: 1 }),
        }),
      }),
    });

    this.addLayer('drawings', drawingLayer, {
      id: 'drawings',
      name: 'Drawings',
      type: 'annotation',
      visible: true,
      opacity: 1,
      zIndex: 100,
    });

    // Conflict indicators layer
    const conflictSource = new VectorSource();
    const conflictLayer = new VectorLayer({
      source: conflictSource,
      zIndex: 200,
      style: new Style({
        image: new CircleStyle({
          radius: 8,
          fill: new Fill({ color: '#ff4444' }),
          stroke: new Stroke({ color: '#ffffff', width: 2 }),
        }),
      }),
    });

    this.addLayer('conflicts', conflictLayer, {
      id: 'conflicts',
      name: 'Conflicts',
      type: 'overlay',
      visible: true,
      opacity: 1,
      zIndex: 200,
    });
  }

  // Create style for utility types
  private createUtilityStyle(config: UtilityTypeConfig): Style {
    let lineDash: number[] = [];
    if (config.lineStyle === 'dashed') {
      lineDash = [10, 5];
    } else if (config.lineStyle === 'dotted') {
      lineDash = [2, 2];
    }

    return new Style({
      stroke: new Stroke({
        color: config.color,
        width: 3,
        lineDash: lineDash,
      }),
      fill: new Fill({
        color: config.color + '20', // 20% opacity
      }),
      image: new CircleStyle({
        radius: 6,
        fill: new Fill({ color: config.color }),
        stroke: new Stroke({ color: '#ffffff', width: 2 }),
      }),
    });
  }

  // Add layer to map and layer registry
  private addLayer(
    id: string,
    layer: TileLayer | VectorLayer<VectorSource>,
    config: LayerConfig
  ): void {
    this.layers.set(id, layer);
    this.map.addLayer(layer);
  }

  // Get layer by ID
  getLayer(id: string): TileLayer | VectorLayer<VectorSource> | undefined {
    return this.layers.get(id);
  }

  // Get utility layer by utility type
  getUtilityLayer(utilityType: string): VectorLayer<VectorSource> | undefined {
    return this.getLayer(`utility_${utilityType}`) as VectorLayer<VectorSource>;
  }

  // Toggle layer visibility
  setLayerVisibility(id: string, visible: boolean): void {
    const layer = this.layers.get(id);
    if (layer) {
      layer.setVisible(visible);

      // Update utility type config if it's a utility layer
      if (id.startsWith('utility_')) {
        const utilityType = id.replace('utility_', '');
        const config = this.utilityTypes.get(utilityType);
        if (config) {
          config.visible = visible;
        }
      }
    }
  }

  // Set layer opacity
  setLayerOpacity(id: string, opacity: number): void {
    const layer = this.layers.get(id);
    if (layer) {
      layer.setOpacity(opacity);
    }
  }

  // Set layer z-index
  setLayerZIndex(id: string, zIndex: number): void {
    const layer = this.layers.get(id);
    if (layer) {
      layer.setZIndex(zIndex);
    }
  }

  // Get all layers
  getAllLayers(): Array<{ id: string; layer: TileLayer | VectorLayer<VectorSource> }> {
    return Array.from(this.layers.entries()).map(([id, layer]) => ({ id, layer }));
  }

  // Get utility types configuration
  getUtilityTypes(): UtilityTypeConfig[] {
    return Array.from(this.utilityTypes.values());
  }

  // Update utility type configuration
  updateUtilityType(id: string, updates: Partial<UtilityTypeConfig>): void {
    const config = this.utilityTypes.get(id);
    if (config) {
      Object.assign(config, updates);

      // Update the layer style if needed
      const layer = this.getUtilityLayer(id);
      if (layer && (updates.color || updates.lineStyle)) {
        layer.setStyle(this.createUtilityStyle(config));
      }

      // Update visibility if changed
      if (updates.visible !== undefined) {
        this.setLayerVisibility(`utility_${id}`, updates.visible);
      }

      // Update z-index if changed
      if (updates.zIndex !== undefined) {
        this.setLayerZIndex(`utility_${id}`, updates.zIndex);
      }
    }
  }

  // Switch base layer
  switchBaseLayer(newBaseLayerId: string): void {
    // Hide all base layers
    this.layers.forEach((layer: any, id: string) => {
      if (id === 'osm' || id === 'satellite') {
        layer.setVisible(false);
      }
    });

    // Show the selected base layer
    const selectedLayer = this.layers.get(newBaseLayerId);
    if (selectedLayer) {
      selectedLayer.setVisible(true);
    }
  }

  // Remove layer
  removeLayer(id: string): void {
    const layer = this.layers.get(id);
    if (layer) {
      this.map.removeLayer(layer);
      this.layers.delete(id);
    }
  }


  // Clear all features from a layer
  clearLayerFeatures(id: string): void {
    const layer = this.getLayer(id) as VectorLayer<VectorSource>;
    if (layer && layer instanceof VectorLayer) {
      const source = layer.getSource();
      if (source) {
        source.clear();
      }
    }
  }

  // Get layer visibility state
  isLayerVisible(id: string): boolean {
    const layer = this.layers.get(id);
    return layer ? layer.getVisible() : false;
  }

  // Get layer opacity
  getLayerOpacity(id: string): number {
    const layer = this.layers.get(id);
    return layer ? layer.getOpacity() : 1;
  }

  // Initialize all default layers
  initializeAllLayers(): void {
    this.createBaseLayers();
    this.createUtilityLayers();
    this.createAnnotationLayers();
  }

  // Switch view mode for 3D visualization
  switchViewMode(viewMode: '2d' | '3d' | 'cross-section'): void {
    // Implementation for switching between view modes
    safeLog.info(`Switching to ${viewMode} view mode`);
    // This would typically involve showing/hiding layers or changing rendering modes
  }

  // Get vector layer by type
  getVectorLayer(type: string): VectorLayer<VectorSource> | undefined {
    const layer = this.layers.get(`utility_${type}`) || this.layers.get(type);
    return layer instanceof VectorLayer ? layer : undefined;
  }

  // Dispose of all layers and cleanup
  dispose(): void {
    this.layers.forEach((layer: any) => {
      this.map.removeLayer(layer);
    });
    this.layers.clear();
    this.utilityTypes.clear();
  }
}
