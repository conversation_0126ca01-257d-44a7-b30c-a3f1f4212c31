import { Geometry, LineString, Point, Polygon } from 'ol/geom.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import Feature from 'ol/Feature.js';
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style.js';
import type { ViewMode } from '~/lib/mapping/types';
import { getFeatureElevation } from './three-d-layer';
import { CoordinateTransformer } from '~/lib/mapping/coordinate-utils';

/**
 * Options for creating a vector-based 3D layer
 */
export interface Vector3DLayerOptions {
  source: VectorSource;
  name: string;
  title: string;
  style?: Style | Style[] | ((feature: any) => Style | Style[]);
  visible?: boolean;
  zIndex?: number;
  verticalScale?: number;
  elevationAttribute?: string;
  depthAttribute?: string;
  elevationOffset?: number;
}

/**
 * Creates a vector layer with 3D capabilities
 * This uses standard OpenLayers VectorLayer but adds properties for 3D rendering
 * and styles that adjust based on elevation/depth
 */
export function createVector3DLayer(options: Vector3DLayerOptions): VectorLayer<VectorSource> {
  const {
    source,
    name,
    title,
    visible = true,
    zIndex = 10,
    verticalScale = 1,
    elevationAttribute = 'elevation',
    depthAttribute = 'depth',
    elevationOffset = 0,
    style,
  } = options;

  // Create a vector layer with 3D properties
  const vectorLayer = new VectorLayer({
    source,
    visible,
    zIndex,
    properties: {
      name,
      title,
      type: 'vector',
      supports3D: true,
      render3D: false, // Initially in 2D mode
      visualMode: '2d', // Initially in 2D mode
      verticalScale,
      elevationAttribute,
      depthAttribute,
      elevationOffset,
    },
  });

  // Set style function that can use elevation data
  if (style) {
    vectorLayer.setStyle(style);
  } else {
    // Default style that can vary based on elevation
    vectorLayer.setStyle((feature) => {
      const featureObj = feature instanceof Feature ? feature : new Feature();
      const elevation = getFeatureElevation(featureObj, elevationAttribute, depthAttribute);

      // Normalize elevation to a color (example: deeper = darker blue)
      const elevationColor =
        elevation !== null
          ? normalizeElevationToColor(elevation, -20, 0) // -20m to 0m range
          : [0, 0, 255, 1]; // Default blue

      return new Style({
        stroke: new Stroke({
          color: `rgba(${elevationColor[0]}, ${elevationColor[1]}, ${elevationColor[2]}, ${elevationColor[3]})`,
          width: 2,
        }),
        fill: new Fill({
          color: `rgba(${elevationColor[0]}, ${elevationColor[1]}, ${elevationColor[2]}, 0.3)`,
        }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({
            color: `rgba(${elevationColor[0]}, ${elevationColor[1]}, ${elevationColor[2]}, ${elevationColor[3]})`,
          }),
        }),
      });
    });
  }

  return vectorLayer;
}

/**
 * Update vector layer with 3D elevation data
 * @param layer The vector layer to update
 * @param elevationAttribute The attribute name for elevation data
 * @param depthAttribute The attribute name for depth data
 */
export function updateVector3DElevation(
  layer: VectorLayer<VectorSource>,
  elevationAttribute: string = 'elevation',
  depthAttribute: string = 'depth'
): void {
  const source = layer.getSource();
  const features = source?.getFeatures() || [];

  // Process each feature to ensure it has elevation data
  features.forEach((feature) => {
    // Skip if feature already has elevation data
    if (feature.get(elevationAttribute) !== undefined) return;

    // Try to get elevation from depth
    const depth = feature.get(depthAttribute);
    if (depth !== undefined && typeof depth === 'number') {
      feature.set(elevationAttribute, -depth); // Convert depth to elevation (negative)
      return;
    }

    // Try to get elevation from geometry
    const geometry = feature.getGeometry();
    if (!geometry) return;

    if (geometry instanceof Point) {
      const coords = geometry.getCoordinates();
      const z = CoordinateTransformer.getZCoordinate(coords);
      if (z !== null) {
        feature.set(elevationAttribute, z);
      }
    } else if (geometry instanceof LineString) {
      const coords = geometry.getCoordinates();
      if (coords) {
        updateLineElevation(feature, coords, elevationAttribute);
      }
    } else if (geometry instanceof Polygon) {
      const coordsArray = geometry.getCoordinates();
      if (coordsArray.length > 0) {
        const coords = coordsArray[0]; // Exterior ring
        if (coords) {
          updateLineElevation(feature, coords, elevationAttribute);
        }
      }
    }
  });

  // Mark layer as having 3D data
  layer.set('supports3D', true);
}

/**
 * Update a line feature with elevation data from its coordinates
 */
function updateLineElevation(
  feature: Feature<Geometry>,
  coords: any[],
  elevationAttribute: string
): void {
  if (coords.length === 0) return;

  // Check if any coordinates have Z values
  const hasZ = coords.some((coord: any) => CoordinateTransformer.hasZCoordinate(coord));

  if (hasZ) {
    // Calculate average elevation
    let sumZ = 0;
    let count = 0;

    coords.forEach((coord) => {
      const z = CoordinateTransformer.getZCoordinate(coord);
      if (z !== null) {
        sumZ += z;
        count++;
      }
    });

    if (count > 0) {
      feature.set(elevationAttribute, sumZ / count);
    }

    // Also set start and end elevations if they exist
    const firstZ = CoordinateTransformer.getZCoordinate(coords[0]);
    if (firstZ !== null) {
      feature.set('startElevation', firstZ);
    }

    const lastZ = CoordinateTransformer.getZCoordinate(coords[coords.length - 1]);
    if (lastZ !== null) {
      feature.set('endElevation', lastZ);
    }
  }
}

/**
 * Normalize an elevation value to an RGBA color array
 * @param elevation The elevation value to normalize
 * @param min The minimum expected elevation
 * @param max The maximum expected elevation
 * @returns RGBA color values in [r, g, b, a] format (values 0-255 for RGB, 0-1 for alpha)
 */
function normalizeElevationToColor(
  elevation: number,
  min: number = -30, // Default min depth -30m
  max: number = 0 // Default max depth 0m (surface)
): [number, number, number, number] {
  // Clamp elevation to range
  const clampedElevation = Math.max(min, Math.min(max, elevation));

  // Normalize to 0-1 range
  const normalized = (clampedElevation - min) / (max - min);

  // Create a color gradient from dark blue (deep) to light blue (shallow)
  const r = Math.floor(0 + normalized * 100); // 0-100
  const g = Math.floor(50 + normalized * 150); // 50-200
  const b = Math.floor(200 + normalized * 55); // 200-255

  return [r, g, b, 1]; // Full opacity
}

/**
 * Convert 2D line coordinates to 3D with interpolated elevations
 * @param coords 2D coordinates array
 * @param startElevation Elevation at start point
 * @param endElevation Elevation at end point
 * @returns Array of 3D coordinates with interpolated elevations
 */
export function interpolateLineElevation(
  coords: any[],
  startElevation: number,
  endElevation: number
): any[] {
  // Interpolate elevation between start and end points
  const result = coords.map((coord, index) => {
    const ratio = index / (coords.length - 1);
    const interpolatedElevation = startElevation + (endElevation - startElevation) * ratio;
    return [...coord, interpolatedElevation];
  });
  return result;
}

/**
 * Get total 3D length of a linestring feature
 * @param feature The linestring feature
 * @param elevationAttribute Attribute containing elevation values
 * @param depthAttribute Attribute containing depth values
 * @returns The 3D length in map units, or the 2D length if no elevation data
 */
export function get3DLength(
  feature: Feature<Geometry>,
  elevationAttribute: string = 'elevation',
  depthAttribute: string = 'depth'
): number {
  const geometry = feature.getGeometry();
  if (!(geometry instanceof LineString)) {
    return 0;
  }

  const coords = geometry.getCoordinates();
  if (coords.length < 2) {
    return 0;
  }

  // If we have start and end elevations, use those
  const startElevation = feature.get('startElevation');
  const endElevation = feature.get('endElevation');

  if (typeof startElevation === 'number' && typeof endElevation === 'number') {
    // Get 3D coordinates with interpolated elevations
    const coords3D = interpolateLineElevation(coords, startElevation, endElevation);

    // Calculate 3D length by summing segments
    let length3D = 0;
    for (let i = 1; i < coords3D.length; i++) {
      const segment = CoordinateTransformer.calculate3DDistance(coords3D[i - 1], coords3D[i]);
      length3D += segment;
    }

    return length3D;
  }

  // Otherwise, use feature elevation if available
  const elevation = getFeatureElevation(feature, elevationAttribute, depthAttribute);
  if (elevation !== null) {
    // With uniform elevation, 3D length is the same as 2D length
    return geometry.getLength();
  }

  // Fall back to 2D length
  return geometry.getLength();
}
