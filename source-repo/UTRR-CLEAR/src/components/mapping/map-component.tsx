'use client';

import { useEffect, useRef, useState } from 'react';
import { Map, View } from 'ol';
import { Tile as TileLayer, Vector as VectorLayer } from 'ol/layer.js';
import { OSM, Vector as VectorSource } from 'ol/source.js';
import { fromLonLat } from 'ol/proj.js';
import { defaults as defaultControls, ScaleLine, MousePosition } from 'ol/control.js';
import { createStringXY } from 'ol/coordinate.js';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Map as MapIcon, Layers, ZoomIn, ZoomOut, Home, Settings } from 'lucide-react';
import 'ol/ol.css';

interface MapComponentProps {
  projectId?: string;
  className?: string;
  showControls?: boolean;
  height?: string;
}

export function MapComponent({
  projectId,
  className = '',
  showControls = true,
  height = '400px',
}: MapComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentZoom, setCurrentZoom] = useState(10);
  const [layerCount, setLayerCount] = useState(2); // Base layers

  useEffect(() => {
    if (!mapRef.current) return;

    // Create base layers
    const osmLayer = new TileLayer({
      source: new OSM(),
      properties: { name: 'OpenStreetMap' },
    });

    // Create utility vector layer (for future utility data)
    const utilitySource = new VectorSource();
    const utilityLayer = new VectorLayer({
      source: utilitySource,
      properties: { name: 'Utilities' },
    });

    // Initialize map
    const map = new Map({
      target: mapRef.current,
      layers: [osmLayer, utilityLayer],
      view: new View({
        center: fromLonLat([-86.1581, 39.7684]), // Indianapolis, IN
        zoom: 10,
      }),
      controls: defaultControls().extend([
        new ScaleLine(),
        new MousePosition({
          coordinateFormat: createStringXY(4),
          projection: 'EPSG:4326',
          className: 'custom-mouse-position',
        }),
      ]),
    });

    // Set up event listeners
    map.getView().on('change:resolution', () => {
      const zoom = map.getView().getZoom();
      if (zoom !== undefined) {
        setCurrentZoom(Math.round(zoom));
      }
    });

    mapInstanceRef.current = map;
    setIsLoaded(true);

    // Cleanup
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setTarget(undefined);
        mapInstanceRef.current = null;
      }
    };
  }, []);

  const zoomIn = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      view.animate({ zoom: view.getZoom()! + 1, duration: 250 });
    }
  };

  const zoomOut = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      view.animate({ zoom: view.getZoom()! - 1, duration: 250 });
    }
  };

  const zoomToExtent = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      view.animate({
        center: fromLonLat([-86.1581, 39.7684]),
        zoom: 10,
        duration: 500,
      });
    }
  };

  return (
    <Card className={className}>
      {showControls && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MapIcon className="h-5 w-5" />
              <CardTitle>Project Map</CardTitle>
              {projectId && <Badge variant="outline">Project {projectId}</Badge>}
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                <Layers className="h-3 w-3 mr-1" />
                {layerCount} layers
              </Badge>
              <Badge variant="secondary" className="text-xs">
                Zoom: {currentZoom}
              </Badge>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className="p-0">
        <div className="relative">
          {/* Map Container */}
          <div ref={mapRef} className="w-full border rounded-b-lg" style={{ height }} />

          {/* Loading State */}
          {!isLoaded && (
            <div className="absolute inset-0 bg-muted/50 flex items-center justify-center rounded-b-lg">
              <div className="text-center">
                <MapIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-pulse" />
                <p className="text-sm text-muted-foreground">Loading map...</p>
              </div>
            </div>
          )}

          {/* Map Controls */}
          {showControls && isLoaded && (
            <div className="absolute top-4 right-4 flex flex-col gap-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0 bg-background"
                onClick={zoomIn}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0 bg-background"
                onClick={zoomOut}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0 bg-background"
                onClick={zoomToExtent}
              >
                <Home className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Status Bar */}
          {showControls && isLoaded && (
            <div className="absolute bottom-2 left-2 flex items-center gap-2">
              <Badge variant="outline" className="text-xs bg-background">
                Ready
              </Badge>
              {projectId && (
                <Badge variant="outline" className="text-xs bg-background">
                  0 utilities loaded
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
