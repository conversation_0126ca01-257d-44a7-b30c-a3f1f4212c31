import React, { useState, useEffect } from 'react';
import { cn } from '~/lib/utils';
import { Button } from '~/components/ui/button';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import {
  ChevronRight,
  ChevronLeft,
  Globe,
  Layers,
  PenTool,
  MapPin,
  LifeBuoy,
  Pipette,
  Ruler,
  Move,
  PenLine,
  FileSymlink,
  Database,
  Eraser,
  CircleDot,
  Wrench,
  ArrowDownToLine,
  ArrowUpToLine,
  Pilcrow,
  Map,
  MoreHorizontal,
  Square,
  Trash2,
  RotateCcw,
  MousePointer,
  Plug,
  AlertTriangle,
  LineChart,
  LayoutGrid,
  Milestone,
  ChevronDown,
} from 'lucide-react';
import { UTILITY_TYPES, INSTALLATION_TYPES } from './utils/utility-specs';

// Coordinate systems
type CoordinateSystem = {
  id: number;
  name: string;
  epsg?: number;
};

export interface ProjectUtility {
  id: number;
  name: string;
  type: string;
  owner: string;
}

export interface UtilitySelections {
  coordinateSystem: CoordinateSystem | null;
  projectUtility: ProjectUtility | null;
  utilityType: string | null;
  installationType: string | null;
  material: string | null;
  size: string | null;
  parameters: Record<string, string>;
  installationDetails: Record<string, string>;
}

interface BreadcrumbMenuProps {
  onUtilitySelectionsChange: (selections: UtilitySelections) => void;
  onDrawingModeChange: (mode: string | null) => void;
  currentMode: string | null;
  hasBoundary: boolean;
  showToolbar?: boolean; // Whether to show toolbar
  showOptions?: boolean; // Whether to show utility options
  projectUtilities?: ProjectUtility[]; // Project utilities for selection
  coordinateSystems?: CoordinateSystem[]; // Available coordinate systems
  selectedCoordinateSystem?: number | null; // Currently selected coordinate system ID
  coordinateSystemLocked?: boolean; // Whether coordinate system is locked
  onCoordinateSystemChange?: (value: string) => void; // Callback for coordinate system change
  onToggleCoordinateSystemEdit?: () => void; // Callback to toggle coordinate system edit mode
  onResetBoundary?: () => void; // Callback to reset the project boundary
  onBoundaryModeToggle?: (mode: boolean) => void; // Callback to toggle boundary drawing mode
  boundaryModeActive?: boolean; // Whether boundary drawing mode is active
}

const BreadcrumbMenu: React.FC<BreadcrumbMenuProps> = ({
  onUtilitySelectionsChange,
  onDrawingModeChange,
  currentMode,
  hasBoundary,
  showToolbar = true,
  showOptions = true,
  projectUtilities = [],
  coordinateSystems = [],
  selectedCoordinateSystem = null,
  coordinateSystemLocked = false,
  onCoordinateSystemChange,
  onToggleCoordinateSystemEdit,
  onResetBoundary,
  onBoundaryModeToggle,
  boundaryModeActive = false,
}) => {
  // State for toolbar dock/undock
  const [isToolbarDocked, setIsToolbarDocked] = useState(false);
  const [showToolbarTemp, setShowToolbarTemp] = useState(false);

  // Timeout state for toolbar visibility
  const [hideToolbarTimeout, setHideToolbarTimeout] = useState<NodeJS.Timeout | null>(null);

  // Initialize with empty selections
  const [selections, setSelections] = useState<UtilitySelections>({
    coordinateSystem: null,
    projectUtility: null,
    utilityType: null,
    installationType: null,
    material: null,
    size: null,
    parameters: {},
    installationDetails: {},
  });

  // Determine which steps to show - keep the proper sequence
  const showProjectUtilityStep = selections.coordinateSystem !== null;
  const showUtilityTypeStep = showProjectUtilityStep && selections.projectUtility !== null;
  const showInstallationTypeStep = showUtilityTypeStep && selections.utilityType !== null;
  const showMaterialStep = showInstallationTypeStep && selections.installationType !== null;
  const showSizeStep = showMaterialStep && selections.material !== null;
  const showParameterSteps = showSizeStep && selections.size !== null;

  // State to track which step's dropdown is currently open (if any)
  const [openDropdownStep, setOpenDropdownStep] = useState<string | null>(null);

  // Current utility details based on selection
  const currentUtilityDetails = selections.utilityType
    ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]
    : undefined;

  // Current installation details based on selection
  const currentInstallationDetails = selections.installationType
    ? INSTALLATION_TYPES[selections.installationType as keyof typeof INSTALLATION_TYPES]
    : undefined;

  // Function to toggle a dropdown step - close it if it's open, open it if it's closed
  // and close all other dropdowns
  const toggleDropdownStep = (stepName: string) => {
    if (openDropdownStep === stepName) {
      setOpenDropdownStep(null);
    } else {
      setOpenDropdownStep(stepName);
    }
  };

  // Notify parent component when selections change
  useEffect(() => {
    onUtilitySelectionsChange(selections);
  }, [selections, onUtilitySelectionsChange]);

  // Drawing mode options
  const drawingModes = [
    { id: 'pan', icon: <Move className="h-4 w-4" />, label: 'Pan' },
    {
      id: 'draw',
      icon: <PenTool className="h-4 w-4" />,
      label: 'Draw',
      hasSubOptions: true,
      subOptions: [
        { id: 'draw-polyline', icon: <LineChart className="h-4 w-4" />, label: 'Polyline' },
        {
          id: 'draw-easement',
          icon: <LayoutGrid className="h-4 w-4" />,
          label: 'Easement',
          requiresUtilitySelection: true,
        },
      ],
    },
    {
      id: 'utility-symbols',
      icon: <Milestone className="h-4 w-4" />,
      label: 'Utility Symbols',
      requiresUtilitySelection: true,
    }, // Added utility symbols tool for poles, manholes, etc.
    { id: 'select', icon: <MousePointer className="h-4 w-4" />, label: 'Select' },
    { id: 'measure', icon: <Ruler className="h-4 w-4" />, label: 'Measure' },
    { id: 'edit', icon: <PenTool className="h-4 w-4" />, label: 'Edit' },
    { id: 'snap', icon: <FileSymlink className="h-4 w-4" />, label: 'Snap' },
    { id: 'erase', icon: <Eraser className="h-4 w-4" />, label: 'Erase' },
    {
      id: 'conflict',
      icon: <AlertTriangle className="h-4 w-4" />,
      label: 'Conflict',
      requiresUtilitySelection: true,
    }, // Moved conflict icon to bottom
  ];

  // Track which modes have expanded sub-options
  const [expandedModes, setExpandedModes] = useState<Record<string, boolean>>({});

  // Handle coordinate system selection
  const handleCoordinateSystemChange = (systemId: string) => {
    // If we have a parent callback, use it
    if (onCoordinateSystemChange) {
      onCoordinateSystemChange(systemId);
    } else {
      // Otherwise use local state (fallback for backward compatibility)
      const selectedSystem =
        coordinateSystems.find((system: any) => system.id.toString() === systemId) || null;
      setSelections((prev) => ({
        ...prev,
        coordinateSystem: selectedSystem,
        projectUtility: null,
        utilityType: null,
        installationType: null,
        material: null,
        size: null,
        parameters: {},
        installationDetails: {},
      }));
    }
  };

  // Effect to sync selectedCoordinateSystem with local state
  useEffect(() => {
    if (selectedCoordinateSystem) {
      const selectedSystem =
        coordinateSystems.find((system: any) => system.id === selectedCoordinateSystem) || null;
      if (selectedSystem && !selections.coordinateSystem) {
        setSelections((prev) => ({
          ...prev,
          coordinateSystem: selectedSystem,
        }));
      }
    }
  }, [selectedCoordinateSystem, coordinateSystems, selections.coordinateSystem]);

  // Handle project utility selection
  const handleProjectUtilityChange = (utilityId: string) => {
    const selectedUtility =
      projectUtilities.find((util: any) => util.id.toString() === utilityId) || null;
    setSelections((prev) => ({
      ...prev,
      projectUtility: selectedUtility,
      utilityType: selectedUtility?.type || null,
      installationType: null,
      material: null,
      size: null,
      parameters: {},
      installationDetails: {},
    }));
  };

  // Handle utility type selection
  const handleUtilityTypeChange = (value: string) => {
    setSelections((prev) => ({
      ...prev,
      utilityType: value,
      installationType: null,
      material: null,
      size: null,
      parameters: {},
    }));
  };

  // Handle installation type selection
  const handleInstallationTypeChange = (value: string) => {
    setSelections((prev) => ({
      ...prev,
      installationType: value,
      installationDetails: {},
    }));
  };

  // Handle material selection
  const handleMaterialChange = (value: string) => {
    setSelections((prev) => ({
      ...prev,
      material: value,
    }));
  };

  // Handle size selection
  const handleSizeChange = (value: string) => {
    setSelections((prev) => ({
      ...prev,
      size: value,
    }));
  };

  // Handle parameter selection
  const handleParameterChange = (paramName: string, value: string) => {
    setSelections((prev) => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramName]: value,
      },
    }));
  };

  // Handle installation detail selection
  const handleInstallationDetailChange = (detailName: string, value: string) => {
    setSelections((prev) => ({
      ...prev,
      installationDetails: {
        ...prev.installationDetails,
        [detailName]: value,
      },
    }));
  };

  // Handle drawing mode selection
  const handleDrawingModeChange = (mode: string) => {
    onDrawingModeChange(mode === currentMode ? null : mode);
  };

  // Check if utilities can be drawn based on boundary and selections
  const canDrawUtility =
    hasBoundary &&
    selections.coordinateSystem &&
    selections.projectUtility &&
    selections.utilityType &&
    selections.installationType &&
    selections.material &&
    selections.size;

  // Render selection item or icon based on selection status
  const renderSelectionItem = (
    step: number,
    icon: React.ReactNode,
    label: string,
    isSelected: boolean,
    isSelectable: boolean,
    selectedValue: string | null | undefined,
    color?: string,
    tooltipText?: string,
    onClick?: () => void,
    onSelect?: (value: string) => void,
    options?: React.ReactNode
  ) => {
    // Check if this is the current step in the sequence - when previous step is completed but this one isn't
    const isCurrentStep = !isSelected && isSelectable;

    return (
      <div className="mb-4">
        {isSelected ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="flex items-center justify-center w-10 h-10 rounded-full text-white font-bold cursor-pointer"
                  style={{ backgroundColor: color || '#888' }}
                  onClick={onClick}
                >
                  {selectedValue?.charAt(0).toUpperCase() || '?'}
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{tooltipText || label}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : isSelectable ? (
          <div className="relative">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center" onClick={onClick}>
                    <div className="w-10 h-10 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700">
                      {icon}
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{label} (click to select)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {options}
          </div>
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-10 h-10 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full opacity-70">
                  {icon}
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>Complete previous steps first</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    );
  };

  // Function to toggle toolbar visibility
  const toggleToolbar = () => {
    setIsToolbarDocked(!isToolbarDocked);
    setShowToolbarTemp(false);
  };

  // Main render method
  return (
    <div
      className="absolute left-[-2px] top-1/2 transform -translate-y-1/2 z-50 flex"
      onMouseLeave={() => {
        if (!isToolbarDocked) {
          // When leaving, create a longer delay before hiding toolbar
          const timeout = setTimeout(() => setShowToolbarTemp(false), 800);
          setHideToolbarTimeout(timeout);
        }
      }}
    >
      {/* Vertical Breadcrumb Menu */}
      <div className="flex flex-col items-center bg-white/80 dark:bg-gray-950/80 backdrop-blur-sm p-1 rounded-lg shadow-md border border-gray-100 dark:border-gray-800">
        {' '}
        {/* Added glassmorphic effect with reduced padding from p-2 to p-1 */}
        {/* Coordinate system selection */}
        {renderSelectionItem(
          1,
          <Globe className="h-5 w-5" />,
          'Coordinate System',
          !!selections.coordinateSystem,
          true,
          selections.coordinateSystem?.name || null,
          '#666',
          selections.coordinateSystem?.name,
          onToggleCoordinateSystemEdit ||
            (() =>
              setSelections((prev) => ({
                ...prev,
                coordinateSystem: null,
                projectUtility: null,
                utilityType: null,
                installationType: null,
                material: null,
                size: null,
                parameters: {},
                installationDetails: {},
              }))),
          handleCoordinateSystemChange,
          !selections.coordinateSystem && !coordinateSystemLocked && (
            <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
              <h3 className="mb-2 font-medium">Select Coordinate System</h3>
              <Select
                value={
                  (selectedCoordinateSystem
                    ? selectedCoordinateSystem.toString()
                    : (selections.coordinateSystem as any)?.id?.toString()) || ''
                }
                onValueChange={handleCoordinateSystemChange}
                disabled={coordinateSystemLocked}
              >
                <SelectTrigger
                  className={`mb-2 ${coordinateSystemLocked ? 'bg-muted opacity-80' : ''}`}
                >
                  <SelectValue placeholder="Select coordinate system" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {coordinateSystems.map((system: any) => (
                      <SelectItem key={system.id} value={system.id.toString()}>
                        {system.name} {system.epsg ? `(EPSG:${system.epsg})` : ''}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <div className="flex items-center justify-between">
                <p className="text-xs text-gray-500 mt-2">
                  Select the coordinate system to continue
                </p>
                {coordinateSystemLocked && onToggleCoordinateSystemEdit && (
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-7 w-7 mt-2"
                    onClick={onToggleCoordinateSystemEdit}
                    title="Edit coordinate system"
                  >
                    <span className="h-3.5 w-3.5">✏️</span>
                  </Button>
                )}
              </div>
            </div>
          )
        )}
        {/* Project boundary drawing/management */}
        {renderSelectionItem(
          2,
          <Square className="h-5 w-5" />,
          'Project Boundary',
          hasBoundary,
          !!selections.coordinateSystem,
          hasBoundary ? 'Boundary' : null,
          '#4a7c59', // Green color for boundary
          hasBoundary ? 'Project boundary is set' : 'Draw project boundary',
          () => {
            // Toggle dropdown visibility
            if (openDropdownStep === 'project_boundary') {
              setOpenDropdownStep(null);
            } else {
              setOpenDropdownStep('project_boundary');
            }

            // If there's a boundary and the handler exists, only run it when clicking reset explicitly
            // This is now handled in the Reset Boundary button onClick
          },
          () => {}, // No selection handler needed
          openDropdownStep === 'project_boundary' && !!selections.coordinateSystem && (
            <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
              <h3 className="mb-2 font-medium">Project Boundary</h3>
              <div className="flex flex-col gap-2">
                <Button
                  variant={boundaryModeActive ? 'secondary' : 'outline'}
                  size="sm"
                  className="w-full flex items-center"
                  onClick={() => onBoundaryModeToggle && onBoundaryModeToggle(!boundaryModeActive)}
                >
                  <Square className="mr-2 h-4 w-4" />
                  {boundaryModeActive ? 'Cancel Drawing' : 'Draw Boundary'}
                </Button>
                {boundaryModeActive && (
                  <div className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                    Click on the map to add points. Double-click to complete the boundary.
                  </div>
                )}
                {hasBoundary && onResetBoundary && (
                  <Button
                    variant="destructive"
                    size="sm"
                    className="w-full mt-2 flex items-center"
                    onClick={() => {
                      if (
                        window.confirm(
                          'Are you sure you want to reset the project boundary? You will need to redraw the boundary.\n\nNote: Existing utility lines will be preserved.'
                        )
                      ) {
                        onResetBoundary();
                      }
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Reset Boundary
                  </Button>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {hasBoundary
                  ? 'Boundary already exists. You can reset it if needed.'
                  : 'Draw a boundary to define the project area'}
              </p>
            </div>
          )
        )}
        {/* Project utility selection */}
        {renderSelectionItem(
          3,
          <Database className="h-5 w-5" />, // Changed from MapPin to Database for better representation of utility selection
          'Project Utility',
          !!selections.projectUtility,
          showProjectUtilityStep,
          selections.projectUtility?.name || null,
          selections.utilityType
            ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
            : '#888',
          `${selections.projectUtility?.name} (${selections.projectUtility?.owner})`,
          () => {
            // Reset selections if already set (for editing)
            if (selections.projectUtility) {
              setSelections((prev) => ({
                ...prev,
                projectUtility: null,
                utilityType: null,
                installationType: null,
                material: null,
                size: null,
                parameters: {},
                installationDetails: {},
              }));
            }

            // Toggle dropdown
            toggleDropdownStep('project_utility');
          },
          handleProjectUtilityChange,
          showProjectUtilityStep && openDropdownStep === 'project_utility' && (
            <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
              <h3 className="mb-2 font-medium">Select Project Utility</h3>
              <Select
                value={selections.projectUtility?.id.toString() || ''}
                onValueChange={handleProjectUtilityChange}
              >
                <SelectTrigger className="mb-2">
                  <SelectValue placeholder="Select utility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {projectUtilities.map((utility: any) => {
                      const utilityType = utility.type;
                      const utilityDetails =
                        UTILITY_TYPES[utilityType as keyof typeof UTILITY_TYPES];
                      const color = utilityDetails?.color || '#888';
                      return (
                        <SelectItem key={utility.id} value={utility.id.toString()}>
                          <div className="flex items-center">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: color }}
                            />
                            {utility.name} ({utility.owner})
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-2">
                Select a project utility owned by a specific company
              </p>
            </div>
          )
        )}
        {/* Installation type selection */}
        {renderSelectionItem(
          4,
          <Plug className="h-5 w-5" />, // Changed from Layers to Plug for better representation
          'Installation Type',
          !!selections.installationType,
          showInstallationTypeStep,
          selections.installationType
            ? INSTALLATION_TYPES[selections.installationType as keyof typeof INSTALLATION_TYPES]
                ?.name
            : null,
          selections.utilityType
            ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
            : '#888',
          selections.installationType === 'underground'
            ? 'Underground'
            : selections.installationType === 'aerial'
              ? 'Aerial'
              : 'Installation Type',
          () => {
            // Reset if already set
            if (selections.installationType) {
              setSelections((prev) => ({
                ...prev,
                installationType: null,
                material: null,
                size: null,
                parameters: {},
                installationDetails: {},
              }));
            }

            // Toggle dropdown and close any previously open dropdown
            toggleDropdownStep('installation_type');
          },
          handleInstallationTypeChange,
          showInstallationTypeStep && openDropdownStep === 'installation_type' && (
            <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
              <h3 className="mb-2 font-medium">Select Installation Type</h3>
              <Select
                value={selections.installationType || ''}
                onValueChange={handleInstallationTypeChange}
              >
                <SelectTrigger className="mb-2">
                  <SelectValue placeholder="Select installation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {Object.entries(INSTALLATION_TYPES).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-2">
                Choose between underground (dashed lines) or aerial (solid lines)
              </p>
            </div>
          )
        )}
        {/* Material selection */}
        {renderSelectionItem(
          4,
          <Pipette className="h-5 w-5" />, // Changed from LifeBuoy to Pipette for better representation of material
          'Material',
          !!selections.material,
          showMaterialStep,
          selections.material ?? undefined,
          selections.utilityType
            ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
            : '#888',
          selections.material ?? undefined,
          () => {
            // Reset if already selected
            if (selections.material) {
              setSelections((prev) => ({
                ...prev,
                material: null,
                size: null,
                parameters: {},
                installationDetails: {},
              }));
            }

            // Toggle dropdown
            toggleDropdownStep('material');
          },
          handleMaterialChange,
          showMaterialStep && openDropdownStep === 'material' && currentUtilityDetails && (
            <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
              <h3 className="mb-2 font-medium">Select Material</h3>
              <Select value={selections.material || ''} onValueChange={handleMaterialChange}>
                <SelectTrigger className="mb-2">
                  <SelectValue placeholder="Select material" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {currentUtilityDetails.materials.map((material: any) => (
                      <SelectItem key={material} value={material}>
                        {material}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-2">
                Choose the construction material for the utility
              </p>
            </div>
          )
        )}
        {/* Size selection */}
        {renderSelectionItem(
          5,
          <Ruler className="h-5 w-5" />, // Keeping Ruler icon as it's appropriate for size
          'Size',
          !!selections.size,
          showSizeStep,
          selections.size ?? undefined,
          selections.utilityType
            ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
            : '#888',
          selections.size || undefined,
          () => {
            // Reset if already selected
            if (selections.size) {
              setSelections((prev) => ({
                ...prev,
                size: null,
                parameters: {},
                installationDetails: {},
              }));
            }

            // Toggle dropdown
            toggleDropdownStep('size');
          },
          handleSizeChange,
          showSizeStep && openDropdownStep === 'size' && currentUtilityDetails && (
            <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
              <h3 className="mb-2 font-medium">Select Size</h3>
              <Select value={selections.size || ''} onValueChange={handleSizeChange}>
                <SelectTrigger className="mb-2">
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {currentUtilityDetails.sizes.map((size: any) => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-2">
                Specify the diameter or gauge of the utility
              </p>
            </div>
          )
        )}
        {/* Utility parameters */}
        {showParameterSteps &&
          currentUtilityDetails &&
          currentUtilityDetails.parameters.map((param, index) =>
            renderSelectionItem(
              6 + index,
              <CircleDot className="h-5 w-5" />,
              param.name,
              !!selections.parameters[param.name],
              showParameterSteps,
              selections.parameters[param.name] || null,
              selections.utilityType
                ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
                : '#888',
              `${param.name}: ${selections.parameters[param.name]}${param.unit ? ` ${param.unit}` : ''}`,
              () =>
                setSelections((prev) => ({
                  ...prev,
                  parameters: {
                    ...prev.parameters,
                    [param.name]: '',
                  },
                })),
              (value) => handleParameterChange(param.name, value),
              showParameterSteps && !selections.parameters[param.name] && (
                <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
                  <h3 className="mb-2 font-medium">
                    {param.name}
                    {param.unit ? ` (${param.unit})` : ''}
                  </h3>
                  <Select
                    value={selections.parameters[param.name] || ''}
                    onValueChange={(value: string) => handleParameterChange(param.name, value)}
                  >
                    <SelectTrigger className="mb-2">
                      <SelectValue placeholder={`Select ${param.name.toLowerCase()}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {param.options.map((option: any) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500 mt-2">
                    Specify the {param.name.toLowerCase()} for this utility
                  </p>
                </div>
              )
            )
          )}
        {/* Installation-specific details */}
        {showParameterSteps && currentInstallationDetails && (
          <>
            {/* Underground installation methods */}
            {selections.installationType === 'underground' &&
              currentInstallationDetails && 
              'installationMethods' in currentInstallationDetails &&
              currentInstallationDetails.installationMethods &&
              renderSelectionItem(
                10,
                <Wrench className="h-5 w-5" />,
                'Installation Method',
                !!selections.installationDetails['method'],
                showParameterSteps,
                selections.installationDetails['method'] || null,
                selections.utilityType
                  ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
                  : '#888',
                `Method: ${selections.installationDetails['method']}`,
                () =>
                  setSelections((prev) => ({
                    ...prev,
                    installationDetails: {
                      ...prev.installationDetails,
                      method: '',
                    },
                  })),
                (value) => handleInstallationDetailChange('method', value),
                showParameterSteps && !selections.installationDetails['method'] && (
                  <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
                    <h3 className="mb-2 font-medium">Installation Method</h3>
                    <Select
                      value={selections.installationDetails['method'] || ''}
                      onValueChange={(value: string) => handleInstallationDetailChange('method', value)}
                    >
                      <SelectTrigger className="mb-2">
                        <SelectValue placeholder="Select method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {currentInstallationDetails.installationMethods.map((method: string) => (
                            <SelectItem key={method} value={method}>
                              {method}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-2">
                      How the utility was installed underground
                    </p>
                  </div>
                )
              )}

            {/* Underground depth options */}
            {selections.installationType === 'underground' &&
              currentInstallationDetails &&
              'depthOptions' in currentInstallationDetails &&
              currentInstallationDetails.depthOptions &&
              renderSelectionItem(
                11,
                <ArrowDownToLine className="h-5 w-5" />,
                'Depth',
                !!selections.installationDetails['depth'],
                showParameterSteps,
                selections.installationDetails['depth'] || null,
                selections.utilityType
                  ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
                  : '#888',
                `Depth: ${selections.installationDetails['depth']}`,
                () =>
                  setSelections((prev) => ({
                    ...prev,
                    installationDetails: {
                      ...prev.installationDetails,
                      depth: '',
                    },
                  })),
                (value) => handleInstallationDetailChange('depth', value),
                showParameterSteps && !selections.installationDetails['depth'] && (
                  <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
                    <h3 className="mb-2 font-medium">Depth</h3>
                    <Select
                      value={selections.installationDetails['depth'] || ''}
                      onValueChange={(value: string) => handleInstallationDetailChange('depth', value)}
                    >
                      <SelectTrigger className="mb-2">
                        <SelectValue placeholder="Select depth" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {currentInstallationDetails.depthOptions.map((depth: string) => (
                            <SelectItem key={depth} value={depth}>
                              {depth}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-2">How deep the utility is buried</p>
                  </div>
                )
              )}

            {/* Aerial support types */}
            {selections.installationType === 'aerial' &&
              currentInstallationDetails &&
              'supportTypes' in currentInstallationDetails &&
              currentInstallationDetails.supportTypes &&
              renderSelectionItem(
                12,
                <Pilcrow className="h-5 w-5" />,
                'Support Type',
                !!selections.installationDetails['support'],
                showParameterSteps,
                selections.installationDetails['support'] || null,
                selections.utilityType
                  ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
                  : '#888',
                `Support: ${selections.installationDetails['support']}`,
                () =>
                  setSelections((prev) => ({
                    ...prev,
                    installationDetails: {
                      ...prev.installationDetails,
                      support: '',
                    },
                  })),
                (value) => handleInstallationDetailChange('support', value),
                showParameterSteps && !selections.installationDetails['support'] && (
                  <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
                    <h3 className="mb-2 font-medium">Support Type</h3>
                    <Select
                      value={selections.installationDetails['support'] || ''}
                      onValueChange={(value: string) => handleInstallationDetailChange('support', value)}
                    >
                      <SelectTrigger className="mb-2">
                        <SelectValue placeholder="Select support" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {currentInstallationDetails.supportTypes.map((support: string) => (
                            <SelectItem key={support} value={support}>
                              {support}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-2">
                      What structure supports the aerial utility
                    </p>
                  </div>
                )
              )}

            {/* Aerial height options */}
            {selections.installationType === 'aerial' &&
              currentInstallationDetails &&
              'heightOptions' in currentInstallationDetails &&
              currentInstallationDetails.heightOptions &&
              renderSelectionItem(
                13,
                <ArrowUpToLine className="h-5 w-5" />,
                'Height',
                !!selections.installationDetails['height'],
                showParameterSteps,
                selections.installationDetails['height'] || null,
                selections.utilityType
                  ? UTILITY_TYPES[selections.utilityType as keyof typeof UTILITY_TYPES]?.color
                  : '#888',
                `Height: ${selections.installationDetails['height']}`,
                () =>
                  setSelections((prev) => ({
                    ...prev,
                    installationDetails: {
                      ...prev.installationDetails,
                      height: '',
                    },
                  })),
                (value) => handleInstallationDetailChange('height', value),
                showParameterSteps && !selections.installationDetails['height'] && (
                  <div className="absolute left-12 top-0 z-20 bg-white dark:bg-gray-950 p-3 rounded-lg shadow-lg min-w-[250px]">
                    <h3 className="mb-2 font-medium">Height</h3>
                    <Select
                      value={selections.installationDetails['height'] || ''}
                      onValueChange={(value: string) => handleInstallationDetailChange('height', value)}
                    >
                      <SelectTrigger className="mb-2">
                        <SelectValue placeholder="Select height" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {currentInstallationDetails.heightOptions.map((height: string) => (
                            <SelectItem key={height} value={height}>
                              {height}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-2">
                      How high the utility is above ground
                    </p>
                  </div>
                )
              )}
          </>
        )}
        {/* Toolbar toggle button - centered vertically */}
        <div className="mt-4 mb-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-full"
                  onMouseEnter={() => {
                    // Clear any existing hide timeout when entering button
                    if (hideToolbarTimeout) {
                      clearTimeout(hideToolbarTimeout);
                      setHideToolbarTimeout(null);
                    }
                    !isToolbarDocked && setShowToolbarTemp(true);
                  }}
                  onClick={() => {
                    toggleToolbar();
                    if (!isToolbarDocked) {
                      setShowToolbarTemp(true);
                    }
                  }}
                >
                  {isToolbarDocked ? (
                    <ChevronLeft className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{isToolbarDocked ? 'Hide tools' : 'Show tools'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Connector between arrow and toolbar - creates a hover bridge */}
      {!isToolbarDocked && (
        <div
          className="h-12 w-1 bg-transparent"
          onMouseEnter={() => {
            // Clear any existing hide timeout when entering bridge
            if (hideToolbarTimeout) {
              clearTimeout(hideToolbarTimeout);
              setHideToolbarTimeout(null);
            }
            setShowToolbarTemp(true);
          }}
        />
      )}

      {/* Toolbar */}
      {(isToolbarDocked || showToolbarTemp) && ( // Toolbar is now persistent when hovering
        <div
          className="bg-white/80 dark:bg-gray-950/80 backdrop-blur-sm p-2 rounded-lg shadow-md border border-gray-100 dark:border-gray-800"
          onMouseEnter={() => {
            // Clear any existing hide timeout when entering toolbar
            if (hideToolbarTimeout) {
              clearTimeout(hideToolbarTimeout);
              setHideToolbarTimeout(null);
            }
            setShowToolbarTemp(true);
          }}
        >
          <div className="flex flex-col items-center space-y-2">
            {drawingModes.map((mode: any) => (
              <div key={mode.id} className="relative">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={currentMode === mode.id ? 'default' : 'outline'}
                        size="icon"
                        className={cn(
                          'h-10 w-10 rounded-full',
                          (mode.id === 'draw' && !canDrawUtility) ||
                            (mode.requiresUtilitySelection &&
                              !canDrawUtility &&
                              'opacity-50 cursor-not-allowed')
                        )}
                        onClick={() => {
                          // If this has sub-options, toggle them instead
                          if (mode.hasSubOptions) {
                            setExpandedModes((prev) => ({
                              ...prev,
                              [mode.id]: !prev[mode.id],
                            }));
                            return;
                          }

                          // Otherwise handle normally
                          if (
                            (mode.id === 'draw' && !canDrawUtility) ||
                            (mode.requiresUtilitySelection && !canDrawUtility)
                          )
                            return;

                          handleDrawingModeChange(mode.id);
                        }}
                      >
                        {mode.icon}
                        {mode.hasSubOptions && (
                          <div className="absolute -bottom-1 -right-1 bg-gray-100 dark:bg-gray-700 rounded-full p-0.5">
                            <ChevronDown className="h-3 w-3" />
                          </div>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{mode.label}</p>
                      {((mode.id === 'draw' && !canDrawUtility) ||
                        (mode.requiresUtilitySelection && !canDrawUtility)) && (
                        <p className="text-xs text-red-500">Complete utility selection first</p>
                      )}
                      {mode.hasSubOptions && (
                        <p className="text-xs text-blue-500">Click for options</p>
                      )}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Sub-options menu */}
                {mode.hasSubOptions && expandedModes[mode.id] && (
                  <div className="absolute left-12 top-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-2 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 z-[9999]">
                    <div className="flex flex-col gap-2">
                      {mode.subOptions?.map((subOption: any) => (
                        <TooltipProvider key={subOption.id}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant={currentMode === subOption.id ? 'default' : 'outline'}
                                size="sm"
                                className={cn(
                                  'flex items-center gap-2 h-9 px-2 py-1',
                                  subOption.requiresUtilitySelection &&
                                    !canDrawUtility &&
                                    'opacity-50 cursor-not-allowed'
                                )}
                                onClick={() => {
                                  if (subOption.requiresUtilitySelection && !canDrawUtility) return;
                                  handleDrawingModeChange(subOption.id);
                                  // Close the submenu after selection
                                  setExpandedModes((prev) => ({
                                    ...prev,
                                    [mode.id]: false,
                                  }));
                                }}
                              >
                                {subOption.icon}
                                <span className="text-xs">{subOption.label}</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="right">
                              <p>{subOption.label}</p>
                              {subOption.requiresUtilitySelection && !canDrawUtility && (
                                <p className="text-xs text-red-500">
                                  Complete utility selection first
                                </p>
                              )}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BreadcrumbMenu;
