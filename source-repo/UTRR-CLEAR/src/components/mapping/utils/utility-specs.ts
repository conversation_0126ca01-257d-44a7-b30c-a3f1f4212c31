// Define utility types and their properties
export const UTILITY_TYPES = {
  electric: {
    name: 'Electric',
    color: '#ff0000',
    materials: ['Copper', 'Aluminum', 'Fiber'],
    sizes: ['#6 AWG', '#4 AWG', '#2 AWG', '#1/0 AWG', '#2/0 AWG', '#4/0 AWG'],
    standardDepth: { min: 24, max: 48, unit: 'inches' },
    parameters: [
      {
        name: 'Voltage',
        unit: 'kV',
        options: ['12.47', '13.8', '34.5', '69', '138', '230', '345', '500'],
      },
      {
        name: 'Phase',
        unit: '',
        options: ['Single', 'Three'],
      },
    ],
  },
  gas: {
    name: 'Gas',
    color: '#a64dff', // Changed from yellow to magenta for better visibility
    materials: ['Steel', 'Plastic', 'Cast Iron', 'Ductile Iron'],
    sizes: ['2 inch', '4 inch', '6 inch', '8 inch', '12 inch', '16 inch', '24 inch', '36 inch'],
    standardDepth: { min: 36, max: 60, unit: 'inches' },
    parameters: [
      {
        name: 'Pressure',
        unit: 'psi',
        options: ['Low (< 100 psi)', 'Medium (100-300 psi)', 'High (> 300 psi)'],
      },
    ],
  },
  water: {
    name: 'Water',
    color: '#0000ff',
    materials: ['PVC', 'Ductile Iron', 'Cast Iron', 'Concrete', 'Steel'],
    sizes: ['4 inch', '6 inch', '8 inch', '12 inch', '16 inch', '24 inch', '36 inch', '48 inch'],
    standardDepth: { min: 36, max: 60, unit: 'inches' },
    parameters: [
      {
        name: 'Type',
        unit: '',
        options: ['Potable', 'Raw', 'Fire'],
      },
    ],
  },
  sewer: {
    name: 'Sewer',
    color: '#00cc00',
    materials: ['PVC', 'Ductile Iron', 'Concrete', 'Vitrified Clay', 'HDPE'],
    sizes: [
      '4 inch',
      '6 inch',
      '8 inch',
      '12 inch',
      '15 inch',
      '18 inch',
      '24 inch',
      '36 inch',
      '48 inch',
    ],
    standardDepth: { min: 48, max: 120, unit: 'inches' },
    parameters: [
      {
        name: 'Type',
        unit: '',
        options: ['Sanitary', 'Storm', 'Combined'],
      },
    ],
  },
  steam: {
    name: 'Steam',
    color: '#a64dff', // Changed from yellow to magenta for better visibility
    materials: ['Steel', 'Cast Iron'],
    sizes: ['2 inch', '4 inch', '6 inch', '8 inch', '12 inch', '16 inch'],
    standardDepth: { min: 36, max: 60, unit: 'inches' },
    parameters: [
      {
        name: 'Pressure',
        unit: 'psi',
        options: ['Low (< 15 psi)', 'Medium (15-125 psi)', 'High (> 125 psi)'],
      },
    ],
  },
};

// Define installation types and their properties
export const INSTALLATION_TYPES = {
  underground: {
    name: 'Underground',
    lineWeight: 2.0,
    linePattern: 'dashed',
    installationMethods: ['Open Cut', 'Directional Drill', 'Bore & Jack'],
    depthOptions: ['< 3 feet', '3-5 feet', '5-10 feet', '> 10 feet'],
    depthRanges: [
      { label: '< 3 feet', min: 0, max: 36, unit: 'inches' },
      { label: '3-5 feet', min: 36, max: 60, unit: 'inches' },
      { label: '5-10 feet', min: 60, max: 120, unit: 'inches' },
      { label: '> 10 feet', min: 120, max: 600, unit: 'inches' },
    ],
  },
  aerial: {
    name: 'Aerial',
    lineWeight: 1.5,
    linePattern: 'solid',
    supportTypes: ['Pole', 'Tower', 'Bridge Attachment'],
    heightOptions: ['< 20 feet', '20-40 feet', '40-60 feet', '> 60 feet'],
    heightRanges: [
      { label: '< 20 feet', min: 0, max: 240, unit: 'inches' },
      { label: '20-40 feet', min: 240, max: 480, unit: 'inches' },
      { label: '40-60 feet', min: 480, max: 720, unit: 'inches' },
      { label: '> 60 feet', min: 720, max: 1200, unit: 'inches' },
    ],
  },
};

// Define vertical clearance requirements between utility types
export const VERTICAL_CLEARANCE_REQUIREMENTS = {
  electric: {
    electric: 12,
    gas: 24,
    water: 18,
    sewer: 24,
    steam: 24,
  },
  gas: {
    electric: 24,
    gas: 12,
    water: 18,
    sewer: 24,
    steam: 24,
  },
  water: {
    electric: 18,
    gas: 18,
    water: 12,
    sewer: 18,
    steam: 18,
  },
  sewer: {
    electric: 24,
    gas: 24,
    water: 18,
    sewer: 12,
    steam: 24,
  },
  steam: {
    electric: 24,
    gas: 24,
    water: 18,
    sewer: 24,
    steam: 12,
  },
};

export interface DepthRange {
  label: string;
  min: number;
  max: number;
  unit: string;
}

export interface StandardDepth {
  min: number;
  max: number;
  unit: string;
}

export interface UtilityParameter {
  name: string;
  unit: string;
  options: string[];
}

export interface UtilityTypeDetails {
  name: string;
  color: string;
  materials: string[];
  sizes: string[];
  standardDepth: StandardDepth;
  parameters: UtilityParameter[];
}

export interface InstallationTypeDetails {
  name: string;
  lineWeight: number;
  linePattern: string;
  installationMethods?: string[];
  depthOptions?: string[];
  depthRanges?: DepthRange[];
  supportTypes?: string[];
  heightOptions?: string[];
  heightRanges?: DepthRange[];
}

/**
 * Calculates the minimum required vertical clearance between two utility types
 * @param utilityType1 - First utility type
 * @param utilityType2 - Second utility type
 * @returns Minimum required vertical clearance in inches
 */
export function getRequiredVerticalClearance(
  utilityType1: keyof typeof UTILITY_TYPES,
  utilityType2: keyof typeof UTILITY_TYPES
): number {
  if (
    VERTICAL_CLEARANCE_REQUIREMENTS[utilityType1] &&
    VERTICAL_CLEARANCE_REQUIREMENTS[utilityType1][utilityType2] !== undefined
  ) {
    return VERTICAL_CLEARANCE_REQUIREMENTS[utilityType1][utilityType2];
  }

  // Default clearance if not specifically defined
  return 18;
}

/**
 * Checks if two utilities are in vertical conflict based on their types and vertical separation
 * @param utilityType1 - First utility type
 * @param utilityType2 - Second utility type
 * @param verticalSeparation - Vertical distance between utilities in inches
 * @returns Boolean indicating whether utilities are in conflict
 */
export function checkVerticalConflict(
  utilityType1: keyof typeof UTILITY_TYPES,
  utilityType2: keyof typeof UTILITY_TYPES,
  verticalSeparation: number
): boolean {
  const requiredClearance = getRequiredVerticalClearance(utilityType1, utilityType2);
  return verticalSeparation < requiredClearance;
}

/**
 * Gets the recommended depth range for a utility type
 * @param utilityType - Utility type
 * @returns Recommended depth range in inches
 */
export function getRecommendedDepthRange(utilityType: keyof typeof UTILITY_TYPES): StandardDepth {
  if (UTILITY_TYPES[utilityType] && UTILITY_TYPES[utilityType].standardDepth) {
    return UTILITY_TYPES[utilityType].standardDepth;
  }

  // Default depth range if not specifically defined
  return { min: 36, max: 60, unit: 'inches' };
}
