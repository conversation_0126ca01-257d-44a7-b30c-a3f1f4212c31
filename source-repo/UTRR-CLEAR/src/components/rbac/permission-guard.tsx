'use client';

/**
 * Permission Guard Component
 * Conditionally renders components based on user permissions
 */

import React from 'react';
import { useHasPermission, useHasAnyPermission, useHasAllPermissions, useIsAdmin, useHasRole } from '~/hooks/use-permissions';
import type { PermissionContext } from '~/lib/rbac/rbac-service';

// Define roles locally since EnterpriseRole is not exported
type EnterpriseRole = 'admin' | 'manager' | 'coordinator' | 'viewer' | 'engineer' | 'billing';
import { Skeleton } from '~/components/ui/skeleton';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { ShieldX } from 'lucide-react';
import { EnhancedPermissionDeniedFallback } from '~/components/ui/access-denied';

export interface PermissionGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  role?: EnterpriseRole | string;
  requireAdmin?: boolean;
  context?: PermissionContext;
  showFallback?: boolean;
  className?: string;
}

/**
 * Main Permission Guard component
 */
export function PermissionGuard({
  children,
  fallback,
  loading,
  permission,
  permissions = [],
  requireAll = true,
  role,
  requireAdmin = false,
  context,
  showFallback = true,
  className,
}: PermissionGuardProps) {
  const isAdmin = useIsAdmin();
  const hasRole = useHasRole(role || '');

  // Permission checking based on props
  const singlePermission = useHasPermission(
    permission || '',
    context,
    { enabled: !!permission }
  );

  const anyPermissions = useHasAnyPermission(
    permissions,
    context,
    { enabled: permissions.length > 0 && !requireAll }
  );

  const allPermissions = useHasAllPermissions(
    permissions,
    context,
    { enabled: permissions.length > 0 && requireAll }
  );

  // Determine loading state
  const isLoading = permission 
    ? singlePermission.loading
    : permissions.length > 0
    ? (requireAll ? allPermissions.loading : anyPermissions.loading)
    : false;

  // Determine access
  let hasAccess = false;

  if (requireAdmin) {
    hasAccess = isAdmin;
  } else if (role) {
    hasAccess = hasRole;
  } else if (permission) {
    hasAccess = singlePermission.hasPermission;
  } else if (permissions.length > 0) {
    hasAccess = requireAll ? allPermissions.hasPermission : anyPermissions.hasPermission;
  } else {
    // No restrictions - allow access
    hasAccess = true;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={className}>
        {loading || <Skeleton className="h-8 w-full" />}
      </div>
    );
  }

  // Show content if user has access
  if (hasAccess) {
    return <div className={className}>{children}</div>;
  }

  // Show fallback or enhanced default fallback
  if (showFallback) {
    if (fallback) {
      return <div className={className}>{fallback}</div>;
    }
    
    // Use enhanced fallback with context
    return (
      <div className={className}>
        <EnhancedPermissionDeniedFallback
          permission={permission}
          role={role}
          variant="alert"
        />
      </div>
    );
  }

  return null;
}

/**
 * Admin-only guard
 */
export function AdminGuard({
  children,
  fallback,
  showFallback = true,
  className,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  className?: string;
}) {
  return (
    <PermissionGuard
      requireAdmin
      showFallback={showFallback}
      fallback={fallback}
      className={className}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * Role-based guard
 */
export function RoleGuard({
  role,
  children,
  fallback,
  showFallback = true,
  className,
}: {
  role: EnterpriseRole | string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  className?: string;
}) {
  return (
    <PermissionGuard
      role={role}
      showFallback={showFallback}
      fallback={fallback}
      className={className}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * Single permission guard
 */
export function HasPermission({
  permission,
  context,
  children,
  fallback,
  showFallback = true,
  className,
}: {
  permission: string;
  context?: PermissionContext;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  className?: string;
}) {
  return (
    <PermissionGuard
      permission={permission}
      context={context}
      showFallback={showFallback}
      fallback={fallback}
      className={className}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * Multiple permissions guard (any)
 */
export function HasAnyPermission({
  permissions,
  context,
  children,
  fallback,
  showFallback = true,
  className,
}: {
  permissions: string[];
  context?: PermissionContext;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  className?: string;
}) {
  return (
    <PermissionGuard
      permissions={permissions}
      requireAll={false}
      context={context}
      showFallback={showFallback}
      fallback={fallback}
      className={className}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * Multiple permissions guard (all)
 */
export function HasAllPermissions({
  permissions,
  context,
  children,
  fallback,
  showFallback = true,
  className,
}: {
  permissions: string[];
  context?: PermissionContext;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  className?: string;
}) {
  return (
    <PermissionGuard
      permissions={permissions}
      requireAll={true}
      context={context}
      showFallback={showFallback}
      fallback={fallback}
      className={className}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * Default permission denied fallback component
 */
export function PermissionDeniedFallback({
  message = "You don't have permission to access this feature.",
  showIcon = true,
  className,
}: {
  message?: string;
  showIcon?: boolean;
  className?: string;
}) {
  return (
    <Alert className={className}>
      {showIcon && <ShieldX className="h-4 w-4" />}
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
}

/**
 * HOC for permission-based component wrapping
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  permissionConfig: Omit<PermissionGuardProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <PermissionGuard {...permissionConfig}>
      <Component {...props} />
    </PermissionGuard>
  );

  WrappedComponent.displayName = `withPermission(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Higher-order component for admin-only components
 */
export function withAdminOnly<P extends object>(Component: React.ComponentType<P>) {
  return withPermission(Component, { requireAdmin: true });
}

/**
 * Higher-order component for role-based components
 */
export function withRole<P extends object>(Component: React.ComponentType<P>, role: EnterpriseRole | string) {
  return withPermission(Component, { role });
}

/**
 * Predefined permission guards for common use cases
 */

export const CanCreateProjects = ({ children, fallback, className }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) => (
  <HasPermission permission="projects:create:organization" fallback={fallback} className={className}>
    {children}
  </HasPermission>
);

export const CanEditProjects = ({ children, fallback, className }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) => (
  <HasAnyPermission 
    permissions={['projects:update:organization', 'projects:update:assigned']} 
    fallback={fallback} 
    className={className}
  >
    {children}
  </HasAnyPermission>
);

export const CanDeleteProjects = ({ children, fallback, className }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) => (
  <HasPermission permission="projects:delete:organization" fallback={fallback} className={className}>
    {children}
  </HasPermission>
);

export const CanManageUsers = ({ children, fallback, className }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) => (
  <HasAnyPermission 
    permissions={['users:create:organization', 'users:update:organization', 'users:assign:organization']} 
    fallback={fallback} 
    className={className}
  >
    {children}
  </HasAnyPermission>
);

export const CanViewAnalytics = ({ children, fallback, className }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) => (
  <HasPermission permission="analytics:read:organization" fallback={fallback} className={className}>
    {children}
  </HasPermission>
);

export const CanExportData = ({ children, fallback, className }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) => (
  <HasPermission permission="data:export:organization" fallback={fallback} className={className}>
    {children}
  </HasPermission>
);