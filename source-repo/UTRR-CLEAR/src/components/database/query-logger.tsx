'use client';

import { useState } from 'react';
import { Clock, User, Database, Code, CheckCircle, XCircle, ChevronDown, ChevronRight } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import { api } from '~/trpc/react';
import { format, formatDistanceToNow } from 'date-fns';

export function QueryLogger() {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const { data: history, isLoading } = api.database.getQueryHistory.useQuery({
    limit: 50,
    offset: 0,
  });

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'CREATE_TABLE':
        return <Database className="h-4 w-4" />;
      case 'DROP_TABLE':
        return <Database className="h-4 w-4 text-destructive" />;
      case 'ADD_COLUMN':
      case 'MODIFY_COLUMN':
        return <Code className="h-4 w-4" />;
      case 'DROP_COLUMN':
        return <Code className="h-4 w-4 text-destructive" />;
      case 'CREATE_FOREIGN_KEY':
        return <Database className="h-4 w-4 text-blue-500" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getOperationLabel = (operation: string) => {
    const labels: Record<string, string> = {
      CREATE_TABLE: 'Created table',
      DROP_TABLE: 'Deleted table',
      ADD_COLUMN: 'Added column',
      MODIFY_COLUMN: 'Modified column',
      DROP_COLUMN: 'Deleted column',
      CREATE_FOREIGN_KEY: 'Created foreign key',
    };
    return labels[operation] || operation;
  };

  const getOperationColor = (operation: string) => {
    if (operation.includes('DROP') || operation.includes('DELETE')) {
      return 'destructive';
    }
    if (operation.includes('CREATE') || operation.includes('ADD')) {
      return 'default';
    }
    return 'secondary';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Query History</CardTitle>
          <CardDescription>Loading database change history...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Query History</CardTitle>
        <CardDescription>
          Track all database schema changes and who made them
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[500px] pr-4">
          <div className="space-y-4">
            {history?.items.map((item: any) => (
              <div
                key={item.id}
                className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium">
                      {item.userName?.charAt(0).toUpperCase() || 'U'}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        {getOperationIcon(item.operationType)}
                        <span className="font-medium">
                          {getOperationLabel(item.operationType)}
                        </span>
                        <Badge variant={getOperationColor(item.operationType)}>
                          {item.tableName}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {item.userName}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatDistanceToNow(new Date(item.executedAt), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.status === 'success' ? (
                      <Badge variant="outline" className="text-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Success
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-600">
                        <XCircle className="h-3 w-3 mr-1" />
                        Failed
                      </Badge>
                    )}
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleExpanded(String(item.id))}
                    >
                      {expandedItems.has(String(item.id)) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {expandedItems.has(String(item.id)) && (
                  <div className="mt-4 space-y-2">
                    <div>
                      <h4 className="text-sm font-medium mb-1">SQL Executed:</h4>
                      <pre className="p-3 bg-muted rounded text-xs overflow-x-auto">
                        {item.sqlExecuted}
                      </pre>
                    </div>
                    {item.errorMessage && (
                      <div>
                        <h4 className="text-sm font-medium mb-1 text-destructive">Error:</h4>
                        <p className="text-sm text-destructive">{item.errorMessage}</p>
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground">
                      Executed at: {format(new Date(item.executedAt), 'PPpp')}
                    </div>
                  </div>
                )}
              </div>
            ))}

            {history?.items.length === 0 && (
              <div className="text-center py-8">
                <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No database changes recorded yet</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}