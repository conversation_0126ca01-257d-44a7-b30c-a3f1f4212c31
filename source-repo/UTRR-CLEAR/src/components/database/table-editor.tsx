'use client';

import { useState } from 'react';
import { Plus, Edit2, Trash2, Key, MoreV<PERSON><PERSON>, Copy, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '~/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import { ColumnEditor } from './column-editor';
import { RelationshipManager } from './relationship-manager';

interface TableEditorProps {
  tableName: string;
  isOpen: boolean;
  onClose: () => void;
  onRefresh: () => void;
}

export function TableEditor({ tableName, isOpen, onClose, onRefresh }: TableEditorProps) {
  const [columnToEdit, setColumnToEdit] = useState<any>(null);
  const [showColumnEditor, setShowColumnEditor] = useState(false);
  const [showRelationships, setShowRelationships] = useState(false);
  const [columnToDelete, setColumnToDelete] = useState<string | null>(null);
  const [isAddingColumn, setIsAddingColumn] = useState(false);

  const { data: schema, refetch } = api.database.getTableSchema.useQuery(
    { tableName },
    { enabled: isOpen }
  );

  const deleteColumn = api.database.deleteColumn.useMutation({
    onSuccess: () => {
      toast({
        title: 'Column deleted',
        description: 'The column has been removed from the table.',
      });
      refetch();
      setColumnToDelete(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleDeleteColumn = (columnName: string) => {
    deleteColumn.mutate({ tableName, columnName });
  };

  const typeColorMap: Record<string, string> = {
    text: 'bg-blue-100 text-blue-800',
    integer: 'bg-green-100 text-green-800',
    decimal: 'bg-green-100 text-green-800',
    boolean: 'bg-purple-100 text-purple-800',
    date: 'bg-orange-100 text-orange-800',
    timestamp: 'bg-orange-100 text-orange-800',
    json: 'bg-pink-100 text-pink-800',
    uuid: 'bg-gray-100 text-gray-800',
  };

  const getSimpleType = (pgType: string): string => {
    const typeMap: Record<string, string> = {
      'character varying': 'text',
      'text': 'text',
      'integer': 'integer',
      'bigint': 'integer',
      'numeric': 'decimal',
      'boolean': 'boolean',
      'date': 'date',
      'timestamp without time zone': 'timestamp',
      'jsonb': 'json',
      'uuid': 'uuid',
    };
    return typeMap[pgType.toLowerCase()] || pgType;
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent className="w-full md:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="text-2xl">{tableName}</SheetTitle>
            <SheetDescription>
              Manage table structure, columns, and relationships
            </SheetDescription>
          </SheetHeader>

          <div className="mt-6 space-y-6">
            {/* Columns Section */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Columns</CardTitle>
                  <CardDescription>Manage table columns and their properties</CardDescription>
                </div>
                <Button
                  size="sm"
                  onClick={() => {
                    setIsAddingColumn(true);
                    setColumnToEdit(null);
                    setShowColumnEditor(true);
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Column
                </Button>
              </CardHeader>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Nullable</TableHead>
                      <TableHead>Default</TableHead>
                      <TableHead>Constraints</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {schema?.columns.map((column: any) => (
                      <TableRow key={column.name}>
                        <TableCell className="font-medium">{column.name}</TableCell>
                        <TableCell>
                          <Badge
                            variant="secondary"
                            className={typeColorMap[getSimpleType(column.type)] || ''}
                          >
                            {getSimpleType(column.type)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={column.nullable ? 'outline' : 'default'}>
                            {column.nullable ? 'Yes' : 'No'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {column.defaultValue || '-'}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            {column.primaryKey && (
                              <Badge variant="default" className="text-xs">
                                <Key className="h-3 w-3 mr-1" />
                                Primary
                              </Badge>
                            )}
                            {column.unique && (
                              <Badge variant="secondary" className="text-xs">
                                Unique
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  setColumnToEdit(column);
                                  setIsAddingColumn(false);
                                  setShowColumnEditor(true);
                                }}
                              >
                                <Edit2 className="h-4 w-4 mr-2" />
                                Edit Column
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  navigator.clipboard.writeText(column.name);
                                  toast({
                                    title: 'Copied',
                                    description: 'Column name copied to clipboard',
                                  });
                                }}
                              >
                                <Copy className="h-4 w-4 mr-2" />
                                Copy Name
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                disabled={column.primaryKey}
                                onClick={() => setColumnToDelete(column.name)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Column
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Foreign Keys Section */}
            {schema?.foreignKeys && schema.foreignKeys.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Foreign Keys</CardTitle>
                  <CardDescription>Relationships with other tables</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {schema.foreignKeys.map((fk, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-2">
                          <Key className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{fk.columnName}</span>
                          <span className="text-muted-foreground">→</span>
                          <span className="text-sm">
                            {fk.referencedTable}.{fk.referencedColumn}
                          </span>
                        </div>
                        <div className="flex gap-2 text-xs">
                          <Badge variant="outline">ON DELETE {fk.onDelete}</Badge>
                          <Badge variant="outline">ON UPDATE {fk.onUpdate}</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Actions */}
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setShowRelationships(true)}>
                <Key className="h-4 w-4 mr-2" />
                Manage Relationships
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Column Editor Dialog */}
      {showColumnEditor && (
        <ColumnEditor
          tableName={tableName}
          column={columnToEdit}
          isAddingNew={isAddingColumn}
          onClose={() => {
            setShowColumnEditor(false);
            setColumnToEdit(null);
            setIsAddingColumn(false);
          }}
          onSuccess={() => {
            setShowColumnEditor(false);
            setColumnToEdit(null);
            setIsAddingColumn(false);
            refetch();
          }}
        />
      )}

      {/* Relationship Manager */}
      {showRelationships && (
        <RelationshipManager
          tableName={tableName}
          onClose={() => setShowRelationships(false)}
          onSuccess={() => {
            setShowRelationships(false);
            refetch();
          }}
        />
      )}

      {/* Delete Column Confirmation */}
      <AlertDialog open={!!columnToDelete} onOpenChange={() => setColumnToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Column
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the column &quot;{columnToDelete}&quot;? This action cannot be
              undone and will permanently remove all data in this column.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => columnToDelete && handleDeleteColumn(columnToDelete)}
            >
              Delete Column
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}