'use client';

import { useState } from 'react';
import { Plus, Trash2, Info } from 'lucide-react';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Checkbox } from '~/components/ui/checkbox';
import { Card } from '~/components/ui/card';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';

interface CreateTableDialogProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface Column {
  id: string;
  name: string;
  type: 'text' | 'integer' | 'decimal' | 'boolean' | 'date' | 'timestamp' | 'json' | 'uuid';
  nullable: boolean;
  unique: boolean;
  defaultValue: string;
  primaryKey: boolean;
}

const dataTypes = [
  { value: 'text', label: 'Text' },
  { value: 'integer', label: 'Number (Integer)' },
  { value: 'decimal', label: 'Number (Decimal)' },
  { value: 'boolean', label: 'Boolean' },
  { value: 'date', label: 'Date' },
  { value: 'timestamp', label: 'Date & Time' },
  { value: 'json', label: 'JSON' },
  { value: 'uuid', label: 'UUID' },
];

export function CreateTableDialog({ onClose, onSuccess }: CreateTableDialogProps) {
  const [tableName, setTableName] = useState('');
  const [comment, setComment] = useState('');
  const [columns, setColumns] = useState<Column[]>([
    {
      id: '1',
      name: 'id',
      type: 'integer',
      nullable: false,
      unique: true,
      defaultValue: '',
      primaryKey: true,
    },
  ]);
  const [showSqlPreview, setShowSqlPreview] = useState(false);

  const createTable = api.database.createTable.useMutation({
    onSuccess: () => {
      toast({
        title: 'Table created',
        description: `Table "${tableName}" has been created successfully.`,
      });
      onSuccess();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const { data: sqlPreview } = api.database.previewSql.useQuery(
    {
      operation: 'CREATE_TABLE',
      params: {
        tableName,
        columns: columns.map(({ id, ...col }) => col),
        comment,
      },
    },
    { enabled: showSqlPreview && !!tableName && columns.length > 0 }
  );

  const addColumn = () => {
    const newColumn: Column = {
      id: Date.now().toString(),
      name: '',
      type: 'text',
      nullable: true,
      unique: false,
      defaultValue: '',
      primaryKey: false,
    };
    setColumns([...columns, newColumn]);
  };

  const updateColumn = (id: string, updates: Partial<Column>) => {
    setColumns(columns.map((col: any) => (col.id === id ? { ...col, ...updates } : col)));
  };

  const removeColumn = (id: string) => {
    setColumns(columns.filter((col: any) => col.id !== id));
  };

  const handleSubmit = () => {
    if (!tableName.trim()) {
      toast({
        title: 'Error',
        description: 'Table name is required',
        variant: 'destructive',
      });
      return;
    }

    const validColumns = columns.filter((col: any) => col.name.trim());
    if (validColumns.length === 0) {
      toast({
        title: 'Error',
        description: 'At least one column is required',
        variant: 'destructive',
      });
      return;
    }

    createTable.mutate({
      tableName,
      columns: validColumns.map(({ id, ...col }) => col),
      comment: comment.trim() || undefined,
    });
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Table</DialogTitle>
          <DialogDescription>
            Design your table structure by adding columns and defining their properties
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Table Name */}
          <div className="space-y-2">
            <Label htmlFor="tableName">Table Name</Label>
            <Input
              id="tableName"
              value={tableName}
              onChange={(e: any) => setTableName(e.target.value)}
              placeholder="table_name"
              className="font-mono"
            />
            <p className="text-xs text-muted-foreground">
              Use lowercase letters, numbers, and underscores only
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="comment">Description (Optional)</Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e: any) => setComment(e.target.value)}
              placeholder="Describe the purpose of this table..."
              className="resize-none"
              rows={2}
            />
          </div>

          {/* Columns */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Columns</Label>
              <Button size="sm" variant="outline" onClick={addColumn}>
                <Plus className="h-4 w-4 mr-2" />
                Add Column
              </Button>
            </div>

            <div className="space-y-2">
              {columns.map((column, index) => (
                <Card key={column.id} className="p-4">
                  <div className="grid grid-cols-12 gap-4 items-end">
                    <div className="col-span-3">
                      <Label className="text-xs">Column Name</Label>
                      <Input
                        value={column.name}
                        onChange={(e: any) => updateColumn(column.id, { name: e.target.value })}
                        placeholder="column_name"
                        className="font-mono"
                      />
                    </div>

                    <div className="col-span-3">
                      <Label className="text-xs">Type</Label>
                      <Select
                        value={column.type}
                        onValueChange={(value) => updateColumn(column.id, { type: value as Column['type'] })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {dataTypes.map((type: any) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="col-span-5 flex items-center gap-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`pk-${column.id}`}
                          checked={column.primaryKey}
                          onCheckedChange={(checked) =>
                            updateColumn(column.id, {
                              primaryKey: !!checked,
                              nullable: checked ? false : column.nullable,
                              unique: checked ? true : column.unique,
                            })
                          }
                        />
                        <Label htmlFor={`pk-${column.id}`} className="text-xs cursor-pointer">
                          Primary Key
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`null-${column.id}`}
                          checked={!column.nullable}
                          onCheckedChange={(checked) =>
                            updateColumn(column.id, { nullable: !checked })
                          }
                          disabled={column.primaryKey}
                        />
                        <Label htmlFor={`null-${column.id}`} className="text-xs cursor-pointer">
                          Required
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`unique-${column.id}`}
                          checked={column.unique}
                          onCheckedChange={(checked) =>
                            updateColumn(column.id, { unique: !!checked })
                          }
                          disabled={column.primaryKey}
                        />
                        <Label htmlFor={`unique-${column.id}`} className="text-xs cursor-pointer">
                          Unique
                        </Label>
                      </div>
                    </div>

                    <div className="col-span-1 flex justify-end">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeColumn(column.id)}
                        disabled={columns.length === 1}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* SQL Preview */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <Button
                variant="link"
                size="sm"
                className="p-0 h-auto"
                onClick={() => setShowSqlPreview(!showSqlPreview)}
              >
                {showSqlPreview ? 'Hide' : 'Show'} SQL Preview
              </Button>
              {showSqlPreview && sqlPreview && (
                <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                  {sqlPreview.sql}
                </pre>
              )}
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={createTable.isPending}>
            {createTable.isPending ? 'Creating...' : 'Create Table'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}