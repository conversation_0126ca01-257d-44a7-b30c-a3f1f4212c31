'use client';

import { useState, useRef, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Textarea } from '~/components/ui/textarea';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { 
  Play, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Clock, 
  Table,
  AlertCircle,
  Download,
  Copy,
  FolderOpen
} from 'lucide-react';
import { format } from 'date-fns';

interface QueryResult {
  rows: any[];
  fields: { name: string; dataTypeID: number }[];
  rowCount: number;
  command: string;
  executionTime: number;
  error?: string;
}

interface SavedQuery {
  id: string;
  name: string;
  query: string;
  createdAt: Date;
  category?: string;
}

export function SQLEditor() {
  const [query, setQuery] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [results, setResults] = useState<QueryResult | null>(null);
  const [queryHistory, setQueryHistory] = useState<SavedQuery[]>([]);
  const [selectedScript, setSelectedScript] = useState<string>('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const supabase = createClientComponentClient();

  // Predefined SQL scripts for RLS setup
  const rlsScripts = [
    {
      id: '01-enable-rls',
      name: '01 - Enable RLS on All Tables',
      category: 'RLS Setup',
      query: `-- Enable Row Level Security on ALL Tables
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND tablename NOT LIKE 'pg_%'
        AND tablename NOT LIKE '_prisma_%'
        AND tablename NOT IN ('spatial_ref_sys', 'geography_columns', 'geometry_columns')
    LOOP
        EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', r.tablename);
        RAISE NOTICE 'Enabled RLS on table: %', r.tablename;
    END LOOP;
END $$;

-- Verify RLS status
SELECT tablename, 
       CASE WHEN c.relrowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_status
FROM pg_tables t
JOIN pg_class c ON c.relname = t.tablename
WHERE t.schemaname = 'public' 
  AND t.tablename NOT LIKE 'pg_%'
  AND t.tablename NOT LIKE '_prisma_%'
ORDER BY t.tablename;`
    },
    {
      id: '02-helper-functions',
      name: '02 - Create Helper Functions',
      category: 'RLS Setup',
      query: `-- Create Helper Functions for RLS
CREATE OR REPLACE FUNCTION public.get_user_organization_id() 
RETURNS INTEGER AS $$
  SELECT COALESCE(
    (SELECT organization_id FROM public.users WHERE auth_user_id = auth.uid()),
    1
  );
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_admin() 
RETURNS BOOLEAN AS $$
  SELECT COALESCE(
    (SELECT is_admin FROM public.users WHERE auth_user_id = auth.uid()),
    COALESCE((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role', '') = 'admin',
    false
  );
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_user_id() 
RETURNS INTEGER AS $$
  SELECT COALESCE(
    (SELECT id FROM public.users WHERE auth_user_id = auth.uid()),
    0
  );
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_user_organization_id() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_id() TO authenticated;`
    },
    {
      id: '03-basic-policies',
      name: '03 - Create Basic RLS Policies',
      category: 'RLS Setup',
      query: `-- Create basic RLS policies for organizations
DROP POLICY IF EXISTS "users_view_own_org" ON public.organizations;
CREATE POLICY "users_view_own_org" ON public.organizations
  FOR SELECT USING (id = public.get_user_organization_id() OR public.is_admin());

-- Create policies for users table
DROP POLICY IF EXISTS "users_view_same_org" ON public.users;
CREATE POLICY "users_view_same_org" ON public.users
  FOR SELECT USING (organization_id = public.get_user_organization_id());

DROP POLICY IF EXISTS "users_update_self" ON public.users;
CREATE POLICY "users_update_self" ON public.users
  FOR UPDATE USING (auth_user_id = auth.uid());

-- Create policies for projects table
DROP POLICY IF EXISTS "projects_view_org" ON public.projects;
CREATE POLICY "projects_view_org" ON public.projects
  FOR SELECT USING (organization_id = public.get_user_organization_id());

DROP POLICY IF EXISTS "projects_insert_org" ON public.projects;
CREATE POLICY "projects_insert_org" ON public.projects
  FOR INSERT WITH CHECK (organization_id = public.get_user_organization_id());

-- More policies can be added here...`
    },
    {
      id: '04-check-status',
      name: 'Check RLS Status',
      category: 'Utilities',
      query: `-- Check RLS and Policy Status
SELECT 
  t.tablename,
  CASE WHEN c.relrowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_enabled,
  COUNT(p.policyname) as policy_count
FROM pg_tables t
LEFT JOIN pg_class c ON c.relname = t.tablename
LEFT JOIN pg_policies p ON p.tablename = t.tablename AND p.schemaname = t.schemaname
WHERE t.schemaname = 'public'
  AND t.tablename NOT LIKE 'pg_%'
  AND t.tablename NOT LIKE '_prisma_%'
GROUP BY t.tablename, c.relrowsecurity
ORDER BY t.tablename;`
    }
  ];

  const executeQuery = async () => {
    if (!query.trim()) return;
    
    setIsExecuting(true);
    const startTime = Date.now();

    try {
      // For Supabase, we need to use a custom RPC function or direct connection
      // This is a simplified version - in production you'd want proper query execution
      const { data, error } = await supabase.rpc('exec_sql', { 
        query: query 
      }).single();

      const executionTime = Date.now() - startTime;

      if (error) {
        setResults({
          rows: [],
          fields: [],
          rowCount: 0,
          command: query.split(' ')[0]?.toUpperCase() || 'UNKNOWN',
          executionTime,
          error: error.message
        });
      } else {
        setResults({
          rows: (data as any)?.rows || [],
          fields: (data as any)?.fields || [],
          rowCount: (data as any)?.rowCount || 0,
          command: (data as any)?.command || 'UNKNOWN',
          executionTime
        });
      }

      // Add to history
      const newQuery: SavedQuery = {
        id: Date.now().toString(),
        name: `Query ${queryHistory.length + 1}`,
        query: query,
        createdAt: new Date()
      };
      setQueryHistory(prev => [newQuery, ...prev].slice(0, 10));

    } catch (err: any) {
      const executionTime = Date.now() - startTime;
      setResults({
        rows: [],
        fields: [],
        rowCount: 0,
        command: 'ERROR',
        executionTime,
        error: err.message || 'An error occurred while executing the query'
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      executeQuery();
    }
  };

  const loadScript = (scriptId: string) => {
    const script = rlsScripts.find(s => s.id === scriptId);
    if (script) {
      setQuery(script.query);
      setSelectedScript(scriptId);
    }
  };

  const copyToClipboard = () => {
    if (query) {
      navigator.clipboard.writeText(query);
    }
  };

  const downloadSQL = () => {
    if (query) {
      const blob = new Blob([query], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `query-${Date.now()}.sql`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>SQL Editor</CardTitle>
              <CardDescription>
                Execute SQL queries directly on your database
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Select value={selectedScript} onValueChange={loadScript}>
                <SelectTrigger className="w-[300px]">
                  <SelectValue placeholder="Load a script..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Clear editor</SelectItem>
                  {rlsScripts.map(script => (
                    <SelectItem key={script.id} value={script.id}>
                      <div className="flex items-center gap-2">
                        <FolderOpen className="h-3 w-3" />
                        {script.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter your SQL query here..."
              className="min-h-[300px] font-mono text-sm"
              spellCheck={false}
            />
            <div className="absolute bottom-2 right-2 flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={copyToClipboard}
                title="Copy to clipboard"
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={downloadSQL}
                title="Download SQL"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Press Ctrl+Enter to execute
            </div>
            <Button 
              onClick={executeQuery}
              disabled={isExecuting || !query.trim()}
            >
              {isExecuting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Executing...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Run Query
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle>Results</CardTitle>
                {results.error ? (
                  <Badge variant="destructive">
                    <XCircle className="mr-1 h-3 w-3" />
                    Error
                  </Badge>
                ) : (
                  <Badge variant="secondary">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Success
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {results.executionTime}ms
                </span>
                <span className="flex items-center gap-1">
                  <Table className="h-3 w-3" />
                  {results.rowCount} rows
                </span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {results.error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Query Error</AlertTitle>
                <AlertDescription className="font-mono text-sm">
                  {results.error}
                </AlertDescription>
              </Alert>
            ) : (
              <ScrollArea className="h-[400px]">
                {results.rows.length > 0 ? (
                  <table className="w-full border-collapse">
                    <thead>
                      <tr>
                        {results.fields.map((field, idx) => (
                          <th key={idx} className="border px-4 py-2 text-left font-medium">
                            {field.name}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {results.rows.map((row, rowIdx) => (
                        <tr key={rowIdx} className="hover:bg-muted/50">
                          {results.fields.map((field, colIdx) => (
                            <td key={colIdx} className="border px-4 py-2 font-mono text-sm">
                              {row[field.name] === null ? (
                                <span className="text-muted-foreground">NULL</span>
                              ) : (
                                String(row[field.name])
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Query executed successfully with no results
                  </div>
                )}
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      )}

      {queryHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Query History</CardTitle>
            <CardDescription>Recently executed queries</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {queryHistory.map((item) => (
                <div
                  key={item.id}
                  className="flex items-start gap-2 p-2 rounded-lg hover:bg-muted/50 cursor-pointer"
                  onClick={() => setQuery(item.query)}
                >
                  <FileText className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-mono truncate">{item.query}</p>
                    <p className="text-xs text-muted-foreground">
                      {format(item.createdAt, 'HH:mm:ss')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Note</AlertTitle>
        <AlertDescription>
          This SQL editor requires the `exec_sql` RPC function to be created in your Supabase database.
          Without it, queries will fail. Use the Supabase Dashboard SQL Editor for now, or create this function:
          <pre className="mt-2 text-xs bg-muted p-2 rounded">
{`CREATE OR REPLACE FUNCTION exec_sql(query text)
RETURNS json AS $$
BEGIN
  -- Implementation would go here
  -- This is a security risk if not properly implemented
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;`}
          </pre>
        </AlertDescription>
      </Alert>
    </div>
  );
}