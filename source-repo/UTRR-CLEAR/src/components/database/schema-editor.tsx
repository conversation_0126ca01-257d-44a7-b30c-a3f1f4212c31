'use client';

import * as React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '~/components/ui/dialog';
import { Badge } from '~/components/ui/badge';
import { Textarea } from '~/components/ui/textarea';
import { Checkbox } from '~/components/ui/checkbox';
import { toast } from '~/hooks/use-toast';
import {
  Database,
  Table as TableIcon,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Key,
  Link,
  AlertTriangle,
  Code,
  Eye,
  Download,
} from 'lucide-react';
import { cn } from '~/lib/utils';

interface DatabaseSchema {
  tables: TableSchema[];
  views: ViewSchema[];
  indexes: IndexSchema[];
  relationships: RelationshipSchema[];
}

interface TableSchema {
  name: string;
  columns: ColumnSchema[];
  description?: string;
  rowCount: number;
  size: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ColumnSchema {
  name: string;
  type: string;
  length?: number;
  nullable: boolean;
  primaryKey: boolean;
  unique: boolean;
  defaultValue?: string;
  description?: string;
  foreignKey?: {
    table: string;
    column: string;
    onDelete: 'CASCADE' | 'RESTRICT' | 'SET NULL' | 'NO ACTION';
    onUpdate: 'CASCADE' | 'RESTRICT' | 'SET NULL' | 'NO ACTION';
  };
}

interface ViewSchema {
  name: string;
  definition: string;
  description?: string;
  dependsOn: string[];
}

interface IndexSchema {
  name: string;
  table: string;
  columns: string[];
  unique: boolean;
  type: 'BTREE' | 'HASH' | 'GIN' | 'GIST';
}

interface RelationshipSchema {
  name: string;
  fromTable: string;
  fromColumn: string;
  toTable: string;
  toColumn: string;
  type: 'ONE_TO_ONE' | 'ONE_TO_MANY' | 'MANY_TO_MANY' | 'MANY_TO_ONE';
}

export function SchemaEditor() {
  const { user } = useAuth();
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [schema, setSchema] = useState<DatabaseSchema | null>(null);
  const [showAddTableDialog, setShowAddTableDialog] = useState(false);
  const [showAddColumnDialog, setShowAddColumnDialog] = useState(false);
  const [editingColumn, setEditingColumn] = useState<ColumnSchema | null>(null);
  const [newTableName, setNewTableName] = useState('');
  const [newTableDescription, setNewTableDescription] = useState('');
  const [newColumn, setNewColumn] = useState<Partial<ColumnSchema>>({});
  const [activeTab, setActiveTab] = useState<'tables' | 'views' | 'indexes' | 'relationships'>(
    'tables'
  );

  // Mock schema data for demonstration
  useEffect(() => {
    setIsLoading(true);

    setTimeout(() => {
      const mockSchema: DatabaseSchema = {
        tables: [
          {
            name: 'users',
            description: 'User accounts and authentication',
            rowCount: 24,
            size: '64 KB',
            createdAt: new Date('2023-01-15'),
            updatedAt: new Date('2024-01-15'),
            columns: [
              {
                name: 'id',
                type: 'VARCHAR',
                length: 255,
                nullable: false,
                primaryKey: true,
                unique: true,
                description: 'Unique user identifier',
              },
              {
                name: 'name',
                type: 'VARCHAR',
                length: 255,
                nullable: true,
                primaryKey: false,
                unique: false,
                description: 'User display name',
              },
              {
                name: 'email',
                type: 'VARCHAR',
                length: 255,
                nullable: false,
                primaryKey: false,
                unique: true,
                description: 'User email address',
              },
              {
                name: 'role',
                type: 'VARCHAR',
                length: 50,
                nullable: true,
                primaryKey: false,
                unique: false,
                defaultValue: 'user',
                description: 'User role/permission level',
              },
              {
                name: 'isActive',
                type: 'BOOLEAN',
                nullable: true,
                primaryKey: false,
                unique: false,
                defaultValue: 'true',
                description: 'Whether user account is active',
              },
              {
                name: 'createdAt',
                type: 'TIMESTAMP',
                nullable: false,
                primaryKey: false,
                unique: false,
                defaultValue: 'CURRENT_TIMESTAMP',
                description: 'Account creation timestamp',
              },
            ],
          },
          {
            name: 'projects',
            description: 'Project management and tracking',
            rowCount: 156,
            size: '2.1 MB',
            createdAt: new Date('2023-01-15'),
            updatedAt: new Date('2024-01-15'),
            columns: [
              {
                name: 'id',
                type: 'VARCHAR',
                length: 255,
                nullable: false,
                primaryKey: true,
                unique: true,
                description: 'Unique project identifier',
              },
              {
                name: 'name',
                type: 'VARCHAR',
                length: 500,
                nullable: false,
                primaryKey: false,
                unique: false,
                description: 'Project name/title',
              },
              {
                name: 'projectNumber',
                type: 'VARCHAR',
                length: 50,
                nullable: true,
                primaryKey: false,
                unique: true,
                description: 'Human-readable project number',
              },
              {
                name: 'status',
                type: 'VARCHAR',
                length: 50,
                nullable: true,
                primaryKey: false,
                unique: false,
                description: 'Current project status',
              },
              {
                name: 'currentPhase',
                type: 'VARCHAR',
                length: 100,
                nullable: true,
                primaryKey: false,
                unique: false,
                description: 'Current project phase',
              },
              {
                name: 'managerId',
                type: 'VARCHAR',
                length: 255,
                nullable: true,
                primaryKey: false,
                unique: false,
                description: 'Project manager user ID',
                foreignKey: {
                  table: 'users',
                  column: 'id',
                  onDelete: 'SET NULL',
                  onUpdate: 'CASCADE',
                },
              },
            ],
          },
          {
            name: 'utilities',
            description: 'Utility companies and infrastructure',
            rowCount: 342,
            size: '1.8 MB',
            createdAt: new Date('2023-01-15'),
            updatedAt: new Date('2024-01-15'),
            columns: [
              {
                name: 'id',
                type: 'VARCHAR',
                length: 255,
                nullable: false,
                primaryKey: true,
                unique: true,
                description: 'Unique utility identifier',
              },
              {
                name: 'companyName',
                type: 'VARCHAR',
                length: 255,
                nullable: false,
                primaryKey: false,
                unique: false,
                description: 'Utility company name',
              },
              {
                name: 'utilityType',
                type: 'VARCHAR',
                length: 50,
                nullable: false,
                primaryKey: false,
                unique: false,
                description: 'Type of utility (electric, gas, water, etc.)',
              },
              {
                name: 'geometry',
                type: 'GEOMETRY',
                nullable: true,
                primaryKey: false,
                unique: false,
                description: 'Spatial geometry data (PostGIS)',
              },
            ],
          },
        ],
        views: [
          {
            name: 'active_projects_view',
            definition:
              "SELECT p.*, u.name as manager_name FROM projects p LEFT JOIN users u ON p.managerId = u.id WHERE p.status = 'Active'",
            description: 'View of all active projects with manager names',
            dependsOn: ['projects', 'users'],
          },
        ],
        indexes: [
          {
            name: 'idx_users_email',
            table: 'users',
            columns: ['email'],
            unique: true,
            type: 'BTREE',
          },
          {
            name: 'idx_projects_status',
            table: 'projects',
            columns: ['status'],
            unique: false,
            type: 'BTREE',
          },
          {
            name: 'idx_utilities_type',
            table: 'utilities',
            columns: ['utilityType'],
            unique: false,
            type: 'BTREE',
          },
          {
            name: 'idx_utilities_geometry',
            table: 'utilities',
            columns: ['geometry'],
            unique: false,
            type: 'GIST',
          },
        ],
        relationships: [
          {
            name: 'fk_projects_manager',
            fromTable: 'projects',
            fromColumn: 'managerId',
            toTable: 'users',
            toColumn: 'id',
            type: 'MANY_TO_ONE',
          },
        ],
      };

      setSchema(mockSchema);
      if (mockSchema.tables.length > 0 && mockSchema.tables[0]) {
        setSelectedTable(mockSchema.tables[0].name);
      }
      setIsLoading(false);
    }, 500);
  }, []);

  const selectedTableSchema = useMemo(() => {
    return schema?.tables.find((t: any) => t.name === selectedTable);
  }, [schema, selectedTable]);

  const dataTypes = [
    'VARCHAR',
    'TEXT',
    'INTEGER',
    'BIGINT',
    'DECIMAL',
    'NUMERIC',
    'BOOLEAN',
    'DATE',
    'TIME',
    'TIMESTAMP',
    'JSON',
    'GEOMETRY',
  ];

  const getColumnTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'varchar':
      case 'text':
        return 'text-blue-600 dark:text-blue-400';
      case 'integer':
      case 'bigint':
      case 'decimal':
      case 'numeric':
        return 'text-green-600 dark:text-green-400';
      case 'boolean':
        return 'text-purple-600 dark:text-purple-400';
      case 'date':
      case 'time':
      case 'timestamp':
        return 'text-orange-600 dark:text-orange-400';
      case 'json':
        return 'text-pink-600 dark:text-pink-400';
      case 'geometry':
        return 'text-indigo-600 dark:text-indigo-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const handleAddTable = () => {
    if (!newTableName.trim() || !schema) return;

    const newTable: TableSchema = {
      name: newTableName.trim(),
      description: newTableDescription.trim() || undefined,
      rowCount: 0,
      size: '0 B',
      createdAt: new Date(),
      updatedAt: new Date(),
      columns: [
        {
          name: 'id',
          type: 'VARCHAR',
          length: 255,
          nullable: false,
          primaryKey: true,
          unique: true,
          description: 'Primary key',
        },
      ],
    };

    setSchema({
      ...schema,
      tables: [...schema.tables, newTable],
    });

    setSelectedTable(newTable.name);
    setNewTableName('');
    setNewTableDescription('');
    setShowAddTableDialog(false);

    toast({
      title: 'Table created',
      description: `Table "${newTable.name}" has been created successfully.`,
    });
  };

  const handleAddColumn = () => {
    if (!newColumn.name || !newColumn.type || !selectedTableSchema || !schema) return;

    const column: ColumnSchema = {
      name: newColumn.name,
      type: newColumn.type,
      length: newColumn.length,
      nullable: newColumn.nullable ?? false,
      primaryKey: newColumn.primaryKey ?? false,
      unique: newColumn.unique ?? false,
      defaultValue: newColumn.defaultValue,
      description: newColumn.description,
      foreignKey: newColumn.foreignKey,
    };

    const updatedTable = {
      ...selectedTableSchema,
      columns: [...selectedTableSchema.columns, column],
      updatedAt: new Date(),
    };

    setSchema({
      ...schema,
      tables: schema.tables.map((t: any) => (t.name === selectedTable ? updatedTable : t)),
    });

    setNewColumn({});
    setShowAddColumnDialog(false);

    toast({
      title: 'Column added',
      description: `Column "${column.name}" has been added to table "${selectedTable}".`,
    });
  };

  const handleDeleteColumn = (columnName: string) => {
    if (!selectedTableSchema || !schema) return;

    const updatedTable = {
      ...selectedTableSchema,
      columns: selectedTableSchema.columns.filter((c: any) => c.name !== columnName),
      updatedAt: new Date(),
    };

    setSchema({
      ...schema,
      tables: schema.tables.map((t: any) => (t.name === selectedTable ? updatedTable : t)),
    });

    toast({
      title: 'Column deleted',
      description: `Column "${columnName}" has been removed from table "${selectedTable}".`,
    });
  };

  const generateDDL = () => {
    if (!selectedTableSchema) return '';

    let ddl = `CREATE TABLE ${selectedTableSchema.name} (\n`;

    selectedTableSchema.columns.forEach((column, index) => {
      ddl += `  ${column.name} ${column.type}`;
      if (column.length) {
        ddl += `(${column.length})`;
      }
      if (!column.nullable) {
        ddl += ' NOT NULL';
      }
      if (column.unique && !column.primaryKey) {
        ddl += ' UNIQUE';
      }
      if (column.defaultValue) {
        ddl += ` DEFAULT ${column.defaultValue}`;
      }
      if (index < selectedTableSchema.columns.length - 1) {
        ddl += ',';
      }
      ddl += '\n';
    });

    // Add primary key constraint
    const primaryKeys = selectedTableSchema.columns.filter((c: any) => c.primaryKey);
    if (primaryKeys.length > 0) {
      ddl += `,\n  PRIMARY KEY (${primaryKeys.map((c: any) => c.name).join(', ')})`;
    }

    // Add foreign key constraints
    const foreignKeys = selectedTableSchema.columns.filter((c: any) => c.foreignKey);
    foreignKeys.forEach((column: any) => {
      if (column.foreignKey) {
        ddl += `,\n  FOREIGN KEY (${column.name}) REFERENCES ${column.foreignKey.table}(${column.foreignKey.column})`;
        ddl += ` ON DELETE ${column.foreignKey.onDelete} ON UPDATE ${column.foreignKey.onUpdate}`;
      }
    });

    ddl += '\n);';

    return ddl;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Schema Editor
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowAddTableDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Table
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Schema
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Navigation Tabs */}
          <div className="flex space-x-1 bg-muted p-1 rounded-lg mb-4">
            {[
              { id: 'tables', label: 'Tables', icon: TableIcon },
              { id: 'views', label: 'Views', icon: Eye },
              { id: 'indexes', label: 'Indexes', icon: Key },
              { id: 'relationships', label: 'Relations', icon: Link },
            ].map((tab: any) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors',
                  activeTab === tab.id
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                )}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </div>

          {/* Table Selection */}
          {activeTab === 'tables' && (
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Table:</label>
                <Select value={selectedTable} onValueChange={setSelectedTable}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select table" />
                  </SelectTrigger>
                  <SelectContent>
                    {schema?.tables.map((table: any) => (
                      <SelectItem key={table.name} value={table.name}>
                        <div className="flex items-center justify-between w-full">
                          <span className="font-mono">{table.name}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {table.rowCount} rows
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedTableSchema && (
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>{selectedTableSchema.description}</span>
                  <Badge variant="outline">{selectedTableSchema.size}</Badge>
                  <Badge variant="outline">{selectedTableSchema.columns.length} columns</Badge>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Content */}
      {activeTab === 'tables' && selectedTableSchema && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Table Structure */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <TableIcon className="h-5 w-5" />
                    Table Structure: {selectedTable}
                  </CardTitle>
                  <Button variant="outline" size="sm" onClick={() => setShowAddColumnDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Column
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Column Name</TableHead>
                        <TableHead>Data Type</TableHead>
                        <TableHead>Constraints</TableHead>
                        <TableHead>Default</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedTableSchema.columns.map((column: any) => (
                        <TableRow key={column.name}>
                          <TableCell className="font-mono font-medium">{column.name}</TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <span className={cn('font-mono', getColumnTypeColor(column.type))}>
                                {column.type}
                                {column.length && `(${column.length})`}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {column.primaryKey && <Badge className="text-xs">PK</Badge>}
                              {column.foreignKey && (
                                <Badge variant="outline" className="text-xs">
                                  FK
                                </Badge>
                              )}
                              {column.unique && !column.primaryKey && (
                                <Badge variant="secondary" className="text-xs">
                                  UNIQUE
                                </Badge>
                              )}
                              {!column.nullable && (
                                <Badge variant="destructive" className="text-xs">
                                  NOT NULL
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="font-mono text-sm">
                            {column.defaultValue ? (
                              <Badge variant="outline" className="text-xs">
                                {column.defaultValue}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground">—</span>
                            )}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {column.description || '—'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-6 w-6"
                                onClick={() => setEditingColumn(column)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-6 w-6 text-destructive hover:text-destructive"
                                onClick={() => handleDeleteColumn(column.name)}
                                disabled={column.primaryKey}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* DDL Preview */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  DDL Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm">
                    <pre className="bg-muted p-3 rounded-md overflow-x-auto text-xs font-mono">
                      {generateDDL()}
                    </pre>
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Copy DDL
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Table Info */}
            <Card className="mt-4">
              <CardHeader>
                <CardTitle>Table Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{selectedTableSchema.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Updated:</span>
                  <span>{selectedTableSchema.updatedAt.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Rows:</span>
                  <span>{selectedTableSchema.rowCount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Size:</span>
                  <span>{selectedTableSchema.size}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Columns:</span>
                  <span>{selectedTableSchema.columns.length}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Add Table Dialog */}
      <Dialog open={showAddTableDialog} onOpenChange={setShowAddTableDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Table</DialogTitle>
            <DialogDescription>Enter the details for the new database table.</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Table Name</label>
              <Input
                placeholder="Enter table name..."
                value={newTableName}
                onChange={(e: any) => setNewTableName(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Description (Optional)</label>
              <Textarea
                placeholder="Enter table description..."
                value={newTableDescription}
                onChange={(e: any) => setNewTableDescription(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddTableDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTable} disabled={!newTableName.trim()}>
              Create Table
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Column Dialog */}
      <Dialog open={showAddColumnDialog} onOpenChange={setShowAddColumnDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Column to {selectedTable}</DialogTitle>
            <DialogDescription>Configure the new column properties.</DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Column Name *</label>
              <Input
                placeholder="Enter column name..."
                value={newColumn.name || ''}
                onChange={(e: any) => setNewColumn((prev) => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Data Type *</label>
              <Select
                value={newColumn.type || ''}
                onValueChange={(value) => setNewColumn((prev) => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select data type" />
                </SelectTrigger>
                <SelectContent>
                  {dataTypes.map((type: any) => (
                    <SelectItem key={type} value={type}>
                      <span className={getColumnTypeColor(type)}>{type}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Length</label>
              <Input
                type="number"
                placeholder="Enter length..."
                value={newColumn.length || ''}
                onChange={(e: any) =>
                  setNewColumn((prev) => ({
                    ...prev,
                    length: parseInt(e.target.value) || undefined,
                  }))
                }
              />
            </div>
            <div>
              <label className="text-sm font-medium">Default Value</label>
              <Input
                placeholder="Enter default value..."
                value={newColumn.defaultValue || ''}
                onChange={(e: any) =>
                  setNewColumn((prev) => ({ ...prev, defaultValue: e.target.value }))
                }
              />
            </div>
            <div className="md:col-span-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea
                placeholder="Enter column description..."
                value={newColumn.description || ''}
                onChange={(e: any) => setNewColumn((prev) => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="md:col-span-2">
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="nullable"
                    checked={newColumn.nullable || false}
                    onCheckedChange={(checked) =>
                      setNewColumn((prev) => ({ ...prev, nullable: !!checked }))
                    }
                  />
                  <label htmlFor="nullable" className="text-sm">
                    Nullable
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="unique"
                    checked={newColumn.unique || false}
                    onCheckedChange={(checked) =>
                      setNewColumn((prev) => ({ ...prev, unique: !!checked }))
                    }
                  />
                  <label htmlFor="unique" className="text-sm">
                    Unique
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="primaryKey"
                    checked={newColumn.primaryKey || false}
                    onCheckedChange={(checked) =>
                      setNewColumn((prev) => ({ ...prev, primaryKey: !!checked }))
                    }
                  />
                  <label htmlFor="primaryKey" className="text-sm">
                    Primary Key
                  </label>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddColumnDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddColumn} disabled={!newColumn.name || !newColumn.type}>
              Add Column
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
