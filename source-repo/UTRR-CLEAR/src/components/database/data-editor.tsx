'use client';

import * as React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { api } from '~/trpc/react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '~/components/ui/dialog';
import { Badge } from '~/components/ui/badge';
import { toast } from '~/hooks/use-toast';
import {
  Database,
  Search,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Filter,
  Download,
  Upload,
  RefreshCw,
} from 'lucide-react';
import { cn } from '~/lib/utils';

interface TableInfo {
  name: string;
  rowCount: number;
  columns: ColumnInfo[];
  description?: string;
}

interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  primaryKey: boolean;
  foreignKey?: string;
  defaultValue?: string;
}

interface RowData {
  [key: string]: any;
}

interface EditingCell {
  rowId: string;
  columnName: string;
  value: string;
}

export function DataEditor() {
  const { user } = useAuth();
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [isLoading, setIsLoading] = useState(false);
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [tableData, setTableData] = useState<RowData[]>([]);
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newRowData, setNewRowData] = useState<RowData>({});
  const [filters, setFilters] = useState<Record<string, string>>({});

  // Mock data for demonstration
  useEffect(() => {
    const mockTables: TableInfo[] = [
      {
        name: 'projects',
        rowCount: 156,
        description: 'Main project records',
        columns: [
          { name: 'id', type: 'varchar', nullable: false, primaryKey: true },
          { name: 'name', type: 'varchar', nullable: false, primaryKey: false },
          { name: 'status', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'projectNumber', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'currentPhase', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'createdAt', type: 'timestamp', nullable: false, primaryKey: false },
          { name: 'updatedAt', type: 'timestamp', nullable: false, primaryKey: false },
        ],
      },
      {
        name: 'users',
        rowCount: 24,
        description: 'User accounts and profiles',
        columns: [
          { name: 'id', type: 'varchar', nullable: false, primaryKey: true },
          { name: 'name', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'email', type: 'varchar', nullable: false, primaryKey: false },
          { name: 'role', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'isActive', type: 'boolean', nullable: true, primaryKey: false },
          { name: 'createdAt', type: 'timestamp', nullable: false, primaryKey: false },
        ],
      },
      {
        name: 'utilities',
        rowCount: 342,
        description: 'Utility companies and infrastructure',
        columns: [
          { name: 'id', type: 'varchar', nullable: false, primaryKey: true },
          { name: 'companyName', type: 'varchar', nullable: false, primaryKey: false },
          { name: 'utilityType', type: 'varchar', nullable: false, primaryKey: false },
          { name: 'contactEmail', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'contactPhone', type: 'varchar', nullable: true, primaryKey: false },
          { name: 'isActive', type: 'boolean', nullable: true, primaryKey: false },
        ],
      },
      {
        name: 'conflicts',
        rowCount: 89,
        description: 'Utility conflicts and resolutions',
        columns: [
          { name: 'id', type: 'varchar', nullable: false, primaryKey: true },
          {
            name: 'projectId',
            type: 'varchar',
            nullable: false,
            primaryKey: false,
            foreignKey: 'projects.id',
          },
          {
            name: 'utility1Id',
            type: 'varchar',
            nullable: false,
            primaryKey: false,
            foreignKey: 'utilities.id',
          },
          {
            name: 'utility2Id',
            type: 'varchar',
            nullable: false,
            primaryKey: false,
            foreignKey: 'utilities.id',
          },
          { name: 'severity', type: 'varchar', nullable: false, primaryKey: false },
          { name: 'status', type: 'varchar', nullable: false, primaryKey: false },
          { name: 'description', type: 'text', nullable: true, primaryKey: false },
        ],
      },
    ];
    setTables(mockTables);

    if (mockTables.length > 0 && mockTables[0]) {
      setSelectedTable(mockTables[0].name);
    }
  }, []);

  // Generate mock data for selected table
  useEffect(() => {
    if (!selectedTable) return;

    const table = tables.find((t: any) => t.name === selectedTable);
    if (!table) return;

    setIsLoading(true);

    // Simulate API delay
    setTimeout(() => {
      const mockData: RowData[] = [];
      const recordsToShow = Math.min(rowsPerPage, table.rowCount);

      for (let i = 0; i < recordsToShow; i++) {
        const row: RowData = {};

        table.columns.forEach((column: any) => {
          switch (column.name) {
            case 'id':
              row[column.name] = `${selectedTable}_${1000 + i}`;
              break;
            case 'name':
              row[column.name] = `Sample ${selectedTable.slice(0, -1)} ${i + 1}`;
              break;
            case 'email':
              row[column.name] = `user${i + 1}@example.com`;
              break;
            case 'status':
              row[column.name] = ['Active', 'Pending', 'Complete', 'On Hold'][i % 4];
              break;
            case 'projectNumber':
              row[column.name] =
                `190${String(58 + i).padStart(3, '0')}-${String(900 + i).padStart(3, '0')}`;
              break;
            case 'currentPhase':
              row[column.name] = ['Planning', 'Design', 'Construction', 'Closeout'][i % 4];
              break;
            case 'companyName':
              row[column.name] = ['Electric Co', 'Gas Corp', 'Water Dept', 'Telecom Inc'][i % 4];
              break;
            case 'utilityType':
              row[column.name] = ['electric', 'gas', 'water', 'telecom'][i % 4];
              break;
            case 'contactEmail':
              row[column.name] = `contact${i + 1}@utility.com`;
              break;
            case 'contactPhone':
              row[column.name] =
                `(555) ${String(100 + i).padStart(3, '0')}-${String(1000 + i).padStart(4, '0')}`;
              break;
            case 'severity':
              row[column.name] = ['High', 'Medium', 'Low'][i % 3];
              break;
            case 'description':
              row[column.name] = `Sample description for record ${i + 1}`;
              break;
            case 'isActive':
              row[column.name] = i % 3 !== 0;
              break;
            case 'role':
              row[column.name] = ['Admin', 'Manager', 'Coordinator', 'Viewer'][i % 4];
              break;
            case 'createdAt':
            case 'updatedAt':
              const date = new Date();
              date.setDate(date.getDate() - i);
              row[column.name] = date.toISOString().split('T')[0];
              break;
            default:
              if (column.foreignKey) {
                row[column.name] = `fk_${Math.floor(Math.random() * 100) + 1}`;
              } else {
                row[column.name] = `value_${i + 1}`;
              }
          }
        });

        mockData.push(row);
      }

      setTableData(mockData);
      setIsLoading(false);
    }, 500);
  }, [selectedTable, currentPage, rowsPerPage, tables]);

  const selectedTableInfo = useMemo(() => {
    return tables.find((t: any) => t.name === selectedTable);
  }, [tables, selectedTable]);

  const filteredData = useMemo(() => {
    let data = tableData;

    // Apply search filter
    if (searchTerm) {
      data = data.filter((row: any) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([column, filterValue]) => {
      if (filterValue) {
        data = data.filter((row: any) =>
          String(row[column] || '')
            .toLowerCase()
            .includes(filterValue.toLowerCase())
        );
      }
    });

    return data;
  }, [tableData, searchTerm, filters]);

  const handleCellEdit = (rowIndex: number, columnName: string, value: any) => {
    setEditingCell({
      rowId: String(rowIndex),
      columnName,
      value: String(value),
    });
  };

  const handleSaveCell = () => {
    if (!editingCell || !selectedTableInfo) return;

    // In a real app, this would make an API call to update the database
    const rowIndex = parseInt(editingCell.rowId);
    const updatedData = [...tableData];
    if (updatedData[rowIndex]) {
      updatedData[rowIndex][editingCell.columnName] = editingCell.value;
      setTableData(updatedData);
    }
    setEditingCell(null);

    toast({
      title: 'Cell updated',
      description: 'The database record has been updated successfully.',
    });
  };

  const handleCancelEdit = () => {
    setEditingCell(null);
  };

  const handleDeleteRow = (rowIndex: number) => {
    // In a real app, this would make an API call to delete from database
    const updatedData = tableData.filter((_, index) => index !== rowIndex);
    setTableData(updatedData);

    toast({
      title: 'Record deleted',
      description: 'The database record has been deleted successfully.',
    });
  };

  const handleAddRow = () => {
    if (!selectedTableInfo) return;

    // In a real app, this would make an API call to insert into database
    const newRow: RowData = { ...newRowData };

    // Generate default values for required fields
    selectedTableInfo.columns.forEach((column: any) => {
      if (!newRow[column.name]) {
        if (column.primaryKey) {
          newRow[column.name] = `new_${Date.now()}`;
        } else if (!column.nullable) {
          newRow[column.name] = column.defaultValue || '';
        }
      }
    });

    setTableData([newRow, ...tableData]);
    setNewRowData({});
    setShowAddDialog(false);

    toast({
      title: 'Record added',
      description: 'New database record has been created successfully.',
    });
  };

  const formatCellValue = (value: any, column: ColumnInfo) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">NULL</span>;
    }

    if (column.type === 'boolean') {
      return <Badge variant={value ? 'default' : 'secondary'}>{value ? 'True' : 'False'}</Badge>;
    }

    if (column.foreignKey) {
      return (
        <Badge variant="outline" className="font-mono text-xs">
          {String(value)}
        </Badge>
      );
    }

    return String(value);
  };

  const getColumnTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'varchar':
      case 'text':
        return 'text-blue-600 dark:text-blue-400';
      case 'integer':
      case 'bigint':
        return 'text-green-600 dark:text-green-400';
      case 'boolean':
        return 'text-purple-600 dark:text-purple-400';
      case 'timestamp':
      case 'date':
        return 'text-orange-600 dark:text-orange-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Data Editor
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Table Selection */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Table:</label>
              <Select value={selectedTable} onValueChange={setSelectedTable}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Select table" />
                </SelectTrigger>
                <SelectContent>
                  {tables.map((table: any) => (
                    <SelectItem key={table.name} value={table.name}>
                      <div className="flex items-center justify-between w-full">
                        <span className="font-mono">{table.name}</span>
                        <Badge variant="outline" className="ml-2 text-xs">
                          {table.rowCount} rows
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedTableInfo && (
              <div className="text-sm text-muted-foreground">{selectedTableInfo.description}</div>
            )}
          </div>

          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search all fields..."
                className="pl-8"
                value={searchTerm}
                onChange={(e: any) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAddDialog(true)}
              disabled={!selectedTable}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Record
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">
                {selectedTable ? `Table: ${selectedTable}` : 'Select a table'}
              </h3>
              {selectedTableInfo && (
                <Badge variant="outline">
                  {filteredData.length} / {selectedTableInfo.rowCount} records
                </Badge>
              )}
            </div>
            {isLoading && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                Loading...
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {selectedTableInfo ? (
            <div className="space-y-4">
              {/* Column Headers with Filters */}
              <div className="grid gap-2 p-2 bg-muted/30 rounded-lg">
                <div className="text-sm font-medium mb-2">Column Filters:</div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  {selectedTableInfo.columns.slice(0, 8).map((column: any) => (
                    <div key={column.name} className="flex flex-col gap-1">
                      <label className="text-xs font-medium flex items-center gap-1">
                        <span>{column.name}</span>
                        {column.primaryKey && (
                          <Badge className="text-[10px] py-0 px-1 h-4">PK</Badge>
                        )}
                        {column.foreignKey && (
                          <Badge variant="outline" className="text-[10px] py-0 px-1 h-4">
                            FK
                          </Badge>
                        )}
                      </label>
                      <Input
                        placeholder={`Filter ${column.name}...`}
                        className="text-xs h-8"
                        value={filters[column.name] || ''}
                        onChange={(e: any) =>
                          setFilters((prev) => ({
                            ...prev,
                            [column.name]: e.target.value,
                          }))
                        }
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Data Table */}
              <div className="overflow-x-auto border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">#</TableHead>
                      {selectedTableInfo.columns.map((column: any) => (
                        <TableHead key={column.name} className="min-w-[120px]">
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-1">
                              <span className="font-medium">{column.name}</span>
                              {column.primaryKey && (
                                <Badge className="text-[10px] py-0 px-1 h-4">PK</Badge>
                              )}
                              {column.foreignKey && (
                                <Badge variant="outline" className="text-[10px] py-0 px-1 h-4">
                                  FK
                                </Badge>
                              )}
                            </div>
                            <span className={cn('text-xs', getColumnTypeColor(column.type))}>
                              {column.type}
                              {!column.nullable && <span className="text-red-500 ml-1">*</span>}
                            </span>
                          </div>
                        </TableHead>
                      ))}
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={selectedTableInfo.columns.length + 2}
                          className="text-center py-8"
                        >
                          {isLoading ? (
                            <div className="flex items-center justify-center gap-2">
                              <RefreshCw className="h-4 w-4 animate-spin" />
                              Loading data...
                            </div>
                          ) : (
                            <div className="text-muted-foreground">
                              {searchTerm || Object.values(filters).some((f) => f)
                                ? 'No records match your filters'
                                : 'No data available'}
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredData.map((row, rowIndex) => (
                        <TableRow key={rowIndex}>
                          <TableCell className="font-mono text-sm text-muted-foreground">
                            {rowIndex + 1}
                          </TableCell>
                          {selectedTableInfo.columns.map((column: any) => (
                            <TableCell key={column.name} className="font-mono text-sm">
                              {editingCell?.rowId === String(rowIndex) &&
                              editingCell?.columnName === column.name ? (
                                <div className="flex items-center gap-2">
                                  <Input
                                    value={editingCell.value}
                                    onChange={(e: any) =>
                                      setEditingCell({
                                        ...editingCell,
                                        value: e.target.value,
                                      })
                                    }
                                    className="h-8 text-sm"
                                    autoFocus
                                  />
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    className="h-6 w-6"
                                    onClick={handleSaveCell}
                                  >
                                    <Save className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    className="h-6 w-6"
                                    onClick={handleCancelEdit}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              ) : (
                                <div
                                  className="cursor-pointer hover:bg-muted/50 p-1 rounded min-h-[24px] flex items-center"
                                  onClick={() =>
                                    handleCellEdit(rowIndex, column.name, row[column.name])
                                  }
                                >
                                  {formatCellValue(row[column.name], column)}
                                </div>
                              )}
                            </TableCell>
                          ))}
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-6 w-6"
                                onClick={() => {
                                  const firstColumn = selectedTableInfo.columns[0];
                                  if (firstColumn) {
                                    handleCellEdit(
                                      rowIndex,
                                      firstColumn.name,
                                      row[firstColumn.name]
                                    );
                                  }
                                }}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-6 w-6 text-destructive hover:text-destructive"
                                onClick={() => handleDeleteRow(rowIndex)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>Rows per page:</span>
                  <Select
                    value={String(rowsPerPage)}
                    onValueChange={(value) => setRowsPerPage(Number(value))}
                  >
                    <SelectTrigger className="w-[70px] h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {currentPage} of{' '}
                    {Math.ceil((selectedTableInfo?.rowCount || 0) / rowsPerPage)}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={
                      currentPage >= Math.ceil((selectedTableInfo?.rowCount || 0) / rowsPerPage)
                    }
                  >
                    Next
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>Select a table to view and edit data</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Record Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Record to {selectedTable}</DialogTitle>
            <DialogDescription>
              Fill in the fields below to create a new record. Required fields are marked with *.
            </DialogDescription>
          </DialogHeader>

          {selectedTableInfo && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[400px] overflow-y-auto">
              {selectedTableInfo.columns.map((column: any) => (
                <div key={column.name} className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    {column.name}
                    {!column.nullable && <span className="text-red-500">*</span>}
                    <Badge variant="outline" className="text-xs">
                      {column.type}
                    </Badge>
                  </label>
                  <Input
                    placeholder={
                      column.defaultValue
                        ? `Default: ${column.defaultValue}`
                        : `Enter ${column.name}...`
                    }
                    value={newRowData[column.name] || ''}
                    onChange={(e: any) =>
                      setNewRowData((prev) => ({
                        ...prev,
                        [column.name]: e.target.value,
                      }))
                    }
                    disabled={column.primaryKey && column.name === 'id'} // Auto-generated IDs
                  />
                </div>
              ))}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddRow}>Add Record</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
