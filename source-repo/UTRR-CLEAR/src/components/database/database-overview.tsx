'use client';

import { useState } from 'react';
import { Plus, Database, Table2, Trash2, Edit3, ExternalLink, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Input } from '~/components/ui/input';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import { TableEditor } from './table-editor';
import { CreateTableDialog } from './create-table-dialog';

export function DatabaseOverview() {
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [showCreateTable, setShowCreateTable] = useState(false);
  const [tableToDelete, setTableToDelete] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const { data: tables, refetch } = api.database.getTables.useQuery();
  
  const deleteTable = api.database.deleteTable.useMutation({
    onSuccess: () => {
      toast({
        title: 'Table deleted',
        description: 'The table has been removed from the database.',
      });
      refetch();
      setTableToDelete(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const filteredTables = tables?.filter((table: any) =>
    table.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatSize = (size: string) => {
    return size || '0 B';
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Database Tables</h2>
          <p className="text-muted-foreground">
            Manage your database tables, columns, and relationships
          </p>
        </div>
        <Button onClick={() => setShowCreateTable(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Table
        </Button>
      </div>

      {/* Search */}
      <div className="max-w-sm">
        <Input
          placeholder="Search tables..."
          value={searchQuery}
          onChange={(e: any) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Tables Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTables?.map((table: any) => (
          <Card
            key={table.name}
            className="hover:shadow-lg transition-shadow cursor-pointer group"
            onClick={() => setSelectedTable(table.name)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="flex items-center gap-2">
                    <Table2 className="h-4 w-4 text-muted-foreground" />
                    {table.name}
                  </CardTitle>
                  {table.comment && (
                    <CardDescription className="text-xs">{table.comment}</CardDescription>
                  )}
                </div>
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e: any) => {
                      e.stopPropagation();
                      setTableToDelete(table.name);
                    }}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Columns</p>
                  <p className="font-semibold">{table.columnCount}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Rows</p>
                  <p className="font-semibold">{formatNumber(table.rowCount)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Size</p>
                  <p className="font-semibold">{formatSize(String(table.size))}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Type</p>
                  <Badge variant="secondary" className="text-xs">
                    Table
                  </Badge>
                </div>
              </div>
              
              <div className="mt-4 flex items-center justify-between">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full"
                  onClick={(e: any) => {
                    e.stopPropagation();
                    setSelectedTable(table.name);
                  }}
                >
                  <Edit3 className="h-3 w-3 mr-2" />
                  Edit Structure
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Empty State */}
        {filteredTables?.length === 0 && (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Database className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No tables found</h3>
              <p className="text-muted-foreground text-center mb-4">
                {searchQuery
                  ? `No tables matching "${searchQuery}"`
                  : 'Get started by creating your first table'}
              </p>
              {!searchQuery && (
                <Button onClick={() => setShowCreateTable(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Table
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Table Editor */}
      {selectedTable && (
        <TableEditor
          tableName={selectedTable}
          isOpen={!!selectedTable}
          onClose={() => setSelectedTable(null)}
          onRefresh={refetch}
        />
      )}

      {/* Create Table Dialog */}
      {showCreateTable && (
        <CreateTableDialog
          onClose={() => setShowCreateTable(false)}
          onSuccess={() => {
            setShowCreateTable(false);
            refetch();
          }}
        />
      )}

      {/* Delete Table Confirmation */}
      <AlertDialog open={!!tableToDelete} onOpenChange={() => setTableToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Table
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the table &quot;{tableToDelete}&quot;? This action cannot be
              undone and will permanently remove the table and all its data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => tableToDelete && deleteTable.mutate({ tableName: tableToDelete })}
            >
              Delete Table
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}