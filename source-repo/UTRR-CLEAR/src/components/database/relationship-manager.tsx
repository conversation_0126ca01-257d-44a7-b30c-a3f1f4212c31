'use client';

import { useState } from 'react';
import { ArrowRight, Plus } from 'lucide-react';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import { Card } from '~/components/ui/card';

interface RelationshipManagerProps {
  tableName: string;
  onClose: () => void;
  onSuccess: () => void;
}

export function RelationshipManager({ tableName, onClose, onSuccess }: RelationshipManagerProps) {
  const [selectedColumn, setSelectedColumn] = useState('');
  const [referencedTable, setReferencedTable] = useState('');
  const [referencedColumn, setReferencedColumn] = useState('');
  const [onDelete, setOnDelete] = useState<'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION'>('RESTRICT');
  const [onUpdate, setOnUpdate] = useState<'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION'>('CASCADE');

  const { data: tables } = api.database.getTables.useQuery();
  const { data: tableSchema } = api.database.getTableSchema.useQuery({ tableName });
  const { data: referencedTableSchema } = api.database.getTableSchema.useQuery(
    { tableName: referencedTable },
    { enabled: !!referencedTable }
  );

  const createRelationship = api.database.createRelationship.useMutation({
    onSuccess: () => {
      toast({
        title: 'Relationship created',
        description: 'Foreign key constraint has been added.',
      });
      onSuccess();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = () => {
    if (!selectedColumn || !referencedTable || !referencedColumn) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    createRelationship.mutate({
      tableName,
      foreignKey: {
        columnName: selectedColumn,
        referencedTable,
        referencedColumn,
        onDelete,
        onUpdate,
      },
    });
  };

  const actionDescriptions = {
    CASCADE: 'Automatically delete/update related records',
    'SET NULL': 'Set the foreign key to NULL',
    RESTRICT: 'Prevent deletion/update if related records exist',
    'NO ACTION': 'Similar to RESTRICT (deferred check)',
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create Relationship</DialogTitle>
          <DialogDescription>
            Create a foreign key relationship between {tableName} and another table
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Visual Relationship Builder */}
          <Card className="p-4 bg-muted/50">
            <div className="flex items-center justify-between">
              <div className="text-center">
                <p className="text-sm font-medium">{tableName}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  {selectedColumn || 'Select column'}
                </p>
              </div>
              <ArrowRight className="h-5 w-5 text-muted-foreground" />
              <div className="text-center">
                <p className="text-sm font-medium">{referencedTable || 'Select table'}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  {referencedColumn || 'Select column'}
                </p>
              </div>
            </div>
          </Card>

          {/* Column Selection */}
          <div className="space-y-2">
            <Label>Column from {tableName}</Label>
            <Select value={selectedColumn} onValueChange={setSelectedColumn}>
              <SelectTrigger>
                <SelectValue placeholder="Select a column" />
              </SelectTrigger>
              <SelectContent>
                {tableSchema?.columns
                  .filter((col: any) => !col.primaryKey)
                  .map((col: any) => (
                    <SelectItem key={col.name} value={col.name}>
                      <div className="flex items-center justify-between w-full">
                        <span>{col.name}</span>
                        <span className="text-xs text-muted-foreground ml-2">{col.type}</span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {/* Referenced Table */}
          <div className="space-y-2">
            <Label>Referenced Table</Label>
            <Select value={referencedTable} onValueChange={setReferencedTable}>
              <SelectTrigger>
                <SelectValue placeholder="Select a table" />
              </SelectTrigger>
              <SelectContent>
                {tables
                  ?.filter((t: any) => t.name !== tableName)
                  .map((table: any) => (
                    <SelectItem key={table.name} value={table.name}>
                      <div className="flex items-center justify-between w-full">
                        <span>{table.name}</span>
                        <span className="text-xs text-muted-foreground ml-2">
                          {table.rowCount} rows
                        </span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {/* Referenced Column */}
          {referencedTable && (
            <div className="space-y-2">
              <Label>Referenced Column</Label>
              <Select value={referencedColumn} onValueChange={setReferencedColumn}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a column" />
                </SelectTrigger>
                <SelectContent>
                  {referencedTableSchema?.columns
                    .filter((col: any) => col.primaryKey || col.unique)
                    .map((col: any) => (
                      <SelectItem key={col.name} value={col.name}>
                        <div className="flex items-center justify-between w-full">
                          <span>{col.name}</span>
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-muted-foreground">{col.type}</span>
                            {col.primaryKey && (
                              <span className="text-xs bg-primary/10 text-primary px-1 rounded">
                                PK
                              </span>
                            )}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Actions */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>On Delete</Label>
              <Select value={onDelete} onValueChange={(v: any) => setOnDelete(v)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(actionDescriptions).map(([action, desc]) => (
                    <SelectItem key={action} value={action}>
                      <div>
                        <div className="font-medium">{action}</div>
                        <div className="text-xs text-muted-foreground">{desc}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>On Update</Label>
              <Select value={onUpdate} onValueChange={(v: any) => setOnUpdate(v)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(actionDescriptions).map(([action, desc]) => (
                    <SelectItem key={action} value={action}>
                      <div>
                        <div className="font-medium">{action}</div>
                        <div className="text-xs text-muted-foreground">{desc}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={createRelationship.isPending}>
            {createRelationship.isPending ? (
              <>
                <Plus className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create Relationship
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}