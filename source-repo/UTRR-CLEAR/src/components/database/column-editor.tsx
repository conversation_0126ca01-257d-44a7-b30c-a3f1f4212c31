'use client';

import { useState } from 'react';
import { Check, Info } from 'lucide-react';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Switch } from '~/components/ui/switch';
import { Checkbox } from '~/components/ui/checkbox';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import { Card } from '~/components/ui/card';
import { Alert, AlertDescription } from '~/components/ui/alert';

interface ColumnEditorProps {
  tableName: string;
  column?: any;
  isAddingNew: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const dataTypes = [
  { value: 'text', label: 'Text', description: 'Variable-length text' },
  { value: 'integer', label: 'Number (Integer)', description: 'Whole numbers' },
  { value: 'decimal', label: 'Number (Decimal)', description: 'Numbers with decimals' },
  { value: 'boolean', label: 'Boolean', description: 'True/False values' },
  { value: 'date', label: 'Date', description: 'Date without time' },
  { value: 'timestamp', label: 'Date & Time', description: 'Date with time' },
  { value: 'json', label: 'JSON', description: 'JSON data structure' },
  { value: 'uuid', label: 'UUID', description: 'Unique identifier' },
];

export function ColumnEditor({
  tableName,
  column,
  isAddingNew,
  onClose,
  onSuccess,
}: ColumnEditorProps) {
  const [name, setName] = useState(column?.name || '');
  const [type, setType] = useState(column?.type || 'text');
  const [nullable, setNullable] = useState(column?.nullable ?? true);
  const [unique, setUnique] = useState(column?.unique || false);
  const [defaultValue, setDefaultValue] = useState(column?.defaultValue || '');
  const [primaryKey, setPrimaryKey] = useState(column?.primaryKey || false);
  const [showSqlPreview, setShowSqlPreview] = useState(false);

  const addColumn = api.database.addColumn.useMutation({
    onSuccess: () => {
      toast({
        title: 'Column added',
        description: `Column "${name}" has been added to ${tableName}.`,
      });
      onSuccess();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const modifyColumn = api.database.modifyColumn.useMutation({
    onSuccess: () => {
      toast({
        title: 'Column updated',
        description: `Column "${column.name}" has been updated.`,
      });
      onSuccess();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const { data: sqlPreview } = api.database.previewSql.useQuery(
    {
      operation: isAddingNew ? 'ADD_COLUMN' : 'MODIFY_COLUMN',
      params: isAddingNew
        ? {
            tableName,
            column: { name, type, nullable, unique, defaultValue, primaryKey },
          }
        : {
            tableName,
            columnName: column?.name,
            changes: {
              newName: name !== column?.name ? name : undefined,
              type,
              nullable,
              defaultValue: defaultValue || null,
            },
          },
    },
    { enabled: showSqlPreview }
  );

  const handleSubmit = () => {
    if (!name.trim()) {
      toast({
        title: 'Error',
        description: 'Column name is required',
        variant: 'destructive',
      });
      return;
    }

    if (isAddingNew) {
      addColumn.mutate({
        tableName,
        column: { name, type, nullable, unique, defaultValue, primaryKey },
      });
    } else {
      modifyColumn.mutate({
        tableName,
        columnName: column.name,
        changes: {
          newName: name !== column.name ? name : undefined,
          type,
          nullable,
          defaultValue: defaultValue || null,
        },
      });
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isAddingNew ? 'Add New Column' : 'Edit Column'}</DialogTitle>
          <DialogDescription>
            {isAddingNew
              ? `Add a new column to the ${tableName} table`
              : `Modify the "${column?.name}" column`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Column Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Column Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e: any) => setName(e.target.value)}
              placeholder="column_name"
              className="font-mono"
            />
            <p className="text-xs text-muted-foreground">
              Use lowercase letters, numbers, and underscores only
            </p>
          </div>

          {/* Data Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Data Type</Label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dataTypes.map((dataType: any) => (
                  <SelectItem key={dataType.value} value={dataType.value}>
                    <div>
                      <div className="font-medium">{dataType.label}</div>
                      <div className="text-xs text-muted-foreground">{dataType.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Constraints */}
          <Card className="p-4">
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Constraints</h4>
              
              {isAddingNew && (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="primaryKey"
                    checked={primaryKey}
                    onCheckedChange={(checked) => {
                      setPrimaryKey(!!checked);
                      if (checked) {
                        setNullable(false);
                        setUnique(true);
                      }
                    }}
                  />
                  <Label htmlFor="primaryKey" className="text-sm font-normal cursor-pointer">
                    Primary Key
                  </Label>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="nullable"
                  checked={!nullable}
                  onCheckedChange={(checked) => setNullable(!checked)}
                  disabled={primaryKey}
                />
                <Label htmlFor="nullable" className="text-sm font-normal cursor-pointer">
                  Required (NOT NULL)
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="unique"
                  checked={unique}
                  onCheckedChange={(checked) => setUnique(!!checked)}
                  disabled={primaryKey}
                />
                <Label htmlFor="unique" className="text-sm font-normal cursor-pointer">
                  Unique values only
                </Label>
              </div>
            </div>
          </Card>

          {/* Default Value */}
          <div className="space-y-2">
            <Label htmlFor="defaultValue">Default Value (Optional)</Label>
            <Input
              id="defaultValue"
              value={defaultValue}
              onChange={(e: any) => setDefaultValue(e.target.value)}
              placeholder="NULL"
              className="font-mono"
            />
            <p className="text-xs text-muted-foreground">
              Enter a default value or leave empty for NULL
            </p>
          </div>

          {/* SQL Preview */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <Button
                variant="link"
                size="sm"
                className="p-0 h-auto"
                onClick={() => setShowSqlPreview(!showSqlPreview)}
              >
                {showSqlPreview ? 'Hide' : 'Show'} SQL Preview
              </Button>
              {showSqlPreview && sqlPreview && (
                <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                  {sqlPreview.sql}
                </pre>
              )}
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={addColumn.isPending || modifyColumn.isPending}>
            {addColumn.isPending || modifyColumn.isPending ? (
              <>
                <Check className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : isAddingNew ? (
              'Add Column'
            ) : (
              'Update Column'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}