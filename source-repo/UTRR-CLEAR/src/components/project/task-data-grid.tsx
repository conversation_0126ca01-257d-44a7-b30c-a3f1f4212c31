"use client";

import React, { useState, useMemo } from 'react';
import { api } from '~/trpc/react';
import { DataGrid, type GridColumn, type ConditionalRule } from '~/components/ui/data-grid';
import { ConditionalFormattingDialog } from '~/components/ui/conditional-formatting-dialog';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Skeleton } from '~/components/ui/skeleton';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { 
  Calendar,
  Clock,
  User,
  Flag,
  CheckCircle2,
  AlertCircle,
  Palette,
  Plus,
  Download
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { safeLog } from '~/lib/error-handler';
import { TaskContextMenu } from './task-context-menu';

interface TaskDataGridProps {
  projectId: string;
  className?: string;
}

interface TaskRow {
  id: string;
  title: string;
  description: string | null;
  status: string;
  priority: 'low' | 'medium' | 'high' | 'critical' | null;
  assignedTo: string | null;
  startDate: string | null;
  endDate: string | null;
  dueDate: string | null;
  estimatedHours: number | null;
  actualHours: number | null;
  progressPercentage: number;
  phase: string | null;
  taskType: string;
  milestone: boolean;
  criticalPath: boolean;
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Priority indicator component
const PriorityBadge: React.FC<{ priority: string | null }> = ({ priority }) => {
  if (!priority) return <span className="text-muted-foreground">-</span>;
  
  const getVariant = (p: string) => {
    switch (p.toLowerCase()) {
      case 'critical': return 'destructive';
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  return (
    <Badge variant={getVariant(priority)} className="text-xs">
      {priority.charAt(0).toUpperCase() + priority.slice(1)}
    </Badge>
  );
};

// Status badge component
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getVariant = (s: string) => {
    const lower = s.toLowerCase();
    if (lower.includes('complete') || lower.includes('done')) return 'default';
    if (lower.includes('progress') || lower.includes('working')) return 'secondary';
    if (lower.includes('blocked') || lower.includes('stuck')) return 'destructive';
    if (lower.includes('pending') || lower.includes('waiting')) return 'outline';
    return 'outline';
  };

  return (
    <Badge variant={getVariant(status)} className="text-xs">
      {status}
    </Badge>
  );
};

// Progress bar component
const ProgressBar: React.FC<{ progress: number }> = ({ progress }) => {
  return (
    <div className="flex items-center gap-2 w-full">
      <div className="flex-1 bg-muted rounded-full h-2">
        <div 
          className="bg-primary h-2 rounded-full transition-all"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      <span className="text-xs text-muted-foreground min-w-[3rem]">
        {progress}%
      </span>
    </div>
  );
};

export const TaskDataGrid: React.FC<TaskDataGridProps> = ({ 
  projectId, 
  className 
}) => {
  const [conditionalRules, setConditionalRules] = useState<ConditionalRule[]>([
    // Default rules for task management
    {
      id: 'critical-priority',
      column: 'priority',
      operator: 'equals',
      value: 'critical',
      format: {
        backgroundColor: '#fee2e2',
        textColor: '#dc2626',
        fontWeight: 'bold',
      },
    },
    {
      id: 'completed-tasks',
      column: 'completed',
      operator: 'equals',
      value: 'true',
      format: {
        backgroundColor: '#dcfce7',
        textColor: '#16a34a',
      },
    },
    {
      id: 'overdue-tasks',
      column: 'completed',
      operator: 'equals',
      value: 'false',
      format: {
        backgroundColor: '#fed7aa',
        textColor: '#ea580c',
        fontWeight: 'bold',
      },
    },
  ]);

  const { 
    data: tasks, 
    isLoading, 
    error,
    refetch
  } = api.tasks.getProjectTasks.useQuery(
    { projectId, includeCompleted: true },
    { enabled: !!projectId }
  );

  const updateTaskMutation = api.tasks.update.useMutation({
    onSuccess: () => {
      void refetch();
    },
  });

  // Transform API data to grid format
  const gridData = useMemo((): TaskRow[] => {
    if (!tasks) return [];

    return tasks.map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      status: task.status,
      priority: task.priority,
      assignedTo: task.assignedToUser?.name || task.assignedTo,
      startDate: task.startDate,
      endDate: task.endDate,
      dueDate: task.dueDate,
      estimatedHours: task.estimatedHours,
      actualHours: task.actualHours,
      progressPercentage: task.progressPercentage,
      phase: task.phase,
      taskType: task.taskType,
      milestone: task.milestone,
      criticalPath: task.criticalPath,
      completed: task.completed,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    }));
  }, [tasks]);

  // Define grid columns with proper typing
  const columns = useMemo((): GridColumn<TaskRow>[] => [
    {
      accessorKey: 'title',
      header: 'Task Name',
      editable: true,
      dataType: 'text',
      minWidth: 200,
      cell: ({ getValue, row }) => (
        <TaskContextMenu
          task={{
            id: row.original.id,
            title: row.original.title,
            project_id: projectId,
            completed: row.original.completed,
          }}
          onSubtaskCreated={() => {
            // Refresh the data when a subtask is created
            // This could be enhanced to be more specific
          }}
        >
          <div className="flex items-center gap-2 cursor-pointer">
            {row.original.milestone && (
              <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            )}
            {row.original.criticalPath && (
              <Flag className="h-3 w-3 text-red-500" />
            )}
            <span className={cn(
              "truncate",
              row.original.completed && "line-through text-muted-foreground"
            )}>
              {getValue() as string}
            </span>
          </div>
        </TaskContextMenu>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      editable: true,
      dataType: 'status',
      width: 120,
      cell: ({ getValue }) => <StatusBadge status={getValue() as string} />,
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      editable: true,
      dataType: 'priority',
      width: 100,
      cell: ({ getValue }) => <PriorityBadge priority={getValue() as string | null} />,
    },
    {
      accessorKey: 'assignedTo',
      header: 'Assigned To',
      editable: true,
      dataType: 'text',
      width: 150,
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value ? (
          <div className="flex items-center gap-2">
            <User className="h-3 w-3 text-muted-foreground" />
            <span className="truncate">{value}</span>
          </div>
        ) : (
          <span className="text-muted-foreground">Unassigned</span>
        );
      },
    },
    {
      accessorKey: 'startDate',
      header: 'Start Date',
      editable: true,
      dataType: 'date',
      width: 120,
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value ? new Date(value).toLocaleDateString() : '-';
      },
    },
    {
      accessorKey: 'endDate',
      header: 'End Date',
      editable: true,
      dataType: 'date',
      width: 120,
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value ? new Date(value).toLocaleDateString() : '-';
      },
    },
    {
      accessorKey: 'dueDate',
      header: 'Due Date',
      editable: true,
      dataType: 'date',
      width: 120,
      cell: ({ getValue, row }) => {
        const value = getValue() as string | null;
        if (!value) return '-';
        
        const dueDate = new Date(value);
        const isOverdue = dueDate < new Date() && !row.original.completed;
        
        return (
          <div className={cn(
            "flex items-center gap-1",
            isOverdue && "text-red-600"
          )}>
            <Calendar className="h-3 w-3" />
            <span>{dueDate.toLocaleDateString()}</span>
            {isOverdue && <AlertCircle className="h-3 w-3" />}
          </div>
        );
      },
    },
    {
      accessorKey: 'progressPercentage',
      header: 'Progress',
      editable: true,
      dataType: 'number',
      width: 150,
      cell: ({ getValue }: any) => <ProgressBar progress={getValue() as number} />,
    },
    {
      accessorKey: 'estimatedHours',
      header: 'Est. Hours',
      editable: true,
      dataType: 'number',
      width: 100,
      cell: ({ getValue }) => {
        const value = getValue() as number | null;
        return value ? (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span>{value}h</span>
          </div>
        ) : '-';
      },
    },
    {
      accessorKey: 'actualHours',
      header: 'Actual Hours',
      editable: true,
      dataType: 'number',
      width: 100,
      cell: ({ getValue }) => {
        const value = getValue() as number | null;
        return value ? (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span>{value}h</span>
          </div>
        ) : '-';
      },
    },
    {
      accessorKey: 'phase',
      header: 'Phase',
      editable: true,
      dataType: 'text',
      width: 120,
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value || '-';
      },
    },
    {
      accessorKey: 'taskType',
      header: 'Type',
      editable: true,
      dataType: 'text',
      width: 100,
    },
    {
      accessorKey: 'completed',
      header: 'Complete',
      editable: true,
      dataType: 'boolean',
      width: 80,
      cell: ({ getValue }: any) => (
        <div className="flex justify-center">
          {getValue() ? (
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          ) : (
            <div className="h-4 w-4 border border-muted-foreground rounded" />
          )}
        </div>
      ),
    },
  ], []);

  // Handle cell edits
  const handleCellEdit = async (rowIndex: number, columnId: string, value: any) => {
    const task = gridData[rowIndex];
    if (!task) return;

    try {
      const updateData: Record<string, any> = {};
      
      // Map column IDs to API fields and ensure proper types
      switch (columnId) {
        case 'title':
          updateData.title = String(value);
          break;
        case 'status':
          updateData.status = String(value);
          break;
        case 'priority':
          updateData.priority = String(value);
          break;
        case 'assignedTo':
          updateData.assignedTo = String(value);
          break;
        case 'startDate':
          updateData.startDate = value ? String(value) : null;
          break;
        case 'endDate':
          updateData.endDate = value ? String(value) : null;
          break;
        case 'dueDate':
          updateData.dueDate = value ? String(value) : null;
          break;
        case 'progressPercentage':
          updateData.progressPercentage = Number(value);
          break;
        case 'estimatedHours':
          updateData.estimatedHours = Number(value);
          break;
        case 'actualHours':
          updateData.actualHours = Number(value);
          break;
        case 'phase':
          updateData.phase = value ? String(value) : null;
          break;
        case 'taskType':
          updateData.taskType = String(value);
          break;
        case 'completed':
          updateData.completed = Boolean(value);
          break;
        default:
          return;
      }

      await updateTaskMutation.mutateAsync({
        id: task.id,
        ...updateData,
      });
    } catch (error) {
      safeLog.error('Failed to update task:', { error: String(error) });
    }
  };

  // Handle export
  const handleExport = (data: TaskRow[]) => {
    const csvData = data.map(row => ({
      'Task Name': row.title,
      'Status': row.status,
      'Priority': row.priority || '',
      'Assigned To': row.assignedTo || '',
      'Start Date': row.startDate || '',
      'End Date': row.endDate || '',
      'Due Date': row.dueDate || '',
      'Progress': `${row.progressPercentage}%`,
      'Est. Hours': row.estimatedHours || '',
      'Actual Hours': row.actualHours || '',
      'Phase': row.phase || '',
      'Type': row.taskType,
      'Completed': row.completed ? 'Yes' : 'No',
    }));

    const csv = [
      Object.keys(csvData[0] || {}).join(','),
      ...csvData.map(row => Object.values(row).map(val => 
        typeof val === 'string' && val.includes(',') ? `"${val}"` : val
      ).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `project-tasks-${projectId}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5" />
            Project Tasks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load project tasks: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  if (!tasks || tasks.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5" />
            Project Tasks
          </CardTitle>
        </CardHeader>
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <CheckCircle2 className="h-12 w-12 text-muted-foreground mx-auto" />
            <div className="space-y-2">
              <h3 className="text-lg font-medium">No Tasks Found</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                This project doesn&apos;t have any tasks yet. Tasks will appear here once they are synced from Monday.com or created manually.
              </p>
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Task
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <DataGrid
        data={gridData}
        columns={columns}
        enableSorting={true}
        enableFiltering={true}
        enablePagination={true}
        enableSelection={true}
        enableColumnResizing={true}
        enableConditionalFormatting={true}
        enableInlineEditing={true}
        enableExport={true}
        pageSize={50}
        onCellEdit={handleCellEdit}
        onExport={handleExport}
        conditionalRules={conditionalRules}
        onConditionalRulesChange={setConditionalRules}
      />
    </div>
  );
};

export default TaskDataGrid;