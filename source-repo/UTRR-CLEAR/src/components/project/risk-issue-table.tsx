'use client';

import * as React from 'react';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  AlertTriangle,
  Shield,
  Plus,
  Search,
  Filter,
  Calendar,
  User,
  Eye,
  Edit,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { useToast } from '~/hooks/use-toast';

interface Issue {
  id: string;
  type: 'Risk' | 'Issue';
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  severity?: 'minor' | 'moderate' | 'major' | 'severe';
  reportedDate: Date;
  reportedBy: string;
  assignedTo?: string;
  targetResolutionDate?: Date;
  actualResolutionDate?: Date;
  category: string;
  impactArea: string;
  mitigation?: string;
  resolutionNotes?: string;
  relatedConflictId?: number;
  attachments?: string[];
  tags?: string[];
}

interface RiskIssueTableProps {
  projectId: string;
}

const typeColors = {
  Risk: 'bg-red-100 text-red-800',
  Issue: 'bg-orange-100 text-orange-800',
};

const statusColors = {
  open: 'bg-red-100 text-red-800',
  'in-progress': 'bg-yellow-100 text-yellow-800',
  resolved: 'bg-green-100 text-green-800',
  closed: 'bg-muted text-gray-800',
};

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

const severityColors = {
  minor: 'bg-blue-100 text-blue-800',
  moderate: 'bg-yellow-100 text-yellow-800',
  major: 'bg-orange-100 text-orange-800',
  severe: 'bg-red-100 text-red-800',
};

export function RiskIssueTable({ projectId }: RiskIssueTableProps) {
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'risks' | 'issues'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const { toast } = useToast();

  const utils = api.useUtils();

  const { data: issues, isLoading } = (api.projects as any).getRisksAndIssues?.useQuery(
    { projectId },
    { enabled: !!projectId }
  ) || { data: [], isLoading: false };

  const createIssueMutation = api.conflicts.create.useMutation({
    onSuccess: () => {
      void (utils.projects as any).getRisksAndIssues?.invalidate({ projectId });
      setIsAddDialogOpen(false);
      toast({
        title: 'Item created',
        description: 'The risk or issue has been successfully created.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create the item. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const updateIssueMutation = api.conflicts.update.useMutation({
    onSuccess: () => {
      void (utils.projects as any).getRisksAndIssues?.invalidate({ projectId });
      setIsDetailDialogOpen(false);
      toast({
        title: 'Item updated',
        description: 'The risk or issue has been successfully updated.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update the item. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Filter issues based on active tab, search, and filters
  const filteredIssues = React.useMemo(() => {
    if (!issues) return [];

    return issues.filter((issue: any) => {
      // Tab filter
      if (activeTab === 'risks' && issue.type !== 'Risk') return false;
      if (activeTab === 'issues' && issue.type !== 'Issue') return false;

      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          issue.title.toLowerCase().includes(query) ||
          issue.description.toLowerCase().includes(query) ||
          issue.category.toLowerCase().includes(query) ||
          issue.assignedTo?.toLowerCase().includes(query) ||
          issue.reportedBy.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (statusFilter && issue.status !== statusFilter) return false;

      // Priority filter
      if (priorityFilter && issue.priority !== priorityFilter) return false;

      return true;
    });
  }, [issues, activeTab, searchQuery, statusFilter, priorityFilter]);

  // Calculate summary statistics
  const stats = React.useMemo(() => {
    if (!issues) return { risks: 0, issues: 0, critical: 0, open: 0 };

    return {
      risks: issues.filter((i: any) => i.type === 'Risk').length,
      issues: issues.filter((i: any) => i.type === 'Issue').length,
      critical: issues.filter((i: any) => i.priority === 'critical').length,
      open: issues.filter((i: any) => i.status === 'open').length,
    };
  }, [issues]);

  const handleViewIssue = (issue: Issue) => {
    setSelectedIssue(issue);
    setIsDetailDialogOpen(true);
  };

  const handleStatusUpdate = (issueId: string, newStatus: string) => {
    updateIssueMutation.mutate({
      id: parseInt(issueId),
      status: newStatus as 'open' | 'in-progress' | 'resolved' | 'closed',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Risk & Issue Management
          </CardTitle>
          <CardDescription>Loading risk and issue data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-10 bg-muted rounded"></div>
            <div className="h-60 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!issues || issues.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Risk & Issue Management
          </CardTitle>
          <CardDescription>Track and manage project risks and issues</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="flex justify-center mb-4">
              <Shield className="h-16 w-16 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-medium text-gray-900">No risks or issues tracked</h3>
            <p className="text-muted-foreground mt-2 mb-6 max-w-md mx-auto">
              Start tracking project risks and issues to ensure successful project delivery and
              proactive problem management.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add First Item
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Risk & Issue Management
              </CardTitle>
              <CardDescription>Track and manage project risks and issues</CardDescription>
            </div>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Risk/Issue
            </Button>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div className="bg-red-50 p-3 rounded-lg border border-red-200">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <div className="text-sm font-medium text-red-600">Risks</div>
              </div>
              <div className="text-2xl font-bold text-red-600">{stats.risks}</div>
            </div>
            <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <div className="text-sm font-medium text-orange-600">Issues</div>
              </div>
              <div className="text-2xl font-bold text-orange-600">{stats.issues}</div>
            </div>
            <div className="bg-red-50 p-3 rounded-lg border border-red-200">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <div className="text-sm font-medium text-red-600">Critical</div>
              </div>
              <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
            </div>
            <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <div className="text-sm font-medium text-yellow-600">Open</div>
              </div>
              <div className="text-2xl font-bold text-yellow-600">{stats.open}</div>
            </div>
          </div>

          {/* Tabs and Filters */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mt-4">
            <Tabs
              value={activeTab}
              onValueChange={(value) => setActiveTab(value as 'all' | 'risks' | 'issues')}
            >
              <TabsList>
                <TabsTrigger value="all">All Items</TabsTrigger>
                <TabsTrigger value="risks">Risks Only</TabsTrigger>
                <TabsTrigger value="issues">Issues Only</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search items..."
                  className="pl-8 w-64"
                  value={searchQuery}
                  onChange={(e: any) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Priority</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {filteredIssues.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">No items found</h3>
              <p className="text-muted-foreground mt-2">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Assigned To</TableHead>
                    <TableHead>Reported</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredIssues.map((issue: any) => (
                    <TableRow
                      key={issue.id}
                      className="cursor-pointer hover:bg-muted"
                      onClick={() => handleViewIssue(issue)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {issue.type === 'Risk' ? (
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-orange-500" />
                          )}
                          <Badge className={typeColors[issue.type as keyof typeof typeColors]}>{issue.type}</Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{issue.title}</div>
                        <div className="text-sm text-muted-foreground truncate max-w-xs">
                          {issue.description}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{issue.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <Select
                          value={issue.status}
                          onValueChange={(value) => {
                            handleStatusUpdate(issue.id, value);
                          }}
                        >
                          <SelectTrigger
                            className="w-auto border-none shadow-none p-0 h-auto"
                            onClick={(e: any) => e.stopPropagation()}
                          >
                            <Badge className={statusColors[issue.status as keyof typeof statusColors]}>
                              {issue.status.replace('-', ' ')}
                            </Badge>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="open">Open</SelectItem>
                            <SelectItem value="in-progress">In Progress</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="closed">Closed</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Badge className={priorityColors[issue.priority as keyof typeof priorityColors]}>{issue.priority}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{issue.assignedTo || 'Unassigned'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {new Date(issue.reportedDate).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">by {issue.reportedBy}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e: any) => {
                              e.stopPropagation();
                              handleViewIssue(issue);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Issue Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedIssue?.type === 'Risk' ? (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-orange-500" />
              )}
              {selectedIssue?.title}
            </DialogTitle>
            <DialogDescription>
              {selectedIssue?.type} • {selectedIssue?.category} • Reported{' '}
              {selectedIssue && new Date(selectedIssue.reportedDate).toLocaleDateString()}
            </DialogDescription>
          </DialogHeader>

          {selectedIssue && (
            <div className="space-y-6">
              {/* Status and Priority Row */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                  <Badge className={statusColors[selectedIssue.status]} variant="secondary">
                    {selectedIssue.status.replace('-', ' ')}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Priority</Label>
                  <Badge className={priorityColors[selectedIssue.priority as keyof typeof priorityColors]} variant="secondary">
                    {selectedIssue.priority}
                  </Badge>
                </div>
                {selectedIssue.severity && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Severity</Label>
                    <Badge className={severityColors[selectedIssue.severity]} variant="secondary">
                      {selectedIssue.severity}
                    </Badge>
                  </div>
                )}
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Impact Area</Label>
                  <Badge variant="outline">{selectedIssue.impactArea}</Badge>
                </div>
              </div>

              {/* Description */}
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                  {selectedIssue.description}
                </div>
              </div>

              {/* Assignment and Dates */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Reported By</Label>
                  <div className="mt-1 text-sm">{selectedIssue.reportedBy}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Assigned To</Label>
                  <div className="mt-1 text-sm">{selectedIssue.assignedTo || 'Unassigned'}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Target Resolution</Label>
                  <div className="mt-1 text-sm">
                    {selectedIssue.targetResolutionDate
                      ? new Date(selectedIssue.targetResolutionDate).toLocaleDateString()
                      : 'Not set'}
                  </div>
                </div>
              </div>

              {/* Mitigation and Resolution */}
              {selectedIssue.mitigation && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Mitigation Strategy</Label>
                  <div className="mt-1 p-3 bg-blue-50 rounded-md text-sm">
                    {selectedIssue.mitigation}
                  </div>
                </div>
              )}

              {selectedIssue.resolutionNotes && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Resolution Notes</Label>
                  <div className="mt-1 p-3 bg-green-50 rounded-md text-sm">
                    {selectedIssue.resolutionNotes}
                  </div>
                </div>
              )}

              {/* Tags */}
              {selectedIssue.tags && selectedIssue.tags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Tags</Label>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {selectedIssue.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
              Close
            </Button>
            <Button
              onClick={() => {
                setIsDetailDialogOpen(false);
                // Note: Edit functionality to be implemented when edit dialog is created
                toast({
                  title: 'Feature coming soon',
                  description: 'Edit functionality will be available in a future update.',
                });
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Risk/Issue Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Risk or Issue</DialogTitle>
            <DialogDescription>
              Create a new risk or issue to track for this project.
            </DialogDescription>
          </DialogHeader>

          {/* Add form would go here - simplified for brevity */}
          <div className="space-y-4">
            <div className="text-center py-8 text-muted-foreground">
              <Plus className="h-12 w-12 mx-auto mb-4" />
              <p>Risk/Issue creation form would be implemented here</p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button disabled>Create Item</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
