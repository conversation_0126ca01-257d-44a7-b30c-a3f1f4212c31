"use client";

import { useState } from "react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "~/components/ui/context-menu";
import { Plus, Edit, Trash2, <PERSON><PERSON>, <PERSON><PERSON>ir<PERSON>, Circle } from "lucide-react";
import { CreateSubtaskDialog } from "./create-subtask-dialog";

interface TaskContextMenuProps {
  children: React.ReactNode;
  task: {
    id: string;
    title: string;
    project_id?: string;
    completed?: boolean;
  };
  onEdit?: (task: any) => void;
  onDelete?: (task: any) => void;
  onDuplicate?: (task: any) => void;
  onToggleComplete?: (task: any) => void;
  onSubtaskCreated?: () => void;
}

export function TaskContextMenu({
  children,
  task,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleComplete,
  onSubtaskCreated,
}: TaskContextMenuProps) {
  const [showCreateSubtask, setShowCreateSubtask] = useState(false);

  const handleCreateSubtask = () => {
    setShowCreateSubtask(true);
  };

  const handleEdit = () => {
    onEdit?.(task);
  };

  const handleDelete = () => {
    onDelete?.(task);
  };

  const handleDuplicate = () => {
    onDuplicate?.(task);
  };

  const handleToggleComplete = () => {
    onToggleComplete?.(task);
  };

  return (
    <>
      <ContextMenu>
        <ContextMenuTrigger asChild>
          {children}
        </ContextMenuTrigger>
        <ContextMenuContent className="w-64">
          <ContextMenuItem onClick={handleCreateSubtask}>
            <Plus className="mr-2 h-4 w-4" />
            Create Subtask
          </ContextMenuItem>
          
          <ContextMenuSeparator />
          
          {onEdit && (
            <ContextMenuItem onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Task
            </ContextMenuItem>
          )}
          
          {onDuplicate && (
            <ContextMenuItem onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" />
              Duplicate Task
            </ContextMenuItem>
          )}
          
          {onToggleComplete && (
            <ContextMenuItem onClick={handleToggleComplete}>
              {task.completed ? (
                <>
                  <Circle className="mr-2 h-4 w-4" />
                  Mark Incomplete
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Mark Complete
                </>
              )}
            </ContextMenuItem>
          )}
          
          <ContextMenuSeparator />
          
          {onDelete && (
            <ContextMenuItem 
              onClick={handleDelete}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Task
            </ContextMenuItem>
          )}
        </ContextMenuContent>
      </ContextMenu>

      <CreateSubtaskDialog
        open={showCreateSubtask}
        onOpenChange={setShowCreateSubtask}
        parentTask={task}
        onSuccess={onSubtaskCreated}
      />
    </>
  );
}