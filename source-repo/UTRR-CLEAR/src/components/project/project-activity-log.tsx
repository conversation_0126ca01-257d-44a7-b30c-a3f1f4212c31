'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { useToast } from '~/hooks/use-toast';
import { formatDate } from '~/lib/utils';
import {
  Plus,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Tag,
  FileText,
  ExternalLink,
  Edit,
  Trash2,
} from 'lucide-react';

// Activity log entry interface
interface ActivityLogEntry {
  id: string;
  title: string;
  description: string;
  category:
    | 'coordination'
    | 'technical'
    | 'administrative'
    | 'meeting'
    | 'communication'
    | 'milestone';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  assignedTo: string;
  reportedBy: string;
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  tags: string[];
  attachments: Array<{
    name: string;
    url: string;
    type: string;
    size: string;
  }>;
  relatedItems: Array<{
    type: 'utility' | 'conflict' | 'document' | 'meeting';
    id: string;
    name: string;
  }>;
}

interface ProjectActivityLogProps {
  projectId: string;
  entries?: ActivityLogEntry[];
  editable?: boolean;
}

export function ProjectActivityLog({
  projectId,
  entries: propEntries,
  editable = true,
}: ProjectActivityLogProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const { toast } = useToast();

  // Mock data - would be replaced with tRPC query
  const mockEntries: ActivityLogEntry[] = [
    {
      id: '1',
      title: 'Duke Energy field inspection completed',
      description:
        'Completed joint field inspection with Duke Energy crew. Confirmed location of primary electric feed and identified potential conflicts with proposed gas line relocation.',
      category: 'coordination',
      priority: 'high',
      status: 'completed',
      assignedTo: 'Sarah Johnson',
      reportedBy: 'Mike Chen',
      createdAt: '2024-01-20T09:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z',
      dueDate: '2024-01-20T17:00:00Z',
      tags: ['field-inspection', 'duke-energy', 'electric', 'conflicts'],
      attachments: [
        {
          name: 'field-inspection-report.pdf',
          url: '/attachments/field-inspection-report.pdf',
          type: 'application/pdf',
          size: '2.1 MB',
        },
        {
          name: 'conflict-photos.zip',
          url: '/attachments/conflict-photos.zip',
          type: 'application/zip',
          size: '15.3 MB',
        },
      ],
      relatedItems: [
        { type: 'utility', id: 'duke-energy-1', name: 'Duke Energy - Primary Feed' },
        { type: 'conflict', id: 'conflict-001', name: 'Electric/Gas Crossing Conflict' },
      ],
    },
    {
      id: '2',
      title: 'Citizens Gas permit application submitted',
      description:
        'Submitted permit application for gas line relocation work. Included all required engineering drawings and environmental assessments.',
      category: 'administrative',
      priority: 'medium',
      status: 'in_progress',
      assignedTo: 'John Smith',
      reportedBy: 'Project Manager',
      createdAt: '2024-01-19T14:00:00Z',
      updatedAt: '2024-01-19T16:45:00Z',
      dueDate: '2024-01-25T17:00:00Z',
      tags: ['permit', 'citizens-gas', 'relocation', 'environmental'],
      attachments: [
        {
          name: 'permit-application.pdf',
          url: '/attachments/permit-application.pdf',
          type: 'application/pdf',
          size: '3.7 MB',
        },
      ],
      relatedItems: [
        { type: 'utility', id: 'citizens-gas-1', name: 'Citizens Gas - Distribution Line' },
        { type: 'document', id: 'env-assessment-1', name: 'Environmental Assessment' },
      ],
    },
    {
      id: '3',
      title: 'Weekly coordination meeting',
      description:
        'Conducted weekly coordination meeting with all utility stakeholders. Discussed progress on conflict resolutions and updated project timeline.',
      category: 'meeting',
      priority: 'medium',
      status: 'completed',
      assignedTo: 'Project Manager',
      reportedBy: 'Project Manager',
      createdAt: '2024-01-18T10:00:00Z',
      updatedAt: '2024-01-18T11:30:00Z',
      tags: ['weekly-meeting', 'coordination', 'stakeholders', 'timeline'],
      attachments: [
        {
          name: 'meeting-minutes.docx',
          url: '/attachments/meeting-minutes.docx',
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          size: '245 KB',
        },
      ],
      relatedItems: [{ type: 'meeting', id: 'meeting-001', name: 'Weekly Coordination Meeting' }],
    },
  ];

  const entries = propEntries || mockEntries;

  // Filter entries
  const filteredEntries = entries.filter((entry: any) => {
    const matchesSearch =
      searchQuery === '' ||
      entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.tags.some((tag: any) => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = filterCategory === 'all' || entry.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || entry.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || entry.priority === filterPriority;

    return matchesSearch && matchesCategory && matchesStatus && matchesPriority;
  });

  // Get badge variants
  const getCategoryVariant = (category: string) => {
    switch (category) {
      case 'coordination':
        return 'default';
      case 'technical':
        return 'secondary';
      case 'administrative':
        return 'outline';
      case 'meeting':
        return 'default';
      case 'communication':
        return 'secondary';
      case 'milestone':
        return 'default';
      default:
        return 'outline';
    }
  };

  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      case 'low':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in_progress':
        return 'secondary';
      case 'pending':
        return 'outline';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Handle new entry creation
  const handleAddEntry = () => {
    // This would open a form dialog to create a new entry
    setShowAddDialog(true);
  };

  // Handle export
  const handleExport = () => {
    // Export filtered entries to CSV or PDF
    const csvContent = [
      ['Title', 'Category', 'Priority', 'Status', 'Assigned To', 'Created', 'Due Date'].join(','),
      ...filteredEntries.map((entry: any) =>
        [
          entry.title,
          entry.category,
          entry.priority,
          entry.status,
          entry.assignedTo,
          formatDate(new Date(entry.createdAt)),
          entry.dueDate ? formatDate(new Date(entry.dueDate)) : '',
        ]
          .map((field: any) => `"${field}"`)
          .join(',')
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `project-activity-log-${projectId}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: 'Activity log exported successfully.',
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Project Activity Log
            </CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              {editable && (
                <Button size="sm" onClick={handleAddEntry}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Entry
                </Button>
              )}
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search activities..."
                value={searchQuery}
                onChange={(e: any) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex gap-2">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="coordination">Coordination</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                  <SelectItem value="administrative">Administrative</SelectItem>
                  <SelectItem value="meeting">Meeting</SelectItem>
                  <SelectItem value="communication">Communication</SelectItem>
                  <SelectItem value="milestone">Milestone</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterPriority} onValueChange={setFilterPriority}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Activity</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Due Date</TableHead>
                {editable && <TableHead>Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEntries.map((entry: any) => (
                <TableRow key={entry.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{entry.title}</div>
                      <div className="text-sm text-muted-foreground truncate max-w-md">
                        {entry.description}
                      </div>
                      {entry.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {entry.tags.slice(0, 3).map((tag: any) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {entry.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{entry.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getCategoryVariant(entry.category)}>{entry.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getPriorityVariant(entry.priority)}>{entry.priority}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(entry.status)}>
                      {entry.status.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {entry.assignedTo}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {formatDate(new Date(entry.createdAt))}
                    </div>
                  </TableCell>
                  <TableCell>
                    {entry.dueDate ? (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {formatDate(new Date(entry.dueDate))}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  {editable && (
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredEntries.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No activity log entries found</p>
              {searchQuery && <p className="text-sm">Try adjusting your search or filters</p>}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Entry Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Activity Log Entry</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input id="title" placeholder="Enter activity title" />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" placeholder="Describe the activity" rows={3} />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="coordination">Coordination</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="administrative">Administrative</SelectItem>
                    <SelectItem value="meeting">Meeting</SelectItem>
                    <SelectItem value="communication">Communication</SelectItem>
                    <SelectItem value="milestone">Milestone</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex gap-4 justify-end">
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowAddDialog(false)}>Add Entry</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
