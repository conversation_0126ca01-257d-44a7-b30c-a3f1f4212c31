'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Progress } from '~/components/ui/progress';
import { Checkbox } from '~/components/ui/checkbox';
import {
  CalendarDays,
  Clock,
  Users,
  FileText,
  MapPin,
  AlertCircle,
  CheckCircle2,
  Circle,
  ArrowRight,
  GitBranch,
  Zap,
  Timer,
  DollarSign,
  Files,
  Link2,
  Calendar,
  Building2,
  Target,
  Flag,
  ChevronRight
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { cn } from '~/lib/utils';

// Workflow template structure
const TEMPLATE_STRUCTURE = {
  columns: {
    basic: ['Name', 'Subitems', 'Task Owner', 'Item ID'],
    status: ['Status', 'Priority', 'Task Type'],
    scheduling: ['Actual Timeline', 'Actual Duration', 'Target Schedule', 'Target Duration', 'Duration Variance'],
    dependencies: ['Dependent On', 'Backwards Schedule Tasks'],
    resources: ['DM Fee Hours', 'PM Fee Hours', 'CT Fee Hours', 'Assumed Utilities'],
    documentation: ['MHJ ID', 'Contract Documents', 'Workplans', 'Plan/Route Files', 'Standard Files'],
    location: ['Project Location'],
    automation: ['Automation Label', 'Last Updated'],
    relationships: ['Portfolio Link', 'Connected Templates']
  },
  statuses: {
    project: ['On-Track | On-Going', 'Complete', 'Issues', 'Theoretical | Proposed', 'Waiting on Client', 'Not Specified', 'N/A'],
    priority: ['Critical ⚠️', 'High', 'Medium', 'Low', 'Not Specified'],
    taskType: ['Schedule Task', 'Milestone', 'UC Task', 'Egis Staff Task', 'Utility Task', 'User Created Task', 'Approved OOS', 'Out of Scope (OOS)']
  }
};

// Sample tasks from the template
const TEMPLATE_TASKS = [
  {
    id: '1',
    name: 'Contact Egis PM and Provide Utility Input to Project Development Schedule',
    status: 'Theoretical | Proposed',
    priority: 'High',
    taskType: 'UC Task',
    targetSchedule: { start: '2023-08-15', end: '2023-08-21' },
    targetDuration: 7,
    mhjId: '1.11',
    owner: null,
    dependencies: [],
    phase: 'Initiation'
  },
  {
    id: '2',
    name: 'Determine Facilities in the Area by Reviewing Information from IUPPS',
    status: 'Theoretical | Proposed',
    priority: 'High',
    taskType: 'UC Task',
    targetSchedule: { start: '2023-08-22', end: '2023-08-28' },
    targetDuration: 7,
    mhjId: '2.04',
    owner: null,
    dependencies: ['1'],
    phase: 'Research'
  },
  {
    id: '3',
    name: 'Preliminary Field Meeting Scheduling',
    status: 'Theoretical | Proposed',
    priority: 'Medium',
    taskType: 'Egis Staff Task',
    targetSchedule: { start: '2023-11-13', end: '2024-01-11' },
    targetDuration: 60,
    owner: null,
    dependencies: ['2'],
    phase: 'Field Verification'
  },
  {
    id: '4',
    name: 'Attend Preliminary Field Meeting and Obtain Input from Utilities on Project',
    status: 'Theoretical | Proposed',
    priority: 'Critical ⚠️',
    taskType: 'Milestone',
    targetSchedule: { start: '2024-01-12', end: '2024-01-12' },
    targetDuration: 0,
    mhjId: '4.08',
    owner: null,
    dependencies: ['3'],
    phase: 'Field Verification'
  },
  {
    id: '5',
    name: 'Stage 3 Approval/Submittal',
    status: 'Theoretical | Proposed',
    priority: 'High',
    taskType: 'Milestone',
    targetSchedule: { start: '2025-04-17', end: '2025-05-20' },
    targetDuration: 34,
    backwardsSchedule: true,
    owner: null,
    dependencies: ['4'],
    phase: 'Approvals'
  }
];

// Workflow phases
const WORKFLOW_PHASES = [
  { name: 'Initiation', color: 'bg-blue-500', tasks: 3 },
  { name: 'Research', color: 'bg-purple-500', tasks: 7 },
  { name: 'Coordination', color: 'bg-indigo-500', tasks: 5 },
  { name: 'Field Verification', color: 'bg-green-500', tasks: 8 },
  { name: 'Conflict Analysis', color: 'bg-yellow-500', tasks: 6 },
  { name: 'Work Planning', color: 'bg-orange-500', tasks: 9 },
  { name: 'Agreements', color: 'bg-red-500', tasks: 12 },
  { name: 'Construction', color: 'bg-pink-500', tasks: 8 }
];

const getStatusIcon = (status: string) => {
  if (status === 'Complete') return <CheckCircle2 className="h-4 w-4 text-green-600" />;
  if (status === 'Issues') return <AlertCircle className="h-4 w-4 text-red-600" />;
  return <Circle className="h-4 w-4 text-gray-400" />;
};

const getPriorityColor = (priority: string) => {
  if (priority === 'Critical ⚠️') return 'text-red-600 bg-red-50 border-red-200';
  if (priority === 'High') return 'text-orange-600 bg-orange-50 border-orange-200';
  if (priority === 'Medium') return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  if (priority === 'Low') return 'text-blue-600 bg-blue-50 border-blue-200';
  return 'text-gray-600 bg-gray-50 border-gray-200';
};

const getTaskTypeIcon = (type: string) => {
  if (type === 'Milestone') return <Flag className="h-4 w-4" />;
  if (type === 'UC Task') return <Building2 className="h-4 w-4" />;
  if (type === 'Schedule Task') return <Calendar className="h-4 w-4" />;
  if (type === 'Egis Staff Task') return <Users className="h-4 w-4" />;
  if (type === 'Utility Task') return <Zap className="h-4 w-4" />;
  return <Circle className="h-4 w-4" />;
};

export function WorkflowTemplateView({ projectId }: { projectId: string }) {
  const [activeView, setActiveView] = useState('board');
  const [selectedPhase, setSelectedPhase] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');

  const filteredTasks = TEMPLATE_TASKS.filter(task => {
    if (selectedPhase !== 'all' && task.phase !== selectedPhase) return false;
    if (selectedStatus !== 'all' && task.status !== selectedStatus) return false;
    if (selectedPriority !== 'all' && task.priority !== selectedPriority) return false;
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Template Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle>Workflow Template Structure</CardTitle>
              <CardDescription>
                Template ID: 11378462 - Comprehensive utility coordination workflow with 29 columns and 87 tasks
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Link2 className="h-4 w-4 mr-2" />
                Connect to Workflow
              </Button>
              <Button size="sm">
                <GitBranch className="h-4 w-4 mr-2" />
                Create from Template
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Column Structure Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Template Columns (29 total)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(TEMPLATE_STRUCTURE.columns).map(([category, columns]) => (
              <div key={category} className="space-y-2">
                <h4 className="font-medium text-sm capitalize flex items-center gap-2">
                  {category === 'basic' && <FileText className="h-4 w-4" />}
                  {category === 'status' && <AlertCircle className="h-4 w-4" />}
                  {category === 'scheduling' && <CalendarDays className="h-4 w-4" />}
                  {category === 'dependencies' && <GitBranch className="h-4 w-4" />}
                  {category === 'resources' && <DollarSign className="h-4 w-4" />}
                  {category === 'documentation' && <Files className="h-4 w-4" />}
                  {category === 'location' && <MapPin className="h-4 w-4" />}
                  {category === 'automation' && <Zap className="h-4 w-4" />}
                  {category === 'relationships' && <Link2 className="h-4 w-4" />}
                  {category.replace(/([A-Z])/g, ' $1').trim()}
                </h4>
                <div className="space-y-1">
                  {columns.map(col => (
                    <div key={col} className="text-sm text-muted-foreground pl-6">
                      • {col}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Workflow Phases */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Workflow Phases</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {WORKFLOW_PHASES.map((phase, idx) => (
                <div key={phase.name} className="relative">
                  <div className="p-3 rounded-lg border bg-card hover:bg-accent transition-colors cursor-pointer">
                    <div className="flex items-center justify-between mb-2">
                      <div className={cn("w-3 h-3 rounded-full", phase.color)} />
                      <span className="text-sm text-muted-foreground">{phase.tasks} tasks</span>
                    </div>
                    <h4 className="font-medium text-sm">{phase.name}</h4>
                  </div>
                  {idx < WORKFLOW_PHASES.length - 1 && (
                    <ChevronRight className="absolute -right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground hidden md:block" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Task Management View */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Task Management</CardTitle>
            <div className="flex gap-2">
              <Tabs value={activeView} onValueChange={setActiveView}>
                <TabsList>
                  <TabsTrigger value="board">Board View</TabsTrigger>
                  <TabsTrigger value="timeline">Timeline</TabsTrigger>
                  <TabsTrigger value="table">Table</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-2 mb-4">
            <Select value={selectedPhase} onValueChange={setSelectedPhase}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Phases" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Phases</SelectItem>
                {WORKFLOW_PHASES.map(phase => (
                  <SelectItem key={phase.name} value={phase.name}>{phase.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {TEMPLATE_STRUCTURE.statuses.project.map(status => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                {TEMPLATE_STRUCTURE.statuses.priority.map(priority => (
                  <SelectItem key={priority} value={priority}>{priority}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Views */}
          {activeView === 'table' && (
            <ScrollArea className="h-[600px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]"></TableHead>
                    <TableHead>Task Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Target Schedule</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Dependencies</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.map(task => (
                    <TableRow key={task.id}>
                      <TableCell>
                        <Checkbox />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTaskTypeIcon(task.taskType)}
                          <div>
                            <div className="font-medium">{task.name}</div>
                            {task.mhjId && (
                              <div className="text-xs text-muted-foreground">ID: {task.mhjId}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {task.taskType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(task.status)}
                          <span className="text-sm">{task.status}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={cn("text-xs", getPriorityColor(task.priority))}>
                          {task.priority}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm">
                        {task.targetSchedule.start} - {task.targetSchedule.end}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Timer className="h-3 w-3" />
                          <span className="text-sm">{task.targetDuration}d</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {task.dependencies.length > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            {task.dependencies.length} deps
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}

          {activeView === 'board' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">To Do</h4>
                {filteredTasks.filter(t => t.status === 'Theoretical | Proposed').map(task => (
                  <Card key={task.id} className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h5 className="text-sm font-medium line-clamp-2">{task.name}</h5>
                        {getTaskTypeIcon(task.taskType)}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={cn("text-xs", getPriorityColor(task.priority))}>
                          {task.priority}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {task.targetDuration}d
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">In Progress</h4>
                {filteredTasks.filter(t => t.status === 'On-Track | On-Going').map(task => (
                  <Card key={task.id} className="p-3 border-blue-200 bg-blue-50/50">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h5 className="text-sm font-medium line-clamp-2">{task.name}</h5>
                        {getTaskTypeIcon(task.taskType)}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={cn("text-xs", getPriorityColor(task.priority))}>
                          {task.priority}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {task.targetDuration}d
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">Complete</h4>
                {filteredTasks.filter(t => t.status === 'Complete').map(task => (
                  <Card key={task.id} className="p-3 border-green-200 bg-green-50/50">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h5 className="text-sm font-medium line-clamp-2">{task.name}</h5>
                        {getTaskTypeIcon(task.taskType)}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={cn("text-xs", getPriorityColor(task.priority))}>
                          {task.priority}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {task.targetDuration}d
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {activeView === 'timeline' && (
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Timeline view showing task dependencies and critical path
              </div>
              <div className="relative">
                {filteredTasks.map((task, idx) => (
                  <div key={task.id} className="flex items-center gap-4 mb-4">
                    <div className="w-32 text-right text-sm">
                      {task.targetSchedule.start}
                    </div>
                    <div className="flex-1 relative">
                      <div 
                        className={cn(
                          "h-8 rounded flex items-center px-3 text-xs font-medium",
                          task.taskType === 'Milestone' ? "bg-purple-500 text-white" : "bg-blue-500 text-white"
                        )}
                        style={{ width: `${Math.max(task.targetDuration * 2, 60)}px` }}
                      >
                        {task.name.slice(0, 30)}...
                      </div>
                      {task.dependencies.length > 0 && idx > 0 && (
                        <div className="absolute -top-4 left-0 h-4 w-px bg-gray-300" />
                      )}
                    </div>
                    <div className="w-20 text-sm">
                      {task.targetDuration}d
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Template Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Dependencies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Strict Mode</span>
                <Badge variant="default">Enabled</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Dependencies</span>
                <span className="font-medium">47</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Critical Path</span>
                <span className="font-medium">12 tasks</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Automation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">Send Letter Task</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">Receive Letter Task</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">Duration Variance Calc</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Connected Boards</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-sm">Subtask Board</div>
              <div className="text-xs text-muted-foreground">ID: 9271218513</div>
              <div className="mt-3 text-sm">5 Template Boards</div>
              <div className="text-xs text-muted-foreground">Portfolio connections</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}