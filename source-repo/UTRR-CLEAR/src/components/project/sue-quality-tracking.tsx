'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Textarea } from '~/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { ScrollArea } from '~/components/ui/scroll-area';
import { format } from 'date-fns';
import {
  MapPin,
  Info,
  Calendar,
  Clock,
  FileText,
  User,
  Building,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ArrowRight,
  CircleAlert,
  Edit,
  Plus,
  Search,
  Layers,
  Map,
  Filter,
  Upload,
  Download,
  ThumbsUp,
  Loader2,
  Cable,
} from 'lucide-react';

interface SueQualityTrackingProps {
  projectId: string;
}

interface SueRecord {
  id: string;
  utilityName: string;
  utilityType: string;
  qualityLevel: 'D' | 'C' | 'B' | 'A';
  dateCollected: string;
  collectionMethod: string;
  collectedBy: string;
  areaDescription?: string;
  notes?: string;
  attachments?: SueAttachment[];
  status: 'verified' | 'unverified' | 'needs_review';
  conflictRisk?: 'low' | 'medium' | 'high' | 'critical';
  lastUpdated: string;
}

interface SueAttachment {
  id: string;
  fileName: string;
  fileType: string;
  uploadDate: string;
  uploadedBy: string;
  url: string;
}

interface NewSueRecordData {
  utilityName: string;
  utilityType: string;
  qualityLevel: 'D' | 'C' | 'B' | 'A';
  dateCollected: string;
  collectionMethod: string;
  collectedBy: string;
  areaDescription?: string;
  notes?: string;
  status: 'verified' | 'unverified' | 'needs_review';
  conflictRisk?: 'low' | 'medium' | 'high' | 'critical';
}

interface UpdateSueRecordData {
  qualityLevel?: 'D' | 'C' | 'B' | 'A';
  status?: 'verified' | 'unverified' | 'needs_review';
  notes?: string;
  conflictRisk?: 'low' | 'medium' | 'high' | 'critical';
}

export function SueQualityTracking({ projectId }: SueQualityTrackingProps) {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('utilities');
  const [selectedQualityLevel, setSelectedQualityLevel] = useState<'D' | 'C' | 'B' | 'A' | 'all'>(
    'all'
  );
  const [filter, setFilter] = useState('');
  const [selectedUtilityTypes, setSelectedUtilityTypes] = useState<string[]>([]);

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDetailsDialogOpen, setIsViewDetailsDialogOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<SueRecord | null>(null);

  const [newRecordData, setNewRecordData] = useState<NewSueRecordData>({
    utilityName: '',
    utilityType: 'Electric',
    qualityLevel: 'D',
    dateCollected: new Date().toISOString().substring(0, 10),
    collectionMethod: 'Record Research',
    collectedBy: '',
    status: 'unverified',
  });

  // Fetch SUE records
  const { data: sueRecords, isLoading } = useQuery<SueRecord[]>({
    queryKey: [`/api/projects/${projectId}/sue-records`],
    enabled: !!projectId,
    queryFn: async () => {
      // Mock data for development
      return [
        {
          id: '1',
          utilityName: 'Water Main 24"',
          utilityType: 'Water',
          qualityLevel: 'B',
          dateCollected: '2023-05-10',
          collectionMethod: 'GPR Survey',
          collectedBy: 'Acme Subsurface',
          areaDescription: 'Main Street to Oak Avenue',
          notes: 'Detected 24-inch water main at approximately 4 feet deep.',
          status: 'verified',
          conflictRisk: 'medium',
          lastUpdated: '2023-05-12',
          attachments: [
            {
              id: 'a1',
              fileName: 'water_main_survey.pdf',
              fileType: 'pdf',
              uploadDate: '2023-05-10',
              uploadedBy: 'John Smith',
              url: '/attachments/water_main_survey.pdf',
            },
            {
              id: 'a2',
              fileName: 'water_main_photos.zip',
              fileType: 'zip',
              uploadDate: '2023-05-10',
              uploadedBy: 'John Smith',
              url: '/attachments/water_main_photos.zip',
            },
          ],
        },
        {
          id: '2',
          utilityName: 'Gas Pipeline 6"',
          utilityType: 'Gas',
          qualityLevel: 'A',
          dateCollected: '2023-06-15',
          collectionMethod: 'Vacuum Excavation',
          collectedBy: 'Acme Subsurface',
          areaDescription: 'Pine Street Intersection',
          notes: 'Test hole confirmed 6-inch gas line at 3.5 feet deep.',
          status: 'verified',
          conflictRisk: 'high',
          lastUpdated: '2023-06-16',
          attachments: [
            {
              id: 'a3',
              fileName: 'gas_line_testhole.pdf',
              fileType: 'pdf',
              uploadDate: '2023-06-15',
              uploadedBy: 'Jane Doe',
              url: '/attachments/gas_line_testhole.pdf',
            },
          ],
        },
        {
          id: '3',
          utilityName: 'Electric Duct Bank',
          utilityType: 'Electric',
          qualityLevel: 'C',
          dateCollected: '2023-04-05',
          collectionMethod: 'Field Observation',
          collectedBy: 'City Power Co.',
          areaDescription: 'Maple Street to Elm Road',
          notes: 'Visible manholes indicate underground duct bank following roadway.',
          status: 'unverified',
          conflictRisk: 'medium',
          lastUpdated: '2023-04-05',
        },
        {
          id: '4',
          utilityName: 'Fiber Optic Cable',
          utilityType: 'Telecommunications',
          qualityLevel: 'D',
          dateCollected: '2023-03-20',
          collectionMethod: 'Record Research',
          collectedBy: 'TeleCorp Inc.',
          areaDescription: 'Full project corridor',
          notes: 'Records from 2015 show fiber installation along west side of road.',
          status: 'needs_review',
          conflictRisk: 'low',
          lastUpdated: '2023-03-22',
        },
        {
          id: '5',
          utilityName: 'Sanitary Sewer 18"',
          utilityType: 'Sewer',
          qualityLevel: 'B',
          dateCollected: '2023-05-25',
          collectionMethod: 'GPR and EM Survey',
          collectedBy: 'Acme Subsurface',
          areaDescription: 'Oak Street to Maple Avenue',
          notes: 'Located 18-inch VCP sanitary line at varying depths (6-12 feet).',
          status: 'verified',
          conflictRisk: 'medium',
          lastUpdated: '2023-05-26',
        },
        {
          id: '6',
          utilityName: 'Storm Drain 36"',
          utilityType: 'Stormwater',
          qualityLevel: 'C',
          dateCollected: '2023-04-12',
          collectionMethod: 'Field Observation',
          collectedBy: 'County Engineering',
          areaDescription: 'Main Street culvert',
          notes: 'Visible headwall and outfall confirmed, but alignment uncertain.',
          status: 'verified',
          conflictRisk: 'high',
          lastUpdated: '2023-04-14',
        },
        {
          id: '7',
          utilityName: 'Petroleum Pipeline',
          utilityType: 'Oil/Petroleum',
          qualityLevel: 'B',
          dateCollected: '2023-06-02',
          collectionMethod: 'EMI Survey',
          collectedBy: 'PetroTech Solutions',
          areaDescription: 'Northeast project boundary',
          notes: 'Located 8-inch petroleum transmission line roughly parallel to highway.',
          status: 'unverified',
          conflictRisk: 'critical',
          lastUpdated: '2023-06-03',
        },
      ];
    },
  });

  // Add SUE record mutation
  const addSueRecordMutation = useMutation({
    mutationFn: async (data: NewSueRecordData) => {
      // In a real implementation, this would be an API call
      return { success: true, data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/sue-records`] });
      setIsAddDialogOpen(false);
      resetNewRecordForm();
    },
  });

  // Update SUE record mutation
  const updateSueRecordMutation = useMutation({
    mutationFn: async ({ recordId, data }: { recordId: string; data: UpdateSueRecordData }) => {
      // In a real implementation, this would be an API call
      return { success: true, recordId, data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/sue-records`] });
      setIsEditDialogOpen(false);
      setCurrentRecord(null);
    },
  });

  // Filter SUE records based on selected quality level and filter text
  const filteredRecords = React.useMemo(() => {
    if (!sueRecords) return [];

    return sueRecords.filter((record: any) => {
      // Filter by quality level
      if (selectedQualityLevel !== 'all' && record.qualityLevel !== selectedQualityLevel) {
        return false;
      }

      // Filter by utility types if any are selected
      if (selectedUtilityTypes.length > 0 && !selectedUtilityTypes.includes(record.utilityType)) {
        return false;
      }

      // Filter by text
      if (
        filter &&
        !record.utilityName.toLowerCase().includes(filter.toLowerCase()) &&
        !record.utilityType.toLowerCase().includes(filter.toLowerCase()) &&
        !record.areaDescription?.toLowerCase().includes(filter.toLowerCase())
      ) {
        return false;
      }

      return true;
    });
  }, [sueRecords, selectedQualityLevel, filter, selectedUtilityTypes]);

  // Get all unique utility types for filtering
  const utilityTypes = React.useMemo(() => {
    if (!sueRecords) return [];

    const types = new Set<string>();
    sueRecords.forEach((record: any) => {
      types.add(record.utilityType);
    });

    return Array.from(types).sort();
  }, [sueRecords]);

  // Count records by quality level
  const qualityLevelCounts = React.useMemo(() => {
    if (!sueRecords) return { A: 0, B: 0, C: 0, D: 0 };

    return sueRecords.reduce(
      (counts, record) => {
        counts[record.qualityLevel]++;
        return counts;
      },
      { A: 0, B: 0, C: 0, D: 0 } as Record<'A' | 'B' | 'C' | 'D', number>
    );
  }, [sueRecords]);

  // Reset new record form
  const resetNewRecordForm = () => {
    setNewRecordData({
      utilityName: '',
      utilityType: 'Electric',
      qualityLevel: 'D',
      dateCollected: new Date().toISOString().substring(0, 10),
      collectionMethod: 'Record Research',
      collectedBy: '',
      status: 'unverified',
    });
  };

  // Handle new record form changes
  const handleNewRecordChange = <K extends keyof NewSueRecordData>(
    key: K,
    value: NewSueRecordData[K]
  ) => {
    setNewRecordData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle utility type toggle for filtering
  const toggleUtilityTypeFilter = (type: string) => {
    setSelectedUtilityTypes((prev) => {
      if (prev.includes(type)) {
        return prev.filter((t: any) => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  // Helper to get quality level badge color
  const getQualityLevelColor = (level: 'D' | 'C' | 'B' | 'A') => {
    switch (level) {
      case 'A':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'B':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'C':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'D':
        return 'bg-muted text-gray-800 border-border';
      default:
        return 'bg-muted text-gray-800 border-border';
    }
  };

  // Helper to get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'unverified':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'needs_review':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-muted text-gray-800 border-border';
    }
  };

  // Helper to get risk badge color
  const getRiskColor = (risk?: string) => {
    switch (risk) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-muted text-gray-800 border-border';
    }
  };

  // Handle view details of a record
  const handleViewDetails = (record: SueRecord) => {
    setCurrentRecord(record);
    setIsViewDetailsDialogOpen(true);
  };

  // Handle edit record
  const handleEditRecord = (record: SueRecord) => {
    setCurrentRecord(record);
    setIsEditDialogOpen(true);
  };

  // Handle update record submit
  const handleUpdateRecord = () => {
    if (!currentRecord) return;

    const updateData: UpdateSueRecordData = {};

    // Only include fields that have changed
    if (
      currentRecord.qualityLevel !==
      (document.getElementById('edit-quality-level') as HTMLSelectElement)?.value
    ) {
      updateData.qualityLevel = (document.getElementById('edit-quality-level') as HTMLSelectElement)
        ?.value as 'D' | 'C' | 'B' | 'A';
    }

    if (
      currentRecord.status !== (document.getElementById('edit-status') as HTMLSelectElement)?.value
    ) {
      updateData.status = (document.getElementById('edit-status') as HTMLSelectElement)?.value as
        | 'verified'
        | 'unverified'
        | 'needs_review';
    }

    if (
      currentRecord.conflictRisk !==
      (document.getElementById('edit-conflict-risk') as HTMLSelectElement)?.value
    ) {
      updateData.conflictRisk = (document.getElementById('edit-conflict-risk') as HTMLSelectElement)
        ?.value as 'low' | 'medium' | 'high' | 'critical';
    }

    const notesValue = (document.getElementById('edit-notes') as HTMLTextAreaElement)?.value;
    if (currentRecord.notes !== notesValue) {
      updateData.notes = notesValue;
    }

    if (Object.keys(updateData).length > 0) {
      updateSueRecordMutation.mutate({
        recordId: currentRecord.id,
        data: updateData,
      });
    } else {
      setIsEditDialogOpen(false);
    }
  };

  // Handle add record submit
  const handleAddRecord = () => {
    addSueRecordMutation.mutate(newRecordData);
  };

  // Collection method options based on quality level
  const getCollectionMethodOptions = (qualityLevel: 'D' | 'C' | 'B' | 'A') => {
    switch (qualityLevel) {
      case 'A':
        return [
          'Vacuum Excavation',
          'Test Pits',
          'Potholing',
          'Hydro Excavation',
          'Hand Excavation',
        ];
      case 'B':
        return [
          'GPR Survey',
          'EM Survey',
          'EMI Survey',
          'Magnetic Survey',
          'Acoustic Survey',
          'MultiSensor Survey',
        ];
      case 'C':
        return [
          'Visual Observation',
          'Field Survey',
          'Manhole Inspection',
          'Valve Location',
          'Surface Feature Survey',
        ];
      case 'D':
        return [
          'Record Research',
          'As-Built Plans',
          'Utility Maps',
          'Historic Documents',
          'Verbal Information',
          'Institutional Knowledge',
        ];
      default:
        return ['Unknown'];
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SUE Quality Level Tracking</CardTitle>
          <CardDescription>Loading SUE data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-60">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div>
            <CardTitle>SUE Quality Level Tracking</CardTitle>
            <CardDescription>
              Track and manage Subsurface Utility Engineering (SUE) quality levels
            </CardDescription>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add SUE Record
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="utilities" onValueChange={setActiveTab}>
          <TabsList className="grid w-[400px] grid-cols-2">
            <TabsTrigger value="utilities">
              <Cable className="h-4 w-4 mr-2" />
              Utility Records
            </TabsTrigger>
            <TabsTrigger value="map">
              <Map className="h-4 w-4 mr-2" />
              Quality Map
            </TabsTrigger>
          </TabsList>

          <TabsContent value="utilities" className="mt-4 space-y-6">
            {/* Quality Level Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card
                className="relative border-2 border-green-300 overflow-hidden cursor-pointer"
                onClick={() => setSelectedQualityLevel(selectedQualityLevel === 'A' ? 'all' : 'A')}
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-green-500"></div>
                <CardContent className="p-4">
                  <h3 className="text-lg font-medium">Quality Level A</h3>
                  <div className="mt-1 text-sm text-muted-foreground">Precise Location</div>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="bg-green-50">
                      {qualityLevelCounts.A} Records
                    </Badge>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p className="max-w-xs">
                            Quality Level A: Precise horizontal and vertical location determined by
                            exposure and measurement.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
              <Card
                className="relative border-2 border-blue-300 overflow-hidden cursor-pointer"
                onClick={() => setSelectedQualityLevel(selectedQualityLevel === 'B' ? 'all' : 'B')}
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-blue-500"></div>
                <CardContent className="p-4">
                  <h3 className="text-lg font-medium">Quality Level B</h3>
                  <div className="mt-1 text-sm text-muted-foreground">Detected Location</div>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="bg-blue-50">
                      {qualityLevelCounts.B} Records
                    </Badge>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p className="max-w-xs">
                            Quality Level B: Horizontal location determined through geophysical
                            methods (GPR, EM, etc.).
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
              <Card
                className="relative border-2 border-yellow-300 overflow-hidden cursor-pointer"
                onClick={() => setSelectedQualityLevel(selectedQualityLevel === 'C' ? 'all' : 'C')}
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-yellow-500"></div>
                <CardContent className="p-4">
                  <h3 className="text-lg font-medium">Quality Level C</h3>
                  <div className="mt-1 text-sm text-muted-foreground">Surveyed Surface</div>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="bg-yellow-50">
                      {qualityLevelCounts.C} Records
                    </Badge>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p className="max-w-xs">
                            Quality Level C: Surface visible features correlated with existing
                            records.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
              <Card
                className="relative border-2 border-gray-300 overflow-hidden cursor-pointer"
                onClick={() => setSelectedQualityLevel(selectedQualityLevel === 'D' ? 'all' : 'D')}
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-muted0"></div>
                <CardContent className="p-4">
                  <h3 className="text-lg font-medium">Quality Level D</h3>
                  <div className="mt-1 text-sm text-muted-foreground">Record Information</div>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="bg-muted">
                      {qualityLevelCounts.D} Records
                    </Badge>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p className="max-w-xs">
                            Quality Level D: Information derived from existing records or oral
                            recollections.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filter Controls */}
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <div className="flex-1">
                <Input
                  placeholder="Filter by utility name or description..."
                  value={filter}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}
                  className="max-w-md"
                />
              </div>
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">Quality Level:</p>
                <Select
                  value={selectedQualityLevel}
                  onValueChange={(value) => setSelectedQualityLevel(value as 'all' | 'D' | 'C' | 'B' | 'A')}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="A">Level A</SelectItem>
                    <SelectItem value="B">Level B</SelectItem>
                    <SelectItem value="C">Level C</SelectItem>
                    <SelectItem value="D">Level D</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Utility Type Filters */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium py-1">Utility Types:</span>
              {utilityTypes.map((type: any) => (
                <Badge
                  key={type}
                  variant={selectedUtilityTypes.includes(type) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => toggleUtilityTypeFilter(type)}
                >
                  {type}
                </Badge>
              ))}
              {selectedUtilityTypes.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={() => setSelectedUtilityTypes([])}
                >
                  Clear
                </Button>
              )}
            </div>

            {/* SUE Records Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-1/5">Utility</TableHead>
                    <TableHead className="w-[100px]">Quality</TableHead>
                    <TableHead className="hidden md:table-cell">Method</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead className="hidden lg:table-cell">Status</TableHead>
                    <TableHead className="hidden lg:table-cell">Risk</TableHead>
                    <TableHead className="w-[120px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        No SUE records matching your filters.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRecords.map((record: any) => (
                      <TableRow
                        key={record.id}
                        className="cursor-pointer hover:bg-muted/40"
                        onClick={() => handleViewDetails(record)}
                      >
                        <TableCell>
                          <div className="font-medium">{record.utilityName}</div>
                          <div className="text-xs text-muted-foreground">{record.utilityType}</div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={getQualityLevelColor(record.qualityLevel)}
                          >
                            QL-{record.qualityLevel}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <span className="text-sm">{record.collectionMethod}</span>
                        </TableCell>
                        <TableCell>{record.areaDescription || 'Project area'}</TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <Badge variant="outline" className={getStatusColor(record.status)}>
                            {record.status.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          {record.conflictRisk ? (
                            <Badge variant="outline" className={getRiskColor(record.conflictRisk)}>
                              {record.conflictRisk}
                            </Badge>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                handleViewDetails(record);
                              }}
                            >
                              <Search className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                handleEditRecord(record);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="map" className="mt-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <Map className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">Quality Level Map View</h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    This view will show a map of utility records color-coded by SUE quality level.
                    Enables spatial visualization of data quality throughout the project area.
                  </p>
                  <Button disabled>Coming Soon</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* View Record Details Dialog */}
      <Dialog open={isViewDetailsDialogOpen} onOpenChange={setIsViewDetailsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="text-xl">SUE Record Details</DialogTitle>
            <DialogDescription>Detailed information about this utility record</DialogDescription>
          </DialogHeader>

          {currentRecord && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="md:col-span-2 space-y-4">
                <div className="flex flex-wrap gap-2 items-center">
                  <h2 className="text-xl font-semibold">{currentRecord.utilityName}</h2>
                  <Badge
                    variant="outline"
                    className={getQualityLevelColor(currentRecord.qualityLevel)}
                  >
                    Quality Level {currentRecord.qualityLevel}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground text-xs">Utility Type</Label>
                    <p className="font-medium">{currentRecord.utilityType}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground text-xs">Collection Method</Label>
                    <p className="font-medium">{currentRecord.collectionMethod}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground text-xs">Date Collected</Label>
                    <p className="font-medium">
                      {format(new Date(currentRecord.dateCollected), 'PPP')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground text-xs">Collected By</Label>
                    <p className="font-medium">{currentRecord.collectedBy}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground text-xs">Status</Label>
                    <Badge variant="outline" className={getStatusColor(currentRecord.status)}>
                      {currentRecord.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div>
                    <Label className="text-muted-foreground text-xs">Conflict Risk</Label>
                    {currentRecord.conflictRisk ? (
                      <Badge variant="outline" className={getRiskColor(currentRecord.conflictRisk)}>
                        {currentRecord.conflictRisk}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">Not assessed</span>
                    )}
                  </div>
                </div>

                <div>
                  <Label className="text-muted-foreground text-xs">Area Description</Label>
                  <p className="mt-1">
                    {currentRecord.areaDescription || 'No specific area description provided.'}
                  </p>
                </div>

                <div>
                  <Label className="text-muted-foreground text-xs">Notes</Label>
                  <div className="mt-1 p-3 bg-muted/30 rounded-md text-sm">
                    {currentRecord.notes || 'No notes available.'}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Attachments</h3>
                  {!currentRecord.attachments || currentRecord.attachments.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No attachments available.</p>
                  ) : (
                    <div className="space-y-2">
                      {currentRecord.attachments.map((attachment: any) => (
                        <div
                          key={attachment.id}
                          className="flex items-center gap-2 p-2 border rounded-md hover:bg-muted/20 cursor-pointer"
                        >
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{attachment.fileName}</p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(attachment.uploadDate), 'PPP')}
                            </p>
                          </div>
                          <Download className="h-4 w-4" />
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">Quality Level Information</h3>
                  <div className="p-3 rounded-md bg-muted/30 text-sm">
                    <p className="mb-2">
                      <span className="font-medium">
                        Quality Level {currentRecord.qualityLevel}:
                      </span>
                    </p>
                    {currentRecord.qualityLevel === 'A' && (
                      <p>
                        Precise horizontal and vertical location determined by exposure and direct
                        measurement of the utility.
                      </p>
                    )}
                    {currentRecord.qualityLevel === 'B' && (
                      <p>
                        Horizontal location determined through application of surface geophysical
                        methods.
                      </p>
                    )}
                    {currentRecord.qualityLevel === 'C' && (
                      <p>
                        Location determined by surveying and plotting visible above-ground utility
                        features and correlation with existing records.
                      </p>
                    )}
                    {currentRecord.qualityLevel === 'D' && (
                      <p>
                        Information derived from existing records or oral recollections. Lowest
                        level of accuracy.
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">Record Timeline</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-start">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                        <Clock className="h-3 w-3 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">Collected</p>
                        <p className="text-muted-foreground">
                          {format(new Date(currentRecord.dateCollected), 'PPP')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                        <Clock className="h-3 w-3 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium">Last Updated</p>
                        <p className="text-muted-foreground">
                          {format(new Date(currentRecord.lastUpdated), 'PPP')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsViewDetailsDialogOpen(false)}>
              Close
            </Button>
            <Button
              onClick={() => {
                setIsViewDetailsDialogOpen(false);
                if (currentRecord) {
                  handleEditRecord(currentRecord);
                }
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Record
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Record Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit SUE Record</DialogTitle>
            <DialogDescription>Update SUE record information</DialogDescription>
          </DialogHeader>

          {currentRecord && (
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-utility-name">Utility Name</Label>
                <Input
                  id="edit-utility-name"
                  value={currentRecord.utilityName}
                  disabled
                  className="bg-muted"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-quality-level">Quality Level</Label>
                <Select defaultValue={currentRecord.qualityLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select quality level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A">Quality Level A (Precise)</SelectItem>
                    <SelectItem value="B">Quality Level B (Detected)</SelectItem>
                    <SelectItem value="C">Quality Level C (Surveyed)</SelectItem>
                    <SelectItem value="D">Quality Level D (Records)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-status">Status</Label>
                <Select defaultValue={currentRecord.status}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="unverified">Unverified</SelectItem>
                    <SelectItem value="needs_review">Needs Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-conflict-risk">Conflict Risk</Label>
                <Select defaultValue={currentRecord.conflictRisk || ''}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select risk level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Not Assessed</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-notes">Notes</Label>
                <Textarea id="edit-notes" defaultValue={currentRecord.notes || ''} rows={3} />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateRecord} disabled={updateSueRecordMutation.isPending}>
              {updateSueRecordMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>Save Changes</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Record Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add New SUE Record</DialogTitle>
            <DialogDescription>Enter information about the utility location data</DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-utility-name">Utility Name</Label>
              <Input
                id="new-utility-name"
                value={newRecordData.utilityName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleNewRecordChange('utilityName', e.target.value)}
                placeholder="e.g. Water Main 24"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-utility-type">Utility Type</Label>
              <Select
                value={newRecordData.utilityType}
                onValueChange={(value: string) => handleNewRecordChange('utilityType', value)}
              >
                <SelectTrigger id="new-utility-type">
                  <SelectValue placeholder="Select utility type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Electric">Electric</SelectItem>
                  <SelectItem value="Water">Water</SelectItem>
                  <SelectItem value="Gas">Gas</SelectItem>
                  <SelectItem value="Sewer">Sewer</SelectItem>
                  <SelectItem value="Telecommunications">Telecommunications</SelectItem>
                  <SelectItem value="Cable/Fiber">Cable/Fiber</SelectItem>
                  <SelectItem value="Oil/Petroleum">Oil/Petroleum</SelectItem>
                  <SelectItem value="Steam">Steam</SelectItem>
                  <SelectItem value="Stormwater">Stormwater</SelectItem>
                  <SelectItem value="Traffic Signal">Traffic Signal</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-quality-level">Quality Level</Label>
              <Select
                value={newRecordData.qualityLevel}
                onValueChange={(value) => handleNewRecordChange('qualityLevel', value as 'D' | 'C' | 'B' | 'A')}
              >
                <SelectTrigger id="new-quality-level">
                  <SelectValue placeholder="Select quality level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">Quality Level A (Precise)</SelectItem>
                  <SelectItem value="B">Quality Level B (Detected)</SelectItem>
                  <SelectItem value="C">Quality Level C (Surveyed)</SelectItem>
                  <SelectItem value="D">Quality Level D (Records)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-collection-method">Collection Method</Label>
              <Select
                value={newRecordData.collectionMethod}
                onValueChange={(value: string) => handleNewRecordChange('collectionMethod', value)}
              >
                <SelectTrigger id="new-collection-method">
                  <SelectValue placeholder="Select collection method" />
                </SelectTrigger>
                <SelectContent>
                  {getCollectionMethodOptions(newRecordData.qualityLevel).map((method: any) => (
                    <SelectItem key={method} value={method}>
                      {method}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-date-collected">Date Collected</Label>
                <Input
                  id="new-date-collected"
                  type="date"
                  value={newRecordData.dateCollected}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleNewRecordChange('dateCollected', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-collected-by">Collected By</Label>
                <Input
                  id="new-collected-by"
                  value={newRecordData.collectedBy}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleNewRecordChange('collectedBy', e.target.value)}
                  placeholder="Organization name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-area-description">Area Description</Label>
              <Input
                id="new-area-description"
                value={newRecordData.areaDescription || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleNewRecordChange('areaDescription', e.target.value)}
                placeholder="e.g. Main Street to Oak Avenue"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-conflict-risk">Conflict Risk</Label>
              <Select
                value={newRecordData.conflictRisk || ''}
                onValueChange={(value) => handleNewRecordChange('conflictRisk', value as 'low' | 'medium' | 'high' | 'critical' | undefined)}
              >
                <SelectTrigger id="new-conflict-risk">
                  <SelectValue placeholder="Select risk level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Not Assessed</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-notes">Notes</Label>
              <Textarea
                id="new-notes"
                value={newRecordData.notes || ''}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleNewRecordChange('notes', e.target.value)}
                rows={3}
                placeholder="Additional information about this utility data..."
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddDialogOpen(false);
                resetNewRecordForm();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddRecord}
              disabled={addSueRecordMutation.isPending || !newRecordData.utilityName}
            >
              {addSueRecordMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                <>Add Record</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
