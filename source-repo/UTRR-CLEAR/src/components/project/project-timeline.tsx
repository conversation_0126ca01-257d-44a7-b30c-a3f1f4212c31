'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import {
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  Circle,
  ArrowRight,
  Flag,
  Users,
  FileText,
  PlayCircle,
  PauseCircle,
  Loader2,
  Download,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';

interface TimelineEvent {
  id: string;
  title: string;
  description?: string;
  date: string;
  type: 'milestone' | 'meeting' | 'deadline' | 'completion' | 'issue';
  status: 'completed' | 'current' | 'upcoming' | 'overdue';
  phase: string;
  assignedTo?: string[];
  dependencies?: string[];
  critical?: boolean;
}

// Mock data - in real app this would come from tRPC/API
const mockTimeline: TimelineEvent[] = [
  {
    id: '1',
    title: 'Project Kickoff Meeting',
    description: 'Initial stakeholder meeting to review project scope and timeline',
    date: '2024-01-15',
    type: 'meeting',
    status: 'completed',
    phase: 'Initiation',
    assignedTo: ['Project Manager', 'Lead Coordinator'],
  },
  {
    id: '2',
    title: 'Utility Identification Complete',
    description: 'All utilities identified and mapped within project corridor',
    date: '2024-02-01',
    type: 'milestone',
    status: 'completed',
    phase: 'Design',
    assignedTo: ['Survey Team'],
  },
  {
    id: '3',
    title: 'SUE Quality Level B Investigation',
    description: 'Subsurface utility engineering investigation to locate underground utilities',
    date: '2024-02-15',
    type: 'completion',
    status: 'current',
    phase: 'Design',
    assignedTo: ['SUE Contractor'],
    critical: true,
  },
  {
    id: '4',
    title: 'Stakeholder Coordination Meeting',
    description: 'Monthly coordination meeting with all utility companies',
    date: '2024-02-28',
    type: 'meeting',
    status: 'upcoming',
    phase: 'Coordination',
    assignedTo: ['All Stakeholders'],
  },
  {
    id: '5',
    title: 'Conflict Analysis Report Due',
    description: 'Comprehensive utility conflict analysis and resolution recommendations',
    date: '2024-03-15',
    type: 'deadline',
    status: 'upcoming',
    phase: 'Coordination',
    assignedTo: ['Engineering Team'],
    critical: true,
    dependencies: ['3'],
  },
  {
    id: '6',
    title: 'Utility Relocation Agreements',
    description: 'All utility relocation agreements signed and executed',
    date: '2024-04-01',
    type: 'milestone',
    status: 'upcoming',
    phase: 'Coordination',
    assignedTo: ['Legal Team', 'Utility Companies'],
    dependencies: ['5'],
  },
  {
    id: '7',
    title: 'Construction Begin',
    description: 'Construction activities commence following utility clearance',
    date: '2024-05-01',
    type: 'milestone',
    status: 'upcoming',
    phase: 'Construction',
    assignedTo: ['Construction Team'],
    critical: true,
    dependencies: ['6'],
  },
];

const typeIcons = {
  milestone: Flag,
  meeting: Users,
  deadline: Clock,
  completion: CheckCircle,
  issue: AlertTriangle,
};

const typeColors = {
  milestone: 'bg-purple-500',
  meeting: 'bg-blue-500',
  deadline: 'bg-orange-500',
  completion: 'bg-green-500',
  issue: 'bg-red-500',
};

const statusColors = {
  completed: 'text-green-600',
  current: 'text-blue-600',
  upcoming: 'text-muted-foreground',
  overdue: 'text-red-600',
};

const statusBadgeVariants = {
  completed: 'default',
  current: 'default',
  upcoming: 'secondary',
  overdue: 'destructive',
} as const;

// Define standard project phases
const PROJECT_PHASES = [
  { name: 'Initiation', icon: PlayCircle, order: 1 },
  { name: 'Planning', icon: FileText, order: 2 },
  { name: 'Design', icon: Circle, order: 3 },
  { name: 'Coordination', icon: Users, order: 4 },
  { name: 'Permitting', icon: CheckCircle, order: 5 },
  { name: 'Construction', icon: Flag, order: 6 },
  { name: 'Inspection', icon: AlertTriangle, order: 7 },
  { name: 'Closeout', icon: CheckCircle, order: 8 },
];

export function ProjectTimeline({ projectId }: { projectId?: string }) {
  const completedEvents = mockTimeline.filter((e: any) => e.status === 'completed').length;
  const totalEvents = mockTimeline.length;
  const progressPercentage = (completedEvents / totalEvents) * 100;

  // Handle PDF export
  const handleExportPDF = async () => {
    try {
      const element = document.querySelector('[data-timeline-export]');
      if (!element) return;

      // Dynamic import for client-side only
      const html2pdf = (await import('html2pdf.js')).default;
      
      const opt = {
        margin: 1,
        filename: `project-timeline-${projectId || 'demo'}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
      };

      html2pdf().set(opt).from(element).save();
      
      toast({
        title: 'Export started',
        description: 'Your timeline PDF is being generated...',
      });
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Unable to export timeline. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const TimelineItem = ({ event, isLast }: { event: TimelineEvent; isLast: boolean }) => {
    const TypeIcon = typeIcons[event.type];
    const isCompleted = event.status === 'completed';
    const isCurrent = event.status === 'current';
    const isOverdue = event.status === 'overdue';

    return (
      <div className="relative">
        {/* Timeline line */}
        {!isLast && <div className="absolute left-6 top-12 w-0.5 h-16 bg-border" />}

        <div className="flex items-start gap-4">
          {/* Icon */}
          <div
            className={`
            w-12 h-12 rounded-full flex items-center justify-center z-10
            ${isCompleted ? 'bg-green-500' : isCurrent ? 'bg-blue-500' : 'bg-muted'}
            ${event.critical ? 'ring-2 ring-orange-400' : ''}
          `}
          >
            <TypeIcon
              className={`h-5 w-5 ${isCompleted || isCurrent ? 'text-white' : 'text-muted-foreground'}`}
            />
          </div>

          {/* Content */}
          <div className="flex-1 pb-8">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm">{event.title}</h4>
              <Badge variant={statusBadgeVariants[event.status]} className="text-xs">
                {event.status}
              </Badge>
              {event.critical && (
                <Badge variant="outline" className="text-xs text-orange-600">
                  Critical
                </Badge>
              )}
            </div>

            {event.description && (
              <p className="text-sm text-muted-foreground mb-2">{event.description}</p>
            )}

            <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(event.date).toLocaleDateString()}
              </span>
              <span className="flex items-center gap-1">
                <Circle className="h-3 w-3" />
                {event.phase}
              </span>
            </div>

            {event.assignedTo && (
              <div className="flex gap-1 mb-2">
                {event.assignedTo.map((assignee: any) => (
                  <Badge key={assignee} variant="outline" className="text-xs">
                    {assignee}
                  </Badge>
                ))}
              </div>
            )}

            {event.dependencies && (
              <div className="text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <ArrowRight className="h-3 w-3" />
                  Depends on: {event.dependencies.join(', ')}
                </span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {event.status === 'current' && (
              <Button variant="outline" size="sm">
                Update
              </Button>
            )}
            {event.status === 'upcoming' && (
              <Button variant="outline" size="sm">
                Schedule
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Project Timeline</CardTitle>
            <CardDescription>
              Track project milestones, meetings, and critical deadlines
            </CardDescription>
          </div>
          <Button variant="outline" onClick={handleExportPDF}>
            <Download className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6" data-timeline-export>
        {/* Progress Overview with Phase Icons */}
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Project Progress</span>
            <span className="font-medium">
              {completedEvents} of {totalEvents} milestones completed
            </span>
          </div>
          
          {/* Progress bar with phase icons */}
          <div className="relative">
            <Progress value={progressPercentage} className="h-3" />
            <div className="flex justify-between items-center mt-2">
              {PROJECT_PHASES.map((phase, index) => {
                const PhaseIcon = phase.icon;
                const isCompleted = index < 3; // Mock data - should be based on actual project status
                const isCurrent = index === 3;
                
                return (
                  <div key={phase.name} className="flex flex-col items-center">
                    <div
                      className={`
                        w-6 h-6 rounded-full flex items-center justify-center mb-1
                        ${isCompleted ? 'bg-green-500 text-white' : 
                          isCurrent ? 'bg-blue-500 text-white' : 
                          'bg-muted text-muted-foreground'}
                      `}
                    >
                      <PhaseIcon className="h-3 w-3" />
                    </div>
                    <span className="text-xs text-center max-w-16 leading-tight">
                      {phase.name}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
          
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Started: January 15, 2024</span>
            <span>Expected completion: May 1, 2024</span>
          </div>
        </div>

        {/* Phase Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg">
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">2</div>
            <div className="text-xs text-muted-foreground">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">1</div>
            <div className="text-xs text-muted-foreground">In Progress</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-muted-foreground">4</div>
            <div className="text-xs text-muted-foreground">Upcoming</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600">3</div>
            <div className="text-xs text-muted-foreground">Critical Path</div>
          </div>
        </div>

        {/* Timeline Events */}
        <div className="space-y-0">
          {mockTimeline.map((event, index) => (
            <TimelineItem key={event.id} event={event} isLast={index === mockTimeline.length - 1} />
          ))}
        </div>

        {/* Legend */}
        <div className="flex flex-wrap gap-4 pt-4 border-t text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full" />
            <span>Completed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full" />
            <span>Current</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-muted rounded-full" />
            <span>Upcoming</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full" />
            <span>Overdue</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-orange-400 rounded-full ring-2 ring-orange-400" />
            <span>Critical Path</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
