'use client';

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import {
  MapPin,
  Layers,
  ZoomIn,
  ZoomOut,
  Map as MapIcon,
  Crosshair,
  AlertTriangle,
  Eye,
  Locate,
} from 'lucide-react';

// Import OpenLayers components
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import TileLayer from 'ol/layer/Tile.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import OSM from 'ol/source/OSM.js';
import { Circle as CircleStyle, Fill, Stroke, Style, Text } from 'ol/style.js';
import { fromLonLat } from 'ol/proj.js';
import Feature from 'ol/Feature.js';
import Point from 'ol/geom/Point.js';
import { defaults as defaultControls } from 'ol/control.js';
import Overlay from 'ol/Overlay.js';
import { safeLog } from '~/lib/error-handler';

interface Conflict {
  id: number;
  description: string;
  status: 'open' | 'in-progress' | 'resolved';
  priority: 'low' | 'medium' | 'high' | 'critical';
  location?: string; // GeoJSON as string
  resolutionNotes?: string;
  createdAt: Date;
  updatedAt: Date;
  detectionMethod: 'automatic' | 'manual';
  confidenceScore?: number;
  utilityId: number;
  utilityName: string;
  utilityType: string;
  likelihoodScore?: number;
  impactScore?: number;
  riskScore?: number;
  conflictType?: 'crossing' | 'proximity' | 'clearance' | 'interference';
}

interface ConflictMapViewProps {
  conflicts: Conflict[];
  projectId: string;
  onConflictSelect?: (conflict: Conflict) => void;
  selectedConflictId?: number | null;
}

export function ConflictMapView({
  conflicts,
  projectId,
  onConflictSelect,
  selectedConflictId,
}: ConflictMapViewProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstance = useRef<Map | null>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const popupCloserRef = useRef<HTMLButtonElement>(null);
  const popupContentRef = useRef<HTMLDivElement>(null);
  const [selectedConflict, setSelectedConflict] = useState<Conflict | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const [popupContent, setPopupContent] = useState<string>('');

  // Style function for conflict features
  const getConflictStyle = useCallback((feature: any) => {
    const conflict = feature.get('conflictData');
    const priority = conflict?.priority || 'medium';

    const colors = {
      critical: '#dc2626', // red-600
      high: '#ea580c', // orange-600
      medium: '#d97706', // amber-600
      low: '#65a30d', // lime-600
    };

    return new Style({
      image: new CircleStyle({
        radius: priority === 'critical' ? 12 : priority === 'high' ? 10 : 8,
        fill: new Fill({
          color: colors[priority as keyof typeof colors] || colors.medium,
        }),
        stroke: new Stroke({
          color: '#ffffff',
          width: 2,
        }),
      }),
    });
  }, []);

  // Handle conflict feature clicks
  const handleConflictClick = useCallback((feature: any) => {
    const conflict = feature.get('conflictData');
    if (conflict && onConflictSelect) {
      onConflictSelect(conflict);
    }

    // Set selected conflict for display
    setSelectedConflict(conflict);
    if (conflict) {
      setPopupContent(`
        <div class="p-3">
          <h4 class="font-semibold">${conflict?.description || 'Conflict'}</h4>
          <p class="text-sm text-gray-600">Priority: ${conflict?.priority || 'Unknown'}</p>
          <p class="text-sm text-gray-600">Status: ${conflict?.status || 'Unknown'}</p>
        </div>
      `);
    }
  }, [onConflictSelect]);

  // Initialize map and add conflicts
  useEffect(() => {
    if (!mapRef.current || !mapLoaded || mapError || !conflicts.length || !mapInstance.current) return;

    try {
      // Create vector source and layer for conflicts
      const conflictsSource = new VectorSource();

      // Remove existing conflicts layer
      const existingLayer = mapInstance.current
        .getLayers()
        .getArray()
        .find((layer: any) => layer.get('name') === 'conflicts');
      if (existingLayer) {
        mapInstance.current.removeLayer(existingLayer);
      }

      // Create new conflicts layer
      const conflictsLayer = new VectorLayer({
        source: conflictsSource,
        style: getConflictStyle,
      });
      conflictsLayer.set('name', 'conflicts');
      mapInstance.current.addLayer(conflictsLayer);

      // Add conflict features to source
      const features: Feature[] = [];
      const bounds: [number, number, number, number] = [Infinity, Infinity, -Infinity, -Infinity]; // [minX, minY, maxX, maxY]
      let hasValidCoordinates = false;

      conflicts.forEach((conflict, index) => {
        let coordinates;

        // Parse location if it's a GeoJSON string
        if (conflict.location) {
          try {
            const locationObj =
              typeof conflict.location === 'string'
                ? JSON.parse(conflict.location)
                : conflict.location;

            if (locationObj.type === 'Point' && Array.isArray(locationObj.coordinates)) {
              coordinates = fromLonLat(locationObj.coordinates);

              // Update bounds with null checks
              const x = coordinates[0];
              const y = coordinates[1];
              if (x != null && y != null) {
                bounds[0] = Math.min(bounds[0], x);
                bounds[1] = Math.min(bounds[1], y);
                bounds[2] = Math.max(bounds[2], x);
                bounds[3] = Math.max(bounds[3], y);
              }
              hasValidCoordinates = true;
            }
          } catch (error) {
            safeLog.error('Error parsing conflict location:', { error: String(error) });
          }
        }

        // If no valid location, generate demo coordinates in Indiana area
        if (!coordinates) {
          // Spread conflicts around Indiana for demo purposes
          const baseCoord = [-86.1349, 39.7903]; // Indianapolis
          const offsetRange = 2; // degrees
          const row = Math.floor(index / 6);
          const col = index % 6;

          const lat = (baseCoord?.[1] || 0) + (row - 2) * (offsetRange / 5);
          const lng = (baseCoord?.[0] || 0) + (col - 2.5) * (offsetRange / 5);

          coordinates = fromLonLat([lng, lat]);
        }

        // Create feature
        const feature = new Feature({
          geometry: new Point(coordinates),
        });

        feature.set('conflictData', conflict);
        feature.setId(conflict.id);
        features.push(feature);
      });

      // Add features to source
      conflictsSource.addFeatures(features);

      // Update map view if we have valid coordinates
      if (hasValidCoordinates && mapInstance.current && conflicts.length > 0) {
        const extent = [bounds[0], bounds[1], bounds[2], bounds[3]];
        const validExtent = extent.every((coord: any) => typeof coord === 'number' && !isNaN(coord));
        if (validExtent) {
          mapInstance.current.getView().fit(extent as any, {
            padding: [20, 20, 20, 20],
            maxZoom: 18,
          });
        }
      } else if (conflicts.length > 0) {
        // Show all demo conflicts
        const source = conflictsLayer.getSource();
        if (source) {
          const extent = source.getExtent();
          if (extent && extent.some((val: any) => isFinite(val))) {
            const validExtent = extent.every((coord: any) => typeof coord === 'number' && !isNaN(coord));
            if (validExtent) {
              mapInstance.current.getView().fit(extent as any, {
                padding: [20, 20, 20, 20],
                maxZoom: 18,
              });
            }
          }
        }
      }
    } catch (error) {
      safeLog.error('Error updating conflicts on map:', { error: String(error) });
    }
  }, [conflicts, mapLoaded, mapError, getConflictStyle]);

  // Highlight selected conflict
  useEffect(() => {
    if (!mapInstance.current || !selectedConflictId) return;

    const conflictsLayer = mapInstance.current
      .getLayers()
      .getArray()
      .find((layer: any) => layer.get('name') === 'conflicts') as VectorLayer<VectorSource> | undefined;

    if (conflictsLayer && conflictsLayer.getSource()) {
      const feature = conflictsLayer.getSource()?.getFeatureById(selectedConflictId);
      if (feature) {
        const geometry = feature.getGeometry();
        const coordinates = geometry instanceof Point ? (geometry as Point).getCoordinates() : null;
        if (coordinates) {
          mapInstance.current?.getView().animate({
            center: coordinates,
            zoom: Math.max(mapInstance.current?.getView().getZoom() || 10, 12),
            duration: 500,
          });
        }
      }
    }
  }, [selectedConflictId]);

  // Set up global callback for popup details button
  useEffect(() => {
    (window as any).conflictMapViewDetails = (conflictId: number) => {
      const conflict = conflicts.find((c: any) => c.id === conflictId);
      if (conflict && onConflictSelect) {
        onConflictSelect(conflict);
      }
    };

    return () => {
      delete (window as any).conflictMapViewDetails;
    };
  }, [conflicts, onConflictSelect]);

  // Update conflicts on map
  useEffect(() => {
    if (!mapInstance.current || !mapLoaded || mapError) return;

    try {
      // Create vector source and layer for conflicts
      const conflictsSource = new VectorSource();

      // Remove existing conflicts layer
      const existingLayer = mapInstance.current
        .getLayers()
        .getArray()
        .find((layer: any) => layer.get('name') === 'conflicts');
      if (existingLayer) {
        mapInstance.current.removeLayer(existingLayer);
      }

      // Create new conflicts layer
      const conflictsLayer = new VectorLayer({
        source: conflictsSource,
        style: getConflictStyle,
      });
      conflictsLayer.set('name', 'conflicts');
      mapInstance.current.addLayer(conflictsLayer);

      // Add conflict features to source
      const features: Feature[] = [];
      const bounds: [number, number, number, number] = [Infinity, Infinity, -Infinity, -Infinity]; // [minX, minY, maxX, maxY]
      let hasValidCoordinates = false;

      conflicts.forEach((conflict, index) => {
        let coordinates;

        // Parse location if it's a GeoJSON string
        if (conflict.location) {
          try {
            const locationObj =
              typeof conflict.location === 'string'
                ? JSON.parse(conflict.location)
                : conflict.location;

            if (locationObj.type === 'Point' && Array.isArray(locationObj.coordinates)) {
              coordinates = fromLonLat(locationObj.coordinates);

              // Update bounds with null checks
              const x = coordinates[0];
              const y = coordinates[1];
              if (x != null && y != null) {
                bounds[0] = Math.min(bounds[0], x);
                bounds[1] = Math.min(bounds[1], y);
                bounds[2] = Math.max(bounds[2], x);
                bounds[3] = Math.max(bounds[3], y);
              }
              hasValidCoordinates = true;
            }
          } catch (error) {
            safeLog.error('Error parsing conflict location:', { error: String(error) });
          }
        }

        // If no valid location, generate demo coordinates in Indiana area
        if (!coordinates) {
          // Spread conflicts around Indiana for demo purposes
          const baseCoord = [-86.1349, 39.7903]; // Indianapolis
          const offsetRange = 2; // degrees
          const row = Math.floor(index / 6);
          const col = index % 6;

          const lat = (baseCoord?.[1] || 0) + (row - 2) * (offsetRange / 5);
          const lng = (baseCoord?.[0] || 0) + (col - 2.5) * (offsetRange / 5);

          coordinates = fromLonLat([lng, lat]);
        }

        // Create feature
        const feature = new Feature({
          geometry: new Point(coordinates),
        });

        feature.set('conflictData', conflict);
        feature.setId(conflict.id);
        features.push(feature);
      });

      // Add features to source
      conflictsSource.addFeatures(features);

      // Update map view if we have valid coordinates
      if (hasValidCoordinates && mapInstance.current && conflicts.length > 0) {
        const extent = [bounds[0], bounds[1], bounds[2], bounds[3]];
        const validExtent = extent.every((coord: any) => typeof coord === 'number' && !isNaN(coord));
        if (validExtent) {
          mapInstance.current.getView().fit(extent as any, {
            padding: [20, 20, 20, 20],
            maxZoom: 18,
          });
        }
      } else if (conflicts.length > 0) {
        // Show all demo conflicts
        const source = conflictsLayer.getSource();
        if (source) {
          const extent = source.getExtent();
          if (extent && extent.some((val: any) => isFinite(val))) {
            const validExtent = extent.every((coord: any) => typeof coord === 'number' && !isNaN(coord));
            if (validExtent) {
              mapInstance.current.getView().fit(extent as any, {
                padding: [20, 20, 20, 20],
                maxZoom: 18,
              });
            }
          }
        }
      }
    } catch (error) {
      safeLog.error('Error updating conflicts on map:', { error: String(error) });
    }
  }, [conflicts, mapLoaded, mapError, getConflictStyle]);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || mapInstance.current) return;

    try {
      // Create popup overlay
      const popup = new Overlay({
        element: popupRef.current!,
        autoPan: {
          animation: {
            duration: 250,
          },
        },
        positioning: 'bottom-center',
        stopEvent: false,
        offset: [0, -20],
      });

      // Initialize map with Indiana-centered view
      const map = new Map({
        target: mapRef.current,
        layers: [
          new TileLayer({
            source: new OSM(),
          }),
        ],
        controls: defaultControls({
          zoom: false,
          attribution: false,
        }),
        view: new View({
          center: fromLonLat([-86.1349, 39.7903]), // Indianapolis, IN
          zoom: 8,
        }),
      });

      // Add popup to map
      map.addOverlay(popup);

      // Add click handler to close popup
      if (popupCloserRef.current) {
        popupCloserRef.current.onclick = function (e) {
          e.preventDefault();
          popup.setPosition(undefined);
          setSelectedConflict(null);
          return false;
        };
      }

      // Add click handler to map for features
      map.on('click', function (evt) {
        const feature = map.forEachFeatureAtPixel(evt.pixel, function (feature) {
          return feature;
        });

        if (feature) {
          const properties = feature.getProperties();
          if (properties.conflictData) {
            const conflict = properties.conflictData as Conflict;
            setSelectedConflict(conflict);

            if (onConflictSelect) {
              onConflictSelect(conflict);
            }

            // Show popup
            if (popupContentRef.current) {
              const riskColor = getRiskScoreColor(conflict.riskScore);

              popupContentRef.current.innerHTML = `
                <div class="min-w-[250px] p-2">
                  <div class="flex items-start justify-between mb-2">
                    <h3 class="text-sm font-semibold text-gray-900 pr-2 leading-tight">${conflict.description}</h3>
                    <div class="flex gap-1">
                      ${getPriorityBadge(conflict.priority)}
                      ${getStatusBadge(conflict.status)}
                    </div>
                  </div>
                  <div class="text-xs text-muted-foreground space-y-1">
                    <div><strong>Utility:</strong> ${conflict.utilityName} (${conflict.utilityType})</div>
                    <div><strong>Detection:</strong> ${conflict.detectionMethod === 'automatic' ? 'Auto' : 'Manual'}${conflict.confidenceScore ? ` (${conflict.confidenceScore}%)` : ''}</div>
                    ${conflict.riskScore ? `<div><strong>Risk Score:</strong> <span class="${riskColor}">${conflict.riskScore}</span></div>` : ''}
                    ${conflict.resolutionNotes ? `<div class="mt-2 pt-1 border-t border-border"><strong>Notes:</strong> ${conflict.resolutionNotes.length > 80 ? conflict.resolutionNotes.substring(0, 80) + '...' : conflict.resolutionNotes}</div>` : ''}
                  </div>
                  <div class="mt-2 pt-2 border-t border-border">
                    <button class="text-xs text-blue-600 hover:text-blue-800 font-medium" onclick="window.conflictMapViewDetails && window.conflictMapViewDetails(${conflict.id})">
                      View Details →
                    </button>
                  </div>
                </div>
              `;

              const coordinates = (feature.getGeometry() as any)?.getCoordinates();
              if (coordinates) {
                popup.setPosition(coordinates);
              }
            }
          }
        } else {
          popup.setPosition(undefined);
          setSelectedConflict(null);
        }
      });

      // Add hover effect
      map.on('pointermove', function (evt) {
        const feature = map.forEachFeatureAtPixel(evt.pixel, function (feature) {
          return feature;
        });

        map.getViewport().style.cursor = feature ? 'pointer' : '';
      });

      mapInstance.current = map;
      setMapLoaded(true);
      setMapError(null);
    } catch (error) {
      safeLog.error('Error initializing map:', { error: String(error) });
      setMapError('Failed to initialize map. Please refresh the page.');
    }

    return () => {
      if (mapInstance.current) {
        mapInstance.current.dispose();
        mapInstance.current = null;
      }
    };
  }, [onConflictSelect]);

  // Utility functions for popup content
  function getPriorityBadge(priority: string): string {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      high: 'bg-orange-100 text-orange-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800',
    };
    const color = colors[priority as keyof typeof colors] || colors.medium;
    return `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${color}">${priority}</span>`;
  }

  function getStatusBadge(status: string): string {
    const colors = {
      open: 'bg-red-100 text-red-800',
      'in-progress': 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800',
    };
    const color = colors[status as keyof typeof colors] || colors.open;
    return `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${color}">${status.replace('-', ' ')}</span>`;
  }

  function getRiskScoreColor(riskScore?: number): string {
    if (!riskScore) return 'text-muted-foreground';
    if (riskScore >= 15) return 'text-red-600 font-bold';
    if (riskScore >= 10) return 'text-orange-600 font-semibold';
    if (riskScore >= 6) return 'text-yellow-600 font-medium';
    return 'text-green-600';
  }

  // Zoom control handlers
  const handleZoomIn = () => {
    if (!mapInstance.current) return;
    const view = mapInstance.current.getView();
    const zoom = view.getZoom() || 0;
    view.animate({
      zoom: zoom + 1,
      duration: 250,
    });
  };

  const handleZoomOut = () => {
    if (!mapInstance.current) return;
    const view = mapInstance.current.getView();
    const zoom = view.getZoom() || 0;
    view.animate({
      zoom: Math.max(zoom - 1, 1),
      duration: 250,
    });
  };

  // Reset view to fit all conflicts
  const handleResetView = () => {
    if (!mapInstance.current || conflicts.length === 0) return;

    const conflictsLayer = mapInstance.current
      .getLayers()
      .getArray()
      .find((layer: any) => layer.get('name') === 'conflicts') as VectorLayer<VectorSource> | undefined;

    if (conflictsLayer && conflictsLayer.getSource()) {
      const extent = conflictsLayer.getSource()?.getExtent();
      if (extent && extent.some((val: any) => isFinite(val))) {
        const validExtent = extent.every((coord: any) => typeof coord === 'number' && !isNaN(coord));
        if (validExtent) {
          mapInstance.current.getView().fit(extent as any, {
            padding: [20, 20, 20, 20],
            maxZoom: 18,
          });
        }
      }
    } else {
      // Reset to Indiana view
      mapInstance.current.getView().animate({
        center: fromLonLat([-86.1349, 39.7903]),
        zoom: 8,
        duration: 1000,
      });
    }
  };

  // Locate specific conflict
  const handleLocateConflict = (conflictId: number) => {
    if (!mapInstance.current) return;

    const conflictsLayer = mapInstance.current
      .getLayers()
      .getArray()
      .find((layer: any) => layer.get('name') === 'conflicts') as VectorLayer<VectorSource> | undefined;

    if (conflictsLayer && conflictsLayer.getSource()) {
      const feature = conflictsLayer.getSource()?.getFeatureById(conflictId);
      if (feature) {
        const geometry = feature.getGeometry();
        const coordinates = geometry instanceof Point ? (geometry as Point).getCoordinates() : null;
        if (coordinates) {
          mapInstance.current?.getView().animate({
            center: coordinates,
            zoom: 15,
            duration: 1000,
          });
        }
      }
    }
  };

  if (mapError) {
    return (
      <div className="w-full h-[500px] bg-slate-100 rounded-md flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900">Map Loading Error</h3>
          <p className="text-muted-foreground mt-2">{mapError}</p>
          <Button onClick={() => window.location.reload()} className="mt-4" variant="outline">
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {/* Map container */}
      <div ref={mapRef} className="w-full h-[500px] bg-slate-100 rounded-md border"></div>

      {/* Popup overlay */}
      <div
        ref={popupRef}
        className="ol-popup absolute bg-white rounded-lg shadow-lg border border-border z-20"
        style={{ display: 'none' }}
      >
        <button
          ref={popupCloserRef}
          type="button"
          className="ol-popup-closer absolute -top-2 -right-2 w-6 h-6 bg-gray-600 text-white rounded-full flex items-center justify-center text-sm hover:bg-gray-700 border-0 cursor-pointer"
          title="Close"
        >
          ×
        </button>
        <div ref={popupContentRef} className="min-w-0"></div>
      </div>

      {/* Controls overlay */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-md border border-border z-10">
        <div className="flex flex-col">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 rounded-t-lg rounded-b-none border-b"
                  onClick={handleZoomIn}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">Zoom In</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 rounded-none border-b"
                  onClick={handleZoomOut}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">Zoom Out</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 rounded-b-lg rounded-t-none"
                  onClick={handleResetView}
                >
                  <Crosshair className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">Reset View</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Legend overlay */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-md border border-border z-10 p-3">
        <div className="text-xs font-semibold mb-2 text-gray-900">Conflict Legend</div>

        <div className="space-y-2 mb-3">
          <div className="text-xs font-medium text-gray-700">Types</div>
          <div className="grid grid-cols-2 gap-x-3 gap-y-1">
            <div className="flex items-center gap-2 text-xs">
              <span className="flex items-center justify-center w-4 h-4 bg-red-500 rounded-full text-white font-bold">
                ×
              </span>
              <span>Crossing</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <span className="flex items-center justify-center w-4 h-4 bg-yellow-500 rounded-full text-white font-bold">
                ◯
              </span>
              <span>Proximity</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <span className="flex items-center justify-center w-4 h-4 bg-orange-500 rounded-full text-white font-bold">
                △
              </span>
              <span>Clearance</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <span className="flex items-center justify-center w-4 h-4 bg-purple-500 rounded-full text-white font-bold">
                ⚡
              </span>
              <span>Interference</span>
            </div>
          </div>
        </div>

        <div className="space-y-2 border-t border-border pt-2">
          <div className="text-xs font-medium text-gray-700">Priority</div>
          <div className="grid grid-cols-2 gap-x-3 gap-y-1">
            <div className="flex items-center gap-1 text-xs">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Critical</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className="w-3 h-3 rounded-full bg-orange-500"></div>
              <span>High</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Medium</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Low</span>
            </div>
          </div>
        </div>
      </div>

      {/* No conflicts message */}
      {conflicts.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/90 rounded-md">
          <div className="text-center max-w-md">
            <MapPin className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900">No conflicts to display</h3>
            <p className="text-muted-foreground mt-2">
              No conflicts were found for this project. Try running conflict detection or adding
              conflicts manually to see them on the map.
            </p>
          </div>
        </div>
      )}

      {/* Loading state */}
      {!mapLoaded && !mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-md">
          <div className="text-center">
            <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900">Loading Map</h3>
            <p className="text-muted-foreground mt-1">Initializing interactive conflict map...</p>
          </div>
        </div>
      )}
    </div>
  );
}
