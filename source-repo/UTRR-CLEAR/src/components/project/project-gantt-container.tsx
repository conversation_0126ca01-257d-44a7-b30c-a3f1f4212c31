"use client";

import React from 'react';
import { api } from '~/trpc/react';
import GanttChart, { type GanttData } from './gantt-chart';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Skeleton } from '~/components/ui/skeleton';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { BarChart3, AlertCircle, Calendar } from 'lucide-react';
import { safeLog } from '~/lib/error-handler';

interface ProjectGanttContainerProps {
  projectId: string;
  className?: string;
}

const ProjectGanttContainer: React.FC<ProjectGanttContainerProps> = ({ 
  projectId, 
  className 
}) => {
  const { 
    data: ganttData, 
    isLoading, 
    error,
    refetch
  } = api.tasks.getProjectGanttData.useQuery(
    { projectId },
    {
      enabled: !!projectId,
    }
  );

  const updateTaskScheduleMutation = api.tasks.updateTaskSchedule.useMutation({
    onSuccess: () => {
      void refetch();
    },
  });

  const handleTaskUpdate = async (
    taskId: string, 
    updates: { startDate?: string; endDate?: string; duration?: number }
  ) => {
    try {
      await updateTaskScheduleMutation.mutateAsync({
        taskId,
        ...updates,
      });
    } catch (error) {
      safeLog.error('Failed to update task:', { error: String(error) });
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Project Gantt Chart
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="w-80 space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="flex-1 space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load Gantt chart data: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  if (!ganttData) {
    return (
      <Alert className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          No Gantt chart data available for this project.
        </AlertDescription>
      </Alert>
    );
  }

  // Show empty state if no tasks
  if (ganttData.tasks.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Project Gantt Chart
          </CardTitle>
        </CardHeader>
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto" />
            <div className="space-y-2">
              <h3 className="text-lg font-medium">No Tasks Found</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                This project doesn&apos;t have any tasks yet. Tasks will appear here once they are synced from Monday.com or created manually.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <GanttChart
      data={ganttData}
      onTaskUpdate={handleTaskUpdate}
      className={className}
    />
  );
};

export default ProjectGanttContainer;