'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { useToast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Checkbox } from '~/components/ui/checkbox';
import { Alert, AlertDescription } from '~/components/ui/alert';
import {
  AlertTriangle,
  Check,
  AlertCircle,
  Eye,
  MapPin,
  Filter,
  Search,
  RefreshCw,
  Zap,
  Plus,
  Settings,
  Server,
  Database,
  Clock,
  TrendingUp,
  BarChart3,
} from 'lucide-react';

interface AdvancedConflictDetectionProps {
  projectId: string;
}

interface Utility {
  id: number;
  name: string;
  type: string;
  status: string;
  location?: string; // GeoJSON as string
}

interface Conflict {
  id: number;
  description: string;
  status: 'open' | 'in-progress' | 'resolved';
  priority: 'low' | 'medium' | 'high' | 'critical';
  location?: string | null; // GeoJSON as string
  resolutionNotes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  detectionMethod: 'automatic' | 'manual';
  confidenceScore?: number; // 0-100
  utilityId: number;
  utilityName: string;
  utilityType: string;
  // For risk matrix visualization
  likelihoodScore?: number | null; // 1-5
  impactScore?: number | null; // 1-5
  riskScore?: number | null; // calculated: impact * likelihood
}

interface ConflictFormData {
  description: string;
  status: 'open' | 'in-progress' | 'resolved';
  priority: 'low' | 'medium' | 'high' | 'critical';
  utilityId: number;
  resolutionNotes?: string;
  confidenceScore?: number;
  impactScore?: number;
  likelihoodScore?: number;
}

interface DetectionSettings {
  autoDetectConflicts: boolean;
  sensitivityLevel: 'low' | 'medium' | 'high';
  detectCrossings: boolean;
  detectProximity: boolean;
  proximityThreshold: number; // in feet
  includeProposedFeatures: boolean;
  notifyOnDetection: boolean;
}

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

const statusColors = {
  open: 'bg-red-100 text-red-800',
  'in-progress': 'bg-yellow-100 text-yellow-800',
  resolved: 'bg-green-100 text-green-800',
};

export function AdvancedConflictDetection({ projectId }: AdvancedConflictDetectionProps) {
  const { toast } = useToast();
  const [isRunningDetection, setIsRunningDetection] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'list' | 'map' | 'matrix'>('list');
  const [isNewConflictDialogOpen, setIsNewConflictDialogOpen] = useState(false);
  const [isConflictDetailDialogOpen, setIsConflictDetailDialogOpen] = useState(false);
  const [selectedConflict, setSelectedConflict] = useState<Conflict | null>(null);
  const [detectionSettings, setDetectionSettings] = useState<DetectionSettings>({
    autoDetectConflicts: true,
    sensitivityLevel: 'medium',
    detectCrossings: true,
    detectProximity: true,
    proximityThreshold: 5, // 5 feet by default
    includeProposedFeatures: true,
    notifyOnDetection: true,
  });
  const [formData, setFormData] = useState<ConflictFormData>({
    description: '',
    status: 'open',
    priority: 'medium',
    utilityId: 0,
    resolutionNotes: '',
    confidenceScore: 100, // Default for manual entry
    impactScore: 3,
    likelihoodScore: 3,
  });
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [filterPriority, setFilterPriority] = useState<string | null>(null);
  const [filterUtility, setFilterUtility] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const utils = api.useUtils();

  // Fetch conflicts
  const { data: conflictsData, isLoading: isLoadingConflicts } = api.conflicts.getByProjectId.useQuery(
    { projectId },
    { enabled: !!projectId }
  );

  // Map API conflicts to our Conflict interface
  const conflicts = conflictsData?.map((conflict: any) => ({
    id: conflict.id,
    description: conflict.description,
    status: conflict.status as 'open' | 'in-progress' | 'resolved',
    priority: conflict.priority as 'low' | 'medium' | 'high' | 'critical',
    location: conflict.location,
    resolutionNotes: conflict.resolutionNotes,
    createdAt: conflict.createdAt,
    updatedAt: conflict.updatedAt,
    detectionMethod: 'manual' as 'automatic' | 'manual',
    confidenceScore: 80,
    utilityId: conflict.utility?.id || 0,
    utilityName: conflict.utility?.name || 'Unknown Utility',
    utilityType: conflict.utility?.type || 'Unknown',
    likelihoodScore: conflict.likelihoodScore,
    impactScore: conflict.impactScore,
    riskScore: conflict.riskScore,
  }));

  // Fetch utilities for the project
  const { data: utilities, isLoading: isLoadingUtilities } = (api.utilities as any).getByProjectId?.useQuery(
    { projectId },
    { enabled: !!projectId }
  ) || { data: [], isLoading: false };

  // Mutation for creating a new conflict
  const createConflictMutation = api.conflicts.create.useMutation({
    onSuccess: () => {
      void utils.conflicts.getByProjectId.invalidate({ projectId });
      setIsNewConflictDialogOpen(false);
      resetForm();
      toast({
        title: 'Conflict Created',
        description: 'New conflict has been successfully created.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create the conflict. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Mutation for updating a conflict
  const updateConflictMutation = api.conflicts.update.useMutation({
    onSuccess: () => {
      void utils.conflicts.getByProjectId.invalidate({ projectId });
      setIsConflictDetailDialogOpen(false);
      setSelectedConflict(null);
      toast({
        title: 'Conflict Updated',
        description: 'Conflict has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update the conflict. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Enhanced conflict detection using Docker spatial processor with fallback
  const runDetectionMutation = api.spatialDataEnhanced.detectConflicts.useMutation({
    onSuccess: (data: any) => {
      void utils.conflicts.getByProjectId.invalidate({ projectId });
      setIsRunningDetection(false);

      const conflictCount = data.metadata?.conflictCount || data.conflicts?.length || 0;
      const method = data.metadata?.detectionMethod || 'unknown';
      const processingTime = data.metadata?.processingTime || 0;

      // Show notification with results if enabled in settings
      if (detectionSettings.notifyOnDetection) {
        toast({
          title: 'Enhanced Conflict Detection Complete',
          description: `Found ${conflictCount} potential conflicts using ${method} in ${processingTime}ms.`,
          variant: conflictCount > 0 ? 'destructive' : 'default',
        });

        // If we have critical conflicts, show an additional warning
        const criticalConflicts = conflicts?.filter((c: any) => c.priority === 'critical').length || 0;
        if (criticalConflicts > 0) {
          setTimeout(() => {
            toast({
              title: 'Critical Conflicts Detected',
              description: `${criticalConflicts} critical conflicts require immediate attention.`,
              variant: 'destructive',
            });
          }, 1000);
        }
      }
    },
    onError: () => {
      setIsRunningDetection(false);
      toast({
        title: 'Detection Failed',
        description: 'An error occurred while running conflict detection.',
        variant: 'destructive',
      });
    },
  });

  // Filter conflicts based on selected filters
  const filteredConflicts =
    conflicts?.filter((conflict: any) => {
      // Status filter
      if (filterStatus && conflict.status !== filterStatus) return false;

      // Priority filter
      if (filterPriority && conflict.priority !== filterPriority) return false;

      // Utility filter
      if (filterUtility && conflict.utilityId !== filterUtility) return false;

      // Search query (check description and resolution notes)
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const inDescription = conflict.description.toLowerCase().includes(query);
        const inNotes = conflict.resolutionNotes?.toLowerCase().includes(query) || false;
        const inUtilityName = (conflict.utilityName || '').toLowerCase().includes(query);

        if (!inDescription && !inNotes && !inUtilityName) return false;
      }

      return true;
    }) || [];

  // Reset the form
  const resetForm = () => {
    setFormData({
      description: '',
      status: 'open',
      priority: 'medium',
      utilityId: utilities && utilities.length > 0 && utilities[0] ? utilities[0].id : 0,
      resolutionNotes: '',
      confidenceScore: 100,
      impactScore: 3,
      likelihoodScore: 3,
    });
  };

  // Handle running enhanced detection with Docker spatial processor
  const handleRunDetection = () => {
    setIsRunningDetection(true);
    runDetectionMutation.mutate({ 
      projectId,
      options: {
        horizontalThreshold: detectionSettings.proximityThreshold,
        verticalThreshold: 1.0, // Default 1 foot vertical threshold
        includeAerial: false,
        includeSurface: false,
        confidenceThreshold: 0.6,
        usePostGIS: true,
        notifyRealtime: detectionSettings.notifyOnDetection,
      },
      useSpatialProcessor: true, // Enable Docker spatial processor
    });
  };

  // Handle opening the conflict detail dialog
  const handleViewConflict = (conflict: Conflict) => {
    setSelectedConflict(conflict);
    setFormData({
      description: conflict.description,
      status: conflict.status,
      priority: conflict.priority,
      utilityId: conflict.utilityId,
      resolutionNotes: conflict.resolutionNotes || '',
      confidenceScore: conflict.confidenceScore,
      impactScore: conflict.impactScore ?? undefined,
      likelihoodScore: conflict.likelihoodScore ?? undefined,
    });
    setIsConflictDetailDialogOpen(true);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string | number) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle settings changes
  const handleSettingsChange = (name: string, value: any) => {
    setDetectionSettings((prev) => ({ ...prev, [name]: value }));
  };

  // Create a new conflict
  const handleCreateConflict = () => {
    const riskScore = (formData.impactScore || 3) * (formData.likelihoodScore || 3);

    createConflictMutation.mutate({
      project_id: projectId,
      utility_id: formData.utilityId,
      description: formData.description,
      priority: formData.priority,
      status: formData.status,
      detection_method: 'manual',
      confidence_score: formData.confidenceScore,
      impact_score: formData.impactScore,
      likelihood_score: formData.likelihoodScore,
    });
  };

  // Apply changes to a conflict
  const handleUpdateConflict = () => {
    if (!selectedConflict) return;

    const riskScore = (formData.impactScore || 3) * (formData.likelihoodScore || 3);

    updateConflictMutation.mutate({
      id: selectedConflict.id,
      ...formData,
    });
  };

  // Initialize the form when utilities are loaded
  useEffect(() => {
    if (utilities && utilities.length > 0 && !formData.utilityId) {
      setFormData((prev) => ({ ...prev, utilityId: utilities[0] ? utilities[0].id : 0 }));
    }
  }, [utilities, formData.utilityId]);

  // If data is loading, show a loading state
  if (isLoadingConflicts || isLoadingUtilities) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Advanced Conflict Detection</CardTitle>
          <CardDescription>Loading conflict data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-10 bg-muted rounded mb-4"></div>
            <div className="h-60 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="space-y-1">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                Advanced Conflict Detection
              </CardTitle>
              <CardDescription>
                Detect, analyze, and manage utility conflicts with AI-powered detection
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={() => setIsSettingsDialogOpen(true)}
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
              >
                <Settings className="h-4 w-4" />
                Settings
              </Button>
              <Button
                onClick={handleRunDetection}
                size="sm"
                disabled={isRunningDetection}
                className="flex items-center gap-1"
              >
                {isRunningDetection ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4" />
                    Run Detection
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Summary Stats */}
          {conflicts && conflicts.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4">
              <div className="bg-red-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {conflicts.filter((c: any) => c.priority === 'critical').length}
                </div>
                <div className="text-sm text-red-600">Critical</div>
              </div>
              <div className="bg-orange-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {conflicts.filter((c: any) => c.priority === 'high').length}
                </div>
                <div className="text-sm text-orange-600">High Priority</div>
              </div>
              <div className="bg-yellow-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {conflicts.filter((c: any) => c.status === 'open').length}
                </div>
                <div className="text-sm text-yellow-600">Open</div>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {conflicts.filter((c: any) => c.status === 'resolved').length}
                </div>
                <div className="text-sm text-green-600">Resolved</div>
              </div>
            </div>
          )}

          <Tabs
            defaultValue="list"
            className="w-full"
            onValueChange={(value) => setActiveTab(value as 'list' | 'map' | 'matrix')}
          >
            <div className="flex items-center justify-between">
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger value="list" className="flex items-center gap-1">
                  <BarChart3 className="h-4 w-4" />
                  List View
                </TabsTrigger>
                <TabsTrigger value="map" className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  Map View
                </TabsTrigger>
                <TabsTrigger value="matrix" className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4" />
                  Risk Matrix
                </TabsTrigger>
              </TabsList>

              <Button
                onClick={() => setIsNewConflictDialogOpen(true)}
                size="sm"
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                Add Conflict
              </Button>
            </div>

            {/* Filter Bar */}
            <div className="flex flex-col sm:flex-row gap-2 my-3">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search conflicts..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e: any) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <Select
                value={filterStatus || ''}
                onValueChange={(value) => setFilterStatus(value || null)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={filterPriority || ''}
                onValueChange={(value) => setFilterPriority(value || null)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Priorities</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
              {utilities && (
                <Select
                  value={filterUtility?.toString() || ''}
                  onValueChange={(value) => setFilterUtility(value ? parseInt(value) : null)}
                >
                  <SelectTrigger className="w-[180px] truncate">
                    <SelectValue placeholder="Utility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Utilities</SelectItem>
                    {utilities.map((utility: any) => (
                      <SelectItem key={utility.id} value={utility.id.toString()}>
                        {utility.name} ({utility.type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Tab Content */}
            <TabsContent value="list" className="mt-2">
              {filteredConflicts.length === 0 ? (
                <div className="text-center py-12 border rounded-md">
                  <div className="flex justify-center mb-4">
                    <Search className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">No conflicts found</h3>
                  <p className="text-muted-foreground mt-2 mb-6">
                    {conflicts && conflicts.length > 0
                      ? 'Try adjusting your filters to see more results.'
                      : 'There are no utility conflicts detected for this project yet.'}
                  </p>
                  <div className="flex justify-center gap-2">
                    <Button onClick={handleRunDetection} disabled={isRunningDetection}>
                      {isRunningDetection ? 'Running Detection...' : 'Run Auto-Detection'}
                    </Button>
                    <Button variant="outline" onClick={() => setIsNewConflictDialogOpen(true)}>
                      Add Manually
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Utility</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Detection</TableHead>
                        <TableHead>Risk Score</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredConflicts.map((conflict: any) => (
                        <TableRow key={conflict.id}>
                          <TableCell>
                            <div className="font-medium">{conflict.utilityName}</div>
                            <div className="text-sm text-muted-foreground">
                              {conflict.utilityType}
                            </div>
                          </TableCell>
                          <TableCell>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="max-w-[200px] truncate">
                                    {conflict.description}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent side="bottom" className="max-w-md">
                                  <p>{conflict.description}</p>
                                  {conflict.resolutionNotes && (
                                    <>
                                      <p className="font-medium mt-2">Resolution Notes:</p>
                                      <p>{conflict.resolutionNotes}</p>
                                    </>
                                  )}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>
                          <TableCell>
                            <Badge className={statusColors[conflict.status as keyof typeof statusColors]}>
                              {conflict.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={priorityColors[conflict.priority as keyof typeof priorityColors]}>
                              {conflict.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {conflict.detectionMethod === 'automatic' ? (
                              <div className="flex items-center">
                                <Zap className="h-3.5 w-3.5 text-amber-500 mr-1" />
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <div className="text-sm">
                                        Auto (
                                        {conflict.confidenceScore
                                          ? `${conflict.confidenceScore}%`
                                          : 'N/A'}
                                        )
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>
                                        Automatically detected with {conflict.confidenceScore}%
                                        confidence
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <Check className="h-3.5 w-3.5 text-green-500 mr-1" />
                                <span className="text-sm">Manual</span>
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {conflict.riskScore !== undefined ? (
                              <Badge
                                variant="outline"
                                className={`
                                  ${
                                    (conflict.riskScore || 0) >= 15
                                      ? 'bg-red-50 text-red-800 border-red-200'
                                      : (conflict.riskScore || 0) >= 10
                                        ? 'bg-orange-50 text-orange-800 border-orange-200'
                                        : (conflict.riskScore || 0) >= 6
                                          ? 'bg-yellow-50 text-yellow-800 border-yellow-200'
                                          : 'bg-green-50 text-green-800 border-green-200'
                                  }
                                `}
                              >
                                {conflict.riskScore}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground">—</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewConflict(conflict)}
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </TabsContent>

            <TabsContent value="map" className="mt-2">
              <div className="border rounded-md overflow-hidden h-[500px] flex items-center justify-center bg-slate-50">
                <div className="text-center">
                  <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Map View</h3>
                  <p className="text-muted-foreground">
                    Interactive conflict mapping will be implemented here
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="matrix" className="mt-2">
              {filteredConflicts.length === 0 ? (
                <div className="text-center py-12 border rounded-md">
                  <div className="flex justify-center mb-4">
                    <AlertCircle className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">No conflicts to display</h3>
                  <p className="text-muted-foreground mt-2">
                    No conflicts match your current filters.
                  </p>
                </div>
              ) : (
                <div className="border rounded-md p-4">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <AlertCircle className="h-4 w-4" />
                      <span>
                        This matrix shows the risk profile of conflicts based on impact severity and
                        likelihood.
                      </span>
                    </div>

                    <RiskMatrix conflicts={filteredConflicts as any[]} />
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardHeader>
      </Card>

      {/* Settings Dialog */}
      <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Detection Settings</DialogTitle>
            <DialogDescription>Configure how automatic conflict detection works</DialogDescription>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="autoDetectConflicts" className="flex-1">
                Auto-detect conflicts
                <p className="text-sm text-muted-foreground">
                  Automatically detect conflicts as project data changes
                </p>
              </Label>
              <Checkbox
                id="autoDetectConflicts"
                checked={detectionSettings.autoDetectConflicts}
                onCheckedChange={(checked) =>
                  handleSettingsChange('autoDetectConflicts', Boolean(checked))
                }
              />
            </div>

            <div className="space-y-3">
              <Label>Sensitivity Level</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="sensitivity-low"
                    name="sensitivityLevel"
                    value="low"
                    checked={detectionSettings.sensitivityLevel === 'low'}
                    onChange={(value: any) => handleSettingsChange('sensitivityLevel', value.target.value)}
                  />
                  <Label htmlFor="sensitivity-low">Low - Only critical conflicts</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="sensitivity-medium"
                    name="sensitivityLevel"
                    value="medium"
                    checked={detectionSettings.sensitivityLevel === 'medium'}
                    onChange={(value: any) => handleSettingsChange('sensitivityLevel', value.target.value)}
                  />
                  <Label htmlFor="sensitivity-medium">Medium - Standard detection</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="sensitivity-high"
                    name="sensitivityLevel"
                    value="high"
                    checked={detectionSettings.sensitivityLevel === 'high'}
                    onChange={(value: any) => handleSettingsChange('sensitivityLevel', value.target.value)}
                  />
                  <Label htmlFor="sensitivity-high">High - Detect all potential conflicts</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2 pt-2">
              <Label>Detection Types</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="detectCrossings"
                    checked={detectionSettings.detectCrossings}
                    onCheckedChange={(checked) =>
                      handleSettingsChange('detectCrossings', Boolean(checked))
                    }
                  />
                  <Label htmlFor="detectCrossings">Detect line crossings</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="detectProximity"
                    checked={detectionSettings.detectProximity}
                    onCheckedChange={(checked) =>
                      handleSettingsChange('detectProximity', Boolean(checked))
                    }
                  />
                  <Label htmlFor="detectProximity">Detect proximity conflicts</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="proximityThreshold" className="flex-1">
                  Proximity Threshold (feet)
                </Label>
                <Input
                  id="proximityThreshold"
                  type="number"
                  className="w-20 text-right"
                  min={1}
                  max={50}
                  value={detectionSettings.proximityThreshold}
                  onChange={(e: any) =>
                    handleSettingsChange('proximityThreshold', parseInt(e.target.value) || 5)
                  }
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Minimum distance between utilities before flagging as a conflict
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSettingsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsSettingsDialogOpen(false)}>Save Settings</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Conflict Dialog */}
      <Dialog open={isNewConflictDialogOpen} onOpenChange={setIsNewConflictDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Conflict</DialogTitle>
            <DialogDescription>Manually add a new utility conflict</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="utilityId" className="text-right">
                Utility
              </Label>
              <Select
                value={formData.utilityId.toString()}
                onValueChange={(value) => handleSelectChange('utilityId', parseInt(value))}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select utility" />
                </SelectTrigger>
                <SelectContent>
                  {utilities?.map((utility: any) => (
                    <SelectItem key={utility.id} value={utility.id.toString()}>
                      {utility.name} ({utility.type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                Priority
              </Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => handleSelectChange('priority', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="impactScore" className="text-right">
                Impact Score
              </Label>
              <Select
                value={formData.impactScore?.toString() || '3'}
                onValueChange={(value) => handleSelectChange('impactScore', parseInt(value))}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select impact severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 - Negligible</SelectItem>
                  <SelectItem value="2">2 - Minor</SelectItem>
                  <SelectItem value="3">3 - Moderate</SelectItem>
                  <SelectItem value="4">4 - Major</SelectItem>
                  <SelectItem value="5">5 - Severe</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="likelihoodScore" className="text-right">
                Likelihood
              </Label>
              <Select
                value={formData.likelihoodScore?.toString() || '3'}
                onValueChange={(value) => handleSelectChange('likelihoodScore', parseInt(value))}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select likelihood" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 - Rare</SelectItem>
                  <SelectItem value="2">2 - Unlikely</SelectItem>
                  <SelectItem value="3">3 - Possible</SelectItem>
                  <SelectItem value="4">4 - Likely</SelectItem>
                  <SelectItem value="5">5 - Almost Certain</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="resolutionNotes" className="text-right pt-2">
                Resolution Notes
              </Label>
              <Textarea
                id="resolutionNotes"
                name="resolutionNotes"
                value={formData.resolutionNotes}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
            {/* Risk score calculated field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="riskScore" className="text-right">
                Risk Score
              </Label>
              <div className="col-span-3 flex items-center space-x-2">
                <Badge
                  variant="outline"
                  className={`
                    ${
                      (formData.impactScore || 3) * (formData.likelihoodScore || 3) >= 15
                        ? 'bg-red-50 text-red-800 border-red-200'
                        : (formData.impactScore || 3) * (formData.likelihoodScore || 3) >= 10
                          ? 'bg-orange-50 text-orange-800 border-orange-200'
                          : (formData.impactScore || 3) * (formData.likelihoodScore || 3) >= 6
                            ? 'bg-yellow-50 text-yellow-800 border-yellow-200'
                            : 'bg-green-50 text-green-800 border-green-200'
                    }
                  `}
                >
                  {(formData.impactScore || 3) * (formData.likelihoodScore || 3)}
                </Badge>
                <span className="text-sm text-muted-foreground">(Impact × Likelihood)</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsNewConflictDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateConflict}
              disabled={
                !formData.description || !formData.utilityId || createConflictMutation.isPending
              }
            >
              {createConflictMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Conflict'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Conflict Detail Dialog */}
      <Dialog open={isConflictDetailDialogOpen} onOpenChange={setIsConflictDetailDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Conflict Details</DialogTitle>
            <DialogDescription>
              {selectedConflict?.utilityName} -{' '}
              {selectedConflict?.detectionMethod === 'automatic'
                ? 'Auto-detected'
                : 'Manually created'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                Priority
              </Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => handleSelectChange('priority', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="resolutionNotes" className="text-right pt-2">
                Resolution Notes
              </Label>
              <Textarea
                id="resolutionNotes"
                name="resolutionNotes"
                value={formData.resolutionNotes}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>

            {/* Meta Information */}
            {selectedConflict && (
              <div className="mt-2 border-t pt-4">
                <h4 className="text-sm font-medium mb-2">Additional Information</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">Created:</span>{' '}
                    {new Date(selectedConflict.createdAt).toLocaleDateString()}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Last Updated:</span>{' '}
                    {new Date(selectedConflict.updatedAt).toLocaleDateString()}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Detection:</span>{' '}
                    {selectedConflict.detectionMethod === 'automatic' ? 'Automatic' : 'Manual'}
                  </div>
                  {selectedConflict.detectionMethod === 'automatic' && (
                    <div>
                      <span className="text-muted-foreground">Confidence:</span>{' '}
                      {selectedConflict.confidenceScore !== undefined
                        ? `${selectedConflict.confidenceScore}%`
                        : 'N/A'}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConflictDetailDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateConflict} disabled={updateConflictMutation.isPending}>
              {updateConflictMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface RiskMatrixProps {
  conflicts: Conflict[];
}

// Risk Matrix Component extracted for readability
function RiskMatrix({ conflicts }: RiskMatrixProps) {
  // Create the grid labels
  const impactLabels = ['Negligible', 'Minor', 'Moderate', 'Major', 'Severe'];
  const likelihoodLabels = ['Rare', 'Unlikely', 'Possible', 'Likely', 'Almost Certain'];

  // Ensure all conflicts have impact and likelihood scores
  const processedConflicts = conflicts.map((conflict: any) => {
    // Map priority to impact/severity score if not already set
    let impactScore = conflict.impactScore;
    if (!impactScore) {
      switch (conflict.priority.toLowerCase()) {
        case 'critical':
          impactScore = 5;
          break;
        case 'high':
          impactScore = 4;
          break;
        case 'medium':
          impactScore = 3;
          break;
        case 'low':
          impactScore = 2;
          break;
        default:
          impactScore = 1;
      }
    }

    // Determine likelihood score based on status if not already set
    let likelihoodScore = conflict.likelihoodScore;
    if (!likelihoodScore) {
      switch (conflict.status.toLowerCase()) {
        case 'open':
          likelihoodScore = 5;
          break; // Most likely to cause issues
        case 'in-progress':
          likelihoodScore = 3;
          break; // Being addressed
        case 'resolved':
          likelihoodScore = 1;
          break; // Least likely to cause issues
        default:
          likelihoodScore = 2;
      }
    }

    return {
      ...conflict,
      impactScore,
      likelihoodScore,
    };
  });

  return (
    <div className="border rounded-md p-4 bg-white">
      <div className="flex">
        {/* Y-axis label */}
        <div className="flex flex-col justify-center mr-2">
          <div className="transform -rotate-90 origin-center whitespace-nowrap text-sm font-medium text-muted-foreground h-40">
            Likelihood
          </div>
        </div>

        {/* Matrix Grid */}
        <div className="flex-1">
          <div className="grid grid-cols-5 gap-1">
            {/* Empty cell for top-left corner */}
            <div className="h-8"></div>

            {/* Impact labels on top */}
            {impactLabels.map((label, i) => (
              <div
                key={`impact-${i}`}
                className="h-8 flex items-center justify-center text-xs font-medium overflow-hidden"
              >
                {label}
              </div>
            ))}

            {/* Generate the 5x5 grid */}
            {Array.from({ length: 5 }).map((_, row) => {
              // Reverse row index to have 5 at the top and 1 at the bottom
              const likelihoodIdx = 4 - row;

              return (
                <React.Fragment key={`row-${row}`}>
                  {/* Likelihood label on the left */}
                  <div className="flex items-center text-xs font-medium h-16">
                    {likelihoodLabels[likelihoodIdx]}
                  </div>

                  {/* Grid cells for this row */}
                  {Array.from({ length: 5 }).map((_, col) => {
                    // Get impact index (col + 1 maps to impact score 1-5)
                    const impactIdx = col;
                    const impactScore = impactIdx + 1;
                    const likelihoodScore = likelihoodIdx + 1;
                    const riskScore = impactScore * likelihoodScore;

                    // Determine cell color based on risk score
                    let bgColor = 'bg-muted';
                    if (riskScore >= 20) bgColor = 'bg-red-500';
                    else if (riskScore >= 15) bgColor = 'bg-red-300';
                    else if (riskScore >= 10) bgColor = 'bg-orange-300';
                    else if (riskScore >= 6) bgColor = 'bg-yellow-200';
                    else if (riskScore >= 3) bgColor = 'bg-green-200';

                    // Find conflicts that fall in this cell
                    const cellConflicts = processedConflicts.filter(
                      (c) =>
                        Math.round(c.impactScore || 0) === impactScore &&
                        Math.round(c.likelihoodScore || 0) === likelihoodScore
                    );

                    return (
                      <div
                        key={`cell-${row}-${col}`}
                        className={`h-16 rounded relative flex items-center justify-center ${bgColor} transition-colors hover:opacity-90 cursor-pointer`}
                      >
                        {cellConflicts.length > 0 ? (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="w-full h-full flex items-center justify-center">
                                  <div className="font-bold text-sm">{cellConflicts.length}</div>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <ScrollArea className="h-fit max-h-[200px]">
                                  <div className="p-1">
                                    {cellConflicts.map((c: any) => (
                                      <div key={c.id} className="mb-2 last:mb-0">
                                        <div className="text-sm font-medium">{c.utilityName}</div>
                                        <div className="text-xs mt-1">{c.description}</div>
                                      </div>
                                    ))}
                                  </div>
                                </ScrollArea>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <div className="text-xs text-muted-foreground">{riskScore}</div>
                        )}
                      </div>
                    );
                  })}
                </React.Fragment>
              );
            })}
          </div>

          {/* X-axis label */}
          <div className="text-center mt-2 text-sm font-medium text-muted-foreground">
            Impact Severity
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdvancedConflictDetection;
