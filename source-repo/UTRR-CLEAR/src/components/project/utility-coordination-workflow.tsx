'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '~/components/ui/alert-dialog';
import { Textarea } from '~/components/ui/textarea';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import {
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  FileText,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Plus,
  Edit,
  Trash2,
  Send,
  Download,
  Upload,
  Filter,
  Search,
  RefreshCw,
  Settings,
  ArrowRight,
  ArrowLeft,
  Timer,
  Target,
  TrendingUp,
  Activity,
} from 'lucide-react';

interface WorkflowPhase {
  id: string;
  name: string;
  description: string;
  order: number;
  estimatedDays: number;
  prerequisites: string[];
  deliverables: string[];
  status: 'not-started' | 'in-progress' | 'completed' | 'blocked' | 'delayed';
  actualStartDate?: string;
  estimatedEndDate?: string;
  actualEndDate?: string;
  assignedTo?: string;
  notes?: string;
  dependencies: string[];
  milestones: WorkflowMilestone[];
}

interface WorkflowMilestone {
  id: string;
  name: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'completed' | 'overdue';
  assignedTo: string;
  deliverables: string[];
}

interface UtilityWorkflowItem {
  id: string;
  utilityCompany: string;
  utilityType: 'electric' | 'gas' | 'water' | 'sewer' | 'telecom' | 'cable' | 'fiber';
  contactPerson: string;
  email: string;
  phone: string;
  currentPhase: string;
  overallProgress: number;
  phases: WorkflowPhase[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  riskLevel: 'low' | 'medium' | 'high';
  lastUpdated: string;
  nextAction: string;
  estimatedCompletion: string;
  actualCost?: number;
  estimatedCost: number;
  documents: Document[];
  communications: Communication[];
}

interface Document {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  size: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface Communication {
  id: string;
  type: 'email' | 'phone' | 'meeting' | 'document';
  date: string;
  summary: string;
  followUpRequired: boolean;
  followUpDate?: string;
}

export default function UtilityCoordinationWorkflow() {
  const [workflows, setWorkflows] = useState<UtilityWorkflowItem[]>([
    {
      id: '1',
      utilityCompany: 'AEP Indiana',
      utilityType: 'electric',
      contactPerson: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      currentPhase: 'design-review',
      overallProgress: 65,
      priority: 'high',
      riskLevel: 'medium',
      lastUpdated: '2024-01-15',
      nextAction: 'Submit final design drawings',
      estimatedCompletion: '2024-02-28',
      estimatedCost: 125000,
      actualCost: 98000,
      phases: [
        {
          id: 'initial-contact',
          name: 'Initial Contact',
          description: 'Establish communication and project requirements',
          order: 1,
          estimatedDays: 5,
          prerequisites: [],
          deliverables: ['Initial meeting notes', 'Project scope document'],
          status: 'completed',
          actualStartDate: '2024-01-01',
          actualEndDate: '2024-01-05',
          assignedTo: 'John Smith',
          dependencies: [],
          milestones: [
            {
              id: 'm1',
              name: 'Initial Meeting',
              description: 'First project coordination meeting',
              dueDate: '2024-01-03',
              status: 'completed',
              assignedTo: 'John Smith',
              deliverables: ['Meeting minutes'],
            },
          ],
        },
        {
          id: 'design-review',
          name: 'Design Review',
          description: 'Review and approve utility design modifications',
          order: 2,
          estimatedDays: 15,
          prerequisites: ['initial-contact'],
          deliverables: ['Approved design drawings', 'Cost estimate'],
          status: 'in-progress',
          actualStartDate: '2024-01-06',
          estimatedEndDate: '2024-01-25',
          assignedTo: 'Sarah Johnson',
          dependencies: ['initial-contact'],
          milestones: [
            {
              id: 'm2',
              name: 'Preliminary Design',
              description: 'Submit initial design concepts',
              dueDate: '2024-01-15',
              status: 'completed',
              assignedTo: 'Design Team',
              deliverables: ['Preliminary drawings'],
            },
            {
              id: 'm3',
              name: 'Final Design',
              description: 'Complete final design documentation',
              dueDate: '2024-01-25',
              status: 'pending',
              assignedTo: 'Design Team',
              deliverables: ['Final drawings', 'Specifications'],
            },
          ],
        },
      ],
      documents: [
        {
          id: 'd1',
          name: 'Design Drawings Rev 1.pdf',
          type: 'design',
          uploadDate: '2024-01-10',
          size: '2.5 MB',
          status: 'approved',
        },
      ],
      communications: [
        {
          id: 'c1',
          type: 'email',
          date: '2024-01-15',
          summary: 'Discussed design revisions and timeline',
          followUpRequired: true,
          followUpDate: '2024-01-20',
        },
      ],
    },
  ]);

  const [selectedWorkflow, setSelectedWorkflow] = useState<UtilityWorkflowItem | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [showAddWorkflow, setShowAddWorkflow] = useState(false);
  const [showPhaseDialog, setShowPhaseDialog] = useState(false);
  const [editingPhase, setEditingPhase] = useState<WorkflowPhase | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'delayed':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-muted text-gray-800 border-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      default:
        return 'bg-green-500 text-white';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-orange-600';
      default:
        return 'text-green-600';
    }
  };

  const getUtilityTypeIcon = (type: string) => {
    switch (type) {
      case 'electric':
        return '⚡';
      case 'gas':
        return '🔥';
      case 'water':
        return '💧';
      case 'sewer':
        return '🚰';
      case 'telecom':
        return '📞';
      case 'cable':
        return '📺';
      case 'fiber':
        return '💻';
      default:
        return '🔧';
    }
  };

  const filteredWorkflows = workflows.filter((workflow: any) => {
    const matchesSearch =
      workflow.utilityCompany.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workflow.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || workflow.phases.some((phase: any) => phase.status === statusFilter);
    const matchesPriority = priorityFilter === 'all' || workflow.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  const addPhase = (workflowId: string, phase: Omit<WorkflowPhase, 'id'>) => {
    setWorkflows((prev) =>
      prev.map((workflow: any) =>
        workflow.id === workflowId
          ? {
              ...workflow,
              phases: [...workflow.phases, { ...phase, id: `phase-${Date.now()}` }],
            }
          : workflow
      )
    );
  };

  const updatePhaseStatus = (
    workflowId: string,
    phaseId: string,
    status: WorkflowPhase['status']
  ) => {
    setWorkflows((prev) =>
      prev.map((workflow: any) =>
        workflow.id === workflowId
          ? {
              ...workflow,
              phases: workflow.phases.map((phase: any) =>
                phase.id === phaseId ? { ...phase, status } : phase
              ),
            }
          : workflow
      )
    );
  };

  const calculateOverallProgress = (phases: WorkflowPhase[]) => {
    if (phases.length === 0) return 0;
    const completedPhases = phases.filter((phase: any) => phase.status === 'completed').length;
    return Math.round((completedPhases / phases.length) * 100);
  };

  const getNextAction = (phases: WorkflowPhase[]) => {
    const inProgressPhase = phases.find((phase: any) => phase.status === 'in-progress');
    if (inProgressPhase) {
      return `Continue ${inProgressPhase.name}`;
    }

    const nextPhase = phases.find((phase: any) => phase.status === 'not-started');
    if (nextPhase) {
      return `Start ${nextPhase.name}`;
    }

    return 'All phases completed';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Utility Coordination Workflow</h2>
          <p className="text-muted-foreground">Manage coordination workflows with utility companies</p>
        </div>
        <Button onClick={() => setShowAddWorkflow(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Workflow
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                <Input
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="not-started">Not Started</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="blocked">Blocked</SelectItem>
                <SelectItem value="delayed">Delayed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Active Workflows</p>
                <p className="text-2xl font-bold">
                  {workflows.filter((w: any) => w.phases.some((p: any) => p.status === 'in-progress')).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">
                  {workflows.filter((w: any) => w.phases.every((p: any) => p.status === 'completed')).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">At Risk</p>
                <p className="text-2xl font-bold">
                  {workflows.filter((w: any) => w.riskLevel === 'high').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Avg Progress</p>
                <p className="text-2xl font-bold">
                  {Math.round(
                    workflows.reduce((acc: any, w: any) => acc + w.overallProgress, 0) / workflows.length
                  )}
                  %
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflows List */}
      <Card>
        <CardHeader>
          <CardTitle>Utility Workflows</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredWorkflows.map((workflow: any) => (
              <Card key={workflow.id} className="border-l-4 border-l-blue-500">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{getUtilityTypeIcon(workflow.utilityType)}</span>
                      <div>
                        <h3 className="font-semibold text-lg">{workflow.utilityCompany}</h3>
                        <p className="text-sm text-muted-foreground">
                          {workflow.contactPerson} • {workflow.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(workflow.priority)}>
                        {workflow.priority}
                      </Badge>
                      <Badge variant="outline" className={getRiskColor(workflow.riskLevel)}>
                        {workflow.riskLevel} risk
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Overall Progress</p>
                      <Progress value={workflow.overallProgress} className="mt-1" />
                      <p className="text-xs text-muted-foreground mt-1">
                        {workflow.overallProgress}% complete
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Current Phase</p>
                      <p className="font-medium">
                        {workflow.currentPhase.replace('-', ' ').toUpperCase()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Est. Completion</p>
                      <p className="font-medium">{workflow.estimatedCompletion}</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Updated {workflow.lastUpdated}
                      </span>
                      <span className="flex items-center gap-1">
                        <Target className="h-4 w-4" />
                        {workflow.nextAction}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedWorkflow(workflow)}
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Workflow Detail Dialog */}
      <Dialog open={!!selectedWorkflow} onOpenChange={() => setSelectedWorkflow(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <span className="text-2xl">
                {selectedWorkflow && getUtilityTypeIcon(selectedWorkflow.utilityType)}
              </span>
              {selectedWorkflow?.utilityCompany} - Workflow Details
            </DialogTitle>
          </DialogHeader>

          {selectedWorkflow && (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="phases">Phases</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="communications">Communications</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Contact Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedWorkflow.contactPerson}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedWorkflow.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedWorkflow.phone}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Project Status</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm">Overall Progress</span>
                          <span className="text-sm font-medium">
                            {selectedWorkflow.overallProgress}%
                          </span>
                        </div>
                        <Progress value={selectedWorkflow.overallProgress} />
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Priority</p>
                          <Badge className={getPriorityColor(selectedWorkflow.priority)}>
                            {selectedWorkflow.priority}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Risk Level</p>
                          <span className={getRiskColor(selectedWorkflow.riskLevel)}>
                            {selectedWorkflow.riskLevel}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Cost Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Estimated Cost</p>
                        <p className="text-xl font-bold text-blue-600">
                          ${selectedWorkflow.estimatedCost.toLocaleString()}
                        </p>
                      </div>
                      {selectedWorkflow.actualCost && (
                        <div>
                          <p className="text-sm text-muted-foreground">Actual Cost</p>
                          <p className="text-xl font-bold text-green-600">
                            ${selectedWorkflow.actualCost.toLocaleString()}
                          </p>
                        </div>
                      )}
                      {selectedWorkflow.actualCost && (
                        <div>
                          <p className="text-sm text-muted-foreground">Variance</p>
                          <p
                            className={`text-xl font-bold ${
                              selectedWorkflow.actualCost > selectedWorkflow.estimatedCost
                                ? 'text-red-600'
                                : 'text-green-600'
                            }`}
                          >
                            {selectedWorkflow.actualCost > selectedWorkflow.estimatedCost
                              ? '+'
                              : '-'}
                            $
                            {Math.abs(
                              selectedWorkflow.actualCost - selectedWorkflow.estimatedCost
                            ).toLocaleString()}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="phases" className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Workflow Phases</h3>
                  <Button onClick={() => setShowPhaseDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Phase
                  </Button>
                </div>

                <div className="space-y-4">
                  {selectedWorkflow.phases
                    .sort((a: any, b: any) => a.order - b.order)
                    .map((phase, index) => (
                      <Card key={phase.id} className="relative">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-4">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 font-semibold">
                                {index + 1}
                              </div>
                              <div>
                                <h4 className="font-semibold">{phase.name}</h4>
                                <p className="text-sm text-muted-foreground">{phase.description}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className={getStatusColor(phase.status)}>
                                {phase.status.replace('-', ' ')}
                              </Badge>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setEditingPhase(phase)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-muted-foreground">Estimated Duration</p>
                              <p className="font-medium">{phase.estimatedDays} days</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Assigned To</p>
                              <p className="font-medium">{phase.assignedTo || 'Unassigned'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Dependencies</p>
                              <p className="font-medium">{phase.dependencies.length || 'None'}</p>
                            </div>
                          </div>

                          {phase.milestones.length > 0 && (
                            <Accordion type="single" collapsible>
                              <AccordionItem value="milestones">
                                <AccordionTrigger>
                                  Milestones ({phase.milestones.length})
                                </AccordionTrigger>
                                <AccordionContent>
                                  <div className="space-y-2">
                                    {phase.milestones.map((milestone: any) => (
                                      <div
                                        key={milestone.id}
                                        className="flex justify-between items-center p-2 bg-muted rounded"
                                      >
                                        <div>
                                          <p className="font-medium">{milestone.name}</p>
                                          <p className="text-sm text-muted-foreground">
                                            {milestone.description}
                                          </p>
                                          <p className="text-xs text-muted-foreground">
                                            Due: {milestone.dueDate}
                                          </p>
                                        </div>
                                        <Badge className={getStatusColor(milestone.status)}>
                                          {milestone.status}
                                        </Badge>
                                      </div>
                                    ))}
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          )}

                          <div className="flex justify-between items-center mt-4">
                            <div className="flex gap-2">
                              {phase.status !== 'completed' && (
                                <>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                      updatePhaseStatus(
                                        selectedWorkflow.id,
                                        phase.id,
                                        'in-progress'
                                      )
                                    }
                                    disabled={phase.status === 'in-progress'}
                                  >
                                    Start
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() =>
                                      updatePhaseStatus(selectedWorkflow.id, phase.id, 'completed')
                                    }
                                  >
                                    Complete
                                  </Button>
                                </>
                              )}
                              {phase.status === 'in-progress' && (
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() =>
                                    updatePhaseStatus(selectedWorkflow.id, phase.id, 'blocked')
                                  }
                                >
                                  Mark Blocked
                                </Button>
                              )}
                            </div>
                            {phase.notes && (
                              <p className="text-sm text-muted-foreground italic">Note: {phase.notes}</p>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="documents" className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Documents</h3>
                  <Button>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Document Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Upload Date</TableHead>
                      <TableHead>Size</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedWorkflow.documents.map((doc: any) => (
                      <TableRow key={doc.id}>
                        <TableCell className="font-medium">{doc.name}</TableCell>
                        <TableCell>{doc.type}</TableCell>
                        <TableCell>{doc.uploadDate}</TableCell>
                        <TableCell>{doc.size}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(doc.status)}>{doc.status}</Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="communications" className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Communication Log</h3>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Log Communication
                  </Button>
                </div>

                <div className="space-y-4">
                  {selectedWorkflow.communications.map((comm: any) => (
                    <Card key={comm.id}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center gap-2">
                            {comm.type === 'email' && <Mail className="h-4 w-4" />}
                            {comm.type === 'phone' && <Phone className="h-4 w-4" />}
                            {comm.type === 'meeting' && <Users className="h-4 w-4" />}
                            {comm.type === 'document' && <FileText className="h-4 w-4" />}
                            <span className="font-medium capitalize">{comm.type}</span>
                          </div>
                          <span className="text-sm text-muted-foreground">{comm.date}</span>
                        </div>
                        <p className="text-sm mb-2">{comm.summary}</p>
                        {comm.followUpRequired && (
                          <div className="flex items-center gap-2 text-sm text-orange-600">
                            <Clock className="h-4 w-4" />
                            Follow-up required by {comm.followUpDate}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Phase Dialog */}
      <Dialog open={showPhaseDialog} onOpenChange={setShowPhaseDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Phase</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="phaseName">Phase Name</Label>
              <Input id="phaseName" placeholder="Enter phase name" />
            </div>
            <div>
              <Label htmlFor="phaseDescription">Description</Label>
              <Textarea id="phaseDescription" placeholder="Enter phase description" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="estimatedDays">Estimated Days</Label>
                <Input id="estimatedDays" type="number" placeholder="0" />
              </div>
              <div>
                <Label htmlFor="assignedTo">Assigned To</Label>
                <Input id="assignedTo" placeholder="Enter assignee" />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowPhaseDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowPhaseDialog(false)}>Add Phase</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
