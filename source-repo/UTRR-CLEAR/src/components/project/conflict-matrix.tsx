'use client';

import React, { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Check, X, AlertTriangle, Info, TrendingUp, BarChart3, Grid3x3 } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { ScrollArea } from '~/components/ui/scroll-area';
import { api } from '~/trpc/react';
import { CommentBadge } from '~/components/comments/comment-badge';
import { useContextMenuRef } from '~/hooks/use-context-menu';

interface ConflictMatrixProps {
  projectId: string;
  onConflictSelect?: (conflict: Conflict) => void;
}

interface Conflict {
  id: number;
  description: string;
  status: 'open' | 'in-progress' | 'resolved';
  priority: 'low' | 'medium' | 'high' | 'critical';
  location?: string | null;
  resolutionNotes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  likelihoodScore?: number | null; // 1-5
  impactScore?: number | null; // 1-5
  riskScore?: number | null;
  utility: {
    id: number;
    name: string;
    type: string;
  };
}

interface UtilityWithConflicts {
  id: number;
  name: string;
  type: string;
  conflictCount: number;
  avgImpact?: number;
  avgLikelihood?: number;
  maxRiskScore?: number;
  conflicts?: Array<{
    id: number;
    description: string;
    status: 'open' | 'in-progress' | 'resolved';
    priority: 'low' | 'medium' | 'high' | 'critical';
    likelihoodScore?: number;
    impactScore?: number;
    riskScore?: number;
    utility: {
      id: number;
      name: string;
      type: string;
    };
    location?: string;
    resolutionNotes?: string;
    createdAt: Date;
    updatedAt: Date;
  }>;
}

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

const statusColors = {
  open: 'bg-red-100 text-red-800',
  'in-progress': 'bg-yellow-100 text-yellow-800',
  resolved: 'bg-green-100 text-green-800',
};

export function ConflictMatrix({ projectId, onConflictSelect }: ConflictMatrixProps) {
  const [activeView, setActiveView] = useState<'matrix' | 'list' | 'summary'>('matrix');

  const { data: conflictsData, isLoading } = api.conflicts.getByProjectId.useQuery(
    { projectId },
    { enabled: !!projectId }
  );
  
  // Transform conflicts to match the component's Conflict interface
  const conflicts = conflictsData?.map((conflict: any) => ({
    id: conflict.id,
    description: conflict.description,
    status: conflict.status as 'open' | 'in-progress' | 'resolved',
    priority: conflict.priority as 'low' | 'medium' | 'high' | 'critical',
    location: conflict.location,
    resolutionNotes: conflict.resolutionNotes,
    createdAt: conflict.createdAt,
    updatedAt: conflict.updatedAt,
    likelihoodScore: conflict.likelihoodScore,
    impactScore: conflict.impactScore,
    riskScore: conflict.riskScore,
    utility: {
      id: conflict.utility?.id || 0,
      name: conflict.utility?.name || 'Unknown',
      type: conflict.utility?.type || 'Unknown',
    },
  }));

  const { data: utilities, isLoading: isLoadingUtilities } =
    (api.projects as any).getUtilitiesWithConflicts?.useQuery({ projectId }, { enabled: !!projectId }) || { data: [], isLoading: false };

  if (isLoading || isLoadingUtilities) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid3x3 className="h-5 w-5" />
            Conflict Matrix
          </CardTitle>
          <CardDescription>Loading conflict analysis...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-10 bg-muted rounded"></div>
            <div className="h-60 bg-muted rounded"></div>
            <div className="h-32 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!conflicts || !utilities || conflicts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid3x3 className="h-5 w-5" />
            Conflict Matrix
          </CardTitle>
          <CardDescription>
            Risk analysis and conflict visualization for this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-16 w-16 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-medium text-gray-900">No conflicts found</h3>
            <p className="text-muted-foreground mt-2 mb-6 max-w-md mx-auto">
              There are no utility conflicts recorded for this project. Run conflict detection or
              add conflicts manually to see the analysis matrix.
            </p>
            <Button>Run Conflict Detection</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Grid3x3 className="h-5 w-5" />
              Conflict Matrix Analysis
            </CardTitle>
            <CardDescription>
              Risk assessment and conflict visualization for utility coordination
            </CardDescription>
          </div>
          <Tabs
            value={activeView}
            onValueChange={(value) => setActiveView(value as 'matrix' | 'list' | 'summary')}
          >
            <TabsList className="grid w-full md:w-[300px] grid-cols-3">
              <TabsTrigger value="matrix" className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4" />
                Matrix
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex items-center gap-1">
                <BarChart3 className="h-4 w-4" />
                Summary
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center gap-1">
                <Grid3x3 className="h-4 w-4" />
                List
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <TabsContent value="matrix" className="mt-0">
          <MatrixView
            conflicts={conflicts}
            utilities={utilities}
            onConflictSelect={onConflictSelect}
          />
        </TabsContent>
        <TabsContent value="summary" className="mt-0">
          <SummaryView conflicts={conflicts} utilities={utilities} />
        </TabsContent>
        <TabsContent value="list" className="mt-0">
          <ListView conflicts={conflicts} onConflictSelect={onConflictSelect} />
        </TabsContent>
      </CardContent>
    </Card>
  );
}

function MatrixView({
  conflicts,
  utilities,
  onConflictSelect,
}: {
  conflicts: Conflict[];
  utilities: UtilityWithConflicts[];
  onConflictSelect?: (conflict: Conflict) => void;
}) {
  // Process conflicts for risk matrix visualization
  const processedConflicts = useMemo(() => {
    return conflicts.map((conflict: any) => {
      // Map priority to impact/severity score if not already set
      let impactScore = conflict.impactScore;
      if (!impactScore) {
        switch (conflict.priority.toLowerCase()) {
          case 'critical':
            impactScore = 5;
            break;
          case 'high':
            impactScore = 4;
            break;
          case 'medium':
            impactScore = 3;
            break;
          case 'low':
            impactScore = 2;
            break;
          default:
            impactScore = 1;
        }
      }

      // Determine likelihood score based on status if not already set
      let likelihoodScore = conflict.likelihoodScore;
      if (!likelihoodScore) {
        switch (conflict.status.toLowerCase()) {
          case 'open':
            likelihoodScore = 5;
            break; // Most likely to cause issues
          case 'in-progress':
            likelihoodScore = 3;
            break; // Being addressed
          case 'resolved':
            likelihoodScore = 1;
            break; // Least likely to cause issues
          default:
            likelihoodScore = 2;
        }
      }

      return {
        ...conflict,
        impactScore,
        likelihoodScore,
      };
    });
  }, [conflicts]);

  // Create the grid labels
  const impactLabels = ['Negligible', 'Minor', 'Moderate', 'Major', 'Severe'];
  const likelihoodLabels = ['Rare', 'Unlikely', 'Possible', 'Likely', 'Almost Certain'];

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2 text-sm text-muted-foreground bg-blue-50 p-3 rounded-lg border border-blue-200">
        <Info className="h-4 w-4 text-blue-600" />
        <span>
          This matrix shows the risk profile of utility conflicts based on impact severity and
          likelihood of occurrence.
        </span>
      </div>

      {/* Risk Matrix Grid */}
      <div className="border rounded-lg p-6 bg-gradient-to-br from-gray-50 to-white shadow-sm">
        <div className="flex">
          {/* Y-axis label */}
          <div className="flex flex-col justify-center mr-4">
            <div className="transform -rotate-90 origin-center whitespace-nowrap text-sm font-semibold text-gray-700 h-48">
              Likelihood of Occurrence
            </div>
          </div>

          {/* Matrix Grid */}
          <div className="flex-1">
            <div className="grid grid-cols-5 gap-2">
              {/* Empty cell for top-left corner */}
              <div className="h-10"></div>

              {/* Impact labels on top */}
              {impactLabels.map((label, i) => (
                <div
                  key={`impact-${i}`}
                  className="h-10 flex items-center justify-center text-sm font-semibold text-gray-700 overflow-hidden"
                >
                  {label}
                </div>
              ))}

              {/* Generate the 5x5 grid */}
              {Array.from({ length: 5 }).map((_, row) => {
                // Reverse row index to have 5 at the top and 1 at the bottom
                const likelihoodIdx = 4 - row;

                return (
                  <React.Fragment key={`row-${row}`}>
                    {/* Likelihood label on the left */}
                    <div className="flex items-center text-sm font-semibold text-gray-700 h-20 pr-2">
                      <div className="text-right leading-tight">
                        {likelihoodLabels[likelihoodIdx]}
                      </div>
                    </div>

                    {/* Grid cells for this row */}
                    {Array.from({ length: 5 }).map((_, col) => {
                      // Get impact index (col + 1 maps to impact score 1-5)
                      const impactIdx = col;
                      const impactScore = impactIdx + 1;
                      const likelihoodScore = likelihoodIdx + 1;
                      const riskScore = impactScore * likelihoodScore;

                      // Determine cell color based on risk score
                      let bgColor = 'bg-muted border-border';
                      let textColor = 'text-muted-foreground';

                      if (riskScore >= 20) {
                        bgColor = 'bg-red-600 border-red-700';
                        textColor = 'text-white';
                      } else if (riskScore >= 15) {
                        bgColor = 'bg-red-400 border-red-500';
                        textColor = 'text-white';
                      } else if (riskScore >= 10) {
                        bgColor = 'bg-orange-400 border-orange-500';
                        textColor = 'text-white';
                      } else if (riskScore >= 6) {
                        bgColor = 'bg-yellow-300 border-yellow-400';
                        textColor = 'text-gray-800';
                      } else if (riskScore >= 3) {
                        bgColor = 'bg-green-300 border-green-400';
                        textColor = 'text-gray-800';
                      }

                      // Find conflicts that fall in this cell
                      const cellConflicts = processedConflicts.filter(
                        (c) =>
                          Math.round(c.impactScore || 0) === impactScore &&
                          Math.round(c.likelihoodScore || 0) === likelihoodScore
                      );

                      return (
                        <div
                          key={`cell-${row}-${col}`}
                          className={`h-20 rounded-lg border-2 relative flex flex-col items-center justify-center ${bgColor} ${textColor} transition-all duration-200 hover:scale-105 hover:shadow-md cursor-pointer group`}
                          onClick={() => {
                            if (cellConflicts.length === 1 && onConflictSelect && cellConflicts[0]) {
                              onConflictSelect(cellConflicts[0]);
                            }
                          }}
                        >
                          {cellConflicts.length > 0 ? (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="w-full h-full flex flex-col items-center justify-center">
                                    <div className="text-xl font-bold mb-1">
                                      {cellConflicts.length}
                                    </div>
                                    <div className="text-xs font-medium opacity-90">
                                      {cellConflicts.length === 1 ? 'Conflict' : 'Conflicts'}
                                    </div>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-sm">
                                  <ScrollArea className="max-h-60">
                                    <div className="p-2 space-y-3">
                                      <div className="font-semibold text-sm border-b pb-1">
                                        Risk Score: {riskScore} ({cellConflicts.length} conflicts)
                                      </div>
                                      {cellConflicts.map((c: any) => (
                                        <div
                                          key={c.id}
                                          className="space-y-1 cursor-pointer hover:bg-muted p-2 rounded"
                                        >
                                          <div className="text-sm font-medium">
                                            {c.utility.name}
                                          </div>
                                          <div className="text-xs text-muted-foreground">
                                            {c.description}
                                          </div>
                                          <div className="flex gap-2">
                                            <Badge
                                              className={(priorityColors as any)[c.priority] || 'bg-gray-100'}
                                              variant="secondary"
                                            >
                                              {c.priority}
                                            </Badge>
                                            <Badge
                                              className={(statusColors as any)[c.status] || 'bg-gray-100'}
                                              variant="secondary"
                                            >
                                              {c.status}
                                            </Badge>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </ScrollArea>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          ) : (
                            <div className="text-sm font-medium opacity-60">{riskScore}</div>
                          )}
                        </div>
                      );
                    })}
                  </React.Fragment>
                );
              })}
            </div>

            {/* X-axis label */}
            <div className="text-center mt-4 text-sm font-semibold text-gray-700">
              Impact Severity
            </div>
          </div>
        </div>
      </div>

      {/* Risk Legend */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-muted rounded-lg border">
        <div className="text-center">
          <div className="w-full h-8 bg-red-600 rounded mb-2"></div>
          <div className="text-sm font-medium">Extreme Risk</div>
          <div className="text-xs text-muted-foreground">20-25</div>
        </div>
        <div className="text-center">
          <div className="w-full h-8 bg-red-400 rounded mb-2"></div>
          <div className="text-sm font-medium">High Risk</div>
          <div className="text-xs text-muted-foreground">15-19</div>
        </div>
        <div className="text-center">
          <div className="w-full h-8 bg-orange-400 rounded mb-2"></div>
          <div className="text-sm font-medium">Medium Risk</div>
          <div className="text-xs text-muted-foreground">10-14</div>
        </div>
        <div className="text-center">
          <div className="w-full h-8 bg-yellow-300 rounded mb-2"></div>
          <div className="text-sm font-medium">Low Risk</div>
          <div className="text-xs text-muted-foreground">6-9</div>
        </div>
        <div className="text-center">
          <div className="w-full h-8 bg-green-300 rounded mb-2"></div>
          <div className="text-sm font-medium">Very Low Risk</div>
          <div className="text-xs text-muted-foreground">1-5</div>
        </div>
      </div>
    </div>
  );
}

function SummaryView({
  conflicts,
  utilities,
}: {
  conflicts: Conflict[];
  utilities: UtilityWithConflicts[];
}) {
  // Calculate utility risk scores
  const utilityRiskScores = useMemo(() => {
    const conflictsByUtility = conflicts.reduce(
      (acc, conflict) => {
        const utilityId = conflict.utility?.id;
        if (!acc[utilityId]) {
          acc[utilityId] = [];
        }
        acc[utilityId].push(conflict);
        return acc;
      },
      {} as Record<number, Conflict[]>
    );

    return utilities.map((utility: any) => {
      const utilityConflicts = conflictsByUtility[utility.id] || [];

      let avgImpact = 0;
      let avgLikelihood = 0;
      let maxRiskScore = 0;

      if (utilityConflicts.length > 0) {
        // Calculate scores using priority mapping
        const conflictsWithScores = utilityConflicts.map((c: any) => {
          const impactScore =
            c.impactScore ||
            (c.priority === 'critical'
              ? 5
              : c.priority === 'high'
                ? 4
                : c.priority === 'medium'
                  ? 3
                  : c.priority === 'low'
                    ? 2
                    : 1);

          const likelihoodScore =
            c.likelihoodScore ||
            (c.status === 'open'
              ? 5
              : c.status === 'in-progress'
                ? 3
                : c.status === 'resolved'
                  ? 1
                  : 2);

          return { ...c, impactScore, likelihoodScore };
        });

        avgImpact =
          conflictsWithScores.reduce((sum: any, c: any) => sum + c.impactScore, 0) /
          conflictsWithScores.length;
        avgLikelihood =
          conflictsWithScores.reduce((sum: any, c: any) => sum + c.likelihoodScore, 0) /
          conflictsWithScores.length;
        maxRiskScore = Math.max(
          ...conflictsWithScores.map((c: any) => c.impactScore * c.likelihoodScore)
        );
      }

      return {
        ...utility,
        avgImpact,
        avgLikelihood,
        maxRiskScore,
        utilityConflicts,
      };
    });
  }, [utilities, conflicts]);

  // Sort by risk score descending
  const sortedUtilities = [...utilityRiskScores].sort((a: any, b: any) => b.maxRiskScore - a.maxRiskScore);

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-red-50 p-4 rounded-lg border border-red-200">
          <div className="text-2xl font-bold text-red-600">
            {conflicts.filter((c: any) => c.priority === 'critical').length}
          </div>
          <div className="text-sm text-red-600 font-medium">Critical Conflicts</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <div className="text-2xl font-bold text-orange-600">
            {conflicts.filter((c: any) => c.priority === 'high').length}
          </div>
          <div className="text-sm text-orange-600 font-medium">High Priority</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <div className="text-2xl font-bold text-yellow-600">
            {conflicts.filter((c: any) => c.status === 'open').length}
          </div>
          <div className="text-sm text-yellow-600 font-medium">Open Conflicts</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="text-2xl font-bold text-green-600">
            {Math.round(
              (conflicts.filter((c: any) => c.status === 'resolved').length / conflicts.length) * 100
            )}
            %
          </div>
          <div className="text-sm text-green-600 font-medium">Resolution Rate</div>
        </div>
      </div>

      {/* Utility Risk Ranking */}
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-muted px-4 py-3 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Utility Risk Assessment</h3>
          <p className="text-sm text-muted-foreground">Utilities ranked by maximum risk score</p>
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Utility</TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="text-center">Conflicts</TableHead>
              <TableHead className="text-center">Max Risk Score</TableHead>
              <TableHead className="text-center">Avg Impact</TableHead>
              <TableHead className="text-center">Avg Likelihood</TableHead>
              <TableHead className="text-center">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedUtilities.map((utility: any) => (
              <TableRow
                key={utility.id}
                className={
                  utility.maxRiskScore >= 15
                    ? 'bg-red-50'
                    : utility.maxRiskScore >= 10
                      ? 'bg-orange-50'
                      : ''
                }
              >
                <TableCell className="font-medium">{utility.name}</TableCell>
                <TableCell>
                  <Badge variant="outline">{utility.type}</Badge>
                </TableCell>
                <TableCell className="text-center">
                  <Badge variant="secondary">{utility.conflictCount}</Badge>
                </TableCell>
                <TableCell className="text-center">
                  {utility.conflictCount > 0 ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            variant="outline"
                            className={`
                              ${
                                utility.maxRiskScore >= 15
                                  ? 'bg-red-100 text-red-800 border-red-200'
                                  : utility.maxRiskScore >= 10
                                    ? 'bg-orange-100 text-orange-800 border-orange-200'
                                    : utility.maxRiskScore >= 6
                                      ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                      : 'bg-green-100 text-green-800 border-green-200'
                              }
                            `}
                          >
                            {utility.maxRiskScore.toFixed(1)}
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-sm">Highest risk conflict for this utility</div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    <span className="text-muted-foreground">N/A</span>
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {utility.conflictCount > 0 ? utility.avgImpact.toFixed(1) : '—'}
                </TableCell>
                <TableCell className="text-center">
                  {utility.conflictCount > 0 ? utility.avgLikelihood.toFixed(1) : '—'}
                </TableCell>
                <TableCell className="text-center">
                  {utility.conflictCount > 0 ? (
                    <div className="flex justify-center gap-1">
                      {utility.utilityConflicts?.some((c: any) => c.status === 'open') && (
                        <Badge variant="destructive" className="text-xs">
                          Open
                        </Badge>
                      )}
                      {utility.utilityConflicts?.some((c: any) => c.status === 'in-progress') && (
                        <Badge
                          variant="secondary"
                          className="bg-yellow-100 text-yellow-800 text-xs"
                        >
                          In Progress
                        </Badge>
                      )}
                      {utility.utilityConflicts?.every((c: any) => c.status === 'resolved') && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                          All Resolved
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <Check className="h-5 w-5 text-green-500 mx-auto" />
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

function ListView({
  conflicts,
  onConflictSelect,
}: {
  conflicts: Conflict[];
  onConflictSelect?: (conflict: Conflict) => void;
}) {
  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Utility</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Risk Score</TableHead>
            <TableHead>Resolution</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {conflicts.map((conflict: any) => {
            const riskScore =
              conflict.riskScore ||
              (conflict.priority === 'critical'
                ? 5
                : conflict.priority === 'high'
                  ? 4
                  : conflict.priority === 'medium'
                    ? 3
                    : 2) *
                (conflict.status === 'open' ? 5 : conflict.status === 'in-progress' ? 3 : 1);

            // Context menu for each conflict row
            const ConflictRow = () => {
              const conflictRef = useContextMenuRef<HTMLTableRowElement>({
                entityType: 'conflict',
                entityId: conflict.id.toString(),
                entityName: conflict.description,
                data: {
                  text: `CF-${conflict.id.toString().padStart(3, '0')}: ${conflict.description}`,
                  onView: () => onConflictSelect?.(conflict),
                  onEdit: () => {
                    // TODO: Implement edit functionality
                    console.log('Edit conflict:', conflict.id);
                  },
                  onShare: () => {
                    navigator.clipboard.writeText(`Conflict CF-${conflict.id.toString().padStart(3, '0')}: ${conflict.description} - Priority: ${conflict.priority}, Status: ${conflict.status}`);
                  },
                },
                securityLevel: 'low',
              });

              return (
                <TableRow
                  ref={conflictRef}
                  key={conflict.id}
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => onConflictSelect?.(conflict)}
                >
                <TableCell>
                  <div className="font-medium">{conflict.utility?.name}</div>
                  <div className="text-sm text-muted-foreground">{conflict.utility?.type}</div>
                </TableCell>
                <TableCell className="max-w-xs">
                  <div className="flex items-center gap-2">
                    <div className="truncate" title={conflict.description}>
                      {conflict.description}
                    </div>
                    <CommentBadge
                      entityType="conflict"
                      entityId={conflict.id.toString()}
                      entityName={conflict.description}
                      variant="icon-only"
                      showZero={false}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={(priorityColors as any)[conflict.priority] || 'bg-gray-100'}>{conflict.priority}</Badge>
                </TableCell>
                <TableCell>
                  <Badge className={(statusColors as any)[conflict.status] || 'bg-gray-100'}>
                    {conflict.status.replace('-', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={
                      riskScore >= 15
                        ? 'bg-red-100 text-red-800 border-red-200'
                        : riskScore >= 10
                          ? 'bg-orange-100 text-orange-800 border-orange-200'
                          : riskScore >= 6
                            ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                            : 'bg-green-100 text-green-800 border-green-200'
                    }
                  >
                    {riskScore}
                  </Badge>
                </TableCell>
                <TableCell className="max-w-xs">
                  <div className="truncate text-sm text-muted-foreground">
                    {conflict.resolutionNotes || 'No resolution notes'}
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e: any) => {
                      e.stopPropagation();
                      onConflictSelect?.(conflict);
                    }}
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
              );
            };

            return <ConflictRow />;
          })}
        </TableBody>
      </Table>
    </div>
  );
}
