'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '~/components/ui/alert-dialog';
import { Textarea } from '~/components/ui/textarea';
import { Checkbox } from '~/components/ui/checkbox';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import {
  Upload,
  Download,
  FileText,
  Database,
  CheckCircle,
  AlertTriangle,
  Clock,
  RefreshCw,
  Settings,
  Filter,
  Search,
  Eye,
  Edit,
  Trash2,
  MapPin,
  FileSpreadsheet,
  FileJson,
  FileImage,
  Globe,
  Layers,
  Target,
  Play,
  Pause,
  Square,
  RotateCcw,
  Info,
  AlertCircle,
} from 'lucide-react';

interface ImportJob {
  id: string;
  name: string;
  type: 'shapefile' | 'csv' | 'excel' | 'geojson' | 'kml' | 'cad' | 'database';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  totalRecords: number;
  processedRecords: number;
  errorCount: number;
  warnings: number;
  startTime: string;
  endTime?: string;
  fileName: string;
  fileSize: string;
  targetTable: string;
  mapping: FieldMapping[];
  validationRules: ValidationRule[];
  createdBy: string;
  logs: ImportLog[];
}

interface ExportJob {
  id: string;
  name: string;
  type: 'shapefile' | 'csv' | 'excel' | 'geojson' | 'kml' | 'pdf' | 'database';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  totalRecords: number;
  processedRecords: number;
  startTime: string;
  endTime?: string;
  query: string;
  filters: ExportFilter[];
  format: ExportFormat;
  downloadUrl?: string;
  createdBy: string;
  expiresAt: string;
}

interface FieldMapping {
  sourceField: string;
  targetField: string;
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'geometry';
  required: boolean;
  defaultValue?: string;
  transformation?: string;
}

interface ValidationRule {
  field: string;
  rule: 'required' | 'unique' | 'format' | 'range' | 'custom';
  parameters: Record<string, any>;
  message: string;
}

interface ImportLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: Record<string, any>;
}

interface ExportFilter {
  field: string;
  operator: 'equals' | 'contains' | 'greater' | 'less' | 'between' | 'in';
  value: any;
}

interface ExportFormat {
  includeGeometry: boolean;
  coordinateSystem: string;
  encoding: string;
  dateFormat: string;
  includeHeaders: boolean;
  customFields?: string[];
}

export default function UtilityDataImportExport() {
  const [activeTab, setActiveTab] = useState('import');
  const [importJobs, setImportJobs] = useState<ImportJob[]>([
    {
      id: '1',
      name: 'Electric Utility Lines Import',
      type: 'shapefile',
      status: 'completed',
      progress: 100,
      totalRecords: 2847,
      processedRecords: 2847,
      errorCount: 0,
      warnings: 12,
      startTime: '2024-01-15T09:00:00Z',
      endTime: '2024-01-15T09:15:32Z',
      fileName: 'electric_lines.shp',
      fileSize: '15.2 MB',
      targetTable: 'utility_lines',
      mapping: [
        {
          sourceField: 'LINE_TYPE',
          targetField: 'line_type',
          dataType: 'string',
          required: true,
        },
        {
          sourceField: 'VOLTAGE',
          targetField: 'voltage',
          dataType: 'number',
          required: false,
        },
      ],
      validationRules: [
        {
          field: 'voltage',
          rule: 'range',
          parameters: { min: 0, max: 500000 },
          message: 'Voltage must be between 0 and 500,000V',
        },
      ],
      createdBy: 'John Smith',
      logs: [
        {
          id: 'l1',
          timestamp: '2024-01-15T09:05:00Z',
          level: 'info',
          message: 'Processing started',
        },
        {
          id: 'l2',
          timestamp: '2024-01-15T09:10:00Z',
          level: 'warning',
          message: '12 records have missing voltage values',
        },
      ],
    },
  ]);

  const [exportJobs, setExportJobs] = useState<ExportJob[]>([
    {
      id: '1',
      name: 'Gas Utilities Report',
      type: 'excel',
      status: 'completed',
      progress: 100,
      totalRecords: 1234,
      processedRecords: 1234,
      startTime: '2024-01-15T10:00:00Z',
      endTime: '2024-01-15T10:08:45Z',
      query: 'SELECT * FROM utilities WHERE type = "gas"',
      filters: [
        {
          field: 'type',
          operator: 'equals',
          value: 'gas',
        },
      ],
      format: {
        includeGeometry: true,
        coordinateSystem: 'EPSG:4326',
        encoding: 'UTF-8',
        dateFormat: 'YYYY-MM-DD',
        includeHeaders: true,
      },
      downloadUrl: '/downloads/gas-utilities-report.xlsx',
      createdBy: 'Sarah Johnson',
      expiresAt: '2024-01-22T10:08:45Z',
    },
  ]);

  const [selectedImportJob, setSelectedImportJob] = useState<ImportJob | null>(null);
  const [selectedExportJob, setSelectedExportJob] = useState<ExportJob | null>(null);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'cancelled':
        return 'bg-muted text-gray-800 border-gray-300';
      default:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'shapefile':
        return <MapPin className="h-4 w-4 text-blue-600" />;
      case 'csv':
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4 text-green-600" />;
      case 'geojson':
      case 'kml':
        return <FileJson className="h-4 w-4 text-purple-600" />;
      case 'cad':
        return <FileImage className="h-4 w-4 text-orange-600" />;
      case 'database':
        return <Database className="h-4 w-4 text-muted-foreground" />;
      default:
        return <FileText className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const filteredImportJobs = importJobs.filter((job: any) => {
    const matchesSearch =
      job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.fileName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredExportJobs = exportJobs.filter((job: any) => {
    const matchesSearch = job.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const startImportJob = (jobId: string) => {
    setImportJobs((prev) =>
      prev.map((job: any) =>
        job.id === jobId ? { ...job, status: 'processing' as const, progress: 0 } : job
      )
    );
  };

  const cancelJob = (jobId: string) => {
    setImportJobs((prev) =>
      prev.map((job: any) => (job.id === jobId ? { ...job, status: 'cancelled' as const } : job))
    );
  };

  const retryJob = (jobId: string) => {
    setImportJobs((prev) =>
      prev.map((job: any) =>
        job.id === jobId ? { ...job, status: 'pending' as const, progress: 0, errorCount: 0 } : job
      )
    );
  };

  const downloadExport = (job: ExportJob) => {
    if (job.downloadUrl) {
      window.open(job.downloadUrl, '_blank');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Utility Data Import/Export</h2>
          <p className="text-muted-foreground">Import and export utility data in various formats</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowImportDialog(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Import Data
          </Button>
          <Button variant="outline" onClick={() => setShowExportDialog(true)}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Imports</p>
                <p className="text-2xl font-bold">{importJobs.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Download className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Exports</p>
                <p className="text-2xl font-bold">{exportJobs.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Processing</p>
                <p className="text-2xl font-bold">
                  {
                    [...importJobs, ...exportJobs].filter((job: any) => job.status === 'processing')
                      .length
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Records Processed</p>
                <p className="text-2xl font-bold">
                  {importJobs.reduce((acc: any, job: any) => acc + job.processedRecords, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                <Input
                  placeholder="Search jobs..."
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Jobs List */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="import">Import Jobs</TabsTrigger>
          <TabsTrigger value="export">Export Jobs</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Import Jobs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredImportJobs.map((job: any) => (
                  <Card key={job.id} className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center gap-3">
                          {getFileTypeIcon(job.type)}
                          <div>
                            <h3 className="font-semibold">{job.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {job.fileName} • {job.fileSize}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(job.status)}
                          <Badge className={getStatusColor(job.status)}>{job.status}</Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Progress</p>
                          <Progress value={job.progress} className="mt-1" />
                          <p className="text-xs text-muted-foreground mt-1">{job.progress}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Records</p>
                          <p className="font-medium">
                            {job.processedRecords.toLocaleString()} /{' '}
                            {job.totalRecords.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Errors</p>
                          <p
                            className={`font-medium ${job.errorCount > 0 ? 'text-red-600' : 'text-green-600'}`}
                          >
                            {job.errorCount}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Warnings</p>
                          <p
                            className={`font-medium ${job.warnings > 0 ? 'text-orange-600' : 'text-green-600'}`}
                          >
                            {job.warnings}
                          </p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="text-sm text-muted-foreground">
                          <span>Target: {job.targetTable}</span>
                          <span className="ml-4">By: {job.createdBy}</span>
                          <span className="ml-4">
                            Started: {new Date(job.startTime).toLocaleString()}
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedImportJob(job)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                          {job.status === 'pending' && (
                            <Button size="sm" onClick={() => startImportJob(job.id)}>
                              <Play className="h-4 w-4 mr-1" />
                              Start
                            </Button>
                          )}
                          {job.status === 'processing' && (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => cancelJob(job.id)}
                            >
                              <Square className="h-4 w-4 mr-1" />
                              Cancel
                            </Button>
                          )}
                          {job.status === 'failed' && (
                            <Button size="sm" onClick={() => retryJob(job.id)}>
                              <RotateCcw className="h-4 w-4 mr-1" />
                              Retry
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export Jobs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredExportJobs.map((job: any) => (
                  <Card key={job.id} className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center gap-3">
                          {getFileTypeIcon(job.type)}
                          <div>
                            <h3 className="font-semibold">{job.name}</h3>
                            <p className="text-sm text-muted-foreground">{job.type.toUpperCase()} export</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(job.status)}
                          <Badge className={getStatusColor(job.status)}>{job.status}</Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Progress</p>
                          <Progress value={job.progress} className="mt-1" />
                          <p className="text-xs text-muted-foreground mt-1">{job.progress}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Records</p>
                          <p className="font-medium">
                            {job.processedRecords.toLocaleString()} /{' '}
                            {job.totalRecords.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Expires</p>
                          <p className="font-medium">
                            {new Date(job.expiresAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="text-sm text-muted-foreground">
                          <span>By: {job.createdBy}</span>
                          <span className="ml-4">
                            Started: {new Date(job.startTime).toLocaleString()}
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedExportJob(job)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                          {job.status === 'completed' && job.downloadUrl && (
                            <Button size="sm" onClick={() => downloadExport(job)}>
                              <Download className="h-4 w-4 mr-1" />
                              Download
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Import Job Details Dialog */}
      <Dialog open={!!selectedImportJob} onOpenChange={() => setSelectedImportJob(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedImportJob && getFileTypeIcon(selectedImportJob.type)}
              Import Job Details: {selectedImportJob?.name}
            </DialogTitle>
          </DialogHeader>

          {selectedImportJob && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="mapping">Field Mapping</TabsTrigger>
                <TabsTrigger value="validation">Validation</TabsTrigger>
                <TabsTrigger value="logs">Logs</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Job Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <span className="text-muted-foreground">File:</span>
                        <span>{selectedImportJob.fileName}</span>
                        <span className="text-muted-foreground">Size:</span>
                        <span>{selectedImportJob.fileSize}</span>
                        <span className="text-muted-foreground">Type:</span>
                        <span>{selectedImportJob.type}</span>
                        <span className="text-muted-foreground">Target:</span>
                        <span>{selectedImportJob.targetTable}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Progress</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Progress value={selectedImportJob.progress} />
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectedImportJob.progress}% complete
                        </p>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Processed</p>
                          <p className="font-bold text-blue-600">
                            {selectedImportJob.processedRecords.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Errors</p>
                          <p className="font-bold text-red-600">{selectedImportJob.errorCount}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Warnings</p>
                          <p className="font-bold text-orange-600">{selectedImportJob.warnings}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="mapping" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Field Mapping</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Source Field</TableHead>
                          <TableHead>Target Field</TableHead>
                          <TableHead>Data Type</TableHead>
                          <TableHead>Required</TableHead>
                          <TableHead>Default Value</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {selectedImportJob.mapping.map((mapping, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{mapping.sourceField}</TableCell>
                            <TableCell>{mapping.targetField}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{mapping.dataType}</Badge>
                            </TableCell>
                            <TableCell>
                              {mapping.required ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <span className="text-muted-foreground">Optional</span>
                              )}
                            </TableCell>
                            <TableCell>{mapping.defaultValue || '-'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="validation" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Validation Rules</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedImportJob.validationRules.map((rule, index) => (
                        <div key={index} className="border rounded p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">{rule.field}</Badge>
                            <Badge>{rule.rule}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{rule.message}</p>
                          {Object.keys(rule.parameters).length > 0 && (
                            <div className="mt-2 text-xs text-muted-foreground">
                              Parameters: {JSON.stringify(rule.parameters)}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="logs" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Import Logs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {selectedImportJob.logs.map((log: any) => (
                        <div
                          key={log.id}
                          className="flex items-start gap-3 p-2 border-l-2 border-l-gray-200"
                        >
                          <div className="flex items-center gap-2 min-w-0">
                            {log.level === 'error' && (
                              <AlertCircle className="h-4 w-4 text-red-600" />
                            )}
                            {log.level === 'warning' && (
                              <AlertTriangle className="h-4 w-4 text-orange-600" />
                            )}
                            {log.level === 'info' && <Info className="h-4 w-4 text-blue-600" />}
                            <span className="text-xs text-muted-foreground">
                              {new Date(log.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm">{log.message}</p>
                            {log.details && (
                              <pre className="text-xs text-muted-foreground mt-1 overflow-x-auto">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* Export Job Details Dialog */}
      <Dialog open={!!selectedExportJob} onOpenChange={() => setSelectedExportJob(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedExportJob && getFileTypeIcon(selectedExportJob.type)}
              Export Job Details: {selectedExportJob?.name}
            </DialogTitle>
          </DialogHeader>

          {selectedExportJob && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="query">Query & Filters</TabsTrigger>
                <TabsTrigger value="format">Format Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Export Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <span className="text-muted-foreground">Type:</span>
                        <span>{selectedExportJob.type}</span>
                        <span className="text-muted-foreground">Records:</span>
                        <span>{selectedExportJob.totalRecords.toLocaleString()}</span>
                        <span className="text-muted-foreground">Created By:</span>
                        <span>{selectedExportJob.createdBy}</span>
                        <span className="text-muted-foreground">Expires:</span>
                        <span>{new Date(selectedExportJob.expiresAt).toLocaleDateString()}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Progress</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Progress value={selectedExportJob.progress} />
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectedExportJob.progress}% complete
                        </p>
                      </div>
                      {selectedExportJob.downloadUrl && (
                        <Button
                          className="w-full"
                          onClick={() => downloadExport(selectedExportJob)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download File
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="query" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>SQL Query</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                      {selectedExportJob.query}
                    </pre>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Applied Filters</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {selectedExportJob.filters.map((filter, index) => (
                        <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded">
                          <Badge variant="outline">{filter.field}</Badge>
                          <span className="text-sm text-muted-foreground">{filter.operator}</span>
                          <Badge>{filter.value}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="format" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Format Settings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm text-muted-foreground">Include Geometry</Label>
                        <p className="font-medium">
                          {selectedExportJob.format.includeGeometry ? 'Yes' : 'No'}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Coordinate System</Label>
                        <p className="font-medium">{selectedExportJob.format.coordinateSystem}</p>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Encoding</Label>
                        <p className="font-medium">{selectedExportJob.format.encoding}</p>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Date Format</Label>
                        <p className="font-medium">{selectedExportJob.format.dateFormat}</p>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Include Headers</Label>
                        <p className="font-medium">
                          {selectedExportJob.format.includeHeaders ? 'Yes' : 'No'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import Utility Data</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="importFile">Select File</Label>
              <Input id="importFile" type="file" accept=".shp,.csv,.xlsx,.geojson,.kml" />
            </div>
            <div>
              <Label htmlFor="importName">Job Name</Label>
              <Input id="importName" placeholder="Enter import job name" />
            </div>
            <div>
              <Label htmlFor="targetTable">Target Table</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select target table" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="utilities">Utilities</SelectItem>
                  <SelectItem value="utility_lines">Utility Lines</SelectItem>
                  <SelectItem value="utility_points">Utility Points</SelectItem>
                  <SelectItem value="conflicts">Conflicts</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowImportDialog(false)}>Start Import</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Export Utility Data</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="exportName">Export Name</Label>
              <Input id="exportName" placeholder="Enter export job name" />
            </div>
            <div>
              <Label htmlFor="exportFormat">Format</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select export format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="shapefile">Shapefile</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="geojson">GeoJSON</SelectItem>
                  <SelectItem value="kml">KML</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="exportQuery">Data Selection</Label>
              <Textarea id="exportQuery" placeholder="SELECT * FROM utilities WHERE..." rows={3} />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox id="includeGeometry" />
                <Label htmlFor="includeGeometry">Include Geometry</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="includeHeaders" />
                <Label htmlFor="includeHeaders">Include Headers</Label>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowExportDialog(false)}>Start Export</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
