"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Calendar } from "~/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { cn } from "~/lib/utils";
import { api } from "~/trpc/react";
import { toast } from "~/hooks/use-toast";
import type { RouterOutputs } from "~/trpc/react";

interface CreateSubtaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  parentTask: {
    id: string;
    title: string;
    project_id?: string;
  };
  onSuccess?: () => void;
}

interface SubtaskFormData {
  title: string;
  description: string;
  utility_company: string;
  utility_contact: string;
  utility_email: string;
  utility_phone: string;
  target_start_date?: Date;
  target_end_date?: Date;
  duration?: number;
  priority: "Low" | "Medium" | "High";
  status: "Not Started" | "In Progress" | "Completed" | "On Hold";
  assigned_to: string;
}

const initialFormData: SubtaskFormData = {
  title: "",
  description: "",
  utility_company: "",
  utility_contact: "",
  utility_email: "",
  utility_phone: "",
  priority: "Medium",
  status: "Not Started",
  assigned_to: "",
};

export function CreateSubtaskDialog({ 
  open, 
  onOpenChange, 
  parentTask, 
  onSuccess 
}: CreateSubtaskDialogProps) {
  const [formData, setFormData] = useState<SubtaskFormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<SubtaskFormData>>({});

  const createSubtaskMutation = api.subtasks.create.useMutation({
    onSuccess: () => {
      toast({
        title: "Subtask created",
        description: `Subtask "${formData.title}" has been created successfully.`,
      });
      setFormData(initialFormData);
      setErrors({});
      onOpenChange(false);
      onSuccess?.();
    },
    onError: (error) => {
      toast({
        title: "Error creating subtask",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Partial<SubtaskFormData> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.utility_company.trim()) {
      newErrors.utility_company = "Utility company is required";
    }

    if (formData.utility_email && !formData.utility_email.includes("@")) {
      newErrors.utility_email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    createSubtaskMutation.mutate({
      parent_task_id: parentTask.id,
      project_id: parentTask.project_id || null,
      title: formData.title,
      description: formData.description || null,
      utility_company: formData.utility_company || null,
      utility_contact: formData.utility_contact || null,
      utility_email: formData.utility_email || null,
      utility_phone: formData.utility_phone || null,
      target_start_date: formData.target_start_date || null,
      target_end_date: formData.target_end_date || null,
      duration: formData.duration || null,
      priority: formData.priority,
      status: formData.status,
      assigned_to: formData.assigned_to || null,
    });
  };

  const updateField = <K extends keyof SubtaskFormData>(
    field: K,
    value: SubtaskFormData[K]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Subtask</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Creating subtask for: <span className="font-medium">{parentTask.title}</span>
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Basic Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <Label htmlFor="title">
                  Title <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => updateField("title", e.target.value)}
                  placeholder="Subtask title"
                  className={errors.title ? "border-red-500" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">{errors.title}</p>
                )}
              </div>

              <div className="col-span-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateField("description", e.target.value)}
                  placeholder="Task description"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value: any) => updateField("priority", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => updateField("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Not Started">Not Started</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="On Hold">On Hold</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-2">
                <Label htmlFor="assigned_to">Assigned To</Label>
                <Input
                  id="assigned_to"
                  value={formData.assigned_to}
                  onChange={(e) => updateField("assigned_to", e.target.value)}
                  placeholder="Person responsible"
                />
              </div>
            </div>
          </div>

          {/* Utility Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Utility Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="utility_company">
                  Utility Company <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="utility_company"
                  value={formData.utility_company}
                  onChange={(e) => updateField("utility_company", e.target.value)}
                  placeholder="Company name"
                  className={errors.utility_company ? "border-red-500" : ""}
                />
                {errors.utility_company && (
                  <p className="text-sm text-red-500 mt-1">{errors.utility_company}</p>
                )}
              </div>

              <div>
                <Label htmlFor="utility_contact">Contact Person</Label>
                <Input
                  id="utility_contact"
                  value={formData.utility_contact}
                  onChange={(e) => updateField("utility_contact", e.target.value)}
                  placeholder="Contact name"
                />
              </div>

              <div>
                <Label htmlFor="utility_email">Email</Label>
                <Input
                  id="utility_email"
                  type="email"
                  value={formData.utility_email}
                  onChange={(e) => updateField("utility_email", e.target.value)}
                  placeholder="<EMAIL>"
                  className={errors.utility_email ? "border-red-500" : ""}
                />
                {errors.utility_email && (
                  <p className="text-sm text-red-500 mt-1">{errors.utility_email}</p>
                )}
              </div>

              <div>
                <Label htmlFor="utility_phone">Phone</Label>
                <Input
                  id="utility_phone"
                  value={formData.utility_phone}
                  onChange={(e) => updateField("utility_phone", e.target.value)}
                  placeholder="(*************"
                />
              </div>
            </div>
          </div>

          {/* Scheduling */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Scheduling</h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label>Target Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.target_start_date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.target_start_date ? (
                        format(formData.target_start_date, "PPP")
                      ) : (
                        <span>Pick date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.target_start_date}
                      onSelect={(date) => updateField("target_start_date", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label>Target End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.target_end_date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.target_end_date ? (
                        format(formData.target_end_date, "PPP")
                      ) : (
                        <span>Pick date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.target_end_date}
                      onSelect={(date) => updateField("target_end_date", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label htmlFor="duration">Duration (days)</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  value={formData.duration || ""}
                  onChange={(e) => updateField("duration", e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="Days"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={createSubtaskMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createSubtaskMutation.isPending}
            >
              {createSubtaskMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Create Subtask
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}