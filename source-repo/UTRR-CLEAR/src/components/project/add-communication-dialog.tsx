'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '~/components/ui/dialog';
import { Calendar } from '~/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command';
import { Badge } from '~/components/ui/badge';
import { Checkbox } from '~/components/ui/checkbox';
import { useToast } from '~/hooks/use-toast';
import { formatDate, cn } from '~/lib/utils';
import { safeLog } from '~/lib/error-handler';
import {
  CalendarIcon,
  Phone,
  Mail,
  Users,
  FileText,
  Clock,
  User,
  Building,
  MessageSquare,
  Plus,
  X,
  Check,
  ChevronDown,
} from 'lucide-react';

// Communication types and their configurations
const COMMUNICATION_TYPES = {
  email: {
    icon: Mail,
    label: 'Email',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    fields: ['recipient', 'subject', 'message', 'attachments'],
  },
  phone: {
    icon: Phone,
    label: 'Phone Call',
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    fields: ['recipient', 'duration', 'summary'],
  },
  meeting: {
    icon: Users,
    label: 'Meeting',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    fields: ['attendees', 'location', 'agenda', 'minutes'],
  },
  document: {
    icon: FileText,
    label: 'Document',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    fields: ['recipient', 'document_type', 'description'],
  },
};

// Stakeholder interface
interface Stakeholder {
  id: string;
  name: string;
  company: string;
  email?: string;
  phone?: string;
  role: string;
  type: 'utility' | 'contractor' | 'owner' | 'regulatory' | 'internal';
}

interface AddCommunicationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  onCommunicationAdded?: (communication: any) => void;
  preselectedType?: keyof typeof COMMUNICATION_TYPES;
  preselectedStakeholder?: string;
}

export function AddCommunicationDialog({
  open,
  onOpenChange,
  projectId,
  onCommunicationAdded,
  preselectedType,
  preselectedStakeholder,
}: AddCommunicationDialogProps) {
  const [communicationType, setCommunicationType] = useState<keyof typeof COMMUNICATION_TYPES>(
    preselectedType || 'email'
  );
  const [selectedStakeholders, setSelectedStakeholders] = useState<Stakeholder[]>([]);
  const [stakeholderSearchOpen, setStakeholderSearchOpen] = useState(false);
  const [stakeholderSearch, setStakeholderSearch] = useState('');
  const [date, setDate] = useState<Date>(new Date());
  const [showCalendar, setShowCalendar] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
    summary: '',
    location: '',
    agenda: '',
    minutes: '',
    duration: '',
    document_type: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    followUpRequired: false,
    followUpDate: null as Date | null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Mock stakeholders data - would be replaced with tRPC query
  const mockStakeholders: Stakeholder[] = [
    {
      id: '1',
      name: 'John Smith',
      company: 'Duke Energy',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'Project Coordinator',
      type: 'utility',
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      company: 'Citizens Gas',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'Engineering Manager',
      type: 'utility',
    },
    {
      id: '3',
      name: 'Mike Chen',
      company: 'AT&T',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'Field Supervisor',
      type: 'utility',
    },
    {
      id: '4',
      name: 'Lisa Rodriguez',
      company: 'INDOT',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'District Engineer',
      type: 'owner',
    },
    {
      id: '5',
      name: 'David Kim',
      company: 'ABC Construction',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'Project Manager',
      type: 'contractor',
    },
  ];

  // Filter stakeholders based on search
  const filteredStakeholders = mockStakeholders.filter(
    (stakeholder) =>
      stakeholder.name.toLowerCase().includes(stakeholderSearch.toLowerCase()) ||
      stakeholder.company.toLowerCase().includes(stakeholderSearch.toLowerCase()) ||
      stakeholder.role.toLowerCase().includes(stakeholderSearch.toLowerCase())
  );

  // Handle form input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Handle stakeholder selection
  const handleStakeholderSelect = (stakeholder: Stakeholder) => {
    if (!selectedStakeholders.find((s: any) => s.id === stakeholder.id)) {
      setSelectedStakeholders((prev) => [...prev, stakeholder]);
    }
    setStakeholderSearch('');
    setStakeholderSearchOpen(false);
  };

  // Remove stakeholder
  const removeStakeholder = (stakeholderId: string) => {
    setSelectedStakeholders((prev) => prev.filter((s: any) => s.id !== stakeholderId));
  };

  // Reset form
  const resetForm = () => {
    setCommunicationType(preselectedType || 'email');
    setSelectedStakeholders([]);
    setDate(new Date());
    setFormData({
      subject: '',
      message: '',
      summary: '',
      location: '',
      agenda: '',
      minutes: '',
      duration: '',
      document_type: '',
      description: '',
      priority: 'medium',
      followUpRequired: false,
      followUpDate: null,
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (selectedStakeholders.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please select at least one stakeholder.',
        variant: 'destructive',
      });
      return;
    }

    const requiredFields = COMMUNICATION_TYPES[communicationType].fields;
    const missingFields = requiredFields.filter((field: any) => {
      if (field === 'recipient' || field === 'attendees') return false; // Handled by stakeholder selection
      return !formData[field as keyof typeof formData];
    });

    if (missingFields.length > 0 && communicationType !== 'phone') {
      toast({
        title: 'Validation Error',
        description: `Please fill in all required fields: ${missingFields.join(', ')}`,
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const communicationData = {
        projectId,
        type: communicationType,
        stakeholders: selectedStakeholders,
        date: date.toISOString(),
        ...formData,
        createdAt: new Date().toISOString(),
      };

      // This would be replaced with tRPC mutation
      safeLog.info('Creating communication:', communicationData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: 'Communication Added',
        description: `${COMMUNICATION_TYPES[communicationType].label} has been logged successfully.`,
      });

      if (onCommunicationAdded) {
        onCommunicationAdded(communicationData);
      }

      resetForm();
      onOpenChange(false);
    } catch (error) {
      safeLog.error('Error creating communication:', { error: String(error) });
      toast({
        title: 'Error',
        description: 'Failed to log communication. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get type configuration
  const typeConfig = COMMUNICATION_TYPES[communicationType];
  const Icon = typeConfig.icon;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon className={`h-5 w-5 ${typeConfig.color}`} />
            Add Communication Log
          </DialogTitle>
          <DialogDescription>
            Record a communication entry for project tracking and coordination.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Communication Type Selection */}
          <div>
            <Label>Communication Type</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {Object.entries(COMMUNICATION_TYPES).map(([type, config]) => {
                const TypeIcon = config.icon;
                return (
                  <Button
                    key={type}
                    variant={communicationType === type ? 'default' : 'outline'}
                    onClick={() => setCommunicationType(type as keyof typeof COMMUNICATION_TYPES)}
                    className="justify-start"
                  >
                    <TypeIcon className="h-4 w-4 mr-2" />
                    {config.label}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Stakeholder Selection */}
          <div>
            <Label>{communicationType === 'meeting' ? 'Attendees' : 'Recipients'}</Label>
            <Popover open={stakeholderSearchOpen} onOpenChange={setStakeholderSearchOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={stakeholderSearchOpen}
                  className="w-full justify-between mt-2"
                >
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {selectedStakeholders.length > 0
                        ? `${selectedStakeholders.length} selected`
                        : 'Select stakeholders...'}
                    </span>
                  </div>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
                <Command className="w-full">
                  <CommandInput
                    placeholder="Search stakeholders..."
                    value={stakeholderSearch}
                    onValueChange={setStakeholderSearch}
                  />
                  <CommandList>
                    <CommandEmpty>No stakeholders found.</CommandEmpty>
                    <CommandGroup className="max-h-64 overflow-auto">
                      {filteredStakeholders.map((stakeholder: any) => (
                        <CommandItem
                          key={stakeholder.id}
                          value={stakeholder.id}
                          onSelect={() => handleStakeholderSelect(stakeholder)}
                          className="flex items-center justify-between"
                        >
                          <div className="flex flex-col">
                            <span className="font-medium">{stakeholder.name}</span>
                            <span className="text-sm text-muted-foreground">
                              {stakeholder.company} • {stakeholder.role}
                            </span>
                          </div>
                          {selectedStakeholders.find((s: any) => s.id === stakeholder.id) && (
                            <Check className="h-4 w-4 text-green-600" />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>

            {/* Selected Stakeholders */}
            {selectedStakeholders.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedStakeholders.map((stakeholder: any) => (
                  <Badge
                    key={stakeholder.id}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {stakeholder.name}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeStakeholder(stakeholder.id)}
                      className="h-auto p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Date Selection */}
          <div>
            <Label>Date</Label>
            <Popover open={showCalendar} onOpenChange={setShowCalendar}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal mt-2',
                    !date && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? formatDate(date) : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(date) => {
                    setDate(date || new Date());
                    setShowCalendar(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Dynamic Form Fields Based on Communication Type */}
          {communicationType === 'email' && (
            <>
              <div>
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  id="subject"
                  value={formData.subject}
                  onChange={(e: any) => handleInputChange('subject', e.target.value)}
                  placeholder="Email subject"
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="message">Message *</Label>
                <Textarea
                  id="message"
                  value={formData.message}
                  onChange={(e: any) => handleInputChange('message', e.target.value)}
                  placeholder="Email content"
                  rows={4}
                  className="mt-2"
                />
              </div>
            </>
          )}

          {communicationType === 'phone' && (
            <>
              <div>
                <Label htmlFor="duration">Duration</Label>
                <Input
                  id="duration"
                  value={formData.duration}
                  onChange={(e: any) => handleInputChange('duration', e.target.value)}
                  placeholder="e.g., 30 minutes"
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="summary">Call Summary</Label>
                <Textarea
                  id="summary"
                  value={formData.summary}
                  onChange={(e: any) => handleInputChange('summary', e.target.value)}
                  placeholder="Summary of the phone conversation"
                  rows={3}
                  className="mt-2"
                />
              </div>
            </>
          )}

          {communicationType === 'meeting' && (
            <>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e: any) => handleInputChange('location', e.target.value)}
                  placeholder="Meeting location or video link"
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="agenda">Agenda</Label>
                <Textarea
                  id="agenda"
                  value={formData.agenda}
                  onChange={(e: any) => handleInputChange('agenda', e.target.value)}
                  placeholder="Meeting agenda and topics"
                  rows={3}
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="minutes">Meeting Minutes</Label>
                <Textarea
                  id="minutes"
                  value={formData.minutes}
                  onChange={(e: any) => handleInputChange('minutes', e.target.value)}
                  placeholder="Meeting notes and action items"
                  rows={4}
                  className="mt-2"
                />
              </div>
            </>
          )}

          {communicationType === 'document' && (
            <>
              <div>
                <Label htmlFor="document_type">Document Type</Label>
                <Select
                  value={formData.document_type}
                  onValueChange={(value) => handleInputChange('document_type', value)}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select document type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="agreement">Agreement</SelectItem>
                    <SelectItem value="permit">Permit</SelectItem>
                    <SelectItem value="drawing">Drawing</SelectItem>
                    <SelectItem value="specification">Specification</SelectItem>
                    <SelectItem value="report">Report</SelectItem>
                    <SelectItem value="correspondence">Correspondence</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e: any) => handleInputChange('description', e.target.value)}
                  placeholder="Document description and purpose"
                  rows={3}
                  className="mt-2"
                />
              </div>
            </>
          )}

          {/* Priority Selection */}
          <div>
            <Label>Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => handleInputChange('priority', value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Follow-up Options */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="followup"
              checked={formData.followUpRequired}
              onCheckedChange={(checked) => handleInputChange('followUpRequired', checked)}
            />
            <Label htmlFor="followup" className="text-sm">
              Follow-up required
            </Label>
          </div>

          {formData.followUpRequired && (
            <div>
              <Label>Follow-up Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal mt-2',
                      !formData.followUpDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.followUpDate ? (
                      formatDate(formData.followUpDate)
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={formData.followUpDate ?? undefined}
                    onSelect={(date) => handleInputChange('followUpDate', date ?? undefined)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Adding...' : 'Add Communication'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
