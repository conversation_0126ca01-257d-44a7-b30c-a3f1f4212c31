'use client';

import { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Input } from '~/components/ui/input';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  FileText,
  Upload,
  Download,
  Search,
  Filter,
  Plus,
  Eye,
  Trash2,
  Calendar,
  User,
  FileIcon,
  Image,
  File,
} from 'lucide-react';
import { CommentBadge } from '~/components/comments/comment-badge';
import { useContextMenuRef } from '~/hooks/use-context-menu';

interface Document {
  id: string;
  name: string;
  type: 'drawing' | 'specification' | 'agreement' | 'permit' | 'photo' | 'report' | 'other';
  description?: string;
  uploadDate: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  fileSize: string;
  fileType: string;
  version: string;
  status: 'draft' | 'review' | 'approved' | 'archived';
  tags?: string[];
  projectPhase?: string;
  isPublic: boolean;
}

// Mock data - in real app this would come from tRPC/API
const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'I-465 Utility Conflict Analysis',
    type: 'report',
    description: 'Comprehensive analysis of utility conflicts along the I-465 corridor',
    uploadDate: '2024-01-20',
    uploadedBy: {
      id: '1',
      name: 'John Smith',
    },
    fileSize: '2.4 MB',
    fileType: 'PDF',
    version: '1.2',
    status: 'approved',
    tags: ['conflict-analysis', 'utilities', 'report'],
    projectPhase: 'Design',
    isPublic: false,
  },
  {
    id: '2',
    name: 'Duke Energy Relocation Agreement',
    type: 'agreement',
    description: 'Signed agreement for gas line relocation timeline and procedures',
    uploadDate: '2024-01-19',
    uploadedBy: {
      id: '2',
      name: 'Sarah Johnson',
    },
    fileSize: '850 KB',
    fileType: 'PDF',
    version: '1.0',
    status: 'approved',
    tags: ['agreement', 'duke-energy', 'relocation'],
    projectPhase: 'Coordination',
    isPublic: true,
  },
  {
    id: '3',
    name: 'Site Survey Photos - Station 125+00',
    type: 'photo',
    description: 'Field photos showing existing utility infrastructure',
    uploadDate: '2024-01-18',
    uploadedBy: {
      id: '3',
      name: 'Mike Wilson',
    },
    fileSize: '15.7 MB',
    fileType: 'ZIP',
    version: '1.0',
    status: 'review',
    tags: ['photos', 'survey', 'infrastructure'],
    projectPhase: 'Field Work',
    isPublic: false,
  },
  {
    id: '4',
    name: 'Utility Relocation Drawings - Rev A',
    type: 'drawing',
    description: 'Updated utility relocation plans incorporating stakeholder feedback',
    uploadDate: '2024-01-17',
    uploadedBy: {
      id: '1',
      name: 'John Smith',
    },
    fileSize: '5.2 MB',
    fileType: 'DWG',
    version: 'Rev A',
    status: 'draft',
    tags: ['drawings', 'relocation', 'design'],
    projectPhase: 'Design',
    isPublic: false,
  },
];

const typeIcons = {
  drawing: FileIcon,
  specification: FileText,
  agreement: FileText,
  permit: FileText,
  photo: Image,
  report: FileText,
  other: File,
};

const typeColors = {
  drawing: 'bg-blue-500',
  specification: 'bg-green-500',
  agreement: 'bg-purple-500',
  permit: 'bg-orange-500',
  photo: 'bg-pink-500',
  report: 'bg-indigo-500',
  other: 'bg-muted0',
};

const statusColors = {
  draft: 'secondary',
  review: 'default',
  approved: 'default',
  archived: 'outline',
} as const;

export function DocumentsManagement({ projectId }: { projectId?: string }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentForm, setDocumentForm] = useState({
    name: '',
    type: '',
    description: '',
    tags: '',
  });

  // TODO: Replace with real tRPC query
  // const { data: documents, refetch } = api.documents.getByProject.useQuery({ projectId });
  // const uploadMutation = api.documents.upload.useMutation();

  const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB in bytes

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > MAX_FILE_SIZE) {
      toast({
        title: 'File too large',
        description: `File size must be less than 100MB. Current file is ${(file.size / 1024 / 1024).toFixed(1)}MB.`,
        variant: 'destructive',
      });
      return;
    }

    setSelectedFile(file);
    setDocumentForm(prev => ({
      ...prev,
      name: prev.name || file.name.split('.')[0] || '',
    }));
  }, []);

  const handleUpload = useCallback(async () => {
    if (!selectedFile || !documentForm.name || !documentForm.type) {
      toast({
        title: 'Missing information',
        description: 'Please select a file, enter a name, and choose a document type.',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    try {
      // TODO: Implement real file upload
      // const formData = new FormData();
      // formData.append('file', selectedFile);
      // formData.append('projectId', projectId || '');
      // formData.append('name', documentForm.name);
      // formData.append('type', documentForm.type);
      // formData.append('description', documentForm.description);
      // formData.append('tags', documentForm.tags);

      // await uploadMutation.mutateAsync(formData);
      
      // Simulate upload for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'Document uploaded successfully',
        description: `${documentForm.name} has been uploaded to the project.`,
      });

      // Reset form
      setSelectedFile(null);
      setDocumentForm({ name: '', type: '', description: '', tags: '' });
      setIsUploadDialogOpen(false);
      
      // TODO: Refetch documents
      // await refetch();
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload document. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  }, [selectedFile, documentForm, projectId]);

  const filteredDocuments = mockDocuments.filter((doc: any) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.uploadedBy.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || doc.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  const DocumentItem = ({ doc }: { doc: Document }) => {
    const TypeIcon = typeIcons[doc.type];

    // Add context menu to each document
    const docRef = useContextMenuRef<HTMLDivElement>({
      entityType: 'document',
      entityId: doc.id,
      entityName: doc.name,
      data: {
        text: `${doc.name} - ${doc.type} document`,
        onView: () => {
          // TODO: Implement view functionality
          console.log('View document:', doc.id);
        },
        onDownload: () => {
          // TODO: Implement download functionality
          console.log('Download document:', doc.id);
        },
        onEdit: () => {
          // TODO: Implement edit functionality
          console.log('Edit document:', doc.id);
        },
        onShare: () => {
          navigator.clipboard.writeText(`Document: ${doc.name} (${doc.type}) - Uploaded by ${doc.uploadedBy.name} on ${doc.uploadDate}`);
        },
      },
      securityLevel: 'medium', // Documents are typically more sensitive
    });

    return (
      <div 
        ref={docRef}
        className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
      >
        <div className="flex items-start gap-3">
          <div
            className={`w-10 h-10 rounded-lg ${typeColors[doc.type]} flex items-center justify-center`}
          >
            <TypeIcon className="h-5 w-5 text-white" />
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm">{doc.name}</h4>
              <CommentBadge
                entityType="document"
                entityId={doc.id}
                entityName={doc.name}
                variant="icon-only"
                showZero={false}
              />
              <Badge variant={statusColors[doc.status]} className="text-xs">
                {doc.status}
              </Badge>
              {doc.isPublic && (
                <Badge variant="outline" className="text-xs">
                  Public
                </Badge>
              )}
            </div>

            {doc.description && (
              <p className="text-sm text-muted-foreground mb-2">{doc.description}</p>
            )}

            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(doc.uploadDate).toLocaleDateString()}
              </span>
              <span className="flex items-center gap-1">
                <User className="h-3 w-3" />
                {doc.uploadedBy.name}
              </span>
              <span>{doc.fileType}</span>
              <span>{doc.fileSize}</span>
              <span>v{doc.version}</span>
            </div>

            {doc.tags && (
              <div className="flex gap-1 mt-2">
                {doc.tags.map((tag: any) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Document Management</CardTitle>
            <CardDescription>
              Manage project documents, drawings, agreements, and files
            </CardDescription>
          </div>
          <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Upload Document
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Upload New Document</DialogTitle>
                <DialogDescription>Add a new document to the project</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="file">File (Max 100MB)</Label>
                  <Input 
                    id="file" 
                    type="file" 
                    onChange={handleFileSelect}
                    accept=".pdf,.doc,.docx,.dwg,.dxf,.jpg,.jpeg,.png,.gif,.xls,.xlsx,.ppt,.pptx"
                  />
                  {selectedFile && (
                    <div className="text-sm text-muted-foreground">
                      Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(1)}MB)
                    </div>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="doc-type">Document Type</Label>
                  <Select value={documentForm.type} onValueChange={(value) => setDocumentForm(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="drawing">Drawing</SelectItem>
                      <SelectItem value="specification">Specification</SelectItem>
                      <SelectItem value="agreement">Agreement</SelectItem>
                      <SelectItem value="permit">Permit</SelectItem>
                      <SelectItem value="photo">Photo</SelectItem>
                      <SelectItem value="report">Report</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="doc-name">Document Name</Label>
                  <Input 
                    id="doc-name" 
                    placeholder="Enter document name" 
                    value={documentForm.name}
                    onChange={(e) => setDocumentForm(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="doc-description">Description</Label>
                  <Textarea 
                    id="doc-description" 
                    placeholder="Document description..." 
                    value={documentForm.description}
                    onChange={(e) => setDocumentForm(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tags">Tags</Label>
                  <Input 
                    id="tags" 
                    placeholder="tag1, tag2, tag3" 
                    value={documentForm.tags}
                    onChange={(e) => setDocumentForm(prev => ({ ...prev, tags: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsUploadDialogOpen(false)} disabled={isUploading}>
                  Cancel
                </Button>
                <Button onClick={handleUpload} disabled={isUploading || !selectedFile}>
                  {isUploading ? 'Uploading...' : 'Upload Document'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e: any) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Document Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="drawing">Drawings</SelectItem>
              <SelectItem value="specification">Specifications</SelectItem>
              <SelectItem value="agreement">Agreements</SelectItem>
              <SelectItem value="permit">Permits</SelectItem>
              <SelectItem value="photo">Photos</SelectItem>
              <SelectItem value="report">Reports</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="review">Review</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold">{mockDocuments.length}</div>
            <div className="text-xs text-muted-foreground">Total Documents</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {mockDocuments.filter((d: any) => d.status === 'approved').length}
            </div>
            <div className="text-xs text-muted-foreground">Approved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {mockDocuments.filter((d: any) => d.status === 'review').length}
            </div>
            <div className="text-xs text-muted-foreground">In Review</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {mockDocuments.filter((d: any) => d.isPublic).length}
            </div>
            <div className="text-xs text-muted-foreground">Public</div>
          </div>
        </div>

        {/* Document List */}
        <div className="space-y-3">
          {filteredDocuments.map((doc: any) => (
            <DocumentItem key={doc.id} doc={doc} />
          ))}
        </div>

        {filteredDocuments.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No documents found</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
