'use client';

import * as React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { Badge } from '~/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  FileText,
  Users,
  CheckCircle,
  AlertTriangle,
  Calendar,
  DollarSign,
  Wrench,
  MessageSquare,
} from 'lucide-react';

interface ProjectStat {
  name: string;
  value: number;
  total: number;
  percentage: number;
  trend?: number[];
  color?: string;
  icon?: string;
}

interface ProjectStatsCardProps {
  projectId: string;
}

export function ProjectStatsCard({ projectId }: ProjectStatsCardProps) {
  const { data, isLoading } = useQuery<{ stats: ProjectStat[] } | ProjectStat[]>({
    queryKey: [`/api/projects/${projectId}/stats`],
    enabled: !!projectId,
    queryFn: async () => {
      // Mock data for development
      return {
        stats: [
          {
            name: 'Documents',
            value: 12,
            total: 20,
            percentage: 60,
            trend: [40, 45, 50, 55, 60],
            color: 'blue',
            icon: 'FileText',
          },
          {
            name: 'Stakeholders',
            value: 8,
            total: 10,
            percentage: 80,
            trend: [70, 72, 75, 78, 80],
            color: 'green',
            icon: 'Users',
          },
          {
            name: 'Conflicts Resolved',
            value: 5,
            total: 8,
            percentage: 62.5,
            trend: [20, 30, 40, 50, 62.5],
            color: 'yellow',
            icon: 'CheckCircle',
          },
          {
            name: 'Tasks Completed',
            value: 15,
            total: 25,
            percentage: 60,
            trend: [30, 40, 45, 55, 60],
            color: 'purple',
            icon: 'Wrench',
          },
        ],
      };
    },
  });

  // Extract the stats array safely from the response
  const stats: ProjectStat[] = React.useMemo(() => {
    if (!data) return [];

    // If data is an array, use it directly
    if (Array.isArray(data)) return data;

    // If data has a stats property that is an array, use that
    if (data.stats && Array.isArray(data.stats)) return data.stats;

    // Default empty array if no valid data format is found
    return [];
  }, [data]);

  const getIcon = (iconName?: string) => {
    switch (iconName) {
      case 'FileText':
        return <FileText className="h-4 w-4" />;
      case 'Users':
        return <Users className="h-4 w-4" />;
      case 'CheckCircle':
        return <CheckCircle className="h-4 w-4" />;
      case 'AlertTriangle':
        return <AlertTriangle className="h-4 w-4" />;
      case 'Calendar':
        return <Calendar className="h-4 w-4" />;
      case 'DollarSign':
        return <DollarSign className="h-4 w-4" />;
      case 'Wrench':
        return <Wrench className="h-4 w-4" />;
      case 'MessageSquare':
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getColorClass = (color?: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-500';
      case 'green':
        return 'bg-green-500';
      case 'yellow':
        return 'bg-yellow-500';
      case 'purple':
        return 'bg-purple-500';
      case 'red':
        return 'bg-red-500';
      default:
        return 'bg-primary';
    }
  };

  const getTrend = (trend?: number[]) => {
    if (!trend || trend.length < 2) return null;

    const lastValue = trend[trend.length - 1];
    const previousValue = trend[trend.length - 2];
    
    if (lastValue === undefined || previousValue === undefined) {
      return null; // No trend to show
    }
    
    const change = lastValue - previousValue;

    if (change > 0) {
      return (
        <div className="flex items-center text-green-600">
          <TrendingUp className="h-3 w-3 mr-1" />
          <span className="text-xs">+{change.toFixed(1)}%</span>
        </div>
      );
    } else if (change < 0) {
      return (
        <div className="flex items-center text-red-600">
          <TrendingDown className="h-3 w-3 mr-1" />
          <span className="text-xs">{change.toFixed(1)}%</span>
        </div>
      );
    }

    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-4 bg-muted rounded w-48 mb-4"></div>
              <div className="h-4 bg-muted rounded w-32"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (stats.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p>No statistics available for this project.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Statistics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {stats.map((stat, index) => (
            <div key={stat.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`p-2 rounded-lg ${getColorClass(stat.color)} bg-opacity-10`}>
                    <div className={getColorClass(stat.color).replace('bg-', 'text-')}>
                      {getIcon(stat.icon)}
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium">{stat.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {stat.value} of {stat.total}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="font-mono">
                    {stat.percentage.toFixed(0)}%
                  </Badge>
                  {getTrend(stat.trend)}
                </div>
              </div>
              <Progress value={stat.percentage} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
