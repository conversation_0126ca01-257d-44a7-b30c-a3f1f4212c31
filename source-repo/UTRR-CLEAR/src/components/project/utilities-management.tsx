'use client';

import * as React from 'react';
import { useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  AlertTriangle,
  Check,
  CheckCircle2,
  Phone,
  Mail,
  Clock,
  FileText,
  AlertCircle,
  Edit,
  Plus,
  Save,
  Eye,
  Building2,
  Users,
  MapPin,
  Calendar,
  Search,
  Filter,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { useToast } from '~/hooks/use-toast';
import { CommentBadge } from '~/components/comments/comment-badge';

interface UtilityPhase {
  phaseId: number;
  phaseName: string;
  status: 'pending' | 'in-progress' | 'complete';
  responseDate: Date | null;
  notes: string | null;
  location?: string;
}

interface Conflict {
  id: number;
  description: string;
  status: string;
  priority: string;
  location: string | null;
  resolution_notes: string | null;
  created_at: Date;
}

interface Utility {
  id: number;
  name: string;
  type: string;
  status: string;
  contactName?: string | null;
  contactEmail?: string | null;
  contactPhone?: string | null;
  notes?: string | null;
  initiationDate?: Date | string | null;
  lastResponse?: Date | string | null;
  conflicts: Conflict[];
  phaseSummaries: UtilityPhase[];
  agreementStatus?: 'pending' | 'signed' | 'expired';
  utilityOwner?: string;
  emergencyContact?: string;
  serviceArea?: string;
}

interface UtilitiesManagementProps {
  projectId: string;
}

interface UtilityFormData {
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'removed';
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
  notes?: string;
  initiationDate?: string;
  utilityOwner?: string;
  emergencyContact?: string;
  serviceArea?: string;
}

interface PhaseFormData {
  status: 'pending' | 'in-progress' | 'complete';
  initiationDate?: string;
  responseDate?: string;
  notes?: string;
}

const utilityTypes = [
  'Electric',
  'Gas',
  'Water',
  'Sewer',
  'Telecommunications',
  'Cable TV',
  'Fiber Optic',
  'Steam',
  'Oil',
  'Other',
];

const statusColors: Record<string, string> = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-yellow-100 text-yellow-800',
  removed: 'bg-red-100 text-red-800',
};

const phaseStatusColors: Record<string, string> = {
  complete: 'bg-green-100 text-green-800',
  'in-progress': 'bg-blue-100 text-blue-800',
  pending: 'bg-yellow-100 text-yellow-800',
};

const priorityColors: Record<string, string> = {
  critical: 'bg-red-100 text-red-800',
  high: 'bg-orange-100 text-orange-800',
  medium: 'bg-yellow-100 text-yellow-800',
  low: 'bg-green-100 text-green-800',
};

const conflictStatusColors: Record<string, string> = {
  resolved: 'bg-green-100 text-green-800',
  'in-progress': 'bg-blue-100 text-blue-800',
  open: 'bg-yellow-100 text-yellow-800',
};

export function UtilitiesManagement({ projectId }: UtilitiesManagementProps) {
  const { toast } = useToast();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isNewUtilityDialogOpen, setIsNewUtilityDialogOpen] = useState(false);
  const [isPhaseEditDialogOpen, setIsPhaseEditDialogOpen] = useState(false);
  const [currentUtility, setCurrentUtility] = useState<Utility | null>(null);
  const [currentPhase, setCurrentPhase] = useState<UtilityPhase | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'inactive' | 'conflicts'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const [formData, setFormData] = useState<UtilityFormData>({
    name: '',
    type: '',
    status: 'active',
    contactName: '',
    contactEmail: '',
    contactPhone: '',
    notes: '',
    utilityOwner: '',
    emergencyContact: '',
    serviceArea: '',
  });

  const [phaseFormData, setPhaseFormData] = useState<PhaseFormData>({
    status: 'pending',
    initiationDate: '',
    responseDate: '',
    notes: '',
  });

  const utils = api.useUtils();

  const {
    data: utilitiesData,
    isLoading,
    error,
  } = api.utilities.getAll.useQuery({ projectId: parseInt(projectId) }, { enabled: !!projectId && !isNaN(parseInt(projectId)) });

  // Extract utilities array from the response structure
  const utilities = utilitiesData?.utilities || [];

  // Mutation for updating a utility
  const updateUtilityMutation = api.utilities.update.useMutation({
    onSuccess: () => {
      void utils.utilities.getAll.invalidate({ projectId: parseInt(projectId) });
      setIsEditDialogOpen(false);
      setCurrentUtility(null);
      toast({
        title: 'Utility updated',
        description: 'The utility has been successfully updated.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update utility. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Mutation for creating a new utility
  const createUtilityMutation = api.utilities.create.useMutation({
    onSuccess: () => {
      void utils.utilities.getAll.invalidate({ projectId: parseInt(projectId) });
      setIsNewUtilityDialogOpen(false);
      resetForm();
      toast({
        title: 'Utility created',
        description: 'The utility has been successfully created.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create utility. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Mutation for updating phase
  const updatePhaseMutation = api.utilities.update.useMutation({
    onSuccess: () => {
      void utils.utilities.getAll.invalidate({ projectId: parseInt(projectId) });
      toast({
        title: 'Phase updated',
        description: 'The phase information has been successfully updated.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update phase. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Filter utilities based on active tab, search, and filters
  const filteredUtilities = React.useMemo(() => {
    if (!utilities) return [];

    return utilities.filter((utility: any) => {
      // Tab filter
      if (activeTab === 'active' && utility.status !== 'active') return false;
      if (activeTab === 'inactive' && utility.status !== 'inactive') return false;
      if (activeTab === 'conflicts' && utility.conflicts.length === 0) return false;

      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          utility.name.toLowerCase().includes(query) ||
          utility.type.toLowerCase().includes(query) ||
          utility.contactName?.toLowerCase().includes(query) ||
          utility.utilityOwner?.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Type filter
      if (typeFilter && typeFilter !== 'all' && utility.type !== typeFilter) return false;

      return true;
    });
  }, [utilities, activeTab, searchQuery, typeFilter]);

  // Calculate summary statistics
  const stats = React.useMemo(() => {
    if (!utilities) return { total: 0, active: 0, inactive: 0, conflicts: 0 };

    return {
      total: utilities.length,
      active: utilities.filter((u: any) => u.status === 'active').length,
      inactive: utilities.filter((u: any) => u.status === 'inactive').length,
      conflicts: utilities.reduce((count: any, u: any) => count + (u.conflicts?.length || 0), 0),
    };
  }, [utilities]);

  // Function to handle opening the edit dialog
  const handleEdit = (utility: Utility) => {
    setCurrentUtility(utility);
    setFormData({
      name: utility.name,
      type: utility.type,
      status: utility.status as 'active' | 'inactive' | 'removed',
      contactName: utility.contactName || '',
      contactEmail: utility.contactEmail || '',
      contactPhone: utility.contactPhone || '',
      notes: utility.notes || '',
      initiationDate: utility.initiationDate
        ? new Date(utility.initiationDate).toISOString().split('T')[0]
        : '',
      utilityOwner: utility.utilityOwner || '',
      emergencyContact: utility.emergencyContact || '',
      serviceArea: utility.serviceArea || '',
    });
    setIsEditDialogOpen(true);
  };

  // Function to handle form submission for updating a utility
  const handleUpdateUtility = () => {
    if (!currentUtility) return;

    updateUtilityMutation.mutate({
      id: currentUtility.id,
      name: formData.name,
      utility_type: formData.type,
      status: formData.status,
      // Note: contact fields and notes are not supported in current schema
      // TODO: Add support for contact_name, contact_email, contact_phone, notes
    });
  };

  // Function to handle form submission for creating a new utility
  const handleCreateUtility = () => {
    createUtilityMutation.mutate({
      project_id: parseInt(projectId),
      name: formData.name,
      utility_type: formData.type,
      // Note: contact fields and notes are not supported in current schema
      // TODO: Add support for contact_name, contact_email, contact_phone, notes
    });
  };

  // Function to handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Function to handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Reset form to default values
  const resetForm = () => {
    setFormData({
      name: '',
      type: '',
      status: 'active',
      contactName: '',
      contactEmail: '',
      contactPhone: '',
      notes: '',
      initiationDate: '',
      utilityOwner: '',
      emergencyContact: '',
      serviceArea: '',
    });
  };

  // Handle opening phase edit dialog
  const handleEditPhase = (utility: Utility, phase: UtilityPhase) => {
    setCurrentUtility(utility);
    setCurrentPhase(phase);
    setPhaseFormData({
      status: phase.status,
      initiationDate: utility.initiationDate
        ? new Date(utility.initiationDate).toISOString().split('T')[0]
        : '',
      responseDate: phase.responseDate
        ? new Date(phase.responseDate).toISOString().split('T')[0]
        : '',
      notes: phase.notes || '',
    });
    setIsPhaseEditDialogOpen(true);
  };

  // Handle phase form input changes
  const handlePhaseInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPhaseFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle phase select changes
  const handlePhaseSelectChange = (name: string, value: string) => {
    setPhaseFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Function to handle form submission for updating a phase
  const handleUpdatePhase = () => {
    if (!currentUtility || !currentPhase) return;

    // Note: The update mutation doesn't actually support phase updates
    // This would need a separate endpoint or different implementation
    updatePhaseMutation.mutate({
      id: currentUtility.id,
      // notes: phaseFormData.notes, // Not supported in current schema
      // last_response: phaseFormData.responseDate ? new Date(phaseFormData.responseDate) : undefined, // Not supported in current schema
    });

    setIsPhaseEditDialogOpen(false);
    setCurrentUtility(null);
    setCurrentPhase(null);
  };

  // Open new utility dialog
  const handleAddUtility = () => {
    resetForm();
    setIsNewUtilityDialogOpen(true);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Utilities Management
          </CardTitle>
          <CardDescription>Loading utility data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-10 bg-muted rounded"></div>
            <div className="h-60 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Utilities Management
          </CardTitle>
          <CardDescription>Error loading utilities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900">Failed to load utilities</h3>
            <p className="text-muted-foreground mt-2">Please try again later.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!utilities || utilities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Utilities Management
          </CardTitle>
          <CardDescription>Track and manage utilities for this project</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900">No utilities found</h3>
            <p className="text-muted-foreground mt-2 mb-6 max-w-md mx-auto">
              Start by adding utility companies involved in this project to track coordination and
              manage communications.
            </p>
            <Button onClick={handleAddUtility}>
              <Plus className="h-4 w-4 mr-2" />
              Add First Utility
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Utilities Management
            </CardTitle>
            <CardDescription>Track and manage utilities for this project</CardDescription>
          </div>
          <Button onClick={handleAddUtility} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Utility
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-4 w-4 text-blue-600" />
                <div className="text-sm font-medium text-blue-600">Total Utilities</div>
              </div>
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <div className="text-sm font-medium text-green-600">Active</div>
              </div>
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <div className="text-sm font-medium text-yellow-600">Inactive</div>
              </div>
              <div className="text-2xl font-bold text-yellow-600">{stats.inactive}</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <div className="text-sm font-medium text-red-600">Conflicts</div>
              </div>
              <div className="text-2xl font-bold text-red-600">{stats.conflicts}</div>
            </div>
          </div>

          {/* Tabs and Filters */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <Tabs
              value={activeTab}
              onValueChange={(value) =>
                setActiveTab(value as 'all' | 'active' | 'inactive' | 'conflicts')
              }
            >
              <TabsList>
                <TabsTrigger value="all">All Utilities</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="inactive">Inactive</TabsTrigger>
                <TabsTrigger value="conflicts">With Conflicts</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search utilities..."
                  className="pl-8 w-64"
                  value={searchQuery}
                  onChange={(e: any) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {utilityTypes.map((type: any) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Utilities List */}
          {filteredUtilities.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">No utilities found</h3>
              <p className="text-muted-foreground mt-2">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredUtilities.map((utility: any) => (
                <Accordion type="single" collapsible className="border rounded-lg" key={utility.id}>
                  <AccordionItem value={`utility-${utility.id}`} className="border-none">
                    <AccordionTrigger className="px-4 py-3 hover:no-underline bg-muted/20 hover:bg-muted/40 rounded-t-lg">
                      <div className="flex w-full justify-between items-center text-left">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-blue-600" />
                            <span className="font-semibold">{utility.name}</span>
                            <CommentBadge
                              entityType="utility"
                              entityId={utility.id.toString()}
                              entityName={utility.name}
                              variant="icon-only"
                              showZero={false}
                            />
                          </div>
                          <Badge className={statusColors[utility.status] || ''}>{utility.status}</Badge>
                          <Badge variant="outline">{utility.type}</Badge>
                        </div>
                        <div className="flex items-center gap-3">
                          {utility.conflicts.length > 0 && (
                            <span className="flex items-center text-amber-600 text-sm">
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              {utility.conflicts.length}{' '}
                              {utility.conflicts.length === 1 ? 'conflict' : 'conflicts'}
                            </span>
                          )}
                          {utility.contactName && (
                            <span className="text-sm text-muted-foreground">
                              {utility.contactName}
                            </span>
                          )}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 py-4">
                      <div className="space-y-6">
                        {/* Edit button */}
                        <div className="flex justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e: any) => {
                              e.stopPropagation();
                              handleEdit(utility);
                            }}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Utility
                          </Button>
                        </div>

                        {/* Utility Details */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-b pb-6">
                          <div>
                            <h4 className="text-sm font-semibold mb-3 flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              Contact Information
                            </h4>
                            <div className="space-y-2">
                              {utility.contactName && (
                                <p className="text-sm">
                                  <span className="font-medium">Primary Contact:</span>{' '}
                                  {utility.contactName}
                                </p>
                              )}
                              {utility.contactPhone && (
                                <p className="text-sm flex items-center">
                                  <Phone className="h-3 w-3 mr-2" />
                                  <span>{utility.contactPhone}</span>
                                </p>
                              )}
                              {utility.contactEmail && (
                                <p className="text-sm flex items-center">
                                  <Mail className="h-3 w-3 mr-2" />
                                  <span>{utility.contactEmail}</span>
                                </p>
                              )}
                              {utility.emergencyContact && (
                                <p className="text-sm">
                                  <span className="font-medium">Emergency:</span>{' '}
                                  {utility.emergencyContact}
                                </p>
                              )}
                              {utility.utilityOwner && (
                                <p className="text-sm">
                                  <span className="font-medium">Owner:</span> {utility.utilityOwner}
                                </p>
                              )}
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-semibold mb-3 flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              Status Information
                            </h4>
                            <div className="space-y-2">
                              <p className="text-sm flex items-center">
                                <Clock className="h-3 w-3 mr-2" />
                                <span>
                                  <span className="font-medium">Initiation Date:</span>{' '}
                                  {utility.initiationDate
                                    ? new Date(utility.initiationDate).toLocaleDateString()
                                    : 'Not set'}
                                </span>
                              </p>
                              <p className="text-sm flex items-center">
                                <Clock className="h-3 w-3 mr-2" />
                                <span>
                                  <span className="font-medium">Last Response:</span>{' '}
                                  {utility.lastResponse
                                    ? new Date(utility.lastResponse).toLocaleDateString()
                                    : 'No response yet'}
                                </span>
                              </p>
                              {utility.serviceArea && (
                                <p className="text-sm flex items-center">
                                  <MapPin className="h-3 w-3 mr-2" />
                                  <span>
                                    <span className="font-medium">Service Area:</span>{' '}
                                    {utility.serviceArea}
                                  </span>
                                </p>
                              )}
                              {utility.notes && (
                                <div className="text-sm flex items-start">
                                  <FileText className="h-3 w-3 mr-2 mt-1" />
                                  <div className="flex-1">
                                    <span className="font-medium">Notes:</span>
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <div className="mt-1 p-2 border rounded-md bg-slate-50 text-sm max-h-20 overflow-hidden hover:bg-slate-100 transition-colors cursor-default">
                                            {utility.notes}
                                          </div>
                                        </TooltipTrigger>
                                        {utility.notes.length > 100 && (
                                          <TooltipContent
                                            side="bottom"
                                            className="max-w-md whitespace-normal"
                                          >
                                            {utility.notes}
                                          </TooltipContent>
                                        )}
                                      </Tooltip>
                                    </TooltipProvider>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Phase Summaries */}
                        {utility.phaseSummaries.length > 0 && (
                          <div className="border-b pb-6">
                            <Accordion type="single" collapsible className="border rounded-md">
                              <AccordionItem value="phase-summaries" className="border-none">
                                <AccordionTrigger className="py-3 px-4 hover:no-underline">
                                  <h4 className="text-sm font-semibold flex items-center gap-2">
                                    <Clock className="h-4 w-4" />
                                    Phase Status
                                  </h4>
                                </AccordionTrigger>
                                <AccordionContent className="px-4 pb-4">
                                  <div className="overflow-x-auto">
                                    <Table>
                                      <TableHeader>
                                        <TableRow>
                                          <TableHead>Phase</TableHead>
                                          <TableHead>Status</TableHead>
                                          <TableHead>Initiation Date</TableHead>
                                          <TableHead>Response Date</TableHead>
                                          <TableHead>Notes</TableHead>
                                          <TableHead className="w-24">Actions</TableHead>
                                        </TableRow>
                                      </TableHeader>
                                      <TableBody>
                                        {(utility.phaseSummaries as UtilityPhase[]).map((phase: any) => (
                                          <TableRow key={phase.phaseId}>
                                            <TableCell className="font-medium">
                                              {phase.phaseName}
                                            </TableCell>
                                            <TableCell>
                                              <Badge className={phaseStatusColors[phase.status] || ''}>
                                                {phase.status}
                                              </Badge>
                                              {phase.status === 'in-progress' &&
                                                !phase.responseDate && (
                                                  <Badge variant="outline" className="ml-1 text-xs">
                                                    Current
                                                  </Badge>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                              {utility.initiationDate
                                                ? new Date(
                                                    utility.initiationDate
                                                  ).toLocaleDateString()
                                                : '--'}
                                            </TableCell>
                                            <TableCell>
                                              {phase.responseDate
                                                ? new Date(phase.responseDate).toLocaleDateString()
                                                : '--'}
                                            </TableCell>
                                            <TableCell>
                                              <TooltipProvider>
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <span className="max-w-md truncate block">
                                                      {phase.notes || '--'}
                                                    </span>
                                                  </TooltipTrigger>
                                                  {phase.notes && (
                                                    <TooltipContent
                                                      side="bottom"
                                                      className="max-w-md whitespace-normal"
                                                    >
                                                      {phase.notes}
                                                    </TooltipContent>
                                                  )}
                                                </Tooltip>
                                              </TooltipProvider>
                                            </TableCell>
                                            <TableCell>
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-8 w-8 p-0"
                                                onClick={() => handleEditPhase(utility, phase)}
                                              >
                                                <Edit className="h-4 w-4" />
                                              </Button>
                                            </TableCell>
                                          </TableRow>
                                        ))}
                                      </TableBody>
                                    </Table>
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        )}

                        {/* Conflicts */}
                        {utility.conflicts.length > 0 && (
                          <div>
                            <Accordion type="single" collapsible className="border rounded-md">
                              <AccordionItem value="conflicts" className="border-none">
                                <AccordionTrigger className="py-3 px-4 hover:no-underline">
                                  <div className="flex items-center gap-2">
                                    <h4 className="text-sm font-semibold flex items-center gap-2">
                                      <AlertTriangle className="h-4 w-4" />
                                      Conflicts
                                    </h4>
                                    <Badge variant="outline">{utility.conflicts.length}</Badge>
                                  </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-4 pb-4">
                                  <div className="overflow-x-auto">
                                    <Table>
                                      <TableHeader>
                                        <TableRow>
                                          <TableHead>Description</TableHead>
                                          <TableHead>Status</TableHead>
                                          <TableHead>Priority</TableHead>
                                          <TableHead>Location</TableHead>
                                          <TableHead>Resolution</TableHead>
                                        </TableRow>
                                      </TableHeader>
                                      <TableBody>
                                        {utility.conflicts.map((conflict: any) => (
                                          <TableRow key={conflict.id}>
                                            <TableCell className="font-medium">
                                              {conflict.description}
                                            </TableCell>
                                            <TableCell>
                                              <Badge
                                                className={conflictStatusColors[conflict.status] || ''}
                                              >
                                                {conflict.status}
                                              </Badge>
                                            </TableCell>
                                            <TableCell>
                                              <Badge className={priorityColors[conflict.priority] || ''}>
                                                {conflict.priority}
                                              </Badge>
                                            </TableCell>
                                            <TableCell>{conflict.location || '--'}</TableCell>
                                            <TableCell>
                                              <TooltipProvider>
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <span className="max-w-md truncate block">
                                                      {conflict.resolution_notes || '--'}
                                                    </span>
                                                  </TooltipTrigger>
                                                  {conflict.resolution_notes && (
                                                    <TooltipContent
                                                      side="bottom"
                                                      className="max-w-md whitespace-normal"
                                                    >
                                                      {conflict.resolution_notes}
                                                    </TooltipContent>
                                                  )}
                                                </Tooltip>
                                              </TooltipProvider>
                                            </TableCell>
                                          </TableRow>
                                        ))}
                                      </TableBody>
                                    </Table>
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Utility Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Utility</DialogTitle>            <DialogDescription>
              Update utility information below. Click save when you&apos;re done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name *
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type *
              </Label>
              <Select
                name="type"
                value={formData.type}
                onValueChange={(value) => handleSelectChange('type', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select utility type" />
                </SelectTrigger>
                <SelectContent>
                  {utilityTypes.map((type: any) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                name="status"
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="removed">Removed</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="initiationDate" className="text-right">
                Initiation Date
              </Label>
              <Input
                id="initiationDate"
                name="initiationDate"
                type="date"
                value={formData.initiationDate}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contactName" className="text-right">
                Contact Name
              </Label>
              <Input
                id="contactName"
                name="contactName"
                value={formData.contactName}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contactEmail" className="text-right">
                Email
              </Label>
              <Input
                id="contactEmail"
                name="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contactPhone" className="text-right">
                Phone
              </Label>
              <Input
                id="contactPhone"
                name="contactPhone"
                value={formData.contactPhone}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="utilityOwner" className="text-right">
                Utility Owner
              </Label>
              <Input
                id="utilityOwner"
                name="utilityOwner"
                value={formData.utilityOwner}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="emergencyContact" className="text-right">
                Emergency Contact
              </Label>
              <Input
                id="emergencyContact"
                name="emergencyContact"
                value={formData.emergencyContact}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="serviceArea" className="text-right">
                Service Area
              </Label>
              <Input
                id="serviceArea"
                name="serviceArea"
                value={formData.serviceArea}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="notes" className="text-right pt-2">
                Notes
              </Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateUtility} disabled={updateUtilityMutation.isPending}>
              {updateUtilityMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Phase Edit Dialog */}
      <Dialog open={isPhaseEditDialogOpen} onOpenChange={setIsPhaseEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Phase Information</DialogTitle>
            <DialogDescription>
              {currentPhase && <span>Update {currentPhase.phaseName} phase information</span>}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                name="status"
                value={phaseFormData.status}
                onValueChange={(value) => handlePhaseSelectChange('status', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="complete">Complete</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="initiationDate" className="text-right">
                Initiation Date
              </Label>
              <Input
                id="initiationDate"
                name="initiationDate"
                type="date"
                value={phaseFormData.initiationDate}
                onChange={handlePhaseInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="responseDate" className="text-right">
                Response Date
              </Label>
              <Input
                id="responseDate"
                name="responseDate"
                type="date"
                value={phaseFormData.responseDate}
                onChange={handlePhaseInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="notes" className="text-right pt-2">
                Notes
              </Label>
              <Textarea
                id="notes"
                name="notes"
                value={phaseFormData.notes}
                onChange={handlePhaseInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPhaseEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdatePhase} disabled={updatePhaseMutation.isPending}>
              {updatePhaseMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Utility Dialog */}
      <Dialog open={isNewUtilityDialogOpen} onOpenChange={setIsNewUtilityDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Utility</DialogTitle>            <DialogDescription>
              Enter utility information below. Click create when you&apos;re done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name *
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="e.g., Duke Energy"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type *
              </Label>
              <Select
                name="type"
                value={formData.type}
                onValueChange={(value) => handleSelectChange('type', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select utility type" />
                </SelectTrigger>
                <SelectContent>
                  {utilityTypes.map((type: any) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                name="status"
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="removed">Removed</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contactName" className="text-right">
                Contact Name
              </Label>
              <Input
                id="contactName"
                name="contactName"
                value={formData.contactName}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="Primary contact person"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contactEmail" className="text-right">
                Email
              </Label>
              <Input
                id="contactEmail"
                name="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="<EMAIL>"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contactPhone" className="text-right">
                Phone
              </Label>
              <Input
                id="contactPhone"
                name="contactPhone"
                value={formData.contactPhone}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="(*************"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="notes" className="text-right pt-2">
                Notes
              </Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
                placeholder="Additional notes about this utility"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsNewUtilityDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateUtility} disabled={createUtilityMutation.isPending}>
              {createUtilityMutation.isPending ? 'Creating...' : 'Create Utility'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
