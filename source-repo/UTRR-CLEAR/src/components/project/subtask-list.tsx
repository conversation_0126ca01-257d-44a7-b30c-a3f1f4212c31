"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Loader2, Plus, Calendar, User, Building2, Phone, Mail } from "lucide-react";
import { CreateSubtaskDialog } from "./create-subtask-dialog";
import { format } from "date-fns";
import { cn } from "~/lib/utils";

interface SubtaskListProps {
  parentTask: {
    id: string;
    title: string;
    project_id?: string;
  };
}

const priorityColors = {
  Low: "bg-gray-100 text-gray-700",
  Medium: "bg-yellow-100 text-yellow-700",
  High: "bg-red-100 text-red-700",
};

const statusColors = {
  "Not Started": "bg-gray-100 text-gray-700",
  "In Progress": "bg-blue-100 text-blue-700",
  "Completed": "bg-green-100 text-green-700",
  "On Hold": "bg-orange-100 text-orange-700",
};

export function SubtaskList({ parentTask }: SubtaskListProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const {
    data: subtasks,
    isLoading,
    refetch,
  } = api.subtasks.getByParentTask.useQuery({
    parentTaskId: parentTask.id,
  });

  const handleSubtaskCreated = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading subtasks...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          Subtasks ({subtasks?.length || 0})
        </h3>
        <Button
          size="sm"
          onClick={() => setShowCreateDialog(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Subtask
        </Button>
      </div>

      {subtasks && subtasks.length > 0 ? (
        <div className="space-y-3">
          {subtasks.map((subtask) => (
            <Card key={subtask.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm mb-1">{subtask.title}</h4>
                    {subtask.description && (
                      <p className="text-sm text-muted-foreground mb-2">
                        {subtask.description}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Badge 
                      variant="secondary"
                      className={cn(priorityColors[subtask.priority as keyof typeof priorityColors])}
                    >
                      {subtask.priority}
                    </Badge>
                    <Badge 
                      variant="secondary"
                      className={cn(statusColors[subtask.status as keyof typeof statusColors])}
                    >
                      {subtask.status}
                    </Badge>
                  </div>
                </div>

                {/* Utility Information */}
                {subtask.utility_company && (
                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{subtask.utility_company}</span>
                    </div>
                    {subtask.utility_contact && (
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{subtask.utility_contact}</span>
                      </div>
                    )}
                    {subtask.utility_email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-blue-600">{subtask.utility_email}</span>
                      </div>
                    )}
                    {subtask.utility_phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{subtask.utility_phone}</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Schedule Information */}
                {(subtask.target_start_date || subtask.target_end_date || subtask.duration) && (
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <div className="flex gap-4">
                      {subtask.target_start_date && (
                        <span>
                          Start: {format(new Date(subtask.target_start_date), "MMM d, yyyy")}
                        </span>
                      )}
                      {subtask.target_end_date && (
                        <span>
                          End: {format(new Date(subtask.target_end_date), "MMM d, yyyy")}
                        </span>
                      )}
                      {subtask.duration && (
                        <span>Duration: {subtask.duration} days</span>
                      )}
                    </div>
                  </div>
                )}

                {/* Progress */}
                {subtask.progress_percentage !== null && subtask.progress_percentage > 0 && (
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{subtask.progress_percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${subtask.progress_percentage}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Assignment */}
                {subtask.assigned_to && (
                  <div className="mt-2 text-sm text-muted-foreground">
                    Assigned to: <span className="font-medium">{subtask.assigned_to}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          <Building2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>No subtasks yet</p>
          <p className="text-sm">Create subtasks to track utility-specific tasks</p>
        </div>
      )}

      <CreateSubtaskDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        parentTask={parentTask}
        onSuccess={handleSubtaskCreated}
      />
    </div>
  );
}