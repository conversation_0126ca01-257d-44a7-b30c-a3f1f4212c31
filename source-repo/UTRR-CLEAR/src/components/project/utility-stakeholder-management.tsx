'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { Label } from '~/components/ui/label';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { CommentBadge } from '~/components/comments/comment-badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { useToast } from '~/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Search,
  UserPlus,
  User,
  Phone,
  Mail,
  Building2,
  FileText,
  Cable,
  Wrench,
  MapPin,
  PlusCircle,
  Calendar,
  Check,
  X,
  Clock,
  Edit,
  Trash2,
  Link,
  GanttChart,
  FileWarning,
  Plus,
  Eye,
} from 'lucide-react';

// Define types
interface UtilityStakeholder {
  id: number;
  fullName: string;
  contactCompany: string;
  companyName: string;
  typeDelivery: string | null;
  stakeholderType: string | null;
  businessPhone: string | null;
  mobilePhone: string | null;
  email: string | null;
  isUtilityCoordinator: boolean;
  serviceArea: string | null;
  primaryContact: boolean;
  emergencyContact: boolean;
  preferredContactMethod: string | null;
  responseTimeAverage: number | null;
  lastContactDate: string | null;
  utilitySystems: string[] | null;
  permittingRequirements: string | null;
  specialNotes: string | null;
  reliabilityRating: number | null;
  pastProjectHistory: any[] | null;
}

interface UtilityCompany {
  id: number;
  name: string;
  abbreviation: string | null;
  utilityTypes: string[];
  mainContactId: number | null;
  mainContact: UtilityStakeholder | null;
  contacts: UtilityStakeholder[];
  relocationTimeAverage: number | null;
  reviewTimeAverage: number | null;
  agreementRequired: boolean;
  permitRequired: boolean;
  regionsServed: string[];
  specialNotes: string | null;
}

interface UtilityStakeholderManagementProps {
  projectId: string;
}

// Form schemas
const utilityStakeholderFormSchema = z.object({
  fullName: z.string().min(2, 'Full name is required'),
  contactCompany: z.string().min(2, 'Company name is required'),
  typeDelivery: z.string().optional().nullable(),
  stakeholderType: z.string().min(1, 'Stakeholder type is required'),
  businessPhone: z.string().optional().nullable(),
  mobilePhone: z.string().optional().nullable(),
  email: z.string().email('Invalid email address').optional().nullable(),
  isUtilityCoordinator: z.boolean().default(false),
  serviceArea: z.string().optional().nullable(),
  primaryContact: z.boolean().default(false),
  emergencyContact: z.boolean().default(false),
  preferredContactMethod: z.string().optional().nullable(),
  utilitySystems: z.array(z.string()).optional().nullable(),
  permittingRequirements: z.string().optional().nullable(),
  specialNotes: z.string().optional().nullable(),
});

type UtilityStakeholderFormValues = z.infer<typeof utilityStakeholderFormSchema>;

export function UtilityStakeholderManagement({ projectId }: UtilityStakeholderManagementProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('utility-companies');
  const [selectedUtilityId, setSelectedUtilityId] = useState<number | null>(null);
  const [isAddStakeholderDialogOpen, setIsAddStakeholderDialogOpen] = useState(false);
  const [isEditNotesDialogOpen, setIsEditNotesDialogOpen] = useState(false);
  const [utilityNotes, setUtilityNotes] = useState('');

  // Fetch utility stakeholders
  const { data: utilityStakeholders, isLoading: isLoadingStakeholders } = useQuery<
    UtilityStakeholder[]
  >({
    queryKey: [`/api/projects/${projectId}/utility-stakeholders`],
    enabled: !!projectId,
    queryFn: async () => {
      // For demo purposes, provide mock data if the endpoint isn't implemented yet
      return getMockUtilityStakeholders();
    },
  });

  // Fetch utility companies
  const { data: utilityCompanies, isLoading: isLoadingCompanies } = useQuery<UtilityCompany[]>({
    queryKey: [`/api/projects/${projectId}/utility-companies`],
    enabled: !!projectId,
    queryFn: async () => {
      // For demo purposes, provide mock data if the endpoint isn't implemented yet
      return getMockUtilityCompanies();
    },
  });

  // Setup form
  const form = useForm({
    resolver: zodResolver(utilityStakeholderFormSchema),
    defaultValues: {
      fullName: '',
      contactCompany: '',
      typeDelivery: '',
      stakeholderType: 'Utility Company',
      businessPhone: '',
      mobilePhone: '',
      email: '',
      isUtilityCoordinator: false,
      serviceArea: '',
      primaryContact: false,
      emergencyContact: false,
      preferredContactMethod: 'Email',
      utilitySystems: [],
      permittingRequirements: '',
      specialNotes: '',
    },
  });

  // Mutation for adding a stakeholder
  const addStakeholderMutation = useMutation({
    mutationFn: async (data: UtilityStakeholderFormValues) => {
      // In a real implementation, this would be an API call
      return { success: true, message: 'Stakeholder added successfully' };
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Utility stakeholder added successfully',
      });
      setIsAddStakeholderDialogOpen(false);
      form.reset();

      // In a real implementation, we'd invalidate the queries
      // queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/utility-stakeholders`] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Failed to add stakeholder. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Mutation for updating utility notes
  const updateUtilityNotesMutation = useMutation({
    mutationFn: async ({ id, notes }: { id: number; notes: string }) => {
      // In a real implementation, this would be an API call
      return { success: true, message: 'Notes updated successfully' };
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Utility company notes updated successfully',
      });
      setIsEditNotesDialogOpen(false);

      // In a real implementation, we'd invalidate the queries
      // queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/utility-companies`] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Failed to update notes. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Handle form submission
  const onSubmitStakeholder = (data: UtilityStakeholderFormValues) => {
    addStakeholderMutation.mutate(data);
  };

  // Handle utility notes update
  const handleSaveNotes = () => {
    if (selectedUtilityId) {
      updateUtilityNotesMutation.mutate({
        id: selectedUtilityId,
        notes: utilityNotes,
      });
    }
  };

  // Filtered utility companies based on search query
  const filteredUtilityCompanies = React.useMemo(() => {
    if (!utilityCompanies) return [];

    return utilityCompanies.filter((company: any) => {
      const searchLower = searchQuery.toLowerCase();
      return (
        company.name.toLowerCase().includes(searchLower) ||
        company.abbreviation?.toLowerCase().includes(searchLower) ||
        company.utilityTypes.some((type: any) => type.toLowerCase().includes(searchLower))
      );
    });
  }, [utilityCompanies, searchQuery]);

  // Filtered utility stakeholders based on search query
  const filteredUtilityStakeholders = React.useMemo(() => {
    if (!utilityStakeholders) return [];

    return utilityStakeholders.filter((stakeholder: any) => {
      const searchLower = searchQuery.toLowerCase();
      return (
        stakeholder.fullName.toLowerCase().includes(searchLower) ||
        stakeholder.contactCompany.toLowerCase().includes(searchLower) ||
        stakeholder.email?.toLowerCase().includes(searchLower)
      );
    });
  }, [utilityStakeholders, searchQuery]);

  // Handle opening the edit notes dialog
  const handleEditNotes = (company: UtilityCompany) => {
    setSelectedUtilityId(company.id);
    setUtilityNotes(company.specialNotes || '');
    setIsEditNotesDialogOpen(true);
  };

  // Mock data for demo purposes
  function getMockUtilityStakeholders(): UtilityStakeholder[] {
    return [
      {
        id: 1,
        fullName: 'John Smith',
        contactCompany: 'Edison Power',
        companyName: 'Edison Power',
        typeDelivery: 'Electric | Distribution',
        stakeholderType: 'Utility Company',
        businessPhone: '(*************',
        mobilePhone: '(*************',
        email: '<EMAIL>',
        isUtilityCoordinator: false,
        serviceArea: 'Central Indiana',
        primaryContact: true,
        emergencyContact: true,
        preferredContactMethod: 'Email',
        responseTimeAverage: 3.2,
        lastContactDate: '2023-11-15',
        utilitySystems: ['Distribution Lines', 'Transformers'],
        permittingRequirements: 'Standard 10-day review process',
        specialNotes: 'Prefers PDF format for all documentation',
        reliabilityRating: 4,
        pastProjectHistory: [
          { projectName: 'Highway 37 Expansion', year: 2022, performance: 'Good' },
        ],
      },
      {
        id: 2,
        fullName: 'Sarah Johnson',
        contactCompany: 'MidState Gas',
        companyName: 'MidState Gas',
        typeDelivery: 'Gas | Distribution',
        stakeholderType: 'Utility Company',
        businessPhone: '(*************',
        mobilePhone: null,
        email: '<EMAIL>',
        isUtilityCoordinator: true,
        serviceArea: 'Indiana, Illinois',
        primaryContact: true,
        emergencyContact: false,
        preferredContactMethod: 'Phone',
        responseTimeAverage: 5.8,
        lastContactDate: '2023-10-20',
        utilitySystems: ['Transmission Lines', 'Distribution Lines'],
        permittingRequirements: 'Requires 15 business days for review',
        specialNotes: 'Always ask for pressure test results on relocation projects',
        reliabilityRating: 3,
        pastProjectHistory: [{ projectName: 'I-65 Widening', year: 2021, performance: 'Average' }],
      },
      {
        id: 3,
        fullName: 'Michael Chen',
        contactCompany: 'Fiber Connect',
        companyName: 'Fiber Connect',
        typeDelivery: 'Telecommunications | Fiber',
        stakeholderType: 'Utility Company',
        businessPhone: '(*************',
        mobilePhone: '(*************',
        email: '<EMAIL>',
        isUtilityCoordinator: false,
        serviceArea: 'Central and Southern Indiana',
        primaryContact: true,
        emergencyContact: false,
        preferredContactMethod: 'Email',
        responseTimeAverage: 2.1,
        lastContactDate: '2023-12-01',
        utilitySystems: ['Fiber Optic Lines'],
        permittingRequirements: 'Fast-track process available',
        specialNotes: 'Always responsive, but needs clear scope definitions',
        reliabilityRating: 5,
        pastProjectHistory: [
          { projectName: 'Downtown Revitalization', year: 2023, performance: 'Excellent' },
        ],
      },
    ];
  }

  function getMockUtilityCompanies(): UtilityCompany[] {
    return [
      {
        id: 1,
        name: 'Edison Power',
        abbreviation: 'EP',
        utilityTypes: ['Electric', 'Distribution'],
        mainContactId: 1,
        mainContact: null,
        contacts: [],
        relocationTimeAverage: 45.2,
        reviewTimeAverage: 15.3,
        agreementRequired: true,
        permitRequired: true,
        regionsServed: ['Central Indiana', 'North Indiana'],
        specialNotes:
          'Prefers early coordination. Response times tend to be slower in winter months.',
      },
      {
        id: 2,
        name: 'MidState Gas',
        abbreviation: 'MSG',
        utilityTypes: ['Gas', 'Distribution', 'Transmission'],
        mainContactId: 2,
        mainContact: null,
        contacts: [],
        relocationTimeAverage: 60.5,
        reviewTimeAverage: 21.7,
        agreementRequired: true,
        permitRequired: true,
        regionsServed: ['Indiana', 'Illinois'],
        specialNotes: 'All relocations require pressure testing and certification.',
      },
      {
        id: 3,
        name: 'Fiber Connect',
        abbreviation: 'FC',
        utilityTypes: ['Telecommunications', 'Fiber'],
        mainContactId: 3,
        mainContact: null,
        contacts: [],
        relocationTimeAverage: 30.1,
        reviewTimeAverage: 10.5,
        agreementRequired: false,
        permitRequired: true,
        regionsServed: ['Central Indiana', 'Southern Indiana'],
        specialNotes: 'Fast response times, but needs detailed scope information.',
      },
    ];
  }

  // Process the data to connect stakeholders to companies
  React.useEffect(() => {
    if (utilityCompanies && utilityStakeholders) {
      // In a real implementation, this would be handled by the backend
      const companiesWithContacts = utilityCompanies.map((company: any) => {
        const contacts = utilityStakeholders.filter(
          (stakeholder) => stakeholder.companyName === company.name
        );

        const mainContact =
          contacts.find(
            (contact) => contact.id === company.mainContactId || contact.primaryContact
          ) || null;

        return {
          ...company,
          contacts,
          mainContact,
        };
      });
    }
  }, [utilityCompanies, utilityStakeholders]);

  const getUtilityStatusColor = (responseTimeAvg: number | null, reviewTimeAvg: number | null) => {
    if (!responseTimeAvg && !reviewTimeAvg) return 'bg-muted text-gray-800';

    const combinedAvg = (responseTimeAvg || 0) + (reviewTimeAvg || 0);

    if (combinedAvg < 20) return 'bg-green-100 text-green-800';
    if (combinedAvg < 40) return 'bg-blue-100 text-blue-800';
    if (combinedAvg < 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-orange-100 text-orange-800';
  };

  const getResponseTimeLabel = (time: number | null) => {
    if (!time) return 'Unknown';

    if (time < 3) return 'Very Fast';
    if (time < 5) return 'Fast';
    if (time < 10) return 'Average';
    if (time < 15) return 'Slow';
    return 'Very Slow';
  };

  const getRelocationTimeLabel = (time: number | null) => {
    if (!time) return 'Unknown';

    if (time < 30) return 'Very Fast';
    if (time < 45) return 'Fast';
    if (time < 60) return 'Average';
    if (time < 90) return 'Slow';
    return 'Very Slow';
  };

  const stakeholderTypeOptions = [
    'Utility Company',
    'Utility Contractor',
    'Government Agency',
    'Consultant',
    'Regulatory Agency',
  ];

  const preferredContactMethodOptions = ['Email', 'Phone', 'Mail', 'In-Person'];

  const utilitySystemOptions = [
    'Distribution Lines',
    'Transmission Lines',
    'Substations',
    'Water Mains',
    'Sewer Lines',
    'Fiber Optic Lines',
    'Copper Lines',
    'Natural Gas Pipelines',
    'Hydrants',
    'Transformers',
    'Service Lines',
    'Valves and Controls',
  ];

  if (isLoadingStakeholders || isLoadingCompanies) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Utility Stakeholder Management</CardTitle>
          <CardDescription>Loading stakeholder data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-muted rounded-md w-[200px]"></div>
              <div className="h-6 bg-muted rounded-md w-[300px]"></div>
              <div className="h-6 bg-muted rounded-md w-[250px]"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="flex flex-col sm:flex-row gap-4 justify-between">
        <div>
          <CardTitle className="text-2xl">Utility Stakeholder Management</CardTitle>
          <CardDescription>
            Manage utility companies and contacts for effective coordination
          </CardDescription>
        </div>

        <div className="flex flex-col xs:flex-row gap-2">
          <Dialog open={isAddStakeholderDialogOpen} onOpenChange={setIsAddStakeholderDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" /> Add Contact
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add Utility Contact</DialogTitle>
                <DialogDescription>
                  Add a new utility stakeholder contact to the system.
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitStakeholder)} className="space-y-4 py-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="fullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input placeholder="John Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contactCompany"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company Name</FormLabel>
                          <FormControl>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select company" />
                              </SelectTrigger>
                              <SelectContent>
                                {utilityCompanies?.map((company: any) => (
                                  <SelectItem key={company.id} value={company.name}>
                                    {company.name}
                                  </SelectItem>
                                ))}
                                <SelectItem value="other">Add New Company...</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="typeDelivery"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Utility Type/Delivery</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="e.g. Electric | Distribution"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="stakeholderType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Stakeholder Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value || 'Utility Company'}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {stakeholderTypeOptions.map((type: any) => (
                                <SelectItem key={type} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="businessPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Phone</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="(*************"
                              {...field}
                              value={field.value || ''}
                              onChange={(e: any) => {
                                // Basic phone number formatting
                                const value = e.target.value.replace(/\D/g, '');
                                const formattedValue = value
                                  .replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3')
                                  .slice(0, 14); // Limit to formatted length
                                field.onChange(formattedValue);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="primaryContact"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            />
                          </FormControl>
                          <FormLabel className="text-sm font-normal">Primary Contact</FormLabel>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyContact"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            />
                          </FormControl>
                          <FormLabel className="text-sm font-normal">Emergency Contact</FormLabel>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="specialNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Special Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Add any special notes about working with this contact"
                            {...field}
                            value={field.value || ''}
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button type="submit">Save Contact</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search utilities and contacts..."
              className="pl-8"
              value={searchQuery}
              onChange={(e: any) => setSearchQuery(e.target.value)}
            />
          </div>

          <Tabs
            defaultValue="utility-companies"
            className="w-full sm:w-auto"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid w-full sm:w-[400px] grid-cols-2">
              <TabsTrigger value="utility-companies">Utility Companies</TabsTrigger>
              <TabsTrigger value="utility-contacts">Utility Contacts</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <ScrollArea className="h-[600px] rounded-md border">
          {activeTab === 'utility-companies' && (
            <div className="p-1">
              {!filteredUtilityCompanies || filteredUtilityCompanies.length === 0 ? (
                <div className="text-center py-10">
                  <Building2 className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No utility companies found</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {searchQuery
                      ? 'Try adjusting your search criteria'
                      : 'Get started by adding a utility company'}
                  </p>
                  <Button variant="outline" className="mt-4">
                    <Plus className="mr-2 h-4 w-4" /> Add Utility Company
                  </Button>
                </div>
              ) : (
                <div className="space-y-6 p-4">
                  {filteredUtilityCompanies.map((company: any) => (
                    <Card key={company.id} className="shadow-sm hover:shadow transition-shadow">
                      <CardHeader className="pb-2">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                          <div>
                            <div className="flex items-center gap-2">
                              <CardTitle>{company.name}</CardTitle>
                              {company.abbreviation && (
                                <Badge variant="outline">{company.abbreviation}</Badge>
                              )}
                            </div>
                            <CardDescription>{company.utilityTypes.join(' | ')}</CardDescription>
                          </div>

                          <div className="flex items-center gap-2">
                            <Badge
                              className={getUtilityStatusColor(
                                company.reviewTimeAverage,
                                company.relocationTimeAverage
                              )}
                            >
                              {company.agreementRequired
                                ? 'Agreement Required'
                                : 'No Agreement Required'}
                            </Badge>
                            <Badge variant={company.permitRequired ? 'destructive' : 'outline'}>
                              {company.permitRequired ? 'Permit Required' : 'No Permit Required'}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="pb-2">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-semibold mb-2 flex items-center gap-1">
                              <User className="h-4 w-4" /> Main Contact
                            </h4>

                            <div className="flex items-center">
                              <p className="text-sm text-muted-foreground">
                                No main contact assigned
                              </p>
                              <Button variant="ghost" size="sm" className="ml-2 h-7 px-2">
                                <PlusCircle className="h-3.5 w-3.5 mr-1" />
                                Assign
                              </Button>
                            </div>
                          </div>

                          <div>
                            <h4 className="text-sm font-semibold mb-2 flex items-center gap-1">
                              <MapPin className="h-4 w-4" /> Regions Served
                            </h4>

                            <div className="flex flex-wrap gap-1">
                              {company.regionsServed.map((region: any) => (
                                <Badge key={region} variant="secondary" className="font-normal">
                                  {region}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h4 className="text-sm font-semibold mb-2 flex items-center gap-1">
                              <Clock className="h-4 w-4" /> Response Time
                            </h4>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {company.reviewTimeAverage
                                  ? `${company.reviewTimeAverage.toFixed(1)} days`
                                  : 'Unknown'}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {getResponseTimeLabel(company.reviewTimeAverage)}
                              </span>
                            </div>
                          </div>

                          <div>
                            <h4 className="text-sm font-semibold mb-2 flex items-center gap-1">
                              <GanttChart className="h-4 w-4" /> Relocation Time
                            </h4>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {company.relocationTimeAverage
                                  ? `${company.relocationTimeAverage.toFixed(1)} days`
                                  : 'Unknown'}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {getRelocationTimeLabel(company.relocationTimeAverage)}
                              </span>
                            </div>
                          </div>

                          <div>
                            <h4 className="text-sm font-semibold mb-2 flex items-center gap-1">
                              <Cable className="h-4 w-4" /> Total Contacts
                            </h4>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{company.contacts?.length || 0}</Badge>
                              <Button variant="ghost" size="sm" className="h-7 px-2.5">
                                <Eye className="h-3.5 w-3.5 mr-1" />
                                View All
                              </Button>
                            </div>
                          </div>
                        </div>

                        <div className="mt-4">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-semibold flex items-center gap-1">
                              <FileWarning className="h-4 w-4" /> Special Requirements
                            </h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 px-2"
                              onClick={() => handleEditNotes(company)}
                            >
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                          </div>

                          <div className="mt-1 p-2 border rounded-md bg-slate-50 dark:bg-slate-900 text-sm max-h-20 overflow-y-auto">
                            {company.specialNotes || 'No special requirements noted.'}
                          </div>
                        </div>
                      </CardContent>

                      <CardFooter className="flex justify-between pt-2">
                        <Button variant="ghost" size="sm">
                          <Plus className="h-3.5 w-3.5 mr-1" /> Add Contact
                        </Button>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <FileText className="h-3.5 w-3.5 mr-1" /> Documents
                          </Button>
                          <Button variant="outline" size="sm">
                            <Wrench className="h-3.5 w-3.5 mr-1" /> Manage
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'utility-contacts' && (
            <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Utility Type</TableHead>
                  <TableHead>Response Time</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Role</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUtilityStakeholders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <User className="h-10 w-10 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">
                          {searchQuery
                            ? 'No contacts match your search criteria'
                            : 'No utility contacts found'}
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => setIsAddStakeholderDialogOpen(true)}
                        >
                          Add Contact
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUtilityStakeholders.map((contact: any) => (
                    <TableRow key={contact.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div>
                            <div className="font-medium">{contact.fullName}</div>
                            <div className="text-sm text-muted-foreground">
                              {contact.primaryContact && (
                                <span className="inline-flex items-center text-xs">
                                  <Check className="h-3 w-3 mr-1 text-green-500" />
                                  Primary Contact
                                </span>
                              )}
                            </div>
                          </div>
                          <CommentBadge
                            entityType="stakeholder"
                            entityId={contact.id.toString()}
                            entityName={contact.fullName}
                            variant="icon-only"
                            showZero={false}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>{contact.contactCompany}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-normal">
                          {contact.typeDelivery || 'Unknown'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {contact.responseTimeAverage ? (
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {contact.responseTimeAverage.toFixed(1)} days
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Last:{' '}
                              {contact.lastContactDate
                                ? new Date(contact.lastContactDate).toLocaleDateString()
                                : 'N/A'}
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">No data</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col space-y-1">
                          {contact.email && (
                            <a
                              href={`mailto:${contact.email}`}
                              className="text-sm flex items-center hover:underline"
                            >
                              <Mail className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                              {contact.email}
                            </a>
                          )}
                          {contact.businessPhone && (
                            <a
                              href={`tel:${contact.businessPhone}`}
                              className="text-sm flex items-center hover:underline"
                            >
                              <Phone className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                              {contact.businessPhone}
                            </a>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {contact.isUtilityCoordinator && (
                            <Badge className="bg-blue-100 text-blue-800 border-blue-200">UC</Badge>
                          )}
                          {contact.emergencyContact && (
                            <Badge className="bg-red-100 text-red-800 border-red-200">
                              Emergency
                            </Badge>
                          )}
                          {contact.stakeholderType === 'Utility Company' && (
                            <Badge className="bg-green-100 text-green-800 border-green-200">
                              Utility
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </ScrollArea>
      </CardContent>

      {/* Edit Notes Dialog */}
      <Dialog open={isEditNotesDialogOpen} onOpenChange={setIsEditNotesDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Utility Requirements</DialogTitle>
            <DialogDescription>
              Update special requirements and notes for this utility company.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Label htmlFor="specialNotes" className="text-sm font-medium mb-2 block">
              Special Requirements & Notes
            </Label>
            <Textarea
              id="specialNotes"
              value={utilityNotes}
              onChange={(e: any) => setUtilityNotes(e.target.value)}
              rows={6}
              placeholder="Enter special requirements, permitting details, or other important notes about working with this utility company."
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditNotesDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveNotes}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
