'use client';

import * as React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Check, Clock, AlertTriangle, AlertCircle, X } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import { Button } from '~/components/ui/button';
import {
  type ProjectRecommendation,
  generateProjectRecommendations,
  getPriorityColor,
} from '~/lib/recommendation-engine';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Textarea } from '~/components/ui/textarea';
import { useToast } from '~/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';

interface ProjectRecommendationsProps {
  projectId: string;
  project: any;
}

export function ProjectRecommendations({ projectId, project }: ProjectRecommendationsProps) {
  const [selectedTab, setSelectedTab] = React.useState<string>('all');

  // Fetch related project data for generating recommendations
  const { data: documentsData } = useQuery<{ count: number; expected: number }>({
    queryKey: [`/api/projects/${projectId}/document-stats`],
    enabled: !!projectId,
    queryFn: async () => {
      // Mock data for development
      return { count: 5, expected: 10 };
    },
  });

  const { data: conflictData } = useQuery<{ total: number; resolved: number }>({
    queryKey: [`/api/projects/${projectId}/conflict-stats`],
    enabled: !!projectId,
    queryFn: async () => {
      // Mock data for development
      return { total: 8, resolved: 3 };
    },
  });

  const { data: communicationData } = useQuery<{ lastCommunication: string | null }>({
    queryKey: [`/api/projects/${projectId}/communication-stats`],
    enabled: !!projectId,
    queryFn: async () => {
      // Mock data for development
      return { lastCommunication: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString() };
    },
  });

  // Generate recommendations based on project data
  const recommendations = React.useMemo(() => {
    return generateProjectRecommendations(
      project,
      documentsData?.count || 0,
      documentsData?.expected || 0,
      conflictData?.total || 0,
      conflictData?.resolved || 0,
      communicationData?.lastCommunication
    );
  }, [project, documentsData, conflictData, communicationData]);

  // Filter recommendations based on selected tab
  const filteredRecommendations = React.useMemo(() => {
    if (selectedTab === 'all') return recommendations;
    return recommendations.filter((rec: any) => rec.priority === selectedTab);
  }, [recommendations, selectedTab]);

  // Count recommendations by priority
  const counts = React.useMemo(() => {
    return {
      high: recommendations.filter((r: any) => r.priority === 'high').length,
      medium: recommendations.filter((r: any) => r.priority === 'medium').length,
      low: recommendations.filter((r: any) => r.priority === 'low').length,
      all: recommendations.length,
    };
  }, [recommendations]);

  if (recommendations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Recommendations</CardTitle>
          <CardDescription>Personalized actions based on project health</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <div className="mb-4 rounded-full bg-green-100 p-3">
              <Check className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="mb-1 text-xl font-semibold">Project on track</h3>
            <p className="text-sm text-muted-foreground">
              No recommendations needed at this time. All project metrics look healthy.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Project Recommendations</CardTitle>
            <CardDescription>Personalized actions based on project health</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              Recommendations based on project data and SOP guidelines
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all" className="flex items-center justify-center">
              All
              <Badge variant="outline" className="ml-2">
                {counts.all}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="high" className="flex items-center justify-center">
              <AlertCircle className="mr-1 h-4 w-4 text-destructive" />
              High
              <Badge variant="outline" className="ml-2">
                {counts.high}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="medium" className="flex items-center justify-center">
              <AlertTriangle className="mr-1 h-4 w-4 text-amber-500" />
              Medium
              <Badge variant="outline" className="ml-2">
                {counts.medium}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="low" className="flex items-center justify-center">
              <Clock className="mr-1 h-4 w-4 text-blue-500" />
              Low
              <Badge variant="outline" className="ml-2">
                {counts.low}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value={selectedTab} className="py-4">
            <Accordion type="single" collapsible className="w-full">
              {filteredRecommendations.map((recommendation, index: number) => (
                <RecommendationItem
                  key={recommendation.id}
                  recommendation={recommendation}
                  defaultOpen={index === 0}
                  projectId={projectId}
                />
              ))}
            </Accordion>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

interface RecommendationItemProps {
  recommendation: ProjectRecommendation;
  defaultOpen?: boolean;
  projectId: string;
}

function RecommendationItem({
  recommendation,
  defaultOpen = false,
  projectId,
}: RecommendationItemProps) {
  const [dismissDialogOpen, setDismissDialogOpen] = React.useState<boolean>(false);
  const [implementDialogOpen, setImplementDialogOpen] = React.useState<boolean>(false);
  const [dismissReason, setDismissReason] = React.useState<string>('');
  const [implementNotes, setImplementNotes] = React.useState<string>('');
  const [implementAssignee, setImplementAssignee] = React.useState<string>('');
  const [selectedUserId, setSelectedUserId] = React.useState<string>('');
  const [implementDueDate, setImplementDueDate] = React.useState<string>('');
  const [isDismissed, setIsDismissed] = React.useState<boolean>(false);
  const [isImplemented, setIsImplemented] = React.useState<boolean>(false);
  const [searchQuery, setSearchQuery] = React.useState<string>('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch users for assignee dropdown
  const { data: users } = useQuery<any[]>({
    queryKey: ['/api/users'],
    queryFn: async () => {
      // Mock data for development
      return [
        { id: 1, firstName: 'John', lastName: 'Smith' },
        { id: 2, firstName: 'Jane', lastName: 'Doe' },
        { id: 3, firstName: 'Mike', lastName: 'Wilson' },
      ];
    },
  });

  // Set current user as default
  React.useEffect(() => {
    if (users && users.length > 0 && !selectedUserId) {
      setSelectedUserId(users[0].id.toString());
    }
  }, [users, selectedUserId]);

  // Mutation for dismissing a recommendation
  const dismissMutation = useMutation({
    mutationFn: async ({
      recommendationId,
      reason,
    }: {
      recommendationId: string;
      reason: string;
    }) => {
      // In a real implementation, this would be an API call
      return { success: true };
    },
    onSuccess: () => {
      // Close dialog and mark as dismissed
      setDismissDialogOpen(false);
      setIsDismissed(true);

      // Show success message
      toast({
        title: 'Recommendation dismissed',
        description: "This recommendation won't appear again for this project.",
      });

      // Invalidate project logs to refresh them if they're being viewed
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/logs`] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: `Failed to dismiss recommendation`,
        variant: 'destructive',
      });
    },
  });

  // Mutation for implementing a recommendation
  const implementMutation = useMutation({
    mutationFn: async ({
      recommendationId,
      notes,
      assignee,
      dueDate,
      assignedToId,
    }: {
      recommendationId: string;
      notes: string;
      assignee: string;
      dueDate: string;
      assignedToId: number;
    }) => {
      // In a real implementation, this would create tasks and log the activity
      return { success: true };
    },
    onSuccess: () => {
      // Close dialog and mark as implemented
      setImplementDialogOpen(false);
      setIsImplemented(true);

      // Show success message with information about tasks created
      const hasActionItems = recommendation.actionItems && recommendation.actionItems.length > 0;
      toast({
        title: 'Recommendation implemented',
        description: hasActionItems
          ? `The recommendation has been logged, and ${recommendation.actionItems?.length || 0} task(s) have been created.`
          : 'The recommendation has been marked as implemented and logged in project activity.',
      });

      // Invalidate tasks and logs
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/upcoming'] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/logs`] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: `Failed to implement recommendation`,
        variant: 'destructive',
      });
    },
  });

  // Handle dismiss button click
  const handleDismiss = () => {
    setDismissDialogOpen(true);
  };

  // Handle implement button click
  const handleImplement = () => {
    setImplementDialogOpen(true);
  };

  // Handle dismiss dialog confirm
  const handleDismissConfirm = () => {
    dismissMutation.mutate({
      recommendationId: recommendation.id,
      reason: dismissReason,
    });
  };

  // Handle implement dialog confirm
  const handleImplementConfirm = () => {
    // Validate that a user is selected
    if (!selectedUserId) {
      toast({
        title: 'Error',
        description: 'Please select a user to assign the tasks to',
        variant: 'destructive',
      });
      return;
    }

    // Find the selected user to get their name for the log
    const selectedUser = users?.find((user: any) => user.id === parseInt(selectedUserId));
    const assigneeName = selectedUser
      ? `${selectedUser.firstName} ${selectedUser.lastName}`
      : implementAssignee;

    implementMutation.mutate({
      recommendationId: recommendation.id,
      notes: implementNotes,
      assignee: assigneeName,
      dueDate: implementDueDate,
      assignedToId: parseInt(selectedUserId),
    });
  };

  // If this recommendation is dismissed or implemented, don't render it
  if (isDismissed || isImplemented) {
    return null;
  }

  return (
    <>
      <AccordionItem value={recommendation.id}>
        <AccordionTrigger className="hover:no-underline">
          <div className="flex items-center gap-3 text-left">
            {recommendation.priority === 'high' && (
              <Badge variant="destructive" className="shrink-0">
                High Priority
              </Badge>
            )}
            {recommendation.priority === 'medium' && (
              <Badge variant="outline" className="border-amber-500 text-amber-600 shrink-0">
                Medium Priority
              </Badge>
            )}
            {recommendation.priority === 'low' && (
              <Badge variant="outline" className="border-blue-500 text-blue-600 shrink-0">
                Low Priority
              </Badge>
            )}
            <span className="font-medium">{recommendation.title}</span>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <div className="pt-2 pb-4">
            <p className="text-sm text-muted-foreground mb-4">{recommendation.description}</p>

            {recommendation.actionItems && recommendation.actionItems.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Action Items:</h4>
                <ul className="text-sm space-y-1 list-disc pl-5">
                  {recommendation.actionItems.map((item, i: number) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="flex flex-wrap gap-4 text-sm">
              <div>
                <span className="font-medium">Category: </span>
                <Badge variant="secondary" className="capitalize">
                  {recommendation.category}
                </Badge>
              </div>
              <div>
                <span className="font-medium">Impact: </span>
                <span className="text-muted-foreground">{recommendation.impact}</span>
              </div>
              <div>
                <span className="font-medium">Effort: </span>
                <Badge variant="outline" className="capitalize">
                  {recommendation.effort}
                </Badge>
              </div>
            </div>

            <div className="mt-4 flex gap-2">
              <Button
                size="sm"
                variant="default"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  handleImplement();
                }}
              >
                Implement
              </Button>
              <Button size="sm" variant="outline">
                Defer
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="ml-auto text-muted-foreground"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  handleDismiss();
                }}
              >
                <X className="h-4 w-4 mr-1" />
                Dismiss
              </Button>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>

      {/* Dismiss Dialog */}
      <Dialog open={dismissDialogOpen} onOpenChange={setDismissDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dismiss Recommendation</DialogTitle>
            <DialogDescription>
              This recommendation won&apos;t appear again for this project. You can optionally provide a
              reason for dismissing it.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Textarea
              placeholder="Reason for dismissal (optional)"
              value={dismissReason}
              onChange={(e: any) => setDismissReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDismissDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleDismissConfirm} disabled={dismissMutation.isPending}>
              {dismissMutation.isPending ? 'Dismissing...' : 'Confirm Dismiss'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Implement Dialog */}
      <Dialog open={implementDialogOpen} onOpenChange={setImplementDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Implement Recommendation</DialogTitle>
            <DialogDescription>
              Fill out this form to track how you&apos;ll implement this recommendation. Tasks will be
              created and added to the assigned user&apos;s task list.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="grid w-full items-center gap-1.5">
              <label htmlFor="assignee" className="text-sm font-medium">
                Assignee
              </label>
              <Select onValueChange={setSelectedUserId} value={selectedUserId}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a user to assign" />
                </SelectTrigger>
                <SelectContent className="max-h-[300px]">
                  <div className="p-2 sticky top-0 bg-background z-10">
                    <input
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Search users..."
                      value={searchQuery}
                      onChange={(e: any) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <SelectGroup className="overflow-y-auto max-h-[200px]">
                    {users
                      ?.filter((user: any) => {
                        if (!searchQuery) return true;
                        const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
                        return fullName.includes(searchQuery.toLowerCase());
                      })
                      .map((user: any) => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {user.firstName} {user.lastName}
                        </SelectItem>
                      ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">
                Tasks created from action items will be assigned to this user
              </p>
            </div>

            <div className="grid w-full items-center gap-1.5">
              <label htmlFor="due-date" className="text-sm font-medium">
                Due Date
              </label>
              <input
                id="due-date"
                type="date"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={implementDueDate}
                onChange={(e: any) => setImplementDueDate(e.target.value)}
              />
            </div>

            <div className="grid w-full items-center gap-1.5">
              <label htmlFor="implementation-notes" className="text-sm font-medium">
                Implementation Notes
              </label>
              <Textarea
                id="implementation-notes"
                placeholder="How will you implement this recommendation?"
                value={implementNotes}
                onChange={(e: any) => setImplementNotes(e.target.value)}
                className="min-h-[120px]"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setImplementDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleImplementConfirm}
              disabled={implementMutation.isPending || !selectedUserId}
            >
              {implementMutation.isPending ? 'Saving...' : 'Confirm Implementation'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
