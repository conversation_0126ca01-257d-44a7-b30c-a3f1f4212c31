'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { useSupabaseRealtime } from '~/hooks/use-supabase-realtime';
import { toast } from '~/hooks/use-toast';

interface ProjectRealtimeUpdatesProps {
  projectId: string;
  organizationId?: string;
  showActivityFeed?: boolean;
}

/**
 * Real-time updates component for project pages
 * Displays live updates for conflicts, utilities, and communications
 */
export function ProjectRealtimeUpdates({ 
  projectId, 
  organizationId, 
  showActivityFeed = true 
}: ProjectRealtimeUpdatesProps) {
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);
  const [conflictCount, setConflictCount] = useState(0);
  const [utilityCount, setUtilityCount] = useState(0);
  const [communicationCount, setCommunicationCount] = useState(0);

  const realtime = useSupabaseRealtime(projectId, organizationId);

  // Handle real-time events using custom event listeners
  useEffect(() => {
    const handleConflictDetected = (event: any) => {
      const conflict = event.detail;
      setConflictCount(prev => prev + 1);
      setRecentUpdates(prev => [
        {
          id: `conflict-${Date.now()}`,
          type: 'Conflict Detected',
          description: `New utility conflict found`,
          timestamp: new Date().toISOString(),
          severity: 'high',
        },
        ...prev.slice(0, 9)
      ]);
      
      toast({
        title: "New Conflict Detected",
        description: "A utility conflict has been identified in your project.",
        variant: "destructive",
      });
    };

    const handleProjectUpdated = (event: any) => {
      const update = event.detail;
      if (update.eventType === 'INSERT') {
        setRecentUpdates(prev => [
          {
            id: `project-${Date.now()}`,
            type: 'Project Updated',
            description: `Project information changed`,
            timestamp: new Date().toISOString(),
            severity: 'medium',
          },
          ...prev.slice(0, 9)
        ]);
      }
    };

    const handleNotificationReceived = (event: any) => {
      const notification = event.detail;
      setRecentUpdates(prev => [
        {
          id: `notification-${Date.now()}`,
          type: 'New Notification',
          description: notification.title || 'New notification received',
          timestamp: new Date().toISOString(),
          severity: 'medium',
        },
        ...prev.slice(0, 9)
      ]);
    };

    // Add event listeners
    window.addEventListener('conflict-detected', handleConflictDetected);
    window.addEventListener('project-updated', handleProjectUpdated);
    window.addEventListener('notification-received', handleNotificationReceived);

    return () => {
      window.removeEventListener('conflict-detected', handleConflictDetected);
      window.removeEventListener('project-updated', handleProjectUpdated);
      window.removeEventListener('notification-received', handleNotificationReceived);
    };
  }, []);

  // Handle connection errors
  useEffect(() => {
    if (realtime.hasErrors) {
      toast({
        title: "Connection Issue",
        description: "Real-time updates may be delayed. Refreshing page may help.",
        variant: "destructive",
      });
    }
  }, [realtime.hasErrors]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Real-time Status</span>
            <RealtimeIndicator showDetails />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{conflictCount}</div>
              <div className="text-sm text-muted-foreground">New Conflicts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{utilityCount}</div>
              <div className="text-sm text-muted-foreground">Utility Updates</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{communicationCount}</div>
              <div className="text-sm text-muted-foreground">New Messages</div>
            </div>
          </div>
          
          {!realtime.isConnected && (
            <Alert className="mt-4">
              <AlertDescription>
                Real-time updates are currently unavailable. Data may not be up to date.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Activity Feed */}
      {showActivityFeed && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Recent Activity</span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setRecentUpdates([])}
              >
                Clear
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {recentUpdates.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                No recent activity
              </p>
            ) : (
              <div className="space-y-2">
                {recentUpdates.map((update: any) => (
                  <div 
                    key={update.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{update.type}</span>
                        <Badge 
                          variant="outline" 
                          className={getSeverityColor(update.severity)}
                        >
                          {update.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{update.description}</p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(update.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

/**
 * Lightweight real-time badge for showing live update counts
 * This component only listens to events, doesn't create its own subscriptions
 */
export function ProjectRealtimeBadge({ projectId }: { projectId: string }) {
  const [updateCount, setUpdateCount] = useState(0);
  // Removed duplicate useSupabaseRealtime call - parent component handles subscriptions

  useEffect(() => {
    const handleUpdate = () => {
      setUpdateCount(prev => prev + 1);
    };

    window.addEventListener('conflict-detected', handleUpdate);
    window.addEventListener('project-updated', handleUpdate);
    window.addEventListener('notification-received', handleUpdate);

    return () => {
      window.removeEventListener('conflict-detected', handleUpdate);
      window.removeEventListener('project-updated', handleUpdate);
      window.removeEventListener('notification-received', handleUpdate);
    };
  }, []);

  if (updateCount === 0) {
    return null;
  }

  return (
    <Badge variant="destructive" className="animate-pulse">
      {updateCount} new
    </Badge>
  );
}