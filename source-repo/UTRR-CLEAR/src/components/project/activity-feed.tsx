'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { formatDate } from '~/lib/utils';
import {
  Clock,
  MessageSquare,
  FileText,
  MapPin,
  Users,
  AlertTriangle,
  CheckCircle,
  Upload,
  Download,
  Edit,
  Trash2,
  Search,
  Filter,
  RefreshCw,
} from 'lucide-react';

// Activity types and their configurations
const ACTIVITY_TYPES = {
  comment: {
    icon: MessageSquare,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    label: 'Comment',
  },
  document: {
    icon: FileText,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    label: 'Document',
  },
  mapping: {
    icon: MapPin,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    label: 'Mapping',
  },
  meeting: {
    icon: Users,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    label: 'Meeting',
  },
  conflict: {
    icon: AlertTriangle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    label: 'Conflict',
  },
  milestone: {
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    label: 'Milestone',
  },
  upload: {
    icon: Upload,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
    label: 'Upload',
  },
  update: {
    icon: Edit,
    color: 'text-muted-foreground',
    bgColor: 'bg-muted',
    label: 'Update',
  },
};

// Activity item interface
interface ActivityItem {
  id: string;
  type: keyof typeof ACTIVITY_TYPES;
  title: string;
  description?: string;
  user: {
    name: string;
    avatar?: string;
    initials: string;
  };
  timestamp: string;
  metadata?: {
    fileName?: string;
    fileSize?: string;
    conflictSeverity?: 'low' | 'medium' | 'high' | 'critical';
    meetingType?: string;
    location?: string;
    status?: string;
  };
  relatedItems?: Array<{
    type: string;
    name: string;
    url?: string;
  }>;
}

interface ActivityFeedProps {
  projectId?: string;
  activities?: ActivityItem[];
  realTime?: boolean;
  maxHeight?: string;
}

export function ActivityFeed({
  projectId,
  activities: propActivities,
  realTime = true,
  maxHeight = '600px',
}: ActivityFeedProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterUser, setFilterUser] = useState<string>('all');

  // Mock activities data - would be replaced with real data from tRPC
  const mockActivities: ActivityItem[] = [
    {
      id: '1',
      type: 'conflict',
      title: 'High severity conflict detected',
      description: 'Electric line crosses gas main at Station 15+00. Immediate attention required.',
      user: { name: 'System Alert', initials: 'SA' },
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      metadata: {
        conflictSeverity: 'high',
        location: 'Station 15+00',
      },
    },
    {
      id: '2',
      type: 'comment',
      title: 'Added comment on utility coordination',
      description:
        "Confirmed Duke Energy's availability for next week's field visit. They can provide crew on Tuesday and Wednesday.",
      user: { name: 'Sarah Johnson', initials: 'SJ', avatar: '/avatars/sarah.jpg' },
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    },
    {
      id: '3',
      type: 'document',
      title: 'Uploaded utility agreement',
      description: 'Duke Energy - Power Relocation Agreement signed and executed.',
      user: { name: 'Mike Chen', initials: 'MC' },
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      metadata: {
        fileName: 'duke-energy-agreement.pdf',
        fileSize: '2.4 MB',
      },
    },
    {
      id: '4',
      type: 'meeting',
      title: 'Utility coordination meeting scheduled',
      description: 'Weekly coordination meeting with all utility stakeholders.',
      user: { name: 'Project Manager', initials: 'PM' },
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      metadata: {
        meetingType: 'Coordination',
        location: 'Conference Room A',
      },
    },
    {
      id: '5',
      type: 'mapping',
      title: 'Updated utility mapping data',
      description: 'Added SUE Quality Level B data for water main between stations 10+00 to 12+50.',
      user: { name: 'John Smith', initials: 'JS' },
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
    },
    {
      id: '6',
      type: 'milestone',
      title: 'Phase 1 Design Review completed',
      description: 'All utility conflicts identified and preliminary solutions proposed.',
      user: { name: 'Design Team', initials: 'DT' },
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      metadata: {
        status: 'completed',
      },
    },
  ];

  const activities = propActivities || mockActivities;

  // Filter activities based on search and filters
  const filteredActivities = activities.filter((activity: any) => {
    const matchesSearch =
      searchQuery === '' ||
      activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.user.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = filterType === 'all' || activity.type === filterType;

    const matchesUser = filterUser === 'all' || activity.user.name === filterUser;

    return matchesSearch && matchesType && matchesUser;
  });

  // Get unique users for filter
  const uniqueUsers = Array.from(new Set(activities.map((a: any) => a.user.name)));

  // Format relative time
  const getRelativeTime = (timestamp: string): string => {
    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffInMs = now.getTime() - activityTime.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(activityTime);
  };

  // Get severity badge variant
  const getSeverityVariant = (severity?: string) => {
    switch (severity) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      case 'low':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Activity Feed
            {realTime && (
              <Badge variant="outline" className="text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                Live
              </Badge>
            )}
          </CardTitle>
          <Button variant="ghost" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search activities..."
              value={searchQuery}
              onChange={(e: any) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              {Object.entries(ACTIVITY_TYPES).map(([key, config]) => (
                <SelectItem key={key} value={key}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterUser} onValueChange={setFilterUser}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="All users" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All users</SelectItem>
              {uniqueUsers.map((user: any) => (
                <SelectItem key={user} value={user}>
                  {user}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="w-full" style={{ height: maxHeight }}>
          <div className="p-6 pt-0">
            {filteredActivities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No activities found</p>
                {searchQuery && <p className="text-sm">Try adjusting your search or filters</p>}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredActivities.map((activity, index) => {
                  const config = ACTIVITY_TYPES[activity.type];
                  const Icon = config.icon;

                  return (
                    <div key={activity.id} className="flex gap-3 group">
                      {/* Timeline */}
                      <div className="flex flex-col items-center">
                        <div className={`p-2 rounded-full ${config.bgColor} ${config.color}`}>
                          <Icon className="h-4 w-4" />
                        </div>
                        {index < filteredActivities.length - 1 && (
                          <div className="w-px h-8 bg-border mt-2"></div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0 pb-4">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-sm">{activity.title}</h4>
                              {activity.metadata?.conflictSeverity && (
                                <Badge
                                  variant={getSeverityVariant(activity.metadata.conflictSeverity)}
                                  className="text-xs"
                                >
                                  {activity.metadata.conflictSeverity}
                                </Badge>
                              )}
                              {activity.metadata?.status && (
                                <Badge variant="outline" className="text-xs">
                                  {activity.metadata.status}
                                </Badge>
                              )}
                            </div>

                            {activity.description && (
                              <p className="text-sm text-muted-foreground mb-2">
                                {activity.description}
                              </p>
                            )}

                            {/* Metadata */}
                            {activity.metadata && (
                              <div className="flex flex-wrap gap-2 text-xs text-muted-foreground mb-2">
                                {activity.metadata.fileName && (
                                  <span className="flex items-center gap-1">
                                    <FileText className="h-3 w-3" />
                                    {activity.metadata.fileName}
                                    {activity.metadata.fileSize &&
                                      ` (${activity.metadata.fileSize})`}
                                  </span>
                                )}
                                {activity.metadata.location && (
                                  <span className="flex items-center gap-1">
                                    <MapPin className="h-3 w-3" />
                                    {activity.metadata.location}
                                  </span>
                                )}
                                {activity.metadata.meetingType && (
                                  <span className="flex items-center gap-1">
                                    <Users className="h-3 w-3" />
                                    {activity.metadata.meetingType}
                                  </span>
                                )}
                              </div>
                            )}

                            {/* Related items */}
                            {activity.relatedItems && activity.relatedItems.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {activity.relatedItems.map((item, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs">
                                    {item.name}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>

                          <div className="text-xs text-muted-foreground whitespace-nowrap">
                            {getRelativeTime(activity.timestamp)}
                          </div>
                        </div>

                        {/* User info */}
                        <div className="flex items-center gap-2 mt-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={activity.user.avatar} />
                            <AvatarFallback className="text-xs">
                              {activity.user.initials}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-muted-foreground">
                            {activity.user.name}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
