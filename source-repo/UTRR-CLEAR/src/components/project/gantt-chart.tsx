"use client";

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { format, addDays, differenceInDays, startOfMonth, endOfMonth, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval, isWeekend } from 'date-fns';
import { cn } from '~/lib/utils';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Separator } from '~/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { Calendar, ChevronLeft, ChevronRight, Clock, User, Flag, BarChart3 } from 'lucide-react';

export interface GanttTask {
  id: string;
  name: string;
  start: string | null;
  end: string | null;
  duration: number;
  progress: number; // 0-1
  dependencies: string[];
  type: 'task' | 'milestone';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: string;
  assignee: string | null;
  phase: string | null;
  criticalPath: boolean;
  estimatedHours: number | null;
  actualHours: number | null;
}

export interface GanttData {
  projectId: string;
  tasks: GanttTask[];
  timeline: {
    start: string | null;
    end: string | null;
  };
  stats: {
    totalTasks: number;
    completedTasks: number;
    milestoneTasks: number;
    criticalPathTasks: number;
  };
}

interface GanttChartProps {
  data: GanttData;
  onTaskUpdate?: (taskId: string, updates: { startDate?: string; endDate?: string; duration?: number }) => void;
  className?: string;
}

type ViewMode = 'days' | 'weeks' | 'months';

const GanttChart: React.FC<GanttChartProps> = ({ data, onTaskUpdate, className }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('weeks');
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Calculate timeline bounds
  const timelineBounds = useMemo(() => {
    const today = new Date();
    const validTasks = data.tasks.filter(task => task.start || task.end);
    
    if (validTasks.length === 0) {
      const start = startOfMonth(today);
      const end = endOfMonth(addDays(today, 90));
      return { start, end };
    }

    const taskDates = validTasks.flatMap((task: any) => [
      task.start ? new Date(task.start) : null,
      task.end ? new Date(task.end) : null,
    ]).filter(Boolean) as Date[];

    const minDate = new Date(Math.min(...taskDates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...taskDates.map(d => d.getTime())));

    // Add padding
    const start = addDays(minDate, -7);
    const end = addDays(maxDate, 14);

    return { start, end };
  }, [data.tasks]);

  // Generate time periods based on view mode
  const timePeriods = useMemo(() => {
    const { start, end } = timelineBounds;
    
    switch (viewMode) {
      case 'days':
        return eachDayOfInterval({ start, end });
      case 'weeks':
        return eachWeekOfInterval({ start, end });
      case 'months':
        return eachMonthOfInterval({ start, end });
      default:
        return eachWeekOfInterval({ start, end });
    }
  }, [timelineBounds, viewMode]);

  const columnWidth = viewMode === 'days' ? 40 : viewMode === 'weeks' ? 80 : 120;
  const rowHeight = 40;

  // Calculate task positions
  const getTaskPosition = (task: GanttTask) => {
    if (!task.start) return null;

    const taskStart = new Date(task.start);
    const taskEnd = task.end ? new Date(task.end) : addDays(taskStart, task.duration || 1);
    
    const startDay = differenceInDays(taskStart, timelineBounds.start);
    const duration = differenceInDays(taskEnd, taskStart) + 1;
    
    const left = (startDay * columnWidth) / (viewMode === 'days' ? 1 : viewMode === 'weeks' ? 7 : 30);
    const width = (duration * columnWidth) / (viewMode === 'days' ? 1 : viewMode === 'weeks' ? 7 : 30);
    
    return { left: Math.max(0, left), width: Math.max(10, width) };
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-blue-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'complete':
      case 'done':
        return 'bg-green-100 border-green-300';
      case 'in progress':
      case 'working':
        return 'bg-blue-100 border-blue-300';
      case 'blocked':
      case 'stuck':
        return 'bg-red-100 border-red-300';
      case 'on hold':
      case 'waiting':
        return 'bg-yellow-100 border-yellow-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  };

  const formatPeriodLabel = (date: Date) => {
    switch (viewMode) {
      case 'days':
        return format(date, 'MMM d');
      case 'weeks':
        return format(date, 'MMM d');
      case 'months':
        return format(date, 'MMM yyyy');
      default:
        return format(date, 'MMM d');
    }
  };

  return (
    <TooltipProvider>
      <Card className={cn("w-full", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Project Gantt Chart
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{data.stats.totalTasks} tasks</span>
                <Separator orientation="vertical" className="h-4" />
                <span>{data.stats.completedTasks} completed</span>
                <Separator orientation="vertical" className="h-4" />
                <span>{data.stats.milestoneTasks} milestones</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === 'days' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('days')}
                  className="rounded-r-none"
                >
                  Days
                </Button>
                <Button
                  variant={viewMode === 'weeks' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('weeks')}
                  className="rounded-none"
                >
                  Weeks
                </Button>
                <Button
                  variant={viewMode === 'months' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('months')}
                  className="rounded-l-none"
                >
                  Months
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <div className="flex border-b">
            {/* Task list column */}
            <div className="w-80 border-r bg-muted/30">
              {/* Header */}
              <div className="h-12 flex items-center px-4 border-b bg-background font-medium">
                Task Name
              </div>
              
              {/* Task rows */}
              <div className="max-h-96 overflow-y-auto">
                {data.tasks.map((task, index) => (
                  <div
                    key={task.id}
                    className={cn(
                      "h-10 flex items-center px-4 border-b cursor-pointer hover:bg-muted/50 transition-colors",
                      selectedTask === task.id && "bg-muted"
                    )}
                    onClick={() => setSelectedTask(selectedTask === task.id ? null : task.id)}
                  >
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      {task.type === 'milestone' && (
                        <Flag className="h-3 w-3 text-yellow-600 flex-shrink-0" />
                      )}
                      {task.criticalPath && (
                        <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0" />
                      )}
                      <span className="truncate text-sm">{task.name}</span>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <Badge variant="outline" className={cn("text-xs px-1 py-0", getPriorityColor(task.priority))}>
                        {task.priority?.[0]?.toUpperCase() || 'M'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Timeline area */}
            <div className="flex-1 overflow-x-auto" ref={scrollRef}>
              <div style={{ width: timePeriods.length * columnWidth }}>
                {/* Timeline header */}
                <div className="h-12 flex border-b bg-background">
                  {timePeriods.map((period, index) => (
                    <div
                      key={index}
                      className="border-r flex items-center justify-center text-xs font-medium"
                      style={{ width: columnWidth }}
                    >
                      {formatPeriodLabel(period)}
                    </div>
                  ))}
                </div>

                {/* Task bars */}
                <div className="relative max-h-96 overflow-y-auto">
                  {data.tasks.map((task, taskIndex) => {
                    const position = getTaskPosition(task);
                    
                    return (
                      <div
                        key={task.id}
                        className="h-10 border-b relative"
                        style={{ minWidth: timePeriods.length * columnWidth }}
                      >
                        {/* Weekend columns */}
                        {viewMode === 'days' && timePeriods.map((period, periodIndex) => 
                          isWeekend(period) ? (
                            <div
                              key={periodIndex}
                              className="absolute top-0 h-full bg-gray-50 border-r"
                              style={{
                                left: periodIndex * columnWidth,
                                width: columnWidth
                              }}
                            />
                          ) : null
                        )}

                        {/* Task bar */}
                        {position && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div
                                className={cn(
                                  "absolute top-1 h-8 rounded border-2 cursor-pointer transition-all hover:shadow-md",
                                  getStatusColor(task.status),
                                  task.criticalPath && "border-red-500 shadow-red-200",
                                  selectedTask === task.id && "ring-2 ring-blue-500"
                                )}
                                style={{
                                  left: position.left,
                                  width: position.width
                                }}
                                onClick={() => setSelectedTask(selectedTask === task.id ? null : task.id)}
                              >
                                {/* Progress bar */}
                                {task.progress > 0 && (
                                  <div
                                    className="h-full bg-green-400/40 rounded-sm"
                                    style={{ width: `${task.progress * 100}%` }}
                                  />
                                )}
                                
                                {/* Task content */}
                                <div className="absolute inset-0 flex items-center px-2">
                                  <span className="text-xs font-medium truncate">
                                    {task.name}
                                  </span>
                                  {task.assignee && position.width > 100 && (
                                    <User className="h-3 w-3 ml-auto text-muted-foreground" />
                                  )}
                                </div>

                                {/* Milestone diamond */}
                                {task.type === 'milestone' && (
                                  <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-3 h-3 bg-yellow-500 rotate-45 border border-yellow-600" />
                                )}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent side="top" className="max-w-xs">
                              <div className="space-y-2">
                                <div className="font-medium">{task.name}</div>
                                <div className="grid grid-cols-2 gap-2 text-xs">
                                  <div>Status: {task.status}</div>
                                  <div>Priority: {task.priority}</div>
                                  <div>Progress: {Math.round(task.progress * 100)}%</div>
                                  <div>Duration: {task.duration} days</div>
                                  {task.assignee && <div className="col-span-2">Assigned: {task.assignee}</div>}
                                  {task.estimatedHours && <div>Est: {task.estimatedHours}h</div>}
                                  {task.actualHours && <div>Actual: {task.actualHours}h</div>}
                                </div>
                                {task.start && (
                                  <div className="text-xs text-muted-foreground">
                                    {task.start} → {task.end || 'TBD'}
                                  </div>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="p-4 border-t bg-muted/20">
            <div className="flex items-center gap-6 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-3 h-2 bg-green-400/40 rounded-sm"></div>
                <span>Progress</span>
              </div>
              <div className="flex items-center gap-2">
                <Flag className="h-3 w-3 text-yellow-600" />
                <span>Milestone</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Critical Path</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-2 bg-gray-50 border border-gray-200 rounded-sm"></div>
                <span>Weekend</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

export default GanttChart;