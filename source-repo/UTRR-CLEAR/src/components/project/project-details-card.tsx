'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import {
  Calendar,
  MapPin,
  DollarSign,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle,
  Building,
  Phone,
  Mail,
  FileText,
  Settings,
  TrendingUp,
  Edit,
  Trash2,
} from 'lucide-react';
import Link from 'next/link.js';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';
import { DeleteConfirmationDialog } from '~/components/ui/delete-confirmation-dialog';

interface ProjectDetails {
  id: string;
  name: string;
  client: string;
  description?: string | null;
  status?: 'active' | 'planning' | 'construction' | 'completed';
  ragStatus?: string | null;
  currentPhase?: string | null;
  startDate?: string | Date | null;
  endDate?: string | Date | null;
  location?: string | null;
  coordinationType?: string | null;
  projectFunding?: string | null;

  // Team information
  manager?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  } | null;
  coordinator?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  } | null;

  // Progress metrics
  progress?: number;
  utilities?: {
    total: number;
    coordinated: number;
    pending: number;
  };
  conflicts?: {
    total: number;
    resolved: number;
    critical: number;
  };

  // Financial information
  budget?: {
    total: number;
    spent: number;
    remaining: number;
  } | null;

  // Key dates
  ntpDate?: string | Date | null;
  lettingBidDate?: string | Date | null;

  // Priority items
  highPriorityItems?: number | null;
  mediumPriorityItems?: number | null;
  lowPriorityItems?: number | null;

  // Raw project data fields
  rag_status?: string | null;
  current_phase?: string | null;
  start_date?: string | Date | null;
  end_date?: string | Date | null;
  ntp_date?: string | Date | null;
  letting_bid_date?: string | Date | null;
  high_priority_items?: number | null;
  medium_priority_items?: number | null;
  low_priority_items?: number | null;
  contract_amount?: number | string | null;
  billed_to_date?: number | string | null;
  egis_project_manager?: string | null;
  client_pm?: string | null;
  work_type?: string | null;
  project_priority?: string | null;
  _count?: {
    utilities?: number;
    conflicts?: number;
    tasks?: number;
    issues?: number;
  };
}


// Calculate project progress based on phase
const calculateProjectProgress = (project: any): number => {
  const phase = project.current_phase?.toLowerCase() || '';
  
  // Map phases to progress percentages
  const phaseProgress: Record<string, number> = {
    'planning': 10,
    'design': 25,
    'pre-construction': 40,
    'utility relocation': 60,
    'construction': 75,
    'testing': 90,
    'completed': 100,
    'close out': 100,
  };
  
  // Check for specific phase matches
  for (const [key, value] of Object.entries(phaseProgress)) {
    if (phase.includes(key)) {
      return value;
    }
  }
  
  // If status is completed, return 100
  if (project.status === 'completed' || project.rag_status?.toLowerCase() === 'complete') {
    return 100;
  }
  
  // Default progress based on dates if available
  if (project.start_date && project.end_date) {
    const start = new Date(project.start_date).getTime();
    const end = new Date(project.end_date).getTime();
    const now = Date.now();
    
    if (now >= end) return 100;
    if (now <= start) return 0;
    
    return Math.round(((now - start) / (end - start)) * 100);
  }
  
  return 0;
};

// Get RAG status color based on the status string
  const getRagColor = (status?: string | null) => {
    if (!status) return 'bg-gray-500';
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('red')) return 'bg-red-500';
    if (lowerStatus.includes('amber') || lowerStatus.includes('yellow')) return 'bg-amber-500';
    if (lowerStatus.includes('green')) return 'bg-green-500';
    return 'bg-gray-500';
  };

const statusColors = {
  active: 'default',
  planning: 'secondary',
  construction: 'outline',
  completed: 'secondary',
} as const;

interface ProjectDetailsCardProps {
  projectId?: string;
  project?: any; // Accept project data from parent
}

export function ProjectDetailsCard({ projectId, project: projectData }: ProjectDetailsCardProps) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Fetch project data if not provided
  const { data: fetchedProject } = api.projects.getById.useQuery(
    { id: parseInt(projectId!) },
    { 
      enabled: !!projectId && !projectData && !isNaN(parseInt(projectId!)),
      refetchOnWindowFocus: false 
    }
  );

  // Use provided project data or fetched data
  const rawProject = projectData || fetchedProject;

  if (!rawProject) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <p className="text-muted-foreground">No project data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Transform raw project data to match our interface
  const project: ProjectDetails = {
    id: rawProject.id,
    name: rawProject.name,
    client: rawProject.client,
    description: rawProject.description,
    ragStatus: rawProject.rag_status || rawProject.ragStatus,
    currentPhase: rawProject.current_phase || rawProject.currentPhase,
    startDate: rawProject.start_date || rawProject.startDate,
    endDate: rawProject.end_date || rawProject.endDate,
    ntpDate: rawProject.ntp_date || rawProject.ntpDate,
    lettingBidDate: rawProject.letting_bid_date || rawProject.lettingBidDate,
    highPriorityItems: rawProject.high_priority_items || rawProject.highPriorityItems || 0,
    mediumPriorityItems: rawProject.medium_priority_items || rawProject.mediumPriorityItems || 0,
    lowPriorityItems: rawProject.low_priority_items || rawProject.lowPriorityItems || 0,
    coordinationType: rawProject.coordination_type || rawProject.work_type,
    projectFunding: rawProject.project_funding,
    location: rawProject.location,
    status: rawProject.status || 'active',
    progress: calculateProjectProgress(rawProject),
    utilities: {
      total: rawProject.utilities?.length || rawProject._count?.utilities || 0,
      coordinated: 0, // TODO: Add status field to utilities table
      pending: rawProject.utilities?.length || rawProject._count?.utilities || 0,
    },
    conflicts: {
      total: rawProject.conflicts?.length || rawProject.utility_conflicts?.length || rawProject._count?.conflicts || 0,
      resolved: 0, // TODO: Add status field to conflicts table
      critical: rawProject.high_priority_items || 0,
    },
    budget: (rawProject.contract_amount && rawProject.billed_to_date) ? {
      total: Number(rawProject.contract_amount) || 0,
      spent: Number(rawProject.billed_to_date) || 0,
      remaining: (Number(rawProject.contract_amount) || 0) - (Number(rawProject.billed_to_date) || 0),
    } : null,
    manager: rawProject.egis_project_manager ? {
      id: rawProject.project_manager_id || rawProject.manager_id || '1',
      name: rawProject.egis_project_manager,
      email: rawProject.egis_project_manager_email || `${rawProject.egis_project_manager.toLowerCase().replace(/\s+/g, '.')}@egis-group.com`,
    } : null,
    coordinator: rawProject.coordinator_id ? {
      id: rawProject.coordinator_id,
      name: rawProject.coordinator_id || 'Not assigned',
      email: rawProject.coordinator_email || 'Not available',
    } : null,
  };

  const deleteProjectMutation = api.projects.delete.useMutation({
    onSuccess: () => {
      toast({
        title: 'Project deleted',
        description: 'The project has been successfully deleted.',
      });
      router.push('/projects');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete project. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleDelete = () => {
    if (projectId) {
      deleteProjectMutation.mutate({ id: parseInt(projectId) });
    }
  };

  const budgetPercentage = project.budget && project.budget.total > 0 
    ? (project.budget.spent / project.budget.total) * 100 
    : 0;
  const utilityCoordinationPercentage = project.utilities && project.utilities.total > 0
    ? (project.utilities.coordinated / project.utilities.total) * 100
    : 0;
  const conflictResolutionPercentage = project.conflicts && project.conflicts.total > 0
    ? (project.conflicts.resolved / project.conflicts.total) * 100
    : 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <CardTitle className="text-xl">{project.name}</CardTitle>
              {project.ragStatus && <div className={`w-3 h-3 rounded-full ${getRagColor(project.ragStatus)}`} />}
              {project.status && (
                <Badge variant={statusColors[project.status] || 'default'}>
                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                </Badge>
              )}
            </div>
            {project.description && (
              <CardDescription className="text-sm">{project.description}</CardDescription>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Link href={`/projects/${project.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
            <Button size="sm">
              <FileText className="mr-2 h-4 w-4" />
              Generate Report
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Project Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Building className="h-4 w-4" />
              Client
            </div>
            <div className="font-medium">{project.client}</div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              Location
            </div>
            <div className="font-medium">{project.location || 'Not specified'}</div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              Current Phase
            </div>
            <div className="font-medium">{project.currentPhase || 'Not specified'}</div>
          </div>
        </div>

        {/* Progress Metrics */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Project Progress</h4>

          {/* Overall Progress */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Overall Completion</span>
              <span className="font-medium">{project.progress || 0}%</span>
            </div>
            <Progress value={project.progress || 0} className="h-2" />
          </div>

          {/* Utility Coordination */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Utility Coordination</span>
              <span className="font-medium">
                {project.utilities?.coordinated || 0}/{project.utilities?.total || 0} companies
              </span>
            </div>
            <Progress value={utilityCoordinationPercentage} className="h-2" />
          </div>

          {/* Conflict Resolution */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Conflict Resolution</span>
              <span className="font-medium">
                {project.conflicts?.resolved || 0}/{project.conflicts?.total || 0} resolved
              </span>
            </div>
            <Progress value={conflictResolutionPercentage} className="h-2" />
          </div>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-muted/30 rounded-lg">
            <div className="text-lg font-bold">{project.utilities?.total || 0}</div>
            <div className="text-xs text-muted-foreground">Total Utilities</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded-lg">
            <div className="text-lg font-bold text-amber-600">{project.conflicts?.critical || 0}</div>
            <div className="text-xs text-muted-foreground">Critical Conflicts</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded-lg">
            <div className="text-lg font-bold text-red-600">{project.highPriorityItems || 0}</div>
            <div className="text-xs text-muted-foreground">High Priority</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded-lg">
            <div className="text-lg font-bold">{project.utilities?.pending || 0}</div>
            <div className="text-xs text-muted-foreground">Pending Actions</div>
          </div>
        </div>

        {/* Team Information */}
        {(project.manager || project.coordinator) && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Project Team</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {project.manager && (
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={project.manager.avatar} />
                    <AvatarFallback>
                      {project.manager.name
                        .split(' ')
                        .map((n: any) => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{project.manager.name}</div>
                    <div className="text-xs text-muted-foreground">Project Manager</div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                      <Mail className="h-3 w-3" />
                      {project.manager.email}
                    </div>
                  </div>
                </div>
              )}

              {project.coordinator && (
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={project.coordinator.avatar} />
                    <AvatarFallback>
                      {project.coordinator.name
                        .split(' ')
                        .map((n: any) => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{project.coordinator.name}</div>
                    <div className="text-xs text-muted-foreground">Utility Coordinator</div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                      <Mail className="h-3 w-3" />
                      {project.coordinator.email}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Budget Information */}
        {project.budget && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Budget Overview</h4>

            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold">
                  ${(project.budget.total / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-muted-foreground">Total Budget</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-600">
                  ${(project.budget.spent / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-muted-foreground">Spent</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600">
                  ${(project.budget.remaining / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-muted-foreground">Remaining</div>
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Budget Utilization</span>
                <span className="font-medium">{budgetPercentage.toFixed(1)}%</span>
              </div>
              <Progress value={budgetPercentage} className="h-2" />
            </div>
          </div>
        )}

        {/* Key Dates */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Key Dates</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Project Start:</span>
              {project.startDate && (
                <span className="font-medium">
                  {new Date(project.startDate).toLocaleDateString()}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Project End:</span>
              {project.endDate && (
                <span className="font-medium">{new Date(project.endDate).toLocaleDateString()}</span>
              )}
            </div>
            {project.ntpDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">NTP Date:</span>
                <span className="font-medium">
                  {new Date(project.ntpDate).toLocaleDateString()}
                </span>
              </div>
            )}
            {project.lettingBidDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Letting/Bid:</span>
                <span className="font-medium">
                  {new Date(project.lettingBidDate).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Danger Zone */}
        <div className="border-t pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-sm text-destructive">Danger Zone</h4>
              <p className="text-xs text-muted-foreground mt-1">
                Permanently delete this project and all its data
              </p>
            </div>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setShowDeleteDialog(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Project
            </Button>
          </div>
        </div>
      </CardContent>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDelete}
        itemName={project.name}
        isDeleting={deleteProjectMutation.isPending}
      />
    </Card>
  );
}
