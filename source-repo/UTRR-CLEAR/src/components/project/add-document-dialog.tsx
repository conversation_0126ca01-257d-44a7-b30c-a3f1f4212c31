'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Badge } from '~/components/ui/badge';
import { Checkbox } from '~/components/ui/checkbox';
import {
  Plus,
  FileText,
  Upload,
  File,
  Image,
  FileSpreadsheet,
  FileCode,
  X,
  AlertCircle,
} from 'lucide-react';
import { api } from '~/trpc/react';
import { useToast } from '~/hooks/use-toast';
import { safeLog } from '~/lib/error-handler';

const documentTypeOptions = [
  { value: 'contract', label: 'Contract', icon: FileText, color: 'bg-blue-100 text-blue-800' },
  { value: 'permit', label: 'Permit', icon: FileText, color: 'bg-green-100 text-green-800' },
  { value: 'drawing', label: 'Drawing/Plan', icon: Image, color: 'bg-purple-100 text-purple-800' },
  {
    value: 'specification',
    label: 'Specification',
    icon: FileCode,
    color: 'bg-orange-100 text-orange-800',
  },
  { value: 'report', label: 'Report', icon: FileText, color: 'bg-red-100 text-red-800' },
  {
    value: 'correspondence',
    label: 'Correspondence',
    icon: File,
    color: 'bg-yellow-100 text-yellow-800',
  },
  {
    value: 'invoice',
    label: 'Invoice',
    icon: FileSpreadsheet,
    color: 'bg-emerald-100 text-emerald-800',
  },
  {
    value: 'agreement',
    label: 'Agreement',
    icon: FileText,
    color: 'bg-indigo-100 text-indigo-800',
  },
  { value: 'survey', label: 'Survey', icon: Image, color: 'bg-pink-100 text-pink-800' },
  { value: 'other', label: 'Other', icon: File, color: 'bg-muted text-gray-800' },
];

const categoryOptions = [
  { value: 'financial', label: 'Financial', description: 'Invoices, payments, budget documents' },
  { value: 'legal', label: 'Legal', description: 'Contracts, agreements, permits' },
  { value: 'technical', label: 'Technical', description: 'Drawings, specifications, reports' },
  {
    value: 'administrative',
    label: 'Administrative',
    description: 'General project administration',
  },
  { value: 'communication', label: 'Communication', description: 'Correspondence, meeting notes' },
  { value: 'safety', label: 'Safety', description: 'Safety reports, certifications' },
  { value: 'quality', label: 'Quality', description: 'Quality control, inspections' },
];

const priorityOptions = [
  { value: 'low', label: 'Low', color: 'text-green-600' },
  { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
  { value: 'high', label: 'High', color: 'text-orange-600' },
  { value: 'critical', label: 'Critical', color: 'text-red-600' },
];

const documentSchema = z.object({
  name: z.string().min(3, 'Document name must be at least 3 characters'),
  description: z.string().optional(),
  documentType: z.string().min(1, 'Please select a document type'),
  category: z.string().min(1, 'Please select a category'),
  priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  tags: z.string().optional(),
  version: z.string().optional(),
  authorName: z.string().optional(),
  authorOrganization: z.string().optional(),
  isConfidential: z.boolean().default(false),
  requiresApproval: z.boolean().default(false),
  expirationDate: z.string().optional(),
  relatedUtilities: z.string().optional(),
  // File upload would be handled separately in real implementation
  fileName: z.string().optional(),
  fileSize: z.number().optional(),
  filePath: z.string().optional(),
});

type DocumentFormValues = z.infer<typeof documentSchema>;

interface AddDocumentDialogProps {
  projectId: string;
  buttonText?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  preselectedType?: string;
  preselectedCategory?: string;
}

export function AddDocumentDialog({
  projectId,
  buttonText = 'Add Document',
  variant = 'default',
  size = 'default',
  className,
  preselectedType,
  preselectedCategory,
}: AddDocumentDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const [dragActive, setDragActive] = React.useState(false);
  const { toast } = useToast();

  const utils = api.useUtils();

  const form = useForm({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      name: '',
      description: '',
      documentType: preselectedType || '',
      category: preselectedCategory || '',
      priority: 'medium',
      tags: '',
      version: '1.0',
      authorName: '',
      authorOrganization: '',
      isConfidential: false,
      requiresApproval: false,
      expirationDate: '',
      relatedUtilities: '',
      fileName: '',
      fileSize: 0,
      filePath: '',
    },
  });

  // Watch fields for conditional rendering
  const selectedType = form.watch('documentType');
  const requiresApproval = form.watch('requiresApproval');
  const isConfidential = form.watch('isConfidential');

  // TODO: Implement addDocument mutation or use correct method
  const addDocumentMutation = { mutate: () => {}, isPending: false } as any;
  /* 
  api.projects.addDocument.useMutation({
    onSuccess: () => {
      // Invalidate related queries
      void utils.projects.getDocuments.invalidate({ projectId });
      void utils.projects.getById.invalidate({ id: projectId });

      // Close dialog and reset form
      setOpen(false);
      form.reset();
      setSelectedFile(null);

      toast({
        title: 'Document added',
        description: 'The document has been successfully added to the project.',
      });
    },
    onError: (error: any) => {
      safeLog.error('Error adding document:', { error: String(error) });
      toast({
        title: 'Error',
        description: error.message || 'Failed to add document. Please try again.',
        variant: 'destructive',
      });
    },
  });
  */

  // Handle file selection
  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    form.setValue('fileName', file.name);
    form.setValue('fileSize', file.size);
    form.setValue('filePath', `/uploads/${projectId}/${Date.now()}-${file.name}`);

    // Auto-populate document name if empty
    if (!form.getValues('name')) {
      const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, '');
      form.setValue('name', nameWithoutExtension);
    }
  };

  // Handle drag and drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  async function onSubmit(data: DocumentFormValues) {
    if (!selectedFile) {
      toast({
        title: 'File Required',
        description: 'Please select a file to upload.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // First, upload the file
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('projectId', projectId);
      formData.append('documentType', data.documentType);

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const error = await uploadResponse.json();
        throw new Error(error.error || 'Failed to upload file');
      }

      const uploadResult = await uploadResponse.json();

      // Process form data with the uploaded file information
      const documentData = {
        ...data,
        projectId,
        uploadedAt: new Date(),
        fileName: uploadResult.file.fileName,
        fileSize: uploadResult.file.fileSize,
        filePath: uploadResult.file.filePath,
        tags: data.tags
          ? data.tags
              .split(',')
              .map((t: any) => t.trim())
              .filter(Boolean)
          : [],
        relatedUtilities: data.relatedUtilities
          ? data.relatedUtilities
              .split(',')
              .map((u: any) => u.trim())
              .filter(Boolean)
          : [],
      };

      addDocumentMutation.mutate(documentData);
    } catch (error) {
      safeLog.error('Upload error:', { error: String(error) });
      toast({
        title: 'Upload Failed',
        description: error instanceof Error ? error.message : 'Failed to upload file. Please try again.',
        variant: 'destructive',
      });
    }
  }

  // Get type configuration
  const selectedTypeData = documentTypeOptions.find((opt: any) => opt.value === selectedType);
  const TypeIcon = selectedTypeData?.icon || FileText;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Plus className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TypeIcon className="h-5 w-5" />
            Add Project Document
          </DialogTitle>
          <DialogDescription>
            Upload and categorize a document for this project. Complete all relevant fields for
            proper organization and searchability.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* File Upload Section */}
            <div>
              <FormLabel>Document File *</FormLabel>
              <div
                className={`mt-2 border-2 border-dashed rounded-lg p-6 text-center ${
                  dragActive ? 'border-primary bg-primary/5' : 'border-gray-300'
                } ${selectedFile ? 'bg-green-50 border-green-300' : ''}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                {selectedFile ? (
                  <div className="flex items-center justify-center gap-2">
                    <File className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium">{selectedFile.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({(selectedFile.size / 1024 / 1024).toFixed(1)} MB)
                    </span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedFile(null);
                        form.setValue('fileName', '');
                        form.setValue('fileSize', 0);
                        form.setValue('filePath', '');
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div>
                    <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground mb-2">
                      Drag and drop a file here, or click to select
                    </p>
                    <Input
                      type="file"
                      onChange={(e: any) => {
                        if (e.target.files?.[0]) {
                          handleFileSelect(e.target.files[0]);
                        }
                      }}
                      className="hidden"
                      id="file-upload"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif,.dwg,.dxf"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById('file-upload')?.click()}
                    >
                      Choose File
                    </Button>
                    <p className="text-xs text-muted-foreground mt-2">
                      Supported: PDF, Word, Excel, PowerPoint, Images, CAD files (max 100MB)
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Document Name and Version */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Document Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter document name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="version"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Version</FormLabel>
                    <FormControl>
                      <Input placeholder="1.0" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the document contents and purpose"
                      className="min-h-[80px]"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Type, Category, and Priority */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="documentType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Type *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {documentTypeOptions.map((type: any) => {
                          const Icon = type.icon;
                          return (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4" />
                                {type.label}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categoryOptions.map((category: any) => (
                          <SelectItem key={category.value} value={category.value}>
                            <div className="flex flex-col">
                              <span>{category.label}</span>
                              <span className="text-xs text-muted-foreground">{category.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {priorityOptions.map((priority: any) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            <span className={priority.color}>{priority.label}</span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Author Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="authorName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Author Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Document author" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="authorOrganization"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Author Organization</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Organization or company"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tags and Related Utilities */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Tags separated by commas"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>e.g., permits, design, coordination</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="relatedUtilities"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Related Utilities</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Utility companies involved"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>e.g., Duke Energy, AT&T</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Document Properties */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="isConfidential"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        Confidential Document
                      </FormLabel>
                      <FormDescription>
                        Mark as confidential if this document contains sensitive information
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="requiresApproval"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Requires Approval</FormLabel>
                      <FormDescription>
                        Check if this document requires approval before implementation
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {requiresApproval && (
                <FormField
                  control={form.control}
                  name="expirationDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiration Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormDescription>When does this document or approval expire?</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Status Indicators */}
            {(isConfidential || requiresApproval) && (
              <div className="flex gap-2">
                {isConfidential && (
                  <Badge variant="secondary" className="bg-red-100 text-red-800">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Confidential
                  </Badge>
                )}
                {requiresApproval && (
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                    Requires Approval
                  </Badge>
                )}
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={addDocumentMutation.isPending || !selectedFile}>
                {addDocumentMutation.isPending ? 'Uploading...' : 'Upload Document'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
