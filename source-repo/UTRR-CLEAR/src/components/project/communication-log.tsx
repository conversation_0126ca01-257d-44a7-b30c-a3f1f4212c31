'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Label } from '~/components/ui/label';
import {
  MessageSquare,
  Phone,
  Mail,
  Calendar,
  Plus,
  Search,
  Filter,
  FileText,
  Clock,
  User,
} from 'lucide-react';
import { CommentBadge } from '~/components/comments/comment-badge';
import { useContextMenuRef } from '~/hooks/use-context-menu';

interface CommunicationEntry {
  id: string;
  type: 'email' | 'phone' | 'meeting' | 'document';
  subject: string;
  description?: string;
  date: string;
  time: string;
  stakeholder: {
    id: string;
    name: string;
    company: string;
    avatar?: string;
  };
  status: 'pending' | 'completed' | 'follow-up';
  priority: 'low' | 'medium' | 'high';
  tags?: string[];
  attachments?: string[];
}

// Mock data - in real app this would come from tRPC/API
const mockCommunications: CommunicationEntry[] = [
  {
    id: '1',
    type: 'email',
    subject: 'Utility relocation timeline confirmation',
    description:
      'Confirmed relocation schedule for gas lines along I-465. Duke Energy will begin work on March 15th.',
    date: '2024-01-20',
    time: '14:30',
    stakeholder: {
      id: '1',
      name: 'John Martinez',
      company: 'Duke Energy',
      avatar: '/avatars/john.jpg',
    },
    status: 'completed',
    priority: 'high',
    tags: ['relocation', 'timeline', 'gas'],
    attachments: ['timeline_duke_energy.pdf'],
  },
  {
    id: '2',
    type: 'phone',
    subject: 'Emergency coordination call',
    description:
      'Discussed potential conflict with water main. AT&T will adjust fiber route to avoid intersection.',
    date: '2024-01-19',
    time: '09:15',
    stakeholder: {
      id: '2',
      name: 'Sarah Chen',
      company: 'AT&T',
      avatar: '/avatars/sarah.jpg',
    },
    status: 'follow-up',
    priority: 'high',
    tags: ['conflict', 'emergency', 'fiber'],
  },
  {
    id: '3',
    type: 'meeting',
    subject: 'Monthly coordination meeting',
    description: 'Regular stakeholder meeting to review project status and upcoming milestones.',
    date: '2024-01-18',
    time: '10:00',
    stakeholder: {
      id: '3',
      name: 'Multiple Stakeholders',
      company: 'Various',
    },
    status: 'completed',
    priority: 'medium',
    tags: ['monthly', 'status', 'coordination'],
  },
];

const typeIcons = {
  email: Mail,
  phone: Phone,
  meeting: Calendar,
  document: FileText,
};

const typeColors = {
  email: 'bg-blue-500',
  phone: 'bg-green-500',
  meeting: 'bg-purple-500',
  document: 'bg-orange-500',
};

const statusColors = {
  pending: 'secondary',
  completed: 'default',
  'follow-up': 'destructive',
} as const;

const priorityColors = {
  low: 'bg-muted0',
  medium: 'bg-blue-500',
  high: 'bg-red-500',
};

export function CommunicationLog({ projectId }: { projectId?: string }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const filteredCommunications = mockCommunications.filter((comm: any) => {
    const matchesSearch =
      comm.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comm.stakeholder.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comm.stakeholder.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || comm.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || comm.status === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  const CommunicationItem = ({ comm }: { comm: CommunicationEntry }) => {
    const TypeIcon = typeIcons[comm.type];

    // Add context menu to each communication entry
    const commRef = useContextMenuRef<HTMLDivElement>({
      entityType: 'communication',
      entityId: comm.id,
      entityName: comm.subject,
      data: {
        text: `${comm.subject} - ${comm.stakeholder.name}`,
        onEdit: () => {
          // TODO: Implement edit functionality
          console.log('Edit communication:', comm.id);
        },
        onView: () => {
          // TODO: Implement view details functionality
          console.log('View communication:', comm.id);
        },
        onShare: () => {
          navigator.clipboard.writeText(`Communication: ${comm.subject} with ${comm.stakeholder.name} on ${comm.date}`);
        },
      },
      securityLevel: 'low',
    });

    return (
      <div 
        ref={commRef}
        className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
      >
        <div className="flex items-start gap-3">
          <div
            className={`w-8 h-8 rounded-full ${typeColors[comm.type]} flex items-center justify-center`}
          >
            <TypeIcon className="h-4 w-4 text-white" />
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm">{comm.subject}</h4>
              <CommentBadge
                entityType="communication"
                entityId={comm.id}
                entityName={comm.subject}
                variant="icon-only"
                showZero={false}
              />
              <div className={`w-2 h-2 rounded-full ${priorityColors[comm.priority]}`} />
              <Badge variant={statusColors[comm.status]} className="text-xs">
                {comm.status}
              </Badge>
            </div>

            {comm.description && (
              <p className="text-sm text-muted-foreground mb-2">{comm.description}</p>
            )}

            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(comm.date).toLocaleDateString()}
              </span>
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {comm.time}
              </span>
              <span className="flex items-center gap-1">
                <User className="h-3 w-3" />
                {comm.stakeholder.company}
              </span>
            </div>

            {comm.tags && (
              <div className="flex gap-1 mt-2">
                {comm.tags.map((tag: any) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {comm.attachments && comm.attachments.length > 0 && (
              <div className="mt-2">
                <span className="text-xs text-muted-foreground">
                  {comm.attachments.length} attachment(s)
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={comm.stakeholder.avatar} />
              <AvatarFallback className="text-xs">
                {comm.stakeholder.name
                  .split(' ')
                  .map((n: any) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Communication Log</CardTitle>
            <CardDescription>
              Track all stakeholder communications and coordination activities
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Log Communication
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Log New Communication</DialogTitle>
                <DialogDescription>
                  Record a new communication with project stakeholders
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="type">Communication Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="phone">Phone Call</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="document">Document</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input id="subject" placeholder="Communication subject" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea id="description" placeholder="Detailed description..." />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="stakeholder">Stakeholder</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select stakeholder" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="duke">John Martinez (Duke Energy)</SelectItem>
                      <SelectItem value="att">Sarah Chen (AT&T)</SelectItem>
                      <SelectItem value="indot">Mike Wilson (INDOT)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsAddDialogOpen(false)}>Save Communication</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search communications..."
              value={searchTerm}
              onChange={(e: any) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="phone">Phone</SelectItem>
              <SelectItem value="meeting">Meeting</SelectItem>
              <SelectItem value="document">Document</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="follow-up">Follow-up</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Communication Entries */}
        <div className="space-y-3">
          {filteredCommunications.map((comm: any) => (
            <CommunicationItem key={comm.id} comm={comm} />
          ))}
        </div>

        {filteredCommunications.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No communications found</p>
          </div>
        )}

        {/* Summary Stats */}
        <div className="flex items-center justify-between pt-4 border-t text-sm text-muted-foreground">
          <span>{filteredCommunications.length} communications</span>
          <div className="flex gap-4">
            <span>
              {filteredCommunications.filter((c: any) => c.status === 'pending').length} pending
            </span>
            <span>
              {filteredCommunications.filter((c: any) => c.status === 'follow-up').length} follow-up
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
