'use client';

import React, { useState } from 'react';
import { api } from '~/trpc/react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { NoteEditorDialog } from '~/components/ui/note-editor-dialog';
import { MarkdownEditor } from '~/components/ui/markdown-editor';
import {
  Plus,
  FileText,
  Calendar,
  Edit,
  Trash,
  User,
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from '~/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';

interface ProjectNotesProps {
  projectId: string;
  projectName: string;
}

export function ProjectNotes({ projectId, projectName }: ProjectNotesProps) {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingDoc, setEditingDoc] = useState<any>(null);
  const [viewingDoc, setViewingDoc] = useState<any>(null);
  const [deleteDocId, setDeleteDocId] = useState<number | null>(null);

  const { data: documentsData, refetch } = api.notes.getAll.useQuery();

  const createDocument = api.notes.create.useMutation({
    onSuccess: () => {
      toast({
        title: 'Note created',
        description: 'Project note has been saved successfully.',
      });
      refetch();
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    },
  });

  const updateDocument = api.notes.update.useMutation({
    onSuccess: () => {
      toast({
        title: 'Note updated',
        description: 'Project note has been updated successfully.',
      });
      refetch();
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    },
  });

  const deleteDocument = api.notes.delete.useMutation({
    onSuccess: () => {
      toast({
        title: 'Note deleted',
        description: 'Project note has been deleted successfully.',
      });
      refetch();
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    },
  });

  const handleCreateNote = () => {
    setEditingDoc(null);
    setIsEditorOpen(true);
  };

  const handleEditNote = (doc: any) => {
    // Parse the content from file_path JSON
    const content = doc.file_path ? JSON.parse(doc.file_path).content : '';
    setEditingDoc({
      ...doc,
      content,
    });
    setIsEditorOpen(true);
  };

  const handleSaveNote = (noteData: any) => {
    if (editingDoc) {
      updateDocument.mutate({
        id: editingDoc.id,
        title: noteData.title,
        content: noteData.content,
        tags: noteData.tags,
      });
    } else {
      createDocument.mutate({
        title: noteData.title,
        content: noteData.content,
        tags: noteData.tags,
        // documentType: 'markdown', // Not supported in current schema
        // category: 'notes', // Not supported in current schema
      });
    }
  };

  const handleDeleteNote = (id: number) => {
    deleteDocument.mutate({ id: String(id) });
    setDeleteDocId(null);
  };

  const handleViewNote = (doc: any) => {
    const content = doc.file_path ? JSON.parse(doc.file_path).content : '';
    setViewingDoc({
      ...doc,
      content,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Project Notes</h3>
        <Button size="sm" onClick={handleCreateNote}>
          <Plus className="h-4 w-4 mr-2" />
          Add Note
        </Button>
      </div>

      {documentsData?.notes && documentsData.notes.length === 0 && (
        <Card className="text-center py-8">
          <CardContent>
            <FileText className="h-10 w-10 mx-auto mb-3 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-3">
              No project notes yet
            </p>
            <Button size="sm" onClick={handleCreateNote}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Note
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-3">
        {documentsData?.notes?.map((doc: any) => (
          <Card key={doc.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <CardTitle className="text-base">{doc.name}</CardTitle>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleViewNote(doc)}
                  >
                    <FileText className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleEditNote(doc)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setDeleteDocId(doc.id)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {doc.users?.first_name} {doc.users?.last_name}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {format(new Date(doc.created_at), 'MMM d, yyyy')}
                </div>
              </div>
              {doc.tags && doc.tags.length > 0 && (
                <div className="flex gap-1 flex-wrap mt-2">
                  {doc.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <NoteEditorDialog
        isOpen={isEditorOpen}
        onClose={() => {
          setIsEditorOpen(false);
          setEditingDoc(null);
        }}
        onSave={handleSaveNote}
        initialData={editingDoc ? {
          title: editingDoc.name,
          content: editingDoc.content,
          tags: editingDoc.tags || [],
        } : undefined}
        mode="project"
        projectName={projectName}
      />

      {viewingDoc && (
        <Dialog open={!!viewingDoc} onOpenChange={() => setViewingDoc(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{viewingDoc.name}</DialogTitle>
              <DialogDescription>
                Created by {viewingDoc.users?.first_name} {viewingDoc.users?.last_name} on{' '}
                {format(new Date(viewingDoc.created_at), 'MMMM d, yyyy')}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <MarkdownEditor
                content={viewingDoc.content}
                onChange={() => {}}
                readOnly
              />
              {viewingDoc.tags && viewingDoc.tags.length > 0 && (
                <div className="flex gap-2 flex-wrap mt-4">
                  {viewingDoc.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setViewingDoc(null)}>
                Close
              </Button>
              <Button onClick={() => {
                handleEditNote(viewingDoc);
                setViewingDoc(null);
              }}>
                Edit Note
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <AlertDialog open={!!deleteDocId} onOpenChange={() => setDeleteDocId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Note</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this project note? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteDocId && handleDeleteNote(deleteDocId)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}