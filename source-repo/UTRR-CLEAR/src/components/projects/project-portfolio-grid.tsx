'use client';

import * as React from 'react';
import { useState, useMemo, useCallback, useEffect } from 'react';
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  GroupingState,
  ExpandedState,
  RowSelectionState,
  Row,
  Table,
  ColumnResizeMode,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { 
  ArrowUpDown, 
  ChevronDown, 
  ChevronRight,
  MoreHorizontal, 
  Edit2, 
  Save, 
  X, 
  Grip,
  Calculator,
  Eye,
  EyeOff,
  Filter,
  Download,
  Copy,
  Archive,
  Trash2,
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { Checkbox } from '~/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { cn } from '~/lib/utils';
import { api } from '~/trpc/react';
import { toast } from '~/hooks/use-toast';
import Link from 'next/link.js';
import { useRouter } from 'next/navigation';
import { exportTableData } from '~/lib/export-utils';

// Helper function to convert decimal-like values to numbers
function toNumber(value: any): number {
  if (typeof value === 'object' && value !== null && 'toString' in value) {
    return parseFloat(value.toString());
  }
  if (typeof value === 'string') {
    return parseFloat(value);
  }
  return value || 0;
}

// Types
interface Project {
  id: string;
  name: string;
  client: string;
  description: string | null;
  rag_status: string | null;
  current_phase?: string | null;
  start_date?: Date | string | null;
  end_date?: Date | string | null;
  project_priority?: string | null;
  egis_project_manager?: string | null;
  client_pm?: string | null;
  contract_amount?: number | string | null | { toString(): string }; // Support Decimal type
  billed_to_date?: number | string | null | { toString(): string }; // Support Decimal type
  updated_at: Date | string;
  created_at: Date | string;
  high_priority_items?: number | null;
  medium_priority_items?: number | null;
  low_priority_items?: number | null;
  utilities?: Array<{
    id: number;
    name: string;
    type: string;
    status: string;
  }>;
  conflicts?: Array<{
    id: number;
    priority: string;
    status: string;
  }>;
  tasks?: Array<{
    id: number;
  }>;
}

interface EditingCell {
  rowId: string;
  columnId: string;
  value: any;
}

// Custom hooks for inline editing
function useInlineEdit() {
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [editedValues, setEditedValues] = useState<Record<string, any>>({});

  const startEdit = (rowId: string, columnId: string, value: any) => {
    setEditingCell({ rowId, columnId, value });
  };

  const cancelEdit = () => {
    setEditingCell(null);
  };

  const saveEdit = (value: any) => {
    if (editingCell) {
      setEditedValues(prev => ({
        ...prev,
        [`${editingCell.rowId}-${editingCell.columnId}`]: value,
      }));
      setEditingCell(null);
    }
  };

  return { editingCell, editedValues, startEdit, cancelEdit, saveEdit };
}

// Column definitions with all features
const createProjectColumns = (
  startEdit: (rowId: string, columnId: string, value: any) => void,
  editingCell: EditingCell | null,
  saveEdit: (value: any) => void,
  cancelEdit: () => void
): ColumnDef<Project>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 40,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Project Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row, column }) => {
      const isEditing = editingCell?.rowId === row.id && editingCell?.columnId === column.id;
      const value = row.getValue('name') as string;

      if (isEditing) {
        return (
          <div className="flex items-center gap-1">
            <Input
              defaultValue={value}
              className="h-8 text-sm"
              onKeyDown={(e) => {
                if (e.key === 'Enter') saveEdit(e.currentTarget.value);
                if (e.key === 'Escape') cancelEdit();
              }}
              autoFocus
            />
            <Button size="sm" variant="ghost" onClick={() => saveEdit(value)}>
              <Save className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" onClick={cancelEdit}>
              <X className="h-3 w-3" />
            </Button>
          </div>
        );
      }

      return (
        <div 
          className="flex items-center gap-2 cursor-pointer group"
          onDoubleClick={() => startEdit(row.id, column.id, value)}
        >
          <Link href={`/projects/${row.original.id}`} className="text-blue-600 hover:underline">
            {value}
          </Link>
          <Edit2 className="h-3 w-3 opacity-0 group-hover:opacity-50" />
        </div>
      );
    },
    enableResizing: true,
    size: 250,
  },
  {
    accessorKey: 'client',
    header: ({ column }) => (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Client
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div className="font-medium">{row.getValue('client')}</div>,
    enableGrouping: true,
    size: 150,
  },
  {
    accessorKey: 'rag_status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('rag_status') as string | null;
      const colorMap: Record<string, string> = {
        Green: 'bg-green-100 text-green-800 border-green-200',
        Red: 'bg-red-100 text-red-800 border-red-200',
        Amber: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        Complete: 'bg-blue-100 text-blue-800 border-blue-200',
      };
      
      return (
        <Badge 
          variant="outline" 
          className={cn('font-medium', status ? colorMap[status] : 'bg-gray-100')}
        >
          {status || 'Active'}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
    enableGrouping: true,
    size: 100,
  },
  {
    accessorKey: 'current_phase',
    header: 'Phase',
    cell: ({ row }) => {
      const phase = row.getValue('current_phase') as string | null;
      return phase ? <div className="text-sm">{phase}</div> : <span className="text-gray-400">-</span>;
    },
    enableGrouping: true,
    size: 120,
  },
  {
    accessorKey: 'contract_amount',
    header: ({ column }) => (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Contract Value
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row, column }) => {
      const amount = row.getValue('contract_amount') as number | string | null;
      const isEditing = editingCell?.rowId === row.id && editingCell?.columnId === column.id;

      if (isEditing) {
        return (
          <div className="flex items-center gap-1">
            <Input
              type="number"
              defaultValue={amount ?? ""}
              className="h-8 text-sm w-24"
              onKeyDown={(e) => {
                if (e.key === 'Enter') saveEdit(parseFloat(e.currentTarget.value));
                if (e.key === 'Escape') cancelEdit();
              }}
              autoFocus
            />
            <Button size="sm" variant="ghost" onClick={() => saveEdit(amount)}>
              <Save className="h-3 w-3" />
            </Button>
          </div>
        );
      }

      const numericAmount = toNumber(amount);
      
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
      }).format(numericAmount);

      return (
        <div 
          className="text-right font-medium cursor-pointer group"
          onDoubleClick={() => startEdit(row.id, column.id, amount)}
        >
          {formatted}
          <Edit2 className="h-3 w-3 opacity-0 group-hover:opacity-50 inline-block ml-1" />
        </div>
      );
    },
    aggregationFn: 'sum',
    aggregatedCell: ({ getValue }) => {
      const total = getValue() as number;
      return (
        <div className="text-right font-bold">
          {new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
          }).format(total)}
        </div>
      );
    },
    size: 130,
  },
  {
    accessorKey: 'billed_to_date',
    header: 'Billed to Date',
    cell: ({ row }) => {
      const amount = row.getValue('billed_to_date') as number | string | null | { toString(): string };
      const numericAmount = toNumber(amount);
      
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
      }).format(numericAmount);
      return <div className="text-right">{formatted}</div>;
    },
    aggregationFn: 'sum',
    size: 130,
  },
  {
    id: 'variance',
    header: 'Variance',
    accessorFn: (row) => {
      const contract = toNumber(row.contract_amount);
      const billed = toNumber(row.billed_to_date);
      
      return contract > 0 ? ((billed - contract) / contract) * 100 : 0;
    },
    cell: ({ getValue }) => {
      const variance = getValue() as number;
      return (
        <div className={cn(
          'text-right font-medium',
          variance > 0 ? 'text-red-600' : 'text-green-600'
        )}>
          {variance.toFixed(1)}%
        </div>
      );
    },
    aggregationFn: 'mean',
    size: 80,
  },
  {
    accessorKey: 'egis_project_manager',
    header: 'Project Manager',
    enableGrouping: true,
    size: 150,
  },
  {
    accessorKey: 'location',
    header: 'Location',
    enableHiding: true,
    size: 150,
  },
  {
    accessorKey: 'utilities',
    header: 'Utilities',
    cell: ({ row }) => {
      const count = row.original.utilities?.length || 0;
      return <div className="text-center">{count}</div>;
    },
    aggregationFn: 'sum',
    size: 80,
  },
  {
    accessorKey: 'conflicts',
    header: 'Conflicts',
    cell: ({ row }) => {
      const count = row.original.conflicts?.length || 0;
      return (
        <div className={cn('text-center', count > 0 && 'text-red-600 font-medium')}>
          {count}
        </div>
      );
    },
    aggregationFn: 'sum',
    size: 80,
  },
  {
    accessorKey: 'updated_at',
    header: ({ column }) => (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Last Updated
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('updated_at'));
      return <div className="text-sm text-gray-600">{date.toLocaleDateString()}</div>;
    },
    size: 120,
  },
  {
    id: 'actions',
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => {
      const project = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link href={`/projects/${project.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/projects/${project.id}/edit`}>
                <Edit2 className="mr-2 h-4 w-4" />
                Edit project
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Copy className="mr-2 h-4 w-4" />
              Duplicate
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Archive className="mr-2 h-4 w-4" />
              Archive
            </DropdownMenuItem>
            <DropdownMenuItem className="text-red-600">
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
    enableSorting: false,
    enableHiding: false,
    size: 40,
  },
];

// Main component
export function ProjectPortfolioGrid({ 
  projects,
  onRefetch,
}: { 
  projects: Project[];
  onRefetch?: () => void;
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('all');
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    location: false,
    actual_cost: false,
  });
  const [grouping, setGrouping] = useState<GroupingState>([]);
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnResizeMode, setColumnResizeMode] = useState<ColumnResizeMode>('onChange');

  const { editingCell, editedValues, startEdit, cancelEdit, saveEdit } = useInlineEdit();

  // Filter projects based on active tab
  const filteredProjects = useMemo(() => {
    switch (activeTab) {
      case 'active':
        return projects.filter(p => p.rag_status !== 'Complete');
      case 'red':
        return projects.filter(p => p.rag_status === 'Red');
      case 'high-value':
        return projects.filter(p => {
          const amount = p.contract_amount;
          const numericAmount = typeof amount === 'object' && amount !== null && 'toString' in amount
            ? parseFloat(amount.toString())
            : typeof amount === 'string'
            ? parseFloat(amount)
            : amount || 0;
          return numericAmount > 1000000;
        });
      case 'my-projects':
        // This would filter by current user - placeholder for now
        return projects;
      default:
        return projects;
    }
  }, [projects, activeTab]);

  // Calculate aggregated stats
  const stats = useMemo(() => {
    const activeProjects = filteredProjects.filter(p => p.rag_status !== 'Complete');
    const totalValue = filteredProjects.reduce((sum: any, p: any) => {
      const amount = p.contract_amount;
      const numericAmount = typeof amount === 'object' && amount !== null && 'toString' in amount
        ? parseFloat(amount.toString())
        : typeof amount === 'string'
        ? parseFloat(amount)
        : amount || 0;
      return sum + numericAmount;
    }, 0);
    const totalUtilities = filteredProjects.reduce((sum: any, p: any) => sum + (p.utilities?.length || 0), 0);
    const totalConflicts = filteredProjects.reduce((sum: any, p: any) => sum + (p.conflicts?.length || 0), 0);

    return {
      active: activeProjects.length,
      totalValue,
      totalUtilities,
      totalConflicts,
      avgValue: filteredProjects.length > 0 ? totalValue / filteredProjects.length : 0,
    };
  }, [filteredProjects]);

  const columns = useMemo(
    () => createProjectColumns(startEdit, editingCell, saveEdit, cancelEdit),
    [startEdit, editingCell, saveEdit, cancelEdit]
  );

  const table = useReactTable<Project>({
    data: filteredProjects,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      grouping,
      expanded,
      rowSelection,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGroupingChange: setGrouping,
    onExpandedChange: setExpanded,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    columnResizeMode,
    enableColumnResizing: true,
    debugTable: false,
  });

  // Handle bulk actions
  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const handleBulkExport = () => {
    const selectedData = selectedRows.map(row => row.original);
    const columns = [
      { key: 'name', label: 'Project Name' },
      { key: 'client', label: 'Client' },
      { key: 'rag_status', label: 'Status' },
      { key: 'contract_amount', label: 'Contract Value', format: (v: any) => v ? `$${v.toLocaleString()}` : '' },
      { key: '_count.utilities', label: 'Utilities' },
      { key: '_count.conflicts', label: 'Conflicts' },
    ];
    
    exportTableData(selectedData as unknown as Record<string, unknown>[], columns, 'projects-export', 'excel');
    toast({ title: 'Export successful', description: `Exported ${selectedData.length} projects` });
  };

  // Virtual scrolling setup
  const tableContainerRef = React.useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtualizer({
    count: table.getRowModel().rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 48,
    overscan: 10,
  });

  return (
    <div className="space-y-4">
      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between">
          <TabsList className="grid w-[500px] grid-cols-5">
            <TabsTrigger value="all">
              All ({projects.length})
            </TabsTrigger>
            <TabsTrigger value="active">
              Active ({projects.filter(p => p.rag_status !== 'Complete').length})
            </TabsTrigger>
            <TabsTrigger value="red">
              Red ({projects.filter(p => p.rag_status === 'Red').length})
            </TabsTrigger>
            <TabsTrigger value="high-value">
              High Value ({projects.filter(p => Number(p.contract_amount || 0) > 1000000).length})
            </TabsTrigger>
            <TabsTrigger value="my-projects">
              My Projects
            </TabsTrigger>
          </TabsList>

          {/* Stats Summary */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              <span>Total Value: {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
              }).format(stats.totalValue)}</span>
            </div>
            <div className="flex items-center gap-2">
              <span>Avg: {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
              }).format(stats.avgValue)}</span>
            </div>
          </div>
        </div>

        <TabsContent value={activeTab} className="mt-4">
          {/* Toolbar */}
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-2">
              {/* Global search */}
              <Input
                placeholder="Search all columns..."
                value={globalFilter ?? ''}
                onChange={(event: any) => setGlobalFilter(event.target.value)}
                className="max-w-sm"
              />

              {/* Column visibility */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    Columns
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {table
                    .getAllColumns()
                    .filter((column: any) => column.getCanHide())
                    .map((column: any) => {
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize"
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) => column.toggleVisibility(!!value)}
                        >
                          {column.id}
                        </DropdownMenuCheckboxItem>
                      );
                    })}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Grouping */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Group by
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Group by column</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setGrouping([])}>
                    None
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setGrouping(['client'])}>
                    Client
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setGrouping(['rag_status'])}>
                    Status
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setGrouping(['current_phase'])}>
                    Phase
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setGrouping(['egis_project_manager'])}>
                    Project Manager
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Bulk actions */}
              {selectedRows.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {selectedRows.length} selected
                  </span>
                  <Button variant="outline" size="sm" onClick={handleBulkExport}>
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm">
                    <Archive className="mr-2 h-4 w-4" />
                    Archive
                  </Button>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </Button>
                </div>
              )}
            </div>

            {/* Pagination info */}
            <div className="text-sm text-muted-foreground">
              {table.getFilteredRowModel().rows.length} of {projects.length} projects
            </div>
          </div>

          {/* Table with virtual scrolling */}
          <div className="rounded-md border">
            <div 
              ref={tableContainerRef}
              className="relative h-[600px] overflow-auto"
            >
              <table className="w-full caption-bottom text-sm">
                <thead className="sticky top-0 z-10 bg-background border-b">
                  {table.getHeaderGroups().map((headerGroup: any) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header: any) => {
                        return (
                          <th
                            key={header.id}
                            colSpan={header.colSpan}
                            style={{ width: header.getSize() }}
                            className="relative h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0"
                          >
                            {header.isPlaceholder ? null : (
                              <>
                                {header.column.getCanGroup() ? (
                                  <button
                                    className="cursor-pointer select-none"
                                    onClick={header.column.getToggleGroupingHandler()}
                                  >
                                    {header.column.getIsGrouped() ? (
                                      <ChevronDown className="h-4 w-4 inline mr-1" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4 inline mr-1" />
                                    )}
                                  </button>
                                ) : null}
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                                {/* Column resize handle */}
                                {header.column.getCanResize() && (
                                  <div
                                    onMouseDown={header.getResizeHandler()}
                                    onTouchStart={header.getResizeHandler()}
                                    className={cn(
                                      'absolute right-0 top-0 h-full w-1 cursor-col-resize select-none touch-none',
                                      header.column.getIsResizing() && 'bg-primary'
                                    )}
                                  >
                                    <div className="absolute right-0 top-1/2 -translate-y-1/2 w-4 h-4 -mr-2">
                                      <Grip className="h-4 w-4 text-gray-400" />
                                    </div>
                                  </div>
                                )}
                              </>
                            )}
                          </th>
                        );
                      })}
                    </tr>
                  ))}
                </thead>
                <tbody className="[&_tr:last-child]:border-0">
                  {rowVirtualizer.getVirtualItems().map((virtualRow: any) => {
                    const row = table.getRowModel().rows[virtualRow.index];
                    if (!row) return null;
                    
                    return (
                      <tr
                        key={row.id}
                        data-state={row.getIsSelected() && 'selected'}
                        className={cn(
                          'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',
                          row.getIsGrouped() && 'font-semibold bg-muted/30'
                        )}
                        style={{
                          height: `${virtualRow.size}px`,
                          transform: `translateY(${virtualRow.start}px)`,
                        }}
                      >
                        {row.getVisibleCells().map((cell: any) => (
                          <td
                            key={cell.id}
                            className="px-2 py-2 align-middle [&:has([role=checkbox])]:pr-0"
                            style={{ width: cell.column.getSize() }}
                          >
                            {cell.getIsGrouped() ? (
                              <>
                                <button
                                  className="cursor-pointer"
                                  onClick={row.getToggleExpandedHandler()}
                                >
                                  {row.getIsExpanded() ? (
                                    <ChevronDown className="h-4 w-4 inline mr-1" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4 inline mr-1" />
                                  )}
                                </button>
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )} ({row.subRows.length})
                              </>
                            ) : cell.getIsAggregated() ? (
                              flexRender(
                                cell.column.columnDef.aggregatedCell ?? cell.column.columnDef.cell,
                                cell.getContext()
                              )
                            ) : cell.getIsPlaceholder() ? null : (
                              flexRender(cell.column.columnDef.cell, cell.getContext())
                            )}
                          </td>
                        ))}
                      </tr>
                    );
                  })}
                </tbody>
                {/* Aggregation footer */}
                {table.getFooterGroups().length > 0 && (
                  <tfoot className="sticky bottom-0 bg-muted/50 border-t">
                    {table.getFooterGroups().map((footerGroup: any) => (
                      <tr key={footerGroup.id}>
                        {footerGroup.headers.map((header: any) => (
                          <th
                            key={header.id}
                            colSpan={header.colSpan}
                            className="px-2 py-2 text-left align-middle font-medium"
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.footer,
                                  header.getContext()
                                )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </tfoot>
                )}
              </table>
            </div>
          </div>

          {/* Pagination controls */}
          <div className="flex items-center justify-between py-4">
            <div className="text-sm text-muted-foreground">
              Page {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}