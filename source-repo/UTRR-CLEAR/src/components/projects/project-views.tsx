"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  Grid3X3,
  List,
  Calendar,
  Kanban,
  Filter,
  SortAsc,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Archive,
  Star,
  Copy,
} from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { Checkbox } from "~/components/ui/checkbox";
import { cn } from "~/lib/utils";
import { CommentBadge } from "~/components/comments/comment-badge";
import { safeLog } from '~/lib/error-handler';

export type ViewMode = "grid" | "list" | "kanban" | "calendar";

interface Project {
  id: string;
  name: string;
  description: string;
  status: "active" | "planning" | "construction" | "completed" | "onhold";
  ragStatus: "green" | "yellow" | "red";
  phase: string;
  client: string;
  budget: number;
  startDate: Date;
  endDate: Date;
  progress: number;
  commentCount: number;
  isStarred: boolean;
}

interface ProjectViewsProps {
  projects: Project[];
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  selectedProjects: string[];
  onProjectSelect: (projectId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  className?: string;
}

export function ProjectViews({
  projects,
  viewMode,
  onViewModeChange,
  selectedProjects,
  onProjectSelect,
  onSelectAll,
  className,
}: ProjectViewsProps) {
  const router = useRouter();

  const getStatusColor = (status: Project["status"]) => {
    const colors = {
      active: "bg-green-100 text-green-800",
      planning: "bg-yellow-100 text-yellow-800",
      construction: "bg-blue-100 text-blue-800",
      completed: "bg-gray-100 text-gray-800",
      onhold: "bg-red-100 text-red-800",
    };
    return colors[status];
  };

  const getRagColor = (rag: Project["ragStatus"]) => {
    const colors = {
      green: "bg-green-500",
      yellow: "bg-yellow-500",
      red: "bg-red-500",
    };
    return colors[rag];
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const handleProjectAction = (projectId: string, action: string) => {
    switch (action) {
      case "view":
        router.push(`/projects/${projectId}`);
        break;
      case "edit":
        router.push(`/projects/${projectId}/edit`);
        break;
      case "duplicate":
        // Handle duplicate
        safeLog.info("Duplicate project", { projectId });
        break;
      case "archive":
        // Handle archive
        safeLog.info("Archive project", { projectId });
        break;
      case "delete":
        // Handle delete
        safeLog.info("Delete project", { projectId });
        break;
      case "star":
        // Handle star toggle
        safeLog.info("Toggle star", { projectId });
        break;
    }
  };

  // Grid View
  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {projects.map((project: any) => (
        <Card
          key={project.id}
          className={cn(
            "cursor-pointer transition-all hover:shadow-md",
            selectedProjects.includes(project.id) && "ring-2 ring-primary"
          )}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedProjects.includes(project.id)}
                  onCheckedChange={(checked) =>
                    onProjectSelect(project.id, checked as boolean)
                  }
                  onClick={(e: any) => e.stopPropagation()}
                />
                <div
                  className={cn("w-3 h-3 rounded-full", getRagColor(project.ragStatus))}
                />
              </div>
              <div className="flex items-center gap-1">
                <CommentBadge
                  entityType="project"
                  entityId={project.id}
                  variant="compact"
                />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e: any) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "view")}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "edit")}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Project
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "duplicate")}>
                      <Copy className="mr-2 h-4 w-4" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "star")}>
                      <Star className="mr-2 h-4 w-4" />
                      {project.isStarred ? "Remove from Favorites" : "Add to Favorites"}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "archive")}>
                      <Archive className="mr-2 h-4 w-4" />
                      Archive
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => handleProjectAction(project.id, "delete")}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <CardTitle
              className="text-base cursor-pointer hover:text-primary"
              onClick={() => router.push(`/projects/${project.id}`)}
            >
              {project.name}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-muted-foreground line-clamp-2">
              {project.description}
            </p>
            
            <div className="flex items-center justify-between">
              <Badge className={getStatusColor(project.status)}>
                {project.status}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {project.phase}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Progress</span>
                <span>{project.progress}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ width: `${project.progress}%` }}
                />
              </div>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Budget</span>
              <span className="font-medium">{formatCurrency(project.budget)}</span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Due</span>
              <span>{formatDate(project.endDate)}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // List View
  const ListView = () => (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={selectedProjects.length === projects.length}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead>Project</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>RAG</TableHead>
            <TableHead>Phase</TableHead>
            <TableHead>Client</TableHead>
            <TableHead>Budget</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead>Progress</TableHead>
            <TableHead>Comments</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project: any) => (
            <TableRow
              key={project.id}
              className={cn(
                "cursor-pointer",
                selectedProjects.includes(project.id) && "bg-muted/50"
              )}
              onClick={() => router.push(`/projects/${project.id}`)}
            >
              <TableCell>
                <Checkbox
                  checked={selectedProjects.includes(project.id)}
                  onCheckedChange={(checked) =>
                    onProjectSelect(project.id, checked as boolean)
                  }
                  onClick={(e: any) => e.stopPropagation()}
                />
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{project.name}</div>
                  <div className="text-sm text-muted-foreground line-clamp-1">
                    {project.description}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(project.status)}>
                  {project.status}
                </Badge>
              </TableCell>
              <TableCell>
                <div className={cn("w-3 h-3 rounded-full", getRagColor(project.ragStatus))} />
              </TableCell>
              <TableCell>{project.phase}</TableCell>
              <TableCell>{project.client}</TableCell>
              <TableCell>{formatCurrency(project.budget)}</TableCell>
              <TableCell>{formatDate(project.endDate)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="w-16 bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                  <span className="text-sm">{project.progress}%</span>
                </div>
              </TableCell>
              <TableCell>
                <CommentBadge
                  entityType="project"
                  entityId={project.id}
                  variant="compact"
                />
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e: any) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "view")}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "edit")}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Project
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "duplicate")}>
                      <Copy className="mr-2 h-4 w-4" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, "archive")}>
                      <Archive className="mr-2 h-4 w-4" />
                      Archive
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleProjectAction(project.id, "delete")}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  // Kanban View
  const KanbanView = () => {
    const statusColumns = [
      { key: "planning", title: "Planning", color: "border-yellow-200" },
      { key: "active", title: "Active", color: "border-green-200" },
      { key: "construction", title: "Construction", color: "border-blue-200" },
      { key: "completed", title: "Completed", color: "border-gray-200" },
      { key: "onhold", title: "On Hold", color: "border-red-200" },
    ];

    return (
      <div className="flex gap-4 overflow-x-auto pb-4">
        {statusColumns.map((column: any) => {
          const columnProjects = projects.filter(p => p.status === column.key);
          
          return (
            <div
              key={column.key}
              className={cn("flex-shrink-0 w-80 border-t-4 rounded-lg bg-muted/30", column.color)}
            >
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{column.title}</h3>
                  <Badge variant="secondary">{columnProjects.length}</Badge>
                </div>
              </div>
              <div className="p-4 space-y-3 max-h-[600px] overflow-y-auto">
                {columnProjects.map((project: any) => (
                  <Card
                    key={project.id}
                    className="cursor-pointer hover:shadow-md transition-all"
                    onClick={() => router.push(`/projects/${project.id}`)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-sm">{project.name}</h4>
                        <div className="flex items-center gap-1">
                          <div className={cn("w-2 h-2 rounded-full", getRagColor(project.ragStatus))} />
                          <CommentBadge
                            entityType="project"
                            entityId={project.id}
                                      variant="icon-only"
                          />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-2 mb-3">
                        {project.description}
                      </p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span className="text-muted-foreground">Progress</span>
                          <span>{project.progress}%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-1">
                          <div
                            className="bg-primary h-1 rounded-full"
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                        <div className="flex justify-between text-xs">
                          <span className="text-muted-foreground">Due</span>
                          <span>{formatDate(project.endDate)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Calendar View (simplified)
  const CalendarView = () => (
    <div className="space-y-4">
      <div className="text-center text-muted-foreground">
        Calendar view coming soon - will show projects on timeline
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {projects.map((project: any) => (
          <Card key={project.id} className="cursor-pointer hover:shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{project.name}</h4>
                <Badge className={getStatusColor(project.status)}>
                  {project.status}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatDate(project.startDate)} - {formatDate(project.endDate)}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderView = () => {
    switch (viewMode) {
      case "grid":
        return <GridView />;
      case "list":
        return <ListView />;
      case "kanban":
        return <KanbanView />;
      case "calendar":
        return <CalendarView />;
      default:
        return <GridView />;
    }
  };

  return (
    <div className={className}>
      {renderView()}
    </div>
  );
}

// View mode selector
export function ViewModeSelector({
  currentMode,
  onModeChange,
}: {
  currentMode: ViewMode;
  onModeChange: (mode: ViewMode) => void;
}) {
  const modes = [
    { key: "grid" as ViewMode, icon: Grid3X3, label: "Grid" },
    { key: "list" as ViewMode, icon: List, label: "List" },
    { key: "kanban" as ViewMode, icon: Kanban, label: "Kanban" },
    { key: "calendar" as ViewMode, icon: Calendar, label: "Calendar" },
  ];

  return (
    <div className="flex items-center gap-1 border rounded-lg p-1">
      {modes.map((mode: any) => (
        <Button
          key={mode.key}
          variant={currentMode === mode.key ? "default" : "ghost"}
          size="sm"
          onClick={() => onModeChange(mode.key)}
          className="h-8 px-3"
        >
          <mode.icon className="h-4 w-4 mr-1" />
          <span className="hidden sm:inline">{mode.label}</span>
        </Button>
      ))}
    </div>
  );
}
