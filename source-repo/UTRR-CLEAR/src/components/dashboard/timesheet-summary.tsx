"use client";

import { useState, useEffect } from "react";
import { api } from "~/trpc/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { Clock, TrendingUp, Calendar } from "lucide-react";
import { startOfWeek, format } from "date-fns";
import Link from "next/link";

export function TimesheetSummary() {
  const currentWeek = startOfWeek(new Date(), { weekStartsOn: 1 });
  
  const { data: weeklyData } = api.timesheet.getWeeklyEntries.useQuery({
    weekStart: currentWeek.toISOString(),
  });

  const { data: userProfile } = api.users.getMyProfile.useQuery();

  // Calculate totals
  const totalHours = weeklyData?.projects.reduce((sum: any, project: any) => {
    return sum + project.entries.reduce((s: any, e: any) => s + Number(e.hours), 0);
  }, 0) || 0;

  const billableHours = weeklyData?.projects
    .filter((p: any) => p.is_billable)
    .reduce((sum: any, project: any) => {
      return sum + project.entries.reduce((s: any, e: any) => s + Number(e.hours), 0);
    }, 0) || 0;

  const overheadHours = weeklyData?.overheadEntries.reduce((sum: any, entry: any) => 
    sum + Number(entry.hours), 0
  ) || 0;

  const weekTotal = totalHours + overheadHours;
  
  // Get weekly hours target from user profile (set by admin), default to 40
  const weeklyTarget = (userProfile as any)?.weeklyHoursTarget || 40;
  const weekProgress = Math.min(100, (weekTotal / weeklyTarget) * 100);

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">This Week&apos;s Hours</CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Target: {weeklyTarget}h/week
            </span>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/timesheet">View Timesheet</Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Weekly Progress</span>
            <span className="font-medium">{weekTotal.toFixed(1)} / {weeklyTarget} hrs</span>
          </div>
          <Progress value={weekProgress} className="h-2" />
          <p className="text-xs text-muted-foreground">
            Week of {format(currentWeek, "MMM d, yyyy")}
          </p>
        </div>

        <div className="grid grid-cols-3 gap-3">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Total</span>
            </div>
            <p className="text-xl font-semibold">{weekTotal.toFixed(1)}</p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-xs text-muted-foreground">Billable</span>
            </div>
            <p className="text-xl font-semibold text-green-600">{billableHours.toFixed(1)}</p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-orange-600" />
              <span className="text-xs text-muted-foreground">Overhead</span>
            </div>
            <p className="text-xl font-semibold text-orange-600">{overheadHours.toFixed(1)}</p>
          </div>
        </div>

        {weekTotal < weeklyTarget && (
          <div className="pt-2 border-t">
            <p className="text-sm text-muted-foreground">
              {(weeklyTarget - weekTotal).toFixed(1)} hours remaining to reach weekly target
            </p>
          </div>
        )}

        {weekTotal >= weeklyTarget && (
          <div className="pt-2 border-t">
            <p className="text-sm text-green-600 font-medium">
              ✅ Weekly target reached! ({(weekTotal - weeklyTarget).toFixed(1)} hours over)
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}