'use client';

import { RefreshCw } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { useRouter } from 'next/navigation';

export function RefreshButton() {
  const router = useRouter();
  
  const handleRefresh = () => {
    router.refresh();
  };
  
  return (
    <Button 
      variant="ghost" 
      size="sm" 
      onClick={handleRefresh}
      aria-label="Refresh dashboard"
      title="Refresh dashboard"
    >
      <RefreshCw className="h-4 w-4" />
    </Button>
  );
}