"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  Toolt<PERSON>,
} from "recharts";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";

interface ProjectStatusData {
  status: string;
  count: number;
  color: string;
}

const STATUS_COLORS = {
  active: "#10b981",
  planning: "#f59e0b", 
  construction: "#3b82f6",
  completed: "#6b7280",
  onhold: "#ef4444",
} as const;

export function ProjectStatusChart() {
  // Using mock data for project status chart
  const projectStats = null;
  const isLoading = false;

  // Mock data for demonstration
  const mockData: ProjectStatusData[] = [
    { status: "Active", count: 12, color: STATUS_COLORS.active },
    { status: "Planning", count: 8, color: STATUS_COLORS.planning },
    { status: "Construction", count: 5, color: STATUS_COLORS.construction },
    { status: "Completed", count: 15, color: STATUS_COLORS.completed },
    { status: "On Hold", count: 2, color: STATUS_COLORS.onhold },
  ];

  const data = projectStats || mockData;

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.status}</p>
          <p className="text-sm text-muted-foreground">
            {data.count} project{data.count !== 1 ? 's' : ''}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-muted-foreground">
              {entry.value} ({entry.payload.count})
            </span>
          </div>
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Status Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="h-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={80}
            paddingAngle={2}
            dataKey="count"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
