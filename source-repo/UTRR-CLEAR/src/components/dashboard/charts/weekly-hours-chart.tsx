"use client";

import React from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { api } from "~/trpc/react";

interface WeeklyHoursData {
  week: string;
  hours: number;
  target: number;
}

export function WeeklyHoursChart() {
  // Using mock data for weekly hours chart
  const hoursData = null;
  const isLoading = false;

  // Mock data for demonstration
  const mockData: WeeklyHoursData[] = [
    { week: "Week 1", hours: 38, target: 40 },
    { week: "Week 2", hours: 42, target: 40 },
    { week: "Week 3", hours: 35, target: 40 },
    { week: "Week 4", hours: 45, target: 40 },
    { week: "Week 5", hours: 40, target: 40 },
    { week: "Week 6", hours: 37, target: 40 },
  ];

  const data = hoursData || mockData;

  const CustomTooltip = ({ active, payload, label }: { active?: boolean; payload?: any[]; label?: string }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-blue-600">
            Actual: {payload[0].value} hours
          </p>
          <p className="text-sm text-gray-500">
            Target: {payload[1]?.value || payload[0].payload.target} hours
          </p>
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="h-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis 
            dataKey="week" 
            tick={{ fontSize: 12 }}
            className="text-muted-foreground"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            className="text-muted-foreground"
          />
          <Tooltip content={<CustomTooltip active={false} payload={[]} label="" />} />
          <Bar 
            dataKey="hours" 
            fill="#3b82f6" 
            radius={[4, 4, 0, 0]}
            name="Actual Hours"
          />
          <Bar 
            dataKey="target" 
            fill="#e5e7eb" 
            radius={[4, 4, 0, 0]}
            name="Target Hours"
            opacity={0.5}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
