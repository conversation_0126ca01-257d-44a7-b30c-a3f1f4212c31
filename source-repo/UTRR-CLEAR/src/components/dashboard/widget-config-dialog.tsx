"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON><PERSON>Footer,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useToast } from "~/hooks/use-toast";
import { api } from "~/trpc/react";
import type { WidgetConfig } from "./dashboard-widget";

interface WidgetConfigDialogProps {
  isOpen: boolean;
  onClose: () => void;
  widget?: WidgetConfig | null;
  onSave?: () => void;
}

export function WidgetConfigDialog({
  isOpen,
  onClose,
  widget,
  onSave,
}: WidgetConfigDialogProps) {
  const { toast } = useToast();
  const [title, setTitle] = useState(widget?.title || "");
  const [description, setDescription] = useState(widget?.description || "");
  const [size, setSize] = useState<"small" | "medium" | "large" | "full">(
    widget?.size || "medium"
  );

  const utils = api.useUtils();

  const updateWidgetMutation = api.widgets.updateWidget.useMutation({
    onSuccess: () => {
      toast({
        title: "Widget Updated",
        description: "Widget configuration has been saved successfully.",
      });
      utils.widgets.getUserWidgets.invalidate();
      onSave?.();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update widget configuration.",
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    if (!widget || !title.trim()) {
      toast({
        title: "Validation Error",
        description: "Widget title is required.",
        variant: "destructive",
      });
      return;
    }

    updateWidgetMutation.mutate({
      id: widget.id,
      config: {
        ...widget.settings,
        title: title.trim(),
        description: description.trim() || undefined,
        size,
      },
    });
  };

  const handleClose = () => {
    // Reset form
    setTitle(widget?.title || "");
    setDescription(widget?.description || "");
    setSize(widget?.size || "medium");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Configure Widget</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e: any) => setTitle(e.target.value)}
              placeholder="Enter widget title"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e: any) => setDescription(e.target.value)}
              placeholder="Enter widget description (optional)"
              rows={3}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="size">Size</Label>
            <Select value={size} onValueChange={(value) => setSize(value as any)}>
              <SelectTrigger id="size">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="small">Small (1x1)</SelectItem>
                <SelectItem value="medium">Medium (2x1)</SelectItem>
                <SelectItem value="large">Large (2x2)</SelectItem>
                <SelectItem value="full">Full Width</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {widget && (
            <div className="grid gap-2">
              <Label>Widget Type</Label>
              <div className="text-sm text-muted-foreground">
                {widget.type}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={updateWidgetMutation.isPending || !title.trim()}
          >
            {updateWidgetMutation.isPending ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}