'use client';

import * as React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { api } from '~/trpc/react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '~/components/ui/dialog';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { toast } from '~/hooks/use-toast';
import { Progress } from '~/components/ui/progress';
import { format, startOfWeek, endOfWeek, addDays } from 'date-fns';
import { Search, X, Trash2, Plus } from 'lucide-react';

// Type for timesheet hours data
type TimesheetHours = {
  [projectId: string]: number[];
};

// Helper function to check if a project ID is an overhead category
function isOverheadCategory(projectId: string): boolean {
  const OVERHEAD_CATEGORIES = ['ADMIN', 'PTO', 'HOLIDAY', 'TRAINING'];
  return OVERHEAD_CATEGORIES.includes(projectId);
}

// TimesheetRow component
function TimesheetRow({
  projectId,
  projectName,
  projectType,
  hours,
  onHoursChange,
  onDeleteEntry,
}: {
  projectId: string;
  projectName: string;
  projectType: string;
  hours: number[];
  onHoursChange: (projectId: string, dayIndex: number, hours: number) => void;
  onDeleteEntry?: (projectId: string) => void;
}) {
  // Calculate total hours for this project
  const totalHours = hours.reduce((sum: any, hour: any) => sum + (hour || 0), 0);

  // Handle hour input changes
  const handleHourChange = (dayIndex: number, value: string) => {
    // Convert empty string to 0
    const hourValue = value === '' ? 0 : parseFloat(value);
    // Only process valid numbers
    if (!isNaN(hourValue)) {
      onHoursChange(projectId, dayIndex, hourValue);
    }
  };

  return (
    <TableRow className="group">
      <TableCell>
        <div className="flex items-start space-x-2">
          {!isOverheadCategory(projectId) ? (
            <Badge variant="outline" className="text-xs">
              {projectId}
            </Badge>
          ) : null}
          <div className="flex flex-col">
            <span className="font-medium">{projectName}</span>
            <span className="text-xs text-muted-foreground">{projectType}</span>
          </div>
          {onDeleteEntry && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 ml-auto opacity-0 group-hover:opacity-100 hover:opacity-100"
              onClick={() => onDeleteEntry(projectId)}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          )}
        </div>
      </TableCell>

      {/* Day inputs */}
      {hours.map((hour, index) => (
        <TableCell key={index} className="text-center px-0">
          <Input
            type="number"
            min="0"
            max="24"
            step="0.5"
            className="w-20 mx-auto text-center"
            value={hour || ''}
            onChange={(e: any) => handleHourChange(index, e.target.value)}
            onBlur={(e) => {
              // On blur, ensure empty values are set to 0
              if (e.target.value === '') {
                handleHourChange(index, '0');
              }
            }}
          />
        </TableCell>
      ))}

      {/* Total hours column */}
      <TableCell className="text-center font-medium px-0">
        <div className="w-20 mx-auto">{totalHours.toFixed(1)}</div>
      </TableCell>
    </TableRow>
  );
}

// Project Selection Dialog component
function ProjectSelectionDialog({
  isOpen,
  onClose,
  projects,
  onSelectProject,
  currentProjects,
}: {
  isOpen: boolean;
  onClose: () => void;
  projects: any[];
  onSelectProject: (projectId: string) => void;
  currentProjects: string[];
}) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredProjects = useMemo(() => {
    if (!searchTerm) return projects;

    return projects.filter((project: any) => {
      const searchLower = searchTerm.toLowerCase();
      const projectName = (project.name || '').toLowerCase();
      const projectId = (project.id || '').toString().toLowerCase();

      return projectName.includes(searchLower) || projectId.includes(searchLower);
    });
  }, [searchTerm, projects]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Add Project to Timesheet</DialogTitle>
          <DialogDescription>
            Select a project to add to your timesheet for the current period.
          </DialogDescription>
        </DialogHeader>

        <div className="relative mb-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by project name or ID..."
              className="pl-8 pr-8"
              value={searchTerm}
              onChange={(e: any) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <X
                className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground"
                onClick={() => setSearchTerm('')}
              />
            )}
          </div>
        </div>

        <div className="max-h-[400px] overflow-y-auto border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[120px]">Project ID</TableHead>
                <TableHead>Project Name</TableHead>
                <TableHead className="w-[100px]">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProjects.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                    {searchTerm ? 'No projects matching your search' : 'No available projects'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredProjects.map((project: any) => {
                  const isAlreadyAdded = currentProjects.includes(project.id);

                  return (
                    <TableRow key={project.id}>
                      <TableCell className="font-medium">
                        <Badge variant="outline">{project.projectNumber || project.id}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-base font-medium">{project.name}</span>
                          {project.description && (
                            <span className="text-xs text-muted-foreground">
                              {project.description}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isAlreadyAdded}
                          onClick={() => {
                            onSelectProject(project.id);
                            onClose();
                          }}
                        >
                          {isAlreadyAdded ? 'Added' : 'Add'}
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function TimesheetEntryWidget() {
  const { user } = useAuth();
  const [timesheetData, setTimesheetData] = useState<TimesheetHours>({});
  const [showProjectDialog, setShowProjectDialog] = useState(false);

  // Calculate the current week dates (Monday to Sunday)
  const now = new Date();
  const monday = startOfWeek(now, { weekStartsOn: 1 }); // 1 represents Monday
  const sunday = endOfWeek(now, { weekStartsOn: 1 });

  // Generate dates for the current week (Monday to Sunday)
  const weekDates = useMemo(() => {
    const dates = [];
    for (let i = 0; i < 7; i++) {
      dates.push(addDays(monday, i));
    }
    return dates;
  }, [monday]);

  // Define day labels
  const dayLabels = weekDates.map((date: any) => format(date, 'EEE (M/d)'));

  // Get projects using tRPC
  const { data: projectsResponse } = api.projects.getAll.useQuery({ limit: 50 });
  const projects = projectsResponse?.projects || [];

  // Initialize timesheet with overhead categories
  useEffect(() => {
    if (Object.keys(timesheetData).length === 0) {
      // Initialize with standard overhead categories
      setTimesheetData({
        ADMIN: [0, 0, 0, 0, 0, 0, 0],
        PTO: [0, 0, 0, 0, 0, 0, 0],
        HOLIDAY: [0, 0, 0, 0, 0, 0, 0],
        TRAINING: [0, 0, 0, 0, 0, 0, 0],
      });
    }
  }, [timesheetData]);

  // Calculate daily totals
  const dailyTotals = useMemo(() => {
    const totals = [0, 0, 0, 0, 0, 0, 0];

    Object.values(timesheetData).forEach((dayHours: any) => {
      dayHours.forEach((hours: any, index: number) => {
        totals[index] = (totals[index] || 0) + (hours || 0);
      });
    });

    return totals;
  }, [timesheetData]);

  // Calculate grand total hours
  const grandTotal = dailyTotals.reduce((sum: any, hours: any) => sum + hours, 0);

  // Calculate billable and non-billable hours
  const { billableTotal, nonBillableTotal } = useMemo(() => {
    let billable = 0;
    let nonBillable = 0;

    Object.entries(timesheetData).forEach(([projectId, hours]) => {
      const total = hours.reduce((sum: any, h: any) => sum + (h || 0), 0);

      if (isOverheadCategory(projectId)) {
        nonBillable += total;
      } else {
        billable += total;
      }
    });

    return { billableTotal: billable, nonBillableTotal: nonBillable };
  }, [timesheetData]);

  // Handle hours change
  const handleHoursChange = (projectId: string, dayIndex: number, hours: number) => {
    setTimesheetData((prev) => {
      // Get current hours array or create a new one
      const currentHours = prev[projectId] || [0, 0, 0, 0, 0, 0, 0];

      // Create a new array with the updated hours
      const newHours = [...currentHours];
      newHours[dayIndex] = hours;

      // Return the updated state
      return {
        ...prev,
        [projectId]: newHours,
      };
    });
  };

  // Handle project selection from dialog
  const handleSelectProject = (projectId: string) => {
    setTimesheetData((prev) => {
      // Only add if not already present
      if (!prev[projectId]) {
        return {
          ...prev,
          [projectId]: [0, 0, 0, 0, 0, 0, 0],
        };
      }
      return prev;
    });

    toast({
      title: 'Project added',
      description: 'Project has been added to your timesheet.',
    });
  };

  // Handle deletion of project entries
  const handleDeleteEntry = (projectId: string) => {
    setTimesheetData((prev) => {
      const updated = { ...prev };
      delete updated[projectId];
      return updated;
    });

    toast({
      title: 'Entry removed',
      description: 'Project entry has been removed from your timesheet.',
    });
  };

  // Format the week display showing Monday to Sunday of current week
  const weekDisplay = `Week of ${format(monday, 'MMM d')} - ${format(sunday, 'MMM d, yyyy')}`;

  return (
    <Card className="border-none shadow-none">
      <CardHeader className="pb-4">
        <div className="flex flex-col space-y-2 md:flex-row md:justify-between md:items-center">
          <CardTitle>myTime (Current Week)</CardTitle>

          <div className="flex items-center space-x-6 text-sm">
            <div className="flex flex-col space-y-1 min-w-[140px]">
              <div className="flex justify-between items-center">
                <span className="text-xs font-medium">Total Hours</span>
                <span className="text-xs">{grandTotal.toFixed(1)} of 40</span>
              </div>
              <Progress
                value={Math.min(100, (grandTotal / 40) * 100)}
                className="h-1.5 bg-muted/30"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Weekly Hours Progress */}
            <div className="bg-muted/30 rounded-md p-3 shadow-sm border">
              <div className="flex flex-col gap-2">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 mb-1">
                  <div className="text-sm font-medium">
                    Weekly Hours:{' '}
                    <span
                      className={`font-semibold ${grandTotal >= 40 ? 'text-green-600' : 'text-amber-600'}`}
                    >
                      {grandTotal.toFixed(1)}
                    </span>{' '}
                    / 40.0 hrs
                  </div>
                  <div className="text-xs px-2 py-1 rounded-full bg-muted/40 font-medium">
                    {((grandTotal / 40) * 100).toFixed(0)}% of weekly target
                  </div>
                </div>

                {/* Hours breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div className="flex gap-2 items-center">
                    <span className="w-3 h-3 rounded-sm bg-blue-500 inline-block"></span>
                    <span>
                      Billable: <span className="font-semibold">{billableTotal.toFixed(1)}</span>{' '}
                      hrs
                    </span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <span className="w-3 h-3 rounded-sm bg-orange-400 inline-block"></span>
                    <span>
                      Non-billable:{' '}
                      <span className="font-semibold">{nonBillableTotal.toFixed(1)}</span> hrs
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between items-center mb-3 pb-2 border-b">
              <h4 className="text-sm font-medium">{weekDisplay}</h4>
            </div>

            <div className="overflow-x-auto pb-2">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[180px]">Time Entries</TableHead>
                    {dayLabels.map((day, index) => (
                      <TableHead key={index} className="text-center px-0 w-[75px]">
                        {day}
                      </TableHead>
                    ))}
                    <TableHead className="text-center px-0 w-[75px]">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* Projects Section Header */}
                  <TableRow className="bg-muted/30">
                    <TableCell colSpan={9} className="py-2 border-t border-b">
                      <span className="text-sm font-medium text-primary">Projects</span>
                    </TableCell>
                  </TableRow>

                  {/* Projects Section */}
                  {Object.keys(timesheetData)
                    .filter((projectId: any) => !isOverheadCategory(projectId))
                    .map((projectId: any) => {
                      // Find project details from the projects array
                      const project = projects.find((p: any) => p.id === projectId);
                      const projectName = project?.name || projectId;

                      return (
                        <TimesheetRow
                          key={projectId}
                          projectId={projectId}
                          projectName={projectName}
                          projectType="Project"
                          hours={timesheetData[projectId] || [0, 0, 0, 0, 0, 0, 0]}
                          onHoursChange={handleHoursChange}
                          onDeleteEntry={handleDeleteEntry}
                        />
                      );
                    })}

                  {/* Project selection row */}
                  <TableRow
                    className="bg-muted/20 hover:bg-muted/30 transition-colors cursor-pointer"
                    onClick={() => setShowProjectDialog(true)}
                  >
                    <TableCell>
                      <span className="flex items-center space-x-2 text-muted-foreground">
                        <Badge variant="outline" className="opacity-50">
                          <Plus className="h-3 w-3" />
                        </Badge>
                        <span>Click to add a new project to your timesheet</span>
                      </span>
                    </TableCell>
                    {dayLabels.map((_, index) => (
                      <TableCell key={index} className="text-center px-0">
                        <div className="w-20 h-[38px] mx-auto opacity-40 border border-dashed rounded-md flex items-center justify-center text-xs text-muted-foreground">
                          —
                        </div>
                      </TableCell>
                    ))}
                    <TableCell className="text-center font-medium px-0">
                      <span className="w-20 mx-auto text-muted-foreground opacity-60 block">
                        0.0
                      </span>
                    </TableCell>
                  </TableRow>

                  {/* Separator Row */}
                  <TableRow className="bg-muted/30">
                    <TableCell colSpan={9} className="py-2 border-t border-b">
                      <span className="text-sm font-medium text-primary">Overhead Categories</span>
                    </TableCell>
                  </TableRow>

                  {/* Overhead Categories Section */}
                  <TimesheetRow
                    projectId="ADMIN"
                    projectName="Administrative Time"
                    projectType="Admin"
                    hours={timesheetData['ADMIN'] || [0, 0, 0, 0, 0, 0, 0]}
                    onHoursChange={handleHoursChange}
                  />
                  <TimesheetRow
                    projectId="TRAINING"
                    projectName="Training & Development"
                    projectType="Training"
                    hours={timesheetData['TRAINING'] || [0, 0, 0, 0, 0, 0, 0]}
                    onHoursChange={handleHoursChange}
                  />
                  <TimesheetRow
                    projectId="PTO"
                    projectName="Paid Time Off"
                    projectType="Paid Time Off"
                    hours={timesheetData['PTO'] || [0, 0, 0, 0, 0, 0, 0]}
                    onHoursChange={handleHoursChange}
                  />
                  <TimesheetRow
                    projectId="HOLIDAY"
                    projectName="Holiday"
                    projectType="Holiday"
                    hours={timesheetData['HOLIDAY'] || [0, 0, 0, 0, 0, 0, 0]}
                    onHoursChange={handleHoursChange}
                  />

                  {/* Daily Totals Row */}
                  <TableRow className="bg-muted/30">
                    <TableCell className="font-medium">Daily Total</TableCell>
                    {dailyTotals.map((total, index) => {
                      // Get daily status color based on hours
                      const isWeekend = index > 4; // Saturday and Sunday
                      let statusColor = 'text-amber-500';
                      let statusBg = 'bg-amber-100 dark:bg-amber-950/30';

                      if (isWeekend && total === 0) {
                        statusColor = 'text-muted-foreground';
                        statusBg = 'bg-transparent';
                      } else if (total >= 7.5 && total <= 8.5) {
                        statusColor = 'text-green-600';
                        statusBg = 'bg-green-100 dark:bg-green-950/30';
                      } else if (total > 8.5) {
                        statusColor = 'text-red-600';
                        statusBg = 'bg-red-100 dark:bg-red-950/30';
                      } else if (total === 0) {
                        statusColor = 'text-muted-foreground';
                        statusBg = 'bg-muted dark:bg-muted/30';
                      }

                      return (
                        <TableCell key={index} className="text-center font-medium px-0">
                          <span
                            className={`mx-auto rounded-md py-1 ${statusBg} min-w-[60px] block`}
                          >
                            <span className={statusColor}>{total.toFixed(1)}</span>
                          </span>
                        </TableCell>
                      );
                    })}
                    <TableCell className="text-center font-medium text-primary px-0">
                      <span className="bg-primary/10 rounded-md py-1 mx-auto min-w-[60px] block">
                        <span className="font-bold">{grandTotal.toFixed(1)}</span>
                      </span>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="mt-4 text-sm flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
            <div>
              <span className="text-muted-foreground">Timesheet Period:</span>{' '}
              <span className="font-medium">
                {format(monday, 'MMM d, yyyy')} - {format(sunday, 'MMM d, yyyy')} (Current)
              </span>
            </div>
            <div className="text-muted-foreground text-xs">
              Changes are saved locally (demo mode)
            </div>
          </div>
        </div>
      </CardContent>

      {/* Project Selection Dialog */}
      <ProjectSelectionDialog
        isOpen={showProjectDialog}
        onClose={() => setShowProjectDialog(false)}
        projects={projects}
        onSelectProject={handleSelectProject}
        currentProjects={Object.keys(timesheetData)}
      />
    </Card>
  );
}
