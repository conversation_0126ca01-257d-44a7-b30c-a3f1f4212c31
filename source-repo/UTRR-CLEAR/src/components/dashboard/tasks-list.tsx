'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Checkbox } from '~/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Calendar, Clock, Filter, AlertCircle, CheckCircle2, Loader2, MoreVertical, Edit, Trash2 } from 'lucide-react';
import { CommentBadge } from '~/components/comments/comment-badge';
import { api } from '~/trpc/react';
import { AddTaskDialog } from './add-task-dialog';
import { EditTaskDialog } from './edit-task-dialog';
import { DeleteConfirmationDialog } from '~/components/ui/delete-confirmation-dialog';
import { EmptyState } from '~/components/ui/empty-state';
import { TaskListSkeleton } from '~/components/ui/loading-skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { formatDate, isOverdue as checkOverdue } from '~/lib/date-utils';
import { showSuccessToast, showErrorToast } from '~/lib/toast-messages';
import { useContextMenuRef } from '~/hooks/use-context-menu';

interface Task {
  id: string;
  title: string;
  description?: string | null;
  projectId?: string | null;
  projectName: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dueDate?: string | null;
  assignedTo: {
    id: string;
    name: string;
    avatar?: string;
  };
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const priorityColors = {
  low: 'bg-muted0',
  medium: 'bg-blue-500',
  high: 'bg-amber-500',
  critical: 'bg-red-500',
};

const priorityBadgeVariants = {
  low: 'secondary',
  medium: 'default',
  high: 'default',
  critical: 'destructive',
} as const;

export function TasksList() {
  const [showCompleted, setShowCompleted] = useState(false);
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [deletingTask, setDeletingTask] = useState<Task | null>(null);

  // Use real tRPC queries
  const { data: tasks = [], isLoading, refetch } = api.tasks.getMyTasks.useQuery({
    includeCompleted: showCompleted,
    limit: 50,
  });

  const toggleTaskMutation = api.tasks.toggleComplete.useMutation({
    onSuccess: () => {
      refetch();
      showSuccessToast.statusChanged('Task', 'updated');
    },
    onError: (error: any) => {
      showErrorToast.updateFailed('task', error.message);
    },
  });

  const deleteTaskMutation = api.tasks.delete.useMutation({
    onSuccess: () => {
      refetch();
      showSuccessToast.deleted('Task');
      setDeletingTask(null);
    },
    onError: (error: any) => {
      showErrorToast.deleteFailed('task', error.message);
    },
  });

  const filteredTasks = tasks.filter((task: any) => {
    if (priorityFilter !== 'all' && task.priority !== priorityFilter) return false;
    return true;
  });

  const pendingTasks = filteredTasks.filter((task: any) => !task.completed);
  const completedTasks = filteredTasks.filter((task: any) => task.completed);

  const toggleTaskComplete = (taskId: string) => {
    toggleTaskMutation.mutate({ taskId });
  };

  const isOverdue = (dueDate: string | null) => {
    return checkOverdue(dueDate);
  };

  const TaskItem = ({ task }: { task: Task }) => {
    // Add context menu to each task item
    const taskRef = useContextMenuRef<HTMLDivElement>({
      entityType: 'task',
      entityId: task.id,
      entityName: task.title,
      data: {
        text: `${task.title} - ${task.projectName}`,
        onEdit: () => setEditingTask(task),
        onDelete: () => setDeletingTask(task),
        onView: () => {
          // Navigate to project if projectId exists
          if (task.projectId) {
            window.location.href = `/projects/${task.projectId}?tab=tasks`;
          }
        },
      },
      securityLevel: 'low',
    });

    return (
      <div
        ref={taskRef}
        className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors hover:bg-muted/50 ${task.completed ? 'opacity-70' : ''}`}
      >
      <Checkbox
        checked={task.completed}
        onCheckedChange={() => toggleTaskComplete(task.id)}
        disabled={toggleTaskMutation.isPending}
        className="mt-0.5"
      />

      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4
            className={`font-medium text-sm ${task.completed ? 'line-through text-muted-foreground' : ''}`}
          >
            {task.title}
          </h4>
          <div className={`w-2 h-2 rounded-full ${priorityColors[task.priority]}`} />
          <Badge variant={priorityBadgeVariants[task.priority]} className="text-xs">
            {task.priority}
          </Badge>
        </div>

        {task.description && (
          <p className="text-sm text-muted-foreground mb-2">{task.description}</p>
        )}

        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          {task.dueDate && (
            <span className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {formatDate(task.dueDate, 'medium')}
              {isOverdue(task.dueDate) && !task.completed && (
                <AlertCircle className="h-3 w-3 text-red-500 ml-1" />
              )}
            </span>
          )}
          <span>{task.projectName}</span>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <CommentBadge
          entityType="task"
          entityId={task.id}
          entityName={task.title}
          variant="icon-only"
          showZero={false}
        />
        <Avatar className="h-6 w-6">
          <AvatarImage src={task.assignedTo.avatar} />
          <AvatarFallback className="text-xs">
            {task.assignedTo.name
              .split(' ')
              .map((n: any) => n[0])
              .join('')}
          </AvatarFallback>
        </Avatar>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setEditingTask(task)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => setDeletingTask(task)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
    );
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Tasks & Actions</CardTitle>
            <CardDescription>
              {pendingTasks.length} pending • {completedTasks.length} completed
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <AddTaskDialog onSuccess={() => refetch()} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Quick Filters */}
        <div className="flex items-center gap-4 pb-2 border-b">
          <label className="flex items-center gap-2 text-sm">
            <Checkbox 
              checked={showCompleted} 
              onCheckedChange={(checked) => setShowCompleted(checked === true)}
              disabled={isLoading}
            />
            Show completed
          </label>
        </div>

        {/* Loading State */}
        {isLoading && <TaskListSkeleton />}

        {/* Content when not loading */}
        {!isLoading && (
          <>
            {/* Pending Tasks */}
            {pendingTasks.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium text-sm flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Pending ({pendingTasks.length})
                </h3>
                {pendingTasks.map((task: any) => (
                  <TaskItem key={task.id} task={task} />
                ))}
              </div>
            )}

            {/* Completed Tasks */}
            {showCompleted && completedTasks.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium text-sm flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  Completed ({completedTasks.length})
                </h3>
                {completedTasks.map((task: any) => (
                  <TaskItem key={task.id} task={task} />
                ))}
              </div>
            )}

            {/* Empty State */}
            {filteredTasks.length === 0 && (
              <EmptyState
                icon={<Clock className="h-12 w-12" />}
                title="No tasks assigned to you"
                description="Tasks will appear here when assigned by project managers"
                variant="default"
              />
            )}
          </>
        )}
      </CardContent>

      {/* Edit Task Dialog */}
      {editingTask && (
        <EditTaskDialog
          task={editingTask}
          open={!!editingTask}
          onOpenChange={(open) => !open && setEditingTask(null)}
          onSuccess={() => setEditingTask(null)}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={!!deletingTask}
        onOpenChange={(open) => !open && setDeletingTask(null)}
        onConfirm={() => {
          if (deletingTask) {
            deleteTaskMutation.mutate({ id: deletingTask.id });
          }
        }}
        itemName={deletingTask?.title}
        isDeleting={deleteTaskMutation.isPending}
      />
    </Card>
  );
}
