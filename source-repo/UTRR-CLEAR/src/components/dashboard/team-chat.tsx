'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';

import { Send, Users, MessageCircle, Loader2 } from 'lucide-react';

import { RealtimeIndicator } from '~/components/realtime/realtime-indicator';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { 
  ChatBubble, 
  ChatBubbleAvatar, 
  ChatBubbleMessage, 
  ChatBubbleTimestamp,
  ChatInput, 
  ChatMessageList
} from '~/components/ui/chat';
import { useAuth } from '~/hooks/use-auth';
import { useRealtimeCommunications } from '~/hooks/use-realtime-events';
import { useSupabaseRealtimeChat } from '~/hooks/use-supabase-realtime';
import { toast } from '~/hooks/use-toast';
import { api } from '~/trpc/react';

// Interface for online users
interface OnlineUser {
  id: string;
  name: string;
  initials: string;
  status: 'online' | 'away' | 'busy' | 'offline';
}

interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  content: string;
  timestamp: Date;
  userInitials: string;
  userEmail?: string | null;
}



export function TeamChat() {
  const { user } = useAuth();
  const [newMessage, setNewMessage] = useState('');

  // Use email for comparison since user_profiles has different IDs than auth
  const currentUserEmail = user?.email || '';

  // Add real-time chat updates
  useRealtimeCommunications('team-chat');
  useSupabaseRealtimeChat();

  // Use tRPC hooks for messages
  const { 
    data: messages = [], 
    isLoading, 
    error: messagesError,
    refetch: refetchMessages 
  } = api.chat.getMessages.useQuery(
    { limit: 50 },
    {
      enabled: !!user,
      refetchInterval: 30000, // Refetch every 30 seconds as fallback (realtime should handle most updates)
      retry: 3,
    }
  ) as { data: ChatMessage[], isLoading: boolean, error: any, refetch: () => void };

  // Listen for real-time message events and refetch when new messages arrive
  useEffect(() => {
    const handleNewMessage = () => {
      void refetchMessages();
    };

    const handleMessageUpdate = () => {
      void refetchMessages();
    };

    const handleMessageDelete = () => {
      void refetchMessages();
    };

    // Listen for the custom events dispatched by the realtime hook
    window.addEventListener('team-message-added', handleNewMessage);
    window.addEventListener('team-message-updated', handleMessageUpdate);
    window.addEventListener('team-message-deleted', handleMessageDelete);

    return () => {
      window.removeEventListener('team-message-added', handleNewMessage);
      window.removeEventListener('team-message-updated', handleMessageUpdate);
      window.removeEventListener('team-message-deleted', handleMessageDelete);
    };
  }, [refetchMessages]);
  
  const { data: onlineUsers = [] } = api.chat.getOnlineUsers.useQuery(undefined, {
    enabled: !!user,
    refetchInterval: 30000, // Refetch every 30 seconds
  }) as { data: OnlineUser[] };
  
  const sendMessageMutation = api.chat.sendMessage.useMutation({
    onSuccess: () => {
      setNewMessage('');
      void refetchMessages();
      toast({
        title: 'Message sent',
        description: 'Your message has been shared with the team.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleSendMessage = () => {
    if (!newMessage.trim() || !currentUserEmail || sendMessageMutation.isPending) return;

    sendMessageMutation.mutate({
      content: newMessage.trim(),
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getStatusColor = (status: OnlineUser['status']) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'busy':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    if (diffInHours >= 24) {
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else if (diffInHours >= 1) {
      return `${diffInHours}h ago`;
    } else if (diffInMinutes >= 1) {
      return `${diffInMinutes}m ago`;
    } else {
      return 'Just now';
    }
  };

  // Remove the user loading guard to always show messages

  return (
    <Card className="h-[600px] flex flex-col overflow-hidden">
      <CardHeader className="pb-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Team Connect
            <RealtimeIndicator />
          </CardTitle>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {onlineUsers.filter((u: OnlineUser) => u.status === 'online').length} online
            </span>
          </div>
        </div>

        {/* Online users */}
        <div className="flex flex-wrap gap-2 mt-2">
          {onlineUsers.slice(0, 6).map((user: OnlineUser) => (
            <div key={user.id} className="flex items-center gap-1">
                             <div className="relative">
                 <ChatBubbleAvatar 
                   fallback={user.initials}
                   className="h-6 w-6"
                 />
                <span
                  className={`absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 rounded-full border border-background ${getStatusColor(user.status)}`}
                />
              </div>
              <span className="text-xs text-muted-foreground hidden sm:inline">
                {user.name.split(' ')[0]}
              </span>
            </div>
          ))}
          {onlineUsers.length > 6 && (
            <Badge variant="outline" className="text-xs">
              +{onlineUsers.length - 6} more
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0 min-h-0 overflow-hidden">
                {/* Messages area */}
        <div className="flex-1 overflow-hidden">
          <ChatMessageList className="h-full">
            {isLoading ? (
              <div className="text-center py-8 text-muted-foreground">
                <Loader2 className="h-8 w-8 mx-auto mb-3 animate-spin" />
                <p>Loading messages...</p>
              </div>
            ) : messagesError ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Error loading messages</p>
                <p className="text-xs mt-1 max-w-md mx-auto break-words">
                  {messagesError.message || 'Failed to load messages'}
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => void refetchMessages()}
                  className="mt-2"
                >
                  Retry
                </Button>
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No messages yet. Start the conversation!</p>
              </div>
            ) : (
              messages.map((message: ChatMessage) => {
                // After migration: Compare by userId (which will be auth.users.id)
                // During migration: Fall back to email comparison
                const isOwnMessage = message.userId === currentUserId || message.userEmail === currentUserEmail;
                console.log('Message comparison:', { 
                  messageUserId: message.userId, 
                  messageUserEmail: message.userEmail,
                  currentUserEmail, 
                  isOwnMessage,
                  userName: message.userName 
                });
                return (
                  <ChatBubble
                    key={message.id}
                    variant={isOwnMessage ? 'sent' : 'received'}
                  >
                    <ChatBubbleAvatar 
                      fallback={message.userInitials}
                      className="h-8 w-8"
                    />
                    <div className="flex flex-col gap-1">
                      <ChatBubbleMessage variant={isOwnMessage ? 'sent' : 'received'}>
                        {message.content}
                      </ChatBubbleMessage>
                      <ChatBubbleTimestamp 
                        timestamp={formatTimeAgo(new Date(message.timestamp))}
                      />
                    </div>
                  </ChatBubble>
                );
              })
            )}
          </ChatMessageList>
        </div>

        {/* Message input */}
        <div className="p-4 border-t">
          <div className="flex gap-2">
            <ChatInput
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              disabled={sendMessageMutation.isPending}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || sendMessageMutation.isPending}
              size="icon"
            >
              {sendMessageMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          <div className="text-xs text-muted-foreground mt-2">
            Press Enter to send • This is a team collaboration space
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 