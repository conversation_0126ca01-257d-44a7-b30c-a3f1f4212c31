'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import {
  Calendar,
  MapPin,
  Clock,
  AlertCircle,
  CheckCircle,
  Users,
  DollarSign,
  TrendingUp,
} from 'lucide-react';
import { useContextMenuRef } from '~/hooks/use-context-menu';
import { useRouter } from 'next/navigation';
import { CommentBadge } from '~/components/comments/comment-badge';

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    client: string;
    status: 'active' | 'planning' | 'construction' | 'completed';
    ragStatus: 'red' | 'amber' | 'green';
    currentPhase: string;
    startDate: string;
    endDate: string;
    coordinator: string;
    location: string;
    utilities: number;
    conflicts: number;
    progress?: number;
    budget?: number;
    billedToDate?: number;
  };
  showFinancials?: boolean;
  onClick?: () => void;
}

const ragColors = {
  red: 'bg-red-500',
  amber: 'bg-amber-500',
  green: 'bg-green-500',
};

const statusColors = {
  active: 'default',
  planning: 'secondary',
  construction: 'outline',
  completed: 'secondary',
} as const;

export function ProjectCard({ project, showFinancials = false, onClick }: ProjectCardProps) {
  const router = useRouter();
  const progressPercentage = project.progress ?? 0;
  const billingPercentage =
    project.budget && project.billedToDate ? (project.billedToDate / project.budget) * 100 : 0;

  // Add context menu to the entire card
  const cardRef = useContextMenuRef<HTMLDivElement>({
    entityType: 'project',
    entityId: project.id,
    entityName: project.name,
    data: {
      text: `${project.name} - ${project.client}`,
      onView: () => router.push(`/projects/${project.id}`),
      onEdit: () => router.push(`/projects/${project.id}/edit`),
      onShare: () => {
        // Share functionality
        navigator.clipboard.writeText(`${window.location.origin}/projects/${project.id}`);
      },
    },
    securityLevel: showFinancials ? 'medium' : 'low',
  });

  return (
    <Card 
      ref={cardRef}
      className="hover:shadow-lg transition-shadow cursor-pointer" 
      onClick={onClick}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-lg">{project.name}</CardTitle>
              <CommentBadge
                entityType="project"
                entityId={project.id}
                entityName={project.name}
                variant="icon-only"
                showZero={false}
              />
              <div className={`w-3 h-3 rounded-full ${ragColors[project.ragStatus]}`} />
            </div>
            <CardDescription className="flex items-center gap-4">
              <span>{project.client}</span>
              <span>•</span>
              <span className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {project.location}
              </span>
            </CardDescription>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge variant={statusColors[project.status]}>
              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </Badge>
            <span className="text-sm text-muted-foreground font-mono">{project.id}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        {project.progress !== undefined && (
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-muted-foreground">Project Progress</span>
              <span className="font-medium">{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        )}

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="flex items-center gap-1 text-muted-foreground mb-1">
              <Clock className="h-3 w-3" />
              Current Phase
            </div>
            <div className="font-medium">{project.currentPhase}</div>
          </div>

          <div>
            <div className="flex items-center gap-1 text-muted-foreground mb-1">
              <Calendar className="h-3 w-3" />
              Timeline
            </div>
            <div className="font-medium">
              {new Date(project.startDate).toLocaleDateString()} -{' '}
              {new Date(project.endDate).toLocaleDateString()}
            </div>
          </div>

          <div>
            <div className="flex items-center gap-1 text-muted-foreground mb-1">
              <Users className="h-3 w-3" />
              Utilities
            </div>
            <div className="font-medium">{project.utilities} companies</div>
          </div>

          <div>
            <div className="flex items-center gap-1 text-muted-foreground mb-1">
              {project.conflicts > 0 ? (
                <AlertCircle className="h-3 w-3 text-amber-500" />
              ) : (
                <CheckCircle className="h-3 w-3 text-green-500" />
              )}
              Conflicts
            </div>
            <div
              className={`font-medium ${project.conflicts > 0 ? 'text-amber-600' : 'text-green-600'}`}
            >
              {project.conflicts === 0 ? 'None' : `${project.conflicts} active`}
            </div>
          </div>
        </div>

        {/* Financial Information */}
        {showFinancials && project.budget && (
          <div className="grid grid-cols-2 gap-4 text-sm pt-4 border-t">
            <div>
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <DollarSign className="h-3 w-3" />
                Budget
              </div>
              <div className="font-medium">${project.budget.toLocaleString()}</div>
            </div>
            <div>
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <TrendingUp className="h-3 w-3" />
                Billed
              </div>
              <div className="font-medium">
                ${project.billedToDate?.toLocaleString() || 0} ({billingPercentage.toFixed(0)}%)
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-3">
            <span className="text-sm text-muted-foreground">
              Coordinator: <span className="font-medium">{project.coordinator}</span>
            </span>
            <CommentBadge
              entityType="project"
              entityId={project.id}
              entityName={project.name}
              variant="compact"
              showZero={false}
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={(e: any) => {
              e.stopPropagation();
              onClick?.();
            }}
          >
            View Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
