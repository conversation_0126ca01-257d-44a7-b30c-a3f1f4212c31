'use client';

import * as React from 'react';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { useSession } from 'next-auth/react';
import { api } from '~/trpc/react';
import { startOfWeek } from 'date-fns';
import { LoadingCard } from '~/components/ui/loading-overlay';

export function WeeklyHoursCard() {
  const [weeklyHoursValue, setWeeklyHoursValue] = React.useState(0);
  const [billableHours, setBillableHours] = React.useState(0);
  const [nonBillableHours, setNonBillableHours] = React.useState(0);

  // Calculate current week start (Monday)
  const currentWeekStart = React.useMemo(() => {
    return startOfWeek(new Date(), { weekStartsOn: 1 }).toISOString();
  }, []);

  // Get current timesheet data from tRPC
  const { data: timesheetData, isLoading } = api.timesheet.getWeeklyEntries.useQuery({
    weekStart: currentWeekStart,
  });

  React.useEffect(() => {
    if (!timesheetData) return;

    let totalHours = 0;
    let billable = 0;
    let nonBillable = 0;

    // Calculate totals from project entries (billable)
    timesheetData.projects.forEach((project: any) => {
      project.entries.forEach((entry: any) => {
        const hourValue = parseFloat(entry.hours.toString()) || 0;
        totalHours += hourValue;
        billable += hourValue;
      });
    });

    // Calculate totals from overhead entries (non-billable)
    timesheetData.overheadEntries.forEach((entry: any) => {
      const hourValue = parseFloat(entry.hours.toString()) || 0;
      totalHours += hourValue;
      nonBillable += hourValue;
    });

    setWeeklyHoursValue(totalHours);
    setBillableHours(billable);
    setNonBillableHours(nonBillable);
  }, [timesheetData]);

  const weeklyHoursPercentage = Math.min(100, Math.round((weeklyHoursValue / 40) * 100));

  if (isLoading) {
    return (
      <Card className="h-fit sticky top-4 shadow-sm">
        <CardHeader className="pb-2 pt-4">
          <h4 className="text-sm font-medium">Weekly Hours Summary</h4>
        </CardHeader>
        <CardContent className="p-4 pt-0 pb-5">
          <LoadingCard rows={4} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-fit sticky top-4 shadow-sm">
      <CardHeader className="pb-2 pt-4">
        <h4 className="text-sm font-medium">Weekly Hours Summary</h4>
      </CardHeader>
      <CardContent className="p-4 pt-0 pb-5">
        {/* Main hours display */}
        <div className="mb-2 text-center">
          <div className="text-5xl font-bold text-primary">{weeklyHoursValue.toFixed(1)}</div>
          <div className="text-xs text-muted-foreground mt-1">of 40 hours</div>
        </div>

        {/* Main progress bar */}
        <div className="my-4">
          <Progress value={weeklyHoursPercentage} className="h-2.5 bg-muted/40" />
        </div>

        <div className="border-t border-muted/30 my-4"></div>

        {/* Hours breakdown */}
        <div className="space-y-5">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Billable Hours</span>
              <span className="text-sm font-medium">{billableHours.toFixed(1)}</span>
            </div>
            <Progress
              value={(billableHours / weeklyHoursValue) * 100 || 0}
              className="h-2 bg-muted/30"
            />
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Non-Billable</span>
              <span className="text-sm font-medium">{nonBillableHours.toFixed(1)}</span>
            </div>
            <Progress
              value={(nonBillableHours / weeklyHoursValue) * 100 || 0}
              className="h-2 bg-muted/30"
            />
          </div>
        </div>

        {/* Status indicator */}
        <div className="mt-4 text-center">
          <div
            className={`text-xs px-2 py-1 rounded-full font-medium ${
              weeklyHoursValue >= 40
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                : weeklyHoursValue >= 35
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                  : 'bg-muted text-gray-800 dark:bg-background dark:text-gray-300'
            }`}
          >
            {weeklyHoursValue >= 40
              ? 'Weekly Goal Met'
              : weeklyHoursValue >= 35
                ? 'Almost There'
                : 'Below Target'}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
