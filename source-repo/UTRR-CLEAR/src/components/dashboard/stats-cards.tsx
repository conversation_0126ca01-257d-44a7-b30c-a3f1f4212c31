'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import {
  FolderOpen,
  AlertTriangle,
  Clock,
  CheckCircle,
  DollarSign,
  TrendingUp,
  Users,
  Calendar,
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    label: string;
    isPositive?: boolean;
  };
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
}

interface StatsCardsProps {
  className?: string;
}

function StatsCard({ title, value, description, icon: Icon, trend, badge }: StatsCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="flex items-center gap-2">
          {badge && (
            <Badge variant={badge.variant || 'secondary'} className="text-xs">
              {badge.text}
            </Badge>
          )}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Icon className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  {title === 'Active Projects' && 'Total number of projects currently in progress'}
                  {title === 'Pending Conflicts' && 'Utility conflicts that need immediate resolution'}
                  {title === 'This Month Hours' && 'Total billable hours logged this month'}
                  {title === 'Completed This Week' && 'Projects marked as complete in the last 7 days'}
                  {title === 'Total Revenue' && 'Cumulative revenue for the current fiscal year'}
                  {title === 'Active Utilities' && 'Number of utility companies currently engaged in projects'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && <CardDescription className="text-xs">{description}</CardDescription>}
        {trend && (
          <div className="flex items-center text-xs mt-2">
            <TrendingUp
              className={`mr-1 h-3 w-3 ${trend.isPositive ? 'text-green-500' : 'text-red-500'}`}
            />
            <span className={trend.isPositive ? 'text-green-600' : 'text-red-600'}>
              {trend.isPositive ? '+' : ''}
              {trend.value}%
            </span>
            <span className="text-muted-foreground ml-1">{trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function StatsCards({ className }: StatsCardsProps) {
  // In a real app, these would come from tRPC/API calls
  const stats = [
    {
      title: 'Active Projects',
      value: 12,
      description: 'Currently in progress',
      icon: FolderOpen,
      trend: {
        value: 8.2,
        label: 'from last month',
        isPositive: true,
      },
    },
    {
      title: 'Pending Conflicts',
      value: 7,
      description: 'Requiring attention',
      icon: AlertTriangle,
      badge: {
        text: 'High Priority',
        variant: 'destructive' as const,
      },
    },
    {
      title: 'This Month Hours',
      value: '142.5',
      description: 'Billable hours logged',
      icon: Clock,
      trend: {
        value: 12.1,
        label: 'vs target',
        isPositive: true,
      },
    },
    {
      title: 'Completed This Week',
      value: 3,
      description: 'Projects finished',
      icon: CheckCircle,
      trend: {
        value: 25.0,
        label: 'completion rate',
        isPositive: true,
      },
    },
    {
      title: 'Total Revenue',
      value: '$847K',
      description: 'Year to date',
      icon: DollarSign,
      trend: {
        value: 15.3,
        label: 'from last year',
        isPositive: true,
      },
    },
    {
      title: 'Active Utilities',
      value: 45,
      description: 'Coordinating companies',
      icon: Users,
      trend: {
        value: 2.1,
        label: 'new this month',
        isPositive: true,
      },
    },
    {
      title: 'Upcoming Deadlines',
      value: 8,
      description: 'Next 30 days',
      icon: Calendar,
      badge: {
        text: '2 Critical',
        variant: 'destructive' as const,
      },
    },
    {
      title: 'Project Health',
      value: '85%',
      description: 'Green/Amber projects',
      icon: TrendingUp,
      trend: {
        value: 5.2,
        label: 'improvement',
        isPositive: true,
      },
    },
  ];

  return (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {stats.map((stat, index) => (
        <StatsCard key={index} {...stat} />
      ))}
    </div>
  );
}
