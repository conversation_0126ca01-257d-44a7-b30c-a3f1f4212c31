'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Search,
  Filter,
  Calendar,
  MapPin,
  Clock,
  AlertCircle,
  CheckCircle,
  Users,
  ExternalLink,
  FolderOpen,
  Loader2,
} from 'lucide-react';
import { EmptyState } from '~/components/ui/empty-state';
import { api } from '~/trpc/react';
import { LoadingCard } from '~/components/ui/loading-overlay';

interface Project {
  id: string;
  name: string;
  client: string;
  status: 'active' | 'planning' | 'construction' | 'completed';
  ragStatus: 'red' | 'amber' | 'green';
  currentPhase: string;
  startDate: string;
  endDate: string;
  coordinator: string;
  location: string;
  utilities: number;
  conflicts: number;
}

// Project data now comes from tRPC - replaced mock data

const ragColors = {
  red: 'bg-red-500',
  amber: 'bg-amber-500',
  green: 'bg-green-500',
};

const statusColors = {
  active: 'default',
  planning: 'secondary',
  construction: 'outline',
  completed: 'secondary',
} as const;

export function ActiveProjects() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [ragFilter, setRagFilter] = useState<string>('all');

  // Fetch projects from tRPC
  const { data: projectsData, isLoading, error } = api.projects.getAll.useQuery({
    limit: 50,
  });

  // Transform projects to match our interface
  const transformedProjects: Project[] = (projectsData?.projects ?? []).map((project: any) => ({
    id: project.id,
    name: project.name || 'Unnamed Project',
    client: project.client || 'Unknown Client',
    status: 'active' as const, // Default status since we don't have this field in schema
    ragStatus: (project.rag_status?.toLowerCase() as 'red' | 'amber' | 'green') || 'green',
    currentPhase: project.current_phase || 'Planning',
    startDate: project.start_date ? new Date(project.start_date).toISOString().split('T')[0]! : '',
    endDate: project.end_date ? new Date(project.end_date).toISOString().split('T')[0]! : '',
    coordinator: 'TBD', // This field would need to be added to the API
    location: 'Unknown Location', // Location is not directly stored on project
    utilities: project._count?.utilities || 0,
    conflicts: project._count?.conflicts || 0,
  }));

  const filteredProjects = transformedProjects.filter((project: any) => {
    const matchesSearch =
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    const matchesRag = ragFilter === 'all' || project.ragStatus === ragFilter;

    return matchesSearch && matchesStatus && matchesRag;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Active Projects</h2>
          <p className="text-muted-foreground">
            Monitor and manage your utility coordination projects
          </p>
        </div>
        <Button>
          <ExternalLink className="mr-2 h-4 w-4" />
          View All Projects
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects, clients, or IDs..."
            value={searchTerm}
            onChange={(e: any) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="planning">Planning</SelectItem>
            <SelectItem value="construction">Construction</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>
        <Select value={ragFilter} onValueChange={setRagFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="RAG Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All RAG</SelectItem>
            <SelectItem value="green">Green</SelectItem>
            <SelectItem value="amber">Amber</SelectItem>
            <SelectItem value="red">Red</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-4">
          <LoadingCard rows={3} />
          <LoadingCard rows={3} />
          <LoadingCard rows={3} />
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-8">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600">Failed to load projects: {error.message}</p>
        </div>
      )}

      {/* Project Cards */}
      {!isLoading && !error && (
        <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
          {filteredProjects.length === 0 ? (
            <EmptyState
              title="No Projects Found"
              description="No projects match your current filters."
              icon={<FolderOpen className="h-12 w-12" />}
            />
          ) : (
            filteredProjects.map((project: any) => (
          <Card key={project.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <div className={`w-3 h-3 rounded-full ${(ragColors as any)[project.ragStatus] || 'bg-gray-400'}`} />
                  </div>
                  <CardDescription className="flex items-center gap-4">
                    <span>{project.client}</span>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {project.location}
                    </span>
                  </CardDescription>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge variant={(statusColors as any)[project.status] || 'default'}>
                    {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                  </Badge>
                  <span className="text-sm text-muted-foreground font-mono">{project.id}</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="flex items-center gap-1 text-muted-foreground mb-1">
                    <Clock className="h-3 w-3" />
                    Current Phase
                  </div>
                  <div className="font-medium">{project.currentPhase}</div>
                </div>
                <div>
                  <div className="flex items-center gap-1 text-muted-foreground mb-1">
                    <Calendar className="h-3 w-3" />
                    Timeline
                  </div>
                  <div className="font-medium">
                    {new Date(project.startDate).toLocaleDateString()} -{' '}
                    {new Date(project.endDate).toLocaleDateString()}
                  </div>
                </div>
                <div>
                  <div className="flex items-center gap-1 text-muted-foreground mb-1">
                    <Users className="h-3 w-3" />
                    Utilities
                  </div>
                  <div className="font-medium">{project.utilities} companies</div>
                </div>
                <div>
                  <div className="flex items-center gap-1 text-muted-foreground mb-1">
                    {project.conflicts > 0 ? (
                      <AlertCircle className="h-3 w-3 text-amber-500" />
                    ) : (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    )}
                    Conflicts
                  </div>
                  <div
                    className={`font-medium ${project.conflicts > 0 ? 'text-amber-600' : 'text-green-600'}`}
                  >
                    {project.conflicts === 0 ? 'None' : `${project.conflicts} active`}
                  </div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Coordinator: <span className="font-medium">{project.coordinator}</span>
                  </span>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
            ))
          )}
        </div>
      )}
    </div>
  );
}
