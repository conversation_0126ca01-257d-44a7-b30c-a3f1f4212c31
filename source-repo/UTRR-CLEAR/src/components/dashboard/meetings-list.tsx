'use client';

import * as React from 'react';
import { useAuth } from '~/hooks/use-auth';
import { api } from '~/trpc/react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { MapPin, Calendar, Clock } from 'lucide-react';
import { CommentBadge } from '~/components/comments/comment-badge';
import { EmptyState } from '~/components/ui/empty-state';
import { LoadingSkeleton } from '~/components/ui/loading-skeleton';
import { formatMeetingTime, formatDate } from '~/lib/date-utils';

interface Meeting {
  id: string;
  title: string;
  description?: string | null;
  startTime: Date;
  endTime: Date;
  location: string;
  type: 'virtual' | 'in-person';
  project?: {
    id: string;
    name: string;
  } | null;
  notes?: string | null;
  createdAt: Date;
}

export function MeetingsList() {
  const { user } = useAuth();
  
  // Use real tRPC query
  const { data: meetingsData = [], isLoading, error } = api.meetings.getUpcoming.useQuery({
    limit: 5,
    daysAhead: 7,
  });
  
  // Type assertion since the API is temporarily returning empty array
  const meetings = meetingsData as Meeting[];

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>myMeetings</CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSkeleton variant="list" count={3} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>myMeetings</CardTitle>
        </CardHeader>
        <CardContent>
          <EmptyState
            icon={<Calendar className="h-12 w-12" />}
            title="Unable to load meetings"
            description="Please try refreshing the page"
            variant="default"
          />
        </CardContent>
      </Card>
    );
  }

  if (!meetings || meetings.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>myMeetings</CardTitle>
        </CardHeader>
        <CardContent>
          <EmptyState
            icon={<Calendar className="h-12 w-12" />}
            title="No Upcoming Meetings"
            description="You don't have any scheduled meetings"
            variant="default"
          />
        </CardContent>
      </Card>
    );
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>myMeetings</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-4">
          {meetings.map((meeting: any) => {
            const meetingDate = new Date(meeting.startTime);
            const month = formatDate(meeting.startTime, 'medium').split(' ')[0]?.toUpperCase() || '';
            const day = meetingDate.getDate();

            const isVirtual = meeting.type === 'virtual';

            // Use the date utility for consistent formatting
            const timeLabel = formatMeetingTime(meeting.startTime).split(' at ')[0] || '';
            const isToday = timeLabel === 'Today';

            return (
              <li
                key={meeting.id}
                className="flex space-x-3 p-3 rounded-lg border hover:bg-muted/30 transition-colors"
              >
                <div className="min-w-12 text-center">
                  <div
                    className={`rounded-md p-1 ${isToday ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
                  >
                    <div className="text-xs font-medium">{month}</div>
                    <div className="text-lg font-bold">{day}</div>
                  </div>
                </div>
                <div className="flex-grow">
                  <div className="flex items-center gap-2">
                    <div className="font-medium">{meeting.title}</div>
                    <CommentBadge
                      entityType="meeting"
                      entityId={meeting.id}
                      entityName={meeting.title}
                      variant="icon-only"
                      showZero={false}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                    <Clock className="h-3 w-3" />
                    <span>
                      {timeLabel} • {formatTime(meeting.startTime)} - {formatTime(meeting.endTime)}
                    </span>
                  </div>
                  <div className="text-xs flex items-center space-x-2 mt-1">
                    <MapPin className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">{meeting.location}</span>
                    {isVirtual && (
                      <Badge variant="secondary" className="text-xs">
                        Virtual
                      </Badge>
                    )}
                  </div>
                  {meeting.project && (
                    <div className="text-xs text-muted-foreground mt-1">
                      Project: {meeting.project.name}
                    </div>
                  )}
                </div>
                <div className="flex flex-col gap-1">
                  <Button variant="ghost" size="sm" className="self-start">
                    {isVirtual ? 'Join' : 'View'}
                  </Button>
                  {isToday && (
                    <Badge variant="outline" className="text-xs">
                      Today
                    </Badge>
                  )}
                </div>
              </li>
            );
          })}
        </ul>

        <div className="mt-4 pt-4 border-t">
          <Button variant="outline" size="sm" className="w-full">
            <Calendar className="h-4 w-4 mr-2" />
            View All Meetings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
