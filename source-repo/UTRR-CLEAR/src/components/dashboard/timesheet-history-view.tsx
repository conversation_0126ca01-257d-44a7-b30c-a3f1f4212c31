'use client';

import * as React from 'react';
import { useState, useMemo } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Calendar, Download, Eye, Clock } from 'lucide-react';
import { format, startOfWeek, endOfWeek } from 'date-fns';

interface TimesheetPeriod {
  id: string;
  startDate: Date;
  endDate: Date;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  totalHours: number;
  billableHours: number;
  nonBillableHours: number;
  submittedAt?: Date;
  approvedAt?: Date;
  notes?: string;
}

interface TimesheetEntry {
  id: string;
  projectId: string;
  projectName: string;
  date: Date;
  hours: number;
  description?: string;
  billable: boolean;
}

export function TimesheetHistoryView() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<string>('');
  const [timesheetPeriods, setTimesheetPeriods] = useState<TimesheetPeriod[]>([]);
  const [timesheetEntries, setTimesheetEntries] = useState<TimesheetEntry[]>([]);

  // Mock data for demonstration
  React.useEffect(() => {
    // Generate mock timesheet periods for the last 8 weeks
    const mockPeriods: TimesheetPeriod[] = [];
    for (let i = 0; i < 8; i++) {
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - i * 7);
      const periodStart = startOfWeek(weekStart, { weekStartsOn: 1 });
      const periodEnd = endOfWeek(weekStart, { weekStartsOn: 1 });

      const totalHours = 35 + Math.random() * 10; // Random hours between 35-45
      const billableHours = totalHours * (0.7 + Math.random() * 0.2); // 70-90% billable

      mockPeriods.push({
        id: `period-${i}`,
        startDate: periodStart,
        endDate: periodEnd,
        status: i === 0 ? 'draft' : i === 1 ? 'submitted' : 'approved',
        totalHours: Math.round(totalHours * 10) / 10,
        billableHours: Math.round(billableHours * 10) / 10,
        nonBillableHours: Math.round((totalHours - billableHours) * 10) / 10,
        submittedAt: i > 0 ? new Date(periodEnd.getTime() + 24 * 60 * 60 * 1000) : undefined,
        approvedAt: i > 1 ? new Date(periodEnd.getTime() + 3 * 24 * 60 * 60 * 1000) : undefined,
      });
    }
    setTimesheetPeriods(mockPeriods);

    // Set the most recent period as selected by default
    if (mockPeriods.length > 0 && mockPeriods[0]) {
      setSelectedPeriod(mockPeriods[0].id);
    }
  }, []);

  // Generate mock timesheet entries for selected period
  React.useEffect(() => {
    if (!selectedPeriod) return;

    const period = timesheetPeriods.find((p: any) => p.id === selectedPeriod);
    if (!period) return;

    const mockEntries: TimesheetEntry[] = [
      {
        id: '1',
        projectId: '190058-908',
        projectName: 'Highway 65 Utility Coordination',
        date: new Date(period.startDate.getTime() + 24 * 60 * 60 * 1000),
        hours: 8,
        description: 'Initial utility assessment and mapping',
        billable: true,
      },
      {
        id: '2',
        projectId: '190058-908',
        projectName: 'Highway 65 Utility Coordination',
        date: new Date(period.startDate.getTime() + 2 * 24 * 60 * 60 * 1000),
        hours: 7.5,
        description: 'Conflict detection and analysis',
        billable: true,
      },
      {
        id: '3',
        projectId: '190045-501',
        projectName: 'Downtown Infrastructure Upgrade',
        date: new Date(period.startDate.getTime() + 3 * 24 * 60 * 60 * 1000),
        hours: 6,
        description: 'Stakeholder coordination meetings',
        billable: true,
      },
      {
        id: '4',
        projectId: 'ADMIN',
        projectName: 'Administrative Time',
        date: new Date(period.startDate.getTime() + 4 * 24 * 60 * 60 * 1000),
        hours: 2,
        description: 'Team meetings and administrative tasks',
        billable: false,
      },
      {
        id: '5',
        projectId: 'TRAINING',
        projectName: 'Training & Development',
        date: new Date(period.startDate.getTime() + 4 * 24 * 60 * 60 * 1000),
        hours: 1.5,
        description: 'GIS software training',
        billable: false,
      },
    ];
    setTimesheetEntries(mockEntries);
  }, [selectedPeriod, timesheetPeriods]);

  const selectedPeriodData = useMemo(() => {
    return timesheetPeriods.find((p: any) => p.id === selectedPeriod);
  }, [selectedPeriod, timesheetPeriods]);

  const getStatusColor = (status: TimesheetPeriod['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-muted text-gray-800 dark:bg-background dark:text-gray-300';
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-muted text-gray-800';
    }
  };

  const formatPeriodLabel = (period: TimesheetPeriod) => {
    return `${format(period.startDate, 'MMM d')} - ${format(period.endDate, 'MMM d, yyyy')}`;
  };

  return (
    <div className="space-y-6">
      {/* Period Selection and Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timesheet History
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Period Selector */}
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Select Period:</label>
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-[300px]">
                <SelectValue placeholder="Select a timesheet period" />
              </SelectTrigger>
              <SelectContent>
                {timesheetPeriods.map((period: any) => (
                  <SelectItem key={period.id} value={period.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{formatPeriodLabel(period)}</span>
                      <Badge
                        className={`ml-2 ${getStatusColor(period.status)}`}
                        variant="secondary"
                      >
                        {period.status.charAt(0).toUpperCase() + period.status.slice(1)}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Period Summary */}
          {selectedPeriodData && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {selectedPeriodData.totalHours.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Total Hours</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {selectedPeriodData.billableHours.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Billable Hours</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {selectedPeriodData.nonBillableHours.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Non-Billable</div>
              </div>
              <div className="text-center">
                <Badge className={getStatusColor(selectedPeriodData.status)} variant="secondary">
                  {selectedPeriodData.status.charAt(0).toUpperCase() +
                    selectedPeriodData.status.slice(1)}
                </Badge>
                <div className="text-sm text-muted-foreground mt-1">Status</div>
              </div>
            </div>
          )}

          {/* Status Timeline */}
          {selectedPeriodData && (
            <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
              <div className="text-sm">
                <div className="font-medium">Period:</div>
                <div className="text-muted-foreground">{formatPeriodLabel(selectedPeriodData)}</div>
              </div>
              {selectedPeriodData.submittedAt && (
                <div className="text-sm">
                  <div className="font-medium">Submitted:</div>
                  <div className="text-muted-foreground">
                    {format(selectedPeriodData.submittedAt, 'MMM d, yyyy')}
                  </div>
                </div>
              )}
              {selectedPeriodData.approvedAt && (
                <div className="text-sm">
                  <div className="font-medium">Approved:</div>
                  <div className="text-muted-foreground">
                    {format(selectedPeriodData.approvedAt, 'MMM d, yyyy')}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Entries */}
      <Card>
        <CardHeader>
          <CardTitle>Time Entries</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-center">Hours</TableHead>
                  <TableHead className="text-center">Type</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {timesheetEntries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      No entries found for selected period
                    </TableCell>
                  </TableRow>
                ) : (
                  timesheetEntries.map((entry: any) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <div className="font-medium">{format(entry.date, 'EEE, MMM d')}</div>
                        <div className="text-xs text-muted-foreground">
                          {format(entry.date, 'yyyy')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{entry.projectName}</div>
                        {entry.projectId !== 'ADMIN' && entry.projectId !== 'TRAINING' && (
                          <Badge variant="outline" className="text-xs mt-1">
                            {entry.projectId}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {entry.description || 'No description provided'}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="font-medium">{entry.hours.toFixed(1)}</div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge
                          variant={entry.billable ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {entry.billable ? 'Billable' : 'Non-billable'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {timesheetEntries.length > 0 && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between items-center text-sm">
                <span className="font-medium">Total entries: {timesheetEntries.length}</span>
                <span className="font-medium">
                  Total hours:{' '}
                  {timesheetEntries.reduce((sum: any, entry: any) => sum + entry.hours, 0).toFixed(1)}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
