'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { ArrowUpIcon, ArrowDownIcon, TrendingUpIcon } from 'lucide-react';

interface ProjectMetric {
  name: string;
  value: number;
  total: number;
  percentage: number;
  trend?: number[];
  color?: string;
}

interface ProjectProgressCardProps {
  metric: ProjectMetric;
  index?: number;
}

// Simple sparkline component using SVG
function SimpleSparkline({
  data,
  height = 60,
  width = 100,
  color = 'hsl(var(--primary))',
  strokeWidth = 2,
}: {
  data: number[];
  height?: number;
  width?: number;
  color?: string;
  strokeWidth?: number;
}) {
  if (!data || data.length < 2) return null;

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  const points = data
    .map((value, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      return `${x},${y}`;
    })
    .join(' ');

  return (
    <svg
      width="100%"
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className="overflow-visible"
    >
      <polyline
        fill="none"
        stroke={color}
        strokeWidth={strokeWidth}
        points={points}
        vectorEffect="non-scaling-stroke"
      />
    </svg>
  );
}

export function ProjectProgressCard({ metric, index = 0 }: ProjectProgressCardProps) {
  // Calculate whether the trend is positive (true) or negative (false)
  const isTrendPositive = React.useMemo(() => {
    if (!metric.trend || metric.trend.length < 2) return true;
    const current = metric.trend[metric.trend.length - 1];
    const previous = metric.trend[metric.trend.length - 2];
    return current !== undefined && previous !== undefined && current >= previous;
  }, [metric.trend]);

  // Map metric names to appropriate colors if not provided
  const getMetricColor = () => {
    if (metric.color) return metric.color;

    switch (metric.name) {
      case 'Utilities Identified':
        return 'hsl(var(--primary))';
      case 'Conflicts Resolved':
        return 'rgb(34, 197, 94)'; // green
      case 'Documents Uploaded':
        return 'rgb(245, 158, 11)'; // amber
      case 'Overall Completion':
        return 'rgb(59, 130, 246)'; // blue
      default:
        return 'hsl(var(--primary))';
    }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <CardDescription className="flex justify-between items-center">
          <span>{metric.name}</span>
          {metric.trend && metric.trend.length > 1 && (
            <span
              className={`inline-flex items-center text-xs font-medium ${isTrendPositive ? 'text-emerald-600' : 'text-red-600'}`}
            >
              {isTrendPositive ? (
                <ArrowUpIcon className="h-3 w-3 mr-1" />
              ) : (
                <ArrowDownIcon className="h-3 w-3 mr-1" />
              )}
              {Math.abs(
                (metric.trend?.[metric.trend.length - 1] || 0) - (metric.trend?.[metric.trend.length - 2] || 0)
              ).toFixed(1)}
              %
            </span>
          )}
        </CardDescription>
        <div className="flex justify-between items-baseline">
          <CardTitle className="text-2xl">
            {metric.value}/{metric.total}
          </CardTitle>
          <span className="text-xl font-bold">{metric.percentage}%</span>
        </div>
      </CardHeader>
      <CardContent className="p-0 h-[60px]">
        {metric.trend && metric.trend.length > 1 ? (
          <div className="h-full p-2">
            <SimpleSparkline
              data={metric.trend}
              height={56}
              width={100}
              color={getMetricColor()}
              strokeWidth={2}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <TrendingUpIcon className="h-5 w-5 mr-2" />
            <span className="text-sm">Tracking started</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
