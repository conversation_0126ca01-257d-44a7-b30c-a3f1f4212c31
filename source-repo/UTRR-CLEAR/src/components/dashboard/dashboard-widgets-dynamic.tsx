'use client';

import dynamic from 'next/dynamic';
import { Skeleton } from '~/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '~/components/ui/card';

// Loading placeholders for each widget
const TasksListSkeleton = () => (
  <Card>
    <CardHeader>
      <Skeleton className="h-6 w-32" />
    </CardHeader>
    <CardContent className="space-y-3">
      {[1, 2, 3].map((i: any) => (
        <div key={i} className="flex items-center space-x-3">
          <Skeleton className="h-5 w-5 rounded" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
      ))}
    </CardContent>
  </Card>
);

const MeetingsListSkeleton = () => (
  <Card>
    <CardHeader>
      <Skeleton className="h-6 w-40" />
    </CardHeader>
    <CardContent className="space-y-3">
      {[1, 2, 3].map((i: any) => (
        <div key={i} className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <div className="flex items-center space-x-2">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-20" />
          </div>
        </div>
      ))}
    </CardContent>
  </Card>
);

const ProjectsListSkeleton = () => (
  <Card>
    <CardHeader>
      <Skeleton className="h-6 w-36" />
    </CardHeader>
    <CardContent className="space-y-3">
      {[1, 2].map((i: any) => (
        <div key={i} className="p-4 border rounded-lg space-y-2">
          <Skeleton className="h-5 w-48" />
          <Skeleton className="h-4 w-full" />
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
        </div>
      ))}
    </CardContent>
  </Card>
);

// Dynamic imports with custom loading states
export const TasksList = dynamic(
  () => import('./tasks-list').then((mod: any) => ({ default: mod.TasksList })),
  {
    loading: () => <TasksListSkeleton />,
    ssr: false,
  }
);

export const MeetingsList = dynamic(
  () => import('./meetings-list').then((mod: any) => ({ default: mod.MeetingsList })),
  {
    loading: () => <MeetingsListSkeleton />,
    ssr: false,
  }
);

export const MyProjectsList = dynamic(
  () => import('./my-projects-list').then((mod: any) => ({ default: mod.MyProjectsList })),
  {
    loading: () => <ProjectsListSkeleton />,
    ssr: false,
  }
);

// These widgets are critical and should load immediately
export { TimesheetSummary } from './timesheet-summary';