"use client";

import React, { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { GripVertical, MoreHorizontal, X, Settings } from "lucide-react";
import { cn } from "~/lib/utils";
import { WidgetConfigDialog } from "./widget-config-dialog";
import { useToast } from "~/hooks/use-toast";
import { api } from "~/trpc/react";
import { safeLog } from '~/lib/error-handler';

export interface WidgetConfig {
  id: string;
  type: string;
  title: string;
  description?: string;
  size: "small" | "medium" | "large" | "full";
  position: { x: number; y: number };
  settings?: Record<string, any>;
  isVisible: boolean;
}

interface DashboardWidgetProps {
  widget: WidgetConfig;
  children: React.ReactNode;
  onRemove?: (widgetId: string) => void;
  onConfigure?: (widgetId: string) => void;
  isDragging?: boolean;
  className?: string;
}

export function DashboardWidget({
  widget,
  children,
  onRemove,
  onConfigure,
  isDragging = false,
  className,
}: DashboardWidgetProps) {
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const { toast } = useToast();
  const utils = api.useUtils();

  const deleteWidgetMutation = api.widgets.deleteWidget.useMutation({
    onSuccess: () => {
      toast({
        title: "Widget Removed",
        description: "Widget has been removed from your dashboard.",
      });
      utils.widgets.getUserWidgets.invalidate();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove widget.",
        variant: "destructive",
      });
    },
  });

  const handleConfigure = () => {
    setIsConfigDialogOpen(true);
    onConfigure?.(widget.id);
  };

  const handleRemove = () => {
    if (confirm("Are you sure you want to remove this widget?")) {
      deleteWidgetMutation.mutate({
        id: widget.id,
      });
      onRemove?.(widget.id);
    }
  };
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: widget.id,
    data: {
      type: "widget",
      widget,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const sizeClasses = {
    small: "col-span-1 row-span-1",
    medium: "col-span-2 row-span-1",
    large: "col-span-2 row-span-2",
    full: "col-span-full row-span-1",
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative group transition-all duration-200",
        sizeClasses[widget.size],
        (isDragging || isSortableDragging) && "opacity-50 scale-105 shadow-lg",
        className
      )}
    >
      {/* Widget Header with Drag Handle */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-base">{widget.title}</CardTitle>
            {widget.description && (
              <CardDescription className="text-sm">
                {widget.description}
              </CardDescription>
            )}
          </div>
          
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {/* Drag Handle */}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 cursor-grab active:cursor-grabbing"
              {...attributes}
              {...listeners}
            >
              <GripVertical className="h-4 w-4" />
            </Button>

            {/* Widget Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleConfigure}>
                  <Settings className="mr-2 h-4 w-4" />
                  Configure
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleRemove}
                  className="text-destructive"
                  disabled={deleteWidgetMutation.isPending}
                >
                  <X className="mr-2 h-4 w-4" />
                  {deleteWidgetMutation.isPending ? "Removing..." : "Remove"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      {/* Widget Content */}
      <CardContent className="pt-0">
        {children}
      </CardContent>

      {/* Configuration Dialog */}
      <WidgetConfigDialog
        isOpen={isConfigDialogOpen}
        onClose={() => setIsConfigDialogOpen(false)}
        widget={widget}
        onSave={() => {
          // Widget will be re-fetched via React Query invalidation
        }}
      />
    </Card>
  );
}

// Widget size utilities
export const WIDGET_SIZES = {
  small: { width: 1, height: 1 },
  medium: { width: 2, height: 1 },
  large: { width: 2, height: 2 },
  full: { width: 12, height: 1 },
} as const;

// Default widget configurations
export const DEFAULT_WIDGETS: Omit<WidgetConfig, "id" | "position">[] = [
  {
    type: "stats",
    title: "Project Stats",
    description: "Overview of project metrics",
    size: "medium",
    isVisible: true,
  },
  {
    type: "chart",
    title: "Project Status Chart",
    description: "Visual breakdown of project statuses",
    size: "medium",
    isVisible: true,
  },
  {
    type: "recent-activity",
    title: "Recent Activity",
    description: "Latest project updates and changes",
    size: "medium",
    isVisible: true,
  },
  {
    type: "quick-actions",
    title: "Quick Actions",
    description: "Frequently used actions",
    size: "small",
    isVisible: true,
  },
  {
    type: "calendar",
    title: "Upcoming Deadlines",
    description: "Important dates and milestones",
    size: "medium",
    isVisible: true,
  },
  {
    type: "team-activity",
    title: "Team Activity",
    description: "What your team is working on",
    size: "large",
    isVisible: true,
  },
];

// Widget type definitions for type safety
export type WidgetType = 
  | "stats"
  | "chart" 
  | "recent-activity"
  | "quick-actions"
  | "calendar"
  | "team-activity";

// Hook for widget management
export function useWidgetConfig() {
  const [widgets, setWidgets] = React.useState<WidgetConfig[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  // Load widget configuration from user preferences
  React.useEffect(() => {
    // Initialize with default layout
    const defaultLayout = DEFAULT_WIDGETS.map((widget, index) => ({
      ...widget,
      id: `widget-${index}`,
      position: { x: (index % 3) * 2, y: Math.floor(index / 3) },
    }));
    
    setWidgets(defaultLayout);
    setIsLoading(false);
  }, []);

  const addWidget = (widgetType: WidgetType) => {
    const template = DEFAULT_WIDGETS.find((w: any) => w.type === widgetType);
    if (!template) return;

    const newWidget: WidgetConfig = {
      ...template,
      id: `widget-${Date.now()}`,
      position: { x: 0, y: 0 }, // Will be positioned by grid
    };

    setWidgets(prev => [...prev, newWidget]);
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  };

  const updateWidget = (widgetId: string, updates: Partial<WidgetConfig>) => {
    setWidgets(prev => 
      prev.map(w => w.id === widgetId ? { ...w, ...updates } : w)
    );
  };

  const reorderWidgets = (newOrder: WidgetConfig[]) => {
    setWidgets(newOrder);
  };

  const saveLayout = async () => {
    safeLog.info("Saving widget layout:", { widgets: JSON.stringify(widgets) });
    return Promise.resolve();
  };

  return {
    widgets,
    isLoading,
    addWidget,
    removeWidget,
    updateWidget,
    reorderWidgets,
    saveLayout,
  };
}
