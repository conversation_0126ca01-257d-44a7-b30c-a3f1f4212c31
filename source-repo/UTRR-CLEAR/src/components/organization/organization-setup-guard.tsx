'use client';

import { useEffect } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { useRouter, usePathname } from 'next/navigation';
import { api } from '~/trpc/react';
import { Loader2, Building2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { safeLog } from '~/lib/error-handler';

export function OrganizationSetupGuard({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  
  const { data: setupStatus, isLoading } = api.organizations.checkSetup.useQuery(undefined, {
    // Only check if we have a user and it's fully loaded
    enabled: !!user && !loading,
    // Since this is single-tenant and org is already setup, we can cache this
    staleTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    // Skip if we're already on the setup page
    if (pathname === '/admin/organization-setup') return;
    
    // If organization is not set up and user is admin, redirect to setup
    if (setupStatus && !setupStatus.isSetup && user?.role === 'admin') {
      safeLog.info('Redirecting to organization setup...');
      router.push('/admin/organization-setup');
    }
  }, [setupStatus, user, router, pathname]);

  // Show loading state while checking
  if (loading || (!user && !loading) || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // If org is not set up and user is NOT admin, show message
  if (setupStatus && !setupStatus.isSetup && user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Alert className="max-w-md">
          <Building2 className="h-4 w-4" />
          <AlertTitle>Organization Setup Required</AlertTitle>
          <AlertDescription className="mt-2">
            Your organization has not been configured yet. Please contact your administrator to complete the setup process.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // If org is not set up and user IS admin but on setup page, allow it
  if (setupStatus && !setupStatus.isSetup && user?.role === 'admin' && pathname === '/admin/organization-setup') {
    return <>{children}</>;
  }

  // Otherwise, render children normally
  return <>{children}</>;
}