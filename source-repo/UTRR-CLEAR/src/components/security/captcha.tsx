'use client';

import { useEffect, useState } from 'react';
import { cn } from '~/lib/utils';

/**
 * Simple CAPTCHA component for critical forms
 * Implements a lightweight, accessible CAPTCHA without external dependencies
 */

interface CaptchaProps {
  onVerify: (isValid: boolean) => void;
  className?: string;
}

interface CaptchaChallenge {
  question: string;
  answer: number;
  display: string;
}

/**
 * Generate a simple math-based CAPTCHA challenge
 */
function generateChallenge(): CaptchaChallenge {
  const operations = ['+', '-', '×'] as const;
  const operation = operations[Math.floor(Math.random() * operations.length)]!;
  
  let a: number;
  let b: number;
  let answer: number;
  let display: string;
  
  switch (operation) {
    case '+':
      a = Math.floor(Math.random() * 10) + 1;
      b = Math.floor(Math.random() * 10) + 1;
      answer = a + b;
      display = `${a} + ${b}`;
      break;
    case '-':
      // Ensure positive result
      a = Math.floor(Math.random() * 10) + 5;
      b = Math.floor(Math.random() * a);
      answer = a - b;
      display = `${a} - ${b}`;
      break;
    case '×':
      a = Math.floor(Math.random() * 5) + 1;
      b = Math.floor(Math.random() * 5) + 1;
      answer = a * b;
      display = `${a} × ${b}`;
      break;
    default:
      // This should never happen due to the type constraint, but TypeScript requires it
      a = 1;
      b = 1;
      answer = 2;
      display = '1 + 1';
  }
  
  return {
    question: `What is ${display}?`,
    answer,
    display
  };
}

export function Captcha({ onVerify, className }: CaptchaProps) {
  const [challenge, setChallenge] = useState<CaptchaChallenge>(() => generateChallenge());
  const [userAnswer, setUserAnswer] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState(false);
  const [attempts, setAttempts] = useState(0);
  
  // Regenerate challenge after too many failed attempts
  useEffect(() => {
    if (attempts >= 3) {
      setChallenge(generateChallenge());
      setAttempts(0);
      setError(false);
      setUserAnswer('');
    }
  }, [attempts]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const answer = parseInt(userAnswer, 10);
    if (isNaN(answer)) {
      setError(true);
      return;
    }
    
    if (answer === challenge.answer) {
      setIsVerified(true);
      setError(false);
      onVerify(true);
    } else {
      setError(true);
      setAttempts(prev => prev + 1);
      onVerify(false);
      
      // Clear input after error
      setTimeout(() => {
        setUserAnswer('');
      }, 1000);
    }
  };
  
  const handleRefresh = () => {
    setChallenge(generateChallenge());
    setUserAnswer('');
    setError(false);
    setIsVerified(false);
    setAttempts(0);
    onVerify(false);
  };
  
  if (isVerified) {
    return (
      <div className={cn('p-4 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800', className)}>
        <div className="flex items-center justify-center space-x-2">
          <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span className="text-sm font-medium text-green-700 dark:text-green-300">Verified</span>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn('space-y-3', className)}>
      <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="captcha-input" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {challenge.question}
            </label>
            <button
              type="button"
              onClick={handleRefresh}
              className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="Refresh CAPTCHA"
            >
              <svg className="w-4 h-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
          
          <div className="flex space-x-2">
            <input
              id="captcha-input"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              value={userAnswer}
              onChange={(e: any) => {
                setUserAnswer(e.target.value);
                setError(false);
              }}
              className={cn(
                'flex-1 px-3 py-2 border rounded-md text-sm',
                'focus:outline-none focus:ring-2 focus:ring-offset-2',
                error
                  ? 'border-red-300 focus:ring-red-500'
                  : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
              )}
              placeholder="Enter your answer"
              aria-label="CAPTCHA answer"
              aria-describedby={error ? 'captcha-error' : undefined}
              required
            />
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!userAnswer}
            >
              Verify
            </button>
          </div>
          
          {error && (
            <p id="captcha-error" className="text-xs text-red-600 dark:text-red-400">
              {attempts >= 2 ? 'Incorrect. One more attempt before refresh.' : 'Incorrect answer. Please try again.'}
            </p>
          )}
        </form>
      </div>
      
      <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
        Please solve this simple math problem to continue
      </p>
    </div>
  );
}

/**
 * Honeypot field for bot detection
 * This should be hidden from users but visible to bots
 */
export function HoneypotField() {
  return (
    <div className="hidden" aria-hidden="true">
      <label htmlFor="website">
        Leave this field empty
        <input
          type="text"
          id="website"
          name="website"
          tabIndex={-1}
          autoComplete="off"
        />
      </label>
    </div>
  );
}