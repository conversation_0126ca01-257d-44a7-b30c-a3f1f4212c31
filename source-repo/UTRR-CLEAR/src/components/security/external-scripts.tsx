import Script from 'next/script';

/**
 * External scripts with Subresource Integrity (SRI) hashes
 * This component manages all external scripts with proper security
 */

// SRI hashes for external resources
// These should be updated when upgrading external libraries
export const SRI_HASHES = {
  // Add SRI hashes as we identify external scripts
  // Example format:
  // 'https://cdn.example.com/script.js': 'sha384-...',
} as const;

interface ExternalScriptProps {
  src: string;
  strategy?: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload' | 'worker';
  onLoad?: () => void;
  onError?: () => void;
  id?: string;
}

/**
 * Secure external script loader with SRI
 */
export function ExternalScript({ 
  src, 
  strategy = 'afterInteractive',
  onLoad,
  onError,
  id
}: ExternalScriptProps) {
  const integrity = SRI_HASHES[src as keyof typeof SRI_HASHES];
  
  if (!integrity && process.env.NODE_ENV === 'production') {
    console.warn(`Missing SRI hash for external script: ${src}`);
  }

  return (
    <Script
      id={id}
      src={src}
      strategy={strategy}
      integrity={integrity}
      crossOrigin="anonymous"
      onLoad={onLoad}
      onError={onError}
    />
  );
}

/**
 * Vercel Analytics with proper security
 * Note: Vercel's own scripts are loaded dynamically and don't support SRI
 * We rely on their domain security instead
 */
export function SecureAnalytics() {
  // Vercel Analytics is loaded via their React component
  // which handles script injection internally
  return null;
}

/**
 * Generate SRI hash for a given resource
 * This is a utility function to help generate hashes during development
 */
export async function generateSRIHash(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    const text = await response.text();
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const hashBuffer = await crypto.subtle.digest('SHA-384', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashBase64 = btoa(String.fromCharCode(...hashArray));
    return `sha384-${hashBase64}`;
  } catch (error) {
    console.error(`Failed to generate SRI hash for ${url}:`, error);
    throw error;
  }
}