'use client';

import React, { useState, useRef } from 'react';
import { record, EventType } from 'rrweb';
import type { eventWithTime, RecordPlugin } from '@rrweb/types';

// Types for rrweb event data
interface IncrementalSnapshotData {
  source: number;
  type?: number;
  id?: number;
  texts?: string[];
  tagName?: string;
  attributes?: {
    placeholder?: string;
    name?: string;
    class?: string;
    [key: string]: string | undefined;
  };
  text?: string;
  isPassword?: boolean;
}

interface ConsolePayload {
  level: string;
  trace: string[];
}

interface PluginEventData {
  plugin: string;
  payload: ConsolePayload;
}
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Card } from '~/components/ui/card';
import { 
  Play, 
  Square, 
  Video,
  AlertCircle,
  MousePointer,
  Keyboard,
  Navigation,
  FileText,
  X
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { safeLog } from '~/lib/error-handler';

interface StepRecorderProps {
  onRecordingComplete: (steps: string) => void;
  onCancel?: () => void;
}

interface RecordedStep {
  type: 'navigation' | 'click' | 'input' | 'scroll' | 'error';
  description: string;
  timestamp: number;
  url?: string;
  element?: string;
  value?: string;
  error?: string;
}

// Privacy plugin to mask sensitive data
const privacyPlugin: RecordPlugin = {
  name: 'privacy',
  options: {},
  eventProcessor: (event: eventWithTime) => {
    // Mask password inputs
    if (event.type === EventType.IncrementalSnapshot) {
      const data = event.data as any;
      if (data.source === 5) { // Input source
        const target = data as any;
        if (target.text && target.isPassword) {
          target.text = '•'.repeat(target.text.length);
        }
      }
    }
    return event;
  },
};

export function StepRecorder({ onRecordingComplete, onCancel }: StepRecorderProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordedSteps, setRecordedSteps] = useState<RecordedStep[]>([]);
  const [events, setEvents] = useState<eventWithTime[]>([]);
  const stopFnRef = useRef<(() => void) | undefined | null>(null);
  const startTimeRef = useRef<number>(0);
  const newTabRef = useRef<Window | null>(null);

  // Function to process events into human-readable steps
  const processEventsToSteps = (events: eventWithTime[]): RecordedStep[] => {
    const steps: RecordedStep[] = [];
    let currentUrl = window.location.href;

    events.forEach((event: any) => {
      // Navigation events
      if (event.type === EventType.Meta && event.data.href) {
        if (event.data.href !== currentUrl) {
          currentUrl = event.data.href;
          steps.push({
            type: 'navigation',
            description: `Navigated to ${new URL(currentUrl).pathname}`,
            timestamp: event.timestamp,
            url: currentUrl,
          });
        }
      }

      // Click events
      if (event.type === EventType.IncrementalSnapshot) {
        const data = event.data as IncrementalSnapshotData;
        
        if (data.source === 2) { // MouseInteraction
          if (data.type === 2) { // Click
            const target = getElementDescription(data);
            if (target) {
              steps.push({
                type: 'click',
                description: `Clicked on ${target}`,
                timestamp: event.timestamp,
                element: target,
              });
            }
          }
        }

        // Input events
        if (data.source === 5) { // Input
          const target = getElementDescription(data);
          if (target && data.text !== undefined) {
            const value = data.isPassword ? '[password]' : data.text;
            steps.push({
              type: 'input',
              description: `Entered "${value}" in ${target}`,
              timestamp: event.timestamp,
              element: target,
              value: value,
            });
          }
        }
      }

      // Error events (console errors)
      if (event.type === EventType.Plugin && 'plugin' in event.data) {
        const pluginData = event.data as PluginEventData;
        if (pluginData.plugin === 'rrweb/console@1') {
          const payload = pluginData.payload;
          if (payload.level === 'error') {
            steps.push({
              type: 'error',
              description: `Error occurred: ${payload.trace[0]}`,
              timestamp: event.timestamp,
              error: payload.trace.join('\n'),
            });
          }
        }
      }
    });

    return steps;
  };

  // Helper to get element description
  const getElementDescription = (data: IncrementalSnapshotData): string | null => {
    // This is simplified - in production you'd want more sophisticated element identification
    if (data.id) {
      const texts = data.texts || [];
      const tag = data.tagName || 'element';
      
      if (texts.length > 0) {
        return `"${texts[0]}" ${tag}`;
      }
      
      if (data.attributes) {
        if (data.attributes.placeholder) {
          return `${tag} with placeholder "${data.attributes.placeholder}"`;
        }
        if (data.attributes.name) {
          return `${tag} named "${data.attributes.name}"`;
        }
        if (data.attributes.class) {
          const classes = data.attributes.class.split(' ');
          const buttonClass = classes.find((c: string) => c.includes('button') || c.includes('btn'));
          if (buttonClass) {
            return 'button';
          }
        }
      }
      
      return tag;
    }
    return null;
  };

  const startRecording = () => {
    setIsRecording(true);
    setRecordedSteps([]);
    setEvents([]);
    startTimeRef.current = Date.now();

    // Open new tab for recording
    const newTab = window.open(window.location.href, '_blank');
    if (!newTab) {
      alert('Please allow pop-ups to record steps in a new tab');
      setIsRecording(false);
      return;
    }
    
    newTabRef.current = newTab;

    // Inject recording script into new tab
    const checkTabReady = setInterval(() => {
      try {
        if (newTab.document && newTab.document.readyState === 'complete') {
          clearInterval(checkTabReady);
          
          // Add recording banner to new tab
          const banner = newTab.document.createElement('div');
          banner.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; right: 0; background: #ef4444; color: white; padding: 12px; text-align: center; z-index: 9999; font-family: system-ui;">
              <strong>🔴 Recording Steps...</strong> Reproduce your issue then close this tab to finish.
            </div>
          `;
          newTab.document.body.appendChild(banner);

          // Start recording in the new tab
          const stopFn = record({
            emit(event) {
              setEvents(prev => [...prev, event]);
            },
            plugins: [privacyPlugin],
            recordCanvas: false,
            recordCrossOriginIframes: false,
            maskAllInputs: false,
            maskInputOptions: {
              password: true,
              email: false,
            },
          });

          stopFnRef.current = stopFn;

          // Listen for tab close
          const checkTabClosed = setInterval(() => {
            if (newTab.closed) {
              clearInterval(checkTabClosed);
              stopRecording();
            }
          }, 1000);
        }
      } catch (e) {
        // Cross-origin error - tab navigated to different domain
        safeLog.warn('Cannot access new tab - cross-origin');
      }
    }, 100);
  };

  const stopRecording = () => {
    if (stopFnRef.current) {
      stopFnRef.current();
      stopFnRef.current = null;
    }

    if (newTabRef.current && !newTabRef.current.closed) {
      newTabRef.current.close();
    }

    setIsRecording(false);

    // Process events to steps
    const steps = processEventsToSteps(events);
    setRecordedSteps(steps);

    // Convert to markdown format
    const stepsMarkdown = steps
      .map((step, index) => {
        const time = new Date(step.timestamp - startTimeRef.current).toISOString().substring(14, 19);
        let icon = '';
        
        switch (step.type) {
          case 'navigation': icon = '🔗'; break;
          case 'click': icon = '👆'; break;
          case 'input': icon = '⌨️'; break;
          case 'error': icon = '❌'; break;
          default: icon = '•';
        }
        
        return `${index + 1}. ${icon} [${time}] ${step.description}`;
      })
      .join('\n');

    if (steps.length > 0) {
      onRecordingComplete(stepsMarkdown);
    }
  };

  const getStepIcon = (type: RecordedStep['type']) => {
    switch (type) {
      case 'navigation': return <Navigation className="h-4 w-4" />;
      case 'click': return <MousePointer className="h-4 w-4" />;
      case 'input': return <Keyboard className="h-4 w-4" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getStepColor = (type: RecordedStep['type']) => {
    switch (type) {
      case 'navigation': return 'text-blue-600';
      case 'click': return 'text-green-600';
      case 'input': return 'text-purple-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-4">
      {!isRecording && recordedSteps.length === 0 && (
        <Card className="p-6">
          <div className="text-center space-y-4">
            <Video className="h-12 w-12 mx-auto text-muted-foreground" />
            <div>
              <h3 className="font-semibold text-lg">Record Steps to Reproduce</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Click &ldquo;Start Recording&rdquo; to open a new tab where you can demonstrate the issue
              </p>
            </div>
            <div className="flex justify-center gap-3">
              <Button onClick={startRecording} size="lg">
                <Play className="mr-2 h-4 w-4" />
                Start Recording
              </Button>
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            </div>
          </div>
        </Card>
      )}

      {isRecording && (
        <Card className="p-6 border-red-200 bg-red-50">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="relative">
                <Video className="h-12 w-12 text-red-600" />
                <div className="absolute -top-1 -right-1">
                  <span className="flex h-3 w-3">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg">Recording in Progress...</h3>
              <p className="text-sm text-muted-foreground mt-1">
                A new tab has opened. Reproduce your issue there, then close the tab to finish.
              </p>
            </div>
            <Button onClick={stopRecording} variant="destructive" size="lg">
              <Square className="mr-2 h-4 w-4" />
              Stop Recording
            </Button>
          </div>
        </Card>
      )}

      {!isRecording && recordedSteps.length > 0 && (
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg">Recorded Steps</h3>
              <Badge variant="secondary">{recordedSteps.length} steps</Badge>
            </div>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {recordedSteps.map((step, index) => (
                <div key={index} className="flex items-start gap-3 p-2 rounded hover:bg-muted/50">
                  <div className={cn("mt-0.5", getStepColor(step.type))}>
                    {getStepIcon(step.type)}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">{step.description}</p>
                    {step.error && (
                      <pre className="text-xs text-red-600 mt-1 p-2 bg-red-50 rounded overflow-x-auto">
                        {step.error}
                      </pre>
                    )}
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {new Date(step.timestamp - startTimeRef.current).toISOString().substring(14, 19)}
                  </span>
                </div>
              ))}
            </div>

            <div className="flex gap-3">
              <Button onClick={startRecording} variant="outline">
                <Play className="mr-2 h-4 w-4" />
                Re-record
              </Button>
              <Button onClick={() => onRecordingComplete('')} variant="outline">
                <X className="mr-2 h-4 w-4" />
                Clear Steps
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}