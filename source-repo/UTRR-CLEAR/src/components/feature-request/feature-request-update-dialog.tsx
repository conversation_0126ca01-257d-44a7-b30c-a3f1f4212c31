'use client';

import * as React from 'react';
import { useState } from 'react';
import { useAuth } from '~/hooks/use-auth';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '~/components/ui/dialog';
import { Button } from '~/components/ui/button';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Badge } from '~/components/ui/badge';
import { Label } from '~/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { toast } from '~/hooks/use-toast';
import {
  MessageSquare,
  User,
  Clock,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  AlertCircle,
  Calendar,
  ArrowRight,
} from 'lucide-react';
import { format } from 'date-fns';

export interface FeatureRequestUpdate {
  id?: string;
  requestId: string;
  status?: 'open' | 'in-progress' | 'resolved' | 'closed' | 'on-hold';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  comment: string;
  isPublic: boolean;
  authorId: string;
  authorName: string;
  authorRole: string;
  createdAt?: Date;
  attachments?: string[];
}

export interface FeatureRequestWithHistory {
  id: string;
  title: string;
  description: string;
  type: 'feature' | 'bug' | 'enhancement' | 'question';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in-progress' | 'resolved' | 'closed' | 'on-hold';
  category: string;
  submitterName: string;
  submitterEmail: string;
  createdAt: Date;
  updatedAt: Date;
  updates: FeatureRequestUpdate[];
}

interface FeatureRequestUpdateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  featureRequest: FeatureRequestWithHistory | null;
  onUpdate: (update: FeatureRequestUpdate) => void;
  userRole?: 'admin' | 'manager' | 'user';
}

export function FeatureRequestUpdateDialog({
  open,
  onOpenChange,
  featureRequest,
  onUpdate,
  userRole = 'user',
}: FeatureRequestUpdateDialogProps) {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [comment, setComment] = useState('');
  const [newStatus, setNewStatus] = useState<string>('');
  const [newPriority, setNewPriority] = useState<string>('');
  const [isPublic, setIsPublic] = useState(true);

  const canUpdateStatus = userRole === 'admin' || userRole === 'manager';
  const canUpdatePriority = userRole === 'admin' || userRole === 'manager';

  const statusOptions = [
    {
      value: 'open',
      label: 'Open',
      icon: AlertCircle,
      color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    },
    {
      value: 'in-progress',
      label: 'In Progress',
      icon: Play,
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    },
    {
      value: 'on-hold',
      label: 'On Hold',
      icon: Pause,
      color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    },
    {
      value: 'resolved',
      label: 'Resolved',
      icon: CheckCircle,
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    },
    {
      value: 'closed',
      label: 'Closed',
      icon: XCircle,
      color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    },
  ];

  const priorityOptions = [
    {
      value: 'low',
      label: 'Low',
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    },
    {
      value: 'medium',
      label: 'Medium',
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    },
    {
      value: 'high',
      label: 'High',
      color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    },
    {
      value: 'critical',
      label: 'Critical',
      color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    },
  ];

  React.useEffect(() => {
    if (open && featureRequest) {
      setNewStatus(featureRequest.status);
      setNewPriority(featureRequest.priority);
      setComment('');
      setIsPublic(true);
    }
  }, [open, featureRequest]);

  const handleSubmit = async () => {
    if (
      !comment.trim() &&
      newStatus === featureRequest?.status &&
      newPriority === featureRequest?.priority
    ) {
      toast({
        title: 'No changes to submit',
        description: 'Please add a comment or change the status/priority.',
        variant: 'destructive',
      });
      return;
    }

    if (!featureRequest || !user) return;

    setIsSubmitting(true);

    try {
      const update: FeatureRequestUpdate = {
        requestId: featureRequest.id,
        comment: comment.trim(),
        isPublic,
        authorId: user.id || '',
        authorName: (user as any).user_metadata?.full_name || user.email || '',
        authorRole: userRole,
        createdAt: new Date(),
      };

      // Add status change if different
      if (newStatus !== featureRequest.status) {
        update.status = newStatus as any;
      }

      // Add priority change if different
      if (newPriority !== featureRequest.priority) {
        update.priority = newPriority as any;
      }

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onUpdate(update);

      toast({
        title: 'Update submitted',
        description: 'Your update has been added to the feature request.',
      });

      setComment('');
      onOpenChange(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit update. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusDisplay = (status: string) => {
    const statusOption = statusOptions.find((s: any) => s.value === status);
    if (!statusOption) return null;

    return (
      <div className="flex items-center gap-2">
        <statusOption.icon className="h-4 w-4" />
        <Badge className={statusOption.color} variant="secondary">
          {statusOption.label}
        </Badge>
      </div>
    );
  };

  const getPriorityDisplay = (priority: string) => {
    const priorityOption = priorityOptions.find((p: any) => p.value === priority);
    if (!priorityOption) return null;

    return (
      <Badge className={priorityOption.color} variant="secondary">
        {priorityOption.label}
      </Badge>
    );
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((word: any) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!featureRequest) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Update Feature Request
          </DialogTitle>
          <DialogDescription>
            Add comments, update status, or change priority for this feature request.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Request Summary */}
            <div className="p-4 bg-muted/30 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{featureRequest.title}</h3>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline">{featureRequest.type}</Badge>
                    <Badge variant="outline">{featureRequest.category}</Badge>
                    {getStatusDisplay(featureRequest.status)}
                    {getPriorityDisplay(featureRequest.priority)}
                  </div>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">{featureRequest.description}</p>
              <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
                <span>Submitted by {featureRequest.submitterName}</span>
                <span>•</span>
                <span>{format(featureRequest.createdAt, 'MMM d, yyyy')}</span>
              </div>
            </div>

            {/* Activity History */}
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Activity History
              </h3>

              <div className="space-y-3">
                {featureRequest.updates.length === 0 ? (
                  <div className="text-center py-6 text-muted-foreground">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No updates yet. Be the first to add a comment!</p>
                  </div>
                ) : (
                  featureRequest.updates.map((update, index) => (
                    <div key={index} className="flex gap-3 p-3 bg-background border rounded-lg">
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarFallback>{getInitials(update.authorName)}</AvatarFallback>
                      </Avatar>

                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm">{update.authorName}</span>
                          <Badge variant="outline" className="text-xs">
                            {update.authorRole}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {format(update.createdAt || new Date(), 'MMM d, yyyy h:mm a')}
                          </span>
                          {!update.isPublic && (
                            <Badge variant="secondary" className="text-xs">
                              Internal
                            </Badge>
                          )}
                        </div>

                        {/* Status/Priority Changes */}
                        {(update.status || update.priority) && (
                          <div className="flex items-center gap-2 mb-2 text-sm">
                            {update.status && (
                              <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                                <ArrowRight className="h-3 w-3" />
                                <span>Status changed to</span>
                                {getStatusDisplay(update.status)}
                              </div>
                            )}
                            {update.priority && (
                              <div className="flex items-center gap-1 text-orange-600 dark:text-orange-400">
                                <ArrowRight className="h-3 w-3" />
                                <span>Priority changed to</span>
                                {getPriorityDisplay(update.priority)}
                              </div>
                            )}
                          </div>
                        )}

                        {update.comment && (
                          <div className="text-sm bg-muted/30 p-3 rounded-md">{update.comment}</div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Status Update */}
            {canUpdateStatus && (
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status: any) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center gap-2">
                          <status.icon className="h-4 w-4" />
                          <span>{status.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Priority Update */}
            {canUpdatePriority && (
              <div className="space-y-2">
                <Label>Priority</Label>
                <Select value={newPriority} onValueChange={setNewPriority}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {priorityOptions.map((priority: any) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        <Badge className={priority.color} variant="secondary">
                          {priority.label}
                        </Badge>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Add Comment */}
            <div className="space-y-2">
              <Label htmlFor="comment">Add Comment</Label>
              <Textarea
                id="comment"
                placeholder="Add a comment or update..."
                value={comment}
                onChange={(e: any) => setComment(e.target.value)}
                className="min-h-[120px]"
              />
            </div>

            {/* Visibility */}
            {(userRole === 'admin' || userRole === 'manager') && (
              <div className="space-y-2">
                <Label>Visibility</Label>
                <Select
                  value={isPublic ? 'public' : 'internal'}
                  onValueChange={(value) => setIsPublic(value === 'public')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span>Public (visible to submitter)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="internal">
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        <span>Internal (staff only)</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Request Info */}
            <div className="p-3 bg-muted/30 rounded-lg space-y-2 text-sm">
              <div className="font-medium">Request Details</div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">ID:</span>
                <span className="font-mono">{featureRequest.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Submitted:</span>
                <span>{format(featureRequest.createdAt, 'MMM d, yyyy')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last Updated:</span>
                <span>{format(featureRequest.updatedAt, 'MMM d, yyyy')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Submitter:</span>
                <span>{featureRequest.submitterName}</span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                Submitting...
              </>
            ) : (
              <>
                <MessageSquare className="h-4 w-4 mr-2" />
                Add Update
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
