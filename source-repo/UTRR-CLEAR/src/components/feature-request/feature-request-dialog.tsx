'use client';

import * as React from 'react';
import { useState } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { useAdminAccess } from '~/hooks/use-auth';
import { diagnosticsCollector } from '~/lib/browser-diagnostics';
import { StepRecorder } from './step-recorder';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '~/components/ui/dialog';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Badge } from '~/components/ui/badge';
import { Label } from '~/components/ui/label';
import { Checkbox } from '~/components/ui/checkbox';
import { toast } from '~/hooks/use-toast';
import {
  Lightbulb,
  Bug,
  Zap,
  Plus,
  X,
  Star,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Terminal,
  Code2,
  Video,
} from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '~/components/ui/collapsible';
import { safeLog } from '~/lib/error-handler';

export interface FeatureRequest {
  id?: string;
  title: string;
  description: string;
  type: 'feature' | 'bug' | 'enhancement' | 'question';
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  expectedBehavior?: string;
  currentBehavior?: string;
  stepsToReproduce?: string;
  browserInfo?: string;
  screenshots?: string[];
  affectsWorkflow: boolean;
  submitterId?: string;
  submitterName?: string;
  submitterEmail?: string;
  status?: 'open' | 'in-progress' | 'resolved' | 'closed';
  createdAt?: Date;
  createGithubIssue?: boolean;
  assignToCopilot?: boolean;
}

interface FeatureRequestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (request: FeatureRequest) => void;
  initialData?: Partial<FeatureRequest>;
  mode?: 'create' | 'edit';
}

export function FeatureRequestDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode = 'create',
}: FeatureRequestDialogProps) {
  const { user } = useAuth();
  const { isAdmin } = useAdminAccess();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [createGithubIssue, setCreateGithubIssue] = useState(true); // Default to true
  const [assignToCopilot, setAssignToCopilot] = useState(false);

  const [formData, setFormData] = useState<Partial<FeatureRequest>>({
    title: '',
    description: '',
    type: 'feature',
    priority: 'medium',
    category: '',
    expectedBehavior: '',
    currentBehavior: '',
    stepsToReproduce: '',
    browserInfo: '',
    affectsWorkflow: false,
    ...initialData,
  });

  const requestTypes = [
    { value: 'feature', label: 'Feature Request', icon: Lightbulb, color: 'text-blue-600' },
    { value: 'bug', label: 'Bug Report', icon: Bug, color: 'text-red-600' },
    { value: 'enhancement', label: 'Enhancement', icon: Zap, color: 'text-purple-600' },
    { value: 'question', label: 'Question/Support', icon: AlertCircle, color: 'text-orange-600' },
  ];

  const priorities = [
    {
      value: 'low',
      label: 'Low',
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    },
    {
      value: 'medium',
      label: 'Medium',
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    },
    {
      value: 'high',
      label: 'High',
      color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    },
    {
      value: 'critical',
      label: 'Critical',
      color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    },
  ];

  const categories = [
    'User Interface',
    'Performance',
    'Database',
    'Mapping/GIS',
    'Project Management',
    'Reporting',
    'Integration',
    'Authentication',
    'Mobile/Responsive',
    'Documentation',
    'Other',
  ];

  // Auto-detect browser info and collect diagnostics
  React.useEffect(() => {
    if (open && !formData.browserInfo) {
      // Collect full diagnostics
      const diagnostics = diagnosticsCollector?.collectDiagnostics();
      
      if (diagnostics) {
        // Create a formatted summary for the browser info field
        const browserInfo = diagnosticsCollector?.formatForReport() || '';
        setFormData((prev) => ({ ...prev, browserInfo }));
        
        // Log to console for debugging
        safeLog.info('📊 Feature Request Diagnostics Collected:', { diagnostics: JSON.stringify(diagnostics) });
        
        // Auto-expand diagnostics for bug reports
        if (formData.type === 'bug') {
          setShowDiagnostics(true);
        }
      } else {
        // Fallback to basic info
        const browserInfo = `${navigator.userAgent} | Screen: ${window.screen.width}x${window.screen.height}`;
        setFormData((prev) => ({ ...prev, browserInfo }));
      }
    }
  }, [open, formData.browserInfo, formData.type]);

  const handleSubmit = async () => {
    // Validation
    if (!formData.title?.trim()) {
      toast({
        title: 'Title required',
        description: 'Please provide a title for your request.',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.description?.trim()) {
      toast({
        title: 'Description required',
        description: 'Please provide a description for your request.',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.category) {
      toast({
        title: 'Category required',
        description: 'Please select a category for your request.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const requestData: FeatureRequest = {
        ...(formData as FeatureRequest),
        submitterId: user?.id || '',
        submitterName: user?.user_metadata?.full_name || '',
        submitterEmail: user?.email || '',
        status: initialData?.status || 'open',
        createdAt: initialData?.createdAt || new Date(),
        createGithubIssue,
        assignToCopilot: isAdmin ? assignToCopilot : false,
      };

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSubmit(requestData);

      toast({
        title: mode === 'create' ? 'Request submitted' : 'Request updated',
        description:
          mode === 'create'
            ? 'Your feature request has been submitted successfully.'
            : 'Your feature request has been updated successfully.',
      });

      // Reset form if creating new request
      if (mode === 'create') {
        setFormData({
          title: '',
          description: '',
          type: 'feature',
          priority: 'medium',
          category: '',
          expectedBehavior: '',
          currentBehavior: '',
          stepsToReproduce: '',
          browserInfo: formData.browserInfo, // Keep browser info
          affectsWorkflow: false,
        });
      }

      onOpenChange(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedType = requestTypes.find((t: any) => t.value === formData.type);
  const selectedPriority = priorities.find((p: any) => p.value === formData.priority);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {selectedType && <selectedType.icon className={`h-5 w-5 ${selectedType.color}`} />}
            {mode === 'create' ? 'Submit Feature Request' : 'Edit Feature Request'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Help us improve the platform by sharing your ideas, reporting bugs, or asking questions.'
              : 'Update your feature request details.'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Request Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: 'feature' | 'bug' | 'enhancement' | 'question') => setFormData((prev) => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select request type" />
                  </SelectTrigger>
                  <SelectContent>
                    {requestTypes.map((type: any) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <type.icon className={`h-4 w-4 ${type.color}`} />
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority *</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value: 'low' | 'medium' | 'high' | 'critical') =>
                    setFormData((prev) => ({ ...prev, priority: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {priorities.map((priority: any) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        <div className="flex items-center gap-2">
                          <Badge className={priority.color} variant="secondary">
                            {priority.label}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category: any) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                placeholder="Enter a descriptive title..."
                value={formData.title}
                onChange={(e: any) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                placeholder="Provide a detailed description..."
                className="min-h-[120px]"
                value={formData.description}
                onChange={(e: any) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
              />
            </div>
          </div>

          {/* Type-specific fields */}
          {formData.type === 'bug' && (
            <div className="space-y-4 p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
              <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
                <Bug className="h-4 w-4" />
                <span className="font-medium">Bug Report Details</span>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currentBehavior">Current Behavior</Label>
                <Textarea
                  id="currentBehavior"
                  placeholder="Describe what actually happens..."
                  value={formData.currentBehavior || ''}
                  onChange={(e: any) =>
                    setFormData((prev) => ({ ...prev, currentBehavior: e.target.value }))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="expectedBehavior">Expected Behavior</Label>
                <Textarea
                  id="expectedBehavior"
                  placeholder="Describe what should happen..."
                  value={formData.expectedBehavior || ''}
                  onChange={(e: any) =>
                    setFormData((prev) => ({ ...prev, expectedBehavior: e.target.value }))
                  }
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="stepsToReproduce">Steps to Reproduce</Label>
                  <StepRecorder
                    onRecordingComplete={(steps) => {
                      setFormData((prev) => ({
                        ...prev,
                        stepsToReproduce: steps
                      }));
                      toast({
                        title: "Recording Complete",
                        description: "Steps have been added to the form",
                      });
                    }}
                  />
                </div>
                <Textarea
                  id="stepsToReproduce"
                  placeholder="1. Go to...&#10;2. Click on...&#10;3. See error..."
                  value={formData.stepsToReproduce || ''}
                  onChange={(e: any) =>
                    setFormData((prev) => ({ ...prev, stepsToReproduce: e.target.value }))
                  }
                />
              </div>
            </div>
          )}

          {formData.type === 'feature' && (
            <div className="space-y-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                <Lightbulb className="h-4 w-4" />
                <span className="font-medium">Feature Request Details</span>
              </div>

              <div className="space-y-2">
                <Label htmlFor="expectedBehavior">Desired Functionality</Label>
                <Textarea
                  id="expectedBehavior"
                  placeholder="Describe how the feature should work..."
                  value={formData.expectedBehavior || ''}
                  onChange={(e: any) =>
                    setFormData((prev) => ({ ...prev, expectedBehavior: e.target.value }))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="currentBehavior">Current Workflow</Label>
                <Textarea
                  id="currentBehavior"
                  placeholder="Describe how you currently accomplish this task..."
                  value={formData.currentBehavior || ''}
                  onChange={(e: any) =>
                    setFormData((prev) => ({ ...prev, currentBehavior: e.target.value }))
                  }
                />
              </div>
            </div>
          )}

          {/* Additional Information */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="affectsWorkflow"
                checked={formData.affectsWorkflow}
                onCheckedChange={(checked) =>
                  setFormData((prev) => ({ ...prev, affectsWorkflow: !!checked }))
                }
              />
              <Label htmlFor="affectsWorkflow" className="text-sm">
                This affects my daily workflow
              </Label>
            </div>

            <Collapsible open={showDiagnostics} onOpenChange={setShowDiagnostics}>
              <div className="space-y-2">
                <CollapsibleTrigger asChild>
                  <Button 
                    type="button" 
                    variant="ghost" 
                    className="w-full justify-between p-2"
                  >
                    <div className="flex items-center gap-2">
                      <Terminal className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Browser Diagnostics & Console Logs
                      </span>
                      {formData.browserInfo && formData.browserInfo.includes('ERRORS (') && 
                        formData.browserInfo.match(/ERRORS \((\d+)\)/)?.[1] !== '0' && (
                        <Badge variant="destructive" className="text-xs">
                          Has Errors
                        </Badge>
                      )}
                    </div>
                    {showDiagnostics ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                
                <CollapsibleContent className="space-y-2">
                  <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
                    <p className="font-medium mb-1">🔍 Auto-collected diagnostics include:</p>
                    <ul className="list-disc list-inside space-y-1 ml-2">
                      <li>Recent console logs (errors, warnings, info)</li>
                      <li>JavaScript errors and stack traces</li>
                      <li>Performance metrics and memory usage</li>
                      <li>Browser and system information</li>
                      <li>Network connection details</li>
                    </ul>
                  </div>
                  
                  <Textarea
                    id="browserInfo"
                    placeholder="Browser and system details..."
                    className="text-xs font-mono bg-muted/30"
                    value={formData.browserInfo || ''}
                    onChange={(e: any) => setFormData((prev) => ({ ...prev, browserInfo: e.target.value }))}
                    rows={12}
                  />
                  
                  <div className="flex items-center gap-2 text-xs">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const diagnostics = diagnosticsCollector?.collectDiagnostics();
                        if (diagnostics) {
                          const browserInfo = diagnosticsCollector?.formatForReport() || '';
                          setFormData((prev) => ({ ...prev, browserInfo }));
                          toast({
                            title: "Diagnostics Refreshed",
                            description: "Browser diagnostics have been updated with the latest information.",
                          });
                        }
                      }}
                    >
                      Refresh Diagnostics
                    </Button>
                    <span className="text-muted-foreground">
                      This data helps developers debug issues faster
                    </span>
                  </div>
                </CollapsibleContent>
              </div>
            </Collapsible>

            {/* GitHub Integration */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-muted/30 rounded-lg">
                <Checkbox
                  id="createGithubIssue"
                  checked={createGithubIssue}
                  onCheckedChange={(checked) => setCreateGithubIssue(!!checked)}
                />
                <Label 
                  htmlFor="createGithubIssue" 
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <Code2 className="h-4 w-4" />
                  <span className="text-sm">
                    Also create a GitHub issue for tracking
                  </span>
                </Label>
                <Badge variant="secondary" className="text-xs">
                  Recommended
                </Badge>
              </div>
              
              {/* Copilot Assignment - Admin Only */}
              {createGithubIssue && (
                <div className={`flex items-center space-x-3 p-3 rounded-lg transition-opacity ${
                  isAdmin ? 'bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800' : 'bg-muted/10 opacity-60'
                }`}>
                  <Checkbox
                    id="assignToCopilot"
                    checked={assignToCopilot}
                    onCheckedChange={(checked) => setAssignToCopilot(!!checked)}
                    disabled={!isAdmin}
                  />
                  <Label 
                    htmlFor="assignToCopilot" 
                    className={`flex items-center gap-2 ${
                      isAdmin ? 'cursor-pointer' : 'cursor-not-allowed'
                    }`}
                  >
                    <Terminal className="h-4 w-4" />
                    <span className="text-sm">
                      Add [COPILOT] prefix for GitHub Copilot assignment
                    </span>
                  </Label>
                  {isAdmin ? (
                    <Badge variant="outline" className="text-xs text-purple-600 dark:text-purple-400">
                      Admin Only
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">
                      Admin Required
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Preview */}
          <div className="p-4 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <span className="text-sm font-medium">Preview:</span>
              {selectedType && <selectedType.icon className={`h-4 w-4 ${selectedType.color}`} />}
              {selectedPriority && (
                <Badge className={selectedPriority.color} variant="secondary">
                  {selectedPriority.label}
                </Badge>
              )}
              {formData.category && <Badge variant="outline">{formData.category}</Badge>}
              {formData.affectsWorkflow && (
                <Badge variant="outline" className="text-orange-600">
                  <Users className="h-3 w-3 mr-1" />
                  Workflow Impact
                </Badge>
              )}
            </div>
            <div className="text-sm">
              <div className="font-medium">{formData.title || 'Title will appear here'}</div>
              <div className="text-muted-foreground mt-1">
                {formData.description || 'Description will appear here'}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                {mode === 'create' ? 'Submitting...' : 'Updating...'}
              </>
            ) : (
              <>
                {mode === 'create' ? (
                  <Plus className="h-4 w-4 mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                {mode === 'create' ? 'Submit Request' : 'Update Request'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
