"use client";

import React, { useState, useRef, useEffect } from "react";
import { useComments } from "./comment-provider";
import { api } from "~/trpc/react";
import { useToast } from "~/hooks/use-toast";
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "~/components/ui/sheet";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Separator } from "~/components/ui/separator";
import { Skeleton } from "~/components/ui/skeleton";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "~/components/ui/dropdown-menu";
import { MoreVertical, Edit, Trash, Reply, Send, X, AtSign } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface Comment {
  id: string;
  content: string;
  created_at: Date;
  updated_at: Date;
  user_id: number;
  commentable_type: string;
  commentable_id: string;
  parent_id: string | null;
  mentions: number[];
  deleted_at: Date | null;
  user: {
    id: number;
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  };
  replies?: Comment[];
}

export function CommentDrawer() {
  const { isDrawerOpen, closeCommentDrawer, entityType, entityId, entityName } = useComments();
  const { toast } = useToast();
  const [newComment, setNewComment] = useState("");
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState("");
  const [replyingToId, setReplyingToId] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const replyTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Fetch comments
  const { data: comments, isLoading, refetch } = api.comments.getComments.useQuery(
    {
      commentableType: entityType ?? "",
      commentableId: entityId ?? "",
    },
    {
      enabled: isDrawerOpen && !!entityType && !!entityId,
    }
  );

  // Mutations
  const createComment = api.comments.create.useMutation({
    onSuccess: () => {
      setNewComment("");
      refetch();
      toast({
        title: "Comment added",
        description: "Your comment has been posted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    },
  });

  const updateComment = api.comments.update.useMutation({
    onSuccess: () => {
      setEditingCommentId(null);
      setEditingContent("");
      refetch();
      toast({
        title: "Comment updated",
        description: "Your comment has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update comment. Please try again.",
        variant: "destructive",
      });
    },
  });

  const deleteComment = api.comments.delete.useMutation({
    onSuccess: () => {
      refetch();
      toast({
        title: "Comment deleted",
        description: "Your comment has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Handle submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !entityType || !entityId) return;

    createComment.mutate({
      commentableType: entityType,
      commentableId: entityId,
      content: newComment.trim(),
    });
  };

  // Handle reply
  const handleReply = (parentId: string) => {
    if (!replyContent.trim() || !entityType || !entityId) return;

    createComment.mutate({
      commentableType: entityType,
      commentableId: entityId,
      content: replyContent.trim(),
      parentId,
    });

    setReplyingToId(null);
    setReplyContent("");
  };

  // Handle edit
  const handleEdit = (commentId: string) => {
    if (!editingContent.trim()) return;

    updateComment.mutate({
      id: commentId,
      content: editingContent.trim(),
    });
  };

  // Auto-resize textarea
  const autoResize = (textarea: HTMLTextAreaElement | null) => {
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  useEffect(() => {
    autoResize(textareaRef.current);
  }, [newComment]);

  useEffect(() => {
    autoResize(replyTextareaRef.current);
  }, [replyContent]);

  // Render comment
  const renderComment = (comment: Comment, isReply = false) => {
    const isEditing = editingCommentId === comment.id;
    const isReplying = replyingToId === comment.id;
    const userInitials = `${comment.user.first_name?.[0] ?? ""}${comment.user.last_name?.[0] ?? ""}`.toUpperCase();

    return (
      <div key={comment.id} className={`${isReply ? "ml-12" : ""} mb-4`}>
        <div className="flex items-start gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={comment.user.avatar_url ?? undefined} />
            <AvatarFallback>{userInitials || "U"}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm">
                  {comment.user.first_name} {comment.user.last_name}
                </span>
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                </span>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {!isReply && (
                    <DropdownMenuItem onClick={() => setReplyingToId(comment.id)}>
                      <Reply className="mr-2 h-4 w-4" />
                      Reply
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={() => {
                      setEditingCommentId(comment.id);
                      setEditingContent(comment.content);
                    }}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => deleteComment.mutate({ id: comment.id })}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            {isEditing ? (
              <div className="mt-2 space-y-2">
                <Textarea
                  value={editingContent}
                  onChange={(e: any) => setEditingContent(e.target.value)}
                  className="min-h-[60px]"
                  autoFocus
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={() => handleEdit(comment.id)}>
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setEditingCommentId(null);
                      setEditingContent("");
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-sm mt-1 whitespace-pre-wrap">{comment.content}</p>
            )}
            {isReplying && (
              <div className="mt-2 space-y-2">
                <Textarea
                  ref={replyTextareaRef}
                  value={replyContent}
                  onChange={(e: any) => {
                    setReplyContent(e.target.value);
                    autoResize(e.target);
                  }}
                  placeholder="Write a reply..."
                  className="min-h-[60px]"
                  autoFocus
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={() => handleReply(comment.id)}>
                    Reply
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setReplyingToId(null);
                      setReplyContent("");
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
        {comment.replies && comment.replies.length > 0 && (
          <div className="mt-3">
            {comment.replies.map((reply: Comment) => renderComment(reply, true))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Sheet open={isDrawerOpen} onOpenChange={closeCommentDrawer}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            Comments {entityName && <span className="text-muted-foreground">on {entityName}</span>}
          </SheetTitle>
        </SheetHeader>
        <div className="mt-6 flex flex-col h-[calc(100vh-140px)]">
          <ScrollArea className="flex-1 pr-4">
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i: any) => (
                  <div key={i} className="flex items-start gap-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-20 w-full" />
                    </div>
                  </div>
                ))}
              </div>
            ) : comments?.comments && comments.comments.length > 0 ? (
              <div className="space-y-4">
                {comments.comments.map((comment: any) => renderComment(comment))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No comments yet. Be the first to comment!
              </div>
            )}
          </ScrollArea>
          <Separator className="my-4" />
          <form onSubmit={handleSubmit} className="space-y-4">
            <Textarea
              ref={textareaRef}
              value={newComment}
              onChange={(e: any) => {
                setNewComment(e.target.value);
                autoResize(e.target);
              }}
              placeholder="Write a comment..."
              className="min-h-[80px] resize-none"
            />
            <div className="flex justify-between items-center">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="text-muted-foreground"
              >
                <AtSign className="h-4 w-4 mr-1" />
                Mention
              </Button>
              <Button type="submit" disabled={!newComment.trim() || createComment.isPending}>
                <Send className="h-4 w-4 mr-2" />
                Comment
              </Button>
            </div>
          </form>
        </div>
      </SheetContent>
    </Sheet>
  );
}