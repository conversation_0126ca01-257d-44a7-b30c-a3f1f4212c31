"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { api } from "~/trpc/react";
import { useToast } from "~/hooks/use-toast";

interface CommentContextValue {
  openCommentDrawer: (entityType: string, entityId: string, entityName?: string) => void;
  closeCommentDrawer: () => void;
  isDrawerOpen: boolean;
  entityType: string | null;
  entityId: string | null;
  entityName: string | null;
}

const CommentContext = createContext<CommentContextValue | undefined>(undefined);

export const useComments = () => {
  const context = useContext(CommentContext);
  if (!context) {
    throw new Error("useComments must be used within a CommentProvider");
  }
  return context;
};

interface CommentProviderProps {
  children: React.ReactNode;
}

export function CommentProvider({ children }: CommentProviderProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [entityType, setEntityType] = useState<string | null>(null);
  const [entityId, setEntityId] = useState<string | null>(null);
  const [entityName, setEntityName] = useState<string | null>(null);

  const openCommentDrawer = useCallback((type: string, id: string, name?: string) => {
    setEntityType(type);
    setEntityId(id);
    setEntityName(name ?? null);
    setIsDrawerOpen(true);
  }, []);

  const closeCommentDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    // Keep entity info for animation duration
    setTimeout(() => {
      setEntityType(null);
      setEntityId(null);
      setEntityName(null);
    }, 300);
  }, []);

  return (
    <CommentContext.Provider
      value={{
        openCommentDrawer,
        closeCommentDrawer,
        isDrawerOpen,
        entityType,
        entityId,
        entityName,
      }}
    >
      {children}
    </CommentContext.Provider>
  );
}