"use client";

import React from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { MessageCircle } from "lucide-react";
import { api } from "~/trpc/react";
import { useComments } from "./comment-provider";
import { cn } from "~/lib/utils";

interface CommentBadgeProps {
  entityType: string;
  entityId: string;
  entityName?: string;
  variant?: "default" | "compact" | "icon-only";
  className?: string;
  showZero?: boolean;
}

export function CommentBadge({
  entityType,
  entityId,
  entityName,
  variant = "default",
  className,
  showZero = false,
}: CommentBadgeProps) {
  const { openCommentDrawer } = useComments();

  // Fetch comment count
  const { data: commentCount = 0 } = api.comments.getCommentCount.useQuery({
    commentableType: entityType,
    commentableId: entityId,
  });

  // Don't render if no comments and showZero is false
  if (commentCount === 0 && !showZero) {
    return null;
  }

  const handleClick = () => {
    openCommentDrawer(entityType, entityId, entityName);
  };

  if (variant === "icon-only") {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClick}
        className={cn(
          "h-8 w-8 p-0 relative",
          className
        )}
        title={`${commentCount} comment${commentCount !== 1 ? 's' : ''}`}
      >
        <MessageCircle className="h-4 w-4" />
        {commentCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center min-w-[20px]"
          >
            {commentCount > 99 ? "99+" : commentCount}
          </Badge>
        )}
      </Button>
    );
  }

  if (variant === "compact") {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClick}
        className={cn(
          "h-7 px-2 text-xs gap-1",
          commentCount > 0 ? "text-blue-600 hover:text-blue-700" : "text-muted-foreground",
          className
        )}
      >
        <MessageCircle className="h-3 w-3" />
        <span>{commentCount}</span>
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleClick}
      className={cn(
        "gap-2 h-8",
        commentCount > 0 ? "text-blue-600 hover:text-blue-700" : "text-muted-foreground",
        className
      )}
    >
      <MessageCircle className="h-4 w-4" />
      <Badge
        variant={commentCount > 0 ? "default" : "secondary"}
        className={cn(
          "text-xs",
          commentCount > 0 ? "bg-blue-100 text-blue-800 hover:bg-blue-200" : ""
        )}
      >
        {commentCount}
      </Badge>
      {commentCount === 1 ? "comment" : "comments"}
    </Button>
  );
}

// Hook for easy comment badge usage
export function useCommentBadge(entityType: string, entityId: string) {
  const { data: commentCount = 0 } = api.comments.getCommentCount.useQuery({
    commentableType: entityType,
    commentableId: entityId,
  });

  return {
    commentCount,
    hasComments: commentCount > 0,
    hasUnreadComments: false,
  };
}
