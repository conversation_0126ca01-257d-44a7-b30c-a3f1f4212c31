'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '~/hooks/use-auth';
import { api } from '~/trpc/react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Send, Loader2, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '~/lib/utils';
import { useRealtimeCommunications } from '~/hooks/use-realtime-events';

interface Message {
  id: string;
  userId: number;
  userName: string;
  message: string;
  timestamp: Date;
}

interface ChatWindowProps {
  conversationId: string;
  recipientName?: string;
}

export function ChatWindow({ conversationId, recipientName }: ChatWindowProps) {
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // Enable real-time updates for this chat
  useRealtimeCommunications(conversationId);

  // Mock loading messages - in production, use tRPC query
  useEffect(() => {
    if (conversationId) {
      setIsLoading(true);
      // Simulate loading messages
      setTimeout(() => {
        setMessages([
          {
            id: '1',
            userId: 2,
            userName: recipientName || 'Other User',
            message: 'Hello! How can I help you with the project?',
            timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
          },
          {
            id: '2',
            userId: parseInt(user?.id || '1'),
            userName: user?.user_metadata?.full_name || 'You',
            message: 'I have a question about the utility coordination timeline.',
            timestamp: new Date(Date.now() - 1000 * 60 * 8), // 8 minutes ago
          },
          {
            id: '3',
            userId: 2,
            userName: recipientName || 'Other User',
            message: 'Sure, what would you like to know?',
            timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
          },
        ]);
        setIsLoading(false);
      }, 500);
    }
  }, [conversationId, user, recipientName]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  const sendMessage = () => {
    if (!message.trim() || !user) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      userId: parseInt(user?.id || '0'),
      userName: user?.user_metadata?.full_name || 'You',
      message: message.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newMessage]);
    setMessage('');

    // In production, send message via tRPC mutation
    // api.chat.sendMessage.mutate({ conversationId, message: message.trim() });

    // Simulate receiving a response after 2 seconds
    setTimeout(() => {
      const responseMessage: Message = {
        id: (Date.now() + 1).toString(),
        userId: 2,
        userName: recipientName || 'Other User',
        message: 'Thanks for your message. I\'ll look into that and get back to you.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, responseMessage]);
    }, 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            {recipientName ? `Chat with ${recipientName}` : 'Chat'}
          </CardTitle>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {false && (
              <>
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span>Live</span>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 p-0 flex flex-col">
        <ScrollArea className="flex-1 p-4" ref={scrollRef}>
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : messages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <p>No messages yet. Start the conversation!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((msg: any) => {
                const isOwnMessage = msg.userId === parseInt(user?.id || '0');
                return (
                  <div
                    key={msg.id}
                    className={cn(
                      'flex gap-3',
                      isOwnMessage && 'flex-row-reverse'
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {msg.userName[0]?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div
                      className={cn(
                        'flex flex-col gap-1 max-w-[70%]',
                        isOwnMessage && 'items-end'
                      )}
                    >
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{msg.userName}</span>
                        <span>·</span>
                        <span>{format(msg.timestamp, 'HH:mm')}</span>
                      </div>
                      <div
                        className={cn(
                          'rounded-lg px-3 py-2 text-sm',
                          isOwnMessage
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        )}
                      >
                        {msg.message}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
        
        <div className="border-t p-4">
          <div className="flex gap-2">
            <Input
              value={message}
              onChange={(e: any) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="flex-1"
            />
            <Button
              onClick={sendMessage}
              disabled={!message.trim()}
              size="icon"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}