"use client";

import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Switch } from "~/components/ui/switch";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ArrowLeft, Send, Paperclip, Link, X, Loader2 } from "lucide-react";

interface EmailComposerProps {
  onClose: () => void;
  onSent: () => void;
  replyTo?: {
    subject: string;
    to: string;
    originalBody?: string;
  };
}

export function EmailComposer({ onClose, onSent, replyTo }: EmailComposerProps) {
  const [to, setTo] = useState(replyTo?.to || "");
  const [toList, setToList] = useState<string[]>([]);
  const [cc, setCc] = useState("");
  const [ccList, setCcList] = useState<string[]>([]);
  const [showCc, setShowCc] = useState(false);
  const [subject, setSubject] = useState(replyTo?.subject || "");
  const [body, setBody] = useState("");
  const [linkToProject, setLinkToProject] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState("");
  const [selectedPhaseId, setSelectedPhaseId] = useState("");
  const [selectedTaskId, setSelectedTaskId] = useState("");

  const { data: projects } = api.projects.getAll.useQuery({ limit: 100 });
  
  const sendEmailMutation = api.email.send.useMutation({
    onSuccess: () => {
      toast.success("Email sent successfully");
      onSent();
    },
    onError: (error: any) => {
      toast.error(`Failed to send email: ${error.message}`);
    },
  });

  const handleAddEmail = (email: string, list: string[], setList: (list: string[]) => void) => {
    const trimmed = email.trim();
    if (trimmed && !list.includes(trimmed)) {
      if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmed)) {
        setList([...list, trimmed]);
      } else {
        toast.error("Invalid email address");
      }
    }
  };

  const handleRemoveEmail = (email: string, list: string[], setList: (list: string[]) => void) => {
    setList(list.filter(e => e !== email));
  };

  const handleSend = () => {
    const allTo = [...toList];
    if (to.trim()) {
      handleAddEmail(to, toList, setToList);
      allTo.push(to.trim());
    }

    const allCc = [...ccList];
    if (cc.trim()) {
      handleAddEmail(cc, ccList, setCcList);
      allCc.push(cc.trim());
    }

    if (allTo.length === 0) {
      toast.error("Please add at least one recipient");
      return;
    }

    if (!subject.trim()) {
      toast.error("Please enter a subject");
      return;
    }

    if (!body.trim()) {
      toast.error("Please enter a message");
      return;
    }

    sendEmailMutation.mutate({
      to: allTo.join(', '), // Convert array to comma-separated string
      cc: allCc.length > 0 ? allCc.join(', ') : undefined,
      subject,
      body: body.replace(/\n/g, '<br>'), // Send as string, not object
      projectId: linkToProject && selectedProjectId ? selectedProjectId : undefined,
      phaseId: linkToProject && selectedPhaseId ? selectedPhaseId : undefined,
      taskId: linkToProject && selectedTaskId ? selectedTaskId : undefined,
    } as any);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={onClose}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-lg font-semibold">New Email</h2>
        </div>
        <Button onClick={handleSend} disabled={sendEmailMutation.isPending}>
          {sendEmailMutation.isPending ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Send
            </>
          )}
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <div className="space-y-2">
          <Label htmlFor="to">To</Label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                id="to"
                type="email"
                placeholder="Enter email address"
                value={to}
                onChange={(e: any) => setTo(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    handleAddEmail(to, toList, setToList);
                    setTo("");
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowCc(!showCc)}
              >
                CC
              </Button>
            </div>
            <div className="flex flex-wrap gap-1">
              {toList.map((email: any) => (
                <Badge key={email} variant="secondary" className="flex items-center gap-1">
                  {email}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveEmail(email, toList, setToList)}
                  />
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {showCc && (
          <div className="space-y-2">
            <Label htmlFor="cc">CC</Label>
            <div className="space-y-2">
              <Input
                id="cc"
                type="email"
                placeholder="Enter CC email address"
                value={cc}
                onChange={(e: any) => setCc(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    handleAddEmail(cc, ccList, setCcList);
                    setCc("");
                  }
                }}
              />
              <div className="flex flex-wrap gap-1">
                {ccList.map((email: any) => (
                  <Badge key={email} variant="secondary" className="flex items-center gap-1">
                    {email}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => handleRemoveEmail(email, ccList, setCcList)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="subject">Subject</Label>
          <Input
            id="subject"
            placeholder="Enter subject"
            value={subject}
            onChange={(e: any) => setSubject(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="body">Message</Label>
          <Textarea
            id="body"
            placeholder="Type your message..."
            value={body}
            onChange={(e: any) => setBody(e.target.value)}
            className="min-h-[200px]"
          />
        </div>

        <div className="border rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              <Label htmlFor="link-project">Link to Project</Label>
            </div>
            <Switch
              id="link-project"
              checked={linkToProject}
              onCheckedChange={setLinkToProject}
            />
          </div>

          {linkToProject && (
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="project">Project</Label>
                <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                  <SelectTrigger id="project">
                    <SelectValue placeholder="Select a project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects?.projects.map((project: any) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedProjectId && (
                <p className="text-sm text-muted-foreground">
                  This email will be linked to the selected project and appear in the communication log.
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}