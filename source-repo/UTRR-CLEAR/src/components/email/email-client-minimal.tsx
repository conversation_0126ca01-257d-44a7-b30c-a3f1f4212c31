"use client";

import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { api } from "~/trpc/react";
import { Mail } from "lucide-react";

export function EmailClient() {
  const { data: isEnabled } = api.email.isEnabled.useQuery();
  
  // Email is not enabled yet
  if (!isEnabled) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center p-6">
        <Mail className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Email Integration Not Enabled</h3>
        <p className="text-sm text-muted-foreground">
          Contact your administrator to enable email integration.
        </p>
      </div>
    );
  }


  return (
    <div className="flex flex-col h-full p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold mb-2">Email Integration</h2>
        <p className="text-sm text-muted-foreground">
          Email functionality is enabled. Full implementation coming soon.
        </p>
      </div>
      
      <div className="bg-muted rounded-lg p-6 text-center">
        <Mail className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">
          Email client interface will be available here
        </p>
      </div>
    </div>
  );
}