"use client";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Separator } from "~/components/ui/separator";
import { Badge } from "~/components/ui/badge";
import { format } from "date-fns";
import { 
  ArrowLeft, 
  Reply, 
  ReplyAll, 
  Forward, 
  Archive, 
  Trash2,
  Star,
  Paperclip,
  Link,
  MoreVertical
} from "lucide-react";
import { type EmailMessage } from "~/lib/email/types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useState, useMemo } from "react";
import { EmailProjectLinker } from "./email-project-linker";
import { sanitizeHtml } from "~/lib/html-sanitizer";

interface EmailViewerProps {
  email: EmailMessage;
  onBack: () => void;
  onReply: () => void;
  onForward: () => void;
}

export function EmailViewer({ email, onBack, onReply, onForward }: EmailViewerProps) {
  const [showLinker, setShowLinker] = useState(false);
  
  // Sanitize HTML content once, outside of conditional rendering
  const sanitizedHtml = useMemo(() => {
    return email.body.html ? sanitizeHtml(email.body.html) : '';
  }, [email.body.html]);

  if (showLinker) {
    return (
      <EmailProjectLinker
        email={email}
        onBack={() => setShowLinker(false)}
        onLinked={() => setShowLinker(false)}
      />
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-lg font-semibold truncate max-w-[300px]">{email.subject}</h2>
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" onClick={onReply}>
            <Reply className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <ReplyAll className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={onForward}>
            <Forward className="h-4 w-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowLinker(true)}>
                <Link className="h-4 w-4 mr-2" />
                Link to Project
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Star className="h-4 w-4 mr-2" />
                Star
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Archive className="h-4 w-4 mr-2" />
                Archive
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-4">
          <div>
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="font-semibold">{email.from.name || email.from.email}</h3>
                <p className="text-sm text-muted-foreground">{email.from.email}</p>
              </div>
              <time className="text-sm text-muted-foreground">
                {format(new Date(email.date), 'MMM d, yyyy h:mm a')}
              </time>
            </div>
            
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">To:</span>
                <span>{email.to.map(addr => addr.name || addr.email).join(', ')}</span>
              </div>
              {email.cc && email.cc.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Cc:</span>
                  <span>{email.cc.map(addr => addr.name || addr.email).join(', ')}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {email.hasAttachments && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <Paperclip className="h-4 w-4" />
              <span className="text-sm">This email has attachments</span>
            </div>
          )}

          <div className="prose prose-sm max-w-none">
            {email.body.html ? (
              <div 
                dangerouslySetInnerHTML={{ 
                  __html: sanitizedHtml
                }} 
              />
            ) : (
              <div className="whitespace-pre-wrap">{email.body.text}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}