"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "~/components/ui/tabs";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Separator } from "~/components/ui/separator";
import { Badge } from "~/components/ui/badge";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { format } from "date-fns";
import { 
  Mail, 
  Calendar, 
  Send, 
  Search, 
  RefreshCw, 
  Paperclip,
  Star,
  Archive,
  Trash2,
  Reply,
  ReplyAll,
  Forward,
  Loader2,
  Link,
  Tag
} from "lucide-react";
import { EmailComposer } from "./email-composer";
import { EmailViewer } from "./email-viewer";
import { type EmailMessage } from "~/lib/email/types";

export function EmailClient() {
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTab, setSelectedTab] = useState("inbox");

  const { data: isEnabled } = api.email.isEnabled.useQuery();
  
  // Mock data since email functionality is not fully implemented
  const emails: EmailMessage[] = [];
  const isLoadingEmails = false;


  if (!isEnabled) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <Mail className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Email Integration Not Enabled</h3>
        <p className="text-sm text-muted-foreground">
          Contact your administrator to enable email integration.
        </p>
      </div>
    );
  }


  if (isComposing) {
    return (
      <EmailComposer
        onClose={() => setIsComposing(false)}
        onSent={() => {
          setIsComposing(false);
          toast.success("Email sent");
        }}
      />
    );
  }

  if (selectedEmail) {
    return (
      <EmailViewer
        email={selectedEmail}
        onBack={() => setSelectedEmail(null)}
        onReply={() => {
          // TODO: Implement reply
          toast.info("Reply feature coming soon");
        }}
        onForward={() => {
          // TODO: Implement forward
          toast.info("Forward feature coming soon");
        }}
      />
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Email</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                // Refresh placeholder
                toast.info("Email refresh not implemented");
              }}
              disabled={isLoadingEmails}
            >
              <RefreshCw className={`h-4 w-4 ${isLoadingEmails ? 'animate-spin' : ''}`} />
            </Button>
            <Button onClick={() => setIsComposing(true)}>
              <Send className="h-4 w-4 mr-2" />
              Compose
            </Button>
          </div>
        </div>
        
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search emails..."
            value={searchQuery}
            onChange={(e: any) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1">
        <TabsList className="w-full justify-start rounded-none border-b">
          <TabsTrigger value="inbox">Inbox</TabsTrigger>
          <TabsTrigger value="unread">Unread</TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
          <TabsTrigger value="linked">Project Linked</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="flex-1 p-0">
          <ScrollArea className="h-[calc(100vh-280px)]">
            {isLoadingEmails ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : emails?.length === 0 ? (
              <div className="text-center p-8 text-muted-foreground">
                No emails found
              </div>
            ) : (
              <div className="divide-y">
                {emails?.map((email: EmailMessage) => (
                  <button
                    key={email.id}
                    onClick={() => {
                      setSelectedEmail(email);
                      if (!email.isRead) {
                        // markAsReadMutation not implemented yet
                      }
                    }}
                    className="w-full text-left p-4 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <span className={`text-sm ${!email.isRead ? 'font-semibold' : ''}`}>
                          {email.from.name || email.from.email}
                        </span>
                        {!email.isRead && (
                          <Badge variant="secondary" className="text-xs">New</Badge>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {format(new Date(email.date), 'MMM d, h:mm a')}
                      </span>
                    </div>
                    <div className="text-sm font-medium mb-1">{email.subject}</div>
                    <div className="text-xs text-muted-foreground line-clamp-2">
                      {email.body.text}
                    </div>
                    {email.hasAttachments && (
                      <div className="flex items-center gap-1 mt-2">
                        <Paperclip className="h-3 w-3" />
                        <span className="text-xs">Has attachments</span>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}