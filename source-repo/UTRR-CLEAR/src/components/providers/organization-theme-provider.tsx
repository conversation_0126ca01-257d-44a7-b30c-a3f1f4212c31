'use client';

import { useEffect } from 'react';
import { api } from '~/trpc/react';

// Convert HEX to HSL
function hexToHsl(hex: string): string {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0;
  const l = (max + min) / 2;
  
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = ((g - b) / d + (g < b ? 6 : 0)) / 6; break;
      case g: h = ((b - r) / d + 2) / 6; break;
      case b: h = ((r - g) / d + 4) / 6; break;
    }
  }
  
  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
}

export function OrganizationThemeProvider({ children }: { children: React.ReactNode }) {
  // Fetch organization data with theme config
  const { data: organization } = api.organizations.getCurrent.useQuery(undefined, {
    retry: false,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if ((organization as any)?.theme_config && typeof (organization as any).theme_config === 'object') {
      const root = document.documentElement;
      const themeConfig = (organization as any).theme_config as Record<string, string>;
      
      // Apply CSS variables from saved theme
      Object.entries(themeConfig).forEach(([key, value]) => {
        if (key.startsWith('--') && typeof value === 'string') {
          // Convert hex to HSL for CSS variables
          const hslValue = hexToHsl(value);
          root.style.setProperty(key, hslValue);
        }
      });
    }
  }, [organization]);

  return <>{children}</>;
}