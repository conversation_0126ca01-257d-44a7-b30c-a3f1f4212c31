import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ProjectDetailsCard } from '~/components/project/project-details-card';
import { api } from '~/trpc/react';

// Mock tRPC
vi.mock('~/trpc/react', () => ({
  api: {
    projects: {
      getById: {
        useQuery: vi.fn(),
      },
      delete: {
        useMutation: vi.fn(),
      },
    },
  },
}));

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

// Mock toast
vi.mock('~/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

describe('ProjectDetailsCard with minimal data', () => {
  it('should render with only required fields', () => {
    const minimalProject = {
      id: 'test-123',
      name: 'Test Project',
      client: 'Test Client',
    };

    // Mock the useQuery hook
    vi.mocked(api.projects.getById.useQuery).mockReturnValue({
      data: minimalProject,
      isLoading: false,
      error: null,
    } as any);

    // Mock the useMutation hook
    vi.mocked(api.projects.delete.useMutation).mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);

    render(<ProjectDetailsCard projectId="test-123" project={minimalProject} />);

    // Check that required fields are displayed
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test Client')).toBeInTheDocument();

    // Check that optional fields show default values
    expect(screen.getAllByText('Not specified').length).toBeGreaterThan(0); // For location and currentPhase
    expect(screen.getAllByText('0').length).toBeGreaterThan(0); // For various counts
    expect(screen.getByText('0%')).toBeInTheDocument(); // For progress
  });

  it('should render with partial data', () => {
    const partialProject = {
      id: 'test-456',
      name: 'Partial Project',
      client: 'Test Client 2',
      description: 'This is a test description',
      rag_status: 'Green',
      current_phase: 'Planning',
      _count: {
        utilities: 5,
        conflicts: 2,
      },
    };

    render(<ProjectDetailsCard projectId="test-456" project={partialProject} />);

    // Check that provided fields are displayed
    expect(screen.getByText('Partial Project')).toBeInTheDocument();
    expect(screen.getByText('Test Client 2')).toBeInTheDocument();
    expect(screen.getByText('This is a test description')).toBeInTheDocument();
    expect(screen.getByText('Planning')).toBeInTheDocument();
    
    // Check that the component renders without errors with partial data
    // The exact display of counts depends on the component implementation
  });

  it('should handle null and undefined values gracefully', () => {
    const projectWithNulls = {
      id: 'test-789',
      name: 'Project with Nulls',
      client: 'Test Client 3',
      description: null,
      rag_status: undefined,
      current_phase: null,
      start_date: null,
      end_date: undefined,
      high_priority_items: null,
      contract_amount: null,
    };

    render(<ProjectDetailsCard projectId="test-789" project={projectWithNulls} />);

    // Should render without crashing
    expect(screen.getByText('Project with Nulls')).toBeInTheDocument();
    expect(screen.getByText('Test Client 3')).toBeInTheDocument();
    
    // Check that null/undefined fields don't cause crashes
    expect(screen.queryByText('null')).not.toBeInTheDocument();
    expect(screen.queryByText('undefined')).not.toBeInTheDocument();
  });
});