import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ProjectDetailsCard } from '~/components/project/project-details-card';
import type { projects as Project } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

// Mock icons
vi.mock('lucide-react', () => ({
  Calendar: () => <div data-testid="calendar-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  DollarSign: () => <div data-testid="dollar-icon" />,
  MapPin: () => <div data-testid="map-icon" />,
  Edit: () => <div data-testid="edit-icon" />,
}));

describe('ProjectDetailsCard', () => {
  const mockProject: Project = {
    id: '1',
    name: 'Test Project',
    description: 'A test project',
    client: 'Test Client',
    start_date: new Date('2024-01-01'),
    end_date: new Date('2024-12-31'),
    current_phase: 'Coordination',
    contract_amount: new Decimal(100000),
    client_pm: '<PERSON>',
    created_at: new Date(),
    updated_at: new Date(),
    monday_id: null,
    billed_to_date: new Decimal(25000),
    wip: new Decimal(5000),
    rag_status: 'Green',
    high_priority_items: 0,
    medium_priority_items: 2,
    low_priority_items: 5,
    manager_id: null,
    record_id: null,
    client_job_number: null,
    work_type: null,
    project_id_only: null,
    phase_id_only: null,
    last_milestone: null,
    coordination_type: null,
    project_funding: null,
    ntp_date: null,
    letting_bid_date: null,
    this_month_status: null,
    status_update_date: null,
    hourly_rate: null,
    project_hours_for_billed: null,
    billed_plus_wip: null,
    current_cost: null,
    billed_percentage: null,
    profit_to_date: null,
    profit_percentage: null,
    project_priority: null,
    egis_project_manager: null,
    egis_project_manager_email: null,
    client_contact: null,
    Project_Health__RAG_: null,
    coordinator_id: null,
    organization_id: null,
    template_id: null,
  };

  const mockOnEdit = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render project details correctly', () => {
    render(<ProjectDetailsCard projectId="1" />);

    // Check header
    expect(screen.getByText('Project Details')).toBeInTheDocument();
    
    // Check project info
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test Client')).toBeInTheDocument();
    expect(screen.getByText('A test project')).toBeInTheDocument();

    // Check dates
    expect(screen.getByText('Jan 1, 2024')).toBeInTheDocument();
    expect(screen.getByText('Dec 31, 2024')).toBeInTheDocument();

    // Check phase
    expect(screen.getByText('Coordination')).toBeInTheDocument();

    // Check financial info
    expect(screen.getByText('$100,000')).toBeInTheDocument();

    // Check coordinator
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('should show edit button when onEdit is provided', () => {
    render(<ProjectDetailsCard projectId="1" />);

    const editButton = screen.getByRole('button', { name: /edit/i });
    expect(editButton).toBeInTheDocument();

    fireEvent.click(editButton);
    expect(mockOnEdit).toHaveBeenCalledTimes(1);
  });

  it('should not show edit button when onEdit is not provided', () => {
    render(<ProjectDetailsCard projectId="1" />);

    const editButton = screen.queryByRole('button', { name: /edit/i });
    expect(editButton).not.toBeInTheDocument();
  });

  it('should handle missing optional fields gracefully', () => {
    const projectWithMissingFields = {
      ...mockProject,
      description: null,
      client_pm: null,
      contract_amount: null,
    };

    render(<ProjectDetailsCard projectId="1" />);

    // Should show placeholders or skip missing fields
    expect(screen.queryByText('No description available')).toBeInTheDocument();
    expect(screen.queryByText('Not specified')).toBeInTheDocument();
  });

  it('should format currency correctly', () => {
    const testCases = [
      { amount: 1000, expected: '$1,000' },
      { amount: 1000000, expected: '$1,000,000' },
      { amount: 50.5, expected: '$50.50' },
      { amount: 0, expected: '$0' },
    ];

    testCases.forEach(({ amount, expected }) => {
      const project = { ...mockProject, contract_amount: new Decimal(amount) };
      const { rerender } = render(<ProjectDetailsCard projectId="1" />);
      expect(screen.getByText(expected)).toBeInTheDocument();
      rerender(<ProjectDetailsCard projectId="1" />);
    });
  });

  it('should format dates correctly', () => {
    render(<ProjectDetailsCard projectId="1" />);

    // Check date format
    expect(screen.getByText('Jan 1, 2024')).toBeInTheDocument();
    expect(screen.getByText('Dec 31, 2024')).toBeInTheDocument();
  });

  it('should display phase badge with correct color', () => {
    const phaseColors = {
      'Initiation': 'bg-gray-100',
      'Research': 'bg-blue-100',
      'Coordination': 'bg-purple-100',
      'Verification': 'bg-yellow-100',
      'Closeout': 'bg-green-100',
    };

    Object.entries(phaseColors).forEach(([phase, colorClass]) => {
      const project = { ...mockProject, current_phase: phase };
      const { container, rerender } = render(<ProjectDetailsCard projectId="1" />);
      
      const badge = screen.getByText(phase);
      expect(badge.className).toContain(colorClass);
      
      rerender(<ProjectDetailsCard projectId="1" />);
    });
  });

  it('should calculate project duration correctly', () => {
    render(<ProjectDetailsCard projectId="1" />);

    // Project runs from Jan 1 to Dec 31, 2024
    expect(screen.getByText(/365 days/i)).toBeInTheDocument();
  });

  it('should handle same day start and end dates', () => {
    const sameDayProject = {
      ...mockProject,
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-01-01'),
    };

    render(<ProjectDetailsCard projectId="1" />);
    expect(screen.getByText(/1 day/i)).toBeInTheDocument();
  });

  it('should handle null dates', () => {
    const nullDatesProject = {
      ...mockProject,
      start_date: null,
      end_date: null,
    };

    render(<ProjectDetailsCard projectId="1" />);
    expect(screen.getByText('Not set')).toBeInTheDocument();
  });

  it('should display financial progress', () => {
    render(<ProjectDetailsCard projectId="1" />);

    // Billed: $25,000, WIP: $5,000, Total: $100,000
    // Progress: 30%
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toHaveAttribute('aria-valuenow', '30');
    expect(screen.getByText('30% Billed')).toBeInTheDocument();
  });

  it('should handle zero contract amount', () => {
    const zeroContractProject = {
      ...mockProject,
      contract_amount: new Decimal(0),
      billed_to_date: new Decimal(0),
      wip: new Decimal(0),
    };

    render(<ProjectDetailsCard projectId="1" />);
    expect(screen.getByText('$0')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const { container } = render(
      <ProjectDetailsCard projectId="1" />
    );

    const card = container.firstChild;
    expect(card).toHaveClass('custom-class');
  });

  it('should render all icons correctly', () => {
    render(<ProjectDetailsCard projectId="1" />);

    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
    expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
    expect(screen.getByTestId('dollar-icon')).toBeInTheDocument();
  });
});