import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { StatsCards } from '~/components/dashboard/stats-cards';
import { api } from '~/trpc/react';

// Mock tRPC
vi.mock('~/trpc/react', () => ({
  api: {
    dashboard: {
      getOverviewStats: {
        useQuery: vi.fn(),
      },
    },
  },
}));

describe('StatsCards', () => {
  it('should render loading state', () => {
    vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    render(<StatsCards />);

    // Should show loading skeletons
    const skeletons = screen.getAllByTestId('stats-skeleton');
    expect(skeletons).toHaveLength(4);
  });

  it('should render stats when data is loaded', () => {
    vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
      data: {
        activeProjects: 12,
        totalStakeholders: 45,
        pendingTasks: 8,
        completionRate: 87.5,
      },
      isLoading: false,
      error: null,
    } as any);

    render(<StatsCards />);

    // Check active projects card
    expect(screen.getByText('Active Projects')).toBeInTheDocument();
    expect(screen.getByText('12')).toBeInTheDocument();

    // Check stakeholders card
    expect(screen.getByText('Total Stakeholders')).toBeInTheDocument();
    expect(screen.getByText('45')).toBeInTheDocument();

    // Check pending tasks card
    expect(screen.getByText('Pending Tasks')).toBeInTheDocument();
    expect(screen.getByText('8')).toBeInTheDocument();

    // Check completion rate card
    expect(screen.getByText('Completion Rate')).toBeInTheDocument();
    expect(screen.getByText('87.5%')).toBeInTheDocument();
  });

  it('should render error state', () => {
    vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch stats'),
    } as any);

    render(<StatsCards />);

    expect(screen.getByText(/failed to load statistics/i)).toBeInTheDocument();
  });

  it('should render with zero values', () => {
    vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
      data: {
        activeProjects: 0,
        totalStakeholders: 0,
        pendingTasks: 0,
        completionRate: 0,
      },
      isLoading: false,
      error: null,
    } as any);

    render(<StatsCards />);

    const zeroValues = screen.getAllByText('0');
    expect(zeroValues).toHaveLength(3);
    expect(screen.getByText('0%')).toBeInTheDocument();
  });

  it('should format large numbers correctly', () => {
    vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
      data: {
        activeProjects: 1234,
        totalStakeholders: 56789,
        pendingTasks: 999,
        completionRate: 99.99,
      },
      isLoading: false,
      error: null,
    } as any);

    render(<StatsCards />);

    expect(screen.getByText('1,234')).toBeInTheDocument();
    expect(screen.getByText('56,789')).toBeInTheDocument();
    expect(screen.getByText('999')).toBeInTheDocument();
    expect(screen.getByText('99.99%')).toBeInTheDocument();
  });

  it('should have correct card styles and icons', () => {
    vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
      data: {
        activeProjects: 10,
        totalStakeholders: 20,
        pendingTasks: 5,
        completionRate: 75,
      },
      isLoading: false,
      error: null,
    } as any);

    const { container } = render(<StatsCards />);

    // Check for card structure
    const cards = container.querySelectorAll('[data-testid="stats-card"]');
    expect(cards).toHaveLength(4);

    // Check for icons
    expect(container.querySelector('[data-testid="briefcase-icon"]')).toBeInTheDocument();
    expect(container.querySelector('[data-testid="users-icon"]')).toBeInTheDocument();
    expect(container.querySelector('[data-testid="clock-icon"]')).toBeInTheDocument();
    expect(container.querySelector('[data-testid="chart-icon"]')).toBeInTheDocument();
  });

  it('should handle percentage edge cases', () => {
    const testCases = [
      { completionRate: 100, expected: '100%' },
      { completionRate: 0, expected: '0%' },
      { completionRate: 50.5, expected: '50.5%' },
      { completionRate: 33.333, expected: '33.33%' },
    ];

    testCases.forEach(({ completionRate, expected }) => {
      vi.mocked(api.dashboard.getOverviewStats.useQuery).mockReturnValue({
        data: {
          activeProjects: 1,
          totalStakeholders: 1,
          pendingTasks: 1,
          completionRate,
        },
        isLoading: false,
        error: null,
      } as any);

      const { rerender } = render(<StatsCards />);
      expect(screen.getByText(expected)).toBeInTheDocument();
      rerender(<StatsCards />);
    });
  });
});