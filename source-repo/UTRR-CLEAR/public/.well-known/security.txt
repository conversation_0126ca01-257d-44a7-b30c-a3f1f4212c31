# UtilitySync Security Policy
# This security.txt file is compliant with RFC 9116

# Required: Contact information for security issues
Contact: mailto:<EMAIL>
Contact: mailto:<EMAIL>
Contact: https://github.com/your-org/utility-sync-t3/security/advisories/new

# Required: Expiry date for this file (ISO 8601 format)
# Update this date when modifying the file
Expires: 2025-12-31T23:59:59.000Z

# Preferred languages for communication
Preferred-Languages: en, es

# Link to security policy
Policy: https://github.com/your-org/utility-sync-t3/blob/main/SECURITY.md

# PGP key for encrypted communication
Encryption: https://utility-sync.com/security-pgp-key.asc
Encryption: dns:5d2d37ab76ac47e89e30b9a5b3bbba42e4e25061._openpgpkey.utility-sync.com?type=OPENPGPKEY

# Acknowledgments page for security researchers
Acknowledgments: https://utility-sync.com/security/acknowledgments

# Hiring information for security positions
Hiring: https://careers.egis.com/security

# Canonical URL for this file
Canonical: https://utility-sync.com/.well-known/security.txt

# Security.txt digital signature
# Generate with: gpg --detach-sign --armor security.txt
# Signature should be in security.txt.sig file