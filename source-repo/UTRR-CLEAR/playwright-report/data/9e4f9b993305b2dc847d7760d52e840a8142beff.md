# Test info

- Name: Verify All 10 Fixes >> Fix 8: Demo button should be removed from portfolio
- Location: /workspaces/CLEAR-nextjs/UTRR-CLEAR/tests/verify-all-fixes.spec.ts:124:3

# Error details

```
Error: browserType.launch: Target page, context or browser has been closed
Browser logs:

╔════════════════════════════════════════════════════════════════════════════════════════════════╗
║ Looks like you launched a headed browser without having a XServer running.                     ║
║ Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. ║
║                                                                                                ║
║ <3 Playwright Team                                                                             ║
╚════════════════════════════════════════════════════════════════════════════════════════════════╝
Call log:
  - <launching> /home/<USER>/.cache/ms-playwright/chromium-1169/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCH<PERSON>rame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --user-data-dir=/tmp/playwright_chromiumdev_profile-uOBAPo --remote-debugging-pipe --no-startup-window
  - <launched> pid=206526
  - [pid=206526][err] [206526:206662:0610/041512.887949:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
  - [pid=206526][err] [206526:206526:0610/041512.907516:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY
  - [pid=206526][err] [206526:206526:0610/041512.907539:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.

```

# Test source

```ts
   24 |     await page.waitForLoadState('networkidle');
   25 |     
   26 |     // Find and click on Team Chat
   27 |     const teamChatCard = page.locator('text=Team Chat').first();
   28 |     await expect(teamChatCard).toBeVisible();
   29 |     
   30 |     // Try to send a message
   31 |     const chatInput = page.locator('textarea[placeholder*="Type your message"]').first();
   32 |     await chatInput.fill('Test message from automated test');
   33 |     
   34 |     // Click send button
   35 |     const sendButton = page.locator('button[aria-label="Send message"]').first();
   36 |     await sendButton.click();
   37 |     
   38 |     // Verify no error toast appears
   39 |     const errorToast = page.locator('.bg-red-500, .bg-destructive');
   40 |     await expect(errorToast).not.toBeVisible({ timeout: 3000 });
   41 |   });
   42 |
   43 |   test('Fix 2: Header logo should match signin page and support dark mode', async ({ page }) => {
   44 |     // Check logo on dashboard
   45 |     await page.goto(`${BASE_URL}/dashboard`);
   46 |     const headerLogo = page.locator('header img[alt*="logo"], header svg').first();
   47 |     await expect(headerLogo).toBeVisible();
   48 |     
   49 |     // Verify it's using OrganizationLogoStatic (should have specific class)
   50 |     const logoContainer = page.locator('.organization-logo-static').first();
   51 |     await expect(logoContainer).toBeVisible();
   52 |   });
   53 |
   54 |   test('Fix 3: Dashboard should not have white bar when scrolling', async ({ page }) => {
   55 |     await page.goto(`${BASE_URL}/dashboard`);
   56 |     await page.waitForLoadState('networkidle');
   57 |     
   58 |     // Scroll to bottom
   59 |     await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
   60 |     await page.waitForTimeout(500);
   61 |     
   62 |     // Check that there's no overflow issue
   63 |     const shell = page.locator('.w-full.min-h-full.bg-background').first();
   64 |     await expect(shell).toBeVisible();
   65 |     
   66 |     // Verify no overflow-auto class that was causing the issue
   67 |     const hasOverflowAuto = await shell.evaluate(el => el.classList.contains('overflow-auto'));
   68 |     expect(hasOverflowAuto).toBe(false);
   69 |   });
   70 |
   71 |   test('Fix 4: Dashboard components should have dynamic heights', async ({ page }) => {
   72 |     await page.goto(`${BASE_URL}/dashboard`);
   73 |     await page.waitForLoadState('networkidle');
   74 |     
   75 |     // Check that TasksList doesn't have fixed height wrapper
   76 |     const tasksSection = page.locator('text=Recent Tasks').locator('..');
   77 |     await expect(tasksSection).toBeVisible();
   78 |     
   79 |     // Verify components are not wrapped in unnecessary divs
   80 |     const tasksList = page.locator('[data-testid="tasks-list"], .space-y-4').first();
   81 |     await expect(tasksList).toBeVisible();
   82 |   });
   83 |
   84 |   test('Fix 5: Stakeholders page should be properly aligned', async ({ page }) => {
   85 |     await page.goto(`${BASE_URL}/stakeholders`);
   86 |     await page.waitForLoadState('networkidle');
   87 |     
   88 |     // Check for proper container styling
   89 |     const container = page.locator('.container.mx-auto.px-4.max-w-7xl').first();
   90 |     await expect(container).toBeVisible();
   91 |   });
   92 |
   93 |   test('Fix 6: Sub-Invoices page should show real data', async ({ page }) => {
   94 |     await page.goto(`${BASE_URL}/sub-invoices`);
   95 |     await page.waitForLoadState('networkidle');
   96 |     
   97 |     // Check that the page loads without mock data indicators
   98 |     const pageTitle = page.locator('h1:has-text("Sub-Consultant Invoices")');
   99 |     await expect(pageTitle).toBeVisible();
  100 |     
  101 |     // Verify real-time indicator is present
  102 |     const realtimeIndicator = page.locator('.realtime-indicator, [data-testid="realtime-indicator"]').first();
  103 |     await expect(realtimeIndicator).toBeVisible();
  104 |     
  105 |     // Check for proper container styling
  106 |     const container = page.locator('.container.mx-auto.px-4.max-w-7xl').first();
  107 |     await expect(container).toBeVisible();
  108 |   });
  109 |
  110 |   test('Fix 7: Project Portfolio should show projects without duplicate titles', async ({ page }) => {
  111 |     await page.goto(`${BASE_URL}/projects`);
  112 |     await page.waitForLoadState('networkidle');
  113 |     
  114 |     // Count how many "Projects" titles appear
  115 |     const projectTitles = page.locator('h1:has-text("Projects")');
  116 |     const count = await projectTitles.count();
  117 |     expect(count).toBe(1); // Should only have one "Projects" title
  118 |     
  119 |     // Verify projects are displayed
  120 |     const projectCards = page.locator('.hover\\:shadow-md').first();
  121 |     await expect(projectCards).toBeVisible({ timeout: 10000 });
  122 |   });
  123 |
> 124 |   test('Fix 8: Demo button should be removed from portfolio', async ({ page }) => {
      |   ^ Error: browserType.launch: Target page, context or browser has been closed
  125 |     await page.goto(`${BASE_URL}/projects`);
  126 |     await page.waitForLoadState('networkidle');
  127 |     
  128 |     // Check if there are no projects (empty state)
  129 |     const emptyState = page.locator('text=No projects found');
  130 |     if (await emptyState.isVisible({ timeout: 2000 })) {
  131 |       // Demo button should NOT be in the empty state
  132 |       const demoButton = page.locator('text=View Demo UI');
  133 |       await expect(demoButton).not.toBeVisible();
  134 |     }
  135 |     
  136 |     // Verify demo page exists at /projects/demo
  137 |     await page.goto(`${BASE_URL}/projects/demo`);
  138 |     await expect(page.locator('text=Demo Mode')).toBeVisible();
  139 |   });
  140 |
  141 |   test('Fix 9: Admin dropdown should be visible for department_manager', async ({ page }) => {
  142 |     await page.goto(`${BASE_URL}/dashboard`);
  143 |     await page.waitForLoadState('networkidle');
  144 |     
  145 |     // Check for admin dropdown in header
  146 |     const adminDropdown = page.locator('button:has-text("Admin")').first();
  147 |     await expect(adminDropdown).toBeVisible();
  148 |     
  149 |     // Click to verify it opens
  150 |     await adminDropdown.click();
  151 |     const adminMenu = page.locator('[role="menu"]').filter({ hasText: 'User Management' });
  152 |     await expect(adminMenu).toBeVisible();
  153 |   });
  154 |
  155 |   test('Fix 10: Project Details tab should show real data', async ({ page }) => {
  156 |     await page.goto(`${BASE_URL}/projects`);
  157 |     await page.waitForLoadState('networkidle');
  158 |     
  159 |     // Click on first project
  160 |     const firstProject = page.locator('a[href^="/projects/"]:not([href$="/new"]):not([href$="/demo"])').first();
  161 |     await firstProject.click();
  162 |     
  163 |     // Wait for project details page
  164 |     await page.waitForLoadState('networkidle');
  165 |     
  166 |     // Verify Details tab is active and shows real data
  167 |     const detailsTab = page.locator('[role="tab"]:has-text("Details")');
  168 |     await expect(detailsTab).toHaveAttribute('data-state', 'active');
  169 |     
  170 |     // Check for project progress (should be calculated, not 0)
  171 |     const progressBar = page.locator('[role="progressbar"]').first();
  172 |     await expect(progressBar).toBeVisible();
  173 |     
  174 |     // Check for real project manager info (not hardcoded emails)
  175 |     const projectManager = page.locator('text=Project Manager').locator('..');
  176 |     await expect(projectManager).toBeVisible();
  177 |     
  178 |     // Verify no "<EMAIL>" hardcoded email
  179 |     const hardcodedEmail = page.locator('text=<EMAIL>');
  180 |     await expect(hardcodedEmail).not.toBeVisible();
  181 |   });
  182 | });
```