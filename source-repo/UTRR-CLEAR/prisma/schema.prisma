generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  schemas   = ["auth", "public"]
}

model password_reset_tokens {
  id         Int       @id @default(autoincrement())
  user_id    Int
  token      String    @unique
  expires_at DateTime  @db.Timestamp(6)
  created_at DateTime  @default(now()) @db.Timestamp(6)
  used_at    DateTime? @db.Timestamp(6)
  users      users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("auth")
}

model permissions {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  description      String?
  created_at       DateTime           @default(now()) @db.Timestamp(6)
  role_permissions role_permissions[]

  @@schema("auth")
}

model role_permissions {
  id            Int         @id @default(autoincrement())
  role          String
  permission_id Int
  created_at    DateTime    @default(now()) @db.Timestamp(6)
  permissions   permissions @relation(fields: [permission_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("auth")
}

model sessions {
  id         Int      @id @default(autoincrement())
  user_id    Int
  token      String   @unique
  user_agent String?
  ip_address String?
  expires_at DateTime @db.Timestamp(6)
  created_at DateTime @default(now()) @db.Timestamp(6)
  users      users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("auth")
}

model auth_user_sessions {
  sid    String   @id @db.VarChar
  sess   Json     @db.Json
  expire DateTime @db.Timestamp(6)

  @@index([expire], map: "IDX_auth_session_expire")
  @@map("user_sessions")
  @@schema("auth")
}

model auth_user_version_acknowledgments {
  id              Int          @id @default(autoincrement())
  user_id         Int
  version_id      Int
  acknowledged_at DateTime     @default(now()) @db.Timestamp(6)
  users           users        @relation("AuthUserVersionAcks", fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  app_versions    app_versions @relation("AuthVersionAcks", fields: [version_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("user_version_acknowledgments")
  @@schema("auth")
}

model users {
  id                                Int                                 @id @default(autoincrement())
  username                          String                              @unique
  password                          String
  first_name                        String
  last_name                         String
  email                             String
  role                              String                              @default("Utility Coordinator")
  avatar_url                        String?
  unit_preference                   String                              @default("imperial")
  custom_settings                   Json?                               @default("{}")
  is_active                         Boolean                             @default(true)
  reset_password_on_login           Boolean                             @default(false)
  is_admin                          Boolean                             @default(false)
  created_by_saml                   Boolean                             @default(false)
  created_at                        DateTime                            @default(now()) @db.Timestamp(6)
  updated_at                        DateTime                            @default(now()) @db.Timestamp(6)
  dashboard_layout                  Json?                               @default("{}")
  ui_preferences                    Json?                               @default("{}")
  password_reset_tokens             password_reset_tokens[]
  sessions                          sessions[]
  user_email_tokens                 user_email_tokens[]
  auth_user_version_acknowledgments auth_user_version_acknowledgments[] @relation("AuthUserVersionAcks")
  activities                        activities[]
  chat_messages                     chat_messages[]
  comments                          comments[]
  communication_logs                communication_logs[]
  dashboard_widgets                 dashboard_widgets[]
  email_project_links               email_project_links[]
  map_documents                     map_documents[]
  pending_changes                   pending_changes[]
  project_documents                 project_documents[]
  project_logs                      project_logs[]
  project_spatial_data              project_spatial_data[]
  created_templates                 project_templates[]                 @relation("CreatedTemplates")
  tasks                             tasks[]
  sentMessages                      TemporaryMessage[]                  @relation("SentMessages")
  receivedMessages                  TemporaryMessage[]                  @relation("ReceivedMessages")
  timesheet_entries                 timesheet_entries[]
  notifications                     notifications[]

  @@schema("auth")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model users_backup {
  id                      Int?
  username                String?
  password                String?
  first_name              String?
  last_name               String?
  email                   String?
  role                    String?
  created_at              DateTime? @db.Timestamp(6)
  updated_at              DateTime? @db.Timestamp(6)
  preferences             Json?
  avatar_url              String?
  is_active               Boolean?
  reset_password_on_login Boolean?
  unit_preference         String?
  custom_settings         Json?

  @@ignore
  @@schema("auth")
}

model activities {
  id         Int      @id @default(autoincrement())
  project_id String
  user_id    Int
  action     String
  target     String?
  timestamp  DateTime @default(now()) @db.Timestamp(6)
  projects   projects @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users      users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model admin_logs {
  id           Int      @id @default(autoincrement())
  action       String   @db.VarChar(100)
  performed_by Int?
  details      Json
  created_at   DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model analytics_events {
  id         Int      @id @default(autoincrement())
  event_type String
  user_id    Int?
  path       String?
  details    Json?
  timestamp  DateTime @default(now()) @db.Timestamp(6)
  session_id String?
  ip_address String?
  user_agent String?
  created_at DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model app_versions {
  id                          Int                                   @id @default(autoincrement())
  version                     String                                @unique
  release_date                DateTime                              @default(now()) @db.Timestamp(6)
  title                       String
  description                 String
  features                    Json?                                 @default("[]")
  bug_fixes                   Json?                                 @default("[]")
  is_active                   Boolean?                              @default(true)
  required                    Boolean?                              @default(false)
  created_at                  DateTime                              @default(now()) @db.Timestamp(6)
  auth_user_acknowledgments   auth_user_version_acknowledgments[]   @relation("AuthVersionAcks")
  pending_changes             pending_changes[]
  public_user_acknowledgments public_user_version_acknowledgments[] @relation("PublicVersionAcks")

  @@schema("public")
}

model chat_messages {
  id         Int      @id @default(autoincrement())
  user_id    Int
  content    String
  timestamp  DateTime @default(now()) @db.Timestamp(6)
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)
  users      users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model clients {
  id           Int      @id @default(autoincrement())
  name         String   @unique
  abbreviation String?
  active       Boolean? @default(true)
  notes        String?
  created_at   DateTime @default(now()) @db.Timestamp(6)
  updated_at   DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model communication_logs {
  id                 Int           @id @default(autoincrement())
  project_id         String
  communication_type String
  subject            String
  content            String
  date               DateTime      @db.Timestamp(6)
  participants       String[]
  attachments        String[]
  user_id            Int?
  stakeholder_id     Int?
  follow_up_required Boolean?      @default(false)
  follow_up_date     DateTime?     @db.Timestamp(6)
  status             String?       @default("active")
  created_at         DateTime      @default(now()) @db.Timestamp(6)
  projects           projects      @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  stakeholders       stakeholders? @relation(fields: [stakeholder_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users              users?        @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model conflicts {
  id                           Int                      @id @default(autoincrement())
  project_id                   String
  utility_id                   Int?
  description                  String
  status                       String                   @default("open")
  priority                     String
  location                     String?
  resolution_notes             String?
  created_at                   DateTime                 @default(now()) @db.Timestamp(6)
  updated_at                   DateTime                 @default(now()) @db.Timestamp(6)
  confidence_score             Int?
  conflict_3d_geometry         Unsupported("geometry")?
  conflict_elevation           Decimal?                 @db.Decimal(10, 3)
  conflict_type                String?
  detected_timestamp           DateTime?                @db.Timestamp(6)
  detection_method             String?                  @default("manual")
  impact_score                 Int?
  is_vertical_conflict         Boolean?                 @default(false)
  likelihood_score             Int?
  reviewed_by                  String?
  reviewed_timestamp           DateTime?                @db.Timestamp(6)
  risk_score                   Int?
  utility2_id                  Int?
  vertical_clearance_violation Decimal?                 @db.Decimal(8, 2)
  projects                     projects                 @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  utility2                     utilities?               @relation("ConflictUtility2", fields: [utility2_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  utilities                    utilities?               @relation("ConflictUtility", fields: [utility_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model coordinate_systems {
  id                            Int                    @id @default(autoincrement())
  name                          String
  srid                          Int
  type                          String
  description                   String?
  created_at                    DateTime               @default(now()) @db.Timestamp(6)
  state                         String?                @default("Indiana")
  zone_name                     String?
  county_code                   String?
  projection_group              String?
  lat_origin_deg                Decimal?               @db.Decimal
  lat_origin_min                Decimal?               @db.Decimal
  lat_origin_sec                Decimal?               @db.Decimal
  central_meridian_deg          Decimal?               @db.Decimal
  central_meridian_min          Decimal?               @db.Decimal
  central_meridian_sec          Decimal?               @db.Decimal
  central_meridian_scale        Decimal?               @db.Decimal
  false_easting                 Decimal?               @db.Decimal
  false_northing                Decimal?               @db.Decimal
  validation_point_easting      Decimal?               @db.Decimal
  validation_point_northing     Decimal?               @db.Decimal
  indiana_2digit_county         String?
  projection_group_abbreviation String?
  map_documents                 map_documents[]
  project_spatial_data          project_spatial_data[]
  utility_line_data             utility_line_data[]

  @@schema("public")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model coordinate_systems_backup {
  id                            Int?
  name                          String?
  srid                          Int?
  type                          String?
  description                   String?
  created_at                    DateTime? @db.Timestamp(6)
  state                         String?
  zone_name                     String?
  county_code                   String?
  projection_group              String?
  lat_origin_deg                Decimal?  @db.Decimal
  lat_origin_min                Decimal?  @db.Decimal
  lat_origin_sec                Decimal?  @db.Decimal
  central_meridian_deg          Decimal?  @db.Decimal
  central_meridian_min          Decimal?  @db.Decimal
  central_meridian_sec          Decimal?  @db.Decimal
  central_meridian_scale        Decimal?  @db.Decimal
  false_easting                 Decimal?  @db.Decimal
  false_northing                Decimal?  @db.Decimal
  validation_point_easting      Decimal?  @db.Decimal
  validation_point_northing     Decimal?  @db.Decimal
  indiana_2digit_county         String?
  projection_group_abbreviation String?

  @@ignore
  @@schema("public")
}

model database_backups {
  id             Int      @id @default(autoincrement())
  filename       String
  size           BigInt
  size_formatted String
  backup_type    String
  user_id        Int
  created_at     DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model feature_request_attachments {
  id               Int              @id @default(autoincrement())
  request_id       Int
  file_name        String
  file_url         String
  file_type        String
  file_size        Int
  uploaded_by      Int
  created_at       DateTime         @default(now()) @db.Timestamp(6)
  feature_requests feature_requests @relation(fields: [request_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

model feature_request_comments {
  id               Int              @id @default(autoincrement())
  request_id       Int
  user_id          Int
  content          String
  created_at       DateTime         @default(now()) @db.Timestamp(6)
  updated_at       DateTime         @default(now()) @db.Timestamp(6)
  feature_requests feature_requests @relation(fields: [request_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

model feature_request_votes {
  id               Int              @id @default(autoincrement())
  request_id       Int
  user_id          Int
  created_at       DateTime         @default(now()) @db.Timestamp(6)
  feature_requests feature_requests @relation(fields: [request_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([request_id, user_id])
  @@schema("public")
}

model feature_requests {
  id                          Int                           @id @default(autoincrement())
  title                       String
  description                 String
  status                      String                        @default("open")
  type                        String
  priority                    String                        @default("medium")
  votes                       Int                           @default(0)
  created_by                  Int
  assigned_to                 Int?
  created_at                  DateTime                      @default(now()) @db.Timestamp(6)
  updated_at                  DateTime                      @default(now()) @db.Timestamp(6)
  resolved_at                 DateTime?                     @db.Timestamp(6)
  github_issue_number         Int?
  github_issue_url            String?                       @db.VarChar(500)
  feature_request_attachments feature_request_attachments[]
  feature_request_comments    feature_request_comments[]
  feature_request_votes       feature_request_votes[]

  @@index([github_issue_number])
  @@schema("public")
}

model fee_template_options {
  id                    Int                   @id @default(autoincrement())
  section_id            Int
  name                  String
  value                 String
  default_value         String?
  display_order         Int
  is_active             Boolean?              @default(true)
  created_at            DateTime              @default(now()) @db.Timestamp(6)
  updated_at            DateTime              @default(now()) @db.Timestamp(6)
  fee_template_sections fee_template_sections @relation(fields: [section_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  option_badges         option_badges[]

  @@schema("public")
}

model fee_template_sections {
  id                   Int                    @id @default(autoincrement())
  template_id          Int
  name                 String
  description          String?
  type                 String
  display_order        Int
  sheet_name           String
  sheet_order          Int?                   @default(1)
  is_visible           Boolean?               @default(true)
  created_at           DateTime               @default(now()) @db.Timestamp(6)
  updated_at           DateTime               @default(now()) @db.Timestamp(6)
  fee_template_options fee_template_options[]
  fee_templates        fee_templates          @relation(fields: [template_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model fee_templates {
  id                      Int                       @id @default(autoincrement())
  name                    String
  description             String?
  company_id              String?
  is_default              Boolean?                  @default(false)
  created_at              DateTime                  @default(now()) @db.Timestamp(6)
  updated_at              DateTime                  @default(now()) @db.Timestamp(6)
  apply_to                String?                   @default("fees")
  fee_template_sections   fee_template_sections[]
  template_allowed_badges template_allowed_badges[]

  @@schema("public")
}

model issues {
  id            String   @id
  project_id    String
  type          String
  description   String
  status        String   @default("Open")
  priority      String   @default("Medium")
  reported_date DateTime @db.Date
  assigned_to   String
  resolution    String?
  created_at    DateTime @default(now()) @db.Timestamp(6)
  updated_at    DateTime @default(now()) @db.Timestamp(6)
  projects      projects @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model knowledge_articles {
  id                   Int                  @id @default(autoincrement())
  category_id          String
  title                String
  content              String
  description          String?
  tags                 Json?                @default("[]")
  created_by_id        Int
  created_at           DateTime             @default(now()) @db.Timestamp(6)
  last_updated         DateTime             @default(now()) @db.Timestamp(6)
  knowledge_categories knowledge_categories @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "knowledge_articles_category_id_knowledge_categories_id_fk")

  @@schema("public")
}

model knowledge_categories {
  id                 String               @id
  name               String
  description        String?
  article_count      Int?                 @default(0)
  created_at         DateTime             @default(now()) @db.Timestamp(6)
  updated_at         DateTime             @default(now()) @db.Timestamp(6)
  knowledge_articles knowledge_articles[]

  @@schema("public")
}

model line_styles {
  id                Int                 @id @default(autoincrement())
  utility_type      String
  installation_type String
  line_color        String
  line_weight       Float               @db.Real
  line_pattern      String
  letter_symbols    String?
  created_at        DateTime            @default(now()) @db.Timestamp(6)
  updated_at        DateTime            @default(now()) @db.Timestamp(6)
  color_name        String?
  dash_pattern      String?
  letter_spacing    Int?
  standard_811      Boolean?            @default(true)
  utility_line_data utility_line_data[]

  @@unique([utility_type, installation_type], map: "line_styles_unique_utility_installation")
  @@schema("public")
}

model map_documents {
  id                            Int                 @id @default(autoincrement())
  project_id                    String
  name                          String
  description                   String?
  document_type                 String
  file_path                     String
  original_coordinate_system_id Int?
  converted_to_projection       Boolean?            @default(false)
  uploaded_at                   DateTime            @default(now()) @db.Timestamp(6)
  uploaded_by_id                Int?
  coordinate_systems            coordinate_systems? @relation(fields: [original_coordinate_system_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  projects                      projects            @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users                         users?              @relation(fields: [uploaded_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model meetings {
  id           Int       @id @default(autoincrement())
  project_id   String?
  title        String
  description  String?
  location     String
  start_time   DateTime  @db.Timestamp(6)
  end_time     DateTime  @db.Timestamp(6)
  attendee_ids Json
  notes        String?
  created_at   DateTime  @default(now()) @db.Timestamp(6)
  updated_at   DateTime  @default(now()) @db.Timestamp(6)
  projects     projects? @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model option_badges {
  id                   Int                  @id @default(autoincrement())
  option_id            Int
  badge_id             Int
  is_default           Boolean?             @default(false)
  created_at           DateTime             @default(now()) @db.Timestamp(6)
  template_badges      template_badges      @relation(fields: [badge_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  fee_template_options fee_template_options @relation(fields: [option_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([option_id, badge_id])
  @@schema("public")
}

model pending_changes {
  id                     Int           @id @default(autoincrement())
  change_type            String
  description            String
  target_version         String?
  added_by_id            Int
  added_at               DateTime      @default(now()) @db.Timestamp(6)
  included               Boolean       @default(false)
  included_in_version_id Int?
  details                Json?         @default("{}")
  app_versions           app_versions? @relation(fields: [included_in_version_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_included_in_version")
  added_by               users         @relation(fields: [added_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model project_documents {
  id             Int      @id @default(autoincrement())
  project_id     String
  name           String
  description    String?
  document_type  String
  file_path      String
  uploaded_at    DateTime @db.Timestamp(6)
  uploaded_by_id Int?
  category       String?
  tags           String[]
  created_at     DateTime @default(now()) @db.Timestamp(6)
  file_size      Int?
  projects       projects @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users          users?   @relation(fields: [uploaded_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model project_fees {
  id                            Int      @id @default(autoincrement())
  project_description           String
  client_project_number         String?
  egis_number                   String
  client                        String
  work_type                     String
  scope_type                    String
  utilities_original            Int      @default(0)
  utilities_supplement1         Int?     @default(0)
  utilities_supplement2         Int?     @default(0)
  utilities_supplement3         Int?     @default(0)
  utilities_supplement4         Int?     @default(0)
  preliminary_field_check       Int?     @default(1)
  utility_coordination_meetings Int?     @default(2)
  final_field_check             Int?     @default(1)
  preconstruction_conference    Int?     @default(1)
  meetings_during_construction  Int?     @default(0)
  miles_one_way                 Int?     @default(0)
  hours_one_way                 Decimal? @default(0) @db.Decimal(4, 2)
  reimbursable_utilities        String?  @default("Assumed NO Reimbursable Utilities (Std)")
  relocations                   String?  @default("Assumed ALL utilities will relocate (Std)")
  project_manager               String?
  department_manager            String?
  utility_coordinator           String
  contract_amount               String?
  last_status                   String?  @default("Pending")
  latest_comment                String?
  version_number                String?  @default("1.0")
  export_path                   String?
  fee_type                      String   @default("Initial Fee")
  supplement_number             Int?     @default(0)
  parent_fee_id                 Int?
  attachments                   String?
  created_at                    DateTime @default(now()) @db.Timestamp(6)
  updated_at                    DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model project_logs {
  id          Int      @id @default(autoincrement())
  project_id  String
  user_id     Int
  action_type String
  entity_type String
  entity_id   String?
  details     Json?    @default("{}")
  timestamp   DateTime @default(now()) @db.Timestamp(6)
  ip_address  String?
  user_agent  String?
  projects    projects @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users       users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model project_phases {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  order_index Int
  created_at  DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model project_spatial_data {
  id                   Int                      @id @default(autoincrement())
  project_id           String
  name                 String
  description          String?
  data_type            String
  coordinate_system_id Int
  geometry             Unsupported("geometry")?
  properties           Json?                    @default("{}")
  created_at           DateTime                 @default(now()) @db.Timestamp(6)
  updated_at           DateTime                 @default(now()) @db.Timestamp(6)
  created_by_id        Int?
  coordinate_systems   coordinate_systems       @relation(fields: [coordinate_system_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users                users?                   @relation(fields: [created_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  projects             projects                 @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model projects {
  monday_id                  String?
  name                       String
  client                     String
  description                String?
  start_date                 DateTime?              @db.Date
  end_date                   DateTime?              @db.Date
  manager_id                 Int?
  high_priority_items        Int?
  medium_priority_items      Int?
  low_priority_items         Int?
  created_at                 DateTime               @db.Timestamp(6)
  updated_at                 DateTime               @db.Timestamp(6)
  record_id                  String?
  client_job_number          String?
  work_type                  String?
  rag_status                 String?
  project_id_only            String?
  phase_id_only              String?
  last_milestone             String?
  coordination_type          String?
  project_funding            String?
  ntp_date                   DateTime?              @db.Date
  letting_bid_date           DateTime?              @db.Date
  this_month_status          String?
  status_update_date         DateTime?              @db.Date
  client_pm                  String?
  hourly_rate                Decimal?               @db.Decimal
  contract_amount            Decimal?               @db.Decimal
  billed_to_date             Decimal?               @db.Decimal
  project_hours_for_billed   Decimal?               @db.Decimal
  wip                        Decimal?               @db.Decimal
  billed_plus_wip            Decimal?               @db.Decimal
  current_cost               Decimal?               @db.Decimal
  billed_percentage          Decimal?               @db.Decimal
  profit_to_date             Decimal?               @db.Decimal
  profit_percentage          Decimal?               @db.Decimal
  project_priority           String?
  egis_project_manager       String?
  egis_project_manager_email String?
  client_contact             String?
  Project_Health__RAG_       String?                @default("Project Health") @map("Project Health (RAG)")
  current_phase              String?
  coordinator_id             String?
  id                         String                 @id
  template_id                String?
  activities                 activities[]
  communication_logs         communication_logs[]
  conflicts                  conflicts[]
  email_project_links        email_project_links[]
  issues                     issues[]
  map_documents              map_documents[]
  meetings                   meetings[]
  project_documents          project_documents[]
  project_logs               project_logs[]
  project_spatial_data       project_spatial_data[]
  template                   project_templates?     @relation("ProjectTemplate", fields: [template_id], references: [id])
  sub_invoices               sub_invoices[]
  tasks                      tasks[]
  subtasks                   subtasks[]
  utilities                  utilities[]

  @@schema("public")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model spatial_ref_sys {
  srid      Int     @id
  auth_name String? @db.VarChar(256)
  auth_srid Int?
  srtext    String? @db.VarChar(2048)
  proj4text String? @db.VarChar(2048)

  @@schema("public")
}

model stakeholders {
  id                      Int                  @id @default(autoincrement())
  full_name               String
  contact_company         String
  company_abbreviation    String?
  type_delivery           String?
  stakeholder_type        String?
  business_phone          String?
  business_fax            String?
  mobile_phone            String?
  email                   String?
  is_utility_coordinator  Boolean?             @default(false)
  is_indot_authorized_rep Boolean?             @default(false)
  aliases                 String?
  company_logo            String?
  client_company          String?
  created_at              DateTime             @default(now()) @db.Timestamp(6)
  updated_at              DateTime             @default(now()) @db.Timestamp(6)
  full_name_company       String?
  communication_logs      communication_logs[]

  @@unique([full_name_company, type_delivery], map: "stakeholders_name_company_type_unique")
  @@schema("public")
}

model sub_invoices {
  id                 Int       @id @default(autoincrement())
  sub_company        String
  invoice_number     String
  associated_project String
  invoiced_amount    Decimal   @default(0) @db.Decimal(10, 2)
  received_date      DateTime? @db.Date
  signed_date        DateTime? @db.Date
  notes              String?
  status             String    @default("Pending")
  attachment_path    String?
  sub_consultant_fee Decimal   @default(0) @db.Decimal(10, 2)
  full_name          String
  contact_company    String
  created_at         DateTime  @default(now()) @db.Timestamp(6)
  updated_at         DateTime  @default(now()) @db.Timestamp(6)
  projects           projects  @relation(fields: [associated_project], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model tasks {
  id                  String    @id
  project_id          String?
  title               String
  description         String?
  priority            String    @default("Medium")
  due_date            DateTime? @db.Date
  completed           Boolean?  @default(false)
  assigned_to_id      Int?
  created_at          DateTime  @default(now()) @db.Timestamp(6)
  updated_at          DateTime  @default(now()) @db.Timestamp(6)
  monday_id           String?   @db.VarChar
  status              String?   @default("Not Started") @db.VarChar
  start_date          DateTime? @db.Date
  end_date            DateTime? @db.Date
  estimated_hours     Decimal?  @db.Decimal(8, 2)
  actual_hours        Decimal?  @db.Decimal(8, 2)
  progress_percentage Int?      @default(0)
  assigned_to         String?   @db.VarChar
  dependencies        Json?     @default("[]")
  predecessors        String[]  @default([]) @db.VarChar
  task_type           String?   @default("Task") @db.VarChar
  milestone           Boolean?  @default(false)
  critical_path       Boolean?  @default(false)
  phase               String?   @db.VarChar
  board_id            String?   @db.VarChar
  users               users?    @relation(fields: [assigned_to_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  projects            projects? @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subtasks            subtasks[]

  @@index([board_id], map: "idx_tasks_board_id")
  @@index([monday_id], map: "idx_tasks_monday_id")
  @@index([start_date], map: "idx_tasks_start_date")
  @@index([status], map: "idx_tasks_status")
  @@schema("public")
}

model template_allowed_badges {
  id              Int             @id @default(autoincrement())
  template_id     Int
  badge_id        Int
  created_at      DateTime        @default(now()) @db.Timestamp(6)
  template_badges template_badges @relation(fields: [badge_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  fee_templates   fee_templates   @relation(fields: [template_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([template_id, badge_id])
  @@schema("public")
}

model template_badges {
  id                      Int                       @id @default(autoincrement())
  name                    String
  value                   String?                   @unique
  label                   String
  description             String?
  color                   String                    @default("#dddddd")
  is_global               Boolean?                  @default(true)
  is_active               Boolean?                  @default(true)
  created_at              DateTime                  @default(now()) @db.Timestamp(6)
  updated_at              DateTime                  @default(now()) @db.Timestamp(6)
  option_badges           option_badges[]
  template_allowed_badges template_allowed_badges[]

  @@schema("public")
}

model timesheet_entries {
  id                  Int               @id @default(autoincrement())
  timesheet_period_id String
  user_id             Int
  project_id          String?
  day_of_week         Int
  hours               Decimal           @db.Decimal(4, 1)
  notes               String?
  status              String            @default("draft")
  created_at          DateTime          @default(now()) @db.Timestamp(6)
  updated_at          DateTime          @default(now()) @db.Timestamp(6)
  timesheet_periods   timesheet_periods @relation(fields: [timesheet_period_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "timesheet_entries_timesheet_period_id_timesheet_periods_id_fk")
  users               users             @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model timesheet_periods {
  id                String              @id
  start_date        DateTime            @db.Date
  end_date          DateTime            @db.Date
  is_current        Boolean?            @default(false)
  created_at        DateTime            @default(now()) @db.Timestamp(6)
  timesheet_entries timesheet_entries[]

  @@schema("public")
}

model public_user_sessions {
  sid    String   @id(map: "session_pkey") @db.VarChar
  sess   Json     @db.Json
  expire DateTime @db.Timestamp(6)

  @@index([expire], map: "IDX_session_expire")
  @@map("user_sessions")
  @@schema("public")
}

model public_user_version_acknowledgments {
  id              Int          @id @default(autoincrement())
  user_id         Int
  version_id      Int
  acknowledged_at DateTime     @default(now()) @db.Timestamp(6)
  app_versions    app_versions @relation("PublicVersionAcks", fields: [version_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("user_version_acknowledgments")
  @@schema("public")
}

model utilities {
  id                 Int                 @id @default(autoincrement())
  project_id         String
  name               String
  type               String
  status             String              @default("active")
  contact_name       String?
  contact_email      String?
  contact_phone      String?
  notes              String?
  last_response      DateTime?           @db.Date
  created_at         DateTime            @default(now()) @db.Timestamp(6)
  updated_at         DateTime            @default(now()) @db.Timestamp(6)
  initiation_date    DateTime?           @db.Date
  depth_notes        String?
  installation_depth Decimal?            @db.Decimal(8, 2)
  vertical_clearance Decimal?            @db.Decimal(8, 2)
  vertical_datum     String?
  conflicts2         conflicts[]         @relation("ConflictUtility2")
  conflicts          conflicts[]         @relation("ConflictUtility")
  projects           projects            @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  utility_line_data  utility_line_data[]

  @@schema("public")
}

model utility_line_data {
  id                   Int                      @id @default(autoincrement())
  project_id           String
  utility_id           Int?
  name                 String
  line_type            String
  utility_type         String
  installation_type    String
  coordinate_system_id Int
  geometry             Unsupported("geometry")?
  properties           Json?                    @default("{}")
  created_at           DateTime                 @default(now()) @db.Timestamp(6)
  updated_at           DateTime                 @default(now()) @db.Timestamp(6)
  created_by_id        Int?
  line_style_id        Int?
  coordinate_systems   coordinate_systems       @relation(fields: [coordinate_system_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  line_styles          line_styles?             @relation(fields: [line_style_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  utilities            utilities?               @relation(fields: [utility_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model utility_phase_statuses {
  id            Int       @id @default(autoincrement())
  utility_id    Int
  phase_id      Int
  status        String?
  response_date DateTime? @db.Date
  notes         String?
  created_at    DateTime? @default(now()) @db.Timestamp(6)
  updated_at    DateTime? @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model work_types {
  id                    Int      @id @default(autoincrement())
  name                  String   @unique
  complexity_multiplier Decimal  @db.Decimal(4, 2)
  active                Boolean? @default(true)
  created_at            DateTime @default(now()) @db.Timestamp(6)
  updated_at            DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model comments {
  id               String     @id @default(uuid())
  commentable_type String     @db.VarChar(50)
  commentable_id   String
  user_id          Int
  content          String
  parent_id        String?
  mentions         Int[]      @default([])
  deleted_at       DateTime?
  created_at       DateTime   @default(now()) @db.Timestamp(6)
  updated_at       DateTime   @default(now()) @db.Timestamp(6)
  parent           comments?  @relation("CommentReplies", fields: [parent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  replies          comments[] @relation("CommentReplies")
  user             users      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([commentable_type, commentable_id])
  @@index([user_id])
  @@index([parent_id])
  @@schema("public")
}

model comment_attachments {
  id         String   @id @default(uuid())
  comment_id String
  file_name  String
  file_url   String
  file_type  String
  file_size  Int
  created_at DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model notification_preferences {
  user_id           Int      @id
  email_enabled     Boolean  @default(true)
  push_enabled      Boolean  @default(true)
  quiet_hours_start String?
  quiet_hours_end   String?
  created_at        DateTime @default(now()) @db.Timestamp(6)

  @@schema("public")
}

model user_activities {
  id          String   @id @default(uuid())
  user_id     Int
  action_type String   @db.VarChar(50)
  entity_type String?  @db.VarChar(50)
  entity_id   String?
  metadata    Json?    @default("{}")
  created_at  DateTime @default(now()) @db.Timestamp(6)

  @@index([user_id])
  @@index([action_type])
  @@index([created_at])
  @@schema("public")
}

model saved_searches {
  id           String   @id @default(uuid())
  user_id      Int
  name         String   @db.VarChar(100)
  search_query String?
  filters      Json?    @default("{}")
  entity_type  String?  @db.VarChar(50)
  created_at   DateTime @default(now()) @db.Timestamp(6)

  @@index([user_id])
  @@schema("public")
}

model user_skills {
  id          String   @id @default(uuid())
  user_id     Int
  skill_name  String   @db.VarChar(100)
  level       Int      @default(1)
  endorsed_by Int[]    @default([])
  created_at  DateTime @default(now()) @db.Timestamp(6)

  @@index([user_id])
  @@schema("public")
}

model user_notes {
  id         String   @id @default(uuid())
  user_id    Int
  title      String   @db.VarChar(255)
  content    String
  tags       String[] @default([])
  is_pinned  Boolean  @default(false)
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @updatedAt @db.Timestamp(6)

  @@index([user_id])
  @@index([created_at])
  @@schema("public")
}

model TemporaryMessage {
  id         Int      @id @default(autoincrement())
  fromUserId Int      @map("from_user_id")
  toUserId   Int      @map("to_user_id")
  message    String
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  expiresAt  DateTime @map("expires_at") @db.Timestamp(6)
  fromUser   users    @relation("SentMessages", fields: [fromUserId], references: [id], onDelete: Cascade)
  toUser     users    @relation("ReceivedMessages", fields: [toUserId], references: [id], onDelete: Cascade)

  @@map("temporary_messages")
  @@schema("public")
}

model project_templates {
  id               String             @id @default(uuid())
  name             String
  description      String?
  icon             String?
  color            String?
  is_active        Boolean            @default(true)
  is_default       Boolean            @default(false)
  created_by_id    Int
  workflow_phases  Json?              @default("[]")
  settings         Json?              @default("{}")
  created_at       DateTime           @default(now()) @db.Timestamp(6)
  updated_at       DateTime           @default(now()) @db.Timestamp(6)
  created_by       users              @relation("CreatedTemplates", fields: [created_by_id], references: [id])
  projects         projects[]         @relation("ProjectTemplate")
  template_columns template_columns[]
  template_tasks   template_tasks[]

  @@index([created_by_id])
  @@schema("public")
}

model template_columns {
  id            String                 @id @default(uuid())
  template_id   String
  name          String
  display_name  String
  column_type   String
  position      Int
  width         Int?                   @default(150)
  is_required   Boolean                @default(false)
  is_visible    Boolean                @default(true)
  is_system     Boolean                @default(false)
  configuration Json?                  @default("{}")
  created_at    DateTime               @default(now()) @db.Timestamp(6)
  updated_at    DateTime               @default(now()) @db.Timestamp(6)
  template      project_templates      @relation(fields: [template_id], references: [id], onDelete: Cascade)
  task_values   template_task_values[]

  @@unique([template_id, position])
  @@index([template_id])
  @@schema("public")
}

model template_tasks {
  id           String                 @id @default(uuid())
  template_id  String
  name         String
  description  String?
  phase        String?
  position     Int
  duration     Int?
  dependencies String[]               @default([])
  created_at   DateTime               @default(now()) @db.Timestamp(6)
  updated_at   DateTime               @default(now()) @db.Timestamp(6)
  task_values  template_task_values[]
  template     project_templates      @relation(fields: [template_id], references: [id], onDelete: Cascade)

  @@unique([template_id, position])
  @@index([template_id])
  @@schema("public")
}

model template_task_values {
  id         String           @id @default(uuid())
  task_id    String
  column_id  String
  value      Json?
  created_at DateTime         @default(now()) @db.Timestamp(6)
  updated_at DateTime         @default(now()) @db.Timestamp(6)
  column     template_columns @relation(fields: [column_id], references: [id], onDelete: Cascade)
  task       template_tasks   @relation(fields: [task_id], references: [id], onDelete: Cascade)

  @@unique([task_id, column_id])
  @@index([task_id])
  @@index([column_id])
  @@schema("public")
}

model subtasks {
  id                    String    @id @default(uuid())
  parent_task_id        String
  project_id            String?
  title                 String
  description           String?
  
  // Utility-specific fields
  utility_company       String?
  utility_contact       String?
  utility_email         String?
  utility_phone         String?
  
  // Scheduling fields (inherited from parent but can be overridden)
  target_start_date     DateTime? @db.Date
  target_end_date       DateTime? @db.Date
  actual_start_date     DateTime? @db.Date
  actual_end_date       DateTime? @db.Date
  duration              Int?      // in days
  progress_percentage   Int?      @default(0)
  
  // Status and priority
  status                String?   @default("Not Started") @db.VarChar
  priority              String    @default("Medium")
  
  // Assignment
  assigned_to_id        Int?
  assigned_to           String?   @db.VarChar
  
  // Dependencies and scheduling
  dependencies          Json?     @default("[]")
  critical_path         Boolean?  @default(false)
  variance_days         Int?      // calculated field for schedule variance
  
  // Standard fields
  completed             Boolean?  @default(false)
  created_at            DateTime  @default(now()) @db.Timestamp(6)
  updated_at            DateTime  @default(now()) @db.Timestamp(6)
  
  // Relations
  parent_task           tasks     @relation(fields: [parent_task_id], references: [id], onDelete: Cascade)
  projects              projects? @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([parent_task_id])
  @@index([project_id])
  @@index([utility_company])
  @@schema("public")
}

model dashboard_widgets {
  id          String   @id @default(uuid())
  user_id     Int
  widget_type String
  position    Json
  config      Json?
  is_visible  Boolean  @default(true)
  created_at  DateTime @default(now()) @db.Timestamp(6)
  updated_at  DateTime @default(now()) @db.Timestamp(6)
  users       users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([user_id])
  @@schema("public")
}

model user_email_tokens {
  id               String   @id @default(uuid())
  user_id          Int
  provider         String
  access_token     String
  refresh_token    String?
  token_expires_at DateTime
  email_address    String
  created_at       DateTime @default(now()) @db.Timestamp(6)
  updated_at       DateTime @default(now()) @db.Timestamp(6)
  users            users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, provider])
  @@index([user_id])
  @@schema("auth")
}

model email_project_links {
  id                String   @id @default(uuid())
  email_id          String
  email_subject     String
  email_from        String
  email_to          String[]
  email_date        DateTime
  project_id        String
  phase_id          String?
  task_id           String?
  linked_by_user_id Int
  provider          String
  email_thread_id   String?
  tags              String[]
  notes             String?
  created_at        DateTime @default(now()) @db.Timestamp(6)
  updated_at        DateTime @default(now()) @db.Timestamp(6)
  linked_by         users    @relation(fields: [linked_by_user_id], references: [id])
  projects          projects @relation(fields: [project_id], references: [id])

  @@index([project_id])
  @@index([linked_by_user_id])
  @@index([email_id, provider])
  @@schema("public")
}

model notifications {
  id           String    @id @default(uuid())
  user_id      Int
  type         String
  title        String
  message      String
  data         Json?
  read         Boolean   @default(false)
  read_at      DateTime? @db.Timestamp(6)
  action_url   String?
  action_label String?
  priority     String    @default("normal")
  expires_at   DateTime? @db.Timestamp(6)
  created_at   DateTime  @default(now()) @db.Timestamp(6)
  updated_at   DateTime  @default(now()) @db.Timestamp(6)
  user         users     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([created_at(sort: Desc)])
  @@index([read])
  @@index([type])
  @@schema("public")
}

model team_messages {
  id             String         @id @default(uuid())
  user_id        String         @db.Uuid // References auth.users.id via user_profiles
  content        String
  created_at     DateTime       @default(now()) @db.Timestamp(6)
  updated_at     DateTime       @default(now()) @db.Timestamp(6)
  user_profiles  user_profiles? @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([created_at])
  @@schema("public")
}

model user_profiles {
  id             String          @id @db.Uuid // References auth.users.id - no default uuid
  email          String          @unique
  display_name   String?
  first_name     String?
  last_name      String?
  created_at     DateTime        @default(now()) @db.Timestamp(6)
  updated_at     DateTime        @default(now()) @db.Timestamp(6)
  team_messages  team_messages[]

  @@schema("public")
}

model saml_configurations {
  id                   String        @id @default(uuid())
  enabled              Boolean       @default(true)
  name                 String
  entry_point          String
  issuer               String
  certificate          String
  signature_algorithm  String        @default("sha256")
  tenant               String
  product              String
  metadata_url         String?
  created_at           DateTime      @default(now()) @db.Timestamp(6)
  updated_at           DateTime      @default(now()) @db.Timestamp(6)

  @@unique([name])
  @@index([tenant, product])
  @@schema("public")
}

model rum_sessions {
  id              Int      @id @default(autoincrement())
  session_id      String   @unique
  user_id         String?
  start_time      DateTime @db.Timestamp(6)
  last_activity   DateTime @db.Timestamp(6)
  total_duration  Int      @default(0)
  device_type     String
  browser         String
  os              String
  screen_size     String
  connection_type String?
  client_ip       String?
  user_agent      String?
  created_at      DateTime @default(now()) @db.Timestamp(6)
  updated_at      DateTime @default(now()) @db.Timestamp(6)

  @@index([session_id])
  @@index([user_id])
  @@index([start_time])
  @@schema("public")
}

model rum_events {
  id              Int      @id @default(autoincrement())
  session_id      String
  user_id         String?
  event_type      String
  page            String
  timestamp       DateTime @db.Timestamp(6)
  event_data      Json?
  client_ip       String?
  user_agent      String?
  device_type     String?
  browser         String?
  os              String?
  screen_size     String?
  connection_type String?
  created_at      DateTime @default(now()) @db.Timestamp(6)

  @@index([session_id])
  @@index([user_id])
  @@index([event_type])
  @@index([page])
  @@index([timestamp])
  @@schema("public")
}

model rum_performance_metrics {
  id                  Int      @id @default(autoincrement())
  session_id          String
  page                String
  fcp                 Decimal? @db.Decimal(10, 2)
  lcp                 Decimal? @db.Decimal(10, 2)
  fid                 Decimal? @db.Decimal(10, 2)
  cls                 Decimal? @db.Decimal(10, 4)
  ttfb                Decimal? @db.Decimal(10, 2)
  dom_content_loaded  Decimal? @db.Decimal(10, 2)
  window_load         Decimal? @db.Decimal(10, 2)
  created_at          DateTime @default(now()) @db.Timestamp(6)
  updated_at          DateTime @default(now()) @db.Timestamp(6)

  @@unique([session_id, page])
  @@index([session_id])
  @@index([page])
  @@schema("public")
}

model rum_errors {
  id             Int      @id @default(autoincrement())
  session_id     String
  user_id        String?
  page           String
  error_message  String
  error_stack    String?
  filename       String?
  line_number    Int?
  column_number  Int?
  user_agent     String?
  timestamp      DateTime @db.Timestamp(6)
  created_at     DateTime @default(now()) @db.Timestamp(6)

  @@index([session_id])
  @@index([page])
  @@index([timestamp])
  @@schema("public")
}

model rum_user_actions {
  id          Int      @id @default(autoincrement())
  session_id  String
  user_id     String?
  page        String
  action_type String
  action_data Json?
  timestamp   DateTime @db.Timestamp(6)
  created_at  DateTime @default(now()) @db.Timestamp(6)

  @@index([session_id])
  @@index([action_type])
  @@index([timestamp])
  @@schema("public")
}

model rum_page_views {
  id         Int      @id @default(autoincrement())
  session_id String
  user_id    String?
  page       String
  page_title String?
  referrer   String?
  timestamp  DateTime @db.Timestamp(6)
  created_at DateTime @default(now()) @db.Timestamp(6)

  @@index([session_id])
  @@index([page])
  @@index([timestamp])
  @@schema("public")
}

model rum_daily_metrics {
  id                     Int      @id @default(autoincrement())
  date                   String
  device_type            String
  page_views             Int      @default(0)
  unique_sessions        Int      @default(0)
  user_actions           Int      @default(0)
  errors                 Int      @default(0)
  avg_fcp                Decimal? @db.Decimal(10, 2)
  avg_lcp                Decimal? @db.Decimal(10, 2)
  avg_fid                Decimal? @db.Decimal(10, 2)
  avg_cls                Decimal? @db.Decimal(10, 4)
  avg_ttfb               Decimal? @db.Decimal(10, 2)
  bounce_rate            Decimal? @db.Decimal(5, 2)
  avg_session_duration   Decimal? @db.Decimal(10, 2)
  created_at             DateTime @default(now()) @db.Timestamp(6)
  updated_at             DateTime @default(now()) @db.Timestamp(6)

  @@unique([date, device_type])
  @@index([date])
  @@index([device_type])
  @@schema("public")
}
