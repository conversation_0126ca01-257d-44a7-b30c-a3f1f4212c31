-- Performance indexes for UtilitySync

-- Foreign key indexes
CREATE INDEX IF NOT EXISTS idx_utilities_project_id ON utilities(project_id);
CREATE INDEX IF NOT EXISTS idx_utilities_utility_company_id ON utilities(utility_company_id);
CREATE INDEX IF NOT EXISTS idx_stakeholders_project_id ON stakeholders(project_id);
CREATE INDEX IF NOT EXISTS idx_conflicts_project_id ON conflicts(project_id);
CREATE INDEX IF NOT EXISTS idx_conflicts_utility_id ON conflicts(utility_id);
CREATE INDEX IF NOT EXISTS idx_project_logs_project_id ON project_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_project_logs_user_id ON project_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_documents_project_id ON documents(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_communications_project_id ON communications(project_id);
CREATE INDEX IF NOT EXISTS idx_communications_user_id ON communications(user_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_projects_rag_status_updated ON projects(rag_status, updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_active_status ON projects(is_active, rag_status);
CREATE INDEX IF NOT EXISTS idx_projects_client_active ON projects(client, is_active);
CREATE INDEX IF NOT EXISTS idx_projects_phase_status ON projects(current_phase, rag_status);
CREATE INDEX IF NOT EXISTS idx_utilities_project_status ON utilities(project_id, status);
CREATE INDEX IF NOT EXISTS idx_utilities_company_type ON utilities(utility_company_id, utility_type);

-- Date range indexes
CREATE INDEX IF NOT EXISTS idx_projects_start_end_date ON projects(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON projects(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_project_logs_created_at ON project_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_communications_created_at ON communications(created_at DESC);

-- Text search indexes (using GIN for PostgreSQL full-text search)
CREATE INDEX IF NOT EXISTS idx_projects_name_gin ON projects USING gin(to_tsvector('english', COALESCE(name, '')));
CREATE INDEX IF NOT EXISTS idx_projects_description_gin ON projects USING gin(to_tsvector('english', COALESCE(description, '')));
CREATE INDEX IF NOT EXISTS idx_projects_client_gin ON projects USING gin(to_tsvector('english', COALESCE(client, '')));
CREATE INDEX IF NOT EXISTS idx_stakeholders_name_gin ON stakeholders USING gin(to_tsvector('english', COALESCE(name, '')));
CREATE INDEX IF NOT EXISTS idx_stakeholders_organization_gin ON stakeholders USING gin(to_tsvector('english', COALESCE(organization, '')));

-- Spatial indexes (if geometry columns exist)
CREATE INDEX IF NOT EXISTS idx_utilities_geometry ON utilities USING gist(geometry) WHERE geometry IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_utilities_location ON utilities USING gist(location) WHERE location IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_boundary ON projects USING gist(boundary) WHERE boundary IS NOT NULL;

-- User and organization indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);

-- Status and enum indexes
CREATE INDEX IF NOT EXISTS idx_projects_rag_status ON projects(rag_status);
CREATE INDEX IF NOT EXISTS idx_utilities_status ON utilities(status);
CREATE INDEX IF NOT EXISTS idx_conflicts_status ON conflicts(status);
CREATE INDEX IF NOT EXISTS idx_conflicts_severity ON conflicts(severity);

-- Partial indexes for common filters
CREATE INDEX IF NOT EXISTS idx_projects_active_red ON projects(id) WHERE is_active = true AND rag_status = 'red';
CREATE INDEX IF NOT EXISTS idx_projects_active_amber ON projects(id) WHERE is_active = true AND rag_status = 'amber';
CREATE INDEX IF NOT EXISTS idx_utilities_active ON utilities(project_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_conflicts_unresolved ON conflicts(project_id) WHERE status != 'resolved';

-- Monday.com integration indexes
CREATE INDEX IF NOT EXISTS idx_projects_monday_item_id ON projects(monday_item_id) WHERE monday_item_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_smart_id ON projects(smart_id) WHERE smart_id IS NOT NULL;

-- Create function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at columns if not exists
DO $$
BEGIN
    -- Projects table
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_projects_updated_at') THEN
        CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Utilities table
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_utilities_updated_at') THEN
        CREATE TRIGGER update_utilities_updated_at BEFORE UPDATE ON utilities
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Stakeholders table
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_stakeholders_updated_at') THEN
        CREATE TRIGGER update_stakeholders_updated_at BEFORE UPDATE ON stakeholders
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Analyze tables to update statistics
ANALYZE projects;
ANALYZE utilities;
ANALYZE stakeholders;
ANALYZE conflicts;
ANALYZE project_logs;
ANALYZE users;
ANALYZE documents;
ANALYZE communications;