-- Critical spatial indexes for PostGIS performance optimization
-- Run this migration to add missing GIST indexes on geometry columns

-- Index for utility line geometry (primary spatial data)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_utility_line_data_geometry 
ON utility_line_data USING GIST(geometry);

-- Index for project spatial data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_spatial_data_geometry 
ON project_spatial_data USING GIST(geometry);

-- Index for 3D conflict geometry
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conflicts_3d_geometry 
ON conflicts USING GIST(conflict_3d_geometry);

-- Composite index for project-based spatial queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_utility_line_data_project_geometry 
ON utility_line_data(project_id) INCLUDE (geometry);

-- Index for utility type filtering with spatial operations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_utility_line_data_type_geometry 
ON utility_line_data(utility_type) INCLUDE (geometry);

-- Index for installation depth (for vertical conflict detection)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_utility_line_data_depth 
ON utility_line_data(installation_depth) WHERE installation_depth IS NOT NULL;

-- Analyze tables after index creation for query planner optimization
ANALYZE utility_line_data;
ANALYZE project_spatial_data;
ANALYZE conflicts;

-- Grant necessary PostGIS permissions if needed
-- GRANT EXECUTE ON FUNCTION ST_DWithin TO application_user;
-- GRANT EXECUTE ON FUNCTION ST_Intersects TO application_user;
-- GRANT EXECUTE ON FUNCTION ST_Distance TO application_user;