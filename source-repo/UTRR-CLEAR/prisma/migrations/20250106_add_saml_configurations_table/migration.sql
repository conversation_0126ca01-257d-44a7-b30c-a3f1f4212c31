-- CreateTable
CREATE TABLE "public"."saml_configurations" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "name" TEXT NOT NULL,
    "entry_point" TEXT NOT NULL,
    "issuer" TEXT NOT NULL,
    "certificate" TEXT NOT NULL,
    "signature_algorithm" TEXT NOT NULL DEFAULT 'sha256',
    "tenant" TEXT NOT NULL,
    "product" TEXT NOT NULL,
    "metadata_url" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "saml_configurations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "saml_configurations_organization_id_idx" ON "public"."saml_configurations"("organization_id");

-- CreateIndex
CREATE INDEX "saml_configurations_tenant_product_idx" ON "public"."saml_configurations"("tenant", "product");

-- CreateIndex
CREATE UNIQUE INDEX "saml_configurations_organization_id_name_key" ON "public"."saml_configurations"("organization_id", "name");

-- AddForeignKey
ALTER TABLE "public"."saml_configurations" ADD CONSTRAINT "saml_configurations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create update trigger
CREATE TRIGGER update_saml_configurations_updated_at BEFORE UPDATE ON "public"."saml_configurations"
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();