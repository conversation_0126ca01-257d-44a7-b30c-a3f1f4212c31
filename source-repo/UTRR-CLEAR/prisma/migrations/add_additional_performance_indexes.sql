-- Additional performance indexes for slow queries identified in testing
-- Based on testing showing slow queries: timesheet.getWeeklyEntries (700ms) and projects.getAll (510ms-2911ms)

-- Timesheet performance indexes
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_user_date ON timesheet_entries(user_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_project_date ON timesheet_entries(project_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_week_range ON timesheet_entries(date) WHERE date >= CURRENT_DATE - INTERVAL '7 days';

-- Projects query optimization indexes
CREATE INDEX IF NOT EXISTS idx_projects_updated_at_desc ON projects(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_search_fields ON projects(name, client, id) WHERE name IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_active_filter ON projects(rag_status, updated_at DESC) WHERE rag_status IN ('Red', 'Amber', 'Green');

-- Activities table indexes for project details
CREATE INDEX IF NOT EXISTS idx_activities_project_timestamp ON activities(project_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_activities_user_timestamp ON activities(user_id, timestamp DESC);

-- Tasks table indexes for project queries
CREATE INDEX IF NOT EXISTS idx_tasks_project_completed ON tasks(project_id, completed, due_date) WHERE completed = false;
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date ASC) WHERE completed = false;

-- Issues table indexes
CREATE INDEX IF NOT EXISTS idx_issues_project_status ON issues(project_id, status, reported_date DESC) WHERE status = 'Open';

-- Meetings table indexes
CREATE INDEX IF NOT EXISTS idx_meetings_project_upcoming ON meetings(project_id, start_time ASC) WHERE start_time >= CURRENT_TIMESTAMP;

-- Communication logs indexes
CREATE INDEX IF NOT EXISTS idx_communication_logs_project ON communication_logs(project_id, timestamp DESC);

-- Conflicts table indexes for utilities relation
CREATE INDEX IF NOT EXISTS idx_conflicts_utility_status ON conflicts(utility_id, status) WHERE status = 'open';
CREATE INDEX IF NOT EXISTS idx_conflicts_utility2_status ON conflicts(utility2_id, status) WHERE status = 'open';

-- Utilities table optimization for project relations
CREATE INDEX IF NOT EXISTS idx_utilities_project_created ON utilities(project_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_utilities_type_status ON utilities(type, status) WHERE status = 'active';

-- Project documents for count queries
CREATE INDEX IF NOT EXISTS idx_project_documents_project ON project_documents(project_id);

-- Sub invoices for project relations
CREATE INDEX IF NOT EXISTS idx_sub_invoices_project ON sub_invoices(project_id);

-- Composite index for getAll query filters
CREATE INDEX IF NOT EXISTS idx_projects_list_query ON projects(rag_status, client, updated_at DESC) 
  WHERE rag_status IS NOT NULL AND client IS NOT NULL;

-- Analyze tables after adding indexes
ANALYZE projects;
ANALYZE utilities;
ANALYZE timesheet_entries;
ANALYZE activities;
ANALYZE tasks;
ANALYZE issues;
ANALYZE meetings;
ANALYZE communication_logs;
ANALYZE conflicts;
ANALYZE project_documents;
ANALYZE sub_invoices;