-- Migration to add Gantt chart fields to tasks table
-- This enhances the existing tasks table with fields needed for full project management

-- Add new columns to tasks table
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "monday_id" VARCHAR;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "status" VARCHAR DEFAULT 'Not Started';
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "start_date" DATE;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "end_date" DATE;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "estimated_hours" DECIMAL(8,2);
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "actual_hours" DECIMAL(8,2);
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "progress_percentage" INTEGER DEFAULT 0;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "assigned_to" VARCHAR;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "dependencies" JSONB DEFAULT '[]';
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "predecessors" VARCHAR[] DEFAULT '{}';
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "successors" VARCHAR[] DEFAULT '{}';
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "task_type" VARCHAR DEFAULT 'Task';
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "milestone" BOOLEAN DEFAULT false;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "critical_path" BOOLEAN DEFAULT false;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "baseline_start" DATE;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "baseline_end" DATE;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "variance_days" INTEGER DEFAULT 0;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "phase" VARCHAR;
ALTER TABLE "public"."tasks" ADD COLUMN IF NOT EXISTS "board_id" VARCHAR;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS "idx_tasks_project_id" ON "public"."tasks"("project_id");
CREATE INDEX IF NOT EXISTS "idx_tasks_monday_id" ON "public"."tasks"("monday_id");
CREATE INDEX IF NOT EXISTS "idx_tasks_status" ON "public"."tasks"("status");
CREATE INDEX IF NOT EXISTS "idx_tasks_start_date" ON "public"."tasks"("start_date");
CREATE INDEX IF NOT EXISTS "idx_tasks_critical_path" ON "public"."tasks"("critical_path");
CREATE INDEX IF NOT EXISTS "idx_tasks_board_id" ON "public"."tasks"("board_id");

-- Add constraint to ensure valid progress percentage
ALTER TABLE "public"."tasks" ADD CONSTRAINT IF NOT EXISTS "chk_progress_percentage" 
  CHECK ("progress_percentage" >= 0 AND "progress_percentage" <= 100);

-- Comment the table and key columns
COMMENT ON TABLE "public"."tasks" IS 'Enhanced tasks table supporting Gantt chart visualization and project management features synced from Monday.com';
COMMENT ON COLUMN "public"."tasks"."monday_id" IS 'Monday.com task item ID for synchronization';
COMMENT ON COLUMN "public"."tasks"."dependencies" IS 'JSON array of task dependencies from Monday.com';
COMMENT ON COLUMN "public"."tasks"."predecessors" IS 'Array of task IDs that must complete before this task';
COMMENT ON COLUMN "public"."tasks"."successors" IS 'Array of task IDs that depend on this task';
COMMENT ON COLUMN "public"."tasks"."critical_path" IS 'Whether this task is on the critical path';
COMMENT ON COLUMN "public"."tasks"."variance_days" IS 'Number of days ahead/behind schedule (negative = ahead)';
COMMENT ON COLUMN "public"."tasks"."board_id" IS 'Monday.com board ID containing this task';