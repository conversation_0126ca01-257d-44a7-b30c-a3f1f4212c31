-- CreateDefaultOrganization
DO $$
BEGIN
    -- Check if any organization exists
    IF NOT EXISTS (SELECT 1 FROM public.organizations LIMIT 1) THEN
        -- Create default organization
        INSERT INTO public.organizations (
            id,
            name,
            primary_color,
            secondary_color,
            timezone,
            date_format,
            currency,
            fiscal_year_start,
            setup_completed,
            setup_completed_at,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            'Default Organization',
            '#3B82F6',
            '#10B981',
            'America/New_York',
            'MM/DD/YYYY',
            'USD',
            1,
            true,
            NOW(),
            NOW(),
            NOW()
        );
    END IF;

    -- Update all users without an organization to use the default organization
    UPDATE auth.users
    SET organization_id = (SELECT id FROM public.organizations LIMIT 1)
    WHERE organization_id IS NULL;
END $$;

-- <PERSON>reate function to auto-assign new users to default organization
CREATE OR REPLACE FUNCTION assign_default_organization()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.organization_id IS NULL THEN
        NEW.organization_id := (SELECT id FROM public.organizations LIMIT 1);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-assign organization on user creation
DROP TRIGGER IF EXISTS auto_assign_organization ON auth.users;
CREATE TRIGGER auto_assign_organization
    BEFORE INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION assign_default_organization();