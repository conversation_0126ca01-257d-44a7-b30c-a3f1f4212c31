-- Migration: Advanced PostGIS Materialized Views and Spatial Clustering
-- Purpose: Create materialized views for complex spatial queries and spatial clustering functions
-- Phase: 3 - Advanced Optimization

-- ==============================================================================
-- MATERIALIZED VIEWS FOR SPATIAL QUERIES
-- ==============================================================================

-- 1. Spatial utility summary by project (refreshed every 30 minutes)
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_project_spatial_summary AS
SELECT 
    p.id as project_id,
    p.name as project_name,
    COUNT(u.id) as total_utilities,
    COUNT(CASE WHEN u.type = 'Water' THEN 1 END) as water_utilities,
    COUNT(CASE WHEN u.type = 'Gas' THEN 1 END) as gas_utilities,
    COUNT(CASE WHEN u.type = 'Electric' THEN 1 END) as electric_utilities,
    COUNT(CASE WHEN u.type = 'Sewer' THEN 1 END) as sewer_utilities,
    COUNT(CASE WHEN u.type = 'Telecom' THEN 1 END) as telecom_utilities,
    ST_Extent(u.geometry) as project_bounds,
    ST_Area(ST_ConvexHull(ST_Collect(u.geometry))) as total_coverage_area,
    AVG(ST_Length(u.geometry)) as avg_utility_length,
    MAX(ST_Length(u.geometry)) as max_utility_length,
    NOW() as last_refreshed
FROM "Project" p
LEFT JOIN "ProjectUtility" pu ON p.id = pu."projectId"
LEFT JOIN "Utility" u ON pu."utilityId" = u.id
WHERE u.geometry IS NOT NULL
GROUP BY p.id, p.name;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_project_spatial_summary_project_id 
ON mv_project_spatial_summary (project_id);

CREATE INDEX IF NOT EXISTS idx_mv_project_spatial_summary_last_refreshed 
ON mv_project_spatial_summary (last_refreshed);

-- 2. Spatial conflict hotspots (areas with highest conflict density)
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_spatial_conflict_hotspots AS
WITH conflict_grid AS (
    SELECT 
        ST_SnapToGrid(ST_Centroid(u1.geometry), 100) as grid_cell, -- 100m grid
        COUNT(*) as conflict_count,
        AVG(ST_Distance(u1.geometry, u2.geometry)) as avg_distance,
        MIN(ST_Distance(u1.geometry, u2.geometry)) as min_distance,
        array_agg(DISTINCT u1.type) as utility_types_1,
        array_agg(DISTINCT u2.type) as utility_types_2
    FROM "Utility" u1
    CROSS JOIN "Utility" u2
    WHERE u1.id != u2.id
        AND ST_DWithin(u1.geometry, u2.geometry, 10) -- Within 10 meters
        AND u1.geometry IS NOT NULL 
        AND u2.geometry IS NOT NULL
    GROUP BY ST_SnapToGrid(ST_Centroid(u1.geometry), 100)
    HAVING COUNT(*) > 2 -- Only cells with multiple conflicts
)
SELECT 
    ST_AsText(grid_cell) as grid_center,
    ST_Buffer(grid_cell, 50) as hotspot_area, -- 50m buffer around grid center
    conflict_count,
    avg_distance,
    min_distance,
    utility_types_1,
    utility_types_2,
    CASE 
        WHEN conflict_count > 20 THEN 'critical'
        WHEN conflict_count > 10 THEN 'high'
        WHEN conflict_count > 5 THEN 'medium'
        ELSE 'low'
    END as severity_level,
    NOW() as last_refreshed
FROM conflict_grid
ORDER BY conflict_count DESC;

-- Create spatial index on hotspot areas
CREATE INDEX IF NOT EXISTS idx_mv_spatial_conflict_hotspots_area 
ON mv_spatial_conflict_hotspots USING GIST (hotspot_area);

-- 3. Utility density clusters for performance optimization
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_utility_density_clusters AS
WITH utility_points AS (
    SELECT 
        id,
        type,
        ST_Centroid(geometry) as center_point,
        ST_Length(geometry) as length_meters,
        "organizationId"
    FROM "Utility" 
    WHERE geometry IS NOT NULL
),
clustered_utilities AS (
    SELECT 
        id,
        type,
        center_point,
        length_meters,
        "organizationId",
        ST_ClusterDBSCAN(center_point, 500, 3) OVER () as cluster_id -- 500m radius, min 3 points
    FROM utility_points
)
SELECT 
    cluster_id,
    COUNT(*) as utility_count,
    array_agg(DISTINCT type) as utility_types,
    ST_ConvexHull(ST_Collect(center_point)) as cluster_boundary,
    ST_Centroid(ST_Collect(center_point)) as cluster_center,
    AVG(length_meters) as avg_utility_length,
    SUM(length_meters) as total_length,
    array_agg(DISTINCT "organizationId") as organizations,
    NOW() as last_refreshed
FROM clustered_utilities
WHERE cluster_id IS NOT NULL
GROUP BY cluster_id
HAVING COUNT(*) >= 3; -- Only clusters with 3+ utilities

-- Create spatial indexes
CREATE INDEX IF NOT EXISTS idx_mv_utility_density_clusters_boundary 
ON mv_utility_density_clusters USING GIST (cluster_boundary);

CREATE INDEX IF NOT EXISTS idx_mv_utility_density_clusters_center 
ON mv_utility_density_clusters USING GIST (cluster_center);

-- ==============================================================================
-- SPATIAL CLUSTERING FUNCTIONS
-- ==============================================================================

-- 1. Function to find optimal spatial clusters for a project
CREATE OR REPLACE FUNCTION find_optimal_spatial_clusters(
    project_id_param UUID,
    cluster_radius_meters DECIMAL DEFAULT 500,
    min_cluster_size INTEGER DEFAULT 3
) RETURNS TABLE (
    cluster_id INTEGER,
    utility_count BIGINT,
    utility_types TEXT[],
    cluster_center GEOMETRY,
    cluster_boundary GEOMETRY,
    avg_utility_length DECIMAL,
    total_coverage_area DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH project_utilities AS (
        SELECT 
            u.id,
            u.type,
            u.geometry,
            ST_Centroid(u.geometry) as center_point,
            ST_Length(u.geometry) as length_meters
        FROM "Utility" u
        JOIN "ProjectUtility" pu ON u.id = pu."utilityId"
        WHERE pu."projectId" = project_id_param
            AND u.geometry IS NOT NULL
    ),
    clustered_utilities AS (
        SELECT 
            *,
            ST_ClusterDBSCAN(center_point, cluster_radius_meters, min_cluster_size) OVER () as cluster_id
        FROM project_utilities
    )
    SELECT 
        cu.cluster_id::INTEGER,
        COUNT(*)::BIGINT as utility_count,
        array_agg(DISTINCT cu.type) as utility_types,
        ST_Centroid(ST_Collect(cu.center_point)) as cluster_center,
        ST_ConvexHull(ST_Collect(cu.geometry)) as cluster_boundary,
        AVG(cu.length_meters) as avg_utility_length,
        ST_Area(ST_ConvexHull(ST_Collect(cu.geometry))) as total_coverage_area
    FROM clustered_utilities cu
    WHERE cu.cluster_id IS NOT NULL
    GROUP BY cu.cluster_id
    ORDER BY utility_count DESC;
END;
$$ LANGUAGE plpgsql STABLE;

-- 2. Function to optimize spatial queries based on clustering
CREATE OR REPLACE FUNCTION optimize_spatial_query_plan(
    project_id_param UUID,
    operation_type TEXT DEFAULT 'conflict_detection'
) RETURNS TABLE (
    optimization_strategy TEXT,
    estimated_performance_gain DECIMAL,
    recommended_cluster_radius DECIMAL,
    recommended_batch_size INTEGER,
    use_materialized_view BOOLEAN
) AS $$
DECLARE
    utility_count INTEGER;
    density_score DECIMAL;
    complexity_score DECIMAL;
BEGIN
    -- Calculate project complexity metrics
    SELECT COUNT(*), 
           COALESCE(ST_Area(ST_ConvexHull(ST_Collect(u.geometry))) / COUNT(*), 0)
    INTO utility_count, density_score
    FROM "Utility" u
    JOIN "ProjectUtility" pu ON u.id = pu."utilityId"
    WHERE pu."projectId" = project_id_param
        AND u.geometry IS NOT NULL;
    
    complexity_score := utility_count * (1 + LOG(GREATEST(density_score, 1)));
    
    RETURN QUERY
    SELECT 
        CASE 
            WHEN utility_count > 1000 THEN 'cluster_and_batch'
            WHEN utility_count > 500 THEN 'spatial_clustering'
            WHEN utility_count > 100 THEN 'materialized_view'
            ELSE 'direct_query'
        END as optimization_strategy,
        CASE 
            WHEN utility_count > 1000 THEN 0.70 -- 70% performance gain
            WHEN utility_count > 500 THEN 0.50  -- 50% performance gain
            WHEN utility_count > 100 THEN 0.30  -- 30% performance gain
            ELSE 0.10                           -- 10% performance gain
        END as estimated_performance_gain,
        CASE 
            WHEN density_score > 10000 THEN 200.0  -- Dense areas need smaller clusters
            WHEN density_score > 1000 THEN 500.0   -- Medium density
            ELSE 1000.0                             -- Sparse areas can use larger clusters
        END as recommended_cluster_radius,
        CASE 
            WHEN utility_count > 1000 THEN 50
            WHEN utility_count > 500 THEN 100
            ELSE 200
        END as recommended_batch_size,
        utility_count > 100 as use_materialized_view;
END;
$$ LANGUAGE plpgsql STABLE;

-- 3. Function for intelligent spatial indexing based on usage patterns
CREATE OR REPLACE FUNCTION create_adaptive_spatial_indexes(
    table_name TEXT,
    geometry_column TEXT DEFAULT 'geometry',
    analysis_period_days INTEGER DEFAULT 30
) RETURNS TABLE (
    index_name TEXT,
    index_type TEXT,
    estimated_selectivity DECIMAL,
    creation_sql TEXT
) AS $$
DECLARE
    query_pattern_analysis TEXT;
    table_size BIGINT;
    geometry_type TEXT;
BEGIN
    -- Analyze table size and geometry types
    EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO table_size;
    
    EXECUTE format('SELECT ST_GeometryType(%I) FROM %I LIMIT 1', geometry_column, table_name) 
    INTO geometry_type;
    
    -- Return recommended indexes based on table characteristics
    RETURN QUERY
    SELECT 
        format('idx_%s_%s_gist', table_name, geometry_column) as index_name,
        'GIST' as index_type,
        CASE 
            WHEN table_size > 100000 THEN 0.95
            WHEN table_size > 10000 THEN 0.85
            ELSE 0.70
        END as estimated_selectivity,
        format('CREATE INDEX CONCURRENTLY %s ON %I USING GIST (%I)', 
               format('idx_%s_%s_gist', table_name, geometry_column),
               table_name, 
               geometry_column) as creation_sql
    
    UNION ALL
    
    -- Additional SPGIST index for point geometries
    SELECT 
        format('idx_%s_%s_spgist', table_name, geometry_column) as index_name,
        'SP-GIST' as index_type,
        0.80 as estimated_selectivity,
        format('CREATE INDEX CONCURRENTLY %s ON %I USING SPGIST (%I)', 
               format('idx_%s_%s_spgist', table_name, geometry_column),
               table_name, 
               geometry_column) as creation_sql
    WHERE geometry_type LIKE '%Point%';
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- MATERIALIZED VIEW REFRESH FUNCTIONS
-- ==============================================================================

-- Function to refresh all spatial materialized views
CREATE OR REPLACE FUNCTION refresh_spatial_materialized_views() RETURNS TEXT AS $$
DECLARE
    refresh_results TEXT := '';
    start_time TIMESTAMP;
    end_time TIMESTAMP;
BEGIN
    start_time := NOW();
    
    -- Refresh project spatial summary
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_project_spatial_summary;
    refresh_results := refresh_results || 'mv_project_spatial_summary refreshed. ';
    
    -- Refresh conflict hotspots
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_spatial_conflict_hotspots;
    refresh_results := refresh_results || 'mv_spatial_conflict_hotspots refreshed. ';
    
    -- Refresh utility density clusters
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_utility_density_clusters;
    refresh_results := refresh_results || 'mv_utility_density_clusters refreshed. ';
    
    end_time := NOW();
    
    refresh_results := refresh_results || format('Total refresh time: %s seconds.', 
                                                EXTRACT(EPOCH FROM (end_time - start_time)));
    
    RETURN refresh_results;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- PERFORMANCE MONITORING VIEWS
-- ==============================================================================

-- View to monitor materialized view performance and usage
CREATE OR REPLACE VIEW v_materialized_view_stats AS
SELECT 
    schemaname,
    matviewname,
    matviewowner,
    tablespace,
    hasindexes,
    ispopulated,
    definition,
    pg_size_pretty(pg_total_relation_size(oid)) as size,
    (SELECT last_refreshed FROM mv_project_spatial_summary LIMIT 1) as last_refresh_project_summary,
    (SELECT last_refreshed FROM mv_spatial_conflict_hotspots LIMIT 1) as last_refresh_conflict_hotspots,
    (SELECT last_refreshed FROM mv_utility_density_clusters LIMIT 1) as last_refresh_density_clusters
FROM pg_matviews 
WHERE matviewname LIKE 'mv_%';

-- ==============================================================================
-- GRANT PERMISSIONS
-- ==============================================================================

-- Grant SELECT permissions on materialized views
GRANT SELECT ON mv_project_spatial_summary TO PUBLIC;
GRANT SELECT ON mv_spatial_conflict_hotspots TO PUBLIC;
GRANT SELECT ON mv_utility_density_clusters TO PUBLIC;
GRANT SELECT ON v_materialized_view_stats TO PUBLIC;

-- Grant EXECUTE permissions on functions
GRANT EXECUTE ON FUNCTION find_optimal_spatial_clusters TO PUBLIC;
GRANT EXECUTE ON FUNCTION optimize_spatial_query_plan TO PUBLIC;
GRANT EXECUTE ON FUNCTION create_adaptive_spatial_indexes TO PUBLIC;
GRANT EXECUTE ON FUNCTION refresh_spatial_materialized_views TO PUBLIC;

-- ==============================================================================
-- INITIAL DATA REFRESH
-- ==============================================================================

-- Populate materialized views with initial data
SELECT refresh_spatial_materialized_views();

-- Create a scheduled job to refresh materialized views every 30 minutes
-- Note: This would typically be done via pg_cron or external scheduler
-- INSERT INTO pg_cron.job (jobname, schedule, command)
-- VALUES ('refresh_spatial_views', '*/30 * * * *', 'SELECT refresh_spatial_materialized_views();');

-- ==============================================================================
-- MIGRATION COMPLETE
-- ==============================================================================

-- Log completion
SELECT 'Materialized views and spatial clustering functions created successfully' as migration_status;