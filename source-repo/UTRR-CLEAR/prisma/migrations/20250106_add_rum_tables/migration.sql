-- Real User Monitoring (RUM) Tables

-- Sessions table - tracks user sessions
CREATE TABLE "public"."rum_sessions" (
    "id" SERIAL PRIMARY KEY,
    "session_id" TEXT NOT NULL UNIQUE,
    "user_id" TEXT,
    "start_time" TIMESTAMP(6) NOT NULL,
    "last_activity" TIMESTAMP(6) NOT NULL,
    "total_duration" INTEGER NOT NULL DEFAULT 0,
    "device_type" TEXT NOT NULL,
    "browser" TEXT NOT NULL,
    "os" TEXT NOT NULL,
    "screen_size" TEXT NOT NULL,
    "connection_type" TEXT,
    "client_ip" TEXT,
    "user_agent" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Events table - stores all RUM events
CREATE TABLE "public"."rum_events" (
    "id" SERIAL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "user_id" TEXT,
    "event_type" TEXT NOT NULL,
    "page" TEXT NOT NULL,
    "timestamp" TIMESTAMP(6) NOT NULL,
    "event_data" JSONB,
    "client_ip" TEXT,
    "user_agent" TEXT,
    "device_type" TEXT,
    "browser" TEXT,
    "os" TEXT,
    "screen_size" TEXT,
    "connection_type" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Performance metrics table - Core Web Vitals and other performance data
CREATE TABLE "public"."rum_performance_metrics" (
    "id" SERIAL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "page" TEXT NOT NULL,
    "fcp" DECIMAL(10,2), -- First Contentful Paint
    "lcp" DECIMAL(10,2), -- Largest Contentful Paint
    "fid" DECIMAL(10,2), -- First Input Delay
    "cls" DECIMAL(10,4), -- Cumulative Layout Shift
    "ttfb" DECIMAL(10,2), -- Time to First Byte
    "dom_content_loaded" DECIMAL(10,2),
    "window_load" DECIMAL(10,2),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(session_id, page)
);

-- Errors table - JavaScript errors and exceptions
CREATE TABLE "public"."rum_errors" (
    "id" SERIAL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "user_id" TEXT,
    "page" TEXT NOT NULL,
    "error_message" TEXT NOT NULL,
    "error_stack" TEXT,
    "filename" TEXT,
    "line_number" INTEGER,
    "column_number" INTEGER,
    "user_agent" TEXT,
    "timestamp" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User actions table - clicks, form submissions, navigation
CREATE TABLE "public"."rum_user_actions" (
    "id" SERIAL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "user_id" TEXT,
    "page" TEXT NOT NULL,
    "action_type" TEXT NOT NULL,
    "action_data" JSONB,
    "timestamp" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Page views table - navigation and page loads
CREATE TABLE "public"."rum_page_views" (
    "id" SERIAL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "user_id" TEXT,
    "page" TEXT NOT NULL,
    "page_title" TEXT,
    "referrer" TEXT,
    "timestamp" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Daily metrics table - aggregated data for reporting
CREATE TABLE "public"."rum_daily_metrics" (
    "id" SERIAL PRIMARY KEY,
    "date" DATE NOT NULL,
    "device_type" TEXT NOT NULL,
    "page_views" INTEGER NOT NULL DEFAULT 0,
    "unique_sessions" INTEGER NOT NULL DEFAULT 0,
    "user_actions" INTEGER NOT NULL DEFAULT 0,
    "errors" INTEGER NOT NULL DEFAULT 0,
    "avg_fcp" DECIMAL(10,2),
    "avg_lcp" DECIMAL(10,2),
    "avg_fid" DECIMAL(10,2),
    "avg_cls" DECIMAL(10,4),
    "avg_ttfb" DECIMAL(10,2),
    "bounce_rate" DECIMAL(5,2),
    "avg_session_duration" DECIMAL(10,2),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date, device_type)
);

-- Indexes for performance
CREATE INDEX "rum_sessions_session_id_idx" ON "public"."rum_sessions"("session_id");
CREATE INDEX "rum_sessions_user_id_idx" ON "public"."rum_sessions"("user_id");
CREATE INDEX "rum_sessions_start_time_idx" ON "public"."rum_sessions"("start_time");

CREATE INDEX "rum_events_session_id_idx" ON "public"."rum_events"("session_id");
CREATE INDEX "rum_events_user_id_idx" ON "public"."rum_events"("user_id");
CREATE INDEX "rum_events_event_type_idx" ON "public"."rum_events"("event_type");
CREATE INDEX "rum_events_page_idx" ON "public"."rum_events"("page");
CREATE INDEX "rum_events_timestamp_idx" ON "public"."rum_events"("timestamp");

CREATE INDEX "rum_performance_metrics_session_id_idx" ON "public"."rum_performance_metrics"("session_id");
CREATE INDEX "rum_performance_metrics_page_idx" ON "public"."rum_performance_metrics"("page");

CREATE INDEX "rum_errors_session_id_idx" ON "public"."rum_errors"("session_id");
CREATE INDEX "rum_errors_page_idx" ON "public"."rum_errors"("page");
CREATE INDEX "rum_errors_timestamp_idx" ON "public"."rum_errors"("timestamp");

CREATE INDEX "rum_user_actions_session_id_idx" ON "public"."rum_user_actions"("session_id");
CREATE INDEX "rum_user_actions_action_type_idx" ON "public"."rum_user_actions"("action_type");
CREATE INDEX "rum_user_actions_timestamp_idx" ON "public"."rum_user_actions"("timestamp");

CREATE INDEX "rum_page_views_session_id_idx" ON "public"."rum_page_views"("session_id");
CREATE INDEX "rum_page_views_page_idx" ON "public"."rum_page_views"("page");
CREATE INDEX "rum_page_views_timestamp_idx" ON "public"."rum_page_views"("timestamp");

CREATE INDEX "rum_daily_metrics_date_idx" ON "public"."rum_daily_metrics"("date");
CREATE INDEX "rum_daily_metrics_device_type_idx" ON "public"."rum_daily_metrics"("device_type");

-- Update triggers
CREATE TRIGGER update_rum_sessions_updated_at BEFORE UPDATE ON "public"."rum_sessions"
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rum_performance_metrics_updated_at BEFORE UPDATE ON "public"."rum_performance_metrics"
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rum_daily_metrics_updated_at BEFORE UPDATE ON "public"."rum_daily_metrics"
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();