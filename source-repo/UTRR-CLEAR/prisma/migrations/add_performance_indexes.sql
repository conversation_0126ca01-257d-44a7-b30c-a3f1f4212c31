-- Performance indexes for commonly queried columns

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_client ON projects(client);
CREATE INDEX IF NOT EXISTS idx_projects_manager_id ON projects(manager_id);
CREATE INDEX IF NOT EXISTS idx_projects_current_phase ON projects(current_phase);
CREATE INDEX IF NOT EXISTS idx_projects_rag_status ON projects(rag_status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_start_end_dates ON projects(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_projects_monday_id ON projects(monday_id) WHERE monday_id IS NOT NULL;

-- Users indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);

-- Utilities indexes
CREATE INDEX IF NOT EXISTS idx_utilities_type ON utilities(type);
CREATE INDEX IF NOT EXISTS idx_utilities_project_id ON utilities(project_id);

-- Conflicts indexes
CREATE INDEX IF NOT EXISTS idx_conflicts_project_id ON conflicts(project_id);
CREATE INDEX IF NOT EXISTS idx_conflicts_severity ON conflicts(severity);
CREATE INDEX IF NOT EXISTS idx_conflicts_status ON conflicts(status);
CREATE INDEX IF NOT EXISTS idx_conflicts_created_at ON conflicts(created_at DESC);

-- Stakeholders indexes
CREATE INDEX IF NOT EXISTS idx_stakeholders_project_id ON stakeholders(project_id);
CREATE INDEX IF NOT EXISTS idx_stakeholders_is_primary ON stakeholders(is_primary);
CREATE INDEX IF NOT EXISTS idx_stakeholders_company ON stakeholders(company);

-- Documents indexes
CREATE INDEX IF NOT EXISTS idx_documents_project_id ON documents(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(type);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by);

-- Communications indexes
CREATE INDEX IF NOT EXISTS idx_communications_project_id ON communications(project_id);
CREATE INDEX IF NOT EXISTS idx_communications_type ON communications(type);
CREATE INDEX IF NOT EXISTS idx_communications_created_at ON communications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_communications_created_by ON communications(created_by);

-- Project utilities composite indexes
CREATE INDEX IF NOT EXISTS idx_project_utilities_project_id ON project_utilities(project_id);
CREATE INDEX IF NOT EXISTS idx_project_utilities_utility_id ON project_utilities(utility_id);
CREATE INDEX IF NOT EXISTS idx_project_utilities_composite ON project_utilities(project_id, utility_id);

-- Utility lines indexes (spatial)
CREATE INDEX IF NOT EXISTS idx_utility_lines_project_id ON utility_lines(project_id);
CREATE INDEX IF NOT EXISTS idx_utility_lines_utility_type ON utility_lines(utility_type);
CREATE INDEX IF NOT EXISTS idx_utility_lines_status ON utility_lines(status);
-- Spatial index for geometry column
CREATE INDEX IF NOT EXISTS idx_utility_lines_geometry ON utility_lines USING GIST(geometry);

-- Activity logs indexes
CREATE INDEX IF NOT EXISTS idx_activity_logs_project_id ON activity_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_entity_type_id ON activity_logs(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at DESC);

-- Auth schema indexes
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON auth.sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON auth.sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON auth.password_reset_tokens(expires_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_projects_active_status ON projects(rag_status, current_phase) WHERE rag_status IN ('Red', 'Amber');
CREATE INDEX IF NOT EXISTS idx_conflicts_unresolved ON conflicts(project_id, severity) WHERE status != 'resolved';
CREATE INDEX IF NOT EXISTS idx_documents_recent ON documents(project_id, created_at DESC) WHERE created_at > CURRENT_DATE - INTERVAL '30 days';

-- Text search indexes
CREATE INDEX IF NOT EXISTS idx_projects_name_search ON projects USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_projects_description_search ON projects USING gin(to_tsvector('english', description));
CREATE INDEX IF NOT EXISTS idx_stakeholders_name_search ON stakeholders USING gin(to_tsvector('english', name));

-- Analyze tables to update statistics
ANALYZE projects;
ANALYZE users;
ANALYZE utilities;
ANALYZE conflicts;
ANALYZE stakeholders;
ANALYZE documents;
ANALYZE communications;
ANALYZE utility_lines;
ANALYZE activity_logs;