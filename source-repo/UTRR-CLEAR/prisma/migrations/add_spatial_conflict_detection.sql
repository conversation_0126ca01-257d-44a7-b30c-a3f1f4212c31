-- Enhanced PostGIS conflict detection function for optimal performance
-- This function replaces client-side O(n²) Turf.js operations with efficient spatial index queries

CREATE OR REPLACE FUNCTION detect_utility_conflicts_optimized(
  p_project_id UUID,
  p_threshold_meters DECIMAL DEFAULT 3.0,
  p_detect_crossings BOOLEAN DEFAULT TRUE,
  p_detect_proximity BOOLEAN DEFAULT TRUE,
  p_include_proposed BOOLEAN DEFAULT FALSE
) RETURNS TABLE (
  utility1_id TEXT,
  utility2_id TEXT,
  utility1_name TEXT,
  utility2_name TEXT,
  utility1_type TEXT,
  utility2_type TEXT,
  conflict_type TEXT,
  distance_meters DECIMAL,
  distance_3d_meters DECIMAL,
  intersection_point GEOMETRY,
  confidence_score INTEGER,
  priority TEXT,
  description TEXT
) AS $$
BEGIN
  RETURN QUERY
  WITH spatial_conflicts AS (
    -- Get all utility lines for the project with their geometries
    SELECT 
      u1.id as u1_id,
      u2.id as u2_id,
      u1.name as u1_name,
      u2.name as u2_name,
      u1.utility_type as u1_type,
      u2.utility_type as u2_type,
      u1.geometry as u1_geom,
      u2.geometry as u2_geom,
      -- Extract depth from properties JSON if available
      COALESCE(
        (u1.properties->>'depth')::DECIMAL, 
        (u1.properties->>'installation_depth')::DECIMAL, 
        0
      ) as u1_depth,
      COALESCE(
        (u2.properties->>'depth')::DECIMAL, 
        (u2.properties->>'installation_depth')::DECIMAL, 
        0
      ) as u2_depth
    FROM utility_line_data u1
    CROSS JOIN utility_line_data u2
    WHERE u1.project_id = p_project_id
      AND u2.project_id = p_project_id
      AND u1.id < u2.id  -- Avoid duplicate pairs and self-comparison
      AND u1.geometry IS NOT NULL
      AND u2.geometry IS NOT NULL
      -- Filter by installation type if not including proposed
      AND (p_include_proposed OR (
        COALESCE(u1.installation_type, 'existing') != 'proposed' 
        AND COALESCE(u2.installation_type, 'existing') != 'proposed'
      ))
      -- Use spatial index for efficient filtering
      AND ST_DWithin(u1.geometry, u2.geometry, p_threshold_meters)
  ),
  crossing_conflicts AS (
    -- Detect crossing conflicts using PostGIS spatial functions
    SELECT 
      sc.u1_id,
      sc.u2_id,
      sc.u1_name,
      sc.u2_name,
      sc.u1_type,
      sc.u2_type,
      'crossing' as conflict_type,
      ST_Distance(sc.u1_geom, sc.u2_geom) as distance_m,
      -- Calculate 3D distance considering depth
      SQRT(
        POWER(ST_Distance(sc.u1_geom, sc.u2_geom), 2) + 
        POWER(ABS(sc.u1_depth - sc.u2_depth), 2)
      ) as distance_3d_m,
      ST_Intersection(sc.u1_geom, sc.u2_geom) as intersection_pt,
      -- Higher confidence for actual intersections
      CASE 
        WHEN ST_Intersects(sc.u1_geom, sc.u2_geom) THEN 95
        WHEN ST_Distance(sc.u1_geom, sc.u2_geom) < 0.5 THEN 85
        ELSE 75
      END as confidence,
      -- Calculate priority based on utility types
      CASE 
        WHEN (sc.u1_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%']) 
              AND sc.u2_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%']))
        THEN 'critical'
        WHEN (sc.u1_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%'])
              OR sc.u2_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%']))
        THEN 'high'
        ELSE 'medium'
      END as priority_level
    FROM spatial_conflicts sc
    WHERE p_detect_crossings
      AND (
        ST_Intersects(sc.u1_geom, sc.u2_geom)
        OR ST_Distance(sc.u1_geom, sc.u2_geom) < 1.0  -- Very close utilities
      )
  ),
  proximity_conflicts AS (
    -- Detect proximity conflicts
    SELECT 
      sc.u1_id,
      sc.u2_id,
      sc.u1_name,
      sc.u2_name,
      sc.u1_type,
      sc.u2_type,
      'proximity' as conflict_type,
      ST_Distance(sc.u1_geom, sc.u2_geom) as distance_m,
      -- Calculate 3D distance considering depth
      SQRT(
        POWER(ST_Distance(sc.u1_geom, sc.u2_geom), 2) + 
        POWER(ABS(sc.u1_depth - sc.u2_depth), 2)
      ) as distance_3d_m,
      ST_ClosestPoint(sc.u1_geom, sc.u2_geom) as intersection_pt,
      -- Confidence based on proximity to threshold
      CASE 
        WHEN ST_Distance(sc.u1_geom, sc.u2_geom) < p_threshold_meters * 0.5 THEN 90
        WHEN ST_Distance(sc.u1_geom, sc.u2_geom) < p_threshold_meters * 0.7 THEN 80
        WHEN ST_Distance(sc.u1_geom, sc.u2_geom) < p_threshold_meters * 0.9 THEN 70
        ELSE 60
      END as confidence,
      -- Calculate priority based on utility types and distance
      CASE 
        WHEN (sc.u1_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%']) 
              AND sc.u2_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%'])
              AND ST_Distance(sc.u1_geom, sc.u2_geom) < p_threshold_meters * 0.5)
        THEN 'critical'
        WHEN (sc.u1_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%'])
              OR sc.u2_type ILIKE ANY(ARRAY['%gas%', '%electric%', '%power%', '%transmission%']))
        THEN 'high'
        WHEN ST_Distance(sc.u1_geom, sc.u2_geom) < p_threshold_meters * 0.7
        THEN 'medium'
        ELSE 'low'
      END as priority_level
    FROM spatial_conflicts sc
    WHERE p_detect_proximity
      AND ST_Distance(sc.u1_geom, sc.u2_geom) <= p_threshold_meters
      AND NOT ST_Intersects(sc.u1_geom, sc.u2_geom)  -- Exclude crossings
  ),
  all_conflicts AS (
    SELECT * FROM crossing_conflicts
    UNION ALL
    SELECT * FROM proximity_conflicts
  )
  SELECT 
    ac.u1_id::TEXT,
    ac.u2_id::TEXT,
    ac.u1_name,
    ac.u2_name,
    ac.u1_type,
    ac.u2_type,
    ac.conflict_type,
    ac.distance_m,
    ac.distance_3d_m,
    ac.intersection_pt,
    ac.confidence,
    ac.priority_level,
    format(
      'Potential %s conflict between %s (%s) and %s (%s). Distance: %.2fm',
      ac.conflict_type,
      ac.u1_name,
      ac.u1_type,
      ac.u2_name,
      ac.u2_type,
      ac.distance_m
    ) as description
  FROM all_conflicts ac
  ORDER BY 
    CASE ac.priority_level
      WHEN 'critical' THEN 1
      WHEN 'high' THEN 2
      WHEN 'medium' THEN 3
      ELSE 4
    END,
    ac.distance_m ASC;
END;
$$ LANGUAGE plpgsql;

-- Create a spatial index for geometry if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_utility_line_data_geometry 
ON utility_line_data USING GIST (geometry);

-- Create an index on project_id for efficient filtering
CREATE INDEX IF NOT EXISTS idx_utility_line_data_project_id 
ON utility_line_data (project_id);

-- Create an index on utility type for filtering
CREATE INDEX IF NOT EXISTS idx_utility_line_data_type 
ON utility_line_data (utility_type);

-- Create an index on installation type for filtering
CREATE INDEX IF NOT EXISTS idx_utility_line_data_installation_type 
ON utility_line_data (installation_type);

-- Comment explaining the function
COMMENT ON FUNCTION detect_utility_conflicts_optimized IS 
'Optimized PostGIS-based utility conflict detection function. 
Replaces client-side O(n²) operations with efficient spatial index queries.
Returns conflicts with distance calculations, priority levels, and confidence scores.';