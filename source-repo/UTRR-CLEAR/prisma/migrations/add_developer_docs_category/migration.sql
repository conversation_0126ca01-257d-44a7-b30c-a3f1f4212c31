-- Add Developer Documentation category and myWhispers Security Architecture article

-- Insert Developer Documentation category
INSERT INTO public.knowledge_categories (
    id,
    name,
    description,
    article_count,
    created_at,
    updated_at
) VALUES (
    'developer-documentation',
    'Developer Documentation',
    'Technical documentation, architecture diagrams, and developer resources',
    0,
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Insert myWhispers Security Architecture article
INSERT INTO public.knowledge_articles (
    category_id,
    title,
    content,
    description,
    tags,
    created_by_id,
    created_at,
    last_updated
) VALUES (
    'developer-documentation',
    'myWhispers Security Architecture',
    '# myWhispers Security Architecture

## Overview
This document provides a visual overview of the myWhispers private messaging system''s security architecture, demonstrating how the system maintains enterprise-grade privacy while delivering a modern user experience.

## Security Architecture Diagram

```mermaid
graph TB
    subgraph "Client Browser"
        A[User Interface<br/>shadcn-chat Components] --> B[WhispersChat Component]
        B --> C[localStorage<br/>5min TTL]
        C --> D[Cross-Tab Sync<br/>StorageEvent]
        D --> E[Auto-Cleanup<br/>Expired Messages]
    end
    
    subgraph "Server"
        F[tRPC API] --> G[User Activity Tracking<br/>Online Status Only]
        G --> H[Redis Cache<br/>Activity Data Only]
    end
    
    subgraph "Security Features"
        I["🛡️ Zero Server Storage"]
        J["⏰ 5min Auto-Expire"]
        K["🔒 Client-Side Only"]
        L["🚫 No Message Logging"]
    end
    
    B -.->|"Activity Updates Only<br/>(No Message Content)"| F
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#fff3e0
    style I fill:#e8f5e8
    style J fill:#fff8e1
    style K fill:#fce4ec
    style L fill:#f1f8e9
```

## Key Security Components

### 🔒 **Client-Side Storage**
- **localStorage Only**: Messages never leave the user''s browser
- **5-Minute TTL**: Automatic expiration prevents data accumulation
- **Cross-Tab Sync**: Real-time updates across browser tabs without server communication

### 🛡️ **Zero-Knowledge Server**
- **Activity Tracking Only**: Server tracks user online status, never message content
- **No Message Storage**: No database tables or logs contain private messages
- **Separation of Concerns**: User presence completely isolated from message data

### ⏰ **Ephemeral Messaging**
- **Auto-Expiration**: Messages automatically deleted after 5 minutes
- **Real-Time Countdown**: Users see time remaining for each message
- **Automatic Cleanup**: Background process removes expired messages every 30 seconds

### 🚫 **Privacy by Design**
- **No Server Logs**: Message content never appears in server logs
- **No Backups**: Messages cannot be recovered once expired
- **No Admin Access**: Even administrators cannot view message content

## Technical Implementation

### Message Storage
```typescript
// Client-side only storage with TTL
const MESSAGE_TTL = 5 * 60 * 1000; // 5 minutes
const STORAGE_KEY = ''whispers_messages'';

// Store message locally
localStorage.setItem(STORAGE_KEY, JSON.stringify(messages));
```

### Cross-Tab Synchronization
```typescript
// Sync across browser tabs without server
window.dispatchEvent(new StorageEvent(''storage'', {
  key: STORAGE_KEY,
  newValue: JSON.stringify(messages),
  storageArea: localStorage
}));
```

### Activity Tracking (Server-Side)
```typescript
// Only track user presence, never message content
const updateActivity = api.whispers.updateActivity.useMutation({
  // Updates Redis cache with user online status only
});
```

## Security Benefits

1. **Zero-Knowledge Architecture**: Server never sees message content
2. **Forward Secrecy**: Messages automatically deleted after TTL
3. **No Data Breach Risk**: No server-side message storage to compromise
4. **Regulatory Compliance**: Meets strict privacy requirements
5. **User Control**: Messages exist only on user''s device

## Use Cases

- **Sensitive Communications**: Internal discussions requiring privacy
- **Temporary Coordination**: Quick messages that don''t need persistence
- **Compliance-Sensitive Environments**: Organizations with strict data retention policies
- **Real-Time Collaboration**: Immediate communication without permanent records

## Comparison with Traditional Chat

| Feature | myWhispers | Traditional Chat |
|---------|------------|------------------|
| Server Storage | ❌ None | ✅ Permanent |
| Message Persistence | ⏰ 5 minutes | ♾️ Forever |
| Admin Access | ❌ Impossible | ✅ Full access |
| Data Breach Risk | 🔒 Minimal | ⚠️ High |
| Compliance | ✅ Privacy-first | ⚠️ Depends |

## Conclusion

The myWhispers security architecture demonstrates that it''s possible to provide modern, user-friendly chat functionality while maintaining the highest levels of privacy and security. By keeping message content exclusively on the client-side and implementing automatic expiration, the system eliminates many common security risks associated with traditional messaging platforms.',
    'Visual overview of the myWhispers private messaging system security architecture with interactive Mermaid diagram',
    '["security", "privacy", "architecture", "messaging", "ephemeral", "zero-knowledge", "client-side", "shadcn-chat", "visual-diagram"]'::json,
    1, -- Assuming user ID 1 exists, this will need to be updated to an actual admin user ID
    NOW(),
    NOW()
) ON CONFLICT DO NOTHING;

-- Update category article count
UPDATE public.knowledge_categories 
SET article_count = article_count + 1,
    updated_at = NOW()
WHERE id = 'developer-documentation';

-- Log the migration
DO $$
BEGIN
    RAISE NOTICE 'Developer Documentation category and myWhispers Security Architecture article added successfully at %', now();
END $$; 