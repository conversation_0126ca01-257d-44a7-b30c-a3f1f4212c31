-- Migration: Automatic Cache Invalidation Triggers
-- Purpose: Create database triggers to automatically invalidate Redis cache when spatial data changes
-- Phase: 3 - Advanced Optimization

-- ==============================================================================
-- CACHE INVALIDATION NOTIFICATION SYSTEM
-- ==============================================================================

-- Create a function to notify cache invalidation via NOTIFY
CREATE OR REPLACE FUNCTION notify_cache_invalidation(
    cache_type TEXT,
    entity_id TEXT,
    operation TEXT DEFAULT 'invalidate'
) RETURNS VOID AS $$
BEGIN
    -- Send notification to cache invalidation channel
    PERFORM pg_notify(
        'cache_invalidation', 
        json_build_object(
            'type', cache_type,
            'entityId', entity_id,
            'operation', operation,
            'timestamp', EXTRACT(EPOCH FROM NOW())
        )::text
    );
    
    -- Log the cache invalidation event
    INSERT INTO public."CacheInvalidationLog" (
        cache_type,
        entity_id,
        operation,
        created_at
    ) VALUES (
        cache_type,
        entity_id,
        operation,
        NOW()
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- Don't fail the main operation if cache invalidation fails
        NULL;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- CACHE INVALIDATION LOG TABLE
-- ==============================================================================

-- Create table to log cache invalidation events for monitoring
CREATE TABLE IF NOT EXISTS public."CacheInvalidationLog" (
    id SERIAL PRIMARY KEY,
    cache_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    operation VARCHAR(50) NOT NULL DEFAULT 'invalidate',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    INDEX_NAME VARCHAR(255)
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_cache_invalidation_log_created_at 
ON public."CacheInvalidationLog" (created_at);

CREATE INDEX IF NOT EXISTS idx_cache_invalidation_log_entity_type 
ON public."CacheInvalidationLog" (cache_type, entity_id);

-- ==============================================================================
-- UTILITY TABLE TRIGGERS
-- ==============================================================================

-- Function to handle utility changes
CREATE OR REPLACE FUNCTION trigger_utility_cache_invalidation() RETURNS TRIGGER AS $$
DECLARE
    project_ids UUID[];
    project_id UUID;
BEGIN
    -- Get affected project IDs
    IF TG_OP = 'DELETE' THEN
        -- For DELETE, get projects from OLD record
        SELECT array_agg(DISTINCT pu."projectId") INTO project_ids
        FROM "ProjectUtility" pu
        WHERE pu."utilityId" = OLD.id;
    ELSE
        -- For INSERT/UPDATE, get projects from NEW record
        SELECT array_agg(DISTINCT pu."projectId") INTO project_ids
        FROM "ProjectUtility" pu
        WHERE pu."utilityId" = COALESCE(NEW.id, OLD.id);
    END IF;
    
    -- Invalidate cache for each affected project
    FOREACH project_id IN ARRAY project_ids
    LOOP
        -- Invalidate project-specific caches
        PERFORM notify_cache_invalidation('project', project_id::text, 'utility_changed');
        PERFORM notify_cache_invalidation('spatial', project_id::text, 'utility_changed');
        PERFORM notify_cache_invalidation('conflict', project_id::text, 'utility_changed');
        
        -- If geometry changed, invalidate spatial computations
        IF TG_OP = 'UPDATE' AND (OLD.geometry IS DISTINCT FROM NEW.geometry) THEN
            PERFORM notify_cache_invalidation('spatial-compute', project_id::text, 'geometry_changed');
        END IF;
    END LOOP;
    
    -- Invalidate utility-specific cache
    IF TG_OP = 'DELETE' THEN
        PERFORM notify_cache_invalidation('utility', OLD.id::text, 'deleted');
    ELSE
        PERFORM notify_cache_invalidation('utility', NEW.id::text, TG_OP);
    END IF;
    
    -- Refresh materialized views if this is a significant change
    IF TG_OP IN ('INSERT', 'DELETE') OR 
       (TG_OP = 'UPDATE' AND (OLD.geometry IS DISTINCT FROM NEW.geometry OR OLD.type != NEW.type)) THEN
        PERFORM notify_cache_invalidation('materialized_view', 'spatial_summary', 'refresh_needed');
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for Utility table
DROP TRIGGER IF EXISTS trigger_utility_cache_invalidation_insert ON "Utility";
DROP TRIGGER IF EXISTS trigger_utility_cache_invalidation_update ON "Utility";
DROP TRIGGER IF EXISTS trigger_utility_cache_invalidation_delete ON "Utility";

CREATE TRIGGER trigger_utility_cache_invalidation_insert
    AFTER INSERT ON "Utility"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_utility_cache_invalidation();

CREATE TRIGGER trigger_utility_cache_invalidation_update
    AFTER UPDATE ON "Utility"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_utility_cache_invalidation();

CREATE TRIGGER trigger_utility_cache_invalidation_delete
    AFTER DELETE ON "Utility"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_utility_cache_invalidation();

-- ==============================================================================
-- PROJECT UTILITY ASSOCIATION TRIGGERS
-- ==============================================================================

-- Function to handle project-utility association changes
CREATE OR REPLACE FUNCTION trigger_project_utility_cache_invalidation() RETURNS TRIGGER AS $$
BEGIN
    -- Invalidate project caches for both old and new projects
    IF TG_OP = 'DELETE' THEN
        PERFORM notify_cache_invalidation('project', OLD."projectId"::text, 'utility_removed');
        PERFORM notify_cache_invalidation('spatial', OLD."projectId"::text, 'utility_removed');
        PERFORM notify_cache_invalidation('conflict', OLD."projectId"::text, 'utility_removed');
    ELSIF TG_OP = 'INSERT' THEN
        PERFORM notify_cache_invalidation('project', NEW."projectId"::text, 'utility_added');
        PERFORM notify_cache_invalidation('spatial', NEW."projectId"::text, 'utility_added');
        PERFORM notify_cache_invalidation('conflict', NEW."projectId"::text, 'utility_added');
    ELSIF TG_OP = 'UPDATE' THEN
        -- If project association changed
        IF OLD."projectId" != NEW."projectId" THEN
            PERFORM notify_cache_invalidation('project', OLD."projectId"::text, 'utility_moved');
            PERFORM notify_cache_invalidation('project', NEW."projectId"::text, 'utility_moved');
            PERFORM notify_cache_invalidation('spatial', OLD."projectId"::text, 'utility_moved');
            PERFORM notify_cache_invalidation('spatial', NEW."projectId"::text, 'utility_moved');
        END IF;
    END IF;
    
    -- Trigger materialized view refresh for significant changes
    IF TG_OP IN ('INSERT', 'DELETE') THEN
        PERFORM notify_cache_invalidation('materialized_view', 'spatial_summary', 'refresh_needed');
        PERFORM notify_cache_invalidation('materialized_view', 'utility_density_clusters', 'refresh_needed');
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for ProjectUtility table
DROP TRIGGER IF EXISTS trigger_project_utility_cache_invalidation_insert ON "ProjectUtility";
DROP TRIGGER IF EXISTS trigger_project_utility_cache_invalidation_update ON "ProjectUtility";
DROP TRIGGER IF EXISTS trigger_project_utility_cache_invalidation_delete ON "ProjectUtility";

CREATE TRIGGER trigger_project_utility_cache_invalidation_insert
    AFTER INSERT ON "ProjectUtility"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_project_utility_cache_invalidation();

CREATE TRIGGER trigger_project_utility_cache_invalidation_update
    AFTER UPDATE ON "ProjectUtility"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_project_utility_cache_invalidation();

CREATE TRIGGER trigger_project_utility_cache_invalidation_delete
    AFTER DELETE ON "ProjectUtility"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_project_utility_cache_invalidation();

-- ==============================================================================
-- PROJECT TABLE TRIGGERS
-- ==============================================================================

-- Function to handle project changes
CREATE OR REPLACE FUNCTION trigger_project_cache_invalidation() RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        -- Invalidate all caches for deleted project
        PERFORM notify_cache_invalidation('project', OLD.id::text, 'deleted');
        PERFORM notify_cache_invalidation('spatial', OLD.id::text, 'deleted');
        PERFORM notify_cache_invalidation('conflict', OLD.id::text, 'deleted');
        PERFORM notify_cache_invalidation('dashboard', OLD.id::text, 'deleted');
    ELSE
        -- Invalidate project-specific caches
        PERFORM notify_cache_invalidation('project', NEW.id::text, TG_OP);
        
        -- If project details that affect dashboard changed
        IF TG_OP = 'UPDATE' AND (
            OLD.name != NEW.name OR 
            OLD.status != NEW.status OR 
            OLD."startDate" != NEW."startDate" OR 
            OLD."endDate" != NEW."endDate"
        ) THEN
            PERFORM notify_cache_invalidation('dashboard', NEW.id::text, 'project_updated');
        END IF;
    END IF;
    
    -- Invalidate general dashboard cache
    PERFORM notify_cache_invalidation('dashboard', 'global', 'project_changed');
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for Project table
DROP TRIGGER IF EXISTS trigger_project_cache_invalidation_update ON "Project";
DROP TRIGGER IF EXISTS trigger_project_cache_invalidation_delete ON "Project";

CREATE TRIGGER trigger_project_cache_invalidation_update
    AFTER UPDATE ON "Project"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_project_cache_invalidation();

CREATE TRIGGER trigger_project_cache_invalidation_delete
    AFTER DELETE ON "Project"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_project_cache_invalidation();

-- ==============================================================================
-- STAKEHOLDER TABLE TRIGGERS
-- ==============================================================================

-- Function to handle stakeholder changes
CREATE OR REPLACE FUNCTION trigger_stakeholder_cache_invalidation() RETURNS TRIGGER AS $$
DECLARE
    project_ids UUID[];
    project_id UUID;
BEGIN
    -- Get affected project IDs through utilities
    IF TG_OP = 'DELETE' THEN
        SELECT array_agg(DISTINCT pu."projectId") INTO project_ids
        FROM "Utility" u
        JOIN "ProjectUtility" pu ON u.id = pu."utilityId"
        WHERE u."stakeholderId" = OLD.id;
    ELSE
        SELECT array_agg(DISTINCT pu."projectId") INTO project_ids
        FROM "Utility" u
        JOIN "ProjectUtility" pu ON u.id = pu."utilityId"
        WHERE u."stakeholderId" = COALESCE(NEW.id, OLD.id);
    END IF;
    
    -- Invalidate project caches
    FOREACH project_id IN ARRAY project_ids
    LOOP
        PERFORM notify_cache_invalidation('project', project_id::text, 'stakeholder_changed');
    END LOOP;
    
    -- Invalidate stakeholder-specific cache
    IF TG_OP = 'DELETE' THEN
        PERFORM notify_cache_invalidation('stakeholder', OLD.id::text, 'deleted');
    ELSE
        PERFORM notify_cache_invalidation('stakeholder', NEW.id::text, TG_OP);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for Stakeholder table
DROP TRIGGER IF EXISTS trigger_stakeholder_cache_invalidation_update ON "Stakeholder";
DROP TRIGGER IF EXISTS trigger_stakeholder_cache_invalidation_delete ON "Stakeholder";

CREATE TRIGGER trigger_stakeholder_cache_invalidation_update
    AFTER UPDATE ON "Stakeholder"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_stakeholder_cache_invalidation();

CREATE TRIGGER trigger_stakeholder_cache_invalidation_delete
    AFTER DELETE ON "Stakeholder"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_stakeholder_cache_invalidation();

-- ==============================================================================
-- MATERIALIZED VIEW REFRESH TRIGGERS
-- ==============================================================================

-- Function to handle materialized view refresh scheduling
CREATE OR REPLACE FUNCTION schedule_materialized_view_refresh() RETURNS TRIGGER AS $$
BEGIN
    -- Schedule refresh of spatial summary materialized view
    -- This will be picked up by the cache warming system
    PERFORM notify_cache_invalidation('materialized_view', 'all', 'schedule_refresh');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- AUTOMATED MATERIALIZED VIEW REFRESH
-- ==============================================================================

-- Function to automatically refresh materialized views based on data changes
CREATE OR REPLACE FUNCTION auto_refresh_materialized_views() RETURNS VOID AS $$
DECLARE
    view_name TEXT;
    refresh_start TIMESTAMP;
    refresh_end TIMESTAMP;
    refresh_duration INTERVAL;
BEGIN
    -- Array of materialized views to refresh
    FOR view_name IN 
        SELECT unnest(ARRAY['mv_project_spatial_summary', 'mv_spatial_conflict_hotspots', 'mv_utility_density_clusters'])
    LOOP
        BEGIN
            refresh_start := NOW();
            
            -- Refresh each materialized view concurrently when possible
            EXECUTE format('REFRESH MATERIALIZED VIEW CONCURRENTLY %I', view_name);
            
            refresh_end := NOW();
            refresh_duration := refresh_end - refresh_start;
            
            -- Log successful refresh
            INSERT INTO public."CacheInvalidationLog" (
                cache_type,
                entity_id,
                operation,
                created_at
            ) VALUES (
                'materialized_view_refresh',
                view_name,
                format('SUCCESS - Duration: %s', refresh_duration),
                refresh_end
            );
            
        EXCEPTION
            WHEN OTHERS THEN
                -- Log failed refresh
                INSERT INTO public."CacheInvalidationLog" (
                    cache_type,
                    entity_id,
                    operation,
                    created_at
                ) VALUES (
                    'materialized_view_refresh',
                    view_name,
                    format('FAILED - Error: %s', SQLERRM),
                    NOW()
                );
        END;
    END LOOP;
    
    -- Notify that materialized view refresh is complete
    PERFORM notify_cache_invalidation('materialized_view', 'all', 'refresh_complete');
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- BULK OPERATIONS TRIGGERS
-- ==============================================================================

-- Function to handle bulk operations efficiently
CREATE OR REPLACE FUNCTION trigger_bulk_cache_invalidation() RETURNS TRIGGER AS $$
DECLARE
    affected_projects UUID[];
    project_id UUID;
BEGIN
    -- For bulk operations, collect all affected projects
    IF TG_TABLE_NAME = 'Utility' THEN
        SELECT array_agg(DISTINCT pu."projectId") INTO affected_projects
        FROM "ProjectUtility" pu
        WHERE pu."utilityId" = COALESCE(NEW.id, OLD.id);
    END IF;
    
    -- Batch invalidate to reduce notification overhead
    FOREACH project_id IN ARRAY affected_projects
    LOOP
        PERFORM notify_cache_invalidation('bulk_operation', project_id::text, 'utility_bulk_change');
    END LOOP;
    
    -- Schedule materialized view refresh for bulk operations
    IF array_length(affected_projects, 1) > 10 THEN
        PERFORM notify_cache_invalidation('materialized_view', 'bulk', 'bulk_refresh_needed');
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- CACHE STATISTICS AND MONITORING
-- ==============================================================================

-- View to monitor cache invalidation patterns
CREATE OR REPLACE VIEW v_cache_invalidation_stats AS
SELECT 
    cache_type,
    operation,
    COUNT(*) as event_count,
    MIN(created_at) as first_event,
    MAX(created_at) as last_event,
    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at))) / COUNT(*) as avg_interval_seconds
FROM public."CacheInvalidationLog"
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY cache_type, operation
ORDER BY event_count DESC;

-- Function to get cache invalidation statistics
CREATE OR REPLACE FUNCTION get_cache_invalidation_stats(hours_back INTEGER DEFAULT 24)
RETURNS TABLE (
    cache_type TEXT,
    operation TEXT,
    event_count BIGINT,
    first_event TIMESTAMP WITH TIME ZONE,
    last_event TIMESTAMP WITH TIME ZONE,
    avg_interval_seconds NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cil.cache_type,
        cil.operation,
        COUNT(*) as event_count,
        MIN(cil.created_at) as first_event,
        MAX(cil.created_at) as last_event,
        EXTRACT(EPOCH FROM (MAX(cil.created_at) - MIN(cil.created_at))) / COUNT(*) as avg_interval_seconds
    FROM public."CacheInvalidationLog" cil
    WHERE cil.created_at >= NOW() - (hours_back || ' hours')::INTERVAL
    GROUP BY cil.cache_type, cil.operation
    ORDER BY event_count DESC;
END;
$$ LANGUAGE plpgsql STABLE;

-- ==============================================================================
-- CLEANUP PROCEDURES
-- ==============================================================================

-- Function to clean up old cache invalidation logs
CREATE OR REPLACE FUNCTION cleanup_cache_invalidation_logs(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public."CacheInvalidationLog"
    WHERE created_at < NOW() - (days_to_keep || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup operation
    INSERT INTO public."CacheInvalidationLog" (
        cache_type,
        entity_id,
        operation,
        created_at
    ) VALUES (
        'system',
        'cleanup',
        format('Cleaned up %s old log entries', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- GRANT PERMISSIONS
-- ==============================================================================

-- Grant necessary permissions
GRANT SELECT, INSERT ON public."CacheInvalidationLog" TO PUBLIC;
GRANT SELECT ON v_cache_invalidation_stats TO PUBLIC;
GRANT EXECUTE ON FUNCTION notify_cache_invalidation TO PUBLIC;
GRANT EXECUTE ON FUNCTION get_cache_invalidation_stats TO PUBLIC;
GRANT EXECUTE ON FUNCTION cleanup_cache_invalidation_logs TO PUBLIC;
GRANT EXECUTE ON FUNCTION auto_refresh_materialized_views TO PUBLIC;

-- ==============================================================================
-- INITIAL SETUP
-- ==============================================================================

-- Perform initial cleanup of any existing logs
SELECT cleanup_cache_invalidation_logs(7);

-- Log the successful migration
INSERT INTO public."CacheInvalidationLog" (
    cache_type,
    entity_id,
    operation,
    created_at
) VALUES (
    'system',
    'migration',
    'Cache invalidation triggers installed successfully',
    NOW()
);

-- ==============================================================================
-- MIGRATION COMPLETE
-- ==============================================================================

SELECT 'Cache invalidation triggers and monitoring system created successfully' as migration_status;