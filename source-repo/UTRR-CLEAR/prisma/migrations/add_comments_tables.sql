-- Create comments table for universal commenting system
CREATE TABLE IF NOT EXISTS public.comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commentable_type VARCHAR(50) NOT NULL,
  commentable_id TEXT NOT NULL,
  user_id INTEGER NOT NULL REFERENCES auth.users(id) ON DELETE NO ACTION ON UPDATE NO ACTION,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES public.comments(id) ON DELETE NO ACTION ON UPDATE NO ACTION,
  mentions INTEGER[] DEFAULT '{}',
  deleted_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_comments_commentable ON public.comments(commentable_type, commentable_id);
CREATE INDEX idx_comments_user_id ON public.comments(user_id);
CREATE INDEX idx_comments_parent_id ON public.comments(parent_id);

-- Create comment_attachments table
CREATE TABLE IF NOT EXISTS public.comment_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id UUID NOT NULL REFERENCES public.comments(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  file_url TEXT NOT NULL,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for attachments
CREATE INDEX idx_comment_attachments_comment_id ON public.comment_attachments(comment_id);