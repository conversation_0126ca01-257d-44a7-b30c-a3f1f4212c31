# High-performance Nginx configuration for CLEAR application
# Optimized for load balancing, SSL termination, and caching

user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;

pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    client_max_body_size 100M;

    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    log_format json escape=json '{'
        '"time_local":"$time_local",'
        '"remote_addr":"$remote_addr",'
        '"remote_user":"$remote_user",'
        '"request":"$request",'
        '"status": "$status",'
        '"body_bytes_sent":"$body_bytes_sent",'
        '"request_time":"$request_time",'
        '"http_referrer":"$http_referer",'
        '"http_user_agent":"$http_user_agent",'
        '"upstream_connect_time":"$upstream_connect_time",'
        '"upstream_header_time":"$upstream_header_time",'
        '"upstream_response_time":"$upstream_response_time"'
    '}';

    access_log /var/log/nginx/access.log json;
    error_log /var/log/nginx/error.log warn;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Brotli compression (if module available)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=5r/m;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=addr:10m;

    # Proxy cache
    proxy_cache_path /var/cache/nginx/proxy levels=1:2 keys_zone=proxy_cache:100m max_size=1g inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:100m max_size=2g inactive=24h use_temp_path=off;

    # Upstream definitions
    upstream app_backend {
        least_conn;
        server app:3000 max_fails=3 fail_timeout=30s;
        # Add more app instances when using Docker Swarm/Kubernetes
        # server app-2:3000 max_fails=3 fail_timeout=30s;
        # server app-3:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream spatial_backend {
        least_conn;
        server spatial-processor:8001 max_fails=3 fail_timeout=30s;
        # server spatial-processor-2:8001 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream websocket_backend {
        ip_hash; # Sticky sessions for WebSocket connections
        server websocket-server:8002 max_fails=3 fail_timeout=30s;
        # server websocket-server-2:8002 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # Real IP configuration (for CloudFlare/reverse proxy)
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;

    # Security headers map
    map $sent_http_content_type $content_security_policy {
        ~*text/html "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com; style-src 'self' 'unsafe-inline' *.googleapis.com; img-src 'self' data: *.googleapis.com *.gstatic.com; font-src 'self' *.googleapis.com *.gstatic.com; connect-src 'self' *.googleapis.com ws: wss:; frame-ancestors 'none';";
    }

    # Include server configurations
    include /etc/nginx/sites-enabled/*;
}