# SSL Certificate Setup

This directory contains SSL certificates for the CLEAR application.

## Development Setup

For development, you can generate self-signed certificates:

```bash
# Generate self-signed certificate for development
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout key.pem \
  -out cert.pem \
  -config <(
    echo '[dn]'
    echo 'CN=localhost'
    echo '[req]'
    echo 'distinguished_name = dn'
    echo '[EXT]'
    echo 'subjectAltName=DNS:localhost,DNS:*.localhost,IP:127.0.0.1'
    echo 'keyUsage=keyEncipherment,dataEncipherment'
    echo 'extendedKeyUsage=serverAuth'
  ) -extensions EXT
```

## Production Setup with Let's Encrypt

For production, use Certbot with Let's Encrypt:

### Option 1: Certbot with Docker

```bash
# Run certbot to obtain certificates
docker run -it --rm \
  -v ./nginx/ssl:/etc/letsencrypt \
  -p 80:80 \
  certbot/certbot certonly --standalone \
  -d your-domain.com \
  -d www.your-domain.com

# Copy certificates to the expected location
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem key.pem
```

### Option 2: Using nginx-certbot Docker Compose

Add this to a separate `docker-compose.certbot.yml`:

```yaml
version: '3.8'
services:
  certbot:
    image: certbot/certbot
    volumes:
      - ./nginx/ssl:/etc/letsencrypt
      - certbot_www:/var/www/certbot
    command: >-
      certonly --webroot 
      --webroot-path=/var/www/certbot 
      --email <EMAIL> 
      --agree-tos 
      --no-eff-email 
      -d your-domain.com 
      -d www.your-domain.com

volumes:
  certbot_www:
```

## Required Files

- `cert.pem` - SSL certificate (public key)
- `key.pem` - Private key

## Certificate Renewal

Set up automatic renewal with cron:

```bash
# Add to crontab (runs twice daily)
0 12 * * * docker-compose -f docker-compose.certbot.yml run --rm certbot renew --quiet && docker-compose exec nginx nginx -s reload
```

## Security Notes

- Never commit private keys to version control
- Use strong encryption (RSA 2048+ or ECDSA P-256+)
- Enable OCSP stapling for better performance
- Use HTTP Public Key Pinning (HPKP) headers for enhanced security