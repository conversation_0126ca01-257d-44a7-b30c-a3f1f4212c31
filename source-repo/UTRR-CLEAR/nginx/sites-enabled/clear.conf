# CLEAR Application Server Configuration
# Production-ready configuration with SSL, caching, and load balancing

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name _;

    # Health check endpoint (no redirect)
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name _;

    # SSL configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy $content_security_policy always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), accelerometer=(), gyroscope=()" always;

    # Rate limiting
    limit_conn addr 20;

    # Static assets with aggressive caching
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$ {
        proxy_pass http://app_backend;
        proxy_cache static_cache;
        proxy_cache_valid 200 1y;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        proxy_cache_lock_timeout 5s;
        
        add_header Cache-Control "public, immutable, max-age=31536000";
        add_header X-Cache-Status $upstream_cache_status;
        
        # Remove server headers for security
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Next.js static files (_next/static)
    location ~* ^/_next/static/ {
        proxy_pass http://app_backend;
        proxy_cache static_cache;
        proxy_cache_valid 200 1y;
        proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
        
        add_header Cache-Control "public, immutable, max-age=31536000";
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Upload endpoint with file size limit and rate limiting
    location /api/upload {
        limit_req zone=upload burst=10 nodelay;
        client_max_body_size 500M;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        
        proxy_pass http://app_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # API endpoints with rate limiting
    location /api/ {
        limit_req zone=api burst=50 nodelay;
        
        proxy_pass http://app_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
        
        # Cache some API responses
        proxy_cache proxy_cache;
        proxy_cache_valid 200 5m;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_bypass $arg_nocache $cookie_nocache $http_cache_control;
        
        add_header X-Cache-Status $upstream_cache_status;
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Authentication endpoints with stricter rate limiting
    location ~ ^/api/(auth|signin|signup|reset-password) {
        limit_req zone=auth burst=5 nodelay;
        
        proxy_pass http://app_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 30s;
        proxy_send_timeout 30s;
        
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Spatial processing API (proxied to separate service)
    location /api/spatial/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://spatial_backend/spatial/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
        
        # Cache spatial computations
        proxy_cache proxy_cache;
        proxy_cache_valid 200 15m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        
        add_header X-Cache-Status $upstream_cache_status;
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # WebSocket connections
    location /ws {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket timeouts
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 30s;
        
        # Disable caching for WebSocket
        proxy_cache off;
        
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Health checks (no caching, minimal logging)
    location = /health {
        access_log off;
        proxy_pass http://app_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_read_timeout 5s;
        proxy_send_timeout 5s;
        proxy_connect_timeout 5s;
    }

    # Monitoring endpoints
    location ~ ^/(metrics|status) {
        access_log off;
        proxy_pass http://app_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_read_timeout 10s;
        proxy_send_timeout 10s;
    }

    # Main application (catch-all)
    location / {
        proxy_pass http://app_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
        
        # Cache HTML pages for a short time
        proxy_cache proxy_cache;
        proxy_cache_valid 200 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_bypass $cookie_nocache $arg_nocache $http_cache_control;
        
        add_header X-Cache-Status $upstream_cache_status;
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }
}