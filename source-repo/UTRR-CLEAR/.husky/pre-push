#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-push checks..."

# Run TypeScript type checking
echo "📝 Checking TypeScript types..."
npm run typecheck
if [ $? -ne 0 ]; then
  echo "❌ TypeScript type check failed! Please fix type errors before pushing."
  exit 1
fi

# Run mock data scan
echo "🔎 Scanning for mock data..."
npm run scan:mock-data
if [ $? -ne 0 ]; then
  echo "❌ Mock data detected! Please remove test data before pushing to production."
  exit 1
fi

echo "✅ All pre-push checks passed!"