# GitHub Codespaces IDE Connection Guide

This guide explains how to connect to GitHub Codespaces from various IDEs, ensuring all team members work in the same standardized environment.

## Available Codespace Configurations

1. **Development Environment** (`.devcontainer/development/`)
   - Full development setup with all tools
   - PostgreSQL, Redis, and mail server included
   - Optimized for daily development work

2. **Testing Environment** (`.devcontainer/testing/`)
   - Focused on testing tools and browsers
   - Pre-installed Playwright, Jest, and performance tools
   - Optimized for running and debugging tests

## Starting a Codespace

### From GitHub Web
1. Go to the repository on GitHub
2. Click the green "Code" button
3. Select "Codespaces" tab
4. Click "Create codespace on master"
5. Choose configuration when prompted:
   - "development" for general development
   - "testing" for test-focused work

### Using GitHub CLI
```bash
# Create development codespace
gh codespace create --repo Craft1563/CLEAR-nextjs --devcontainer-path .devcontainer/development

# Create testing codespace
gh codespace create --repo Craft1563/CLEAR-nextjs --devcontainer-path .devcontainer/testing

# List your codespaces
gh codespace list

# Connect to existing codespace
gh codespace code --codespace <name>
```

## Connecting from Different IDEs

### VS Code (Desktop)

1. **Install Extension**
   ```
   code --install-extension GitHub.codespaces
   ```

2. **Connect to Codespace**
   - Open VS Code
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Codespaces: Connect to Codespace"
   - Select your codespace from the list

3. **Alternative: GitHub CLI**
   ```bash
   gh codespace code --codespace <name>
   ```

### JetBrains IDEs (WebStorm, IntelliJ IDEA)

1. **Install JetBrains Gateway**
   - Download from: https://www.jetbrains.com/remote-development/gateway/

2. **Connect via Gateway**
   - Open JetBrains Gateway
   - Click "Connect to Codespaces"
   - Sign in with GitHub
   - Select your codespace
   - Choose your IDE (WebStorm for this project)

3. **Direct SSH Connection**
   ```bash
   # Get SSH details
   gh codespace ssh --codespace <name> --config
   
   # Add to ~/.ssh/config, then connect from IDE
   ```

### Visual Studio 2022

1. **Requirements**
   - Visual Studio 2022 v17.3 or later
   - GitHub extension installed

2. **Connection Steps**
   - File → Connect to a Codespace
   - Sign in to GitHub
   - Select your codespace
   - Wait for connection

### Vim/Neovim (via SSH)

1. **Get SSH Command**
   ```bash
   gh codespace ssh --codespace <name>
   ```

2. **Configure for Development**
   ```bash
   # Once connected
   cd /workspace/utility-sync-t3
   nvim .  # Your configured Neovim will work
   ```

### Any SSH-Compatible Editor

1. **Get SSH Configuration**
   ```bash
   gh codespace ssh --codespace <name> --config > codespace-config
   ```

2. **Add to SSH Config**
   ```bash
   cat codespace-config >> ~/.ssh/config
   ```

3. **Connect from Your Editor**
   - Use the hostname from the config
   - Default user is `vscode` or `developer`

## Codespace Features by IDE

| Feature | VS Code | JetBrains | Visual Studio | SSH/Terminal |
|---------|---------|-----------|---------------|--------------|
| Port Forwarding | ✅ Automatic | ✅ Manual | ✅ Automatic | ✅ Manual |
| Terminal Access | ✅ Integrated | ✅ Integrated | ✅ Integrated | ✅ Native |
| Debugging | ✅ Full | ✅ Full | ✅ Limited | ⚠️ GDB/CLI |
| Extensions | ✅ Synced | ✅ Plugins | ✅ Extensions | ❌ N/A |
| Git Integration | ✅ Full | ✅ Full | ✅ Full | ✅ CLI |
| Live Share | ✅ Built-in | ❌ No | ✅ Built-in | ❌ No |

## Best Practices

### 1. **Codespace Management**
```bash
# Stop codespace when not in use (saves billing)
gh codespace stop --codespace <name>

# Delete old codespaces
gh codespace delete --codespace <name>

# List all codespaces
gh codespace list
```

### 2. **Performance Tips**
- Use the "4-core" machine type for better performance
- Enable prebuilds for faster startup
- Stop codespaces when not actively working

### 3. **Team Collaboration**
- Commit `.devcontainer` changes to ensure consistency
- Use the same configuration type for similar work
- Share codespace URLs for pair programming

## Prebuild Configuration

Add to `.github/workflows/codespaces-prebuild.yml`:

```yaml
name: Codespaces Prebuilds
on:
  push:
    branches: [main, master]
  workflow_dispatch:

jobs:
  prebuild:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: github/codespaces-prebuild@v1
        with:
          devcontainer-path: .devcontainer/development
      - uses: github/codespaces-prebuild@v1
        with:
          devcontainer-path: .devcontainer/testing
```

## Troubleshooting

### Connection Issues
```bash
# Reset Codespace
gh codespace rebuild --codespace <name>

# View logs
gh codespace logs --codespace <name>
```

### Port Forwarding
```bash
# Manual port forward
gh codespace ports forward 3000:3000 --codespace <name>

# List forwarded ports
gh codespace ports list --codespace <name>
```

### Performance Issues
- Upgrade to a larger machine type
- Check for resource-intensive processes
- Ensure prebuilds are enabled

## Environment Variables

Each IDE handles environment variables differently:

- **VS Code**: Reads from `.devcontainer/devcontainer.json`
- **JetBrains**: Set in Run Configurations
- **SSH**: Export in shell or use `.env` files

## Quick Start Commands

Once connected to any Codespace:

```bash
# Development Environment
cd /workspace/utility-sync-t3
npm run dev          # Start development server

# Testing Environment
cd /workspace/utility-sync-t3
npm run test:e2e:ui  # Open Playwright UI
npm test             # Run all tests
```

## Cost Optimization

- **Stop inactive codespaces**: Automatically stops after 30 min
- **Delete unused codespaces**: Clean up regularly
- **Use prebuilds**: Reduces startup time and costs
- **Right-size machines**: Use 2-core for light work, 4-core for development