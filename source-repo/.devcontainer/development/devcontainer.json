{
  "name": "CLEAR - Full Development Environment",
  "dockerComposeFile": "docker-compose.yml",
  "service": "development",
  "workspaceFolder": "/workspace",
  
  // Features to add to the dev container
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "20",
      "nodeGypDependencies": true
    },
    "ghcr.io/devcontainers/features/sshd:1": {
    "version": "latest"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "configureZshAsDefaultShell": true,
      "installOhMyZsh": true,
      "username": "developer"
    },
    "ghcr.io/devcontainers-contrib/features/postgres-asdf:1": {},
    "ghcr.io/devcontainers-contrib/features/redis-homebrew:1": {},
    "ghcr.io/devcontainers-contrib/features/terraform-asdf:2": {}
  },

  // Configure tool-specific properties
  "customizations": {
    // VS Code settings
    "vscode": {
      "settings": {
        "terminal.integrated.defaultProfile.linux": "zsh",
        "editor.formatOnSave": true,
        "editor.defaultFormatter": null,
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit"
        },
        "typescript.updateImportsOnFileMove.enabled": "always",
        "typescript.preferences.importModuleSpecifier": "shortest",
        "files.exclude": {
          "**/node_modules": true,
          "**/.next": true,
          "**/dist": true
        },
        // Remote development specific settings
        "remote.autoForwardPorts": false,
        "remote.extensionKind": {
          "ms-vscode.vscode-typescript-next": ["workspace"],
          "dbaeumer.vscode-eslint": ["workspace"],
          "esbenp.prettier-vscode": ["workspace"],
          "prisma.prisma": ["workspace"],
          "bradlc.vscode-tailwindcss": ["workspace"],
          "ms-playwright.playwright": ["workspace"]
        },
        "extensions.autoUpdate": false,
        "extensions.autoCheckUpdates": false
      },
      "extensions": [
        // Core development
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode",
        "prisma.prisma",
        "bradlc.vscode-tailwindcss",
        
        // Git & GitHub
        "github.vscode-pull-request-github",
        "eamodio.gitlens",
        "github.copilot",
        
        // Database
        "mtxr.sqltools",
        "mtxr.sqltools-driver-pg",
        
        // Testing
        "ms-playwright.playwright",
        "vitest.explorer",
        
        // Utilities
        "streetsidesoftware.code-spell-checker",
        "wayou.vscode-todo-highlight",
        "gruntfuggly.todo-tree",
        "yzhang.markdown-all-in-one",
        "ms-vscode.vscode-typescript-next"
      ]
    },
    
    // JetBrains IDEs support (WebStorm, IntelliJ)
    "jetbrains": {
      "plugins": [
        "com.intellij.nodejs",
        "com.jetbrains.plugins.Prisma",
        "com.dmarcotte.eslint",
        "prettier"
      ]
    }
  },

  // Port forwarding
  "forwardPorts": [
    3000,    // Next.js dev server
    5432,    // PostgreSQL
    6379,    // Redis
    9229,    // Node.js debug
    4000,    // Alternative dev server
    8080,    // Alternative API port
    3001     // Storybook (if added)
  ],
  
  "portsAttributes": {
    "3000": {
      "label": "Next.js App",
      "onAutoForward": "notify",
      "requireLocalPort": true
    },
    "5432": {
      "label": "PostgreSQL Database",
      "onAutoForward": "ignore"
    },
    "6379": {
      "label": "Redis Cache",
      "onAutoForward": "ignore"
    }
  },

  // Startup commands
  "postCreateCommand": "bash .devcontainer/development/post-create.sh",
  "postStartCommand": "bash .devcontainer/development/post-start.sh",
  "postAttachCommand": "cd utility-sync-t3 && npm run dev",

  // Environment variables
  "remoteEnv": {
    "NODE_ENV": "development",
    "DATABASE_URL": "postgresql://postgres:postgres@localhost:5432/utility_sync_dev",
    "REDIS_URL": "redis://localhost:6379",
    "NEXTAUTH_URL": "http://localhost:3000",
    "NEXTAUTH_SECRET": "development-secret-change-in-production"
  },

  // Container settings
  "containerEnv": {
    "SHELL": "/bin/zsh",
    "POSTGRES_PASSWORD": "postgres",
    "POSTGRES_USER": "postgres",
    "POSTGRES_DB": "utility_sync_dev"
  },

  // Mounts
  "mounts": [
    "source=${localWorkspaceFolderBasename}-node_modules,target=/workspace/utility-sync-t3/node_modules,type=volume",
    "source=${localWorkspaceFolderBasename}-postgres-data,target=/var/lib/postgresql/data,type=volume"
  ],

  // User settings
  "remoteUser": "developer",
  
  // Prebuild configuration
  "onCreateCommand": "echo 'Container created!'",
  "updateContentCommand": "cd utility-sync-t3 && npm install",
  
  // Performance optimizations
  "hostRequirements": {
    "cpus": 4,
    "memory": "8gb",
    "storage": "32gb"
  }
}
