version: '3.8'

services:
  development:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspace:cached
      - node_modules:/workspace/utility-sync-t3/node_modules
      - postgres-data:/var/lib/postgresql/data
      - ~/.ssh:/home/<USER>/.ssh:ro
      - ~/.gitconfig:/home/<USER>/.gitconfig:ro
    
    # Overrides default command so things don't shut down after the process ends
    command: sleep infinity
    
    # Network mode for better performance
    network_mode: host
    
    # Environment
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=utility_sync_dev
      - DATABASE_URL=postgresql://postgres:postgres@localhost:5432/utility_sync_dev
      - REDIS_URL=redis://localhost:6379
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  postgres:
    image: postgis/postgis:16-3.4
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: utility_sync_dev
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    environment:
      MH_STORAGE: memory

volumes:
  node_modules:
  postgres-data: