#!/bin/bash
set -e

echo "🔄 Starting development services..."

# Ensure PostgreSQL is running
if ! pg_isready -h localhost -p 5432 -U postgres > /dev/null 2>&1; then
    echo "⚠️  PostgreSQL is not running. Please check the postgres service."
fi

# Ensure Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "⚠️  Redis is not running. Please check the redis service."
fi

# Update dependencies if package.json has changed
cd /workspace/utility-sync-t3
if [ package.json -nt node_modules ]; then
    echo "📦 Package.json has changed, updating dependencies..."
    npm install
fi

# Run any pending migrations
echo "🗄️  Checking for database migrations..."
npm run db:migrate || true

# Clear any stale locks
rm -f .next/cache/webpack.lock 2>/dev/null || true

echo "✅ Development environment is ready!"
echo "💡 Run 'npm run dev' to start the development server"