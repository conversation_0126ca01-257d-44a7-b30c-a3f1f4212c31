#!/bin/bash
set -e

echo "🚀 Setting up CLEAR development environment..."

# Navigate to the T3 app directory
cd /workspace/utility-sync-t3

# Install dependencies
echo "📦 Installing npm dependencies..."
npm install

# Set up environment files
if [ ! -f .env ]; then
    echo "📋 Creating .env file from example..."
    cp .env.example .env
    
    # Update database URL for Codespace
    sed -i 's|DATABASE_URL=.*|DATABASE_URL="postgresql://postgres:postgres@localhost:5432/utility_sync_dev"|g' .env
    
    # Set development secrets
    echo "NEXTAUTH_SECRET=development-secret-$(openssl rand -hex 32)" >> .env
    echo "NEXTAUTH_URL=http://localhost:3000" >> .env
fi

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL..."
until pg_isready -h localhost -p 5432 -U postgres; do
    sleep 2
done

# Set up database
echo "🗄️  Setting up database..."
npm run db:push

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npm run db:generate

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
npx playwright install --with-deps chromium

# Create useful directories
mkdir -p /workspace/docs/notes
mkdir -p /workspace/utility-sync-t3/test-results
mkdir -p /workspace/utility-sync-t3/coverage

# Git configuration
git config --global --add safe.directory /workspace

# Display success message
echo "✅ Development environment setup complete!"
echo ""
echo "🎯 Quick commands:"
echo "  dev     - Start development server"
echo "  test    - Run tests"
echo "  build   - Build for production"
echo ""
echo "📚 Documentation:"
echo "  /workspace/docs            - Project documentation"
echo "  /workspace/CLAUDE.md       - AI assistant guide"
echo ""
echo "🌐 Services:"
echo "  http://localhost:3000      - Next.js app"
echo "  http://localhost:5432      - PostgreSQL"
echo "  http://localhost:6379      - Redis"
echo "  http://localhost:8025      - MailHog UI"