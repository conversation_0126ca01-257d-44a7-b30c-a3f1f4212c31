# GitHub Codespaces Configuration

This directory contains GitHub Codespaces configurations for the CLEAR project, providing standardized development environments for all team members.

## 🚀 Quick Start

### Creating a Codespace

1. **From GitHub.com**:
   - Click the green "Code" button
   - Select "Codespaces" tab
   - Click "+" to create a new codespace
   - Choose your configuration when prompted

2. **From Command Line**:
   ```bash
   # Development environment
   gh codespace create --repo Craft1563/CLEAR-nextjs -d .devcontainer/development
   
   # Testing environment
   gh codespace create --repo Craft1563/CLEAR-nextjs -d .devcontainer/testing
   ```

## 📁 Available Configurations

### Development Environment (`/development`)
Full-featured development setup including:
- Node.js 20 with all development tools
- PostgreSQL with PostGIS
- Redis for caching
- MailHog for email testing
- Pre-configured VS Code extensions
- Zsh with Oh My Zsh
- Git, Docker, and cloud tools

**Use for**: Daily development, feature implementation, debugging

### Testing Environment (`/testing`)
Optimized for running and writing tests:
- All Playwright browsers pre-installed
- Jest, Vitest, and testing utilities
- Performance testing tools (K6, Lighthouse)
- Test report generators
- Visual regression testing setup

**Use for**: Writing tests, running E2E tests, debugging test failures

## 🔧 IDE Support

Connect from any IDE:
- **VS Code**: Native integration
- **JetBrains IDEs**: Via Gateway or SSH
- **Visual Studio**: Built-in support
- **Vim/Neovim**: SSH connection
- **Any SSH-compatible editor**: Manual configuration

See [CODESPACES-IDE-GUIDE.md](./CODESPACES-IDE-GUIDE.md) for detailed connection instructions.

## ⚡ Performance Features

- **Prebuilds**: Environments are pre-built for instant startup
- **Caching**: Dependencies cached in volumes
- **Resource optimization**: 4-8 CPU cores, 8-16GB RAM
- **GPU support**: Available for 3D visualization tasks

## 🛠️ Customization

### Adding Tools
Edit the relevant `devcontainer.json`:
```json
"features": {
  "ghcr.io/devcontainers/features/rust:1": {}
}
```

### VS Code Extensions
Add to the extensions array:
```json
"extensions": [
  "your.extension-id"
]
```

### Environment Variables
Add to `remoteEnv`:
```json
"remoteEnv": {
  "MY_CUSTOM_VAR": "value"
}
```

## 📊 Resource Usage

| Configuration | CPU | RAM | Storage | Cost/Hour |
|---------------|-----|-----|---------|-----------|
| Development | 4 cores | 8GB | 32GB | ~$0.18 |
| Testing | 4 cores | 8GB | 32GB | ~$0.18 |

**Note**: Codespaces stop automatically after 30 minutes of inactivity.

## 🔍 Troubleshooting

### Slow Performance
- Upgrade to 8-core machine type
- Check for resource-intensive processes
- Ensure prebuilds are working

### Connection Issues
```bash
# Rebuild codespace
gh codespace rebuild -c <name>

# View logs
gh codespace logs -c <name>
```

### Missing Dependencies
Check post-create scripts:
- Development: `/development/post-create.sh`
- Testing: `/testing/setup-testing.sh`

## 🤝 Team Guidelines

1. **Stop codespaces** when not in use to save costs
2. **Delete old codespaces** regularly
3. **Use the appropriate configuration** for your task
4. **Commit environment changes** to share with team
5. **Report issues** in the repository

## 📚 Additional Resources

- [GitHub Codespaces Documentation](https://docs.github.com/en/codespaces)
- [VS Code Remote Development](https://code.visualstudio.com/docs/remote/remote-overview)
- [JetBrains Gateway](https://www.jetbrains.com/remote-development/gateway/)
- [Project Documentation](/docs)