{
  "name": "CLEAR - Choose Environment",
  
  // This is a multi-configuration setup
  // Users will be prompted to choose between:
  // 1. Development - Full development environment
  // 2. Testing - Testing-focused environment
  
  // Default to development if no choice is made
  "configureFiles": [
    ".devcontainer/development/devcontainer.json",
    ".devcontainer/testing/devcontainer.json"
  ],
  
  // Fallback configuration if selection isn't supported
  "image": "mcr.microsoft.com/devcontainers/typescript-node:20",
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "20"
    },
    "ghcr.io/devcontainers/features/sshd:1": {
        "version": "latest"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  
  "postCreateCommand": "echo 'Please use either the development or testing configuration. Run: gh codespace create --devcontainer-path .devcontainer/development'",
  
  "customizations": {
    "vscode": {
      "extensions": [
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode"
      ]
    }
  }
}
