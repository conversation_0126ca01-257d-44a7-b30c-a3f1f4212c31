{
  "name": "CLEAR - Testing Environment",
  "image": "mcr.microsoft.com/devcontainers/typescript-node:20",
  
  // Features for testing
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "20"
    },
    "ghcr.io/devcontainers/features/sshd:1": {
    "version": "latest"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers-contrib/features/playwright:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers-contrib/features/cypress:1": {},
    "ghcr.io/devcontainers-contrib/features/k6:1": {}
  },

  // VS Code customizations
  "customizations": {
    "vscode": {
      "settings": {
        "testing.automaticallyOpenPeekView": "never",
        "playwright.reuseBrowser": true,
        "playwright.showTrace": true,
        "terminal.integrated.defaultProfile.linux": "bash"
      },
      "extensions": [
        // Testing extensions
        "ms-playwright.playwright",
        "vitest.explorer",
        "Orta.vscode-jest",
        "hbenl.vscode-test-explorer",
        "kavod-io.vscode-jest-test-adapter",
        "ZixuanChen.vitest-explorer",
        
        // Code quality
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode",
        
        // Debugging
        "msjsdiag.debugger-for-chrome",
        "firefox-devtools.vscode-firefox-debug",
        
        // Utilities
        "christian-kohler.path-intellisense",
        "streetsidesoftware.code-spell-checker",
        "wayou.vscode-todo-highlight"
      ]
    }
  },

  // Port forwarding for test servers
  "forwardPorts": [
    3000,    // Next.js app
    9323,    // Playwright Inspector
    6006,    // Storybook
    9229,    // Node debug
    8080,    // Test reports server
    5432     // PostgreSQL
  ],
  
  "portsAttributes": {
    "3000": {
      "label": "Next.js App",
      "onAutoForward": "notify"
    },
    "9323": {
      "label": "Playwright Inspector",
      "onAutoForward": "notify"
    },
    "8080": {
      "label": "Test Reports",
      "onAutoForward": "openPreview"
    }
  },

  // Commands
  "postCreateCommand": "bash .devcontainer/testing/setup-testing.sh",
  "postStartCommand": "cd utility-sync-t3 && echo '🧪 Testing environment ready! Run npm test to start'",

  // Environment variables optimized for testing
  "remoteEnv": {
    "NODE_ENV": "test",
    "CI": "false",
    "PLAYWRIGHT_BROWSERS_PATH": "/home/<USER>/.cache/ms-playwright",
    "DATABASE_URL": "postgresql://postgres:postgres@localhost:5432/utility_sync_test",
    "NEXTAUTH_URL": "http://localhost:3000",
    "NEXTAUTH_SECRET": "test-secret-for-testing",
    "FORCE_COLOR": "1"
  },

  // Mounts for test artifacts
  "mounts": [
    "source=${localWorkspaceFolderBasename}-test-results,target=/workspace/utility-sync-t3/test-results,type=volume",
    "source=${localWorkspaceFolderBasename}-playwright-cache,target=/home/<USER>/.cache/ms-playwright,type=volume",
    "source=${localWorkspaceFolderBasename}-coverage,target=/workspace/utility-sync-t3/coverage,type=volume"
  ],

  // Performance settings for testing
  "runArgs": [
    "--shm-size=2gb",  // More shared memory for browser testing
    "--cap-add=SYS_ADMIN"  // Required for some browser features
  ],
  
  // Host requirements
  "hostRequirements": {
    "cpus": 4,
    "memory": "8gb",
    "storage": "32gb"
  }
}
