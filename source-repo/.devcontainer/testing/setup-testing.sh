#!/bin/bash
set -e

echo "🧪 Setting up CLEAR testing environment..."

# Navigate to the T3 app directory
cd /workspace/utility-sync-t3

# Install dependencies
echo "📦 Installing npm dependencies..."
npm install

# Set up test environment
if [ ! -f .env ]; then
    echo "📋 Creating .env file for testing..."
    cp .env.example .env
    sed -i 's|DATABASE_URL=.*|DATABASE_URL="postgresql://postgres:postgres@localhost:5432/utility_sync_test"|g' .env
    echo "NEXTAUTH_SECRET=test-secret-$(openssl rand -hex 32)" >> .env
    echo "NEXTAUTH_URL=http://localhost:3000" >> .env
fi

# Install all Playwright browsers
echo "🎭 Installing Playwright browsers..."
npx playwright install --with-deps

# Install additional testing tools
echo "🔧 Installing testing utilities..."
npm install -g serve http-server lighthouse jest-html-reporter

# Create test directories
mkdir -p test-results/{e2e,unit,visual,performance}
mkdir -p coverage
mkdir -p playwright-report

# Set up test database
echo "🗄️  Setting up test database..."
npm run db:push

# Pre-build the application for faster test runs
echo "🏗️  Pre-building application..."
npm run build || true

# Generate test report index
cat > test-results/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>CLEAR Test Reports</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; }
        .report-link { 
            display: block; 
            padding: 10px; 
            margin: 10px 0; 
            background: #f0f0f0; 
            text-decoration: none; 
            color: #333;
            border-radius: 5px;
        }
        .report-link:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>CLEAR Test Reports</h1>
    <a href="/e2e" class="report-link">📸 E2E Test Results</a>
    <a href="/unit" class="report-link">🧪 Unit Test Coverage</a>
    <a href="/visual" class="report-link">🎨 Visual Regression Results</a>
    <a href="/performance" class="report-link">⚡ Performance Metrics</a>
</body>
</html>
EOF

# Display available test commands
echo "✅ Testing environment setup complete!"
echo ""
echo "🧪 Available test commands:"
echo "  npm test                    - Run all tests"
echo "  npm run test:e2e           - Run E2E tests"
echo "  npm run test:e2e:ui        - Run E2E tests with UI"
echo "  npm run test:unit          - Run unit tests"
echo "  npm run test:visual        - Run visual regression tests"
echo "  npm run test:coverage      - Run tests with coverage"
echo ""
echo "📊 View test reports:"
echo "  serve test-results -p 8080  - Serve test reports"
echo "  npx playwright show-report  - Show Playwright report"
echo ""
echo "🎯 Quick test commands:"
echo "  npx playwright test --ui                     - Interactive test runner"
echo "  npx playwright test --debug                  - Debug mode"
echo "  npx playwright test --headed                 - Run with browser visible"
echo "  npx playwright test --project=chromium       - Test specific browser"
echo "  npx playwright codegen http://localhost:3000 - Generate test code"