"""
Celery configuration for CLEAR project.
Handles background tasks including scheduled reports.
"""

import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clear_htmx.settings')

app = Celery('clear_htmx')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Configure beat schedule for periodic tasks
app.conf.beat_schedule = {
    'run-scheduled-reports': {
        'task': 'CLEAR.tasks.run_scheduled_reports',
        'schedule': 300.0,  # Run every 5 minutes
    },
}
app.conf.timezone = 'UTC'

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')