"""
Django settings for CLEAR HTMX project.

CLEAR - Comprehensive Location-based Engineering and Analysis Resource
A modern utility infrastructure management platform.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# GDAL/GEOS/SpatiaLite library paths for Windows (QGIS install)
# if os.name == 'nt':
#     GDAL_LIBRARY_PATH = r'C:\Program Files\QGIS 3.34.12\bin\gdal309.dll'
#     GEOS_LIBRARY_PATH = r'C:\Program Files\QGIS 3.34.12\bin\geos.dll'
#     SPATIALITE_LIBRARY_PATH = r'C:\Program Files\QGIS 3.34.12\bin\mod_spatialite.dll'

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-h*rrq()57o(vl#^su#&(hc4z*c^3)r4q0ff8$do8f+$(d8(6h_')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1,testserver,clear-development.up.railway.app').split(',')

# Application definition
INSTALLED_APPS = [
    # Django admin (customize for CLEAR branding)
    'django.contrib.admin',

    # Django core apps
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # GIS support for spatial operations
    'django.contrib.gis',

    # Third-party apps
    'django_htmx',
    'django_extensions',
    'crispy_forms',
    'crispy_bootstrap5',
    'rest_framework',
    'corsheaders',
    'channels',
    'django_filters',
    'reversion',  # Git-like versioning
    # 'django_mcp_server',  # Django MCP server with auto model tools - temporarily disabled for deployment

    # CLEAR main application
    'CLEAR.apps.ClearConfig',

    # Development tools
    'django_browser_reload',
]

# Basic Django middleware stack for authentication
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django_browser_reload.middleware.BrowserReloadMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django_htmx.middleware.HtmxMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'reversion.middleware.RevisionMiddleware',  # Git-like versioning
    # Custom middleware removed for basic Django setup
    # 'CLEAR.middleware.UserActivityMiddleware',
    # 'CLEAR.middleware.SecurityMiddleware',
]

ROOT_URLCONF = 'clear_htmx.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'CLEAR.feature_flags.feature_flags_context',  # Feature flags
            ],
        },
    },
]

WSGI_APPLICATION = 'clear_htmx.wsgi.application'
ASGI_APPLICATION = 'clear_htmx.asgi.application'

# Database
# Use SpatiaLite for local development (switch to PostGIS for production)
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.spatialite',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Custom User Model
AUTH_USER_MODEL = 'CLEAR.User'

# Django's default ModelBackend handles USERNAME_FIELD = 'email' automatically
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'America/New_York'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Crispy Forms Configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 25,
}

# CORS settings for API access
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

if not DEBUG:
    CORS_ALLOWED_ORIGINS.extend([
        "https://www.clear-egis.com",
        "https://clear-egis.com",
    ])

# Authentication settings
LOGIN_URL = '/auth/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/auth/login/'

# Use DB-backed sessions for stability in development
SESSION_ENGINE = 'django.contrib.sessions.backends.db'

# Session settings - Security-focused configuration
SESSION_COOKIE_AGE = 3600 * 4  # 4 hours (secure for enterprise apps)
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_COOKIE_SECURE = not DEBUG  # HTTPS only in production
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access
SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
SESSION_ENGINE = 'django.contrib.sessions.backends.db'  # Database-backed sessions

# CSRF settings
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = ['https://clear-development.up.railway.app']

if not DEBUG:
    CSRF_TRUSTED_ORIGINS.extend([
        'https://www.clear-egis.com',
        'https://clear-egis.com',
    ])

# Cache configuration (Redis in production)
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Fallback to local memory cache if Redis not available
if os.environ.get('USE_MEMORY_CACHE', 'False') == 'True':
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        }
    }

# Redis Configuration with Connection Pooling
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

# Django Caches Configuration with Redis
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
                'socket_timeout': 5,
                'socket_connect_timeout': 5,
                'health_check_interval': 30,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'clear_htmx',
        'TIMEOUT': 300,  # 5 minutes default
    },
    'messages': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 20,
                'retry_on_timeout': True,
                'socket_timeout': 5,
                'socket_connect_timeout': 5,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
        },
        'KEY_PREFIX': 'clear_messages',
        'TIMEOUT': 1800,  # 30 minutes for message cache
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 10,
                'retry_on_timeout': True,
            },
        },
        'KEY_PREFIX': 'clear_sessions',
        'TIMEOUT': 86400,  # 24 hours for sessions
    }
}

# Fallback to local memory cache if Redis not available
if os.environ.get('USE_MEMORY_CACHE', 'False') == 'True':
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
            'TIMEOUT': 300,
            'OPTIONS': {
                'MAX_ENTRIES': 1000,
            }
        },
        'messages': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'messages-cache',
            'TIMEOUT': 1800,
            'OPTIONS': {
                'MAX_ENTRIES': 500,
            }
        },
        'sessions': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'sessions-cache',
            'TIMEOUT': 86400,
            'OPTIONS': {
                'MAX_ENTRIES': 100,
            }
        }
    }

# Channels configuration for WebSocket support with enhanced stability
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [REDIS_URL],
            'capacity': 2000,  # Increased message capacity for high-traffic
            'expiry': 120,     # Longer message retention for better reliability
            'group_expiry': 86400,  # 24 hours group retention
            'symmetric_encryption_keys': [SECRET_KEY],
            'connection_kwargs': {
                'max_connections': 30,  # Increased connection pool
                'retry_on_timeout': True,
                'retry_on_error': [ConnectionError, TimeoutError],
                'socket_timeout': 10,   # Longer timeout for stability
                'socket_connect_timeout': 10,
                'socket_keepalive': True,
                'socket_keepalive_options': {
                    1: 1,  # TCP_KEEPIDLE
                    2: 3,  # TCP_KEEPINTVL  
                    3: 5,  # TCP_KEEPCNT
                },
                'health_check_interval': 30,
                'connection_pool_kwargs': {
                    'retry_on_timeout': True,
                    'max_connections': 30,
                },
            },
            # Performance optimizations
            'prefix': 'clear_channels:',
            'statistics_interval': 60,  # Enable performance monitoring
        },
    },
}

# Fallback to in-memory channel layer if Redis not available
if os.environ.get('USE_MEMORY_CHANNELS', 'False') == 'True':
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels.layers.InMemoryChannelLayer',
            'capacity': 100,
            'expiry': 60,
        },
    }

# Celery Configuration for background tasks
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
        },
        'websocket_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'websocket.log',
            'formatter': 'verbose',
            'maxBytes': 5 * 1024 * 1024,  # 5MB
            'backupCount': 3,
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'error.log',
            'formatter': 'verbose',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'CLEAR': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'CLEAR.consumers': {
            'handlers': ['websocket_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'channels': {
            'handlers': ['websocket_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'channels_redis': {
            'handlers': ['websocket_file', 'console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'django.channels': {
            'handlers': ['websocket_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'asyncio': {
            'handlers': ['error_file', 'console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'websockets': {
            'handlers': ['websocket_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Create logs directory if it doesn't exist
os.makedirs(BASE_DIR / 'logs', exist_ok=True)

# Security settings for production
if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_REDIRECT_EXEMPT = []
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    USE_TLS = True
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True

    # CSP settings
    CSP_DEFAULT_SRC = ("'self'",)
    CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "https://unpkg.com")
    CSP_STYLE_SRC = ("'self'", "'unsafe-inline'", "https://fonts.googleapis.com")
    CSP_FONT_SRC = ("'self'", "https://fonts.gstatic.com", "https://assets.vercel.com")
    CSP_IMG_SRC = ("'self'", "data:", "https:")

# Email configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'localhost')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True') == 'True'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', 'CLEAR <<EMAIL>>')

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000

# CLEAR-specific settings
CLEAR_SETTINGS = {
    'ORGANIZATION_NAME': os.environ.get('ORGANIZATION_NAME', 'EGIS'),
    'DEFAULT_COORDINATE_SYSTEM': os.environ.get('DEFAULT_COORDINATE_SYSTEM', 'EPSG:4326'),
    'ENABLE_3D_VISUALIZATION': os.environ.get('ENABLE_3D_VISUALIZATION', 'True') == 'True',
    'ENABLE_CONFLICT_DETECTION': os.environ.get('ENABLE_CONFLICT_DETECTION', 'True') == 'True',
    'ENABLE_REAL_TIME_UPDATES': os.environ.get('ENABLE_REAL_TIME_UPDATES', 'True') == 'True',
    'MAX_UPLOAD_SIZE_MB': int(os.environ.get('MAX_UPLOAD_SIZE_MB', '100')),
    'SUPPORTED_CAD_FORMATS': ['dxf', 'dwg', 'shp', 'geojson', 'kml'],
    'DEFAULT_THEME': os.environ.get('DEFAULT_THEME', 'light'),
}

# Django MCP Server Configuration
DJANGO_MCP_GLOBAL_SERVER_CONFIG = {
    "name": "CLEAR-HTMX-Server",
    "instructions": "Django MCP server for CLEAR utility coordination platform. Use tools to query and manage projects, utilities, conflicts, and documents. All data is filtered by user permissions.",
    "stateless": False  # Use Django sessions for state management
}

# Django MCP Authentication Configuration
# For stdio transport, disable authentication requirements
DJANGO_MCP_AUTHENTICATION_CLASSES = []

# Feature Flags Configuration
FEATURE_FLAGS = {
    'USE_HTMX_ENDPOINTS': os.environ.get('FEATURE_FLAG_USE_HTMX_ENDPOINTS', 'False') == 'True',
    'USE_OPENLAYERS': os.environ.get('FEATURE_FLAG_USE_OPENLAYERS', 'False') == 'True',
    'USE_BOOTSTRAP_ONLY': os.environ.get('FEATURE_FLAG_USE_BOOTSTRAP_ONLY', 'False') == 'True',
    'USE_VERSIONING': os.environ.get('FEATURE_FLAG_USE_VERSIONING', 'False') == 'True',
    'USE_ENHANCED_TESTING': os.environ.get('FEATURE_FLAG_USE_ENHANCED_TESTING', 'True') == 'True',
}
