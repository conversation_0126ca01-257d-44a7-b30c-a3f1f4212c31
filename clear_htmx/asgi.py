"""
ASGI config for CLEAR HTMX project.

Enables WebSocket support for real-time features using Django Channels.
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator
import CLEAR.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clear_htmx.settings')

# Initialize Django ASGI application early to ensure the AppRegistry
# is populated before importing code that may import ORM models.
django_asgi_app = get_asgi_application()

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(
                CLEAR.routing.websocket_urlpatterns
            )
        )
    ),
})
