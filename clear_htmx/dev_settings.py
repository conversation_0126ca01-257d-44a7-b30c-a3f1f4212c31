"""
Development-specific Django settings for CLEAR HTMX project.

These settings are specifically for local development using SpatiaLite.
"""

from .settings import *
import os

# Override database configuration for development
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.spatialite',
        'NAME': BASE_DIR / 'dev_db.sqlite3',
    }
}

# SpatiaLite library path (required for Windows)
if os.name == 'nt':
    SPATIALITE_LIBRARY_PATH = r'C:\OSGeo4W64\bin\mod_spatialite.dll'  # Update path as needed

# Development-specific settings
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver']

# Use in-memory cache for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

# Use in-memory channel layer for development
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
        'capacity': 100,
        'expiry': 60,
    },
}

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Disable CSRF for easier development with HTMX
CSRF_TRUSTED_ORIGINS = ['http://localhost:8743', 'http://127.0.0.1:8743']

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'CLEAR': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Static files development settings
STATICFILES_DIRS = [
    BASE_DIR / "static",
]

# Media files for development
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'