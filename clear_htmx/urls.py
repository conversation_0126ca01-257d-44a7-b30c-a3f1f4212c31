"""
URL configuration for CLEAR HTMX project.

CLEAR - Comprehensive Location-based Engineering and Analysis Resource
Main URL routing for the utility coordination platform.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from django.http import JsonResponse
from django.db import connection

def health_check(request):
    """Simple health check endpoint for production"""
    try:
        # Test database connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        db_status = "ok"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    status = "ok" if db_status == "ok" else "error"
    
    return JsonResponse({
        "status": status,
        "database": db_status,
        "timestamp": "2024-12-13T00:00:00Z"
    })

urlpatterns = [
    # Health check endpoint for production
    path('health/', health_check, name='health_check'),
    
    # Admin interface (customized for CLEAR)
    path('admin/', admin.site.urls),
    
    # API endpoints (will be phased out with feature flags)
    path('api/', include('CLEAR.api_urls')),
    
    # HTMX endpoints (new HDA-compliant endpoints)
    path('htmx/', include('CLEAR.htmx_urls')),
    
    # Authentication - Using custom views in CLEAR.urls
    # path('auth/', include('django.contrib.auth.urls')),
    
    # Main CLEAR application (includes root redirect)
    path('', include('CLEAR.urls')),
    
    # Browser auto-reload for development
    path('__reload__/', include('django_browser_reload.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Customize admin site
admin.site.site_header = "CLEAR Administration"
admin.site.site_title = "CLEAR Admin"
admin.site.index_title = "CLEAR Platform Management"
