name: Django CI

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:

    runs-on: ubuntu-24.04
    
    # Add service containers for PostgreSQL and Redis
    services:
      postgres:
        image: postgis/postgis:15-3.3
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_clear_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    strategy:
      max-parallel: 1
      matrix:
        python-version: ['3.13']

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    # Install system dependencies for GIS support
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          binutils \
          libproj-dev \
          gdal-bin \
          libgdal-dev \
          python3-gdal \
          libgeos-dev \
          libgeos++-dev \
          libspatialite-dev \
          sqlite3
    
    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Check requirements.txt exists
      run: |
        if [ ! -f requirements.txt ]; then
          echo "Error: requirements.txt not found"
          exit 1
        fi
        echo "requirements.txt found"
    
    - name: Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    # Set up environment variables required by Django
    - name: Set up environment variables
      run: |
        echo "SECRET_KEY=test-secret-key-for-ci-only-not-secure" >> $GITHUB_ENV
        echo "DEBUG=True" >> $GITHUB_ENV
        echo "DATABASE_URL=postgis://postgres:postgres@localhost:5432/test_clear_db" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV
        echo "USE_MEMORY_CACHE=False" >> $GITHUB_ENV
        echo "USE_MEMORY_CHANNELS=False" >> $GITHUB_ENV
        echo "ALLOWED_HOSTS=localhost,127.0.0.1,testserver" >> $GITHUB_ENV
    
    # Run Django system checks
    - name: Run Django system checks
      run: |
        python manage.py check
    
    # Create and run migrations
    - name: Run migrations
      run: |
        python manage.py migrate
    
    # Collect static files
    - name: Collect static files
      run: |
        python manage.py collectstatic --noinput
    
    - name: Run Tests
      run: |
        python manage.py test
