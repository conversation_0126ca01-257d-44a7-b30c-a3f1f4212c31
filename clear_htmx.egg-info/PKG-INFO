Metadata-Version: 2.4
Name: clear-htmx
Version: 0.1.0
Summary: CLEAR - Utility Coordination Platform (Django + HTMX Migration)
Requires-Python: >=3.12
Requires-Dist: django>=5.2.3
Requires-Dist: django-htmx>=1.19.0
Requires-Dist: django-extensions>=3.2.3
Requires-Dist: psycopg2-binary>=2.9.9
Requires-Dist: django-environ>=0.11.2
Requires-Dist: django-crispy-forms>=2.1
Requires-Dist: crispy-bootstrap5>=2024.2
Requires-Dist: pillow>=10.4.0
Requires-Dist: django-storages>=1.14.4
Requires-Dist: django-cors-headers>=4.4.0
Requires-Dist: djangorestframework>=3.15.2
Requires-Dist: django-allauth>=65.0.2
Requires-Dist: celery>=5.4.0
Requires-Dist: redis>=5.1.1
Requires-Dist: django-redis>=6.0.0
Requires-Dist: whitenoise>=6.8.2
Requires-Dist: gunicorn>=23.0.0
Requires-Dist: gevent>=24.2.1
Requires-Dist: dj-config-url>=0.1.1
Requires-Dist: dj-database-url>=3.0.0
Requires-Dist: python-dotenv>=1.1.0
Requires-Dist: GDAL<3.9.0,>=3.4.0
Requires-Dist: Fiona>=1.9.0
Requires-Dist: channels>=4.2.2
Requires-Dist: channels-redis>=4.2.0
Requires-Dist: django-filter>=25.1
Requires-Dist: psutil>=6.1.0
Requires-Dist: playwright>=1.50.0
Requires-Dist: django-browser-reload>=1.12.1
Requires-Dist: pytest>=8.4.1
Requires-Dist: pytest-django>=4.9.0
Requires-Dist: pytest-asyncio>=0.21.0
Requires-Dist: pytest-playwright>=0.6.0
Requires-Dist: certifi>=2025.6.15
Requires-Dist: urllib3>=2.5.0
Requires-Dist: docker-mcp>=0.2.0
Requires-Dist: beautifulsoup4>=4.12.3
Requires-Dist: daphne>=4.1.2
Provides-Extra: test
Requires-Dist: pytest-cov>=5.0.0; extra == "test"
Requires-Dist: coverage>=7.5.4; extra == "test"
